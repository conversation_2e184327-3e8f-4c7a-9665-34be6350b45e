'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, Settings, UserPlus, UserMinus, Grid, TrendingUp, Heart, MoreVertical, Flag, UserX } from 'lucide-react';
import { User, Post } from '@/types';
import { petAPI, authAPI } from '@/lib/cloudbase';
import { useAuthContext } from '@/components/auth/AuthProvider';
import Button from '@/components/ui/Button';
import PetCard from '@/components/home/<USER>';
import { UserProfileSkeleton, PetCardSkeleton } from '@/components/ui/Loading';
import { showToast } from '@/components/ui/Toast';
import { formatNumber } from '@/utils';
import { cn } from '@/utils';
import UserReportModal from '@/components/user/UserReportModal';
import BlockUserModal from '@/components/user/BlockUserModal';

type SortOption = 'created_at' | 'likes_count' | 'wants_count';

interface ProfilePageClientProps {
  userId?: string;
}

const ProfilePageClient: React.FC<ProfilePageClientProps> = ({ userId: propUserId }) => {
  const params = useParams();
  const router = useRouter();
  const { user: currentUser, isLoggedIn } = useAuthContext();
  const userId = propUserId || (params.id as string);
  const [profileUser, setProfileUser] = useState<User | null>(null);
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [postsLoading, setPostsLoading] = useState(false);
  const [following, setFollowing] = useState(false);
  const [sortBy, setSortBy] = useState<SortOption>('created_at');
  const [showMoreMenu, setShowMoreMenu] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [showBlockModal, setShowBlockModal] = useState(false);

  const isOwnProfile = currentUser?._id === userId;

  // 获取用户信息
  const fetchUserProfile = async () => {
    try {
      setLoading(true);

      if (!userId) {
        showToast.error('用户ID无效');
        router.push('/');
        return;
      }

      const result = await petAPI.getUserInfo({ userId });

      if (result.success) {
        setProfileUser(result.data);

        // 检查是否已关注该用户
        if (isLoggedIn && currentUser && currentUser._id !== userId) {
          try {
            const followResult = await petAPI.getUserFollowing({
              targetUserId: currentUser._id,
              limit: 1000 // 获取所有关注列表来检查
            });

            if (followResult.success) {
              const isFollowingUser = followResult.data.some(
                (follow: any) => follow.following_id === userId
              );
              setFollowing(isFollowingUser);
            }
          } catch (error) {
            console.warn('检查关注状态失败:', error);
          }
        }
      } else {
        showToast.error('用户不存在');
        router.push('/');
      }
    } catch (error: any) {
      console.error('获取用户信息失败:', error);
      showToast.error('获取用户信息失败');
      router.push('/');
    } finally {
      setLoading(false);
    }
  };

  // 获取用户发布的宠物
  const fetchUserPosts = async () => {
    try {
      setPostsLoading(true);
      const result = await petAPI.getUserPosts({
        targetUserId: userId,
        limit: 50,
        offset: 0,
      });

      if (result.success) {
        setPosts(result.data || []);
      }
    } catch (error: any) {
      console.error('获取用户帖子失败:', error);
    } finally {
      setPostsLoading(false);
    }
  };

  // 关注/取消关注
  const handleFollow = async () => {
    if (!isLoggedIn) {
      showToast.warning('请先登录');
      return;
    }

    try {
      const result = await petAPI.toggleFollow({ targetUserId: userId });
      
      if (result.success) {
        setFollowing(result.action === 'followed');
        showToast.success(result.message);
        // 更新粉丝数
        if (profileUser) {
          setProfileUser(prev => prev ? {
            ...prev,
            followers_count: (prev.followers_count || 0) + (result.action === 'followed' ? 1 : -1)
          } : null);
        }
      } else {
        showToast.error(result.message || '操作失败');
      }
    } catch (error: any) {
      showToast.error(error.message || '操作失败');
    }
  };

  useEffect(() => {
    if (userId) {
      fetchUserProfile();
    }
  }, [userId]);

  useEffect(() => {
    if (userId) {
      fetchUserPosts();
    }
  }, [userId, sortBy]);

  // 监听用户登录状态变化，重新检查关注状态
  useEffect(() => {
    if (userId && isLoggedIn && currentUser && profileUser) {
      const checkFollowStatus = async () => {
        try {
          const followResult = await petAPI.getUserFollowing({
            targetUserId: currentUser._id,
            limit: 1000
          });

          if (followResult.success) {
            const isFollowingUser = followResult.data.some(
              (follow: any) => follow.following_id === userId
            );
            setFollowing(isFollowingUser);
          }
        } catch (error) {
          console.warn('检查关注状态失败:', error);
        }
      };

      if (currentUser._id !== userId) {
        checkFollowStatus();
      }
    }
  }, [isLoggedIn, currentUser, userId, profileUser]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center h-16">
              <button
                onClick={() => router.push('/')}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
        <UserProfileSkeleton />
      </div>
    );
  }

  if (!profileUser) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">用户不存在</h2>
          <Button onClick={() => router.push('/')}>
            返回首页
          </Button>
        </div>
      </div>
    );
  }

  const sortOptions = [
    { value: 'created_at', label: '最新', icon: Grid },
    { value: 'likes_count', label: '最受欢迎', icon: Heart },
    { value: 'wants_count', label: '收藏最多', icon: TrendingUp },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部导航 */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/')}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
              <h1 className="text-lg font-semibold text-gray-900">
                {profileUser.nickname}
              </h1>
            </div>

            {isOwnProfile ? (
              <Button
                variant="outline"
                icon={<Settings className="h-4 w-4" />}
                onClick={() => router.push('/profile')}
              >
                个人设置
              </Button>
            ) : isLoggedIn && (
              <div className="relative">
                <button
                  onClick={() => setShowMoreMenu(!showMoreMenu)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <MoreVertical className="h-5 w-5 text-gray-600" />
                </button>

                {/* 更多菜单下拉 */}
                {showMoreMenu && (
                  <div className="absolute right-0 top-full mt-2 w-40 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-20">
                    <button
                      onClick={() => {
                        setShowReportModal(true);
                        setShowMoreMenu(false);
                      }}
                      className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-gray-100"
                    >
                      <Flag className="h-4 w-4" />
                      <span>举报用户</span>
                    </button>
                    <button
                      onClick={() => {
                        setShowBlockModal(true);
                        setShowMoreMenu(false);
                      }}
                      className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <UserX className="h-4 w-4" />
                      <span>拉黑用户</span>
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 用户信息区域 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col items-center space-y-6">
            {/* 头像 */}
            <div className="relative">
              {profileUser.avatar_url ? (
                <img
                  src={profileUser.avatar_url}
                  alt={profileUser.nickname}
                  className="w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg"
                />
              ) : (
                <div className="w-24 h-24 rounded-full bg-gray-300 border-4 border-white shadow-lg flex items-center justify-center">
                  <span className="text-2xl font-bold text-gray-600">
                    {profileUser.nickname.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
            </div>

            {/* 用户名和简介 */}
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                {profileUser.nickname}
              </h2>
              {profileUser.bio ? (
                <p className="text-gray-600 max-w-md">
                  {profileUser.bio}
                </p>
              ) : (
                <p className="text-gray-400 italic">
                  {isOwnProfile ? '点击添加简介，让大家了解你' : '这个人很懒，什么都没留下'}
                </p>
              )}
            </div>

            {/* 统计数据 */}
            <div className="flex space-x-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {formatNumber(profileUser.total_likes || 0)}
                </div>
                <div className="text-sm text-gray-500">获赞</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {formatNumber(profileUser.followers_count || 0)}
                </div>
                <div className="text-sm text-gray-500">粉丝</div>
              </div>
            </div>

            {/* 关注按钮 */}
            {!isOwnProfile && isLoggedIn && (
              <Button
                onClick={handleFollow}
                variant={following ? 'outline' : 'primary'}
                icon={following ? <UserMinus className="h-4 w-4" /> : <UserPlus className="h-4 w-4" />}
              >
                {following ? '已关注' : '关注'}
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* 排序选项 */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">
            发布的宠物 ({profileUser.posts_count || 0})
          </h3>
          
          <div className="flex space-x-2">
            {sortOptions.map((option) => {
              const Icon = option.icon;
              return (
                <button
                  key={option.value}
                  onClick={() => setSortBy(option.value as SortOption)}
                  className={cn(
                    'flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                    sortBy === option.value
                      ? 'bg-primary-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
                  )}
                >
                  <Icon className="h-4 w-4" />
                  <span>{option.label}</span>
                </button>
              );
            })}
          </div>
        </div>

        {/* 宠物网格 */}
        {postsLoading ? (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {Array.from({ length: 8 }).map((_, index) => (
              <PetCardSkeleton key={index} />
            ))}
          </div>
        ) : posts.length > 0 ? (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {posts.map((post) => (
              <PetCard key={post._id} post={post} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-500">
              <div className="text-6xl mb-4">🐾</div>
              <p className="text-lg font-medium mb-2">
                {isOwnProfile ? '还没有发布宠物' : 'TA还没有发布宠物'}
              </p>
              <p className="text-sm">
                {isOwnProfile ? '快去发布第一只宠物吧！' : '期待TA的第一次分享'}
              </p>
              {isOwnProfile && (
                <Button
                  className="mt-4"
                  onClick={() => router.push('/upload')}
                >
                  发布宠物
                </Button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 举报用户模态框 */}
      <UserReportModal
        isOpen={showReportModal}
        onClose={() => setShowReportModal(false)}
        targetUserId={userId}
        targetUserName={profileUser?.nickname || ''}
        onSuccess={() => {
          showToast.success('举报提交成功');
          setShowReportModal(false);
        }}
      />

      {/* 拉黑用户模态框 */}
      <BlockUserModal
        isOpen={showBlockModal}
        onClose={() => setShowBlockModal(false)}
        targetUserId={userId}
        targetUserName={profileUser?.nickname || ''}
        onSuccess={() => {
          showToast.success('已拉黑该用户');
          setShowBlockModal(false);
          router.push('/'); // 拉黑后返回首页
        }}
      />
    </div>
  );
};

export default ProfilePageClient;
