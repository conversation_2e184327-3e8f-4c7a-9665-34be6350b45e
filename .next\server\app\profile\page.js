(()=>{var e={};e.id=178,e.ids=[178],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},82980:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>h,tree:()=>o}),s(85282),s(16953),s(35866);var a=s(23191),r=s(88716),l=s(37922),i=s.n(l),n=s(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let o=["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,85282)),"D:\\web-cloudbase-project\\src\\app\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\web-cloudbase-project\\src\\app\\profile\\page.tsx"],x="/profile/page",m={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},21437:(e,t,s)=>{Promise.resolve().then(s.bind(s,63379))},51896:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},924:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},70003:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},58038:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},98091:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},67187:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]])},63379:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>W});var a=s(10326),r=s(35047),l=s(17577),i=s(74131),n=s(94019),c=s(70003),o=s(86333),d=s(79635),x=s(51896),m=s(77636),h=s(76557);let u=(0,h.Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]),g=(0,h.Z)("PenTool",[["path",{d:"m12 19 7-7 3 3-7 7-3-3z",key:"rklqx2"}],["path",{d:"m18 13-1.5-7.5L2 2l3.5 14.5L13 18l5-5z",key:"1et58u"}],["path",{d:"m2 2 7.586 7.586",key:"etlp93"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]]);var p=s(88378),f=s(39730),y=s(67427);let j=(0,h.Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var b=s(924),v=s(48998),N=s(33734),w=s(12714),k=s(99837),C=s(20603),_=s(41828);let Z=e=>e.startsWith("cloud://")||!e.startsWith("http")&&!e.startsWith("/"),S=({fileId:e,alt:t="",className:s="",width:r,height:i,style:n,onClick:c})=>{let[o,d]=(0,l.useState)(""),[x,m]=(0,l.useState)(!0),[h,u]=(0,l.useState)(!1),[g,p]=(0,l.useState)(0);return((0,l.useEffect)(()=>{(async()=>{if(!e){u(!0),m(!1);return}if(e.startsWith("http")){d(e),m(!1);return}try{m(!0),u(!1),p(0);let t=await (0,_.getImageUrl)(e);d(t)}catch(e){console.error("加载图片失败:",e),u(!0)}finally{m(!1)}})()},[e]),x)?a.jsx("div",{className:`bg-gray-200 animate-pulse flex items-center justify-center ${s}`,style:{width:r,height:i,...n},children:a.jsx("div",{className:"text-gray-400 text-sm",children:"加载中..."})}):h||!o?a.jsx("div",{className:`bg-gray-200 flex items-center justify-center ${s}`,style:{width:r,height:i,...n},children:a.jsx("div",{className:"text-gray-400 text-sm",children:"图片加载失败"})}):a.jsx("img",{src:o,alt:t,className:s,width:r,height:i,style:n,onClick:c,onError:async()=>{if(console.log("图片加载失败，尝试刷新URL，重试次数:",g),g<2){if(p(e=>e+1),Z(e))try{let t=await _.petAPI.getImage({fileId:e});t.success&&t.data?.url?(console.log("获取新的图片URL:",t.data.url),d(t.data.url)):(console.error("刷新图片URL失败:",t.message),u(!0))}catch(e){console.error("刷新图片URL异常:",e),u(!0)}else console.log("普通URL加载失败，无法刷新"),u(!0)}else u(!0)}})};var O=s(90434),P=s(8040);function D({isOpen:e,onClose:t,currentBio:s,onSave:r}){let[i,c]=(0,l.useState)(""),[o,d]=(0,l.useState)(!1),x=async()=>{if(i.length>200){C.C.error("个人简介不能超过200字");return}d(!0);try{await r(i),C.C.success("个人简介更新成功"),t()}catch(e){C.C.error("更新失败，请重试")}finally{d(!1)}};return e?a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[a.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"编辑个人简介"}),a.jsx("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:a.jsx(n.Z,{className:"w-6 h-6"})})]}),a.jsx("div",{className:"p-6",children:(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"个人简介"}),a.jsx("textarea",{value:i,onChange:e=>c(e.target.value),placeholder:"介绍一下自己吧...",rows:4,maxLength:200,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"}),a.jsx("div",{className:"flex justify-between items-center mt-2",children:(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[i.length,"/200"]})})]})}),(0,a.jsxs)("div",{className:"flex space-x-3 p-6 border-t",children:[a.jsx(k.Z,{variant:"outline",onClick:t,className:"flex-1",children:"取消"}),a.jsx(k.Z,{onClick:x,loading:o,className:"flex-1",children:"保存"})]})]})}):null}var E=s(58038),I=s(67187);function L({isOpen:e,onClose:t,userId:s}){let[r,i]=(0,l.useState)("following"),[c,o]=(0,l.useState)([]),[x,m]=(0,l.useState)([]),[h,u]=(0,l.useState)(!1),g=async e=>{try{o(t=>t.map(t=>t._id===e?{...t,isFollowing:!1}:t)),C.C.success("已取消关注")}catch(e){C.C.error("取消关注失败")}},p=async e=>{try{o(t=>t.map(t=>t._id===e?{...t,isFollowing:!0}:t)),C.C.success("关注成功")}catch(e){C.C.error("关注失败")}},f=async e=>{try{m(t=>t.filter(t=>t._id!==e)),C.C.success("已取消拉黑")}catch(e){C.C.error("取消拉黑失败")}};return e?a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",onClick:e=>{e.target===e.currentTarget&&t()},children:(0,a.jsxs)("div",{className:"bg-white rounded-lg w-[480px] h-[500px] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b flex-shrink-0",children:[a.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"following"===r?"我的关注":"黑名单"}),a.jsx("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:a.jsx(n.Z,{className:"w-6 h-6"})})]}),a.jsx("div",{className:"border-b border-gray-200 flex-shrink-0",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsxs)("button",{onClick:()=>i("following"),className:`flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors ${"following"===r?"text-blue-600 border-b-2 border-blue-600 bg-blue-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,children:[a.jsx(y.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"我的关注"})]}),(0,a.jsxs)("button",{onClick:()=>i("blocked"),className:`flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors ${"blocked"===r?"text-blue-600 border-b-2 border-blue-600 bg-blue-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,children:[a.jsx(E.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"黑名单"})]})]})}),a.jsx("div",{className:"flex-1 overflow-y-auto",children:h?a.jsx("div",{className:"flex items-center justify-center py-12",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):"following"===r?0===c.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"text-gray-400 mb-4",children:a.jsx(y.Z,{className:"w-16 h-16 mx-auto"})}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有关注任何人"}),a.jsx("p",{className:"text-gray-600",children:"去发现更多有趣的用户吧！"})]}):a.jsx("div",{className:"divide-y divide-gray-200",children:c.map(e=>a.jsx("div",{className:"p-4 hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-12 h-12 rounded-full bg-gray-200 overflow-hidden flex-shrink-0",children:e.avatar_url?a.jsx("img",{src:e.avatar_url,alt:e.nickname,className:"w-full h-full object-cover"}):a.jsx("div",{className:"w-full h-full flex items-center justify-center",children:a.jsx(d.Z,{className:"w-6 h-6 text-gray-400"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx("h3",{className:"text-sm font-medium text-gray-900 truncate",children:e.nickname}),e.bio&&a.jsx("p",{className:"text-sm text-gray-500 truncate",children:e.bio})]}),a.jsx("div",{className:"flex-shrink-0",children:e.isFollowing?a.jsx(k.Z,{variant:"outline",size:"sm",onClick:()=>g(e._id),children:"取消关注"}):a.jsx(k.Z,{size:"sm",onClick:()=>p(e._id),children:"关注"})})]})},e._id))}):0===x.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"text-gray-400 mb-4",children:a.jsx(E.Z,{className:"w-16 h-16 mx-auto"})}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"黑名单为空"}),a.jsx("p",{className:"text-gray-600",children:"您还没有拉黑任何用户"})]}):a.jsx("div",{className:"divide-y divide-gray-200",children:x.map(e=>a.jsx("div",{className:"p-4 hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-12 h-12 rounded-full bg-gray-200 overflow-hidden flex-shrink-0",children:e.avatar_url?a.jsx("img",{src:e.avatar_url,alt:e.nickname,className:"w-full h-full object-cover"}):a.jsx("div",{className:"w-full h-full flex items-center justify-center",children:a.jsx(I.Z,{className:"w-6 h-6 text-gray-400"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx("h3",{className:"text-sm font-medium text-gray-900 truncate",children:e.nickname}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 truncate",children:["拉黑时间：",new Date(e.blockedAt).toLocaleDateString()]})]}),a.jsx("div",{className:"flex-shrink-0",children:a.jsx(k.Z,{variant:"outline",size:"sm",onClick:()=>f(e._id),className:"text-red-600 border-red-200 hover:bg-red-50",children:"取消拉黑"})})]})},e._id))})})]})}):null}var $=s(88673),M=s(13570),z=s(22502),R=s(98091),A=s(37202);let q=({isOpen:e,onClose:t,onConfirm:s,title:r,message:i,confirmText:c="确定",cancelText:o="取消",type:d="danger",loading:x=!1})=>((0,l.useEffect)(()=>{let a=a=>{e&&("Escape"===a.key?t():"Enter"!==a.key||x||s())};return document.addEventListener("keydown",a),()=>document.removeEventListener("keydown",a)},[e,t,s,x]),(0,l.useEffect)(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[e]),e)?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",role:"dialog","aria-modal":"true","aria-labelledby":"confirm-modal-title","aria-describedby":"confirm-modal-description",children:[a.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50 transition-opacity",onClick:t,"aria-hidden":"true"}),(0,a.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all",children:[a.jsx("button",{onClick:t,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors","aria-label":"关闭对话框",children:a.jsx(n.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(()=>{switch(d){case"danger":return a.jsx(R.Z,{className:"w-6 h-6 text-red-600"});case"warning":return a.jsx(A.Z,{className:"w-6 h-6 text-yellow-600"});default:return a.jsx(A.Z,{className:"w-6 h-6 text-blue-600"})}})(),a.jsx("h3",{id:"confirm-modal-title",className:"text-lg font-medium text-gray-900",children:r||"确认操作"})]}),a.jsx("p",{id:"confirm-modal-description",className:"text-gray-600 mb-6 leading-relaxed",children:i}),(0,a.jsxs)("div",{className:"flex space-x-3 justify-end",children:[a.jsx(k.Z,{variant:"outline",onClick:t,disabled:x,className:"px-4 py-2",children:o}),a.jsx(k.Z,{variant:(()=>{switch(d){case"danger":return"danger";case"warning":return"warning";default:return"primary"}})(),onClick:s,loading:x,disabled:x,className:"px-4 py-2",children:c})]})]})]})]}):null,U=({isOpen:e,onClose:t,items:s,position:r,className:i=""})=>{let n=(0,l.useRef)(null);if((0,l.useEffect)(()=>{let s=e=>{n.current&&!n.current.contains(e.target)&&t()};return e&&(document.addEventListener("mousedown",s),document.addEventListener("touchstart",s)),()=>{document.removeEventListener("mousedown",s),document.removeEventListener("touchstart",s)}},[e,t]),(0,l.useEffect)(()=>{let s=s=>{e&&"Escape"===s.key&&t()};return document.addEventListener("keydown",s),()=>document.removeEventListener("keydown",s)},[e,t]),(0,l.useEffect)(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[e]),!e)return null;let c=e=>{e.onClick(),t()};return(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-30",onClick:t,"aria-hidden":"true"}),a.jsx("div",{ref:n,className:`
          fixed z-50 bg-white rounded-xl shadow-2xl border border-gray-200
          min-w-48 py-3 mx-4 transform transition-all duration-200 ease-out
          ${r?"":"bottom-6 left-0 right-0 md:bottom-auto md:left-auto md:right-auto md:mx-0"}
          ${e?"scale-100 opacity-100":"scale-95 opacity-0"}
          ${i}
        `,style:r?{left:r.x,top:r.y,transform:"translate(-50%, -100%)"}:void 0,role:"menu","aria-orientation":"vertical",children:s.map((e,t)=>(0,a.jsxs)("button",{onClick:()=>c(e),className:`
              w-full px-5 py-4 text-left flex items-center space-x-3
              transition-all duration-150 font-medium text-base
              ${"danger"===e.variant?"text-red-600 hover:bg-red-50 active:bg-red-100 hover:scale-[1.02]":"text-gray-700 hover:bg-gray-50 active:bg-gray-100 hover:scale-[1.02]"}
              ${0===t?"rounded-t-xl":""}
              ${t===s.length-1?"rounded-b-xl":""}
              ${t>0?"border-t border-gray-100":""}
            `,role:"menuitem",tabIndex:0,children:[e.icon&&a.jsx("span",{className:"flex-shrink-0 w-5 h-5 flex items-center justify-center",children:e.icon}),a.jsx("span",{className:"flex-1",children:e.label})]},e.id))})]})},F=({onLongPress:e,onPress:t,delay:s=500,shouldPreventDefault:a=!0})=>{let[r,i]=(0,l.useState)(!1),n=(0,l.useRef)(),c=(0,l.useRef)(),o=(0,l.useCallback)(t=>{a&&t.target&&(t.target.addEventListener("touchend",x,{passive:!1}),c.current=t.target),i(!1),n.current=setTimeout(()=>{i(!0),navigator.vibrate&&navigator.vibrate([50,30,50]),e()},s)},[e,s,a]),d=(0,l.useCallback)((e,s=!0)=>{n.current&&clearTimeout(n.current),s&&!r&&t&&t(),i(!1),a&&c.current&&c.current.removeEventListener("touchend",x)},[a,r,t]),x=e=>{e.touches&&e.touches.length<2&&e.preventDefault&&e.preventDefault()};return{onMouseDown:e=>o(e),onMouseUp:e=>d(e),onMouseLeave:e=>d(e,!1),onTouchStart:e=>o(e),onTouchEnd:e=>d(e),onTouchMove:e=>{e.touches[0]&&d(e,!1)},isLongPressing:r}},T=({post:e,onLongPress:t,onDelete:s,onEdit:r,type:l,isDraft:i=!1,onClick:c})=>{let o=F({onLongPress:t,onPress:c,delay:500});return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{...o,className:`relative transition-all duration-200 ${o.isLongPressing?"scale-95 opacity-80 ring-2 ring-blue-500 ring-opacity-50":""} ${c?"cursor-pointer":""}`,children:[a.jsx(z.Z,{post:e,isDraft:i}),o.isLongPressing&&a.jsx("div",{className:"absolute inset-0 bg-blue-500 bg-opacity-10 rounded-lg flex items-center justify-center md:hidden",children:a.jsx("div",{className:"bg-white bg-opacity-90 px-3 py-1 rounded-full text-sm font-medium text-blue-600",children:"松开显示选项"})})]}),a.jsx("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),s()},className:"absolute top-2 right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-colors shadow-lg z-10 md:flex hidden",title:(()=>{switch(l){case"published":return"下架宝贝并移至待发布";case"draft":return"删除草稿";case"history":return"从浏览历史中移除";default:return"删除"}})(),children:a.jsx(n.Z,{className:"w-3 h-3"})})]})};function W(){let e=(0,r.useRouter)();(0,r.useSearchParams)().get("id");let{user:t,isLoggedIn:h,isLoading:Z}=(0,i.a)(),[E,I]=(0,l.useState)(null),[R,A]=(0,l.useState)(!0),[F,W]=(0,l.useState)(!1),[G,H]=(0,l.useState)("posts"),[J,X]=(0,l.useState)(!1),[V,B]=(0,l.useState)(!1),[K,Y]=(0,l.useState)(!1),[Q,ee]=(0,l.useState)([]),[et,es]=(0,l.useState)([]),[ea,er]=(0,l.useState)([]),[el,ei]=(0,l.useState)(!1),[en,ec]=(0,l.useState)(!1),[eo,ed]=(0,l.useState)(!1),[ex,em]=(0,l.useState)(""),[eh,eu]=(0,l.useState)([]),[eg,ep]=(0,l.useState)({isOpen:!1,message:"",onConfirm:()=>{}}),[ef,ey]=(0,l.useState)({isOpen:!1,postId:"",postType:"published"}),ej=e=>{try{console.warn("不在客户端环境，无法跳转");return}catch(e){console.error("跳转到发布页面失败:",e),C.C.error("跳转失败，请重试")}},eb=async e=>{ep({isOpen:!0,title:"确认下架",message:"下架这个宝贝并移动到待发布吗？",onConfirm:()=>ev(e)})},ev=async e=>{ep(e=>({...e,loading:!0}));try{C.C.loading("正在下架宝贝...");let t=await _.petAPI.getPostDetail({postId:e});if(!t.success){C.C.dismiss(),C.C.error("获取帖子信息失败");return}let s=t.data,a=await _.petAPI.deletePost({postId:e});if(!a||!a.success){C.C.dismiss(),C.C.error("下架失败："+(a?.message||"服务器删除失败"));return}console.log("服务器删除成功，开始保存草稿");let r=[];if(s.images&&Array.isArray(s.images))for(let e of s.images)try{if("string"==typeof e&&e.startsWith("http")){let t=await fetch(e),s=await t.blob(),a=await new Promise((e,t)=>{let a=new FileReader;a.onload=()=>e(a.result),a.onerror=t,a.readAsDataURL(s)});r.push(a),console.log("成功转换图片URL为base64:",e.substring(0,50)+"...")}else r.push(e)}catch(t){console.error("转换图片失败:",e,t),r.push(e)}let l={id:`archived_${e}_${Date.now()}`,title:s.breed||"",breed:s.breed||"",description:s.description||"",images:r,category:s.category||"",location:s.location||"",contact_info:s.contact_info||{},type:s.type||"selling",price:s.price||"",age:s.age||"",gender:s.gender||"",vaccination:s.vaccination||!1,health_certificate:s.health_certificate||!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),isArchived:!0,originalPostId:e},i=(0,$.Oe)(),n=[l,...i];localStorage.setItem("petDrafts",JSON.stringify(n)),console.log("草稿保存成功，包含",r.length,"张图片"),er(t=>t.filter(t=>t._id!==e)),"drafts"===G&&ee(n),C.C.dismiss(),C.C.success("宝贝已下架并移至待发布")}catch(e){console.error("下架宝贝失败:",e),C.C.dismiss(),C.C.error(e.message||"下架失败，请重试")}finally{ep(e=>({...e,isOpen:!1,loading:!1}))}},eN=async e=>{try{let t=(0,$.Oe)().find(t=>t.id===e);if(!t){C.C.error("草稿不存在");return}let s=t.isArchived?"确定要删除这个归档的宝贝吗？删除后无法恢复。":"确定要删除这个草稿吗？";ep({isOpen:!0,title:"确认删除",message:s,onConfirm:()=>ew(e)})}catch(e){console.error("删除草稿失败:",e),C.C.error("删除失败，请重试")}},ew=async e=>{try{let t=(0,$.Oe)(),s=t.find(t=>t.id===e),a=t.filter(t=>t.id!==e);localStorage.setItem("petDrafts",JSON.stringify(a)),ee(a);let r=s?.isArchived?"归档宝贝已删除":"草稿已删除";C.C.success(r),ep(e=>({...e,isOpen:!1}))}catch(e){console.error("删除草稿失败:",e),C.C.error("删除失败，请重试"),ep(e=>({...e,isOpen:!1}))}},ek=e=>{try{M.KX.removeRecord(e),eu(t=>t.filter(t=>t.postId!==e)),C.C.success("已从浏览历史中移除")}catch(e){console.error("删除历史记录失败:",e),C.C.error("操作失败，请重试")}},eC=(e,t)=>{ey({isOpen:!0,postId:e,postType:t})},e_=async e=>{try{let s=await _.petAPI.updateProfile(e);if(s.success)C.C.success("资料更新成功"),E&&I({...E,nickname:e.nickname||E.nickname,bio:e.bio||E.bio,contact_info:e.contactInfo||E.contact_info,address:e.address||E.address}),e.contactInfo&&t&&localStorage.setItem(`contact_${t._id}`,JSON.stringify(e.contactInfo)),void 0!==e.address&&t&&localStorage.setItem(`address_${t._id}`,e.address);else throw Error(s.message||"更新失败")}catch(e){throw C.C.error(e.message||"更新失败，请重试"),e}},eZ=async e=>{let a=e.target.files?.[0];if(a){if(!a.type.startsWith("image/")){em("请选择图片文件");return}if(a.size>5242880){em("图片大小不能超过5MB");return}ed(!0),em("");try{let{uploadFile:e}=await Promise.resolve().then(s.bind(s,41828)),r=document.createElement("canvas"),l=r.getContext("2d"),i=new Image,n=await new Promise((e,t)=>{i.onload=()=>{let{width:s,height:n}=i;s>n?s>300&&(n=300*n/s,s=300):n>300&&(s=300*s/n,n=300),r.width=s,r.height=n,l?.drawImage(i,0,0,s,n),r.toBlob(s=>{if(s){let t=new File([s],a.name,{type:"image/jpeg",lastModified:Date.now()});e(t)}else t(Error("压缩失败"))},"image/jpeg",.9)},i.onerror=()=>t(Error("图片加载失败")),i.src=URL.createObjectURL(a)});console.log(`头像压缩: ${a.size} -> ${n.size} (${Math.round((1-n.size/a.size)*100)}% 减少)`);let{petAPI:c}=await Promise.resolve().then(s.bind(s,41828)),o=await new Promise((e,t)=>{let s=new FileReader;s.onload=()=>{let t=s.result.split(",")[1];e(t)},s.onerror=t,s.readAsDataURL(n)}),d=Date.now(),x=Math.random().toString(36).substring(2,8),m=`avatar_${d}_${x}.jpg`,h=await c.uploadToStatic({fileName:m,fileData:o,contentType:"image/jpeg"});if(!h.success)throw Error(h.message||"头像上传失败");let u=h.data.url,g=await c.updateAvatar({avatarUrl:u});if(g.success){if(I(e=>e?{...e,avatar_url:u}:null),t)try{let e={...t,avatar_url:u};localStorage.setItem("pet_platform_user",JSON.stringify(e))}catch(e){console.error("保存头像到本地存储失败:",e)}C.C.success("您的头像已经更改完成，三十天内只能修改一次头像")}else console.error("头像更新失败:",g),em(g.message||"头像更新失败")}catch(e){console.error("头像上传失败:",e),em("头像上传失败，请重试: "+(e instanceof Error?e.message:String(e)))}finally{ed(!1)}}},eS=async e=>{try{E&&I({...E,bio:e||"这个人很懒，什么都没有留下..."})}catch(e){throw e}},eO=t=>{e.push(`/upload?draftId=${t}`)};return R?a.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600",children:"加载中..."})]})}):E?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("div",{className:"bg-white shadow-sm",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[a.jsx("button",{onClick:()=>e.push("/"),className:"mb-6 p-2 hover:bg-gray-100 rounded-full transition-colors",children:a.jsx(o.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"w-24 h-24 md:w-32 md:h-32 rounded-full bg-gray-200 overflow-hidden",children:[E.avatar_url?a.jsx(S,{fileId:E.avatar_url,alt:E.nickname,className:"w-full h-full object-cover"}):a.jsx("div",{className:"w-full h-full flex items-center justify-center",children:a.jsx(d.Z,{className:"w-12 h-12 md:w-16 md:h-16 text-gray-400"})}),eo&&a.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-full",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-white"})})]}),F&&(0,a.jsxs)("div",{className:"absolute bottom-0 right-0",children:[a.jsx("input",{type:"file",accept:"image/*",onChange:eZ,className:"hidden",id:"avatar-upload",disabled:eo}),a.jsx("label",{htmlFor:"avatar-upload",className:`bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 transition-colors cursor-pointer block ${eo?"opacity-50 cursor-not-allowed":""}`,children:a.jsx(x.Z,{className:"w-4 h-4"})})]}),ex&&a.jsx("div",{className:"absolute top-full left-0 mt-2 text-red-500 text-sm",children:ex})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl md:text-3xl font-bold text-gray-900",children:E.nickname}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-gray-600",children:[E.location&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(m.Z,{className:"w-4 h-4"}),a.jsx("span",{children:E.location})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(u,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:["加入于 ",new Date(E.created_at).getFullYear(),"年"]})]})]}),(0,a.jsxs)("div",{className:"mt-3 flex items-center space-x-2",children:[a.jsx("p",{className:"text-gray-700",children:E.bio||"这个人很懒，什么都没有留下..."}),F&&a.jsx("button",{onClick:()=>B(!0),className:"text-gray-400 hover:text-gray-600 transition-colors",children:a.jsx(g,{className:"w-4 h-4"})})]})]}),a.jsx("div",{className:"flex space-x-3 mt-4 md:mt-0",children:F?a.jsx(k.Z,{variant:"outline",icon:a.jsx(p.Z,{className:"w-4 h-4"}),onClick:()=>X(!0),children:"个人设置"}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(k.Z,{icon:a.jsx(f.Z,{className:"w-4 h-4"}),children:"私信"}),a.jsx(k.Z,{variant:"outline",icon:a.jsx(y.Z,{className:"w-4 h-4"}),children:"关注"})]})})]}),(0,a.jsxs)("div",{className:"flex space-x-6 mt-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-xl font-bold text-gray-900",children:E.likes_count}),a.jsx("div",{className:"text-sm text-gray-600",children:"获赞"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-xl font-bold text-gray-900",children:E.followers_count}),a.jsx("div",{className:"text-sm text-gray-600",children:"粉丝"})]}),(0,a.jsxs)("div",{className:"text-center cursor-pointer",onClick:()=>F&&Y(!0),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx("span",{className:"text-xl font-bold text-gray-900",children:E.following_count}),F&&a.jsx(j,{className:"w-4 h-4 text-gray-400"})]}),a.jsx("div",{className:"text-sm text-gray-600",children:"关注"})]})]})]})]})]})}),a.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[F?a.jsx(a.Fragment,{children:(0,a.jsxs)("div",{className:"flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1",children:[(0,a.jsxs)("button",{onClick:()=>H("posts"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"posts"===G?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(b.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"已发布宝贝"})]}),(0,a.jsxs)("button",{onClick:()=>H("drafts"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"drafts"===G?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(v.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"待发布宝贝"})]}),(0,a.jsxs)("button",{onClick:()=>H("wants"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"wants"===G?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(N.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"评分"})]}),(0,a.jsxs)("button",{onClick:()=>H("history"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"history"===G?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(w.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"浏览历史"})]})]})}):a.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:`${E.nickname}的发布`}),F&&a.jsx("div",{className:"mb-4 md:hidden",children:a.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:a.jsx("p",{className:"text-sm text-blue-700",children:"\uD83D\uDCA1 提示：长按卡片可显示操作选项"})})}),"posts"===G&&(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:[a.jsx(O.default,{href:"/upload",children:a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow cursor-pointer",children:a.jsx("div",{className:"aspect-square bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(x.Z,{className:"w-12 h-12 text-blue-500 mx-auto mb-3"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-1",children:"发布宝贝"}),a.jsx("p",{className:"text-sm text-gray-600",children:"分享您的宠物"})]})})})}),en?a.jsx("div",{className:"col-span-full text-center py-8 text-gray-500",children:a.jsx("p",{children:"加载中..."})}):0===ea.length?a.jsx("div",{className:"col-span-full text-center py-8 text-gray-500",children:a.jsx("p",{children:"还没有发布任何内容"})}):ea.map(e=>a.jsx(T,{post:e,onLongPress:()=>eC(e._id,"published"),onDelete:()=>eb(e._id),type:"published"},e._id))]}),"drafts"===G&&(0,a.jsxs)(a.Fragment,{children:[Q.length>0&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"待发布宝贝"}),a.jsx("button",{onClick:()=>{ep({isOpen:!0,title:"确认清空",message:"确定要清空所有待发布的宝贝吗？此操作不可恢复。",onConfirm:()=>{localStorage.removeItem("petDrafts"),ee([]),C.C.success("所有待发布宝贝已清空"),ep(e=>({...e,isOpen:!1}))}})},className:"text-sm text-red-600 hover:text-red-800 transition-colors font-medium",children:"全部清空"})]}),0===Q.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"text-gray-400 mb-4",children:a.jsx(v.Z,{className:"w-16 h-16 mx-auto"})}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有待发布的内容"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"在发布页面保存草稿后会显示在这里"}),a.jsx(O.default,{href:"/upload",children:a.jsx(k.Z,{icon:a.jsx(x.Z,{className:"w-4 h-4"}),children:"创建内容"})})]}):a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:Q.map(e=>{let t=e.images?.map(e=>"string"==typeof e?e:(console.warn("草稿中发现非字符串图片数据，使用默认图片:",e),"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center"))||[],s={_id:e.id,breed:e.breed||"未命名草稿",description:e.description||"",images:t,author:{_id:"draft",nickname:"草稿"},location:e.location||"",likes_count:0,created_at:e.updated_at||e.created_at,type:e.type||"selling",gender:e.gender};return(0,a.jsxs)("div",{className:"relative",children:[a.jsx(T,{post:s,onLongPress:()=>eC(e.id,"draft"),onDelete:()=>eN(e.id),onClick:()=>ej(e.id),type:"draft",isDraft:!0}),e.isArchived&&a.jsx("div",{className:"absolute top-2 left-2 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium z-10",children:"已归档"})]},e.id)})})]}),"wants"===G&&a.jsx(a.Fragment,{children:el?a.jsx("div",{className:"text-center py-12",children:a.jsx("div",{className:"text-gray-400",children:"加载中..."})}):0===et.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"text-gray-400 mb-4",children:a.jsx(N.Z,{className:"w-16 h-16 mx-auto"})}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有评分任何内容"}),a.jsx("p",{className:"text-gray-600",children:"给喜欢的宠物评分后会显示在这里"})]}):a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:et.map(e=>a.jsx(z.Z,{post:e},e._id))})}),"history"===G&&(0,a.jsxs)("div",{children:[eh.length>0&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["浏览记录保留3天，共",eh.length,"条记录"]}),a.jsx("button",{onClick:()=>{ep({isOpen:!0,title:"确认清空",message:"确定要清空所有浏览历史吗？",onConfirm:()=>{M.KX.clearHistory(),eu([]),C.C.success("浏览历史已清空"),ep(e=>({...e,isOpen:!1}))}})},className:"text-sm text-red-600 hover:text-red-800 transition-colors font-medium",children:"清空历史"})]}),0===eh.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"text-gray-400 mb-4",children:a.jsx(w.Z,{className:"w-16 h-16 mx-auto"})}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有浏览历史"}),a.jsx("p",{className:"text-gray-600",children:"浏览过的内容会显示在这里，记录保留3天"})]}):a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:eh.map(e=>{let t={_id:e.postId,breed:e.title,description:"",images:e.image?[e.image]:[],author:{_id:"history",nickname:e.author||"匿名用户"},location:"",likes_count:0,created_at:new Date(e.timestamp).toISOString()};return a.jsx(T,{post:t,onLongPress:()=>eC(e.postId,"history"),onDelete:()=>ek(e.postId),type:"history"},e.postId)})})]})]})}),a.jsx(P.Z,{isOpen:J,onClose:()=>X(!1),currentUser:t,onUpdate:e_}),a.jsx(D,{isOpen:V,onClose:()=>B(!1),currentBio:E?.bio||"",onSave:eS}),a.jsx(L,{isOpen:K,onClose:()=>Y(!1),userId:t?._id||""}),a.jsx(q,{isOpen:eg.isOpen,onClose:()=>ep(e=>({...e,isOpen:!1})),onConfirm:eg.onConfirm,title:eg.title,message:eg.message,loading:eg.loading}),a.jsx(U,{isOpen:ef.isOpen,onClose:()=>ey(e=>({...e,isOpen:!1})),items:(()=>{let{postId:e,postType:t}=ef;switch(t){case"published":return[{id:"delete",label:"下架宝贝",icon:a.jsx(n.Z,{className:"w-4 h-4"}),onClick:()=>{ey(e=>({...e,isOpen:!1})),eb(e)},variant:"danger"}];case"draft":return[{id:"edit",label:"编辑草稿",icon:a.jsx(c.Z,{className:"w-4 h-4"}),onClick:()=>{ey(e=>({...e,isOpen:!1})),eO(e)}},{id:"delete",label:"删除草稿",icon:a.jsx(n.Z,{className:"w-4 h-4"}),onClick:()=>{ey(e=>({...e,isOpen:!1})),eN(e)},variant:"danger"}];case"history":return[{id:"delete",label:"从历史中移除",icon:a.jsx(n.Z,{className:"w-4 h-4"}),onClick:()=>{ey(e=>({...e,isOpen:!1})),ek(e)},variant:"danger"}];default:return[]}})(),position:ef.position})]}):a.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"用户不存在"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"抱歉，找不到该用户的信息"}),a.jsx(O.default,{href:"/",children:a.jsx(k.Z,{children:"返回首页"})})]})})}},88673:(e,t,s)=>{"use strict";s.d(t,{DO:()=>l,Oe:()=>a,XD:()=>i});let a=()=>{try{return[]}catch(e){return console.error("获取草稿失败:",e),[]}},r=async e=>{let t=[];for(let s of e)if(s instanceof File)try{let e=await new Promise((e,t)=>{let a=new FileReader;a.onload=()=>e(a.result),a.onerror=t,a.readAsDataURL(s)});t.push(e)}catch(e){console.error("转换图片失败:",e),t.push("https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center")}else"string"==typeof s&&t.push(s);return t},l=async e=>{try{throw Error("无法在服务器端保存草稿")}catch(e){throw console.error("保存草稿失败:",e),Error("保存草稿失败")}},i=async(e,t)=>{try{let s=a(),l=s.findIndex(t=>t.id===e);if(-1!==l){let e=await r(t.images);s[l]={...s[l],...t,images:e,updated_at:new Date().toISOString()},localStorage.setItem("petDrafts",JSON.stringify(s))}}catch(e){throw console.error("更新草稿失败:",e),Error("更新草稿失败")}}},85282:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\profile\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[276,201,240,535,162],()=>s(82980));module.exports=a})();