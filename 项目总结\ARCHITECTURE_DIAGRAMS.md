# 宠物交易平台 - 架构图集

## 🏗️ 系统架构图

### 整体架构
```mermaid
graph TB
    subgraph "用户端"
        A[Web浏览器]
        B[移动端浏览器]
    end
    
    subgraph "CDN层"
        C[腾讯云CDN]
    end
    
    subgraph "前端层"
        D[Next.js应用]
        E[静态资源]
    end
    
    subgraph "API网关层"
        F[CloudBase API网关]
    end
    
    subgraph "业务逻辑层"
        G[pet-api云函数]
        H[用户管理模块]
        I[内容管理模块]
        J[活动系统模块]
        K[通知系统模块]
        L[广告系统模块]
    end
    
    subgraph "数据存储层"
        M[(云数据库 MongoDB)]
        N[云存储 COS]
        O[静态网站托管]
    end
    
    subgraph "监控运维"
        P[性能监控]
        Q[错误监控]
        R[日志分析]
    end
    
    A --> C
    B --> C
    C --> D
    D --> E
    D --> F
    F --> G
    G --> H
    G --> I
    G --> J
    G --> K
    G --> L
    H --> M
    I --> M
    J --> M
    K --> M
    L --> M
    D --> N
    E --> O
    G --> P
    G --> Q
    G --> R
```

### 数据流架构
```mermaid
graph LR
    subgraph "数据输入"
        A[用户操作]
        B[系统事件]
        C[定时任务]
    end
    
    subgraph "数据处理"
        D[前端验证]
        E[API路由]
        F[业务逻辑]
        G[数据验证]
    end
    
    subgraph "数据存储"
        H[(用户数据)]
        I[(内容数据)]
        J[(活动数据)]
        K[(系统配置)]
    end
    
    subgraph "数据输出"
        L[页面渲染]
        M[API响应]
        N[通知推送]
        O[统计报表]
    end
    
    A --> D
    B --> E
    C --> F
    D --> E
    E --> F
    F --> G
    G --> H
    G --> I
    G --> J
    G --> K
    H --> L
    I --> L
    J --> M
    K --> N
    H --> O
    I --> O
    J --> O
```

## 📱 页面架构图

### 前端页面结构
```mermaid
graph TD
    A[根布局 Layout] --> B[主页 /]
    A --> C[搜索页 /search]
    A --> D[发布页 /upload]
    A --> E[个人中心 /profile]
    A --> F[收藏页 /favorites]
    A --> G[通知页 /notifications]
    A --> H[消息页 /messages]
    A --> I[活动中心 /activities]
    A --> J[帖子详情 /post/detail]
    
    E --> E1[个人主页]
    E --> E2[他人主页 /profile/[id]]
    
    K[管理后台 /admin] --> L[管理员登录]
    K --> M[仪表板 /admin/dashboard]
    K --> N[帖子管理 /admin/posts]
    K --> O[活动管理 /admin/activities]
    K --> P[广告管理 /admin/ads]
```

### 组件层次结构
```mermaid
graph TD
    A[App] --> B[Layout]
    B --> C[Header]
    B --> D[Main Content]
    B --> E[Footer]
    
    C --> C1[Logo]
    C --> C2[Navigation]
    C --> C3[User Menu]
    
    D --> D1[Page Components]
    D1 --> D11[HomePage]
    D1 --> D12[SearchPage]
    D1 --> D13[ProfilePage]
    
    D11 --> D111[PetCard]
    D11 --> D112[FilterBar]
    D11 --> D113[AdBanner]
    
    D111 --> D1111[PetImage]
    D111 --> D1112[PetInfo]
    D111 --> D1113[ActionButtons]
    
    E --> E1[Links]
    E --> E2[Copyright]
```

## 🔄 业务流程图

### 用户注册流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API网关
    participant C as 云函数
    participant D as 数据库

    U->>F: 访问注册页面
    F->>U: 显示注册表单
    U->>F: 填写注册信息
    F->>F: 前端表单验证
    alt 验证失败
        F->>U: 显示错误提示
    else 验证通过
        F->>A: 提交注册请求
        A->>C: 调用用户注册函数
        C->>D: 检查用户名是否存在
        D->>C: 返回检查结果
        alt 用户名已存在
            C->>A: 返回错误信息
            A->>F: 传递错误信息
            F->>U: 显示用户名已存在
        else 用户名可用
            C->>C: 密码加密处理
            C->>D: 创建用户记录
            D->>C: 返回创建结果
            C->>A: 返回注册成功
            A->>F: 传递成功信息
            F->>F: 自动登录处理
            F->>U: 跳转到个人中心
        end
    end
```

### 宠物发布流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant S as 云存储
    participant A as API网关
    participant C as 云函数
    participant D as 数据库

    U->>F: 点击发布按钮
    F->>U: 显示发布表单
    U->>F: 填写宠物信息
    U->>F: 选择上传图片
    
    loop 图片上传
        F->>S: 上传图片文件
        S->>F: 返回图片URL
    end
    
    U->>F: 点击发布按钮
    F->>F: 表单验证
    F->>A: 提交发布请求
    A->>C: 调用创建帖子函数
    C->>C: 计算内容质量评分
    C->>D: 保存帖子信息
    D->>C: 返回保存结果
    C->>C: 创建发布通知
    C->>A: 返回发布成功
    A->>F: 传递成功信息
    F->>U: 显示发布成功提示
    F->>F: 跳转到个人主页
```

### 活动投票流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API网关
    participant C as 云函数
    participant D as 数据库

    U->>F: 访问活动页面
    F->>A: 获取活动列表
    A->>C: 调用获取活动函数
    C->>D: 查询活动数据
    D->>C: 返回活动列表
    C->>A: 返回活动数据
    A->>F: 传递活动数据
    F->>U: 显示活动列表
    
    U->>F: 选择参与活动
    F->>U: 显示活动详情
    U->>F: 选择投票选项
    F->>A: 提交投票请求
    A->>C: 调用投票函数
    C->>D: 检查是否已投票
    D->>C: 返回检查结果
    
    alt 已经投票
        C->>A: 返回已投票提示
        A->>F: 传递提示信息
        F->>U: 显示已投票提示
    else 未投票
        C->>D: 记录投票信息
        C->>D: 更新活动统计
        C->>D: 记录参与者信息
        D->>C: 返回更新结果
        C->>A: 返回投票成功
        A->>F: 传递成功信息
        F->>U: 显示投票结果
        
        opt 允许评论
            U->>F: 发表评论
            F->>A: 提交评论请求
            A->>C: 调用评论函数
            C->>D: 保存评论信息
            D->>C: 返回保存结果
            C->>A: 返回评论成功
            A->>F: 传递成功信息
            F->>U: 显示评论成功
        end
    end
```

## 🗄️ 数据库设计图

### 数据库ER图
```mermaid
erDiagram
    USERS {
        string _id PK
        string username UK
        string nickname
        string avatar_url
        string phone
        string location
        string bio
        number reputation_score
        number posts_count
        number successful_adoptions
        datetime created_at
        datetime last_active
        string status
    }
    
    PETS {
        string _id PK
        string title
        text description
        string user_id FK
        string category
        string breed
        string age
        string gender
        string location
        number price
        array images
        object contact_info
        string status
        string priority_level
        number quality_score
        number views
        number likes
        number bookmarks
        datetime created_at
        datetime updated_at
    }
    
    ACTIVITIES {
        string _id PK
        string title
        text description
        string type
        string status
        array options
        object settings
        object stats
        datetime start_time
        datetime end_time
        string created_by FK
        datetime created_at
    }
    
    ACTIVITY_VOTES {
        string _id PK
        string activity_id FK
        string user_id FK
        string option_id
        datetime created_at
    }
    
    ACTIVITY_COMMENTS {
        string _id PK
        string activity_id FK
        string user_id FK
        text content
        datetime created_at
    }
    
    NOTIFICATIONS {
        string _id PK
        string type
        string recipient_id FK
        string sender_id FK
        string post_id FK
        string message
        object data
        boolean is_read
        datetime created_at
    }
    
    BOOKMARKS {
        string _id PK
        string user_id FK
        string post_id FK
        datetime created_at
    }
    
    ADMINS {
        string _id PK
        string username UK
        string password
        string role
        array permissions
        datetime created_at
        datetime last_login
        string status
    }
    
    SYSTEM_CONFIG {
        string _id PK
        string key UK
        object value
        string description
        datetime updated_at
    }

    USERS ||--o{ PETS : "发布"
    USERS ||--o{ ACTIVITY_VOTES : "投票"
    USERS ||--o{ ACTIVITY_COMMENTS : "评论"
    USERS ||--o{ NOTIFICATIONS : "接收"
    USERS ||--o{ NOTIFICATIONS : "发送"
    USERS ||--o{ BOOKMARKS : "收藏"
    ACTIVITIES ||--o{ ACTIVITY_VOTES : "包含"
    ACTIVITIES ||--o{ ACTIVITY_COMMENTS : "包含"
    PETS ||--o{ NOTIFICATIONS : "关联"
    PETS ||--o{ BOOKMARKS : "被收藏"
    ADMINS ||--o{ ACTIVITIES : "创建"
```

### 数据关系图
```mermaid
graph TD
    A[用户 Users] --> B[宠物帖子 Pets]
    A --> C[活动投票 Activity_Votes]
    A --> D[活动评论 Activity_Comments]
    A --> E[收藏 Bookmarks]
    A --> F[通知接收 Notifications]
    A --> G[通知发送 Notifications]
    
    H[活动 Activities] --> C
    H --> D
    
    B --> E
    B --> F
    
    I[管理员 Admins] --> H
    I --> J[系统配置 System_Config]
    
    K[帖子质量评分 Post_Quality_Scores] --> B
    L[活动参与者 Activity_Participants] --> H
    L --> A
```

## 🔐 安全架构图

### 权限控制架构
```mermaid
graph TD
    A[用户请求] --> B{身份验证}
    B -->|未登录| C[匿名访问权限]
    B -->|已登录| D[用户权限验证]
    
    D --> E{角色检查}
    E -->|普通用户| F[用户权限]
    E -->|管理员| G[管理员权限]
    E -->|超级管理员| H[超级管理员权限]
    
    C --> I[公开资源访问]
    F --> J[个人数据操作]
    F --> K[内容发布/编辑]
    F --> L[社区互动]
    
    G --> M[内容审核]
    G --> N[用户管理]
    G --> O[活动管理]
    
    H --> P[系统配置]
    H --> Q[管理员管理]
    H --> R[数据备份/恢复]
    
    I --> S[资源返回]
    J --> S
    K --> S
    L --> S
    M --> S
    N --> S
    O --> S
    P --> S
    Q --> S
    R --> S
```

### 数据安全流程
```mermaid
sequenceDiagram
    participant C as 客户端
    participant G as API网关
    participant A as 认证服务
    participant F as 云函数
    participant D as 数据库
    participant L as 日志服务

    C->>G: 发送请求
    G->>G: 请求频率限制检查
    G->>A: 验证用户身份
    A->>A: JWT Token验证
    A->>G: 返回用户信息
    G->>F: 转发请求
    F->>F: 参数验证和清理
    F->>F: 权限检查
    F->>D: 数据库操作
    D->>F: 返回数据
    F->>F: 数据脱敏处理
    F->>L: 记录操作日志
    F->>G: 返回响应
    G->>C: 返回结果
```

## 📊 监控架构图

### 监控体系架构
```mermaid
graph TB
    subgraph "数据采集层"
        A[前端监控SDK]
        B[云函数监控]
        C[数据库监控]
        D[CDN监控]
    end
    
    subgraph "数据处理层"
        E[日志聚合]
        F[指标计算]
        G[异常检测]
        H[告警规则]
    end
    
    subgraph "存储层"
        I[时序数据库]
        J[日志存储]
        K[配置存储]
    end
    
    subgraph "展示层"
        L[监控仪表板]
        M[告警通知]
        N[报表生成]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    
    E --> F
    E --> G
    F --> H
    G --> H
    
    F --> I
    E --> J
    H --> K
    
    I --> L
    J --> L
    K --> L
    
    H --> M
    L --> N
```

### 性能监控流程
```mermaid
sequenceDiagram
    participant U as 用户操作
    participant F as 前端应用
    participant M as 监控SDK
    participant A as API服务
    participant D as 监控后台
    participant N as 告警系统

    U->>F: 页面访问/操作
    F->>M: 性能数据采集
    M->>M: 数据预处理
    M->>D: 发送监控数据
    
    F->>A: API请求
    A->>A: 记录请求指标
    A->>D: 发送API监控数据
    A->>F: 返回响应
    
    D->>D: 数据聚合分析
    D->>D: 异常检测
    
    alt 发现异常
        D->>N: 触发告警
        N->>N: 告警规则匹配
        N->>N: 发送通知
    end
    
    D->>D: 生成监控报表
```
