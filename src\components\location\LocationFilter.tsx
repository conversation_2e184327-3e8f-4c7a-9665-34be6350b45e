'use client';

import React, { useState, useRef, useEffect } from 'react';
import { MapPin, Search, X, Clock, Trash2 } from 'lucide-react';
import { locationHistoryManager, HistoryItem } from '@/utils/historyManager';

interface LocationFilterProps {
  selectedLocation: string;
  onLocationChange: (location: string) => void;
}

const LocationFilter: React.FC<LocationFilterProps> = ({
  selectedLocation,
  onLocationChange,
}) => {
  const [inputValue, setInputValue] = useState(selectedLocation);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [history, setHistory] = useState<HistoryItem[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  // 地理位置建议（根据用户输入动态生成）
  const commonLocations = [
    '湖南省',
    '湖南省长沙市',
    '湖南省郴州市',
    '湖南省株洲市',
    '湖南省湘潭市',
    '湖南省衡阳市',
    '湖南省邵阳市',
    '湖南省岳阳市',
    '湖南省常德市',
    '湖南省张家界市',
    '湖南省益阳市',
    '湖南省郴州市临武县',
    '湖南省郴州市桂阳县',
    '湖南省郴州市永兴县',
    '湖南省长沙市芙蓉区',
    '湖南省长沙市天心区',
    '湖南省长沙市岳麓区',
    '湖南省长沙市开福区',
    '湖南省长沙市雨花区',
    '广东省',
    '广东省广州市',
    '广东省深圳市',
    '广东省珠海市',
    '广东省佛山市',
    '广东省东莞市',
    '浙江省',
    '浙江省杭州市',
    '浙江省宁波市',
    '浙江省温州市',
    '江苏省',
    '江苏省南京市',
    '江苏省苏州市',
    '江苏省无锡市',
    '北京市',
    '上海市',
    '重庆市',
    '天津市',
  ];

  // 加载历史记录
  useEffect(() => {
    setHistory(locationHistoryManager.getHistory());
  }, []);

  // 根据输入内容过滤建议
  const filterSuggestions = (input: string) => {
    if (!input.trim()) {
      return []; // 没有输入时不显示建议
    }

    return commonLocations.filter(location =>
      location.includes(input.trim())
    ).slice(0, 10);
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    // 如果输入为空，立即触发搜索以显示所有帖子
    if (!value.trim()) {
      onLocationChange('');
      setShowSuggestions(false);
      return;
    }

    const filtered = filterSuggestions(value);
    setSuggestions(filtered);
    setShowSuggestions(filtered.length > 0);
  };

  // 处理建议选择
  const handleSuggestionClick = (location: string) => {
    setInputValue(location);
    onLocationChange(location);
    setShowSuggestions(false);
    setShowHistory(false);
    // 添加到历史记录
    locationHistoryManager.addItem(location);
    setHistory(locationHistoryManager.getHistory());
  };

  // 处理历史记录选择
  const handleHistoryClick = (location: string) => {
    setInputValue(location);
    onLocationChange(location);
    setShowHistory(false);
    setShowSuggestions(false);
    // 重新添加到历史记录（更新时间戳）
    locationHistoryManager.addItem(location);
    setHistory(locationHistoryManager.getHistory());
  };

  // 删除历史记录项
  const handleRemoveHistory = (location: string, e: React.MouseEvent) => {
    e.stopPropagation();
    locationHistoryManager.removeItem(location);
    setHistory(locationHistoryManager.getHistory());
  };

  // 清空历史记录
  const handleClearHistory = () => {
    locationHistoryManager.clearHistory();
    setHistory([]);
  };

  // 处理搜索
  const handleSearch = () => {
    const trimmedValue = inputValue.trim();
    if (trimmedValue) {
      // 添加到历史记录
      locationHistoryManager.addItem(trimmedValue);
      setHistory(locationHistoryManager.getHistory());
    }
    onLocationChange(trimmedValue);
    setShowSuggestions(false);
    setShowHistory(false);
  };

  // 处理回车键
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
      setShowHistory(false);
    }
  };

  // 清除输入
  const handleClear = () => {
    setInputValue('');
    onLocationChange(''); // 立即触发清空筛选
    setShowSuggestions(false);
    setShowHistory(false);
    inputRef.current?.focus();
  };

  // 点击外部关闭建议和历史记录
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (inputRef.current && !inputRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
        setShowHistory(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 同步外部变化
  useEffect(() => {
    setInputValue(selectedLocation);
  }, [selectedLocation]);

  return (
    <div className="relative">
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MapPin className="h-4 w-4 text-gray-400" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyPress}
          onFocus={() => {
            if (inputValue.trim()) {
              // 有输入内容时显示建议
              const filtered = filterSuggestions(inputValue);
              setSuggestions(filtered);
              setShowSuggestions(filtered.length > 0);
              setShowHistory(false);
            } else {
              // 无输入内容时显示历史记录
              setShowHistory(history.length > 0);
              setShowSuggestions(false);
            }
          }}
          placeholder="请输入地理位置进行精准匹配"
          className="block w-full pl-10 pr-20 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
        />
        
        <div className="absolute inset-y-0 right-0 flex items-center">
          {inputValue && (
            <button
              onClick={handleClear}
              className="p-1 mr-1 text-gray-400 hover:text-gray-600 rounded"
            >
              <X className="h-4 w-4" />
            </button>
          )}
          
          <button
            onClick={handleSearch}
            className="px-3 py-1 mr-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm"
          >
            <Search className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* 建议列表 */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {suggestions.map((location, index) => (
            <button
              key={index}
              onClick={() => handleSuggestionClick(location)}
              className="w-full px-4 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none text-sm border-b border-gray-100 last:border-b-0"
            >
              <div className="flex items-center">
                <MapPin className="h-3 w-3 text-gray-400 mr-2 flex-shrink-0" />
                <span className="text-gray-900">{location}</span>
              </div>
            </button>
          ))}
        </div>
      )}

      {/* 历史记录列表 */}
      {showHistory && history.length > 0 && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          <div className="p-2">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs text-gray-500 font-medium">搜索历史</span>
              <button
                onClick={handleClearHistory}
                className="text-xs text-gray-400 hover:text-gray-600 flex items-center space-x-1"
              >
                <Trash2 className="h-3 w-3" />
                <span>清空</span>
              </button>
            </div>

            {history.map((item, index) => (
              <button
                key={index}
                onClick={() => handleHistoryClick(item.value)}
                className="w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none text-sm rounded-md group flex items-center justify-between"
              >
                <div className="flex items-center space-x-2 flex-1 min-w-0">
                  <Clock className="h-3 w-3 text-gray-400 flex-shrink-0" />
                  <span className="text-gray-700 truncate">{item.label}</span>
                </div>
                <button
                  onClick={(e) => handleRemoveHistory(item.value, e)}
                  className="opacity-0 group-hover:opacity-100 text-gray-400 hover:text-gray-600 transition-opacity p-1"
                >
                  <X className="h-3 w-3" />
                </button>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default LocationFilter;
