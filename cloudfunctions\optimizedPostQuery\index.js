const cloudbase = require('@cloudbase/node-sdk');

const app = cloudbase.init({
  env: cloudbase.SYMBOL_CURRENT_ENV
});

const db = app.database();

// 增强缓存管理
const cache = new Map();
const CACHE_TTL = {
  posts: 2 * 60 * 1000,      // 帖子缓存2分钟
  users: 10 * 60 * 1000,     // 用户信息缓存10分钟
  categories: 30 * 60 * 1000, // 分类信息缓存30分钟
  hot_posts: 1 * 60 * 1000   // 热门帖子缓存1分钟
};

// 缓存统计
const cacheStats = {
  hits: 0,
  misses: 0,
  sets: 0,
  evictions: 0
};

// 生成缓存键
function generateCacheKey(params) {
  return JSON.stringify(params);
}

// 获取缓存（增强版）
function getCache(key, cacheType = 'posts') {
  const cached = cache.get(key);
  const ttl = CACHE_TTL[cacheType] || CACHE_TTL.posts;

  if (cached && Date.now() - cached.timestamp < ttl) {
    cacheStats.hits++;
    // 更新访问时间（LRU策略）
    cached.lastAccess = Date.now();
    return cached.data;
  }

  if (cached) {
    // 过期缓存，删除
    cache.delete(key);
  }

  cacheStats.misses++;
  return null;
}

// 设置缓存（增强版）
function setCache(key, data, cacheType = 'posts') {
  // 缓存大小限制
  if (cache.size >= 1000) {
    evictOldestCache();
  }

  cache.set(key, {
    data,
    timestamp: Date.now(),
    lastAccess: Date.now(),
    type: cacheType
  });

  cacheStats.sets++;
}

// 缓存淘汰策略（LRU）
function evictOldestCache() {
  let oldestKey = null;
  let oldestTime = Date.now();

  for (const [key, value] of cache.entries()) {
    if (value.lastAccess < oldestTime) {
      oldestTime = value.lastAccess;
      oldestKey = key;
    }
  }

  if (oldestKey) {
    cache.delete(oldestKey);
    cacheStats.evictions++;
  }
}

// 清理过期缓存
function cleanExpiredCache() {
  const now = Date.now();
  for (const [key, value] of cache.entries()) {
    if (now - value.timestamp >= CACHE_TTL) {
      cache.delete(key);
    }
  }
}

// 构建查询条件
function buildQuery(params) {
  const query = {};
  
  // 分类筛选
  if (params.category) {
    query.category = params.category;
  }
  
  // 帖子类型筛选
  if (params.type && params.type !== 'all') {
    query.type = params.type;
  }
  
  // 地理位置筛选
  if (params.location) {
    // 支持模糊匹配
    query.location = db.RegExp({
      regexp: params.location,
      options: 'i'
    });
  }
  
  // 状态筛选（只显示已发布的帖子）
  query.status = 'published';
  
  return query;
}

// 构建排序条件
function buildSort(sortBy) {
  switch (sortBy) {
    case 'created_at':
      return { created_at: -1 };
    case 'likes_count':
      return { likes_count: -1, created_at: -1 };
    case 'wants_count':
      return { wants_count: -1, created_at: -1 };
    case 'avg_rating':
      return { avg_rating: -1, created_at: -1 };
    case 'priority':
      return { priority_score: -1, created_at: -1 };
    default:
      return { created_at: -1 };
  }
}

// 优化的帖子查询函数
async function optimizedPostQuery(params) {
  try {
    const {
      page = 1,
      limit = 20,
      sortBy = 'created_at',
      category,
      type,
      location,
      includeUserInfo = true,
      includeCategoryInfo = true
    } = params;

    // 生成缓存键
    const cacheKey = generateCacheKey({
      page,
      limit,
      sortBy,
      category,
      type,
      location,
      includeUserInfo,
      includeCategoryInfo
    });

    // 检查缓存
    const cached = getCache(cacheKey);
    if (cached) {
      return {
        success: true,
        data: cached.posts,
        pagination: cached.pagination,
        fromCache: true
      };
    }

    // 构建查询条件
    const query = buildQuery({ category, type, location });
    const sort = buildSort(sortBy);

    // 计算分页
    const skip = (page - 1) * limit;

    // 执行查询
    const postsQuery = db.collection('posts')
      .where(query)
      .orderBy(Object.keys(sort)[0], Object.values(sort)[0] === 1 ? 'asc' : 'desc');

    // 如果有多个排序字段，添加次要排序
    const sortKeys = Object.keys(sort);
    if (sortKeys.length > 1) {
      for (let i = 1; i < sortKeys.length; i++) {
        postsQuery.orderBy(sortKeys[i], Object.values(sort)[i] === 1 ? 'asc' : 'desc');
      }
    }

    // 获取总数和分页数据
    const [countResult, postsResult] = await Promise.all([
      db.collection('posts').where(query).count(),
      postsQuery.skip(skip).limit(limit).get()
    ]);

    let posts = postsResult.data || [];
    const total = countResult.total || 0;

    // 批量获取用户信息
    if (includeUserInfo && posts.length > 0) {
      const userIds = [...new Set(posts.map(post => post.user_id).filter(Boolean))];
      if (userIds.length > 0) {
        const usersResult = await db.collection('users')
          .where({
            _id: db.command.in(userIds)
          })
          .field({
            _id: true,
            nickname: true,
            avatar: true,
            avatar_url: true,
            verified: true
          })
          .get();

        const usersMap = new Map();
        usersResult.data.forEach(user => {
          // 优先使用 avatar_url，如果没有则使用 avatar
          const avatar = user.avatar_url || user.avatar || '';
          usersMap.set(user._id, {
            ...user,
            avatar
          });
        });

        // 将用户信息附加到帖子
        posts = posts.map(post => ({
          ...post,
          user: usersMap.get(post.user_id) || null
        }));
      }
    }

    // 批量获取分类信息
    if (includeCategoryInfo && posts.length > 0) {
      const categoryIds = [...new Set(posts.map(post => post.category).filter(Boolean))];
      if (categoryIds.length > 0) {
        const categoriesResult = await db.collection('categories')
          .where({
            id: db.command.in(categoryIds)
          })
          .get();

        const categoriesMap = new Map();
        categoriesResult.data.forEach(category => {
          categoriesMap.set(category.id, category);
        });

        // 将分类信息附加到帖子
        posts = posts.map(post => ({
          ...post,
          categoryInfo: categoriesMap.get(post.category) || null
        }));
      }
    }

    // 构建分页信息
    const pagination = {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasMore: page * limit < total,
      hasPrev: page > 1
    };

    // 缓存结果
    setCache(cacheKey, { posts, pagination });

    // 定期清理过期缓存
    if (Math.random() < 0.1) { // 10% 概率清理
      cleanExpiredCache();
    }

    return {
      success: true,
      data: posts,
      pagination,
      fromCache: false
    };

  } catch (error) {
    console.error('优化帖子查询失败:', error);
    return {
      success: false,
      message: error.message || '查询失败',
      error: error
    };
  }
}

// 云函数入口
exports.main = async (event, context) => {
  const { action = 'query', ...params } = event;

  switch (action) {
    case 'query':
      return await optimizedPostQuery(params);
    
    case 'clearCache':
      cache.clear();
      return {
        success: true,
        message: '缓存已清理'
      };
    
    case 'getCacheStats':
      return {
        success: true,
        data: {
          cacheSize: cache.size,
          cacheKeys: Array.from(cache.keys())
        }
      };
    
    default:
      return {
        success: false,
        message: '不支持的操作'
      };
  }
};
