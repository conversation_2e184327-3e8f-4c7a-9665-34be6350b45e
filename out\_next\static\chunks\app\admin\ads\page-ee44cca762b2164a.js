(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[463],{69371:function(e,t,s){Promise.resolve().then(s.bind(s,4803))},4803:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return h}});var a=s(57437),r=s(2265),l=s(43836),i=s(42548);let d=r.forwardRef(function(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}),c=r.forwardRef(function(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),n=r.forwardRef(function(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"}))});var x=s(12594);let o=r.forwardRef(function(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 5.25v13.5m-7.5-13.5v13.5"}))});var m=s(61601);function h(){let[e,t]=(0,r.useState)([]),[s,h]=(0,r.useState)([]),[p,u]=(0,r.useState)(!0),[g,j]=(0,r.useState)("ads"),[b,v]=(0,r.useState)(!1);(0,r.useEffect)(()=>{y()},[]);let y=async()=>{u(!0);try{await Promise.all([N(),f()])}catch(e){console.error("加载数据失败:",e)}finally{u(!1)}},N=async()=>{try{t([{_id:"ad_001",title:"优质宠物用品推荐",advertiser_id:"advertiser_001",advertiser_name:"宠物之家商城",position_id:"home_banner",position_name:"首页横幅广告",ad_type:"banner",content:"为您的爱宠提供最好的生活用品，健康快乐每一天！全场8折优惠中。",target_url:"https://example.com/pet-products",start_date:"2025-01-01T00:00:00.000Z",end_date:"2025-03-31T23:59:59.000Z",status:"active",priority:1,budget:5e3,spent:1250.5,impressions:15420,clicks:342,ctr:.0222,created_at:"2025-01-01T00:00:00.000Z"},{_id:"ad_002",title:"专业宠物医院",advertiser_id:"advertiser_002",advertiser_name:"爱宠医疗中心",position_id:"home_feed",position_name:"首页信息流广告",ad_type:"feed",content:"24小时宠物医疗服务，专业医师团队，让您的爱宠健康无忧。",target_url:"https://example.com/pet-hospital",start_date:"2025-01-10T00:00:00.000Z",end_date:"2025-02-28T23:59:59.000Z",status:"active",priority:2,budget:3e3,spent:890.25,impressions:8750,clicks:156,ctr:.0178,created_at:"2025-01-10T00:00:00.000Z"},{_id:"ad_003",title:"宠物美容服务",advertiser_id:"advertiser_003",advertiser_name:"美宠工坊",position_id:"home_feed",position_name:"首页信息流广告",ad_type:"feed",content:"专业宠物美容，让您的爱宠更加美丽动人。新客户首次服务7折。",target_url:"https://example.com/pet-grooming",start_date:"2025-01-15T00:00:00.000Z",end_date:"2025-04-15T23:59:59.000Z",status:"paused",priority:3,budget:2e3,spent:450.75,impressions:4200,clicks:89,ctr:.0212,created_at:"2025-01-15T00:00:00.000Z"}])}catch(e){console.error("加载广告失败:",e)}},f=async()=>{try{h([{position_id:"home_banner",name:"首页横幅广告",page:"home",location:"top",width:728,height:90,ad_type:"banner",max_ads:3,rotation_interval:5e3,status:"active"},{position_id:"home_feed",name:"首页信息流广告",page:"home",location:"feed",width:300,height:200,ad_type:"feed",max_ads:5,rotation_interval:0,status:"active"},{position_id:"sidebar_banner",name:"侧边栏广告",page:"all",location:"sidebar",width:300,height:250,ad_type:"banner",max_ads:2,rotation_interval:8e3,status:"inactive"}])}catch(e){console.error("加载广告位失败:",e)}},w=e=>{let t={active:{label:"投放中",color:"bg-green-100 text-green-800"},paused:{label:"已暂停",color:"bg-yellow-100 text-yellow-800"},expired:{label:"已过期",color:"bg-red-100 text-red-800"},pending:{label:"待审核",color:"bg-blue-100 text-blue-800"},inactive:{label:"未启用",color:"bg-gray-100 text-gray-800"}},s=t[e]||t.pending;return(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(s.color),children:s.label})},k=e=>"\xa5".concat(e.toFixed(2)),_=e=>"".concat((100*e).toFixed(2),"%");return p?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"广告管理"}),(0,a.jsx)("p",{className:"text-gray-600",children:"管理平台广告投放和收益"})]}),(0,a.jsxs)("button",{onClick:()=>v(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2",children:[(0,a.jsx)(l.Z,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"创建广告"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,a.jsx)(i.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总展示量"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.reduce((e,t)=>e+t.impressions,0).toLocaleString()})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,a.jsx)(d,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总点击量"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.reduce((e,t)=>e+t.clicks,0).toLocaleString()})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,a.jsx)(c,{className:"h-6 w-6 text-yellow-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总收益"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:k(e.reduce((e,t)=>e+t.spent,0))})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,a.jsx)(n,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"活跃广告"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.filter(e=>"active"===e.status).length})]})]})})]}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{key:"ads",label:"广告列表",icon:i.Z},{key:"positions",label:"广告位管理",icon:d},{key:"statistics",label:"数据统计",icon:c}].map(e=>(0,a.jsxs)("button",{onClick:()=>j(e.key),className:"".concat(g===e.key?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"," whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2"),children:[(0,a.jsx)(e.icon,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:e.label})]},e.key))})}),"ads"===g&&(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"广告列表"})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"广告信息"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"广告位"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"数据"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"收益"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:0===e.length?(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:6,className:"px-6 py-12 text-center text-gray-500",children:"暂无广告数据"})}):e.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.image_url&&(0,a.jsx)("img",{src:e.image_url,alt:e.title,className:"h-10 w-10 rounded object-cover mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.advertiser_name})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.position_name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.ad_type})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:w(e.status)}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["展示: ",e.impressions.toLocaleString()]}),(0,a.jsxs)("div",{children:["点击: ",e.clicks.toLocaleString()]}),(0,a.jsxs)("div",{children:["CTR: ",_(e.ctr)]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["预算: ",k(e.budget)]}),(0,a.jsxs)("div",{children:["已花费: ",k(e.spent)]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-900",children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})}),(0,a.jsx)("button",{className:"text-green-600 hover:text-green-900",children:(0,a.jsx)(x.Z,{className:"h-4 w-4"})}),(0,a.jsx)("button",{className:"text-yellow-600 hover:text-yellow-900",children:"active"===e.status?(0,a.jsx)(o,{className:"h-4 w-4"}):(0,a.jsx)(n,{className:"h-4 w-4"})}),(0,a.jsx)("button",{className:"text-red-600 hover:text-red-900",children:(0,a.jsx)(m.Z,{className:"h-4 w-4"})})]})})]},e._id))})]})})]}),"positions"===g&&(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"广告位管理"})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"广告位信息"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"位置"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"尺寸"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"类型"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.page}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.location})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.width," \xd7 ",e.height]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.ad_type}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:w(e.status)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"text-green-600 hover:text-green-900",children:(0,a.jsx)(x.Z,{className:"h-4 w-4"})}),(0,a.jsx)("button",{className:"text-yellow-600 hover:text-yellow-900",children:"active"===e.status?(0,a.jsx)(o,{className:"h-4 w-4"}):(0,a.jsx)(n,{className:"h-4 w-4"})})]})})]},e._id))})]})})]}),"statistics"===g&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"用户体验策略"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"广告频率控制"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"每日最大展示次数"}),(0,a.jsx)("input",{type:"number",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",defaultValue:10,min:1,max:50})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最小展示间隔（分钟）"}),(0,a.jsx)("input",{type:"number",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",defaultValue:30,min:5,max:120})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"respectUserChoice",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",defaultChecked:!0}),(0,a.jsx)("label",{htmlFor:"respectUserChoice",className:"ml-2 block text-sm text-gray-900",children:"尊重用户隐藏选择"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"adaptiveFrequency",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",defaultChecked:!0}),(0,a.jsx)("label",{htmlFor:"adaptiveFrequency",className:"ml-2 block text-sm text-gray-900",children:"自适应展示频率"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"广告位策略"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"首页横幅"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"用户友好度：高"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"启用"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"信息流广告"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"用户友好度：中"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"启用"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"详情页底部"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"用户友好度：高"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"启用"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-red-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"启动弹窗"}),(0,a.jsx)("div",{className:"text-sm text-red-600",children:"用户友好度：低"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"禁用"}),(0,a.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-blue-600"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"用户体验建议"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"基于用户行为数据的智能推荐"})]}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:"保存设置"})]}),(0,a.jsxs)("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-800",children:"推荐"})]}),(0,a.jsx)("p",{className:"text-sm text-green-700 mt-1",children:"原生信息流广告，用户接受度高"})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-yellow-800",children:"谨慎"})]}),(0,a.jsx)("p",{className:"text-sm text-yellow-700 mt-1",children:"横幅广告需要控制频率"})]}),(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-red-800",children:"避免"})]}),(0,a.jsx)("p",{className:"text-sm text-red-700 mt-1",children:"弹窗广告容易引起用户反感"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"广告效果分析"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"92%"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"用户满意度"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"3.2%"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"平均点击率"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-yellow-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:"15s"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"平均停留时间"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:"8%"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"广告隐藏率"})]})]})]})]})]})}},42548:function(e,t,s){"use strict";var a=s(2265);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))});t.Z=r},12594:function(e,t,s){"use strict";var a=s(2265);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))});t.Z=r},43836:function(e,t,s){"use strict";var a=s(2265);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))});t.Z=r},61601:function(e,t,s){"use strict";var a=s(2265);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))});t.Z=r}},function(e){e.O(0,[971,117,744],function(){return e(e.s=69371)}),_N_E=e.O()}]);