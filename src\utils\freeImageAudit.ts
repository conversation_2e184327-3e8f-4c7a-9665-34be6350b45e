/**
 * 免费图片审核系统
 * 基于规则引擎，无需调用付费API
 */

export interface AuditResult {
  status: 'approved' | 'rejected' | 'manual_review';
  confidence: number;
  reason: string;
  details: any;
  riskLevel: 'low' | 'medium' | 'high';
}

export interface AuditRule {
  name: string;
  weight: number;
  check: (context: AuditContext) => Promise<RuleResult>;
}

export interface RuleResult {
  passed: boolean;
  confidence: number;
  reason?: string;
  details?: any;
}

export interface AuditContext {
  imageUrl: string;
  fileName: string;
  fileSize: number;
  userId: string;
  userHistory?: UserAuditHistory;
}

export interface UserAuditHistory {
  totalUploads: number;
  rejectedCount: number;
  approvedCount: number;
  recentViolations: number;
  trustScore: number; // 0-1
}

class FreeImageAuditEngine {
  private rules: AuditRule[] = [];
  private riskKeywords: Map<string, number> = new Map();
  
  constructor() {
    this.initializeRules();
    this.initializeKeywords();
  }

  /**
   * 初始化审核规则
   */
  private initializeRules(): void {
    this.rules = [
      {
        name: 'file_format_check',
        weight: 0.1,
        check: this.checkFileFormat.bind(this)
      },
      {
        name: 'file_size_check', 
        weight: 0.1,
        check: this.checkFileSize.bind(this)
      },
      {
        name: 'filename_analysis',
        weight: 0.3,
        check: this.analyzeFileName.bind(this)
      },
      {
        name: 'url_analysis',
        weight: 0.2,
        check: this.analyzeUrl.bind(this)
      },
      {
        name: 'user_trust_score',
        weight: 0.2,
        check: this.checkUserTrustScore.bind(this)
      },
      {
        name: 'upload_frequency',
        weight: 0.1,
        check: this.checkUploadFrequency.bind(this)
      }
    ];
  }

  /**
   * 初始化风险关键词库
   */
  private initializeKeywords(): void {
    // 高风险关键词（直接拒绝）
    const highRiskKeywords = [
      'blood', 'violence', 'weapon', 'gun', 'knife', 'terror', 'bomb',
      'drug', 'cocaine', 'heroin', 'marijuana', 'abuse', 'torture',
      'nude', 'naked', 'porn', 'sex', 'adult', 'xxx', 'nsfw',
      'dead', 'death', 'kill', 'murder', 'suicide', 'harm'
    ];
    
    // 中风险关键词（需要审核）
    const mediumRiskKeywords = [
      'fight', 'angry', 'aggressive', 'bite', 'attack', 'wound',
      'sick', 'disease', 'injured', 'bleeding', 'pain', 'suffer',
      'illegal', 'stolen', 'fake', 'scam', 'fraud', 'cheat',
      'money', 'cash', 'price', 'cheap', 'expensive', 'urgent'
    ];
    
    // 宠物相关风险词
    const petRiskKeywords = [
      'puppy-mill', 'backyard-breeder', 'no-papers', 'unregistered',
      'pregnant', 'breeding', 'stud', 'mate', 'litter', 'newborn',
      'rescue', 'abandoned', 'stray', 'found', 'lost'
    ];
    
    // 设置权重
    highRiskKeywords.forEach(keyword => this.riskKeywords.set(keyword, 0.9));
    mediumRiskKeywords.forEach(keyword => this.riskKeywords.set(keyword, 0.6));
    petRiskKeywords.forEach(keyword => this.riskKeywords.set(keyword, 0.4));
  }

  /**
   * 执行图片审核
   */
  async audit(context: AuditContext): Promise<AuditResult> {
    console.log(`开始免费审核: ${context.imageUrl}`);
    
    try {
      const ruleResults: Array<{rule: AuditRule, result: RuleResult}> = [];
      
      // 执行所有规则
      for (const rule of this.rules) {
        try {
          const result = await rule.check(context);
          ruleResults.push({ rule, result });
        } catch (error) {
          console.warn(`规则 ${rule.name} 执行失败:`, error);
          // 规则失败时给予中性分数
          ruleResults.push({
            rule,
            result: { passed: true, confidence: 0.5, reason: 'rule_execution_failed' }
          });
        }
      }
      
      // 计算综合分数
      const finalResult = this.calculateFinalScore(ruleResults);
      
      console.log(`审核完成: ${context.imageUrl}, 结果: ${finalResult.status}`);
      return finalResult;
      
    } catch (error) {
      console.error('审核过程失败:', error);

      // 审核失败时的保守策略
      return {
        status: 'manual_review',
        confidence: 0.5,
        reason: 'audit_system_error',
        details: { error: error instanceof Error ? error.message : String(error) },
        riskLevel: 'medium'
      };
    }
  }

  /**
   * 检查文件格式
   */
  private async checkFileFormat(context: AuditContext): Promise<RuleResult> {
    const { fileName } = context;
    const allowedFormats = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
    
    const hasValidFormat = allowedFormats.some(format => 
      fileName.toLowerCase().endsWith(format)
    );
    
    return {
      passed: hasValidFormat,
      confidence: hasValidFormat ? 0.95 : 0.1,
      reason: hasValidFormat ? 'valid_image_format' : 'invalid_image_format'
    };
  }

  /**
   * 检查文件大小
   */
  private async checkFileSize(context: AuditContext): Promise<RuleResult> {
    const { fileSize } = context;
    const maxSize = 10 * 1024 * 1024; // 10MB
    const minSize = 1024; // 1KB
    
    const validSize = fileSize >= minSize && fileSize <= maxSize;
    
    return {
      passed: validSize,
      confidence: validSize ? 0.9 : 0.2,
      reason: validSize ? 'valid_file_size' : 'invalid_file_size',
      details: { fileSize, maxSize, minSize }
    };
  }

  /**
   * 分析文件名
   */
  private async analyzeFileName(context: AuditContext): Promise<RuleResult> {
    const { fileName } = context;
    const lowerFileName = fileName.toLowerCase();
    
    let riskScore = 0;
    const matchedKeywords: string[] = [];
    
    // 检查风险关键词
    for (const [keyword, weight] of Array.from(this.riskKeywords.entries())) {
      if (lowerFileName.includes(keyword)) {
        riskScore = Math.max(riskScore, weight);
        matchedKeywords.push(keyword);
      }
    }
    
    // 检查可疑模式
    const suspiciousPatterns = [
      /\d{10,}/, // 长数字串（可能是时间戳或ID）
      /[a-f0-9]{32}/, // MD5哈希
      /temp|tmp|cache/, // 临时文件
      /test|demo|sample/, // 测试文件
    ];
    
    const hasSuspiciousPattern = suspiciousPatterns.some(pattern => 
      pattern.test(lowerFileName)
    );
    
    if (hasSuspiciousPattern) {
      riskScore = Math.max(riskScore, 0.3);
    }
    
    const confidence = 1 - riskScore;
    const passed = riskScore < 0.7;
    
    return {
      passed,
      confidence,
      reason: matchedKeywords.length > 0 ? 
        `suspicious_keywords: ${matchedKeywords.join(', ')}` : 
        'filename_analysis_passed',
      details: { riskScore, matchedKeywords, hasSuspiciousPattern }
    };
  }

  /**
   * 分析URL
   */
  private async analyzeUrl(context: AuditContext): Promise<RuleResult> {
    const { imageUrl } = context;
    const lowerUrl = imageUrl.toLowerCase();
    
    // 检查URL中的风险关键词
    let riskScore = 0;
    const matchedKeywords: string[] = [];
    
    for (const [keyword, weight] of Array.from(this.riskKeywords.entries())) {
      if (lowerUrl.includes(keyword)) {
        riskScore = Math.max(riskScore, weight * 0.8); // URL权重稍低
        matchedKeywords.push(keyword);
      }
    }
    
    // 检查可疑域名模式
    const suspiciousDomains = [
      'temp', 'tmp', 'test', 'demo', 'localhost', '127.0.0.1',
      'adult', 'xxx', 'porn', 'sex', 'violence', 'illegal'
    ];
    
    const hasSuspiciousDomain = suspiciousDomains.some(domain => 
      lowerUrl.includes(domain)
    );
    
    if (hasSuspiciousDomain) {
      riskScore = Math.max(riskScore, 0.8);
    }
    
    const confidence = 1 - riskScore;
    const passed = riskScore < 0.6;
    
    return {
      passed,
      confidence,
      reason: matchedKeywords.length > 0 ? 
        `suspicious_url_keywords: ${matchedKeywords.join(', ')}` : 
        'url_analysis_passed',
      details: { riskScore, matchedKeywords, hasSuspiciousDomain }
    };
  }

  /**
   * 检查用户信任分数
   */
  private async checkUserTrustScore(context: AuditContext): Promise<RuleResult> {
    const { userHistory } = context;
    
    if (!userHistory) {
      // 新用户给予中等信任度
      return {
        passed: true,
        confidence: 0.6,
        reason: 'new_user_default_trust'
      };
    }
    
    const { trustScore, rejectedCount, totalUploads, recentViolations } = userHistory;
    
    // 计算用户可信度
    let userConfidence = trustScore;
    
    // 如果最近有违规，降低信任度
    if (recentViolations > 0) {
      userConfidence *= (1 - recentViolations * 0.2);
    }
    
    // 如果拒绝率过高，降低信任度
    const rejectionRate = totalUploads > 0 ? rejectedCount / totalUploads : 0;
    if (rejectionRate > 0.3) {
      userConfidence *= 0.5;
    }
    
    const passed = userConfidence > 0.3;
    
    return {
      passed,
      confidence: userConfidence,
      reason: passed ? 'user_trusted' : 'user_low_trust',
      details: { trustScore, rejectionRate, recentViolations }
    };
  }

  /**
   * 检查上传频率
   */
  private async checkUploadFrequency(context: AuditContext): Promise<RuleResult> {
    // 这里可以实现上传频率检查
    // 例如：检查用户在短时间内是否上传了过多图片
    
    // 简化实现：总是通过
    return {
      passed: true,
      confidence: 0.8,
      reason: 'upload_frequency_normal'
    };
  }

  /**
   * 计算最终分数
   */
  private calculateFinalScore(ruleResults: Array<{rule: AuditRule, result: RuleResult}>): AuditResult {
    let totalWeight = 0;
    let weightedScore = 0;
    let hasRejection = false;
    let highestRisk = 0;
    
    const details: any = {};
    
    for (const { rule, result } of ruleResults) {
      totalWeight += rule.weight;
      weightedScore += result.confidence * rule.weight;
      
      details[rule.name] = result;
      
      // 如果有规则明确拒绝且置信度高，直接拒绝
      if (!result.passed && result.confidence > 0.8) {
        hasRejection = true;
      }
      
      // 记录最高风险分数
      highestRisk = Math.max(highestRisk, 1 - result.confidence);
    }
    
    const finalConfidence = weightedScore / totalWeight;
    
    // 决策逻辑
    let status: 'approved' | 'rejected' | 'manual_review';
    let reason: string;
    let riskLevel: 'low' | 'medium' | 'high';
    
    if (hasRejection || finalConfidence < 0.3) {
      status = 'rejected';
      reason = 'high_risk_content_detected';
      riskLevel = 'high';
    } else if (finalConfidence < 0.6 || highestRisk > 0.5) {
      status = 'manual_review';
      reason = 'medium_risk_needs_review';
      riskLevel = 'medium';
    } else {
      status = 'approved';
      reason = 'content_appears_safe';
      riskLevel = 'low';
    }
    
    return {
      status,
      confidence: finalConfidence,
      reason,
      details,
      riskLevel
    };
  }
}

// 导出单例
export const freeImageAudit = new FreeImageAuditEngine();

// 便捷函数
export async function auditImage(
  imageUrl: string,
  fileName: string,
  fileSize: number,
  userId: string,
  userHistory?: UserAuditHistory
): Promise<AuditResult> {
  const context: AuditContext = {
    imageUrl,
    fileName,
    fileSize,
    userId,
    userHistory
  };
  
  return await freeImageAudit.audit(context);
}
