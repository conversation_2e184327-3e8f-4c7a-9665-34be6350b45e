'use client';

import React, { useState, useEffect } from 'react';
import { classifyBreed, BreedClassification } from '@/utils/smartBreedClassifier';
import { useClickOutside } from '@/hooks/useClickOutside';

interface SimpleBreedInputProps {
  value: string;
  onChange: (value: string, classification: BreedClassification | null) => void;
  error?: string;
  placeholder?: string;
}

// 本地存储键名
const HISTORY_STORAGE_KEY = 'pet_breed_input_history';
const MAX_HISTORY_ITEMS = 10;

const SimpleBreedInput: React.FC<SimpleBreedInputProps> = ({
  value,
  onChange,
  error,
  placeholder = '请输入宠物品种，如：拉布拉多、英短蓝猫等'
}) => {
  const [classification, setClassification] = useState<BreedClassification | null>(null);
  const [inputHistory, setInputHistory] = useState<string[]>([]);
  const [showHistory, setShowHistory] = useState(false);

  // 从本地存储加载历史记录
  const loadHistory = (): string[] => {
    try {
      const stored = localStorage.getItem(HISTORY_STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  };

  // 保存历史记录到本地存储
  const saveHistory = (history: string[]) => {
    try {
      localStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(history));
    } catch {
      // 忽略存储错误
    }
  };

  // 添加到历史记录
  const addToHistory = (breed: string) => {
    if (!breed.trim()) return;
    
    const currentHistory = loadHistory();
    const newHistory = [breed, ...currentHistory.filter(item => item !== breed)]
      .slice(0, MAX_HISTORY_ITEMS);
    
    setInputHistory(newHistory);
    saveHistory(newHistory);
  };

  // 清除历史记录
  const clearHistory = () => {
    setInputHistory([]);
    saveHistory([]);
    setShowHistory(false);
  };

  // 初始化历史记录
  useEffect(() => {
    setInputHistory(loadHistory());
  }, []);

  // 更新分类
  useEffect(() => {
    if (!value || !value.trim()) {
      setClassification(null);
      // 清空时也要通知父组件
      onChange(value, null);
      return;
    }

    const newClassification = classifyBreed(value);
    setClassification(newClassification);

    // 将分类结果传递给父组件
    onChange(value, newClassification);
  }, [value]); // 移除onChange依赖，避免无限循环

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // 立即通知父组件更新值，分类会在useEffect中处理
    onChange(newValue, null);
  };

  // 处理历史记录选择
  const handleHistoryClick = (historyItem: string) => {
    // 立即通知父组件更新值，分类会在useEffect中处理
    onChange(historyItem, null);
    setShowHistory(false);
  };

  // 处理输入确认
  const handleInputConfirm = () => {
    if (value && value.trim()) {
      addToHistory(value.trim());
    }
  };

  // 切换历史记录显示
  const toggleHistory = () => {
    setShowHistory(!showHistory);
  };

  // 点击外部关闭历史记录
  const historyRef = useClickOutside<HTMLDivElement>(() => {
    setShowHistory(false);
  });

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleInputConfirm();
    }
  };

  return (
    <div className="space-y-3">
      {/* 输入框和历史记录按钮 */}
      <div className="flex gap-2">
        <div className="flex-1">
          <input
            type="text"
            value={value}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onBlur={handleInputConfirm}
            placeholder={placeholder}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
              error ? 'border-red-500' : 'border-gray-300'
            }`}
          />
          {error && (
            <p className="mt-1 text-sm text-red-500">{error}</p>
          )}
        </div>
        
        {/* 历史记录按钮 */}
        <div className="relative" ref={historyRef}>
          <button
            type="button"
            onClick={toggleHistory}
            className="px-3 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            title="查看输入历史"
          >
            <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </button>
          
          {/* 历史记录下拉列表 */}
          {showHistory && inputHistory.length > 0 && (
            <div className="absolute right-0 top-full mt-1 w-64 bg-white border border-gray-300 rounded-lg shadow-lg z-10">
              {/* 历史记录标题 */}
              <div className="px-4 py-2 bg-gray-50 border-b border-gray-200 flex items-center justify-between">
                <span className="text-sm text-gray-600 flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  输入历史
                </span>
                <button
                  onClick={clearHistory}
                  className="text-xs text-red-500 hover:text-red-700 transition-colors"
                >
                  清除
                </button>
              </div>
              
              {/* 历史记录列表 */}
              <div className="max-h-48 overflow-y-auto">
                {inputHistory.map((historyItem, index) => (
                  <div
                    key={`${historyItem}-${index}`}
                    onClick={() => handleHistoryClick(historyItem)}
                    className="px-4 py-2 cursor-pointer hover:bg-gray-50 transition-colors"
                  >
                    <span className="text-sm">{historyItem}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 智能分类结果 */}
      {classification && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
          <div className="flex items-center text-sm text-green-800">
            <span className="font-medium">智能分类结果：</span>
          </div>
          <div className="mt-1 flex items-center">
            <span className="text-green-600 mr-2">✅</span>
            <span className="font-medium text-green-800">
              {classification.primaryCategoryName} &gt; {classification.secondaryCategoryName || '其他'}
            </span>
            <span className="ml-2 text-green-600">
              ({Math.round(classification.confidence * 100)}%匹配)
            </span>
          </div>
          <div className="mt-1 text-sm text-green-700">
            {classification.isGeneralCategory
              ? '💡 输入具体品种可以让其他用户更精准地找到您的宝贝'
              : '💡 分类识别成功！'
            }
          </div>
        </div>
      )}

      {/* 帮助文本 */}
      {!value && (
        <div className="text-xs text-gray-500">
          <p>💡 输入具体品种有助于其他用户更快找到您的宝贝</p>
          {inputHistory.length > 0 && (
            <p>🕒 点击历史记录按钮查看之前的输入</p>
          )}
        </div>
      )}
    </div>
  );
};

export default SimpleBreedInput;
