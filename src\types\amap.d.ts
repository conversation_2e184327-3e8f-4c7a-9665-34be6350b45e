// 高德地图API类型声明

declare global {
  interface Window {
    AMap: any;
  }
}

// 高德地图基础类型
export interface AmapPosition {
  lng: number;
  lat: number;
}

export interface AmapPixel {
  x: number;
  y: number;
}

export interface AmapAddressComponent {
  province: string;
  city: string;
  district: string;
  street: string;
  streetNumber: string;
  neighborhood: string;
  building: string;
  adcode: string;
  towncode: string;
}

export interface AmapGeolocationResult {
  position: AmapPosition;
  accuracy: number;
  location_type: string;
  message: string;
  isConverted: boolean;
  status: number;
  addressComponent: AmapAddressComponent;
  formattedAddress: string;
  roads: any[];
  crosses: any[];
  pois: any[];
}

export interface AmapGeolocationOptions {
  enableHighAccuracy?: boolean;
  timeout?: number;
  maximumAge?: number;
  convert?: boolean;
  showButton?: boolean;
  buttonPosition?: string;
  buttonOffset?: any;
  showMarker?: boolean;
  showCircle?: boolean;
  panToLocation?: boolean;
  zoomToAccuracy?: boolean;
}

// 高德地图类声明
declare class AmapGeolocation {
  constructor(options?: AmapGeolocationOptions);
  getCurrentPosition(callback: (status: string, result: AmapGeolocationResult | any) => void): void;
  watchPosition(): void;
  clearWatch(): void;
}

declare class AmapGeocoder {
  constructor(options?: any);
  getLocation(address: string, callback: (status: string, result: any) => void): void;
  getAddress(position: AmapPosition, callback: (status: string, result: any) => void): void;
}

declare class AmapPixelClass {
  constructor(x: number, y: number);
}

declare namespace AMap {
  export const Geolocation: typeof AmapGeolocation;
  export const Geocoder: typeof AmapGeocoder;
  export const Pixel: typeof AmapPixelClass;
}

export {};
