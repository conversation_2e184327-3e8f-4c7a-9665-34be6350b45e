const cloudbase = require('@cloudbase/node-sdk');

const app = cloudbase.init({
  env: cloudbase.SYMBOL_CURRENT_ENV
});

const storage = app.storage();
const db = app.database();

// 获取静态托管域名
const STATIC_DOMAIN = 'yichongyuzhou-3g9112qwf5f3487b-1368816056.tcloudbaseapp.com';

// 从云存储下载文件并上传到静态托管
async function migrateImageToStatic(cloudId, targetPath) {
  try {
    console.log(`开始迁移图片: ${cloudId} -> ${targetPath}`);

    // 1. 从云存储下载文件
    const downloadResult = await storage.downloadFile({
      fileID: cloudId
    });

    if (!downloadResult || !downloadResult.fileContent) {
      throw new Error('下载文件失败');
    }

    console.log(`文件下载成功，大小: ${downloadResult.fileContent.length} bytes`);

    // 2. 上传到静态托管
    const uploadResult = await storage.uploadFile({
      cloudPath: `images/${targetPath}`,
      fileContent: downloadResult.fileContent
    });

    if (uploadResult && uploadResult.fileID) {
      const publicUrl = `https://${STATIC_DOMAIN}/images/${targetPath}`;
      console.log(`图片迁移成功: ${publicUrl}`);
      
      return {
        success: true,
        originalCloudId: cloudId,
        newCloudId: uploadResult.fileID,
        publicUrl: publicUrl,
        targetPath: `images/${targetPath}`
      };
    } else {
      throw new Error('上传到静态托管失败');
    }

  } catch (error) {
    console.error(`迁移图片失败 ${cloudId}:`, error);
    return {
      success: false,
      originalCloudId: cloudId,
      error: error.message
    };
  }
}

// 生成公开访问的图片路径
function generatePublicImagePath(originalPath) {
  // 从原始路径提取文件名
  const fileName = originalPath.split('/').pop();
  return fileName;
}

// 迁移所有帖子的图片
async function migrateAllPostImages() {
  try {
    console.log('开始迁移所有帖子图片...');

    // 获取所有包含图片的帖子
    const postsResult = await db.collection('posts')
      .where({
        images: db.command.exists(true)
      })
      .get();

    console.log(`找到 ${postsResult.data.length} 个包含图片的帖子`);

    const migrationResults = [];
    let successCount = 0;
    let failCount = 0;

    for (const post of postsResult.data) {
      if (post.images && post.images.length > 0) {
        console.log(`处理帖子: ${post._id}, 图片数量: ${post.images.length}`);

        const newImages = [];
        const imageResults = [];

        for (let i = 0; i < post.images.length; i++) {
          const originalImageUrl = post.images[i];
          
          // 检查是否已经是公开URL
          if (originalImageUrl.startsWith('https://')) {
            console.log(`图片已经是公开URL: ${originalImageUrl}`);
            newImages.push(originalImageUrl);
            continue;
          }

          // 生成新的图片路径
          const publicPath = generatePublicImagePath(originalImageUrl);
          const targetPath = `${post._id}_${i}_${publicPath}`;

          // 迁移图片
          const migrationResult = await migrateImageToStatic(originalImageUrl, targetPath);
          imageResults.push(migrationResult);

          if (migrationResult.success) {
            newImages.push(migrationResult.publicUrl);
            successCount++;
          } else {
            // 迁移失败，保留原URL
            newImages.push(originalImageUrl);
            failCount++;
          }
        }

        // 更新帖子的图片URL
        if (newImages.length > 0) {
          await db.collection('posts').doc(post._id).update({
            images: newImages,
            images_migrated: true,
            migration_time: new Date()
          });

          console.log(`帖子 ${post._id} 图片URL已更新`);
        }

        migrationResults.push({
          postId: post._id,
          originalImages: post.images,
          newImages: newImages,
          imageResults: imageResults
        });
      }
    }

    return {
      success: true,
      totalPosts: postsResult.data.length,
      successCount,
      failCount,
      results: migrationResults
    };

  } catch (error) {
    console.error('迁移所有图片失败:', error);
    return {
      success: false,
      message: error.message,
      error: error
    };
  }
}

// 迁移用户头像
async function migrateUserAvatars() {
  try {
    console.log('开始迁移用户头像...');

    const usersResult = await db.collection('users')
      .where({
        $or: [
          { avatar: db.command.exists(true) },
          { avatar_url: db.command.exists(true) }
        ]
      })
      .get();

    console.log(`找到 ${usersResult.data.length} 个用户`);

    const migrationResults = [];
    let successCount = 0;
    let failCount = 0;

    for (const user of usersResult.data) {
      const avatarUrl = user.avatar_url || user.avatar;
      
      if (avatarUrl && !avatarUrl.startsWith('https://')) {
        console.log(`处理用户头像: ${user._id}`);

        const publicPath = generatePublicImagePath(avatarUrl);
        const targetPath = `avatars/${user._id}_${publicPath}`;

        const migrationResult = await migrateImageToStatic(avatarUrl, targetPath);

        if (migrationResult.success) {
          // 更新用户头像URL
          await db.collection('users').doc(user._id).update({
            avatar: migrationResult.publicUrl,
            avatar_url: migrationResult.publicUrl,
            avatar_migrated: true,
            migration_time: new Date()
          });

          successCount++;
          console.log(`用户 ${user._id} 头像迁移成功`);
        } else {
          failCount++;
        }

        migrationResults.push({
          userId: user._id,
          originalAvatar: avatarUrl,
          migrationResult: migrationResult
        });
      }
    }

    return {
      success: true,
      totalUsers: usersResult.data.length,
      successCount,
      failCount,
      results: migrationResults
    };

  } catch (error) {
    console.error('迁移用户头像失败:', error);
    return {
      success: false,
      message: error.message,
      error: error
    };
  }
}

// 测试图片访问
async function testImageAccess(imageUrl) {
  try {
    console.log(`测试图片访问: ${imageUrl}`);
    
    // 这里可以添加HTTP请求测试图片是否可访问
    // 由于云函数环境限制，我们返回URL信息
    
    return {
      success: true,
      imageUrl: imageUrl,
      isPublic: imageUrl.startsWith('https://'),
      domain: imageUrl.includes(STATIC_DOMAIN) ? 'static_hosting' : 'external'
    };

  } catch (error) {
    return {
      success: false,
      imageUrl: imageUrl,
      error: error.message
    };
  }
}

// 云函数入口
exports.main = async (event, context) => {
  const { action, cloudId, targetPath, imageUrl } = event;

  try {
    switch (action) {
      case 'migrateImage':
        return await migrateImageToStatic(cloudId, targetPath);
      
      case 'migrateAllPosts':
        return await migrateAllPostImages();
      
      case 'migrateAvatars':
        return await migrateUserAvatars();
      
      case 'testAccess':
        return await testImageAccess(imageUrl);
      
      case 'migrateAll':
        // 迁移所有图片
        const postResult = await migrateAllPostImages();
        const avatarResult = await migrateUserAvatars();
        
        return {
          success: true,
          posts: postResult,
          avatars: avatarResult
        };
      
      default:
        return {
          success: false,
          message: '不支持的操作'
        };
    }
  } catch (error) {
    console.error('云函数执行失败:', error);
    return {
      success: false,
      message: error.message,
      error: error
    };
  }
};
