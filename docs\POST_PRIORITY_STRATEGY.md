# 🎯 帖子优先级管理策略

## 📋 概述

本文档详细介绍了宠物平台的帖子优先级管理系统，包括质量评分算法、优先级策略和内容管理建议。

## 🏆 **优先级等级系统**

### **1. 优先级分类**

| 优先级 | 标识 | 说明 | 展示策略 |
|--------|------|------|----------|
| **高优先级** | 🔥 | 优质内容，优先展示 | 置顶显示，获得更多曝光 |
| **普通** | ⭐ | 正常内容 | 按算法正常排序 |
| **低优先级** | 📉 | 质量一般，降低展示 | 排序靠后，减少曝光 |
| **已隐藏** | 👁️‍🗨️ | 问题内容，不公开展示 | 不在列表中显示 |

### **2. 质量评分算法（0-100分）**

#### **A. 基础分数：50分**

#### **B. 内容质量评分（0-30分）**
- **图片质量**（0-15分）：每张图片+3分，最多15分
- **描述质量**（0-10分）：
  - 50字以上：+10分
  - 20-49字：+6分
  - 10-19字：+3分
- **标签完整性**（0-5分）：每个标签+1分，最多5分

#### **C. 用户信誉评分（0-20分）**
- **用户等级**（0-10分）：等级×2分，最多10分
- **历史发帖质量**（0-10分）：
  - 平均分≥80：+10分
  - 平均分≥70：+8分
  - 平均分≥60：+6分
  - 平均分≥50：+4分

#### **D. 互动质量评分（0-20分）**
- **点赞数**（0-10分）：点赞数×0.5分，最多10分
- **评论数**（0-10分）：评论数×2分，最多10分

#### **E. 时效性评分（0-10分）**
- 24小时内：10分
- 48小时内：8分
- 72小时内：6分
- 一周内：4分
- 超过一周：2分

#### **F. 违规扣分（-50到0分）**
- **广告内容**：-20分
- **用户举报**：每个举报-5分
- **违规等级**：
  - 轻微违规：-10分
  - 中度违规：-25分
  - 严重违规：-50分

## 🎯 **针对广告内容的策略**

### **1. 广告内容识别**

#### **自动识别标准**
- 包含明显的商业推广词汇
- 图片中包含价格、联系方式
- 描述中包含"出售"、"价格"、"联系"等关键词
- 用户历史发帖中广告比例过高

#### **手动标记**
- 管理员可手动标记 `hasAds: true`
- 用户可举报广告内容
- 系统自动降低优先级

### **2. 广告内容处理策略**

#### **A. 不下架但降低优先级**
```javascript
// 广告内容自动降级
if (post.hasAds || post.isPromotional) {
  priority_level = 'low';
  quality_score -= 20;
  reason = '包含广告内容，自动降低优先级';
}
```

#### **B. 智能分流策略**
- **高质量广告**：允许正常展示，但标记"推广"
- **低质量广告**：降低优先级，减少曝光
- **垃圾广告**：隐藏处理，不公开展示

### **3. 平衡用户体验与商业价值**

#### **用户体验优先**
- 广告内容不超过总内容的20%
- 高质量非广告内容优先展示
- 用户可选择屏蔽广告内容

#### **商业价值保护**
- 付费推广内容可获得优先级提升
- 优质商家可申请认证，提高信誉度
- 建立广告内容质量标准

## 🛠️ **管理操作指南**

### **1. 手动调整优先级**

#### **提升优先级的情况**
- 内容质量特别优秀
- 用户反馈积极
- 具有教育价值
- 社区互动活跃

#### **降低优先级的情况**
- 内容质量较差
- 用户举报较多
- 包含广告内容
- 违反社区规范

### **2. 批量管理操作**

#### **批量提升**
```javascript
// 批量提升优质用户的帖子
await petAPI.batchUpdatePostPriority({
  post_ids: selectedPostIds,
  priority_level: 'high',
  reason: '优质内容批量提升'
});
```

#### **批量降级**
```javascript
// 批量处理广告内容
await petAPI.batchUpdatePostPriority({
  post_ids: adPostIds,
  priority_level: 'low',
  reason: '广告内容批量降级'
});
```

### **3. 自动化规则**

#### **自动提升规则**
- 质量评分≥85分：自动设为高优先级
- 24小时内获得20+点赞：自动提升
- 用户等级≥5且无违规：优先展示

#### **自动降级规则**
- 质量评分≤30分：自动降低优先级
- 举报数≥3：自动隐藏待审核
- 检测到广告内容：自动降级

## 📊 **效果监控**

### **1. 关键指标**

#### **内容质量指标**
- 平均质量评分
- 高质量内容比例
- 用户互动率
- 内容完成度

#### **用户体验指标**
- 用户停留时间
- 内容点击率
- 用户满意度
- 举报率变化

#### **商业平衡指标**
- 广告内容曝光率
- 付费推广效果
- 用户流失率
- 收益变化

### **2. 数据分析**

#### **质量分布分析**
```sql
-- 质量评分分布
SELECT 
  CASE 
    WHEN quality_score >= 80 THEN '优秀'
    WHEN quality_score >= 60 THEN '良好'
    WHEN quality_score >= 40 THEN '一般'
    ELSE '较差'
  END as quality_level,
  COUNT(*) as count,
  AVG(likes) as avg_likes,
  AVG(comments) as avg_comments
FROM posts 
GROUP BY quality_level;
```

#### **优先级效果分析**
```sql
-- 优先级对互动的影响
SELECT 
  priority_level,
  AVG(views) as avg_views,
  AVG(likes) as avg_likes,
  AVG(comments) as avg_comments,
  COUNT(*) as total_posts
FROM posts 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY priority_level;
```

## 🔄 **持续优化**

### **1. 算法调优**
- 根据用户反馈调整评分权重
- 优化广告内容识别准确率
- 改进时效性评分算法

### **2. 策略迭代**
- 定期评估优先级策略效果
- 根据平台发展调整规则
- 平衡用户体验与商业目标

### **3. 用户反馈**
- 收集用户对内容质量的反馈
- 分析用户行为数据
- 优化推荐算法

## 📝 **最佳实践**

### **1. 管理员操作建议**
- 定期审查高举报内容
- 关注质量评分异常的帖子
- 平衡不同类型内容的曝光

### **2. 用户引导**
- 鼓励用户发布高质量内容
- 提供内容创作指导
- 建立社区规范和奖励机制

### **3. 技术优化**
- 优化算法性能
- 提高自动识别准确率
- 完善数据统计和分析

---

## 🎯 **总结**

通过智能的帖子优先级管理系统，我们可以：

1. **提升内容质量** - 鼓励用户创作优质内容
2. **优化用户体验** - 让用户看到更好的内容
3. **平衡商业价值** - 合理处理广告内容
4. **维护社区秩序** - 有效管理违规内容

这个系统既保护了用户体验，又为平台的商业化提供了灵活的管理工具。🐾
