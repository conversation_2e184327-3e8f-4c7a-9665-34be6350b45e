'use client';

import { useState, useEffect } from 'react';
import { CheckCircle, Clock, Shield, Sparkles } from 'lucide-react';

interface AuditWaitingModalProps {
  isOpen: boolean;
  onComplete: () => void;
  postTitle?: string;
}

export default function AuditWaitingModal({ isOpen, onComplete, postTitle = '您的宠物' }: AuditWaitingModalProps) {
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [timeLeft, setTimeLeft] = useState(12);

  const auditSteps = [
    { icon: Shield, text: '内容安全检查', duration: 2000 },
    { icon: Sparkles, text: '图片质量分析', duration: 3000 },
    { icon: Clock, text: '信息完整性验证', duration: 2000 },
    { icon: CheckCircle, text: '最终审核确认', duration: 3000 }
  ];

  useEffect(() => {
    if (!isOpen) {
      setProgress(0);
      setCurrentStep(0);
      setTimeLeft(12);
      return;
    }

    // 随机审核时长 8-12秒
    const totalDuration = Math.floor(Math.random() * 5000) + 8000;
    setTimeLeft(Math.ceil(totalDuration / 1000));

    let currentTime = 0;
    const interval = setInterval(() => {
      currentTime += 100;
      const newProgress = Math.min((currentTime / totalDuration) * 100, 100);
      setProgress(newProgress);
      setTimeLeft(Math.max(0, Math.ceil((totalDuration - currentTime) / 1000)));

      // 更新当前步骤
      const stepProgress = newProgress / 25;
      setCurrentStep(Math.min(Math.floor(stepProgress), auditSteps.length - 1));

      if (currentTime >= totalDuration) {
        clearInterval(interval);
        setTimeout(() => {
          onComplete();
        }, 500);
      }
    }, 100);

    return () => clearInterval(interval);
  }, [isOpen, onComplete]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-md w-full p-8 text-center">
        {/* 标题 */}
        <div className="mb-8">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Shield className="w-8 h-8 text-blue-600 animate-pulse" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            正在审核您的发布
          </h2>
          <p className="text-gray-600 text-sm">
            {postTitle} 正在接受平台安全审核
          </p>
        </div>

        {/* 进度条 */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-500">审核进度</span>
            <span className="text-sm font-medium text-blue-600">
              {Math.round(progress)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className="mt-2 text-xs text-gray-500">
            预计还需 {timeLeft} 秒
          </div>
        </div>

        {/* 审核步骤 */}
        <div className="space-y-4 mb-8">
          {auditSteps.map((step, index) => {
            const StepIcon = step.icon;
            const isActive = index === currentStep;
            const isCompleted = index < currentStep;
            
            return (
              <div 
                key={index}
                className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-300 ${
                  isActive 
                    ? 'bg-blue-50 border border-blue-200' 
                    : isCompleted 
                      ? 'bg-green-50 border border-green-200'
                      : 'bg-gray-50 border border-gray-200'
                }`}
              >
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  isActive 
                    ? 'bg-blue-100 text-blue-600' 
                    : isCompleted 
                      ? 'bg-green-100 text-green-600'
                      : 'bg-gray-100 text-gray-400'
                }`}>
                  {isCompleted ? (
                    <CheckCircle className="w-5 h-5" />
                  ) : (
                    <StepIcon className={`w-5 h-5 ${isActive ? 'animate-pulse' : ''}`} />
                  )}
                </div>
                <span className={`text-sm font-medium ${
                  isActive 
                    ? 'text-blue-900' 
                    : isCompleted 
                      ? 'text-green-900'
                      : 'text-gray-500'
                }`}>
                  {step.text}
                </span>
                {isActive && (
                  <div className="ml-auto">
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                  </div>
                )}
                {isCompleted && (
                  <div className="ml-auto">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* 提示信息 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <Clock className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-yellow-800">
              <p className="font-medium mb-1">审核说明</p>
              <ul className="text-xs space-y-1">
                <li>• 我们正在检查内容是否符合平台规范</li>
                <li>• 审核通过后将自动发布到平台</li>
                <li>• 请耐心等待，不要关闭页面</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
