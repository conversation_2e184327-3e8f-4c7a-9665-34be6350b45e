'use client';

import { useState, useEffect } from 'react';
import { petAPI } from '@/lib/cloudbase';
import { 
  PlusIcon, 
  EyeIcon, 
  PencilIcon, 
  TrashIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  PlayIcon,
  PauseIcon
} from '@heroicons/react/24/outline';

interface Advertisement {
  _id: string;
  title: string;
  advertiser_id: string;
  advertiser_name: string;
  position_id: string;
  position_name: string;
  ad_type: 'banner' | 'feed' | 'popup' | 'video';
  image_url?: string;
  video_url?: string;
  content: string;
  target_url: string;
  start_date: string;
  end_date: string;
  status: 'active' | 'paused' | 'expired' | 'pending';
  priority: number;
  budget: number;
  spent: number;
  impressions: number;
  clicks: number;
  ctr: number;
  created_at: string;
}

interface AdPosition {
  _id?: string;
  position_id: string;
  name: string;
  description?: string;
  page: string;
  location: string;
  width: number;
  height: number;
  ad_type: string;
  max_ads: number;
  rotation_interval?: number;
  status: 'active' | 'inactive';
}

export default function AdsManagement() {
  const [ads, setAds] = useState<Advertisement[]>([]);
  const [positions, setPositions] = useState<AdPosition[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'ads' | 'positions' | 'statistics'>('ads');
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadAds(),
        loadPositions()
      ]);
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadAds = async () => {
    try {
      // 暂时使用模拟数据
      const mockAds: Advertisement[] = [
        {
          _id: 'ad_001',
          title: '优质宠物用品推荐',
          advertiser_id: 'advertiser_001',
          advertiser_name: '宠物之家商城',
          position_id: 'home_banner',
          position_name: '首页横幅广告',
          ad_type: 'banner',
          content: '为您的爱宠提供最好的生活用品，健康快乐每一天！全场8折优惠中。',
          target_url: 'https://example.com/pet-products',
          start_date: '2025-01-01T00:00:00.000Z',
          end_date: '2025-03-31T23:59:59.000Z',
          status: 'active',
          priority: 1,
          budget: 5000,
          spent: 1250.50,
          impressions: 15420,
          clicks: 342,
          ctr: 0.0222,
          created_at: '2025-01-01T00:00:00.000Z'
        },
        {
          _id: 'ad_002',
          title: '专业宠物医院',
          advertiser_id: 'advertiser_002',
          advertiser_name: '爱宠医疗中心',
          position_id: 'home_feed',
          position_name: '首页信息流广告',
          ad_type: 'feed',
          content: '24小时宠物医疗服务，专业医师团队，让您的爱宠健康无忧。',
          target_url: 'https://example.com/pet-hospital',
          start_date: '2025-01-10T00:00:00.000Z',
          end_date: '2025-02-28T23:59:59.000Z',
          status: 'active',
          priority: 2,
          budget: 3000,
          spent: 890.25,
          impressions: 8750,
          clicks: 156,
          ctr: 0.0178,
          created_at: '2025-01-10T00:00:00.000Z'
        },
        {
          _id: 'ad_003',
          title: '宠物美容服务',
          advertiser_id: 'advertiser_003',
          advertiser_name: '美宠工坊',
          position_id: 'home_feed',
          position_name: '首页信息流广告',
          ad_type: 'feed',
          content: '专业宠物美容，让您的爱宠更加美丽动人。新客户首次服务7折。',
          target_url: 'https://example.com/pet-grooming',
          start_date: '2025-01-15T00:00:00.000Z',
          end_date: '2025-04-15T23:59:59.000Z',
          status: 'paused',
          priority: 3,
          budget: 2000,
          spent: 450.75,
          impressions: 4200,
          clicks: 89,
          ctr: 0.0212,
          created_at: '2025-01-15T00:00:00.000Z'
        }
      ];

      setAds(mockAds);

      // TODO: 后续调用真实API
      // const result = await petAPI.getAds();
      // if (result.success) {
      //   setAds(result.data || []);
      // }
    } catch (error) {
      console.error('加载广告失败:', error);
    }
  };

  const loadPositions = async () => {
    try {
      // 暂时使用模拟数据
      const mockPositions: AdPosition[] = [
        {
          position_id: 'home_banner',
          name: '首页横幅广告',
          page: 'home',
          location: 'top',
          width: 728,
          height: 90,
          ad_type: 'banner',
          max_ads: 3,
          rotation_interval: 5000,
          status: 'active'
        },
        {
          position_id: 'home_feed',
          name: '首页信息流广告',
          page: 'home',
          location: 'feed',
          width: 300,
          height: 200,
          ad_type: 'feed',
          max_ads: 5,
          rotation_interval: 0,
          status: 'active'
        },
        {
          position_id: 'sidebar_banner',
          name: '侧边栏广告',
          page: 'all',
          location: 'sidebar',
          width: 300,
          height: 250,
          ad_type: 'banner',
          max_ads: 2,
          rotation_interval: 8000,
          status: 'inactive'
        }
      ];

      setPositions(mockPositions);

      // TODO: 后续调用真实API
      // const result = await petAPI.getAdPositions();
      // if (result.success) {
      //   setPositions(result.data || []);
      // }
    } catch (error) {
      console.error('加载广告位失败:', error);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { label: '投放中', color: 'bg-green-100 text-green-800' },
      paused: { label: '已暂停', color: 'bg-yellow-100 text-yellow-800' },
      expired: { label: '已过期', color: 'bg-red-100 text-red-800' },
      pending: { label: '待审核', color: 'bg-blue-100 text-blue-800' },
      inactive: { label: '未启用', color: 'bg-gray-100 text-gray-800' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  const formatCurrency = (amount: number) => {
    return `¥${amount.toFixed(2)}`;
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(2)}%`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">广告管理</h1>
          <p className="text-gray-600">管理平台广告投放和收益</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <PlusIcon className="h-5 w-5" />
          <span>创建广告</span>
        </button>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <EyeIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">总展示量</p>
              <p className="text-2xl font-bold text-gray-900">
                {ads.reduce((sum, ad) => sum + ad.impressions, 0).toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <ChartBarIcon className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">总点击量</p>
              <p className="text-2xl font-bold text-gray-900">
                {ads.reduce((sum, ad) => sum + ad.clicks, 0).toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <CurrencyDollarIcon className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">总收益</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(ads.reduce((sum, ad) => sum + ad.spent, 0))}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <PlayIcon className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">活跃广告</p>
              <p className="text-2xl font-bold text-gray-900">
                {ads.filter(ad => ad.status === 'active').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 标签页 */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { key: 'ads', label: '广告列表', icon: EyeIcon },
            { key: 'positions', label: '广告位管理', icon: ChartBarIcon },
            { key: 'statistics', label: '数据统计', icon: CurrencyDollarIcon }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`${
                activeTab === tab.key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
            >
              <tab.icon className="h-5 w-5" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* 广告列表 */}
      {activeTab === 'ads' && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">广告列表</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    广告信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    广告位
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    数据
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    收益
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {ads.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-12 text-center text-gray-500">
                      暂无广告数据
                    </td>
                  </tr>
                ) : (
                  ads.map((ad) => (
                    <tr key={ad._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {ad.image_url && (
                            <img
                              src={ad.image_url}
                              alt={ad.title}
                              className="h-10 w-10 rounded object-cover mr-3"
                            />
                          )}
                          <div>
                            <div className="text-sm font-medium text-gray-900">{ad.title}</div>
                            <div className="text-sm text-gray-500">{ad.advertiser_name}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{ad.position_name}</div>
                        <div className="text-sm text-gray-500">{ad.ad_type}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(ad.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>展示: {ad.impressions.toLocaleString()}</div>
                        <div>点击: {ad.clicks.toLocaleString()}</div>
                        <div>CTR: {formatPercentage(ad.ctr)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>预算: {formatCurrency(ad.budget)}</div>
                        <div>已花费: {formatCurrency(ad.spent)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button className="text-blue-600 hover:text-blue-900">
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-900">
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button className="text-yellow-600 hover:text-yellow-900">
                            {ad.status === 'active' ? <PauseIcon className="h-4 w-4" /> : <PlayIcon className="h-4 w-4" />}
                          </button>
                          <button className="text-red-600 hover:text-red-900">
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* 广告位管理 */}
      {activeTab === 'positions' && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">广告位管理</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    广告位信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    位置
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    尺寸
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    类型
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {positions.map((position) => (
                  <tr key={position._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{position.name}</div>
                        <div className="text-sm text-gray-500">{position.description}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{position.page}</div>
                      <div className="text-sm text-gray-500">{position.location}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {position.width} × {position.height}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {position.ad_type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(position.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button className="text-green-600 hover:text-green-900">
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button className="text-yellow-600 hover:text-yellow-900">
                          {position.status === 'active' ? <PauseIcon className="h-4 w-4" /> : <PlayIcon className="h-4 w-4" />}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* 数据统计 */}
      {activeTab === 'statistics' && (
        <div className="space-y-6">
          {/* 用户体验策略 */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">用户体验策略</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">广告频率控制</h4>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    每日最大展示次数
                  </label>
                  <input
                    type="number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    defaultValue={10}
                    min={1}
                    max={50}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    最小展示间隔（分钟）
                  </label>
                  <input
                    type="number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    defaultValue={30}
                    min={5}
                    max={120}
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="respectUserChoice"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    defaultChecked
                  />
                  <label htmlFor="respectUserChoice" className="ml-2 block text-sm text-gray-900">
                    尊重用户隐藏选择
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="adaptiveFrequency"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    defaultChecked
                  />
                  <label htmlFor="adaptiveFrequency" className="ml-2 block text-sm text-gray-900">
                    自适应展示频率
                  </label>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">广告位策略</h4>

                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">首页横幅</div>
                      <div className="text-sm text-gray-600">用户友好度：高</div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">启用</span>
                      <input type="checkbox" defaultChecked className="h-4 w-4 text-blue-600" />
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">信息流广告</div>
                      <div className="text-sm text-gray-600">用户友好度：中</div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">启用</span>
                      <input type="checkbox" defaultChecked className="h-4 w-4 text-blue-600" />
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">详情页底部</div>
                      <div className="text-sm text-gray-600">用户友好度：高</div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">启用</span>
                      <input type="checkbox" defaultChecked className="h-4 w-4 text-blue-600" />
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">启动弹窗</div>
                      <div className="text-sm text-red-600">用户友好度：低</div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">禁用</span>
                      <input type="checkbox" className="h-4 w-4 text-blue-600" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="font-medium text-gray-900">用户体验建议</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    基于用户行为数据的智能推荐
                  </p>
                </div>
                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                  保存设置
                </button>
              </div>

              <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-sm font-medium text-green-800">推荐</span>
                  </div>
                  <p className="text-sm text-green-700 mt-1">
                    原生信息流广告，用户接受度高
                  </p>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                    <span className="text-sm font-medium text-yellow-800">谨慎</span>
                  </div>
                  <p className="text-sm text-yellow-700 mt-1">
                    横幅广告需要控制频率
                  </p>
                </div>

                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                    <span className="text-sm font-medium text-red-800">避免</span>
                  </div>
                  <p className="text-sm text-red-700 mt-1">
                    弹窗广告容易引起用户反感
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 数据统计图表 */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">广告效果分析</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">92%</div>
                <div className="text-sm text-gray-600">用户满意度</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">3.2%</div>
                <div className="text-sm text-gray-600">平均点击率</div>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">15s</div>
                <div className="text-sm text-gray-600">平均停留时间</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">8%</div>
                <div className="text-sm text-gray-600">广告隐藏率</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
