'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { User, MapPin, Calendar, Heart, MessageCircle, Star, Camera, Settings, Edit, PenTool, ChevronRight, Clock, Eye, Grid, ArrowLeft, X, RotateCcw, Trash2 } from 'lucide-react';
import Button from '@/components/ui/Button';
import { showToast } from '@/components/ui/Toast';
import CloudImage from '@/components/ui/CloudImage';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import PersonalInfoModal from '@/components/profile/PersonalInfoModal';
import BioEditModal from '@/components/profile/BioEditModal';
import FollowingListModal from '@/components/profile/FollowingListModal';
import { getDrafts, deleteDraft, type Draft } from '@/utils/drafts';
import { petAPI } from '@/lib/cloudbase';
import { browse<PERSON><PERSON>oryManager, BrowseHistoryItem } from '@/utils/historyManager';
import PetCard from '@/components/home/<USER>';
import { formatAddressForDisplay } from '@/utils/addressUtils';
import ConfirmModal from '@/components/ui/ConfirmModal';
import ContextMenu from '@/components/ui/ContextMenu';
import PostCardWithLongPress from '@/components/profile/PostCardWithLongPress';

interface UserProfile {
  _id: string;
  nickname: string;
  email?: string;
  avatar_url?: string;
  location?: string;
  bio?: string;
  created_at: string;
  posts_count: number;
  likes_count: number; // 获赞数
  followers_count: number;
  following_count: number;
  rating: number;
  rating_count: number;
  contact_info?: { type: 'wechat' | 'phone'; value: string };
  address?: string;
}

type TabType = 'posts' | 'drafts' | 'wants' | 'history';

interface Post {
  _id: string;
  breed?: string;
  description: string;
  images: string[];
  category: string;
  type: string;
  location: string;
  created_at: string;
  likes_count: number;

  ratings_count: number;
  avg_rating: number;
}

export default function ProfilePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const userId = searchParams.get('id');
  const { user: currentUser, isLoggedIn, isLoading: authLoading } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [isOwnProfile, setIsOwnProfile] = useState(false);
  const [activeTab, setActiveTab] = useState<TabType>('posts');
  const [showPersonalInfoModal, setShowPersonalInfoModal] = useState(false);
  const [showBioEditModal, setShowBioEditModal] = useState(false);
  const [showFollowingModal, setShowFollowingModal] = useState(false);
  const [drafts, setDrafts] = useState<Draft[]>([]);
  const [ratedPosts, setRatedPosts] = useState<Post[]>([]);
  const [userPosts, setUserPosts] = useState<Post[]>([]);
  const [ratingsLoading, setRatingsLoading] = useState(false);
  const [postsLoading, setPostsLoading] = useState(false);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [avatarUploadError, setAvatarUploadError] = useState('');
  const [browseHistory, setBrowseHistory] = useState<BrowseHistoryItem[]>([]);

  // 确认模态框状态
  const [confirmModal, setConfirmModal] = useState<{
    isOpen: boolean;
    title?: string;
    message: string;
    onConfirm: () => void;
    loading?: boolean;
  }>({
    isOpen: false,
    message: '',
    onConfirm: () => {}
  });

  // 上下文菜单状态
  const [contextMenu, setContextMenu] = useState<{
    isOpen: boolean;
    postId: string;
    postType: 'published' | 'draft' | 'history';
    position?: { x: number; y: number };
  }>({
    isOpen: false,
    postId: '',
    postType: 'published'
  });

  // 加载草稿数据
  useEffect(() => {
    if (activeTab === 'drafts') {
      const localDrafts = getDrafts();
      setDrafts(localDrafts);
    }
  }, [activeTab]);

  // 加载浏览历史数据
  useEffect(() => {
    if (activeTab === 'history' && isOwnProfile) {
      const history = browseHistoryManager.getHistory();
      setBrowseHistory(history);
    }
  }, [activeTab, isOwnProfile]);

  // 处理草稿卡片点击
  const handleDraftClick = (draftId: string) => {
    try {
      // 检查是否在客户端环境
      if (typeof window === 'undefined') {
        console.warn('不在客户端环境，无法跳转');
        return;
      }

      // 检查草稿ID是否有效
      if (!draftId) {
        showToast.error('草稿ID无效');
        return;
      }

      console.log('准备跳转到发布页面，草稿ID:', draftId);

      // 使用Next.js router进行跳转
      router.push(`/upload?draftId=${encodeURIComponent(draftId)}`);
    } catch (error) {
      console.error('跳转到发布页面失败:', error);
      showToast.error('跳转失败，请重试');
    }
  };

  // 重新上架归档的帖子
  const restoreArchivedPost = async (draftId: string) => {
    try {
      const currentDrafts = getDrafts();
      const targetDraft = currentDrafts.find(d => d.id === draftId);

      if (!targetDraft || !(targetDraft as any).isArchived) {
        showToast.error('无效的归档帖子');
        return;
      }

      const originalPostId = (targetDraft as any).originalPostId;
      if (!originalPostId) {
        showToast.error('无法找到原始帖子ID');
        return;
      }

      // 尝试重新上架
      const response = await petAPI.updatePostStatus({
        postId: originalPostId,
        status: 'published'
      });

      if (response && response.success) {
        // 从草稿列表中移除
        const updatedDrafts = currentDrafts.filter(d => d.id !== draftId);
        localStorage.setItem('petDrafts', JSON.stringify(updatedDrafts));
        setDrafts(updatedDrafts);

        // 刷新已发布列表
        if (activeTab === 'posts') {
          loadUserPosts();
        }

        showToast.success('宝贝已重新上架');
      } else {
        showToast.error('重新上架失败，请重试');
      }
    } catch (error) {
      console.error('重新上架失败:', error);
      showToast.error('重新上架失败，请重试');
    }
  };

  // 加载用户评分的帖子
  const loadRatedPosts = async () => {
    try {
      setRatingsLoading(true);

      // 尝试调用专门的API
      try {
        const result = await petAPI.getUserRatedPosts({ limit: 20 });
        if (result && result.success && result.data) {
          setRatedPosts(result.data);
          return;
        }
      } catch (apiError) {
        console.warn('getUserRatedPosts API不可用，使用替代方案');
      }

      // 替代方案：从本地存储获取用户评分记录
      const ratedPostIds = JSON.parse(localStorage.getItem('userRatedPosts') || '[]');
      if (ratedPostIds.length === 0) {
      setRatedPosts([]);
      return;
    }

    // 过滤掉草稿ID（以 archived_ 开头的ID）
    const validPostIds = ratedPostIds.filter((id: string) => !id.startsWith('archived_'));

    if (validPostIds.length === 0) {
        setRatedPosts([]);
        return;
      }

      // 获取这些帖子的详细信息
      const ratedPosts = [];
      for (const postId of validPostIds.slice(0, 20)) { // 限制最多20个
        try {
          const postDetail = await petAPI.getPostDetail({ postId });
          if (postDetail.success && postDetail.data) {
            ratedPosts.push(postDetail.data);
          }
        } catch (error) {
          console.warn(`获取帖子 ${postId} 详情失败:`, error);
        }
      }

      setRatedPosts(ratedPosts);
    } catch (error) {
      console.error('加载评分记录失败:', error);
      showToast.error('加载评分记录失败');
    } finally {
      setRatingsLoading(false);
    }
  };

  // 加载用户发布
  const loadUserPosts = async () => {
    if (!isLoggedIn) return;

    // 获取目标用户ID，优先使用URL参数，否则使用当前用户ID
    const targetUserId = userId || (currentUser?._id);
    if (!targetUserId) return;

    try {
      setPostsLoading(true);
      const result = await petAPI.getUserPosts({
        limit: 20,
        targetUserId: targetUserId
      });
      if (result.success) {
        setUserPosts(result.data || []);
      }
    } catch (error) {
      console.error('加载发布失败:', error);
      showToast.error('加载发布失败');
    } finally {
      setPostsLoading(false);
    }
  };

  // 根据标签页加载数据
  useEffect(() => {
    if (activeTab === 'wants' && isOwnProfile) {
      loadRatedPosts();
    } else if (activeTab === 'posts') {
      loadUserPosts();
    }
  }, [activeTab, isOwnProfile, isLoggedIn]);

  // 下架宝贝（从已发布移到待发布）
  const archivePost = async (postId: string) => {
    setConfirmModal({
      isOpen: true,
      title: '确认下架',
      message: '下架这个宝贝并移动到待发布吗？',
      onConfirm: () => performArchivePost(postId)
    });
  };

  // 执行下架操作
  const performArchivePost = async (postId: string) => {
    setConfirmModal(prev => ({ ...prev, loading: true }));

    try {
      // 显示加载状态
      showToast.loading('正在下架宝贝...');
      // 1. 先获取帖子详情
      const postDetail = await petAPI.getPostDetail({ postId });
      if (!postDetail.success) {
        showToast.dismiss();
        showToast.error('获取帖子信息失败');
        return;
      }

      const post = postDetail.data;

      // 2. 先从服务器删除该帖子（关键：必须先确保数据库删除成功）
      const deleteResponse = await petAPI.deletePost({ postId });
      if (!deleteResponse || !deleteResponse.success) {
        showToast.dismiss();
        showToast.error('下架失败：' + (deleteResponse?.message || '服务器删除失败'));
        return;
      }

      console.log('服务器删除成功，开始保存草稿');

      // 3. 转换图片URL为base64数据以确保持久性
      const convertedImages: string[] = [];
      if (post.images && Array.isArray(post.images)) {
        for (const imageUrl of post.images) {
          try {
            if (typeof imageUrl === 'string' && imageUrl.startsWith('http')) {
              // 尝试将图片URL转换为base64
              const response = await fetch(imageUrl);
              const blob = await response.blob();
              const base64 = await new Promise<string>((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result as string);
                reader.onerror = reject;
                reader.readAsDataURL(blob);
              });
              convertedImages.push(base64);
              console.log('成功转换图片URL为base64:', imageUrl.substring(0, 50) + '...');
            } else {
              // 如果已经是base64或其他格式，直接使用
              convertedImages.push(imageUrl);
            }
          } catch (error) {
            console.error('转换图片失败:', imageUrl, error);
            // 转换失败时保留原URL
            convertedImages.push(imageUrl);
          }
        }
      }

      // 4. 数据库删除成功后，将帖子信息保存到本地草稿
      const draftData = {
        id: `archived_${postId}_${Date.now()}`,
        title: post.breed || '',
        breed: post.breed || '',
        description: post.description || '',
        images: convertedImages, // 使用转换后的图片数据
        category: post.category || '',
        location: post.location || '',
        contact_info: post.contact_info || {},
        type: post.type || 'selling',
        price: post.price || '',
        age: post.age || '',
        gender: post.gender || '',
        vaccination: post.vaccination || false,
        health_certificate: post.health_certificate || false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        isArchived: true, // 标记为归档的帖子
        originalPostId: postId // 保存原始帖子ID
      };

      // 5. 保存到本地草稿
      const existingDrafts = getDrafts();
      const updatedDrafts = [draftData, ...existingDrafts];
      localStorage.setItem('petDrafts', JSON.stringify(updatedDrafts));
      console.log('草稿保存成功，包含', convertedImages.length, '张图片');

      // 6. 最后更新UI状态（只有在数据库操作成功后才更新UI）
      setUserPosts(prev => prev.filter(post => post._id !== postId));

      // 7. 如果当前在待发布标签页，立即刷新数据
      if (activeTab === 'drafts') {
        setDrafts(updatedDrafts);
      }

      showToast.dismiss();
      showToast.success('宝贝已下架并移至待发布');

    } catch (error: any) {
      console.error('下架宝贝失败:', error);
      showToast.dismiss();
      showToast.error(error.message || '下架失败，请重试');

      // 发生错误时不更新UI，保持原状态
      // 这样用户可以重试操作
    } finally {
      // 关闭确认模态框
      setConfirmModal(prev => ({ ...prev, isOpen: false, loading: false }));
    }
  };

  // 删除草稿
  const deleteDraft = async (draftId: string) => {
    try {
      const currentDrafts = getDrafts();
      const targetDraft = currentDrafts.find(d => d.id === draftId);

      if (!targetDraft) {
        showToast.error('草稿不存在');
        return;
      }

      // 确认删除
      const confirmMessage = (targetDraft as any).isArchived
        ? '确定要删除这个归档的宝贝吗？删除后无法恢复。'
        : '确定要删除这个草稿吗？';

      setConfirmModal({
        isOpen: true,
        title: '确认删除',
        message: confirmMessage,
        onConfirm: () => performDeleteDraft(draftId)
      });
    } catch (error) {
      console.error('删除草稿失败:', error);
      showToast.error('删除失败，请重试');
    }
  };

  // 执行删除草稿操作
  const performDeleteDraft = async (draftId: string) => {
    try {
      const currentDrafts = getDrafts();

      const targetDraft = currentDrafts.find(d => d.id === draftId);

      // 删除草稿
      const updatedDrafts = currentDrafts.filter(d => d.id !== draftId);
      localStorage.setItem('petDrafts', JSON.stringify(updatedDrafts));
      setDrafts(updatedDrafts);

      const successMessage = (targetDraft as any)?.isArchived ? '归档宝贝已删除' : '草稿已删除';
      showToast.success(successMessage);

      // 关闭确认模态框
      setConfirmModal(prev => ({ ...prev, isOpen: false }));
    } catch (error) {
      console.error('删除草稿失败:', error);
      showToast.error('删除失败，请重试');
      setConfirmModal(prev => ({ ...prev, isOpen: false }));
    }
  };

  // 删除浏览历史记录
  const deleteHistoryRecord = (postId: string) => {
    try {
      // 从本地浏览历史中移除
      browseHistoryManager.removeRecord(postId);
      setBrowseHistory(prev => prev.filter(item => item.postId !== postId));
      showToast.success('已从浏览历史中移除');
    } catch (error) {
      console.error('删除历史记录失败:', error);
      showToast.error('操作失败，请重试');
    }
  };

  // 处理长按交互
  const handleLongPress = (postId: string, postType: 'published' | 'draft' | 'history') => {
    setContextMenu({
      isOpen: true,
      postId,
      postType
    });
  };

  // 获取上下文菜单项
  const getContextMenuItems = () => {
    const { postId, postType } = contextMenu;

    switch (postType) {
      case 'published':
        return [
          {
            id: 'delete',
            label: '下架宝贝',
            icon: <X className="w-4 h-4" />,
            onClick: () => {
              setContextMenu(prev => ({ ...prev, isOpen: false }));
              archivePost(postId);
            },
            variant: 'danger' as const
          }
        ];
      case 'draft':
        return [
          {
            id: 'edit',
            label: '编辑草稿',
            icon: <Edit className="w-4 h-4" />,
            onClick: () => {
              setContextMenu(prev => ({ ...prev, isOpen: false }));
              handleEditDraft(postId);
            }
          },
          {
            id: 'delete',
            label: '删除草稿',
            icon: <X className="w-4 h-4" />,
            onClick: () => {
              setContextMenu(prev => ({ ...prev, isOpen: false }));
              deleteDraft(postId);
            },
            variant: 'danger' as const
          }
        ];
      case 'history':
        return [
          {
            id: 'delete',
            label: '从历史中移除',
            icon: <X className="w-4 h-4" />,
            onClick: () => {
              setContextMenu(prev => ({ ...prev, isOpen: false }));
              deleteHistoryRecord(postId);
            },
            variant: 'danger' as const
          }
        ];
      default:
        return [];
    }
  };

  useEffect(() => {
    // 如果没有userId参数，且用户已登录，则显示当前用户的个人主页
    const targetUserId = userId || (currentUser?._id);

    // 如果没有目标用户ID，且用户认证已完成但未登录，才显示错误
    if (!targetUserId) {
      if (!authLoading && !isLoggedIn) {
        showToast.error('请先登录');
      }
      setLoading(false);
      return;
    }

    // 检查是否是当前用户的个人主页
    if (currentUser && currentUser._id === targetUserId) {
      setIsOwnProfile(true);
      setProfile({
        _id: currentUser._id,
        nickname: currentUser.nickname,
        email: currentUser.email,
        avatar_url: currentUser.avatar_url,
        location: '未设置',
        bio: currentUser.bio || '这个人很懒，什么都没有留下...',
        created_at: currentUser.created_at?.toString() || new Date().toISOString(),
        posts_count: currentUser.posts_count || 0,
        likes_count: currentUser.total_likes || 0,
        followers_count: currentUser.followers_count || 0,
        following_count: currentUser.following_count || 0,
        rating: 5.0,
        rating_count: 0
      });
      setLoading(false);
    } else {
      // 加载其他用户的个人资料
      loadUserProfile(targetUserId);
    }
  }, [userId, currentUser, authLoading, isLoggedIn]);

  // 当个人资料加载完成后，加载用户的帖子
  useEffect(() => {
    if (profile && isLoggedIn && !postsLoading) {
      loadUserPosts();
    }
  }, [profile, isLoggedIn]);

  const loadUserProfile = async (id: string) => {
    try {
      setLoading(true);
      const result = await petAPI.getUserInfo({ userId: id });
      if (result.success && result.data) {
        setProfile({
          ...result.data,
          posts_count: result.data.posts_count || 0,
          likes_count: result.data.likes_count || 0,
          followers_count: result.data.followers_count || 0,
          following_count: result.data.following_count || 0,
          rating: 0,
          rating_count: 0
        });
      } else {
        // 使用模拟数据作为后备
        const mockProfile: UserProfile = {
          _id: id,
          nickname: `用户${id.slice(-6)}`,
          avatar_url: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
          location: '北京市朝阳区',
          bio: '热爱宠物，专业繁殖者',
          created_at: '2024-01-01T00:00:00Z',
          posts_count: 12,
          likes_count: 89,
          followers_count: 156,
          following_count: 89,
          rating: 4.8,
          rating_count: 23
        };

        setProfile(mockProfile);
      }
    } catch (error) {
      console.error('加载用户资料失败:', error);
      showToast.error('加载用户资料失败');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateProfile = async (data: any) => {
    try {
      const result = await petAPI.updateProfile(data);
      if (result.success) {
        showToast.success('资料更新成功');

        // 更新本地状态
        if (profile) {
          setProfile({
            ...profile,
            nickname: data.nickname || profile.nickname,
            bio: data.bio || profile.bio,
            contact_info: data.contactInfo || profile.contact_info,
            address: data.address || profile.address
          });
        }

        // 如果更新了联系方式，同时保存到本地存储
        if (data.contactInfo && currentUser) {
          localStorage.setItem(`contact_${currentUser._id}`, JSON.stringify(data.contactInfo));
        }
        // 如果更新了地址，同时保存到本地存储
        if (data.address !== undefined && currentUser) {
          localStorage.setItem(`address_${currentUser._id}`, data.address);
        }
      } else {
        throw new Error(result.message || '更新失败');
      }
    } catch (error: any) {
      showToast.error(error.message || '更新失败，请重试');
      throw error;
    }
  };

  // 头像上传处理
  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      setAvatarUploadError('请选择图片文件');
      return;
    }

    // 检查文件大小（限制为5MB）
    if (file.size > 5 * 1024 * 1024) {
      setAvatarUploadError('图片大小不能超过5MB');
      return;
    }

    setIsUploadingAvatar(true);
    setAvatarUploadError('');

    try {
      // 压缩头像图片（头像压缩到300px，质量90%，保证清晰度）
      const { uploadFile } = await import('@/lib/cloudbase');

      // 创建canvas进行头像压缩
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      const compressedFile = await new Promise<File>((resolve, reject) => {
        img.onload = () => {
          // 头像压缩到300x300，保证在各种场景下都清晰
          const maxSize = 300;
          let { width, height } = img;

          // 保持宽高比，取较大边作为基准
          if (width > height) {
            if (width > maxSize) {
              height = (height * maxSize) / width;
              width = maxSize;
            }
          } else {
            if (height > maxSize) {
              width = (width * maxSize) / height;
              height = maxSize;
            }
          }

          canvas.width = width;
          canvas.height = height;

          // 绘制压缩后的图片
          ctx?.drawImage(img, 0, 0, width, height);

          // 转换为blob，质量90%保证清晰度
          canvas.toBlob((blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: 'image/jpeg',
                lastModified: Date.now()
              });
              resolve(compressedFile);
            } else {
              reject(new Error('压缩失败'));
            }
          }, 'image/jpeg', 0.9);
        };

        img.onerror = () => reject(new Error('图片加载失败'));
        img.src = URL.createObjectURL(file);
      });

      console.log(`头像压缩: ${file.size} -> ${compressedFile.size} (${Math.round((1 - compressedFile.size / file.size) * 100)}% 减少)`);

      // 上传头像到静态托管（绕过云存储权限限制）
      const { petAPI } = await import('@/lib/cloudbase');

      // 将压缩后的文件转换为base64
      const base64Data = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          // 移除data:image/jpeg;base64,前缀
          const base64 = result.split(',')[1];
          resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(compressedFile);
      });

      // 生成唯一的头像文件名
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substring(2, 8);
      const fileName = `avatar_${timestamp}_${randomStr}.jpg`;

      // 通过云函数上传到静态托管
      const uploadResult = await petAPI.uploadToStatic({
        fileName,
        fileData: base64Data,
        contentType: 'image/jpeg'
      });

      if (!uploadResult.success) {
        throw new Error(uploadResult.message || '头像上传失败');
      }

      const avatarUrl = uploadResult.data.url;

      // 更新用户头像 - 使用petAPI调用
      const result = await petAPI.updateAvatar({ avatarUrl });

      if (result.success) {
        // 更新本地状态
        setProfile(prev => prev ? { ...prev, avatar_url: avatarUrl } : null);

        // 更新用户信息到本地存储，确保头像持久保存
        if (currentUser) {
          try {
            const updatedUser = { ...currentUser, avatar_url: avatarUrl };
            localStorage.setItem('pet_platform_user', JSON.stringify(updatedUser));
          } catch (error) {
            console.error('保存头像到本地存储失败:', error);
          }
        }

        showToast.success('您的头像已经更改完成，三十天内只能修改一次头像');
      } else {
        console.error('头像更新失败:', result);
        setAvatarUploadError(result.message || '头像更新失败');
      }
    } catch (error) {
      console.error('头像上传失败:', error);
      setAvatarUploadError('头像上传失败，请重试: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsUploadingAvatar(false);
    }
  };

  const handleUpdateBio = async (bio: string) => {
    try {
      // 这里应该调用API更新个人简介
      if (profile) {
        setProfile({
          ...profile,
          bio: bio || '这个人很懒，什么都没有留下...'
        });
      }
    } catch (error) {
      throw error;
    }
  };

  // 删除草稿
  const handleDeleteDraft = async (draftId: string) => {
    try {
      deleteDraft(draftId);
      const updatedDrafts = getDrafts();
      setDrafts(updatedDrafts);
      showToast.success('草稿已删除');
    } catch (error) {
      console.error('删除草稿失败:', error);
      showToast.error('删除失败，请重试');
    }
  };

  // 编辑草稿
  const handleEditDraft = (draftId: string) => {
    // 跳转到上传页面，并传递草稿ID
    router.push(`/upload?draftId=${draftId}`);
  };

  // 清空所有草稿
  const clearAllDrafts = () => {
    setConfirmModal({
      isOpen: true,
      title: '确认清空',
      message: '确定要清空所有待发布的宝贝吗？此操作不可恢复。',
      onConfirm: () => {
        localStorage.removeItem('petDrafts');
        setDrafts([]);
        showToast.success('所有待发布宝贝已清空');
        setConfirmModal(prev => ({ ...prev, isOpen: false }));
      }
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">用户不存在</h1>
          <p className="text-gray-600 mb-6">抱歉，找不到该用户的信息</p>
          <Link href="/">
            <Button>返回首页</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 个人资料头部 */}
      <div className="bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* 返回按钮 */}
          <button
            onClick={() => router.push('/')}
            className="mb-6 p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div className="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6">
            {/* 头像 */}
            <div className="relative">
              <div className="w-24 h-24 md:w-32 md:h-32 rounded-full bg-gray-200 overflow-hidden">
                {profile.avatar_url ? (
                  <CloudImage
                    fileId={profile.avatar_url}
                    alt={profile.nickname}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <User className="w-12 h-12 md:w-16 md:h-16 text-gray-400" />
                  </div>
                )}
                {isUploadingAvatar && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-full">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                  </div>
                )}
              </div>
              {isOwnProfile && (
                <div className="absolute bottom-0 right-0">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleAvatarUpload}
                    className="hidden"
                    id="avatar-upload"
                    disabled={isUploadingAvatar}
                  />
                  <label
                    htmlFor="avatar-upload"
                    className={`bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 transition-colors cursor-pointer block ${
                      isUploadingAvatar ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    <Camera className="w-4 h-4" />
                  </label>
                </div>
              )}
              {avatarUploadError && (
                <div className="absolute top-full left-0 mt-2 text-red-500 text-sm">
                  {avatarUploadError}
                </div>
              )}
            </div>

            {/* 用户信息 */}
            <div className="flex-1">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                  <h1 className="text-2xl md:text-3xl font-bold text-gray-900">{profile.nickname}</h1>
                  <div className="flex items-center space-x-4 mt-2 text-gray-600">
                    {profile.location && (
                      <div className="flex items-center space-x-1">
                        <MapPin className="w-4 h-4" />
                        <span>{profile.location}</span>
                      </div>
                    )}
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>加入于 {new Date(profile.created_at).getFullYear()}年</span>
                    </div>
                  </div>
                  <div className="mt-3 flex items-center space-x-2">
                    <p className="text-gray-700">{profile.bio || '这个人很懒，什么都没有留下...'}</p>
                    {isOwnProfile && (
                      <button
                        onClick={() => setShowBioEditModal(true)}
                        className="text-gray-400 hover:text-gray-600 transition-colors"
                      >
                        <PenTool className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex space-x-3 mt-4 md:mt-0">
                  {isOwnProfile ? (
                    <Button
                      variant="outline"
                      icon={<Settings className="w-4 h-4" />}
                      onClick={() => setShowPersonalInfoModal(true)}
                    >
                      个人设置
                    </Button>
                  ) : (
                    <>
                      <Button icon={<MessageCircle className="w-4 h-4" />}>
                        私信
                      </Button>
                      <Button variant="outline" icon={<Heart className="w-4 h-4" />}>
                        关注
                      </Button>
                    </>
                  )}
                </div>
              </div>

              {/* 统计信息 */}
              <div className="flex space-x-6 mt-6">
                <div className="text-center">
                  <div className="text-xl font-bold text-gray-900">{profile.likes_count}</div>
                  <div className="text-sm text-gray-600">获赞</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold text-gray-900">{profile.followers_count}</div>
                  <div className="text-sm text-gray-600">粉丝</div>
                </div>
                <div className="text-center cursor-pointer" onClick={() => isOwnProfile && setShowFollowingModal(true)}>
                  <div className="flex items-center space-x-1">
                    <span className="text-xl font-bold text-gray-900">{profile.following_count}</span>
                    {isOwnProfile && <ChevronRight className="w-4 h-4 text-gray-400" />}
                  </div>
                  <div className="text-sm text-gray-600">关注</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-sm p-6">
          {isOwnProfile ? (
            <>
              {/* 标签页导航 */}
              <div className="flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setActiveTab('posts')}
                  className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'posts'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Grid className="w-4 h-4" />
                  <span>已发布宝贝</span>
                </button>
                <button
                  onClick={() => setActiveTab('drafts')}
                  className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'drafts'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Clock className="w-4 h-4" />
                  <span>待发布宝贝</span>
                </button>
                <button
                  onClick={() => setActiveTab('wants')}
                  className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'wants'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Star className="w-4 h-4" />
                  <span>评分</span>
                </button>
                <button
                  onClick={() => setActiveTab('history')}
                  className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'history'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Eye className="w-4 h-4" />
                  <span>浏览历史</span>
                </button>
              </div>

            </>
          ) : (
            <h2 className="text-xl font-bold text-gray-900 mb-6">
              {`${profile.nickname}的发布`}
            </h2>
          )}
          
          {/* 移动端操作提示 */}
          {isOwnProfile && (
            <div className="mb-4 md:hidden">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <p className="text-sm text-blue-700">
                  💡 提示：长按卡片可显示操作选项
                </p>
              </div>
            </div>
          )}

          {/* 根据不同标签页显示不同内容 */}
          {activeTab === 'posts' && (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {/* 发布宝贝按钮卡片 */}
              <Link href="/upload">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow cursor-pointer">
                  <div className="aspect-square bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
                    <div className="text-center">
                      <Camera className="w-12 h-12 text-blue-500 mx-auto mb-3" />
                      <h3 className="text-lg font-medium text-gray-900 mb-1">发布宝贝</h3>
                      <p className="text-sm text-gray-600">分享您的宠物</p>
                    </div>
                  </div>
                </div>
              </Link>

              {/* 用户发布的帖子 */}
              {postsLoading ? (
                <div className="col-span-full text-center py-8 text-gray-500">
                  <p>加载中...</p>
                </div>
              ) : userPosts.length === 0 ? (
                <div className="col-span-full text-center py-8 text-gray-500">
                  <p>还没有发布任何内容</p>
                </div>
              ) : (
                userPosts.map((post) => (
                  <PostCardWithLongPress
                    key={post._id}
                    post={post as any}
                    onLongPress={() => handleLongPress(post._id, 'published')}
                    onDelete={() => archivePost(post._id)}
                    type="published"
                  />
                ))
              )}
            </div>
          )}

          {activeTab === 'drafts' && (
            <>
              {/* 草稿标题和清空按钮 */}
              {drafts.length > 0 && (
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">待发布宝贝</h3>
                  <button
                    onClick={clearAllDrafts}
                    className="text-sm text-red-600 hover:text-red-800 transition-colors font-medium"
                  >
                    全部清空
                  </button>
                </div>
              )}

              {drafts.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-gray-400 mb-4">
                    <Clock className="w-16 h-16 mx-auto" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    还没有待发布的内容
                  </h3>
                  <p className="text-gray-600 mb-6">
                    在发布页面保存草稿后会显示在这里
                  </p>
                  <Link href="/upload">
                    <Button icon={<Camera className="w-4 h-4" />}>
                      创建内容
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {drafts.map((draft) => {
                    // 处理图片路径 - 现在草稿中的图片都是字符串（base64或URL）
                    const processedImages = draft.images?.map(img => {
                      if (typeof img === 'string') {
                        return img;
                      } else {
                        // 如果不是字符串，使用默认图片
                        console.warn('草稿中发现非字符串图片数据，使用默认图片:', img);
                        return 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center';
                      }
                    }) || [];

                    // 转换草稿数据为Post格式
                    const draftPost = {
                      _id: draft.id,
                      breed: draft.breed || '未命名草稿',
                      description: draft.description || '',
                      images: processedImages,
                      author: {
                        _id: 'draft',
                        nickname: '草稿'
                      },
                      location: draft.location || '',
                      likes_count: 0,
                      created_at: draft.updated_at || draft.created_at,
                      type: draft.type || 'selling',
                      gender: draft.gender
                    };

                    return (
                      <div key={draft.id} className="relative">
                        <PostCardWithLongPress
                          post={draftPost as any}
                          onLongPress={() => handleLongPress(draft.id, 'draft')}
                          onDelete={() => deleteDraft(draft.id)}
                          onClick={() => handleDraftClick(draft.id)}
                          type="draft"
                          isDraft={true}
                        />

                        {/* 归档标识 */}
                        {(draft as any).isArchived && (
                          <div className="absolute top-2 left-2 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium z-10">
                            已归档
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </>
          )}

          {activeTab === 'wants' && (
            <>
              {ratingsLoading ? (
                <div className="text-center py-12">
                  <div className="text-gray-400">加载中...</div>
                </div>
              ) : ratedPosts.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-gray-400 mb-4">
                    <Star className="w-16 h-16 mx-auto" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    还没有评分任何内容
                  </h3>
                  <p className="text-gray-600">
                    给喜欢的宠物评分后会显示在这里
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {ratedPosts.map((post) => (
                    <PetCard
                      key={post._id}
                      post={post as any}
                    />
                  ))}
                </div>
              )}
            </>
          )}

          {activeTab === 'history' && (
            <div>
              {/* 清空历史按钮 - 移动到标签页标题下方 */}
              {browseHistory.length > 0 && (
                <div className="flex items-center justify-between mb-6">
                  <p className="text-sm text-gray-500">
                    浏览记录保留3天，共{browseHistory.length}条记录
                  </p>
                  <button
                    onClick={() => {
                      setConfirmModal({
                        isOpen: true,
                        title: '确认清空',
                        message: '确定要清空所有浏览历史吗？',
                        onConfirm: () => {
                          browseHistoryManager.clearHistory();
                          setBrowseHistory([]);
                          showToast.success('浏览历史已清空');
                          setConfirmModal(prev => ({ ...prev, isOpen: false }));
                        }
                      });
                    }}
                    className="text-sm text-red-600 hover:text-red-800 transition-colors font-medium"
                  >
                    清空历史
                  </button>
                </div>
              )}

              {browseHistory.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-gray-400 mb-4">
                    <Eye className="w-16 h-16 mx-auto" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    还没有浏览历史
                  </h3>
                  <p className="text-gray-600">
                    浏览过的内容会显示在这里，记录保留3天
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {browseHistory.map((item) => {
                    // 将浏览历史项转换为Post格式
                    const historyPost = {
                      _id: item.postId,
                      breed: item.title,
                      description: '',
                      images: item.image ? [item.image] : [],
                      author: {
                        _id: 'history',
                        nickname: item.author || '匿名用户'
                      },
                      location: '',
                      likes_count: 0,
                      created_at: new Date(item.timestamp).toISOString()
                    };

                    return (
                      <PostCardWithLongPress
                        key={item.postId}
                        post={historyPost as any}
                        onLongPress={() => handleLongPress(item.postId, 'history')}
                        onDelete={() => deleteHistoryRecord(item.postId)}
                        type="history"
                      />
                    );
                  })}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 弹窗组件 */}
      <PersonalInfoModal
        isOpen={showPersonalInfoModal}
        onClose={() => setShowPersonalInfoModal(false)}
        currentUser={currentUser}
        onUpdate={handleUpdateProfile}
      />

      <BioEditModal
        isOpen={showBioEditModal}
        onClose={() => setShowBioEditModal(false)}
        currentBio={profile?.bio || ''}
        onSave={handleUpdateBio}
      />

      <FollowingListModal
        isOpen={showFollowingModal}
        onClose={() => setShowFollowingModal(false)}
        userId={currentUser?._id || ''}
      />

      {/* 确认模态框 */}
      <ConfirmModal
        isOpen={confirmModal.isOpen}
        onClose={() => setConfirmModal(prev => ({ ...prev, isOpen: false }))}
        onConfirm={confirmModal.onConfirm}
        title={confirmModal.title}
        message={confirmModal.message}
        loading={confirmModal.loading}
      />

      {/* 移动端上下文菜单 */}
      <ContextMenu
        isOpen={contextMenu.isOpen}
        onClose={() => setContextMenu(prev => ({ ...prev, isOpen: false }))}
        items={getContextMenuItems()}
        position={contextMenu.position}
      />
    </div>
  );
}
