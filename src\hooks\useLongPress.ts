import { useCallback, useRef, useState } from 'react';

interface UseLongPressOptions {
  onLongPress: () => void;
  onPress?: () => void;
  delay?: number;
  shouldPreventDefault?: boolean;
}

interface UseLongPressReturn {
  onMouseDown: (event: React.MouseEvent) => void;
  onMouseUp: (event: React.MouseEvent) => void;
  onMouseLeave: (event: React.MouseEvent) => void;
  onTouchStart: (event: React.TouchEvent) => void;
  onTouchEnd: (event: React.TouchEvent) => void;
  onTouchMove: (event: React.TouchEvent) => void;
  isLongPressing: boolean;
}

export const useLongPress = ({
  onLongPress,
  onPress,
  delay = 500,
  shouldPreventDefault = true
}: UseLongPressOptions): UseLongPressReturn => {
  const [isLongPressing, setIsLongPressing] = useState(false);
  const timeout = useRef<NodeJS.Timeout>();
  const target = useRef<EventTarget>();

  const start = useCallback(
    (event: React.MouseEvent | React.TouchEvent) => {
      if (shouldPreventDefault && event.target) {
        event.target.addEventListener('touchend', preventDefault, {
          passive: false
        });
        target.current = event.target;
      }

      setIsLongPressing(false);

      timeout.current = setTimeout(() => {
        setIsLongPressing(true);

        // 触觉反馈（如果支持）
        if (navigator.vibrate) {
          navigator.vibrate([50, 30, 50]); // 更明显的震动模式
        }

        onLongPress();
      }, delay);
    },
    [onLongPress, delay, shouldPreventDefault]
  );

  const clear = useCallback(
    (event: React.MouseEvent | React.TouchEvent, shouldTriggerPress = true) => {
      timeout.current && clearTimeout(timeout.current);
      
      if (shouldTriggerPress && !isLongPressing && onPress) {
        onPress();
      }

      setIsLongPressing(false);

      if (shouldPreventDefault && target.current) {
        target.current.removeEventListener('touchend', preventDefault);
      }
    },
    [shouldPreventDefault, isLongPressing, onPress]
  );

  const preventDefault = (event: Event) => {
    const touchEvent = event as TouchEvent;
    if (!touchEvent.touches) {
      return;
    }

    if (touchEvent.touches.length < 2 && event.preventDefault) {
      event.preventDefault();
    }
  };

  return {
    onMouseDown: (event: React.MouseEvent) => start(event),
    onMouseUp: (event: React.MouseEvent) => clear(event),
    onMouseLeave: (event: React.MouseEvent) => clear(event, false),
    onTouchStart: (event: React.TouchEvent) => start(event),
    onTouchEnd: (event: React.TouchEvent) => clear(event),
    onTouchMove: (event: React.TouchEvent) => {
      // 如果手指移动太多，取消长按
      const touch = event.touches[0];
      if (touch) {
        // 这里可以添加移动距离检测逻辑
        // 暂时简单处理，任何移动都取消长按
        clear(event, false);
      }
    },
    isLongPressing
  };
};
