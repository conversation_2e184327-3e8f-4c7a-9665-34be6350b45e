const cloudbase = require('@cloudbase/node-sdk');

const app = cloudbase.init({
  env: cloudbase.SYMBOL_CURRENT_ENV
});

const storage = app.storage();

// 获取图片临时访问URL
async function getImageTempUrls(cloudIds) {
  try {
    if (!cloudIds || cloudIds.length === 0) {
      return {
        success: false,
        message: '没有提供图片ID'
      };
    }

    console.log('获取图片临时URL，cloudIds:', cloudIds);

    const results = [];
    
    for (const cloudId of cloudIds) {
      try {
        // 获取临时访问URL，有效期1小时
        const result = await storage.getTempFileURL({
          fileList: [cloudId],
          maxAge: 3600 // 1小时
        });

        if (result.fileList && result.fileList.length > 0) {
          const fileInfo = result.fileList[0];
          results.push({
            cloudId,
            tempUrl: fileInfo.tempFileURL,
            status: fileInfo.status,
            errCode: fileInfo.errCode
          });
        } else {
          results.push({
            cloudId,
            tempUrl: null,
            status: 'error',
            errCode: 'NO_RESULT'
          });
        }
      } catch (error) {
        console.error(`获取图片 ${cloudId} 临时URL失败:`, error);
        results.push({
          cloudId,
          tempUrl: null,
          status: 'error',
          errCode: error.code || 'UNKNOWN_ERROR',
          error: error.message
        });
      }
    }

    return {
      success: true,
      data: results
    };

  } catch (error) {
    console.error('获取图片临时URL失败:', error);
    return {
      success: false,
      message: error.message,
      error: error
    };
  }
}

// 测试单个图片URL
async function testImageUrl(cloudId) {
  try {
    console.log('测试图片URL:', cloudId);

    // 获取临时访问URL
    const result = await storage.getTempFileURL({
      fileList: [cloudId],
      maxAge: 3600
    });

    console.log('临时URL结果:', JSON.stringify(result, null, 2));

    if (result.fileList && result.fileList.length > 0) {
      const fileInfo = result.fileList[0];
      
      return {
        success: true,
        cloudId,
        tempUrl: fileInfo.tempFileURL,
        status: fileInfo.status,
        errCode: fileInfo.errCode,
        downloadUrl: fileInfo.download_url || fileInfo.tempFileURL
      };
    } else {
      return {
        success: false,
        cloudId,
        message: '未获取到临时URL'
      };
    }

  } catch (error) {
    console.error('测试图片URL失败:', error);
    return {
      success: false,
      cloudId,
      message: error.message,
      error: error
    };
  }
}

// 获取存储文件信息
async function getFileInfo(cloudId) {
  try {
    console.log('获取文件信息:', cloudId);

    // 尝试获取文件元数据
    const result = await storage.getTempFileURL({
      fileList: [cloudId],
      maxAge: 60 // 1分钟，仅用于测试
    });

    return {
      success: true,
      cloudId,
      result: result
    };

  } catch (error) {
    console.error('获取文件信息失败:', error);
    return {
      success: false,
      cloudId,
      message: error.message,
      error: error
    };
  }
}

// 云函数入口
exports.main = async (event, context) => {
  const { action, cloudIds, cloudId } = event;

  try {
    switch (action) {
      case 'getTempUrls':
        return await getImageTempUrls(cloudIds);
      
      case 'testUrl':
        return await testImageUrl(cloudId);
      
      case 'getFileInfo':
        return await getFileInfo(cloudId);
      
      case 'testBoth':
        // 测试两个帖子的图片
        const testUrls = [
          'cloud://yichongyuzhou-3g9112qwf5f3487b.7969-yichongyuzhou-3g9112qwf5f3487b-1368816056/uploads/1752958661371_oxrk55c7p3_1752958661685_4em2mf.webp',
          'cloud://yichongyuzhou-3g9112qwf5f3487b.7969-yichongyuzhou-3g9112qwf5f3487b-1368816056/uploads/1752957926914_fxr6jioy8xk_1752957927454_rs3e2a.jpg'
        ];
        return await getImageTempUrls(testUrls);
      
      default:
        return {
          success: false,
          message: '不支持的操作'
        };
    }
  } catch (error) {
    console.error('云函数执行失败:', error);
    return {
      success: false,
      message: error.message,
      error: error
    };
  }
};
