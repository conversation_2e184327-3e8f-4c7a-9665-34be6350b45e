# 宠物交易平台 - API文档

## 📋 API概述

### 基础信息
- **API版本**: v1.0.0
- **基础URL**: `https://yichongyuzhou-3g9112qwf5f3487b-1368816056.tcloudbaseapp.com`
- **云函数入口**: `pet-api`
- **认证方式**: CloudBase OpenID
- **数据格式**: JSON

### 请求格式
```javascript
{
  "action": "API_ACTION_NAME",
  "data": {
    // 请求参数
  }
}
```

### 响应格式
```javascript
{
  "success": true,
  "data": {
    // 响应数据
  },
  "message": "操作成功",
  "timestamp": "2025-01-16T06:00:00.000Z"
}
```

### 错误响应
```javascript
{
  "success": false,
  "error": "错误信息",
  "code": "ERROR_CODE",
  "timestamp": "2025-01-16T06:00:00.000Z"
}
```

---

## 👤 用户管理API

### 获取用户信息
**Action**: `getUserInfo`

**请求参数**:
```javascript
{
  "userId": "user_001"  // 用户ID
}
```

**响应数据**:
```javascript
{
  "success": true,
  "data": {
    "_id": "user_001",
    "username": "petlover001",
    "nickname": "宠物爱好者",
    "avatar_url": "https://example.com/avatar.jpg",
    "location": "北京市朝阳区",
    "bio": "热爱宠物，专业繁殖者",
    "reputation_score": 85,
    "posts_count": 12,
    "likes_count": 89,
    "followers_count": 156,
    "following_count": 89,
    "bookmarks_count": 23,
    "created_at": "2024-01-01T00:00:00.000Z",
    "last_active": "2025-01-16T06:00:00.000Z"
  }
}
```

### 更新用户信息
**Action**: `updateUserInfo`

**请求参数**:
```javascript
{
  "nickname": "新昵称",
  "avatar_url": "https://example.com/new-avatar.jpg",
  "location": "上海市浦东新区",
  "bio": "更新的个人简介"
}
```

**响应数据**:
```javascript
{
  "success": true,
  "message": "用户信息更新成功"
}
```

---

## 🐕 宠物帖子API

### 获取帖子列表
**Action**: `getPosts`

**请求参数**:
```javascript
{
  "category": "狗狗",        // 可选：宠物分类
  "location": "北京市",      // 可选：地区筛选
  "priceRange": [0, 5000],   // 可选：价格范围
  "sortBy": "created_at",    // 可选：排序字段
  "sortOrder": "desc",       // 可选：排序方向
  "limit": 20,               // 可选：返回数量，默认20
  "offset": 0                // 可选：偏移量，默认0
}
```

**响应数据**:
```javascript
{
  "success": true,
  "data": [
    {
      "_id": "pet_001",
      "title": "可爱的金毛寻找新家",
      "description": "这是一只非常可爱的金毛犬...",
      "user_id": "user_001",
      "category": "狗狗",
      "breed": "金毛寻回犬",
      "age": "2岁",
      "gender": "公",
      "location": "北京市",
      "price": 2000,
      "images": ["image1.jpg", "image2.jpg"],
      "status": "available",
      "priority_level": "normal",
      "quality_score": 75,
      "views": 120,
      "likes": 15,
      "bookmarks": 8,
      "created_at": "2025-01-15T10:00:00.000Z",
      "updated_at": "2025-01-16T06:00:00.000Z"
    }
  ],
  "total": 150,
  "hasMore": true
}
```

### 创建帖子
**Action**: `createPost`

**请求参数**:
```javascript
{
  "title": "可爱的金毛寻找新家",
  "description": "详细描述...",
  "category": "狗狗",
  "breed": "金毛寻回犬",
  "age": "2岁",
  "gender": "公",
  "location": "北京市",
  "price": 2000,
  "images": ["image1.jpg", "image2.jpg"],
  "contact_info": {
    "phone": "13800138000",
    "wechat": "petlover001"
  }
}
```

**响应数据**:
```javascript
{
  "success": true,
  "data": {
    "_id": "pet_001",
    "quality_score": 75,
    "priority_level": "normal"
  },
  "message": "帖子发布成功"
}
```

### 按优先级获取帖子
**Action**: `getPostsByPriority`

**请求参数**:
```javascript
{
  "priority_level": "all",     // high, normal, low, hidden, all
  "limit": 20,
  "offset": 0,
  "include_scores": true       // 是否包含评分详情
}
```

### 更新帖子优先级（管理员）
**Action**: `updatePostPriority`

**请求参数**:
```javascript
{
  "post_id": "pet_001",
  "priority_level": "high",    // high, normal, low, hidden
  "reason": "优质内容推荐",
  "openId": "admin_user_id"
}
```

### 批量更新帖子质量评分（管理员）
**Action**: `batchUpdateAllPostsQuality`

**请求参数**:
```javascript
{
  "openId": "admin_user_id"
}
```

---

## 🎊 活动系统API

### 获取活动列表
**Action**: `getActivities`

**请求参数**:
```javascript
{
  "status": "active",          // active, ended, all
  "limit": 10,
  "offset": 0
}
```

**响应数据**:
```javascript
{
  "success": true,
  "data": [
    {
      "_id": "activity_001",
      "title": "测试投票活动：猫咪 VS 狗狗",
      "description": "你更喜欢猫咪还是狗狗？",
      "type": "vote",
      "status": "active",
      "options": [
        {
          "id": "cats",
          "text": "猫咪",
          "votes": 2
        },
        {
          "id": "dogs",
          "text": "狗狗", 
          "votes": 1
        }
      ],
      "stats": {
        "total_votes": 3,
        "total_comments": 1,
        "total_participants": 3
      },
      "start_time": "2025-01-16T00:00:00.000Z",
      "end_time": "2025-01-23T23:59:59.000Z",
      "created_at": "2025-01-16T00:00:00.000Z"
    }
  ]
}
```

### 获取活动详情
**Action**: `getActivityDetail`

**请求参数**:
```javascript
{
  "activity_id": "activity_001",
  "openId": "user_001"
}
```

**响应数据**:
```javascript
{
  "success": true,
  "data": {
    // 活动基本信息
    "_id": "activity_001",
    "title": "测试投票活动：猫咪 VS 狗狗",
    // ... 其他活动信息
    
    // 用户参与状态
    "user_participation": {
      "has_voted": true,
      "voted_option": "cats",
      "can_comment": true,
      "has_commented": false
    },
    
    // 评论列表
    "comments": [
      {
        "_id": "comment_001",
        "user_id": "user_002",
        "content": "我觉得猫咪更好！",
        "created_at": "2025-01-16T07:30:00.000Z"
      }
    ]
  }
}
```

### 活动投票
**Action**: `voteInActivity`

**请求参数**:
```javascript
{
  "activity_id": "activity_001",
  "option_id": "cats",
  "openId": "user_001"
}
```

**响应数据**:
```javascript
{
  "success": true,
  "message": "投票成功",
  "data": {
    "option_id": "cats",
    "current_votes": 3,
    "total_votes": 4
  }
}
```

### 添加活动评论
**Action**: `addActivityComment`

**请求参数**:
```javascript
{
  "activity_id": "activity_001",
  "content": "我觉得猫咪更可爱！",
  "openId": "user_001"
}
```

**响应数据**:
```javascript
{
  "success": true,
  "message": "评论发表成功",
  "data": {
    "_id": "comment_001",
    "created_at": "2025-01-16T07:30:00.000Z"
  }
}
```

---

## 🔔 通知系统API

### 获取用户通知
**Action**: `getUserNotifications`

**请求参数**:
```javascript
{
  "limit": 20,
  "offset": 0,
  "unread_only": false,        // 只获取未读通知
  "openId": "user_001"
}
```

**响应数据**:
```javascript
{
  "success": true,
  "data": [
    {
      "_id": "notif_001",
      "type": "like",
      "recipient_id": "user_001",
      "sender_id": "user_002",
      "post_id": "pet_001",
      "message": "有人点赞了您的帖子",
      "data": {
        "post_title": "可爱的金毛寻找新家",
        "sender_name": "快速出售"
      },
      "is_read": false,
      "created_at": "2025-01-16T06:30:00.000Z"
    }
  ],
  "unread_count": 5
}
```

### 标记通知已读
**Action**: `markNotificationRead`

**请求参数**:
```javascript
{
  "notificationId": "notif_001",
  "openId": "user_001"
}
```

**响应数据**:
```javascript
{
  "success": true,
  "message": "通知已标记为已读"
}
```

### 批量标记通知已读
**Action**: `markAllNotificationsRead`

**请求参数**:
```javascript
{
  "openId": "user_001"
}
```

---

## ⭐ 收藏系统API

### 切换收藏状态
**Action**: `toggleBookmark`

**请求参数**:
```javascript
{
  "postId": "pet_001",
  "openId": "user_001"
}
```

**响应数据**:
```javascript
{
  "success": true,
  "data": {
    "bookmarked": true,         // 当前收藏状态
    "bookmark_count": 9         // 该帖子总收藏数
  },
  "message": "收藏成功"
}
```

### 获取用户收藏列表
**Action**: `getUserBookmarks`

**请求参数**:
```javascript
{
  "limit": 20,
  "offset": 0,
  "openId": "user_001"
}
```

**响应数据**:
```javascript
{
  "success": true,
  "data": [
    {
      "_id": "bookmark_001",
      "post": {
        "_id": "pet_001",
        "title": "可爱的金毛寻找新家",
        "price": 2000,
        "images": ["image1.jpg"],
        "location": "北京市",
        "created_at": "2025-01-15T10:00:00.000Z"
      },
      "created_at": "2025-01-16T08:00:00.000Z"
    }
  ],
  "total": 23
}
```

---

## 🔍 搜索API

### 搜索宠物
**Action**: `searchPets`

**请求参数**:
```javascript
{
  "keyword": "金毛",           // 搜索关键词
  "category": "狗狗",          // 可选：分类筛选
  "location": "北京",          // 可选：地区筛选
  "priceRange": [0, 3000],     // 可选：价格范围
  "limit": 20,
  "offset": 0
}
```

**响应数据**:
```javascript
{
  "success": true,
  "data": [
    // 搜索结果，格式同getPosts
  ],
  "total": 45,
  "keyword": "金毛"
}
```

---

## 📊 系统配置API

### 获取系统配置
**Action**: `getSystemConfig`

**请求参数**:
```javascript
{
  "keys": ["activity_system", "ad_system"]  // 可选：指定配置键
}
```

**响应数据**:
```javascript
{
  "success": true,
  "data": {
    "activity_system": {
      "enabled": true,
      "allow_comments": true,
      "comment_cooldown": 10,
      "max_comment_length": 100
    },
    "ad_system": {
      "enabled": true,
      "banner_ads": true,
      "feed_ads": true
    }
  }
}
```

---

## 🛡️ 管理员API

### 管理员登录验证
**Action**: `adminLogin`

**请求参数**:
```javascript
{
  "username": "superadminTT",
  "password": "019870416tao"
}
```

### 获取管理统计数据
**Action**: `getAdminStats`

**请求参数**:
```javascript
{
  "period": "7d",              // 统计周期：1d, 7d, 30d
  "openId": "admin_user_id"
}
```

**响应数据**:
```javascript
{
  "success": true,
  "data": {
    "users": {
      "total": 1250,
      "new_today": 15,
      "active_today": 89
    },
    "posts": {
      "total": 3420,
      "new_today": 23,
      "pending_review": 5
    },
    "activities": {
      "active": 2,
      "total_participants": 156,
      "total_votes": 234
    }
  }
}
```

---

## 📝 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| `AUTH_REQUIRED` | 需要用户认证 | 请先登录 |
| `PERMISSION_DENIED` | 权限不足 | 检查用户权限 |
| `INVALID_PARAMS` | 参数错误 | 检查请求参数格式 |
| `RESOURCE_NOT_FOUND` | 资源不存在 | 检查资源ID是否正确 |
| `RATE_LIMIT_EXCEEDED` | 请求频率超限 | 降低请求频率 |
| `SERVER_ERROR` | 服务器内部错误 | 稍后重试或联系技术支持 |

---

## 🔧 SDK使用示例

### JavaScript SDK
```javascript
// 初始化CloudBase
const app = cloudbase.init({
  env: 'yichongyuzhou-3g9112qwf5f3487b'
});

// API调用封装
class PetAPI {
  async callFunction(action, data = {}) {
    try {
      const result = await app.callFunction({
        name: 'pet-api',
        data: { action, data }
      });
      return result.result;
    } catch (error) {
      console.error(`API调用失败 [${action}]:`, error);
      throw error;
    }
  }

  // 获取帖子列表
  async getPosts(params = {}) {
    return this.callFunction('getPosts', params);
  }

  // 创建帖子
  async createPost(postData) {
    return this.callFunction('createPost', postData);
  }

  // 活动投票
  async voteInActivity(voteData) {
    return this.callFunction('voteInActivity', voteData);
  }
}

// 使用示例
const petAPI = new PetAPI();

// 获取帖子列表
const posts = await petAPI.getPosts({
  category: '狗狗',
  limit: 20
});

// 创建帖子
const newPost = await petAPI.createPost({
  title: '可爱的小猫咪',
  description: '寻找爱心主人...',
  category: '猫咪',
  price: 500
});
```

---

## 📋 API测试

### 测试环境
- **测试URL**: `https://yichongyuzhou-3g9112qwf5f3487b-1368816056.tcloudbaseapp.com`
- **测试工具**: Postman, curl, 或浏览器开发者工具

### 测试用例示例
```bash
# 获取帖子列表
curl -X POST \
  https://yichongyuzhou-3g9112qwf5f3487b-1368816056.tcloudbaseapp.com/pet-api \
  -H 'Content-Type: application/json' \
  -d '{
    "action": "getPosts",
    "data": {
      "limit": 5
    }
  }'

# 获取活动列表
curl -X POST \
  https://yichongyuzhou-3g9112qwf5f3487b-1368816056.tcloudbaseapp.com/pet-api \
  -H 'Content-Type: application/json' \
  -d '{
    "action": "getActivities",
    "data": {
      "status": "active"
    }
  }'
```

---

*API文档最后更新时间: 2025年1月16日*
*API版本: v1.0.0*
