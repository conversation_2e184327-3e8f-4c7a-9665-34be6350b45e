// 缓存管理工具
export interface CacheConfig {
  ttl: number; // 生存时间（毫秒）
  maxSize: number; // 最大缓存条目数
  strategy: 'LRU' | 'FIFO' | 'TTL'; // 缓存策略
}

export interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccess: number;
}

// 内存缓存管理器
export class MemoryCache<T> {
  private cache = new Map<string, CacheItem<T>>();
  private config: CacheConfig;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      ttl: 5 * 60 * 1000, // 默认5分钟
      maxSize: 100,
      strategy: 'LRU',
      ...config
    };
  }

  // 设置缓存
  set(key: string, data: T, customTtl?: number): void {
    const now = Date.now();
    const ttl = customTtl || this.config.ttl;

    // 检查缓存大小限制
    if (this.cache.size >= this.config.maxSize) {
      this.evict();
    }

    this.cache.set(key, {
      data,
      timestamp: now,
      ttl,
      accessCount: 0,
      lastAccess: now
    });
  }

  // 获取缓存
  get(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    const now = Date.now();
    
    // 检查是否过期
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    // 更新访问信息
    item.accessCount++;
    item.lastAccess = now;

    return item.data;
  }

  // 删除缓存
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  // 清空缓存
  clear(): void {
    this.cache.clear();
  }

  // 缓存淘汰策略
  private evict(): void {
    if (this.cache.size === 0) return;

    let keyToEvict: string | undefined;

    switch (this.config.strategy) {
      case 'LRU':
        keyToEvict = this.findLRUKey();
        break;
      case 'FIFO':
        keyToEvict = this.findFIFOKey();
        break;
      case 'TTL':
        keyToEvict = this.findExpiredKey();
        break;
      default:
        keyToEvict = this.cache.keys().next().value;
    }

    if (keyToEvict) {
      this.cache.delete(keyToEvict);
    }
  }

  private findLRUKey(): string | undefined {
    let oldestKey: string | undefined;
    let oldestTime = Date.now();

    this.cache.forEach((item, key) => {
      if (item.lastAccess < oldestTime) {
        oldestTime = item.lastAccess;
        oldestKey = key;
      }
    });

    return oldestKey;
  }

  private findFIFOKey(): string | undefined {
    let oldestKey: string | undefined;
    let oldestTime = Date.now();

    this.cache.forEach((item, key) => {
      if (item.timestamp < oldestTime) {
        oldestTime = item.timestamp;
        oldestKey = key;
      }
    });

    return oldestKey;
  }

  private findExpiredKey(): string | undefined {
    const now = Date.now();

    let expiredKey: string | undefined;
    this.cache.forEach((item, key) => {
      if (now - item.timestamp > item.ttl) {
        expiredKey = key;
        return;
      }
    });

    if (expiredKey) {
      return expiredKey;
    }

    // 如果没有过期的，使用LRU策略
    return this.findLRUKey();
  }

  // 获取缓存统计信息
  getStats() {
    const now = Date.now();
    let expired = 0;
    let totalSize = 0;

    this.cache.forEach((item) => {
      if (now - item.timestamp > item.ttl) {
        expired++;
      }
      totalSize += JSON.stringify(item.data).length;
    });

    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      expired,
      totalSize,
      hitRate: this.calculateHitRate()
    };
  }

  private calculateHitRate(): number {
    // 简化的命中率计算
    let totalAccess = 0;
    this.cache.forEach((item) => {
      totalAccess += item.accessCount;
    });
    return totalAccess > 0 ? (this.cache.size / totalAccess) * 100 : 0;
  }
}

// 浏览器存储缓存管理器
export class BrowserStorageCache {
  private prefix: string;
  private storage: Storage | null;

  constructor(prefix: string = 'pet_cache_', useSessionStorage: boolean = false) {
    this.prefix = prefix;
    // 检查是否在浏览器环境
    if (typeof window !== 'undefined') {
      this.storage = useSessionStorage ? sessionStorage : localStorage;
    } else {
      this.storage = null;
    }
  }

  // 设置缓存
  set(key: string, data: any, ttl: number = 24 * 60 * 60 * 1000): void {
    if (!this.storage) return;

    const item = {
      data,
      timestamp: Date.now(),
      ttl
    };

    try {
      this.storage.setItem(this.prefix + key, JSON.stringify(item));
    } catch (error) {
      console.warn('浏览器存储空间不足，清理过期缓存');
      this.cleanup();
      try {
        this.storage.setItem(this.prefix + key, JSON.stringify(item));
      } catch (retryError) {
        console.error('缓存设置失败:', retryError);
      }
    }
  }

  // 获取缓存
  get<T>(key: string): T | null {
    if (!this.storage) return null;

    try {
      const itemStr = this.storage.getItem(this.prefix + key);
      if (!itemStr) return null;

      const item = JSON.parse(itemStr);
      const now = Date.now();

      // 检查是否过期
      if (now - item.timestamp > item.ttl) {
        this.storage.removeItem(this.prefix + key);
        return null;
      }

      return item.data;
    } catch (error) {
      console.error('缓存读取失败:', error);
      return null;
    }
  }

  // 删除缓存
  delete(key: string): void {
    if (!this.storage) return;
    this.storage.removeItem(this.prefix + key);
  }

  // 清理过期缓存
  cleanup(): void {
    if (!this.storage) return;

    const now = Date.now();
    const keysToRemove: string[] = [];

    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i);
      if (key && key.startsWith(this.prefix)) {
        try {
          const itemStr = this.storage.getItem(key);
          if (itemStr) {
            const item = JSON.parse(itemStr);
            if (now - item.timestamp > item.ttl) {
              keysToRemove.push(key);
            }
          }
        } catch (error) {
          keysToRemove.push(key);
        }
      }
    }

    keysToRemove.forEach(key => this.storage!.removeItem(key));
  }

  // 清空所有缓存
  clear(): void {
    if (!this.storage) return;

    const keysToRemove: string[] = [];

    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i);
      if (key && key.startsWith(this.prefix)) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => this.storage!.removeItem(key));
  }
}

// 缓存管理器实例
export const imageCache = new MemoryCache<string>({
  ttl: 30 * 60 * 1000, // 图片缓存30分钟
  maxSize: 50,
  strategy: 'LRU'
});

export const dataCache = new MemoryCache<any>({
  ttl: 5 * 60 * 1000, // 数据缓存5分钟
  maxSize: 100,
  strategy: 'LRU'
});

export const persistentCache = new BrowserStorageCache('pet_app_');

// 缓存装饰器
export function cached(ttl: number = 5 * 60 * 1000) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const cache = new MemoryCache<any>({ ttl });

    descriptor.value = async function (...args: any[]) {
      const key = `${propertyName}_${JSON.stringify(args)}`;
      
      // 尝试从缓存获取
      const cached = cache.get(key);
      if (cached !== null) {
        return cached;
      }

      // 执行原方法
      const result = await method.apply(this, args);
      
      // 缓存结果
      cache.set(key, result);
      
      return result;
    };

    return descriptor;
  };
}
