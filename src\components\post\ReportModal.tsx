import React, { useState } from 'react';
import { Modal, ModalBody } from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import { POST_REPORT_REASONS } from '@/types';
import { petAPI } from '@/lib/cloudbase';
import { showToast } from '@/components/ui/Toast';
import { cn } from '@/utils';

interface ReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  postId: string;
  onSuccess: () => void;
}

const ReportModal: React.FC<ReportModalProps> = ({
  isOpen,
  onClose,
  postId,
  onSuccess,
}) => {
  const [selectedReason, setSelectedReason] = useState<number>(0);
  const [loading, setLoading] = useState(false);

  // 重置状态
  const resetState = () => {
    setSelectedReason(0);
    setLoading(false);
  };

  // 处理关闭
  const handleClose = () => {
    resetState();
    onClose();
  };

  // 提交举报
  const handleSubmit = async () => {
    if (selectedReason === 0) {
      showToast.warning('请选择举报原因');
      return;
    }

    try {
      setLoading(true);
      const result = await petAPI.reportPost({
        postId,
        reason: POST_REPORT_REASONS[selectedReason as keyof typeof POST_REPORT_REASONS],
      });

      if (result.success) {
        onSuccess();
      } else {
        showToast.error(result.message || '举报失败');
      }
    } catch (error: any) {
      showToast.error(error.message || '举报失败');
    } finally {
      setLoading(false);
    }
  };

  const reportReasons = Object.entries(POST_REPORT_REASONS).map(([key, value]) => ({
    id: parseInt(key),
    label: value,
  }));

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="举报帖子"
      size="sm"
    >
      <ModalBody>
        <div className="space-y-6">
          {/* 说明 */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <p className="text-sm text-yellow-800">
              请选择举报原因，我们会认真处理每一个举报。恶意举报可能会影响您的账户信誉。
            </p>
          </div>

          {/* 举报原因选择 */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900">请选择举报原因：</h4>
            <div className="space-y-2">
              {reportReasons.map((reason) => (
                <label
                  key={reason.id}
                  className={cn(
                    'flex items-center p-3 rounded-lg border cursor-pointer transition-colors',
                    selectedReason === reason.id
                      ? 'border-red-500 bg-red-50'
                      : 'border-gray-200 hover:bg-gray-50'
                  )}
                >
                  <input
                    type="radio"
                    name="reportReason"
                    value={reason.id}
                    checked={selectedReason === reason.id}
                    onChange={() => setSelectedReason(reason.id)}
                    className="sr-only"
                  />
                  <div className={cn(
                    'w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center',
                    selectedReason === reason.id
                      ? 'border-red-500 bg-red-500'
                      : 'border-gray-300'
                  )}>
                    {selectedReason === reason.id && (
                      <div className="w-2 h-2 rounded-full bg-white" />
                    )}
                  </div>
                  <span className={cn(
                    'text-sm',
                    selectedReason === reason.id
                      ? 'text-red-700 font-medium'
                      : 'text-gray-700'
                  )}>
                    {reason.label}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* 特别提醒 */}
          {selectedReason === 1 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-sm text-red-800 font-medium">
                ⚠️ 非法野生动物交易是违法行为，我们将严肃处理此类举报。
              </p>
            </div>
          )}

          {/* 按钮 */}
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleClose}
              className="flex-1"
              disabled={loading}
            >
              取消
            </Button>
            <Button
              variant="danger"
              onClick={handleSubmit}
              loading={loading}
              className="flex-1"
              disabled={selectedReason === 0}
            >
              提交举报
            </Button>
          </div>
        </div>
      </ModalBody>
    </Modal>
  );
};

export default ReportModal;
