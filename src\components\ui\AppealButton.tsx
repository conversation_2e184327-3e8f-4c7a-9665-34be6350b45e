import React, { useState } from 'react';
import { Modal, ModalBody } from './Modal';
import Button from './Button';
import { AlertTriangle, MessageSquare } from 'lucide-react';
import { showToast } from './Toast';
import { petAPI } from '@/lib/cloudbase';

interface AppealButtonProps {
  className?: string;
  disabled?: boolean;
}

const AppealButton: React.FC<AppealButtonProps> = ({
  className = '',
  disabled = false
}) => {
  const [showModal, setShowModal] = useState(false);
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (!reason.trim()) {
      showToast.error('请填写申诉理由');
      return;
    }

    try {
      setLoading(true);
      const result = await petAPI.submitAppeal({ reason: reason.trim() });

      if (result.success) {
        showToast.success('申诉已提交，请等待管理员处理');
        setShowModal(false);
        setReason('');
      } else {
        showToast.error(result.message || '申诉提交失败');
      }
    } catch (error: any) {
      console.error('申诉提交失败:', error);
      showToast.error(error.message || '申诉提交失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setShowModal(true)}
        disabled={disabled}
        className={`flex items-center space-x-1 ${className}`}
      >
        <MessageSquare className="w-4 h-4" />
        <span>申诉</span>
      </Button>

      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title="提交申诉"
        size="md"
      >
        <ModalBody>
          <div className="space-y-4">
            {/* 说明 */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-yellow-800">
                  <p className="font-medium mb-1">申诉说明</p>
                  <ul className="list-disc list-inside space-y-1">
                    <li>请详细说明您认为处罚不当的理由</li>
                    <li>提供相关证据或说明情况</li>
                    <li>申诉将由管理员审核，请耐心等待</li>
                    <li>恶意申诉可能影响您的账户信誉</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* 申诉理由输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                申诉理由 <span className="text-red-500">*</span>
              </label>
              <textarea
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="请详细说明您认为处罚不当的理由..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                rows={6}
                maxLength={500}
              />
              <div className="text-right text-xs text-gray-500 mt-1">
                {reason.length}/500
              </div>
            </div>

            {/* 按钮 */}
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowModal(false)}
                className="flex-1"
                disabled={loading}
              >
                取消
              </Button>
              <Button
                variant="primary"
                onClick={handleSubmit}
                loading={loading}
                className="flex-1"
                disabled={!reason.trim() || loading}
              >
                提交申诉
              </Button>
            </div>
          </div>
        </ModalBody>
      </Modal>
    </>
  );
};

export default AppealButton;
