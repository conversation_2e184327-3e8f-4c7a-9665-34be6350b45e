// 宠物页面
Page({
  data: {
    categories: [],
    pets: [],
    selectedCategory: 'all'
  },

  onLoad: function () {
    this.loadCategories()
    this.loadPets()
  },

  loadCategories: function() {
    // 调用云函数获取分类
    wx.cloud.callFunction({
      name: 'pet-api',
      data: {
        action: 'getCategories'
      },
      success: res => {
        this.setData({
          categories: res.result.data || []
        })
      },
      fail: err => {
        console.error('获取分类失败', err)
      }
    })
  },

  loadPets: function() {
    wx.showLoading({
      title: '加载中...',
    })

    wx.cloud.callFunction({
      name: 'pet-api',
      data: {
        action: 'getPets',
        category: this.data.selectedCategory
      },
      success: res => {
        this.setData({
          pets: res.result.data || []
        })
      },
      fail: err => {
        console.error('获取宠物失败', err)
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        })
      },
      complete: () => {
        wx.hideLoading()
      }
    })
  },

  selectCategory: function(e) {
    const category = e.currentTarget.dataset.category
    this.setData({
      selectedCategory: category
    })
    this.loadPets()
  }
})
