'use client';

import React, { useState, useEffect } from 'react';
import { ArrowLeft, AlertTriangle, Heart, MessageCircle, Clock, CheckCircle, ShoppingBag, Search, Settings, X, Copy, ExternalLink } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import Header from '@/components/layout/Header';
import { useAuthContext } from '@/components/auth/AuthProvider';
import { petAPI } from '@/lib/cloudbase';

// 消息类型定义
interface SystemMessage {
  id: string;
  type: 'system';
  title: string;
  content: string;
  timestamp: string;
  read: boolean;
  severity: 'info' | 'warning' | 'error';
  canAppeal?: boolean; // 是否可以申诉
  reportId?: string; // 关联的举报ID
  appealStatus?: 'none' | 'pending' | 'approved' | 'rejected'; // 申诉状态
}

interface ContactMessage {
  id: string;
  type: 'contact';
  fromUser: {
    id: string;
    nickname: string;
    avatar: string;
  };
  toUser: {
    id: string;
    nickname: string;
    avatar: string;
  };
  postInfo: {
    id: string;
    title: string;
    image: string;
    postType: 'selling' | 'breeding' | 'lost' | 'wanted';
  };
  contactInfo: {
    method: 'wechat' | 'phone';
    value: string;
  };
  timestamp: string;
  read: boolean;
}

type Message = SystemMessage | ContactMessage;

const MessagesPage: React.FC = () => {
  const router = useRouter();
  const { user, isLoggedIn } = useAuthContext();
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'trade' | 'breeding' | 'lost' | 'system'>('trade');
  const [showAppealModal, setShowAppealModal] = useState(false);
  const [appealingMessage, setAppealingMessage] = useState<SystemMessage | null>(null);
  const [appealReason, setAppealReason] = useState('');

  // 加载通知数据
  const loadNotifications = async () => {
    if (!isLoggedIn) return;

    try {
      setLoading(true);
      const result = await petAPI.getUserNotifications({
        limit: 50,
        type: 'contact'
      });

      if (result.success) {
        // 转换为Message格式
        const convertedMessages: Message[] = (result.data || []).map((notification: any) => {
          if (notification.type === 'contact') {
            return {
              id: notification._id,
              type: 'contact',
              fromUser: {
                id: notification.sender_id,
                nickname: notification.data?.sender_nickname || notification.data?.author_nickname || '用户',
                avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
              },
              toUser: {
                id: notification.recipient_id,
                nickname: user?.nickname || '',
                avatar: user?.avatar_url || ''
              },
              postInfo: {
                id: notification.post_id,
                title: notification.data?.post_title || '宠物帖子',
                image: 'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=300&h=300&fit=crop',
                postType: notification.data?.post_type || 'selling'
              },
              contactInfo: {
                method: notification.data?.sender_contact?.type || notification.data?.author_contact?.type || 'wechat',
                value: notification.data?.sender_contact?.value || notification.data?.author_contact?.value || '未提供'
              },
              timestamp: notification.created_at,
              read: notification.read || false
            };
          } else {
            return {
              id: notification._id,
              type: 'system',
              title: notification.message,
              content: notification.message,
              timestamp: notification.created_at,
              read: notification.read || false,
              severity: 'info'
            };
          }
        });

        setMessages(convertedMessages);
      } else {
        // 如果API失败，使用模拟数据
        loadMockData();
      }
    } catch (error) {
      console.error('加载通知失败:', error);
      // 如果API失败，使用模拟数据
      loadMockData();
    } finally {
      setLoading(false);
    }
  };

  // 模拟数据加载函数
  const loadMockData = () => {
      const mockMessages: Message[] = [
        {
          id: '1',
          type: 'system',
          title: '发布限制通知',
          content: '由于您发布的内容违反了社区规定，您的账号被限制发布功能7天。限制期间：2024年1月1日 - 2024年1月8日。有意见可以申诉。',
          timestamp: '2024-01-01T10:00:00Z',
          read: false,
          severity: 'warning',
          canAppeal: true,
          reportId: 'report-123',
          appealStatus: 'none'
        },
        {
          id: '2',
          type: 'contact',
          fromUser: {
            id: 'user-2',
            nickname: '爱猫人士',
            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
          },
          toUser: {
            id: user?._id || '',
            nickname: user?.nickname || '',
            avatar: user?.avatar_url || ''
          },
          postInfo: {
            id: 'post-1',
            title: '可爱的金毛宝宝找新家',
            image: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=300&h=300&fit=crop',
            postType: 'selling'
          },
          contactInfo: {
            method: 'wechat',
            value: 'wechat_user123'
          },
          timestamp: '2024-01-02T14:30:00Z',
          read: false
        },
        {
          id: '3',
          type: 'contact',
          fromUser: {
            id: 'user-3',
            nickname: '繁育专家',
            avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
          },
          toUser: {
            id: user?._id || '',
            nickname: user?.nickname || '',
            avatar: user?.avatar_url || ''
          },
          postInfo: {
            id: 'post-2',
            title: '优质拉布拉多配种服务',
            image: 'https://images.unsplash.com/photo-1518717758536-85ae29035b6d?w=300&h=300&fit=crop',
            postType: 'breeding'
          },
          contactInfo: {
            method: 'phone',
            value: '138****8888'
          },
          timestamp: '2024-01-02T16:20:00Z',
          read: false
        },
        {
          id: '4',
          type: 'contact',
          fromUser: {
            id: 'user-4',
            nickname: '寻宠志愿者',
            avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'
          },
          toUser: {
            id: user?._id || '',
            nickname: user?.nickname || '',
            avatar: user?.avatar_url || ''
          },
          postInfo: {
            id: 'post-3',
            title: '寻找走失的橘猫咪咪',
            image: 'https://images.unsplash.com/photo-1574158622682-e40e69881006?w=300&h=300&fit=crop',
            postType: 'lost'
          },
          contactInfo: {
            method: 'wechat',
            value: 'helper_volunteer'
          },
          timestamp: '2024-01-02T18:45:00Z',
          read: true
        },
        {
          id: '5',
          type: 'contact',
          fromUser: {
            id: 'user-5',
            nickname: '宠物爱好者',
            avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
          },
          toUser: {
            id: user?._id || '',
            nickname: user?.nickname || '',
            avatar: user?.avatar_url || ''
          },
          postInfo: {
            id: 'post-4',
            title: '求购健康的边牧幼犬',
            image: 'https://images.unsplash.com/photo-1551717743-49959800b1f6?w=300&h=300&fit=crop',
            postType: 'wanted'
          },
          contactInfo: {
            method: 'wechat',
            value: 'wechat123456'
          },
          timestamp: '2024-01-01T20:15:00Z',
          read: false
        },
        {
          id: '6',
          type: 'system',
          title: '平台欢迎通知',
          content: '欢迎您加入我们的宠物交易平台！请遵守社区规定，文明交易，共同维护良好的交易环境。如需帮助，请查看使用指南。',
          timestamp: '2023-12-25T09:00:00Z',
          read: true,
          severity: 'info'
        }
      ];
      setMessages(mockMessages);
    };

  useEffect(() => {
    if (!isLoggedIn) {
      router.push('/');
      return;
    }

    loadNotifications();
  }, [isLoggedIn, router, activeTab]);

  // 过滤消息
  const filteredMessages = messages.filter(message => {
    if (activeTab === 'system') {
      return message.type === 'system';
    }

    if (message.type === 'contact') {
      const contactMessage = message as ContactMessage;
      switch (activeTab) {
        case 'trade':
          return contactMessage.postInfo.postType === 'selling' || contactMessage.postInfo.postType === 'wanted';
        case 'breeding':
          return contactMessage.postInfo.postType === 'breeding';
        case 'lost':
          return contactMessage.postInfo.postType === 'lost';
        default:
          return false;
      }
    }

    return false;
  });

  // 标记消息为已读
  const markAsRead = (messageId: string) => {
    setMessages(prev => prev.map(msg =>
      msg.id === messageId ? { ...msg, read: true } : msg
    ));
  };

  // 删除消息
  const handleDeleteMessage = async (messageId: string) => {
    if (deletingId) return; // 防止重复点击

    setDeletingId(messageId);

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));

      // 从列表中移除消息
      setMessages(prev => prev.filter(msg => msg.id !== messageId));

      // 显示成功提示（这里可以用toast）
      console.log('消息已删除');
    } catch (error) {
      console.error('删除失败:', error);
    } finally {
      setDeletingId(null);
    }
  };

  // 处理申诉
  const handleAppeal = (message: SystemMessage) => {
    setAppealingMessage(message);
    setShowAppealModal(true);
  };

  // 提交申诉
  const submitAppeal = async () => {
    if (!appealingMessage || !appealReason.trim()) {
      alert('请填写申诉理由');
      return;
    }

    if (!appealingMessage.reportId) {
      alert('申诉信息不完整');
      return;
    }

    try {
      const result = await petAPI.submitAppeal({
        reportId: appealingMessage.reportId,
        reason: appealReason,
        type: 'post' // 默认为帖子申诉，实际应根据消息类型确定
      });

      if (result.success) {
        // 更新消息状态
        setMessages(prev => prev.map(msg =>
          msg.id === appealingMessage.id
            ? { ...msg, appealStatus: 'pending' as const }
            : msg
        ));

        // 关闭模态框
        setShowAppealModal(false);
        setAppealingMessage(null);
        setAppealReason('');

        alert('申诉已提交，我们会在3个工作日内处理');
      } else {
        alert(result.message || '申诉提交失败');
      }
    } catch (error: any) {
      console.error('申诉提交失败:', error);
      alert(error.message || '申诉提交失败，请稍后重试');
    }
  };

  // 清空分类通知
  const handleClearCategory = async () => {
    const confirmMessage = {
      'trade': '确定清空所有买卖通知吗？',
      'breeding': '确定清空所有配种通知吗？',
      'lost': '确定清空所有寻宠通知吗？',
      'system': '确定清空所有系统通知吗？'
    }[activeTab];

    if (!confirm(confirmMessage)) return;

    try {
      // 获取当前分类的消息ID
      const idsToDelete = filteredMessages.map(msg => msg.id);

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));

      // 从列表中移除这些消息
      setMessages(prev => prev.filter(msg => !idsToDelete.includes(msg.id)));

      console.log(`已清空${filteredMessages.length}条通知`);
    } catch (error) {
      console.error('清空失败:', error);
    }
  };

  // 格式化时间
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return `${Math.floor(diffInHours / 24)}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  // 获取系统消息图标
  const getSystemIcon = (severity: string) => {
    switch (severity) {
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-orange-500" />;
      case 'error':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      default:
        return <CheckCircle className="w-5 h-5 text-blue-500" />;
    }
  };

  if (!isLoggedIn) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-4xl mx-auto px-4 py-6">
        {/* 页面标题 */}
        <div className="flex items-center mb-6">
          <button
            onClick={() => router.back()}
            className="mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <h1 className="text-2xl font-bold text-gray-900">通知中心</h1>
        </div>

        {/* 标签页导航 */}
        <div className="flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('trade')}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'trade'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <ShoppingBag className="w-4 h-4" />
            <span>买卖通知</span>
          </button>
          <button
            onClick={() => setActiveTab('breeding')}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'breeding'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Heart className="w-4 h-4" />
            <span>配种通知</span>
          </button>
          <button
            onClick={() => setActiveTab('lost')}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'lost'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Search className="w-4 h-4" />
            <span>寻宠通知</span>
          </button>
          <button
            onClick={() => setActiveTab('system')}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'system'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Settings className="w-4 h-4" />
            <span>系统通知</span>
          </button>
        </div>

        {/* 清空按钮 */}
        {filteredMessages.length > 0 && (
          <div className="flex justify-end mb-4">
            <button
              onClick={handleClearCategory}
              className="px-4 py-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
            >
              清空当前分类
            </button>
          </div>
        )}

        {/* 消息列表 */}
        <div className="space-y-4">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="text-gray-500 mt-4">加载中...</p>
            </div>
          ) : filteredMessages.length === 0 ? (
            <div className="text-center py-12">
              <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无通知</h3>
              <p className="text-gray-500">您还没有收到任何通知</p>
            </div>
          ) : (
            filteredMessages.map((message) => (
              <div
                key={message.id}
                className={`bg-white rounded-lg border p-4 hover:shadow-md transition-shadow cursor-pointer ${
                  !message.read ? 'border-blue-200 bg-blue-50' : 'border-gray-200'
                }`}
                onClick={() => markAsRead(message.id)}
              >
                {message.type === 'system' ? (
                  // 系统消息
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      {getSystemIcon((message as SystemMessage).severity)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {(message as SystemMessage).title}
                        </h3>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-500">
                            {formatTime(message.timestamp)}
                          </span>
                          {!message.read && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteMessage(message.id);
                            }}
                            disabled={deletingId === message.id}
                            className="p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50"
                          >
                            {deletingId === message.id ? (
                              <div className="w-3 h-3 border border-gray-300 border-t-transparent rounded-full animate-spin"></div>
                            ) : (
                              <X className="w-3 h-3" />
                            )}
                          </button>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 line-clamp-2">
                        {(message as SystemMessage).content}
                      </p>

                      {/* 申诉按钮 */}
                      {(message as SystemMessage).canAppeal && (
                        <div className="mt-3 pt-3 border-t border-gray-200">
                          {(message as SystemMessage).appealStatus === 'none' && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleAppeal(message as SystemMessage);
                              }}
                              className="text-sm bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
                            >
                              申诉
                            </button>
                          )}
                          {(message as SystemMessage).appealStatus === 'pending' && (
                            <span className="text-sm text-orange-600 bg-orange-100 px-3 py-1 rounded-full">
                              申诉处理中...
                            </span>
                          )}
                          {(message as SystemMessage).appealStatus === 'approved' && (
                            <span className="text-sm text-green-600 bg-green-100 px-3 py-1 rounded-full">
                              申诉已通过，处罚已撤销
                            </span>
                          )}
                          {(message as SystemMessage).appealStatus === 'rejected' && (
                            <span className="text-sm text-red-600 bg-red-100 px-3 py-1 rounded-full">
                              申诉已驳回，处罚维持
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  // 交易消息
                  <div className="flex items-start space-x-3">
                    <img
                      src={(message as ContactMessage).fromUser.avatar}
                      alt={(message as ContactMessage).fromUser.nickname}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-sm font-medium text-gray-900">
                          {(() => {
                            const contactMsg = message as ContactMessage;
                            switch (contactMsg.postInfo.postType) {
                              case 'selling':
                                return '有用户想购买您的宠物';
                              case 'wanted':
                                return '有卖家回应了您的求购';
                              case 'breeding':
                                return '有用户需要您的配种服务';
                              case 'lost':
                                return '有用户提供了走失线索';
                              default:
                                return '有用户联系了您';
                            }
                          })()}
                        </h3>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-500">
                            {formatTime(message.timestamp)}
                          </span>
                          {!message.read && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteMessage(message.id);
                            }}
                            disabled={deletingId === message.id}
                            className="p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50"
                          >
                            {deletingId === message.id ? (
                              <div className="w-3 h-3 border border-gray-300 border-t-transparent rounded-full animate-spin"></div>
                            ) : (
                              <X className="w-3 h-3" />
                            )}
                          </button>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3 mb-3">
                        <img
                          src={(message as ContactMessage).postInfo.image}
                          alt={(message as ContactMessage).postInfo.title}
                          className="w-12 h-12 rounded-lg object-cover"
                        />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm text-gray-900 truncate">
                            {(message as ContactMessage).postInfo.title}
                          </p>
                          <p className="text-xs text-gray-500">
                            {(() => {
                              const contactMsg = message as ContactMessage;
                              switch (contactMsg.postInfo.postType) {
                                case 'selling':
                                  return `买家：${contactMsg.fromUser.nickname}`;
                                case 'wanted':
                                  return `卖家：${contactMsg.fromUser.nickname}`;
                                case 'breeding':
                                  return `需求方：${contactMsg.fromUser.nickname}`;
                                case 'lost':
                                  return `提供者：${contactMsg.fromUser.nickname}`;
                                default:
                                  return `联系人：${contactMsg.fromUser.nickname}`;
                              }
                            })()}
                          </p>
                        </div>
                        <button className="text-xs text-blue-600 hover:text-blue-800 transition-colors">
                          查看帖子
                        </button>
                      </div>
                      <div className="bg-blue-50 rounded-lg p-3 border border-blue-200">
                        <p className="text-xs text-blue-600 mb-2 font-medium">买家联系方式</p>
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-gray-900">
                            {(message as ContactMessage).contactInfo.method === 'wechat' && '微信：'}
                            {(message as ContactMessage).contactInfo.method === 'phone' && '电话：'}
                            {(message as ContactMessage).contactInfo.value}
                          </p>
                          <button
                            className="text-xs bg-blue-500 text-white px-3 py-1 rounded-full hover:bg-blue-600 transition-colors"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigator.clipboard.writeText((message as ContactMessage).contactInfo.value);
                              // 这里可以添加复制成功的提示
                            }}
                          >
                            复制
                          </button>
                        </div>

                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </main>

      {/* 申诉模态框 */}
      {showAppealModal && appealingMessage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">申诉处罚</h3>

            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">处罚内容：</p>
              <p className="text-sm bg-gray-100 p-3 rounded-lg">{appealingMessage.content}</p>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                申诉理由 <span className="text-red-500">*</span>
              </label>
              <textarea
                value={appealReason}
                onChange={(e) => setAppealReason(e.target.value)}
                placeholder="请详细说明您认为处罚不当的理由..."
                className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              />
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => {
                  setShowAppealModal(false);
                  setAppealingMessage(null);
                  setAppealReason('');
                }}
                className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                取消
              </button>
              <button
                onClick={submitAppeal}
                disabled={!appealReason.trim()}
                className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                提交申诉
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MessagesPage;
