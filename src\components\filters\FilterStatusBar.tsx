'use client';

import React from 'react';
import { X, RotateCcw } from 'lucide-react';
import { FilterState } from '@/hooks/usePostFilters';

interface FilterStatusBarProps {
  filters: FilterState;
  onRemoveFilter: (key: keyof FilterState) => void;
  onClearAll: () => void;
  hasActiveFilters: boolean;
  totalCount?: number;
}

const FilterStatusBar: React.FC<FilterStatusBarProps> = ({
  filters,
  onRemoveFilter,
  onClearAll,
  hasActiveFilters,
  totalCount
}) => {
  if (!hasActiveFilters) {
    return null;
  }

  const getFilterDisplayName = (key: keyof FilterState, value: any): string => {
    switch (key) {
      case 'category':
        return `分类: ${value}`;
      case 'petType':
        const typeMap = {
          breeding: '配种',
          selling: '出售',
          lost: '寻回'
        };
        return `类型: ${typeMap[value as keyof typeof typeMap] || value}`;
      case 'location':
        return `地区: ${value}`;
      case 'breed':
        return `品种: ${value}`;
      case 'sortBy':
        const sortMap = {
          created_at: '最新发布',
          likes_count: '最多点赞',
          wants_count: '最想要',
          avg_rating: '最高评分',
          priority: '智能推荐'
        };
        return `排序: ${sortMap[value as keyof typeof sortMap] || value}`;
      default:
        return `${key}: ${value}`;
    }
  };

  const activeFilters = Object.entries(filters).filter(([key, value]) => {
    if (key === 'page' || key === 'limit') return false;
    if (key === 'petType' && value === 'all') return false;
    if (key === 'sortBy' && value === 'priority') return false;
    return value && value !== '';
  });

  return (
    <div className="bg-white border-b border-gray-200 px-4 py-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 flex-wrap">
          <span className="text-sm text-gray-600 font-medium">
            筛选条件:
          </span>
          
          {activeFilters.map(([key, value]) => (
            <div
              key={key}
              className="inline-flex items-center bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-1 rounded-full"
            >
              <span>{getFilterDisplayName(key as keyof FilterState, value)}</span>
              <button
                onClick={() => onRemoveFilter(key as keyof FilterState)}
                className="ml-1.5 inline-flex items-center justify-center w-3 h-3 text-blue-600 hover:text-blue-800 hover:bg-blue-200 rounded-full transition-colors"
                aria-label={`移除${key}筛选条件`}
              >
                <X className="w-2 h-2" />
              </button>
            </div>
          ))}
          
          {totalCount !== undefined && (
            <span className="text-sm text-gray-500">
              共找到 {totalCount} 个结果
            </span>
          )}
        </div>

        <button
          onClick={onClearAll}
          className="inline-flex items-center text-sm text-gray-600 hover:text-gray-800 transition-colors"
        >
          <RotateCcw className="w-4 h-4 mr-1" />
          清除全部
        </button>
      </div>
    </div>
  );
};

export default FilterStatusBar;
