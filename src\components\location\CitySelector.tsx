'use client';

import { useState, useEffect } from 'react';
import { MapPin, Search, X, Navigation } from 'lucide-react';
import { City, hotCities, allCities, searchCities } from '@/data/cities';
import AmapLocation, { LocationInfo } from './AmapLocation';

interface CitySelectorProps {
  selectedCity: City | null;
  onCityChange: (city: City) => void;
  className?: string;
}

const CitySelector: React.FC<CitySelectorProps> = ({
  selectedCity,
  onCityChange,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [searchResults, setSearchResults] = useState<City[]>([]);
  const [activeTab, setActiveTab] = useState<'hot' | 'all' | 'search'>('hot');

  // 搜索城市
  useEffect(() => {
    if (searchKeyword.trim()) {
      const results = searchCities(searchKeyword);
      setSearchResults(results);
      setActiveTab('search');
    } else {
      setSearchResults([]);
      setActiveTab('hot');
    }
  }, [searchKeyword]);

  // 选择城市
  const handleCitySelect = (city: City) => {
    onCityChange(city);
    setIsOpen(false);
    setSearchKeyword('');
  };

  // 定位成功回调
  const handleLocationSuccess = (location: LocationInfo) => {
    handleCitySelect(location.city);
  };

  // 定位失败回调
  const handleLocationError = (error: string) => {
    console.error('定位失败:', error);
    // 可以显示错误提示
  };

  return (
    <div className={`relative ${className}`}>
      {/* 城市选择按钮 */}
      <button
        onClick={() => setIsOpen(true)}
        className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors"
      >
        <MapPin className="h-4 w-4" />
        <span>{selectedCity?.name || '选择城市'}</span>
        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* 城市选择弹窗 */}
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            {/* 背景遮罩 */}
            <div 
              className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
              onClick={() => setIsOpen(false)}
            />

            {/* 弹窗内容 */}
            <div className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
              {/* 头部 */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">选择城市</h3>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              {/* 定位按钮 */}
              <div className="p-4 border-b border-gray-200">
                <AmapLocation
                  onLocationSuccess={handleLocationSuccess}
                  onLocationError={handleLocationError}
                >
                  {({ getCurrentLocation, loading, error }) => (
                    <button
                      onClick={getCurrentLocation}
                      disabled={loading}
                      className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors disabled:opacity-50"
                    >
                      <Navigation className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                      <span>{loading ? '定位中...' : '自动定位'}</span>
                    </button>
                  )}
                </AmapLocation>
              </div>

              {/* 搜索框 */}
              <div className="p-4 border-b border-gray-200">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="搜索城市"
                    value={searchKeyword}
                    onChange={(e) => setSearchKeyword(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* 城市列表 */}
              <div className="max-h-96 overflow-y-auto">
                {/* 搜索结果 */}
                {activeTab === 'search' && (
                  <div className="p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-3">搜索结果</h4>
                    {searchResults.length > 0 ? (
                      <div className="grid grid-cols-3 gap-2">
                        {searchResults.map((city) => (
                          <button
                            key={city.code}
                            onClick={() => handleCitySelect(city)}
                            className="px-3 py-2 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors text-center"
                          >
                            {city.name}
                          </button>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500 text-center py-4">未找到相关城市</p>
                    )}
                  </div>
                )}

                {/* 热门城市 */}
                {activeTab === 'hot' && (
                  <div className="p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-3">热门城市</h4>
                    <div className="grid grid-cols-3 gap-2">
                      {hotCities.map((city) => (
                        <button
                          key={city.code}
                          onClick={() => handleCitySelect(city)}
                          className="px-3 py-2 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors text-center"
                        >
                          {city.name}
                        </button>
                      ))}
                    </div>

                    {/* 切换到全部城市 */}
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <button
                        onClick={() => setActiveTab('all')}
                        className="w-full px-3 py-2 text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors"
                      >
                        查看全部城市
                      </button>
                    </div>
                  </div>
                )}

                {/* 全部城市 */}
                {activeTab === 'all' && (
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-sm font-medium text-gray-900">全部城市</h4>
                      <button
                        onClick={() => setActiveTab('hot')}
                        className="text-sm text-blue-600 hover:text-blue-700"
                      >
                        返回热门
                      </button>
                    </div>
                    
                    {Object.entries(allCities).map(([letter, cities]) => (
                      <div key={letter} className="mb-4">
                        <h5 className="text-xs font-medium text-gray-500 mb-2 sticky top-0 bg-white py-1">
                          {letter}
                        </h5>
                        <div className="grid grid-cols-3 gap-2">
                          {cities.map((city) => (
                            <button
                              key={city.code}
                              onClick={() => handleCitySelect(city)}
                              className="px-3 py-2 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors text-center"
                            >
                              {city.name}
                            </button>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CitySelector;
