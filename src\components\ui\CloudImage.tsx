import React, { useState, useEffect } from 'react';
import { getImageUrl, petAPI } from '@/lib/cloudbase';

// 检查字符串是否是云开发的fileID
const isCloudFileID = (str: string): boolean => {
  return str.startsWith('cloud://') || (!str.startsWith('http') && !str.startsWith('/'));
};

interface CloudImageProps {
  fileId: string;
  alt?: string;
  className?: string;
  width?: number;
  height?: number;
  style?: React.CSSProperties;
  onClick?: () => void;
}

export const CloudImage: React.FC<CloudImageProps> = ({
  fileId,
  alt = '',
  className = '',
  width,
  height,
  style,
  onClick
}) => {
  const [imageUrl, setImageUrl] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    const loadImage = async () => {
      if (!fileId) {
        setError(true);
        setLoading(false);
        return;
      }

      // 如果已经是完整的URL，直接使用
      if (fileId.startsWith('http')) {
        setImageUrl(fileId);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(false);
        setRetryCount(0);
        const url = await getImageUrl(fileId);
        setImageUrl(url);
      } catch (err) {
        console.error('加载图片失败:', err);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    loadImage();
  }, [fileId]);

  if (loading) {
    return (
      <div 
        className={`bg-gray-200 animate-pulse flex items-center justify-center ${className}`}
        style={{ width, height, ...style }}
      >
        <div className="text-gray-400 text-sm">加载中...</div>
      </div>
    );
  }

  if (error || !imageUrl) {
    return (
      <div 
        className={`bg-gray-200 flex items-center justify-center ${className}`}
        style={{ width, height, ...style }}
      >
        <div className="text-gray-400 text-sm">图片加载失败</div>
      </div>
    );
  }

  return (
    <img
      src={imageUrl}
      alt={alt}
      className={className}
      width={width}
      height={height}
      style={style}
      onClick={onClick}
      onError={async () => {
        console.log('图片加载失败，尝试刷新URL，重试次数:', retryCount);

        if (retryCount < 2) { // 最多重试2次
          setRetryCount(prev => prev + 1);

          // 只有当fileId是云开发fileID时才尝试刷新URL
          if (isCloudFileID(fileId)) {
            try {
              // 尝试刷新图片URL
              const result = await petAPI.getImage({ fileId });
              if (result.success && result.data?.url) {
                console.log('获取新的图片URL:', result.data.url);
                setImageUrl(result.data.url);
              } else {
                console.error('刷新图片URL失败:', result.message);
                setError(true);
              }
            } catch (error) {
              console.error('刷新图片URL异常:', error);
              setError(true);
            }
          } else {
            // 如果是普通URL，直接标记为错误
            console.log('普通URL加载失败，无法刷新');
            setError(true);
          }
        } else {
          setError(true);
        }
      }}
    />
  );
};

export default CloudImage;
