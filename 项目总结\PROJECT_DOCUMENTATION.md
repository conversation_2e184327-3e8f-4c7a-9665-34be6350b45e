# 宠物交易平台 - 完整项目文档

## 📋 项目概述

### 项目名称
**宠物交易平台** (Pet Trading Platform)

### 项目简介
一个基于腾讯云开发(CloudBase)的现代化宠物交易与社区平台，提供宠物发布、交易、社区互动、活动参与等全方位服务。

### 技术栈
- **前端**: Next.js 14 + TypeScript + Tailwind CSS
- **后端**: 腾讯云开发 CloudBase
- **数据库**: 云数据库 MongoDB
- **云函数**: Node.js
- **存储**: 云存储 + 静态网站托管
- **部署**: 腾讯云开发环境

### 项目特色
- 🎯 **智能内容管理** - AI驱动的帖子质量评分和排序
- 🎊 **社区活动系统** - 投票、评论、互动功能
- 📢 **非侵入式广告** - 用户友好的广告展示
- 🔔 **实时通知系统** - 完整的消息通知机制
- ⭐ **个性化收藏** - 用户收藏和个人中心
- 🛡️ **安全管理** - 举报、黑名单、内容审核

---

## 🎨 设计理念

### 核心设计原则

#### 1. 用户体验优先
- **简洁直观的界面设计**
- **响应式布局适配各种设备**
- **流畅的交互体验**
- **最小化用户操作步骤**

#### 2. 内容质量导向
- **智能质量评分算法**
- **优质内容优先展示**
- **广告内容自动降权**
- **用户信誉体系建设**

#### 3. 社区生态建设
- **鼓励用户互动参与**
- **定期社区活动组织**
- **公平透明的管理机制**
- **多元化的交流方式**

#### 4. 安全可靠保障
- **完善的举报机制**
- **内容审核和过滤**
- **用户隐私保护**
- **数据安全存储**

### UI/UX设计风格

#### 视觉设计
- **现代简约风格**
- **温暖的色彩搭配**
- **清晰的信息层级**
- **友好的宠物主题元素**

#### 交互设计
- **直观的导航结构**
- **一致的操作模式**
- **及时的反馈机制**
- **无障碍访问支持**

---

## 🏗️ 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层 (Next.js)                      │
├─────────────────────────────────────────────────────────────┤
│  主页  │ 搜索  │ 发布  │ 个人中心 │ 活动  │ 通知  │ 管理后台  │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      API网关层 (CloudBase)                   │
├─────────────────────────────────────────────────────────────┤
│              统一云函数入口 (pet-api)                        │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                        业务逻辑层                            │
├─────────────────────────────────────────────────────────────┤
│ 用户管理 │ 帖子管理 │ 活动系统 │ 通知系统 │ 广告系统 │ 权重系统 │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                        数据存储层                            │
├─────────────────────────────────────────────────────────────┤
│   云数据库   │   云存储   │   静态托管   │   配置管理        │
└─────────────────────────────────────────────────────────────┘
```

### 数据库设计

#### 核心数据集合
- **users** - 用户信息
- **pets** - 宠物帖子
- **activities** - 社区活动
- **notifications** - 通知消息
- **bookmarks** - 用户收藏
- **admins** - 管理员账户
- **system_config** - 系统配置

---

## 🎯 功能设计

### 功能模块总览

#### 1. 用户系统
- **用户注册/登录**
- **个人资料管理**
- **用户信誉评级**
- **行为统计分析**

#### 2. 内容管理
- **宠物信息发布**
- **图片上传处理**
- **内容质量评分**
- **智能排序推荐**

#### 3. 社区互动
- **点赞收藏功能**
- **评论互动系统**
- **用户关注机制**
- **消息通知推送**

#### 4. 活动系统
- **投票活动创建**
- **用户参与统计**
- **评论互动管理**
- **活动结果展示**

#### 5. 广告系统
- **广告位管理**
- **内容智能识别**
- **用户体验优化**
- **收益统计分析**

#### 6. 管理后台
- **用户管理**
- **内容审核**
- **活动管理**
- **数据统计**

---

## 📱 页面结构设计

### 前端页面分布

#### 用户端页面
```
/                    - 主页 (宠物列表展示)
├── /search          - 搜索页面
├── /upload          - 发布宠物信息
├── /profile         - 个人主页
├── /profile/[id]    - 他人主页
├── /favorites       - 收藏列表
├── /notifications   - 通知中心
├── /messages        - 消息页面
├── /activities      - 活动中心
└── /post/detail     - 帖子详情
```

#### 管理端页面
```
/admin               - 管理员登录
├── /dashboard       - 管理仪表板
├── /posts           - 帖子管理
├── /activities      - 活动管理
└── /ads             - 广告管理
```

### 页面功能详述

#### 主页 (/)
- **功能**: 展示所有宠物信息，支持筛选和排序
- **特色**: 
  - 智能权重排序
  - 广告内容展示
  - 无限滚动加载
  - 响应式卡片布局

#### 搜索页面 (/search)
- **功能**: 全文搜索宠物信息
- **特色**:
  - 实时搜索建议
  - 多条件筛选
  - 搜索历史记录
  - 热门搜索推荐

#### 发布页面 (/upload)
- **功能**: 发布宠物交易信息
- **特色**:
  - 图片批量上传
  - 表单验证提示
  - 自动保存草稿
  - 发布预览功能

#### 个人中心 (/profile)
- **功能**: 用户个人信息管理
- **特色**:
  - 统计数据展示
  - 发布历史管理
  - 个人设置配置
  - 头像上传功能

#### 活动中心 (/activities)
- **功能**: 社区活动参与
- **特色**:
  - 投票参与功能
  - 评论互动系统
  - 活动进度展示
  - 结果统计分析

---

## 🔄 业务流程设计

### 用户注册流程
```mermaid
graph TD
    A[访问网站] --> B[点击注册]
    B --> C[填写用户信息]
    C --> D[验证信息格式]
    D --> E{验证通过?}
    E -->|是| F[创建用户账户]
    E -->|否| C
    F --> G[自动登录]
    G --> H[跳转个人中心]
```

### 宠物发布流程
```mermaid
graph TD
    A[用户登录] --> B[点击发布]
    B --> C[填写宠物信息]
    C --> D[上传宠物图片]
    D --> E[预览发布内容]
    E --> F{确认发布?}
    F -->|是| G[提交到云函数]
    F -->|否| C
    G --> H[质量评分计算]
    H --> I[保存到数据库]
    I --> J[跳转个人主页]
```

### 活动参与流程
```mermaid
graph TD
    A[浏览活动列表] --> B[选择感兴趣活动]
    B --> C[查看活动详情]
    C --> D{需要投票?}
    D -->|是| E[选择投票选项]
    D -->|否| F[查看活动内容]
    E --> G[提交投票]
    G --> H[更新统计数据]
    H --> I{允许评论?}
    I -->|是| J[发表评论]
    I -->|否| K[查看结果]
    J --> K
    F --> I
```

---

## ⚙️ 核心算法设计

### 帖子质量评分算法

#### 评分维度 (总分100分)
1. **内容质量 (30分)**
   - 标题长度和质量: 0-10分
   - 描述详细程度: 0-15分
   - 信息完整性: 0-5分

2. **用户信誉 (25分)**
   - 用户历史评分: 0-15分
   - 成功交易次数: 0-10分

3. **互动数据 (20分)**
   - 浏览量权重: 0-8分
   - 点赞数权重: 0-7分
   - 收藏数权重: 0-5分

4. **时间因素 (15分)**
   - 发布时间新鲜度: 0-10分
   - 最后更新时间: 0-5分

5. **违规扣分 (-50到0分)**
   - 举报次数扣分
   - 违规内容扣分

6. **广告降权 (-20到0分)**
   - 广告关键词检测
   - 联系方式识别
   - 商业性内容判断

### 广告内容识别算法

#### 识别规则
```javascript
// 广告关键词库
const adKeywords = [
  '微信', '联系', '电话', 'QQ', '加我', '私聊', '详谈',
  '优惠', '促销', '打折', '特价', '限时', '包邮',
  '代理', '加盟', '招商', '合作', '投资', '赚钱'
];

// 降权计算
function calculateAdPenalty(post) {
  let penalty = 0;
  
  // 检查广告标记
  if (post.has_ads) penalty -= 15;
  
  // 关键词匹配
  const keywordCount = countAdKeywords(post.description);
  if (keywordCount >= 3) penalty -= 10;
  else if (keywordCount >= 1) penalty -= 5;
  
  // 联系方式检测
  if (hasContactInfo(post)) penalty -= 8;
  
  return Math.max(-20, penalty);
}
```

---

## 📊 数据统计设计

### 用户行为统计
- **发布统计**: 发布数量、成功率、平均质量分
- **互动统计**: 点赞数、收藏数、评论数
- **活动参与**: 投票次数、评论次数、活跃度

### 平台运营统计
- **内容统计**: 总帖子数、日新增、质量分布
- **用户统计**: 注册用户数、活跃用户、留存率
- **活动统计**: 活动参与率、投票统计、评论分析

### 管理后台数据看板
- **实时数据**: 在线用户、今日发布、待审核内容
- **趋势分析**: 用户增长、内容质量、活跃度变化
- **异常监控**: 违规内容、异常行为、系统错误

---

## 🔐 安全设计

### 用户安全
- **密码加密**: bcrypt哈希加密存储
- **会话管理**: JWT token认证
- **权限控制**: 基于角色的访问控制
- **输入验证**: 前后端双重验证

### 内容安全
- **内容审核**: 关键词过滤和人工审核
- **举报机制**: 用户举报和管理员处理
- **黑名单系统**: 用户和内容黑名单管理
- **反垃圾**: 频率限制和行为分析

### 系统安全
- **API安全**: 请求频率限制和参数验证
- **数据备份**: 定期数据备份和恢复
- **监控告警**: 异常行为监控和告警
- **日志记录**: 完整的操作日志记录

---

## 🚀 部署架构

### 云开发环境配置
- **环境ID**: yichongyuzhou-3g9112qwf5f3487b
- **地域**: 上海 (ap-shanghai)
- **套餐**: 按量付费
- **域名**: yichongyuzhou-3g9112qwf5f3487b-1368816056.tcloudbaseapp.com

### 资源配置
- **云函数**: pet-api (Node.js 18.15)
- **云数据库**: MongoDB 4.4
- **云存储**: 对象存储 COS
- **静态托管**: CDN加速分发

### 访问地址
- **主站**: https://yichongyuzhou-3g9112qwf5f3487b-1368816056.tcloudbaseapp.com/pet-platform-final/
- **管理后台**: https://yichongyuzhou-3g9112qwf5f3487b-1368816056.tcloudbaseapp.com/pet-platform-final/admin/

---

## 📈 项目里程碑

### 已完成功能 ✅
1. **基础架构搭建** - Next.js + CloudBase
2. **用户系统** - 注册、登录、个人中心
3. **内容管理** - 发布、展示、搜索
4. **社区功能** - 点赞、收藏、通知
5. **活动系统** - 投票、评论、统计
6. **广告系统** - 展示、管理、降权
7. **权重系统** - 质量评分、智能排序
8. **管理后台** - 用户管理、内容审核
9. **安全机制** - 举报、黑名单、审核

### 技术特色 🌟
- **智能内容管理** - AI驱动的质量评分
- **非侵入式广告** - 用户体验优先
- **实时数据统计** - 完整的分析体系
- **响应式设计** - 全设备适配
- **模块化架构** - 易于维护扩展

---

## 🎯 未来规划

### 短期优化 (1-3个月)
- **性能优化**: 图片懒加载、CDN优化
- **用户体验**: 搜索历史、热门推荐
- **移动端**: PWA支持、原生应用感受
- **数据分析**: 用户行为分析、转化率统计

### 中期发展 (3-6个月)
- **AI增强**: 智能推荐、内容理解
- **社交功能**: 用户关注、私信系统
- **交易功能**: 在线支付、订单管理
- **地理服务**: LBS定位、附近推荐

### 长期愿景 (6-12个月)
- **生态建设**: 宠物服务商入驻
- **知识社区**: 养宠知识、专家问答
- **线下活动**: 宠物聚会、领养活动
- **数据开放**: API开放、第三方集成

---

## 📝 开发规范

### 代码规范
- **TypeScript**: 严格类型检查
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Git Flow**: 分支管理规范

### 文档规范
- **API文档**: 详细的接口说明
- **组件文档**: 组件使用说明
- **部署文档**: 环境配置说明
- **用户手册**: 功能使用指南

### 测试规范
- **单元测试**: 核心功能测试
- **集成测试**: API接口测试
- **E2E测试**: 用户流程测试
- **性能测试**: 负载和压力测试

---

## 🤝 团队协作

### 角色分工
- **产品经理**: 需求分析、功能规划
- **UI设计师**: 界面设计、用户体验
- **前端开发**: 页面实现、交互逻辑
- **后端开发**: API开发、数据库设计
- **测试工程师**: 质量保证、bug修复
- **运维工程师**: 部署维护、监控告警

### 协作流程
- **需求评审**: 产品需求讨论和确认
- **技术方案**: 架构设计和技术选型
- **开发实现**: 并行开发和代码审查
- **测试验收**: 功能测试和用户验收
- **上线部署**: 灰度发布和全量上线
- **运营维护**: 监控反馈和持续优化

---

---

## 🛠️ 技术实现详解

### 云函数架构设计

#### 统一API入口 (pet-api)
```javascript
// 云函数主入口
exports.main = async (event, context) => {
  const { action, data } = event;
  const openId = context.OPENID || data.openId;

  switch (action) {
    case 'getUserInfo': return await getUserInfo(data, openId);
    case 'createPost': return await createPost(data, openId);
    case 'getActivities': return await getActivities(data, openId);
    case 'voteInActivity': return await voteInActivity(data, openId);
    case 'toggleBookmark': return await toggleBookmark(data, openId);
    case 'getUserNotifications': return await getUserNotifications(data, openId);
    // ... 更多API
    default: throw new Error('Unknown action');
  }
};
```

#### 数据库操作封装
```javascript
// 标准化数据库操作
const db = cloud.database();

// 创建文档
async function createDocument(collection, data) {
  return await db.collection(collection).add({ data });
}

// 查询文档
async function queryDocuments(collection, query = {}, options = {}) {
  let dbQuery = db.collection(collection);

  if (query) dbQuery = dbQuery.where(query);
  if (options.orderBy) dbQuery = dbQuery.orderBy(options.orderBy.field, options.orderBy.direction);
  if (options.limit) dbQuery = dbQuery.limit(options.limit);
  if (options.skip) dbQuery = dbQuery.skip(options.skip);

  return await dbQuery.get();
}
```

### 前端状态管理

#### React Hooks使用模式
```typescript
// 自定义Hook示例
const useUserProfile = (userId: string) => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProfile = async () => {
      try {
        setLoading(true);
        const result = await petAPI.getUserInfo({ userId });
        if (result.success) {
          setProfile(result.data);
        } else {
          setError(result.message);
        }
      } catch (err) {
        setError('加载用户信息失败');
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      loadProfile();
    }
  }, [userId]);

  return { profile, loading, error, refetch: loadProfile };
};
```

#### API调用封装
```typescript
// CloudBase API封装
class PetAPI {
  private app: any;

  constructor() {
    this.app = cloudbase.init({
      env: process.env.NEXT_PUBLIC_CLOUDBASE_ENV_ID
    });
  }

  async callFunction(name: string, action: string, data: any = {}) {
    try {
      const result = await this.app.callFunction({
        name,
        data: { action, data }
      });
      return result.result;
    } catch (error) {
      console.error(`API调用失败 [${action}]:`, error);
      throw error;
    }
  }

  // 用户相关API
  async getUserInfo(params: { userId: string }) {
    return this.callFunction('pet-api', 'getUserInfo', params);
  }

  // 帖子相关API
  async createPost(postData: any) {
    return this.callFunction('pet-api', 'createPost', postData);
  }

  // 活动相关API
  async getActivities(params: any = {}) {
    return this.callFunction('pet-api', 'getActivities', params);
  }
}
```

### 数据库设计详解

#### 用户表 (users)
```json
{
  "_id": "user_001",
  "username": "petlover001",
  "nickname": "宠物爱好者",
  "avatar_url": "https://example.com/avatar.jpg",
  "phone": "13800138000",
  "location": "北京市朝阳区",
  "bio": "热爱宠物，专业繁殖者",
  "reputation_score": 85,
  "posts_count": 12,
  "successful_adoptions": 5,
  "created_at": "2024-01-01T00:00:00Z",
  "last_active": "2025-01-16T06:00:00Z",
  "status": "active"
}
```

#### 宠物帖子表 (pets)
```json
{
  "_id": "pet_001",
  "title": "可爱的金毛寻找新家",
  "description": "这是一只非常可爱的金毛犬...",
  "user_id": "user_001",
  "category": "狗狗",
  "breed": "金毛寻回犬",
  "age": "2岁",
  "gender": "公",
  "location": "北京市",
  "price": 2000,
  "images": ["image1.jpg", "image2.jpg"],
  "contact_info": {
    "phone": "13800138000",
    "wechat": "petlover001"
  },
  "status": "available",
  "priority_level": "normal",
  "quality_score": 75,
  "views": 120,
  "likes": 15,
  "bookmarks": 8,
  "created_at": "2025-01-15T10:00:00Z",
  "updated_at": "2025-01-16T06:00:00Z"
}
```

#### 活动表 (activities)
```json
{
  "_id": "activity_001",
  "title": "测试投票活动：猫咪 VS 狗狗",
  "description": "你更喜欢猫咪还是狗狗？",
  "type": "vote",
  "status": "active",
  "options": [
    {
      "id": "cats",
      "text": "猫咪",
      "votes": 2
    },
    {
      "id": "dogs",
      "text": "狗狗",
      "votes": 1
    }
  ],
  "settings": {
    "allow_comments": true,
    "require_vote_to_comment": true,
    "max_comment_length": 100,
    "comment_cooldown": 10
  },
  "stats": {
    "total_votes": 3,
    "total_comments": 1,
    "total_participants": 3
  },
  "start_time": "2025-01-16T00:00:00Z",
  "end_time": "2025-01-23T23:59:59Z",
  "created_by": "admin",
  "created_at": "2025-01-16T00:00:00Z"
}
```

#### 通知表 (notifications)
```json
{
  "_id": "notif_001",
  "type": "like",
  "recipient_id": "user_001",
  "sender_id": "user_002",
  "post_id": "pet_001",
  "message": "有人点赞了您的帖子",
  "data": {
    "post_title": "可爱的金毛寻找新家",
    "sender_name": "快速出售"
  },
  "is_read": false,
  "created_at": "2025-01-16T06:30:00Z"
}
```

### 权限控制系统

#### 管理员权限验证
```javascript
async function checkAdminPermission(openId) {
  try {
    const admin = await db.collection('admins').doc(openId).get();
    return admin.data && admin.data.status === 'active';
  } catch (error) {
    return false;
  }
}

// 使用示例
async function updatePostPriority(data, openId) {
  const isAdmin = await checkAdminPermission(openId);
  if (!isAdmin) {
    throw new Error('无权限执行此操作');
  }
  // 执行管理员操作...
}
```

#### 用户操作权限
```javascript
async function checkPostOwnership(postId, userId) {
  const post = await db.collection('pets').doc(postId).get();
  return post.data && post.data.user_id === userId;
}

// 权限中间件
function requireAuth(handler) {
  return async (data, openId) => {
    if (!openId) {
      throw new Error('用户未登录');
    }
    return await handler(data, openId);
  };
}
```

---

## 📊 详细流程图

### 用户注册登录流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant C as CloudBase
    participant D as 数据库

    U->>F: 访问注册页面
    F->>U: 显示注册表单
    U->>F: 填写用户信息
    F->>F: 前端验证
    F->>C: 调用注册API
    C->>D: 检查用户名是否存在
    D->>C: 返回检查结果
    alt 用户名已存在
        C->>F: 返回错误信息
        F->>U: 显示错误提示
    else 用户名可用
        C->>D: 创建用户记录
        D->>C: 返回创建结果
        C->>F: 返回成功信息
        F->>U: 自动登录并跳转
    end
```

### 帖子发布流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant C as CloudBase
    participant S as 云存储
    participant D as 数据库

    U->>F: 点击发布按钮
    F->>U: 显示发布表单
    U->>F: 填写宠物信息
    U->>F: 选择上传图片
    F->>S: 上传图片到云存储
    S->>F: 返回图片URL
    F->>C: 提交帖子数据
    C->>C: 计算质量评分
    C->>D: 保存帖子信息
    D->>C: 返回保存结果
    C->>F: 返回发布结果
    F->>U: 显示发布成功
    F->>F: 跳转到个人主页
```

### 活动投票流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant C as CloudBase
    participant D as 数据库

    U->>F: 浏览活动列表
    F->>C: 获取活动数据
    C->>D: 查询活动信息
    D->>C: 返回活动列表
    C->>F: 返回活动数据
    F->>U: 显示活动列表
    U->>F: 选择活动参与
    F->>U: 显示活动详情
    U->>F: 选择投票选项
    F->>C: 提交投票请求
    C->>D: 检查是否已投票
    D->>C: 返回检查结果
    alt 已经投票
        C->>F: 返回已投票提示
        F->>U: 显示提示信息
    else 未投票
        C->>D: 记录投票信息
        C->>D: 更新活动统计
        D->>C: 返回更新结果
        C->>F: 返回投票成功
        F->>U: 显示投票结果
    end
```

### 通知系统流程
```mermaid
sequenceDiagram
    participant U1 as 用户A
    participant U2 as 用户B
    participant F as 前端
    participant C as CloudBase
    participant D as 数据库

    U1->>F: 点赞用户B的帖子
    F->>C: 调用点赞API
    C->>D: 更新帖子点赞数
    C->>D: 创建通知记录
    D->>C: 返回操作结果
    C->>F: 返回点赞成功
    F->>U1: 显示点赞成功

    Note over U2,D: 用户B查看通知
    U2->>F: 访问通知页面
    F->>C: 获取通知列表
    C->>D: 查询用户通知
    D->>C: 返回通知数据
    C->>F: 返回通知列表
    F->>U2: 显示通知信息
    U2->>F: 点击标记已读
    F->>C: 调用标记已读API
    C->>D: 更新通知状态
    D->>C: 返回更新结果
    C->>F: 返回操作成功
    F->>U2: 更新显示状态
```

---

## 🎨 UI组件设计规范

### 设计系统

#### 色彩规范
```css
/* 主色调 */
--primary-color: #3B82F6;      /* 蓝色 - 主要按钮、链接 */
--primary-hover: #2563EB;      /* 蓝色悬停态 */
--primary-light: #DBEAFE;      /* 浅蓝色 - 背景 */

/* 辅助色 */
--secondary-color: #10B981;    /* 绿色 - 成功状态 */
--warning-color: #F59E0B;      /* 黄色 - 警告状态 */
--danger-color: #EF4444;       /* 红色 - 错误状态 */

/* 中性色 */
--gray-50: #F9FAFB;
--gray-100: #F3F4F6;
--gray-200: #E5E7EB;
--gray-300: #D1D5DB;
--gray-400: #9CA3AF;
--gray-500: #6B7280;
--gray-600: #4B5563;
--gray-700: #374151;
--gray-800: #1F2937;
--gray-900: #111827;
```

#### 字体规范
```css
/* 字体大小 */
--text-xs: 0.75rem;    /* 12px */
--text-sm: 0.875rem;   /* 14px */
--text-base: 1rem;     /* 16px */
--text-lg: 1.125rem;   /* 18px */
--text-xl: 1.25rem;    /* 20px */
--text-2xl: 1.5rem;    /* 24px */
--text-3xl: 1.875rem;  /* 30px */

/* 字体权重 */
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
```

#### 间距规范
```css
/* 间距系统 (基于4px) */
--space-1: 0.25rem;    /* 4px */
--space-2: 0.5rem;     /* 8px */
--space-3: 0.75rem;    /* 12px */
--space-4: 1rem;       /* 16px */
--space-5: 1.25rem;    /* 20px */
--space-6: 1.5rem;     /* 24px */
--space-8: 2rem;       /* 32px */
--space-10: 2.5rem;    /* 40px */
--space-12: 3rem;      /* 48px */
```

### 组件库设计

#### 按钮组件
```typescript
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'outline' | 'ghost';
  size: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  children: React.ReactNode;
  onClick?: () => void;
}

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  icon,
  children,
  onClick
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors';

  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700',
    outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50',
    ghost: 'text-gray-700 hover:bg-gray-100'
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      disabled={disabled || loading}
      onClick={onClick}
    >
      {loading && <LoadingSpinner className="mr-2" />}
      {icon && <span className="mr-2">{icon}</span>}
      {children}
    </button>
  );
};
```

#### 卡片组件
```typescript
interface CardProps {
  title?: string;
  subtitle?: string;
  image?: string;
  actions?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

const Card: React.FC<CardProps> = ({
  title,
  subtitle,
  image,
  actions,
  children,
  className = ''
}) => {
  return (
    <div className={`bg-white rounded-lg shadow-md overflow-hidden ${className}`}>
      {image && (
        <div className="aspect-w-16 aspect-h-9">
          <img src={image} alt={title} className="w-full h-full object-cover" />
        </div>
      )}

      <div className="p-6">
        {title && (
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            {subtitle && (
              <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
            )}
          </div>
        )}

        <div className="mb-4">
          {children}
        </div>

        {actions && (
          <div className="flex justify-end space-x-2">
            {actions}
          </div>
        )}
      </div>
    </div>
  );
};
```

---

## 📱 响应式设计

### 断点系统
```css
/* 移动设备优先的响应式断点 */
@media (min-width: 640px) { /* sm */ }
@media (min-width: 768px) { /* md */ }
@media (min-width: 1024px) { /* lg */ }
@media (min-width: 1280px) { /* xl */ }
@media (min-width: 1536px) { /* 2xl */ }
```

### 布局适配策略

#### 主页布局
```typescript
const HomePage = () => {
  return (
    <div className="container mx-auto px-4">
      {/* 头部导航 */}
      <header className="sticky top-0 z-50 bg-white shadow-sm">
        <nav className="flex items-center justify-between py-4">
          <Logo />
          <div className="hidden md:flex space-x-6">
            <NavLinks />
          </div>
          <div className="md:hidden">
            <MobileMenu />
          </div>
        </nav>
      </header>

      {/* 主要内容 */}
      <main className="py-8">
        {/* 筛选栏 - 桌面端横向，移动端纵向 */}
        <div className="mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <SearchBar />
            <FilterButtons />
          </div>
        </div>

        {/* 宠物卡片网格 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {pets.map(pet => (
            <PetCard key={pet._id} pet={pet} />
          ))}
        </div>
      </main>
    </div>
  );
};
```

#### 移动端优化
```typescript
const MobilePetCard = ({ pet }: { pet: Pet }) => {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* 图片区域 - 移动端更大的图片比例 */}
      <div className="aspect-w-4 aspect-h-3">
        <img
          src={pet.images[0]}
          alt={pet.title}
          className="w-full h-full object-cover"
        />
      </div>

      {/* 内容区域 - 移动端更紧凑的布局 */}
      <div className="p-4">
        <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
          {pet.title}
        </h3>

        <div className="flex items-center justify-between text-sm text-gray-600 mb-3">
          <span>{pet.location}</span>
          <span className="font-medium text-orange-600">¥{pet.price}</span>
        </div>

        {/* 移动端操作按钮 - 更大的触摸区域 */}
        <div className="flex space-x-2">
          <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md text-sm font-medium">
            联系
          </button>
          <button className="p-2 border border-gray-300 rounded-md">
            <HeartIcon className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
};
```

---

---

## 🧪 测试策略

### 测试金字塔

#### 单元测试 (70%)
```typescript
// 工具函数测试
describe('质量评分算法', () => {
  test('应该正确计算内容质量分数', () => {
    const post = {
      title: '可爱的金毛寻找新家',
      description: '这是一只非常可爱的金毛犬，性格温顺，适合家庭饲养。已经接种疫苗，身体健康。',
      images: ['image1.jpg', 'image2.jpg']
    };

    const score = calculateContentQuality(post);
    expect(score).toBeGreaterThan(20);
    expect(score).toBeLessThanOrEqual(30);
  });

  test('应该正确识别广告内容', () => {
    const adPost = {
      title: '美短猫咪找主人，价格优惠',
      description: '联系微信：cat123，私聊详谈，限时促销'
    };

    const penalty = calculateAdPenalty(adPost);
    expect(penalty).toBeLessThan(0);
  });
});

// React组件测试
describe('PetCard组件', () => {
  test('应该正确渲染宠物信息', () => {
    const mockPet = {
      _id: 'pet_001',
      title: '测试宠物',
      price: 1000,
      location: '北京市',
      images: ['test.jpg']
    };

    render(<PetCard pet={mockPet} />);

    expect(screen.getByText('测试宠物')).toBeInTheDocument();
    expect(screen.getByText('¥1000')).toBeInTheDocument();
    expect(screen.getByText('北京市')).toBeInTheDocument();
  });
});
```

#### 集成测试 (20%)
```typescript
// API集成测试
describe('宠物API集成测试', () => {
  beforeEach(async () => {
    // 清理测试数据
    await cleanupTestData();
  });

  test('应该能够创建和获取宠物帖子', async () => {
    // 创建测试用户
    const user = await createTestUser();

    // 创建宠物帖子
    const postData = {
      title: '测试宠物帖子',
      description: '这是一个测试帖子',
      category: '狗狗',
      price: 1000
    };

    const createResult = await petAPI.createPost(postData);
    expect(createResult.success).toBe(true);

    // 获取帖子列表
    const listResult = await petAPI.getPosts({ limit: 10 });
    expect(listResult.success).toBe(true);
    expect(listResult.data.length).toBeGreaterThan(0);
  });

  test('活动投票流程应该正常工作', async () => {
    // 创建测试活动
    const activity = await createTestActivity();

    // 用户投票
    const voteResult = await petAPI.voteInActivity({
      activity_id: activity._id,
      option_id: 'option1',
      openId: 'test_user'
    });

    expect(voteResult.success).toBe(true);

    // 检查投票统计
    const activityDetail = await petAPI.getActivityDetail({
      activity_id: activity._id
    });

    expect(activityDetail.data.stats.total_votes).toBe(1);
  });
});
```

#### E2E测试 (10%)
```typescript
// Playwright E2E测试
describe('用户完整流程测试', () => {
  test('用户注册到发布宠物的完整流程', async ({ page }) => {
    // 访问首页
    await page.goto('/');

    // 点击注册
    await page.click('[data-testid="register-button"]');

    // 填写注册信息
    await page.fill('[data-testid="username-input"]', 'testuser');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="submit-button"]');

    // 验证登录成功
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();

    // 发布宠物
    await page.click('[data-testid="publish-button"]');
    await page.fill('[data-testid="pet-title"]', '测试宠物');
    await page.fill('[data-testid="pet-description"]', '这是一个测试宠物');
    await page.selectOption('[data-testid="pet-category"]', '狗狗');
    await page.fill('[data-testid="pet-price"]', '1000');

    // 提交发布
    await page.click('[data-testid="publish-submit"]');

    // 验证发布成功
    await expect(page.locator('text=发布成功')).toBeVisible();
  });
});
```

### 性能测试

#### 负载测试
```javascript
// K6负载测试脚本
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // 2分钟内增加到100用户
    { duration: '5m', target: 100 }, // 保持100用户5分钟
    { duration: '2m', target: 200 }, // 2分钟内增加到200用户
    { duration: '5m', target: 200 }, // 保持200用户5分钟
    { duration: '2m', target: 0 },   // 2分钟内减少到0用户
  ],
};

export default function () {
  // 测试首页加载
  let response = http.get('https://yichongyuzhou-3g9112qwf5f3487b-1368816056.tcloudbaseapp.com/pet-platform-final/');
  check(response, {
    '首页状态码为200': (r) => r.status === 200,
    '首页响应时间小于2秒': (r) => r.timings.duration < 2000,
  });

  sleep(1);

  // 测试API接口
  let apiResponse = http.post('https://yichongyuzhou-3g9112qwf5f3487b-1368816056.tcloudbaseapp.com/pet-platform-final/api/pets', {
    action: 'getPosts',
    data: { limit: 20 }
  });

  check(apiResponse, {
    'API状态码为200': (r) => r.status === 200,
    'API响应时间小于1秒': (r) => r.timings.duration < 1000,
  });

  sleep(2);
}
```

---

## 🚀 部署与运维

### CI/CD流程

#### GitHub Actions配置
```yaml
name: Deploy to CloudBase

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run tests
      run: npm run test

    - name: Run linting
      run: npm run lint

    - name: Build project
      run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build project
      run: npm run build

    - name: Deploy to CloudBase
      env:
        CLOUDBASE_SECRET_ID: ${{ secrets.CLOUDBASE_SECRET_ID }}
        CLOUDBASE_SECRET_KEY: ${{ secrets.CLOUDBASE_SECRET_KEY }}
      run: |
        npm install -g @cloudbase/cli
        tcb login --apiKeyId $CLOUDBASE_SECRET_ID --apiKey $CLOUDBASE_SECRET_KEY
        tcb hosting deploy out -e yichongyuzhou-3g9112qwf5f3487b
        tcb functions deploy pet-api -e yichongyuzhou-3g9112qwf5f3487b
```

### 监控与告警

#### 性能监控
```javascript
// 前端性能监控
class PerformanceMonitor {
  constructor() {
    this.metrics = {};
    this.init();
  }

  init() {
    // 页面加载性能
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0];
      this.metrics.pageLoad = {
        dns: navigation.domainLookupEnd - navigation.domainLookupStart,
        tcp: navigation.connectEnd - navigation.connectStart,
        request: navigation.responseStart - navigation.requestStart,
        response: navigation.responseEnd - navigation.responseStart,
        dom: navigation.domContentLoadedEventEnd - navigation.responseEnd,
        total: navigation.loadEventEnd - navigation.navigationStart
      };

      this.sendMetrics();
    });

    // API请求性能
    this.monitorAPIRequests();
  }

  monitorAPIRequests() {
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const start = performance.now();
      try {
        const response = await originalFetch(...args);
        const duration = performance.now() - start;

        this.recordAPIMetric({
          url: args[0],
          method: args[1]?.method || 'GET',
          status: response.status,
          duration
        });

        return response;
      } catch (error) {
        const duration = performance.now() - start;
        this.recordAPIMetric({
          url: args[0],
          method: args[1]?.method || 'GET',
          status: 0,
          duration,
          error: error.message
        });
        throw error;
      }
    };
  }

  recordAPIMetric(metric) {
    if (!this.metrics.api) this.metrics.api = [];
    this.metrics.api.push({
      ...metric,
      timestamp: Date.now()
    });
  }

  sendMetrics() {
    // 发送性能数据到监控服务
    fetch('/api/metrics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(this.metrics)
    });
  }
}

// 初始化性能监控
new PerformanceMonitor();
```

#### 错误监控
```javascript
// 全局错误捕获
class ErrorMonitor {
  constructor() {
    this.init();
  }

  init() {
    // JavaScript错误
    window.addEventListener('error', (event) => {
      this.reportError({
        type: 'javascript',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        timestamp: Date.now()
      });
    });

    // Promise未捕获错误
    window.addEventListener('unhandledrejection', (event) => {
      this.reportError({
        type: 'promise',
        message: event.reason?.message || 'Unhandled Promise Rejection',
        stack: event.reason?.stack,
        timestamp: Date.now()
      });
    });

    // React错误边界
    this.setupReactErrorBoundary();
  }

  reportError(error) {
    // 发送错误信息到监控服务
    fetch('/api/errors', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...error,
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: this.getCurrentUserId()
      })
    }).catch(console.error);
  }

  getCurrentUserId() {
    // 获取当前用户ID
    return localStorage.getItem('userId') || 'anonymous';
  }
}

// 初始化错误监控
new ErrorMonitor();
```

### 数据备份策略

#### 自动备份脚本
```javascript
// 云函数：数据备份
exports.main = async (event, context) => {
  const db = cloud.database();
  const collections = ['users', 'pets', 'activities', 'notifications', 'bookmarks'];

  try {
    for (const collectionName of collections) {
      console.log(`开始备份集合: ${collectionName}`);

      // 获取集合数据
      const { data } = await db.collection(collectionName).get();

      // 生成备份文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `backup/${collectionName}_${timestamp}.json`;

      // 上传到云存储
      await cloud.uploadFile({
        cloudPath: fileName,
        fileContent: Buffer.from(JSON.stringify(data, null, 2))
      });

      console.log(`集合 ${collectionName} 备份完成: ${fileName}`);
    }

    return {
      success: true,
      message: '数据备份完成',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('数据备份失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
```

#### 备份恢复流程
```javascript
// 数据恢复云函数
exports.main = async (event, context) => {
  const { backupFile, collectionName } = event;
  const db = cloud.database();

  try {
    // 下载备份文件
    const { fileContent } = await cloud.downloadFile({
      fileID: backupFile
    });

    // 解析备份数据
    const backupData = JSON.parse(fileContent.toString());

    // 清空现有数据（谨慎操作）
    if (event.clearExisting) {
      await db.collection(collectionName).where({}).remove();
    }

    // 批量插入备份数据
    const batchSize = 100;
    for (let i = 0; i < backupData.length; i += batchSize) {
      const batch = backupData.slice(i, i + batchSize);
      await db.collection(collectionName).add(batch);
    }

    return {
      success: true,
      message: `集合 ${collectionName} 恢复完成`,
      restoredCount: backupData.length
    };
  } catch (error) {
    console.error('数据恢复失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
```

---

## 📊 数据分析与统计

### 用户行为分析

#### 埋点设计
```typescript
// 埋点事件定义
interface TrackingEvent {
  event: string;
  properties: Record<string, any>;
  userId?: string;
  timestamp: number;
}

class Analytics {
  private events: TrackingEvent[] = [];

  track(event: string, properties: Record<string, any> = {}) {
    const trackingEvent: TrackingEvent = {
      event,
      properties: {
        ...properties,
        page: window.location.pathname,
        referrer: document.referrer,
        userAgent: navigator.userAgent
      },
      userId: this.getCurrentUserId(),
      timestamp: Date.now()
    };

    this.events.push(trackingEvent);
    this.sendEvent(trackingEvent);
  }

  private sendEvent(event: TrackingEvent) {
    // 发送到分析服务
    fetch('/api/analytics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(event)
    });
  }

  private getCurrentUserId(): string | undefined {
    return localStorage.getItem('userId') || undefined;
  }
}

// 全局分析实例
const analytics = new Analytics();

// 页面浏览埋点
analytics.track('page_view', {
  page_title: document.title
});

// 用户交互埋点
document.addEventListener('click', (event) => {
  const target = event.target as HTMLElement;
  if (target.dataset.track) {
    analytics.track('click', {
      element: target.dataset.track,
      text: target.textContent,
      position: { x: event.clientX, y: event.clientY }
    });
  }
});
```

#### 数据统计报表
```javascript
// 云函数：生成统计报表
exports.main = async (event, context) => {
  const db = cloud.database();
  const { startDate, endDate, type } = event;

  try {
    let report = {};

    switch (type) {
      case 'user_activity':
        report = await generateUserActivityReport(db, startDate, endDate);
        break;
      case 'content_quality':
        report = await generateContentQualityReport(db, startDate, endDate);
        break;
      case 'activity_engagement':
        report = await generateActivityEngagementReport(db, startDate, endDate);
        break;
      default:
        throw new Error('未知的报表类型');
    }

    return {
      success: true,
      data: report,
      generatedAt: new Date().toISOString()
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
};

async function generateUserActivityReport(db, startDate, endDate) {
  // 用户活跃度统计
  const activeUsers = await db.collection('users')
    .where({
      last_active: db.command.gte(startDate).and(db.command.lte(endDate))
    })
    .count();

  // 新用户注册统计
  const newUsers = await db.collection('users')
    .where({
      created_at: db.command.gte(startDate).and(db.command.lte(endDate))
    })
    .count();

  // 用户发布统计
  const userPosts = await db.collection('pets')
    .where({
      created_at: db.command.gte(startDate).and(db.command.lte(endDate))
    })
    .count();

  return {
    activeUsers: activeUsers.total,
    newUsers: newUsers.total,
    userPosts: userPosts.total,
    avgPostsPerUser: userPosts.total / Math.max(activeUsers.total, 1)
  };
}
```

---

## 🔧 运维工具

### 系统健康检查
```javascript
// 健康检查云函数
exports.main = async (event, context) => {
  const checks = [];

  try {
    // 数据库连接检查
    const dbCheck = await checkDatabase();
    checks.push({ name: 'database', status: dbCheck.status, message: dbCheck.message });

    // 云存储检查
    const storageCheck = await checkStorage();
    checks.push({ name: 'storage', status: storageCheck.status, message: storageCheck.message });

    // 外部API检查
    const apiCheck = await checkExternalAPIs();
    checks.push({ name: 'external_apis', status: apiCheck.status, message: apiCheck.message });

    // 系统资源检查
    const resourceCheck = await checkSystemResources();
    checks.push({ name: 'resources', status: resourceCheck.status, message: resourceCheck.message });

    const overallStatus = checks.every(check => check.status === 'healthy') ? 'healthy' : 'unhealthy';

    return {
      status: overallStatus,
      checks,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

async function checkDatabase() {
  try {
    const db = cloud.database();
    await db.collection('users').limit(1).get();
    return { status: 'healthy', message: '数据库连接正常' };
  } catch (error) {
    return { status: 'unhealthy', message: `数据库连接失败: ${error.message}` };
  }
}

async function checkStorage() {
  try {
    const result = await cloud.getTempFileURL({
      fileList: ['test-file.txt']
    });
    return { status: 'healthy', message: '云存储服务正常' };
  } catch (error) {
    return { status: 'unhealthy', message: `云存储服务异常: ${error.message}` };
  }
}
```

### 日志管理
```javascript
// 日志收集和分析
class Logger {
  constructor(level = 'info') {
    this.level = level;
    this.levels = { error: 0, warn: 1, info: 2, debug: 3 };
  }

  log(level, message, meta = {}) {
    if (this.levels[level] <= this.levels[this.level]) {
      const logEntry = {
        timestamp: new Date().toISOString(),
        level,
        message,
        meta: {
          ...meta,
          function: context.FUNCTION_NAME,
          requestId: context.REQUEST_ID,
          memory: process.memoryUsage()
        }
      };

      console.log(JSON.stringify(logEntry));

      // 发送到日志服务
      this.sendToLogService(logEntry);
    }
  }

  error(message, meta) { this.log('error', message, meta); }
  warn(message, meta) { this.log('warn', message, meta); }
  info(message, meta) { this.log('info', message, meta); }
  debug(message, meta) { this.log('debug', message, meta); }

  async sendToLogService(logEntry) {
    try {
      // 这里可以集成第三方日志服务
      // 如阿里云SLS、腾讯云CLS等
    } catch (error) {
      console.error('发送日志失败:', error);
    }
  }
}

// 全局日志实例
const logger = new Logger(process.env.LOG_LEVEL || 'info');
```

---

*本文档最后更新时间: 2025年1月16日*
*项目版本: v1.0.0*
*维护者: AI开发团队*

---

## 📚 附录

### 常用命令
```bash
# 开发环境启动
npm run dev

# 构建项目
npm run build

# 部署到云开发
npm run deploy

# 运行测试
npm run test

# 代码检查
npm run lint

# 云函数部署
tcb functions deploy pet-api

# 静态资源部署
tcb hosting deploy out
```

### 环境变量配置
```env
# CloudBase配置
NEXT_PUBLIC_CLOUDBASE_ENV_ID=yichongyuzhou-3g9112qwf5f3487b
CLOUDBASE_SECRET_ID=your_secret_id
CLOUDBASE_SECRET_KEY=your_secret_key

# 应用配置
NEXT_PUBLIC_APP_NAME=宠物交易平台
NEXT_PUBLIC_APP_VERSION=1.0.0

# 功能开关
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=true
```

### 故障排查指南
1. **页面无法加载** - 检查静态托管配置和CDN缓存
2. **API调用失败** - 检查云函数状态和权限配置
3. **数据库连接异常** - 检查网络配置和数据库权限
4. **图片上传失败** - 检查云存储配置和文件大小限制
5. **用户登录问题** - 检查认证配置和会话管理

### 联系方式
- **技术支持**: <EMAIL>
- **产品反馈**: <EMAIL>
- **商务合作**: <EMAIL>
