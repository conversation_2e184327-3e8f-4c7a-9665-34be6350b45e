// 图片优化工具集
export interface ImageOptimizeOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  outputFormat?: 'webp' | 'jpeg' | 'png' | 'auto';
  generateThumbnails?: boolean;
  thumbnailSizes?: number[];
}

export interface OptimizedImageResult {
  original: File;
  compressed: File;
  thumbnails?: File[];
  compressionRatio: number;
  outputFormat: string;
}

// 检测浏览器支持的图片格式（增强版）
export const getSupportedImageFormat = (): string => {
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;

  // 检测AVIF支持（优先级最高）
  if (canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0) {
    return 'avif';
  }

  // 检测WebP支持
  if (canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0) {
    return 'webp';
  }

  // 降级到JPEG
  return 'jpeg';
};

// 异步检测AVIF支持（更准确的方法）
export const checkAVIFSupport = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const avif = new Image();
    avif.onload = () => resolve(true);
    avif.onerror = () => resolve(false);
    avif.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
  });
};

// 获取最佳图片格式（异步版本）
export const getBestImageFormat = async (): Promise<string> => {
  // 检测AVIF支持
  const supportsAVIF = await checkAVIFSupport();
  if (supportsAVIF) {
    return 'avif';
  }

  // 检测WebP支持
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;
  if (canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0) {
    return 'webp';
  }

  // 降级到JPEG
  return 'jpeg';
};

// 智能图片压缩（增强版）
export const optimizeImage = async (
  file: File,
  options: ImageOptimizeOptions = {}
): Promise<OptimizedImageResult> => {
  const {
    maxWidth = 1000,        // 降低最大宽度
    maxHeight = 1000,       // 降低最大高度
    quality = 0.75,         // 降低质量到75%
    outputFormat = 'auto',
    generateThumbnails = false,
    thumbnailSizes = [150, 300, 600]
  } = options;

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = async () => {
      try {
        // 计算最优尺寸
        const { width, height } = img;
        const { targetWidth, targetHeight, targetQuality } = calculateOptimalSize(
          width, height, maxWidth, maxHeight, quality
        );

        // 确定输出格式（异步检测）
        const finalFormat = outputFormat === 'auto' ? await getBestImageFormat() : outputFormat;
        const mimeType = `image/${finalFormat}`;

        console.log(`选择的图片格式: ${finalFormat}`);

        // 压缩主图
        canvas.width = targetWidth;
        canvas.height = targetHeight;
        
        if (ctx) {
          // 高质量绘制设置
          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';
          ctx.drawImage(img, 0, 0, targetWidth, targetHeight);
        }

        const compressedFile = await canvasToFile(
          canvas, 
          file.name, 
          mimeType, 
          targetQuality,
          finalFormat
        );

        // 生成缩略图（如果需要）
        let thumbnails: File[] = [];
        if (generateThumbnails) {
          thumbnails = await generateThumbnailsInternal(img, file.name, thumbnailSizes, finalFormat);
        }

        const compressionRatio = Math.round((1 - compressedFile.size / file.size) * 100);

        resolve({
          original: file,
          compressed: compressedFile,
          thumbnails,
          compressionRatio,
          outputFormat: finalFormat
        });

      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => reject(new Error('图片加载失败'));
    img.src = URL.createObjectURL(file);
  });
};

// 计算最优尺寸（激进压缩版）
const calculateOptimalSize = (
  width: number,
  height: number,
  maxWidth: number,
  maxHeight: number,
  quality: number
) => {
  let targetWidth = width;
  let targetHeight = height;
  let targetQuality = quality;

  // 计算宽高比
  const aspectRatio = width / height;

  // 文件大小估算（粗略）
  const estimatedSize = width * height * 3; // RGB估算

  // 超大图片处理（激进压缩）
  if (width > 2000 || height > 2000 || estimatedSize > 5000000) {
    // 大幅缩小尺寸
    const scaleFactor = Math.min(maxWidth / width, maxHeight / height, 0.6);
    targetWidth = Math.floor(width * scaleFactor);
    targetHeight = Math.floor(height * scaleFactor);
    targetQuality = Math.max(0.6, quality - 0.15); // 更激进的质量压缩
  }
  // 大图片处理
  else if (width > maxWidth || height > maxHeight) {
    if (aspectRatio > 1) {
      targetWidth = maxWidth;
      targetHeight = targetWidth / aspectRatio;
    } else {
      targetHeight = maxHeight;
      targetWidth = targetHeight * aspectRatio;
    }
    targetQuality = Math.max(0.65, quality - 0.1); // 降低质量
  }
  // 中等图片也进行适度压缩
  else if (width > 800 || height > 800) {
    const scaleFactor = 0.8; // 缩小到80%
    targetWidth = Math.floor(width * scaleFactor);
    targetHeight = Math.floor(height * scaleFactor);
    targetQuality = Math.max(0.7, quality - 0.05);
  }
  // 小图片保持原尺寸但降低质量
  else {
    targetQuality = Math.max(0.8, quality);
  }

  return { targetWidth, targetHeight, targetQuality };
};

// Canvas转File
const canvasToFile = (
  canvas: HTMLCanvasElement, 
  originalName: string, 
  mimeType: string, 
  quality: number,
  format: string
): Promise<File> => {
  return new Promise((resolve, reject) => {
    canvas.toBlob((blob) => {
      if (blob) {
        const fileName = originalName.replace(/\.[^/.]+$/, `.${format}`);
        const file = new File([blob], fileName, {
          type: mimeType,
          lastModified: Date.now()
        });
        resolve(file);
      } else {
        reject(new Error('Canvas转换失败'));
      }
    }, mimeType, quality);
  });
};

// 生成缩略图
const generateThumbnailsInternal = async (
  img: HTMLImageElement, 
  originalName: string, 
  sizes: number[],
  format: string
): Promise<File[]> => {
  const thumbnails: File[] = [];
  const mimeType = `image/${format}`;

  for (const size of sizes) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // 计算缩略图尺寸（保持宽高比）
    const { width, height } = img;
    const aspectRatio = width / height;
    
    let thumbWidth = size;
    let thumbHeight = size;
    
    if (aspectRatio > 1) {
      thumbHeight = size / aspectRatio;
    } else {
      thumbWidth = size * aspectRatio;
    }

    canvas.width = thumbWidth;
    canvas.height = thumbHeight;

    if (ctx) {
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';
      ctx.drawImage(img, 0, 0, thumbWidth, thumbHeight);
    }

    const thumbnailFile = await canvasToFile(
      canvas, 
      originalName.replace(/\.[^/.]+$/, `_thumb_${size}.${format}`), 
      mimeType, 
      0.8,
      format
    );

    thumbnails.push(thumbnailFile);
  }

  return thumbnails;
};

// 图片预加载
export const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = () => reject(new Error(`图片预加载失败: ${src}`));
    img.src = src;
  });
};

// 批量预加载图片
export const preloadImages = async (urls: string[]): Promise<void> => {
  const promises = urls.map(url => preloadImage(url));
  await Promise.allSettled(promises);
};

// 图片尺寸检测
export const getImageDimensions = (file: File): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.width, height: img.height });
    };
    img.onerror = () => reject(new Error('无法获取图片尺寸'));
    img.src = URL.createObjectURL(file);
  });
};
