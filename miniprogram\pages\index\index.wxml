<!--首页模板-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="userinfo">
    <block wx:if="{{!hasUserInfo && canIUse}}">
      <button open-type="getUserInfo" bindgetuserinfo="getUserInfo" class="userinfo-btn">
        获取头像昵称
      </button>
    </block>
    <block wx:else>
      <image bindtap="bindViewTap" class="userinfo-avatar" src="{{userInfo.avatarUrl}}" mode="cover"></image>
      <text class="userinfo-nickname">{{userInfo.nickName}}</text>
    </block>
  </view>

  <!-- 搜索栏 -->
  <view class="search-bar">
    <input placeholder="搜索宠物..." class="search-input" />
    <button class="search-btn">搜索</button>
  </view>

  <!-- 宠物帖子列表 -->
  <view class="posts-container">
    <view class="section-title">最新宠物</view>
    <scroll-view scroll-y="true" class="posts-scroll">
      <view wx:for="{{posts}}" wx:key="id" class="post-item" bindtap="viewPost" data-id="{{item._id}}">
        <image src="{{item.images[0]}}" class="post-image" mode="aspectFill"></image>
        <view class="post-info">
          <view class="post-title">{{item.title}}</view>
          <view class="post-price">¥{{item.price}}</view>
          <view class="post-location">{{item.location}}</view>
          <view class="post-time">{{item.createTime}}</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 快速操作按钮 -->
  <view class="quick-actions">
    <button class="action-btn" bindtap="goToPublish">发布宠物</button>
    <button class="action-btn" bindtap="goToFavorites">我的收藏</button>
  </view>
</view>
