@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
  
  * {
    @apply border-gray-200;
  }
}

@layer components {
  /* 文本截断样式 */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
  
  /* 滚动条样式 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* 自定义滚动条 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
  
  /* 毛玻璃效果 */
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
  
  /* 渐变文字 */
  .gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* 动画效果 */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }
  
  .animate-heart-beat {
    animation: heartBeat 1s ease-in-out infinite;
  }
  
  /* 响应式网格 */
  .grid-responsive {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
  }
  
  @media (max-width: 640px) {
    .grid-responsive {
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
  }
  
  /* 卡片悬停效果 */
  .card-hover {
    transition: all 0.2s ease-in-out;
  }
  
  .card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  /* 按钮点击效果 */
  .btn-press {
    transition: transform 0.1s ease-in-out;
  }
  
  .btn-press:active {
    transform: scale(0.95);
  }
  
  /* 图片加载占位符 */
  .image-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }
  
  @keyframes loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
  
  /* 表单聚焦效果 */
  .form-focus {
    transition: all 0.2s ease-in-out;
  }
  
  .form-focus:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  }
  
  /* 标签样式 */
  .tag {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .tag-primary {
    @apply bg-primary-100 text-primary-800;
  }
  
  .tag-secondary {
    @apply bg-gray-100 text-gray-800;
  }
  
  .tag-success {
    @apply bg-green-100 text-green-800;
  }
  
  .tag-warning {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .tag-danger {
    @apply bg-red-100 text-red-800;
  }
  
  /* 徽章样式 */
  .badge {
    @apply inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 rounded-full;
  }
  
  .badge-primary {
    @apply bg-primary-600;
  }
  
  .badge-danger {
    @apply bg-red-600;
  }
  
  .badge-warning {
    @apply bg-yellow-600;
  }
  
  .badge-success {
    @apply bg-green-600;
  }
}

@layer utilities {
  /* 安全区域适配 */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
  
  /* 触摸优化 */
  .touch-manipulation {
    touch-action: manipulation;
  }
  
  /* 选择禁用 */
  .select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  /* 硬件加速 */
  .gpu {
    transform: translateZ(0);
  }
  
  /* 平滑滚动 */
  .scroll-smooth {
    scroll-behavior: smooth;
  }
}

/* Swiper 样式覆盖 */
.swiper-pagination-bullet {
  @apply bg-white opacity-60;
}

.swiper-pagination-bullet-active {
  @apply opacity-100;
}

.swiper-button-next,
.swiper-button-prev {
  @apply text-white;
}

.swiper-button-next::after,
.swiper-button-prev::after {
  @apply text-sm;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .swiper-button-next,
  .swiper-button-prev {
    display: none;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    @apply text-black bg-white;
  }
}
