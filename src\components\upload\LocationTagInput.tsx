'use client';

import React, { useState, useEffect } from 'react';
import { MapPin, Search } from 'lucide-react';

interface LocationTagInputProps {
  value: string;
  onChange: (location: string) => void;
  error?: string;
  userId?: string; // 用户ID，用于获取保存的地址
}

const LocationTagInput: React.FC<LocationTagInputProps> = ({
  value,
  onChange,
  error,
  userId
}) => {
  const [inputValue, setInputValue] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [savedAddress, setSavedAddress] = useState('');

  // 全国主要城市列表
  const allCities = [
    // 直辖市
    '北京', '上海', '天津', '重庆',
    // 省会城市
    '广州', '深圳', '杭州', '成都', '武汉', '西安', '南京', '苏州',
    '长沙', '郑州', '济南', '青岛', '大连', '沈阳', '哈尔滨', '长春',
    '石家庄', '太原', '呼和浩特', '兰州', '西宁', '银川', '乌鲁木齐',
    '合肥', '福州', '厦门', '南昌', '昆明', '贵阳', '拉萨', '海口',
    '南宁', '宁波', '温州', '嘉兴', '绍兴', '金华', '台州', '丽水',
    '湖州', '衢州', '舟山', '无锡', '常州', '徐州', '南通', '连云港',
    '淮安', '盐城', '扬州', '镇江', '泰州', '宿迁'
  ];

  // 加载用户保存的地址
  useEffect(() => {
    if (userId) {
      const userAddress = localStorage.getItem(`address_${userId}`);
      if (userAddress) {
        setSavedAddress(userAddress);
        // 如果当前没有值，使用保存的地址作为默认值
        if (!value && userAddress) {
          const locationWithTag = `#${userAddress}`;
          onChange(locationWithTag);
          setInputValue(userAddress);
        }
      }
    }
  }, [userId, onChange]);

  // 初始化输入值
  useEffect(() => {
    if (value) {
      // 移除#前缀显示
      setInputValue(value.replace(/^#/, ''));
    }
  }, [value]);

  // 处理输入变化
  const handleInputChange = (inputValue: string) => {
    setInputValue(inputValue);
    
    if (inputValue.length > 0) {
      // 智能搜索建议
      const filtered = allCities.filter(city => 
        city.includes(inputValue) || 
        city.toLowerCase().includes(inputValue.toLowerCase())
      ).slice(0, 8);
      setSuggestions(filtered);
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }

    // 自动添加#前缀并更新
    const locationWithTag = inputValue ? `#${inputValue}` : '';
    onChange(locationWithTag);
  };

  // 选择城市
  const handleCitySelect = (city: string) => {
    setInputValue(city);
    setShowSuggestions(false);
    onChange(`#${city}`);
  };

  // 处理输入框失焦
  const handleBlur = () => {
    // 延迟隐藏建议，让点击建议有时间执行
    setTimeout(() => {
      setShowSuggestions(false);
    }, 200);
  };

  return (
    <div className="space-y-3">
      <label className="block text-sm font-medium text-gray-700">
        发布地区 <span className="text-red-500">*</span>
      </label>

      {/* 用户保存的地址快捷选择 */}
      {savedAddress && (
        <div className="space-y-2">
          <div className="text-xs text-gray-500">您的常用地址：</div>
          <button
            type="button"
            onClick={() => handleCitySelect(savedAddress)}
            className={`px-3 py-1 text-sm rounded-full border transition-colors ${
              inputValue === savedAddress
                ? 'bg-blue-500 text-white border-blue-500'
                : 'bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100'
            }`}
          >
            #{savedAddress}
          </button>
        </div>
      )}

      {/* 自定义输入 */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MapPin className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          value={inputValue}
          onChange={(e) => handleInputChange(e.target.value)}
          onFocus={() => inputValue && setShowSuggestions(suggestions.length > 0)}
          onBlur={handleBlur}
          className={`block w-full pl-10 pr-3 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            error ? 'border-red-300' : 'border-gray-300'
          }`}
          placeholder="输入发布地区，建议精确到县/区一级，如：北京市朝阳区"
        />
        
        {/* 搜索建议 */}
        {showSuggestions && suggestions.length > 0 && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto">
            {suggestions.map(city => (
              <button
                key={city}
                type="button"
                onClick={() => handleCitySelect(city)}
                className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center space-x-2"
              >
                <MapPin className="h-4 w-4 text-gray-400" />
                <span>#{city}</span>
              </button>
            ))}
          </div>
        )}
      </div>

      {/* 预览效果 */}
      {value && (
        <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded-lg">
          <MapPin className="h-4 w-4 text-blue-600" />
          <span className="text-sm text-blue-700">
            位置标签: <span className="font-medium">{value}</span>
          </span>
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}

      {/* 使用说明 */}
      <div className="text-xs text-gray-500">
        <p>• 系统会自动添加#标签，便于搜索和筛选</p>
      </div>
    </div>
  );
};

export default LocationTagInput;
