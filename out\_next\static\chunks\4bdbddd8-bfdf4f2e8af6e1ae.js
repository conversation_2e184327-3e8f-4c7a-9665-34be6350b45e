"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[19],{86847:function(t,e,n){n.d(e,{Wr:function(){return y}});var i=n(6299),r=n(9208),o=n(43957),s=function(){return(s=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var r in e=arguments[n])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},u=function(t,e,n,i){var r,o=arguments.length,s=o<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,n,i);else for(var u=t.length-1;u>=0;u--)(r=t[u])&&(s=(o<3?r(s):o>3?r(e,n,s):r(e,n))||s);return o>3&&s&&Object.defineProperty(e,n,s),s},a=function(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)},c=function(t,e,n,i){return new(n||(n=Promise))(function(r,o){function s(t){try{a(i.next(t))}catch(t){o(t)}}function u(t){try{a(i.throw(t))}catch(t){o(t)}}function a(t){var e;t.done?r(t.value):((e=t.value)instanceof n?e:new n(function(t){t(e)})).then(s,u)}a((i=i.apply(t,e||[])).next())})},h=function(t,e){var n,i,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function u(u){return function(a){return function(u){if(n)throw TypeError("Generator is already executing.");for(;o&&(o=0,u[0]&&(s=0)),s;)try{if(n=1,i&&(r=2&u[0]?i.return:u[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,u[1])).done)return r;switch(i=0,r&&(u=[2&u[0],r.value]),u[0]){case 0:case 1:r=u;break;case 4:return s.label++,{value:u[1],done:!1};case 5:s.label++,i=u[1],u=[0];continue;case 7:u=s.ops.pop(),s.trys.pop();continue;default:if(!(r=(r=s.trys).length>0&&r[r.length-1])&&(6===u[0]||2===u[0])){s=0;continue}if(3===u[0]&&(!r||u[1]>r[0]&&u[1]<r[3])){s.label=u[1];break}if(6===u[0]&&s.label<r[1]){s.label=r[1],r=u;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(u);break}r[2]&&s.ops.pop(),s.trys.pop();continue}u=e.call(t,s)}catch(t){u=[6,t],i=0}finally{n=r=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,a])}}},d={LOGIN_STATE_CHANGED:"loginStateChanged"},p=new o.yE,f=function(){function t(t){var e=t.cache,n=t.oauthInstance;this.cache=e,this.oauthInstance=n,this.setUserInfo()}return t.prototype.checkLocalInfo=function(){return c(this,void 0,void 0,function(){return h(this,function(t){return this.uid=this.getLocalUserInfo("uid"),this.gender=this.getLocalUserInfo("gender"),this.picture=this.getLocalUserInfo("picture"),this.email=this.getLocalUserInfo("email"),this.emailVerified=this.getLocalUserInfo("email_verified"),this.phoneNumber=this.getLocalUserInfo("phone_number"),this.username=this.getLocalUserInfo("username"),this.name=this.getLocalUserInfo("name"),this.birthdate=this.getLocalUserInfo("birthdate"),this.zoneinfo=this.getLocalUserInfo("zoneinfo"),this.locale=this.getLocalUserInfo("locale"),this.sub=this.getLocalUserInfo("sub"),this.createdFrom=this.getLocalUserInfo("created_from"),this.providers=this.getLocalUserInfo("providers"),[2]})})},t.prototype.checkLocalInfoAsync=function(){return c(this,void 0,void 0,function(){var t,e,n,i;return h(this,function(r){switch(r.label){case 0:return t=this,[4,this.getLocalUserInfoAsync("uid")];case 1:return t.uid=r.sent(),e=this,[4,this.getLocalUserInfoAsync("gender")];case 2:return e.gender=r.sent(),this.picture=this.getLocalUserInfo("picture"),n=this,[4,this.getLocalUserInfoAsync("email")];case 3:return n.email=r.sent(),this.emailVerified=this.getLocalUserInfo("email_verified"),this.phoneNumber=this.getLocalUserInfo("phone_number"),i=this,[4,this.getLocalUserInfoAsync("username")];case 4:return i.username=r.sent(),this.name=this.getLocalUserInfo("name"),this.birthdate=this.getLocalUserInfo("birthdate"),this.zoneinfo=this.getLocalUserInfo("zoneinfo"),this.locale=this.getLocalUserInfo("locale"),this.sub=this.getLocalUserInfo("sub"),this.createdFrom=this.getLocalUserInfo("created_from"),this.providers=this.getLocalUserInfo("providers"),[2]}})})},t.prototype.update=function(t){return c(this,void 0,void 0,function(){var e;return h(this,function(n){switch(n.label){case 0:return[4,this.oauthInstance.authApi.setUserProfile(s({},t))];case 1:return e=n.sent(),this.setLocalUserInfo(e),[2]}})})},t.prototype.updateUserBasicInfo=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){switch(e.label){case 0:return[4,this.oauthInstance.authApi.updateUserBasicInfo(s({},t))];case 1:return e.sent(),this.setLocalUserInfo({username:t.username}),[2]}})})},t.prototype.updatePassword=function(t,e){return this.oauthInstance.authApi.updatePasswordByOld({old_password:e,new_password:t})},t.prototype.updateUsername=function(t){return"string"!=typeof t&&(0,o._y)(o.Sg.INVALID_PARAMS,"username must be a string"),this.update({username:t})},t.prototype.refresh=function(t){return c(this,void 0,void 0,function(){var e;return h(this,function(n){switch(n.label){case 0:return[4,this.oauthInstance.authApi.getUserInfo(t)];case 1:return e=n.sent(),this.setLocalUserInfo(e),[2,e]}})})},t.prototype.getLocalUserInfo=function(t){var e=this.cache.keys.userInfoKey;return this.cache.getStore(e)[t]},t.prototype.getLocalUserInfoAsync=function(t){return c(this,void 0,void 0,function(){var e;return h(this,function(n){switch(n.label){case 0:return e=this.cache.keys.userInfoKey,[4,this.cache.getStoreAsync(e)];case 1:return[2,n.sent()[t]]}})})},t.prototype.setUserInfo=function(){var t=this,e=this.cache.keys.userInfoKey,n=this.cache.getStore(e);["uid","email","name","gender","picture","email_verified","phone_number","birthdate","zoneinfo","locale","sub","created_from","providers","username"].forEach(function(e){t[e]=n[e]})},t.prototype.setLocalUserInfo=function(t){var e=this.cache.keys.userInfoKey;this.cache.setStore(e,t),this.setUserInfo()},u([(0,o.Dz)({title:"更新用户信息失败",messages:["请确认以下各项：","  1 - 调用 User.update() 的语法或参数是否正确","  2 - 用户信息中是否包含非法值","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[Object]),a("design:returntype",Promise)],t.prototype,"update",null),u([(0,o.Dz)({title:"更新密码失败",messages:["请确认以下各项：","  1 - 调用 User.updatePassword() 的语法或参数是否正确","  3 - 新密码中是否包含非法字符","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[String,String]),a("design:returntype",void 0)],t.prototype,"updatePassword",null),u([(0,o.Dz)({title:"更新用户名失败",messages:["请确认以下各项：","  1 - 调用 User.updateUsername() 的语法或参数是否正确","  2 - 当前环境是否开通了用户名密码登录","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[String]),a("design:returntype",void 0)],t.prototype,"updateUsername",null),u([(0,o.Dz)({title:"刷新本地用户信息失败",messages:["请确认以下各项：","  1 - 调用 User.refresh() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[Object]),a("design:returntype",Promise)],t.prototype,"refresh",null),t}(),l=function(){function t(t){var e=t.envId,n=t.cache,i=t.oauthInstance;e||(0,o._y)(o.Sg.INVALID_PARAMS,"envId is not defined"),this.cache=n,this.oauthInstance=i,this.user=new f({cache:this.cache,oauthInstance:i})}return t.prototype.checkLocalState=function(){var t;this.oauthLoginState=null===(t=this.oauthInstance)||void 0===t?void 0:t.authApi.hasLoginStateSync(),this.user.checkLocalInfo()},t.prototype.checkLocalStateAsync=function(){var t;return c(this,void 0,void 0,function(){return h(this,function(e){switch(e.label){case 0:return[4,null===(t=this.oauthInstance)||void 0===t?void 0:t.authApi.getLoginState()];case 1:return e.sent(),[4,this.user.checkLocalInfoAsync()];case 2:return e.sent(),[2]}})})},t}(),v=function(){function t(t){this.config=t,this.cache=t.cache,this.oauthInstance=t.oauthInstance}return t.prototype.bindPhoneNumber=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.editContact(t)]})})},t.prototype.unbindProvider=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.unbindProvider(t)]})})},t.prototype.bindEmail=function(t){return this.oauthInstance.authApi.editContact(t)},t.prototype.verify=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.verify(t)]})})},t.prototype.getVerification=function(t,e){return c(this,void 0,void 0,function(){return h(this,function(n){return[2,this.oauthInstance.authApi.getVerification(t,e)]})})},Object.defineProperty(t.prototype,"currentUser",{get:function(){if("async"===this.cache.mode){(0,o.xe)(o.Sg.INVALID_OPERATION,"current platform's storage is asynchronous, please use getCurrentUser insteed");return}var t=this.hasLoginState();return t&&t.user||null},enumerable:!1,configurable:!0}),t.prototype.getCurrentUser=function(){return c(this,void 0,void 0,function(){var t;return h(this,function(e){switch(e.label){case 0:return[4,this.getLoginState()];case 1:if(!(t=e.sent()))return[3,3];return[4,t.user.checkLocalInfoAsync()];case 2:return e.sent(),[2,t.user||null];case 3:return[2,null]}})})},t.prototype.signInAnonymously=function(t){return void 0===t&&(t={}),c(this,void 0,void 0,function(){return h(this,function(e){switch(e.label){case 0:return[4,this.oauthInstance.authApi.signInAnonymously(t)];case 1:return e.sent(),[2,this.createLoginState()]}})})},t.prototype.signInAnonymouslyInWx=function(t){var e=(void 0===t?{}:t).useWxCloud;return c(this,void 0,void 0,function(){var t,n,i=this;return h(this,function(r){switch(r.label){case 0:if(!o.Us.isMatch())throw Error("wx api undefined");return t=wx.getAccountInfoSync().miniProgram,n=function(n){return c(i,void 0,void 0,function(){var i,r;return h(this,function(o){switch(o.label){case 0:i=void 0,r=void 0,o.label=1;case 1:return o.trys.push([1,4,,5]),[4,this.oauthInstance.authApi.grantProviderToken({provider_id:null==t?void 0:t.appId,provider_code:n,provider_params:{provider_code_type:"open_id",appid:null==t?void 0:t.appId}},e)];case 2:if((null==(i=o.sent())?void 0:i.error_code)||!i.provider_token)throw i;return[4,this.oauthInstance.authApi.signInAnonymously({provider_token:i.provider_token},e)];case 3:if(null==(r=o.sent())?void 0:r.error_code)throw r;return[3,5];case 4:throw o.sent();case 5:return[2]}})})},[4,new Promise(function(t,e){wx.login({success:function(r){return c(i,void 0,void 0,function(){return h(this,function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,n(r.code)];case 1:return i.sent(),t(!0),[3,3];case 2:return e(i.sent()),[3,3];case 3:return[2]}})})},fail:function(t){var n=Error(null==t?void 0:t.errMsg);n.code=null==t?void 0:t.errno,e(n)}})})];case 1:return r.sent(),[2,this.createLoginState(void 0,{asyncRefreshUser:!0})]}})})},t.prototype.bindOpenId=function(){return c(this,void 0,void 0,function(){var t,e,n=this;return h(this,function(i){switch(i.label){case 0:if(!o.Us.isMatch())throw Error("wx api undefined");return t=wx.getAccountInfoSync().miniProgram,e=function(e){return c(n,void 0,void 0,function(){var n;return h(this,function(i){switch(i.label){case 0:n=void 0,i.label=1;case 1:return i.trys.push([1,4,,5]),[4,this.oauthInstance.authApi.grantProviderToken({provider_id:null==t?void 0:t.appId,provider_code:e,provider_params:{provider_code_type:"open_id",appid:null==t?void 0:t.appId}})];case 2:if((null==(n=i.sent())?void 0:n.error_code)||!n.provider_token)throw n;return[4,this.oauthInstance.authApi.bindWithProvider({provider_token:n.provider_token})];case 3:return i.sent(),[3,5];case 4:throw i.sent();case 5:return[2]}})})},[4,new Promise(function(t,i){wx.login({success:function(r){return c(n,void 0,void 0,function(){return h(this,function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,e(r.code)];case 1:return n.sent(),t(!0),[3,3];case 2:return i(n.sent()),[3,3];case 3:return[2]}})})},fail:function(t){var e=Error(null==t?void 0:t.errMsg);e.code=null==t?void 0:t.errno,i(e)}})})];case 1:return i.sent(),[2]}})})},t.prototype.signInWithOpenId=function(t){var e=(void 0===t?{}:t).useWxCloud,n=void 0===e||e;return c(this,void 0,void 0,function(){var t,e,i=this;return h(this,function(r){switch(r.label){case 0:if(!o.Us.isMatch())throw Error("wx api undefined");return t=wx.getAccountInfoSync().miniProgram,e=function(e){return c(i,void 0,void 0,function(){var i,r;return h(this,function(o){switch(o.label){case 0:i=void 0,r=void 0,o.label=1;case 1:return o.trys.push([1,4,,5]),[4,this.oauthInstance.authApi.grantProviderToken({provider_id:null==t?void 0:t.appId,provider_code:e,provider_params:{provider_code_type:"open_id",appid:null==t?void 0:t.appId}},n)];case 2:if((null==(i=o.sent())?void 0:i.error_code)||!i.provider_token)throw i;return[4,this.oauthInstance.authApi.signInWithProvider({provider_token:i.provider_token},n)];case 3:if(null==(r=o.sent())?void 0:r.error_code)throw r;return[3,5];case 4:throw o.sent();case 5:return[4,this.oauthInstance.oauth2client.setCredentials(r)];case 6:return o.sent(),[2]}})})},[4,new Promise(function(t,n){wx.login({success:function(r){return c(i,void 0,void 0,function(){return h(this,function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,e(r.code)];case 1:return i.sent(),t(!0),[3,3];case 2:return n(i.sent()),[3,3];case 3:return[2]}})})},fail:function(t){var e=Error(null==t?void 0:t.errMsg);e.code=null==t?void 0:t.errno,n(e)}})})];case 1:return r.sent(),[2,this.createLoginState()]}})})},t.prototype.signInWithUnionId=function(){return c(this,void 0,void 0,function(){var t=this;return h(this,function(e){switch(e.label){case 0:if(!o.Us.isMatch())throw Error("wx api undefined");e.label=1;case 1:return e.trys.push([1,3,,4]),[4,new Promise(function(e,n){var i=wx.getAccountInfoSync().miniProgram;wx.login({success:function(r){return c(t,void 0,void 0,function(){var t,o,s,u;return h(this,function(a){switch(a.label){case 0:t=null==i?void 0:i.appId,a.label=1;case 1:return a.trys.push([1,4,,5]),[4,this.oauthInstance.authApi.grantProviderToken({provider_code:r.code,provider_id:t,provider_params:{provider_code_type:"union_id",appid:null==i?void 0:i.appId}})];case 2:if(!(s=(o=a.sent()).provider_token))return n(o),[2];return[4,this.oauthInstance.authApi.signInWithProvider({provider_id:t,provider_token:s})];case 3:if(null==(u=a.sent())?void 0:u.error_code)return n(u),[2];return e(!0),[3,5];case 4:return n(a.sent()),[3,5];case 5:return[2]}})})},fail:function(t){var e=Error(null==t?void 0:t.errMsg);e.code=null==t?void 0:t.errno,n(e)}})})];case 2:return e.sent(),[3,4];case 3:throw e.sent();case 4:return[2,this.createLoginState()]}})})},t.prototype.signInWithPhoneAuth=function(t){var e=t.phoneCode,n=void 0===e?"":e;return c(this,void 0,void 0,function(){var t,e,i,r,s;return h(this,function(u){switch(u.label){case 0:if(!o.Us.isMatch())throw Error("wx api undefined");return e={provider_params:{provider_code_type:"phone"},provider_id:(t=wx.getAccountInfoSync().miniProgram).appId},[4,wx.login()];case 1:i=u.sent().code,e.provider_code=i,u.label=2;case 2:return u.trys.push([2,6,,7]),[4,this.oauthInstance.authApi.grantProviderToken(e)];case 3:if((r=u.sent()).error_code)throw r;return[4,this.oauthInstance.authApi.patchProviderToken({provider_token:r.provider_token,provider_id:t.appId,provider_params:{code:n,provider_code_type:"phone"}})];case 4:if((r=u.sent()).error_code)throw r;return[4,this.oauthInstance.authApi.signInWithProvider({provider_token:r.provider_token})];case 5:if(null==(s=u.sent())?void 0:s.error_code)throw s;return[3,7];case 6:throw u.sent();case 7:return[2,this.createLoginState()]}})})},t.prototype.signInWithSms=function(t){var e=t.verificationInfo,n=void 0===e?{verification_id:"",is_user:!1}:e,i=t.verificationCode,r=void 0===i?"":i,o=t.phoneNum,s=void 0===o?"":o,u=t.bindInfo,a=void 0===u?void 0:u;return c(this,void 0,void 0,function(){return h(this,function(t){try{return[2,this.signInWithUsername({verificationInfo:n,verificationCode:r,bindInfo:a,username:s,loginType:"sms"})]}catch(t){throw t}return[2]})})},t.prototype.signInWithEmail=function(t){var e=t.verificationInfo,n=void 0===e?{verification_id:"",is_user:!1}:e,i=t.verificationCode,r=void 0===i?"":i,o=t.bindInfo,s=void 0===o?void 0:o,u=t.email,a=void 0===u?"":u;return c(this,void 0,void 0,function(){return h(this,function(t){try{return[2,this.signInWithUsername({verificationInfo:n,verificationCode:r,bindInfo:s,username:a,loginType:"email"})]}catch(t){throw t}return[2]})})},t.prototype.setCustomSignFunc=function(t){this.oauthInstance.authApi.setCustomSignFunc(t)},t.prototype.signInWithCustomTicket=function(){return c(this,void 0,void 0,function(){return h(this,function(t){switch(t.label){case 0:return[4,this.oauthInstance.authApi.signInWithCustomTicket()];case 1:return t.sent(),[2,this.createLoginState()]}})})},t.prototype.signIn=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){switch(e.label){case 0:return[4,this.oauthInstance.authApi.signIn(t)];case 1:return e.sent(),[2,this.createLoginState(t)]}})})},t.prototype.signUp=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){switch(e.label){case 0:return[4,this.oauthInstance.authApi.signUp(t)];case 1:return e.sent(),[2,this.createLoginState()]}})})},t.prototype.setPassword=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.setPassword(t)]})})},t.prototype.isUsernameRegistered=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){switch(e.label){case 0:return"string"!=typeof t&&(0,o._y)(o.Sg.INVALID_PARAMS,"username must be a string"),[4,this.oauthInstance.authApi.checkIfUserExist({username:t})];case 1:return[2,e.sent().exist]}})})},t.prototype.signOut=function(t){return c(this,void 0,void 0,function(){var e,n;return h(this,function(i){switch(i.label){case 0:return e=this.cache.keys.userInfoKey,[4,this.oauthInstance.authApi.signOut(t)];case 1:return n=i.sent(),[4,this.cache.removeStoreAsync(e)];case 2:return i.sent(),p.fire(d.LOGIN_STATE_CHANGED),[2,n]}})})},t.prototype.hasLoginState=function(){var t;if("async"===this.cache.mode){(0,o.xe)(o.Sg.INVALID_OPERATION,"current platform's storage is asynchronous, please use getLoginState insteed");return}return(null===(t=this.oauthInstance)||void 0===t?void 0:t.authApi.hasLoginStateSync())?new l({envId:this.config.env,cache:this.cache,oauthInstance:this.oauthInstance}):null},t.prototype.getLoginState=function(){return c(this,void 0,void 0,function(){var t;return h(this,function(e){switch(e.label){case 0:t=null,e.label=1;case 1:return e.trys.push([1,3,,4]),[4,this.oauthInstance.authApi.getLoginState()];case 2:return t=e.sent(),[3,4];case 3:return e.sent(),[2,null];case 4:if(t)return[2,new l({envId:this.config.env,cache:this.cache,oauthInstance:this.oauthInstance})];return[2,null]}})})},t.prototype.getUserInfo=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.getUserInfo(t)]})})},t.prototype.getWedaUserInfo=function(){return c(this,void 0,void 0,function(){return h(this,function(t){return[2,this.oauthInstance.authApi.getWedaUserInfo()]})})},t.prototype.updateUserBasicInfo=function(t){return c(this,void 0,void 0,function(){var e;return h(this,function(n){switch(n.label){case 0:return[4,this.getLoginState()];case 1:if(!(e=n.sent()))return[3,3];return[4,e.user.updateUserBasicInfo(t)];case 2:n.sent(),n.label=3;case 3:return[2]}})})},t.prototype.getAuthHeader=function(){return console.error("Auth.getAuthHeader API 已废弃"),{}},t.prototype.bindWithProvider=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.bindWithProvider(t)]})})},t.prototype.queryUser=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.queryUserProfile(t)]})})},t.prototype.getAccessToken=function(){return c(this,void 0,void 0,function(){return h(this,function(t){switch(t.label){case 0:return[4,this.oauthInstance.oauth2client.getAccessToken()];case 1:return[2,{accessToken:t.sent(),env:this.config.env}]}})})},t.prototype.grantProviderToken=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.grantProviderToken(t)]})})},t.prototype.patchProviderToken=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.patchProviderToken(t)]})})},t.prototype.signInWithProvider=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){switch(e.label){case 0:return[4,this.oauthInstance.authApi.signInWithProvider(t)];case 1:return e.sent(),[2,this.createLoginState(t)]}})})},t.prototype.signInWithWechat=function(t){return void 0===t&&(t={}),c(this,void 0,void 0,function(){return h(this,function(e){switch(e.label){case 0:return[4,this.oauthInstance.authApi.signInWithWechat(t)];case 1:return e.sent(),[2,this.createLoginState(t)]}})})},t.prototype.grantToken=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){switch(e.label){case 0:return[4,this.oauthInstance.authApi.grantToken(t)];case 1:return e.sent(),[2,this.createLoginState()]}})})},t.prototype.genProviderRedirectUri=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.genProviderRedirectUri(t)]})})},t.prototype.resetPassword=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.resetPassword(t)]})})},t.prototype.deviceAuthorize=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.deviceAuthorize(t)]})})},t.prototype.sudo=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.sudo(t)]})})},t.prototype.deleteMe=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.deleteMe(t)]})})},t.prototype.getProviders=function(){return c(this,void 0,void 0,function(){return h(this,function(t){return[2,this.oauthInstance.authApi.getProviders()]})})},t.prototype.loginScope=function(){return c(this,void 0,void 0,function(){return h(this,function(t){return[2,this.oauthInstance.authApi.loginScope()]})})},t.prototype.loginGroups=function(){return c(this,void 0,void 0,function(){return h(this,function(t){return[2,this.oauthInstance.authApi.loginGroups()]})})},t.prototype.onLoginStateChanged=function(t){return c(this,void 0,void 0,function(){var e,n=this;return h(this,function(i){switch(i.label){case 0:return p.on(d.LOGIN_STATE_CHANGED,function(){return c(n,void 0,void 0,function(){var e;return h(this,function(n){switch(n.label){case 0:return[4,this.getLoginState()];case 1:return e=n.sent(),t.call(this,e),[2]}})})}),[4,this.getLoginState()];case 1:return e=i.sent(),t.call(this,e),[2]}})})},t.prototype.refreshTokenForce=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.refreshTokenForce(t)]})})},t.prototype.getCredentials=function(){return c(this,void 0,void 0,function(){return h(this,function(t){return[2,this.oauthInstance.authApi.getCredentials()]})})},t.prototype.setCredentials=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){switch(e.label){case 0:return[4,this.oauthInstance.oauth2client.setCredentials(t)];case 1:return e.sent(),[2]}})})},t.prototype.getProviderSubType=function(){return c(this,void 0,void 0,function(){return h(this,function(t){return[2,this.oauthInstance.authApi.getProviderSubType()]})})},t.prototype.createCaptchaData=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.createCaptchaData(t)]})})},t.prototype.verifyCaptchaData=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.verifyCaptchaData(t)]})})},t.prototype.getMiniProgramQrCode=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.getMiniProgramCode(t)]})})},t.prototype.getMiniProgramQrCodeStatus=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.getMiniProgramQrCodeStatus(t)]})})},t.prototype.modifyPassword=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.modifyPassword(t)]})})},t.prototype.modifyPasswordWithoutLogin=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.modifyPasswordWithoutLogin(t)]})})},t.prototype.getUserBehaviorLog=function(t){return c(this,void 0,void 0,function(){return h(this,function(e){return[2,this.oauthInstance.authApi.getUserBehaviorLog(t)]})})},t.prototype.toDefaultLoginPage=function(t){return void 0===t&&(t={}),c(this,void 0,void 0,function(){var e,n,i,r;return h(this,function(s){return e=t.config_version||"env",o.Us.isMatch()?wx.navigateTo({url:"/packages/$wd_system/pages/login/index"}):(i=new URL(n=t.redirect_uri||window.location.href),r="".concat(i.origin,"/__auth/?app_id=").concat(t.app_id||"","&env_id=").concat(this.config.env,"&client_id=").concat(this.config.clientId,"&config_version=").concat(e,"&redirect_uri=").concat(encodeURIComponent(n)),window.location.href=r),[2]})})},t.prototype.createLoginState=function(t,e){return c(this,void 0,void 0,function(){var n;return h(this,function(i){switch(i.label){case 0:return[4,(n=new l({envId:this.config.env,cache:this.cache,oauthInstance:this.oauthInstance})).checkLocalStateAsync()];case 1:if(i.sent(),!(null==e?void 0:e.asyncRefreshUser))return[3,2];return n.user.refresh(t),[3,4];case 2:return[4,n.user.refresh(t)];case 3:i.sent(),i.label=4;case 4:return p.fire(d.LOGIN_STATE_CHANGED),[2,n]}})})},t.prototype.signInWithUsername=function(t){var e=t.verificationInfo,n=void 0===e?{verification_id:"",is_user:!1}:e,i=t.verificationCode,r=void 0===i?"":i,o=t.username,u=void 0===o?"":o,a=t.bindInfo,d=void 0===a?void 0:a,p=t.loginType,f=void 0===p?"":p;return c(this,void 0,void 0,function(){var t,e,i,o,a,c,p;return h(this,function(h){switch(h.label){case 0:return h.trys.push([0,8,,9]),[4,this.oauthInstance.authApi.verify({verification_id:n.verification_id,verification_code:r})];case 1:if(null==(t=h.sent())?void 0:t.error_code)throw t;if(e=t.verification_token,o={phone_number:i="+86 ".concat(u)},"email"===f&&(o={email:i=u}),!n.is_user)return[3,5];return[4,this.oauthInstance.authApi.signIn({username:i,verification_token:e})];case 2:if(null==(a=h.sent())?void 0:a.error_code)throw a;if(!d)return[3,4];return[4,this.oauthInstance.authApi.bindWithProvider({provider_token:null==d?void 0:d.providerToken})];case 3:if(null==(c=h.sent())?void 0:c.error_code)throw c;h.label=4;case 4:return[3,7];case 5:return[4,this.oauthInstance.authApi.signUp(s(s({},o),{verification_token:e,provider_token:null==d?void 0:d.providerId}))];case 6:if(null==(p=h.sent())?void 0:p.error_code)throw p;h.label=7;case 7:return[2,this.createLoginState()];case 8:throw h.sent();case 9:return[2]}})})},u([(0,o.Dz)({title:"绑定手机号失败",messages:["请确认以下各项：","  1 - 调用 auth().bindPhoneNumber() 的语法或参数是否正确","  2 - 当前环境是否开通了短信验证码登录","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[Object]),a("design:returntype",Promise)],t.prototype,"bindPhoneNumber",null),u([(0,o.Dz)({title:"解除三方绑定失败",messages:["请确认以下各项：","  1 - 调用 auth().unbindProvider() 的语法或参数是否正确","  2 - 当前账户是否已经与此登录方式解绑","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[Object]),a("design:returntype",Promise)],t.prototype,"unbindProvider",null),u([(0,o.Dz)({title:"绑定邮箱地址失败",messages:["请确认以下各项：","  1 - 调用 auth().bindEmail() 的语法或参数是否正确","  2 - 当前环境是否开通了邮箱密码登录","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[Object]),a("design:returntype",void 0)],t.prototype,"bindEmail",null),u([(0,o.Dz)({title:"验证码验证失败",messages:["请确认以下各项：","  1 - 调用 auth().verify() 的语法或参数是否正确","  2 - 当前环境是否开通了手机验证码/邮箱登录","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[Object]),a("design:returntype",Promise)],t.prototype,"verify",null),u([(0,o.Dz)({title:"获取验证码失败",messages:["请确认以下各项：","  1 - 调用 auth().getVerification() 的语法或参数是否正确","  2 - 当前环境是否开通了手机验证码/邮箱登录","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[Object,Object]),a("design:returntype",Promise)],t.prototype,"getVerification",null),u([(0,o.Dz)({title:"获取用户信息失败",messages:["请确认以下各项：","  1 - 调用 auth().getCurrentUser() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[]),a("design:returntype",Promise)],t.prototype,"getCurrentUser",null),u([(0,o.Dz)({title:"匿名登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了匿名登录","  2 - 调用 auth().signInAnonymously() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[Object]),a("design:returntype",Promise)],t.prototype,"signInAnonymously",null),u([(0,o.Dz)({title:"小程序匿名登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了匿名登录","  2 - 调用 auth().signInAnonymouslyInWx() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[Object]),a("design:returntype",Promise)],t.prototype,"signInAnonymouslyInWx",null),u([(0,o.Dz)({title:"小程序绑定OpenID失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了小程序openId静默登录","  2 - 调用 auth().bindOpenId() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[]),a("design:returntype",Promise)],t.prototype,"bindOpenId",null),u([(0,o.Dz)({title:"小程序openId静默登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了小程序openId静默登录","  2 - 调用 auth().signInWithOpenId() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[Object]),a("design:returntype",Promise)],t.prototype,"signInWithOpenId",null),u([(0,o.Dz)({title:"小程序unionId静默登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了小程序unionId静默登录","  2 - 调用 auth().signInWithUnionId() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[]),a("design:returntype",Promise)],t.prototype,"signInWithUnionId",null),u([(0,o.Dz)({title:"小程序手机号授权登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了小程序手机号授权登录","  2 - 调用 auth().signInWithPhoneAuth() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[Object]),a("design:returntype",Promise)],t.prototype,"signInWithPhoneAuth",null),u([(0,o.Dz)({title:"短信验证码登陆",messages:["请确认以下各项：","  1 - 当前环境是否开启了小程序短信验证码登陆","  2 - 调用 auth().signInWithSms() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[Object]),a("design:returntype",Promise)],t.prototype,"signInWithSms",null),u([(0,o.Dz)({title:"邮箱验证码登陆",messages:["请确认以下各项：","  1 - 当前环境是否开启了邮箱登陆","  2 - 调用 auth().signInWithEmail() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[Object]),a("design:returntype",Promise)],t.prototype,"signInWithEmail",null),u([(0,o.Dz)({title:"自定义登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了自定义登录","  2 - 调用 auth().signInWithCustomTicket() 的语法或参数是否正确","  3 - ticket 是否归属于当前环境","  4 - 创建 ticket 的自定义登录私钥是否过期","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[]),a("design:returntype",Promise)],t.prototype,"signInWithCustomTicket",null),u([(0,o.Dz)({title:"注册失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了指定登录方式","  2 - 调用 auth().signUp() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[Object]),a("design:returntype",Promise)],t.prototype,"signUp",null),u([(0,o.Dz)({title:"获取用户是否被占用失败",messages:["请确认以下各项：","  1 - 调用 auth().isUsernameRegistered() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[String]),a("design:returntype",Promise)],t.prototype,"isUsernameRegistered",null),u([(0,o.Dz)({title:"用户登出失败",messages:["请确认以下各项：","  1 - 调用 auth().signOut() 的语法或参数是否正确","  2 - 当前用户是否为匿名登录（匿名登录不支持signOut）","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[Object]),a("design:returntype",Promise)],t.prototype,"signOut",null),u([(0,o.Dz)({title:"获取本地登录态失败",messages:["请确认以下各项：","  1 - 调用 auth().getLoginState() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[]),a("design:returntype",Promise)],t.prototype,"getLoginState",null),u([(0,o.Dz)({title:"获取用户信息失败",messages:["请确认以下各项：","  1 - 是否已登录","  2 - 调用 auth().getUserInfo() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[Object]),a("design:returntype",Promise)],t.prototype,"getUserInfo",null),u([(0,o.Dz)({title:"获取微搭插件用户信息失败",messages:["请确认以下各项：","  1 - 是否已登录","  2 - 调用 auth().getWedaUserInfo() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[]),a("design:returntype",Promise)],t.prototype,"getWedaUserInfo",null),u([(0,o.Dz)({title:"绑定第三方登录方式失败",messages:["请确认以下各项：","  1 - 调用 auth().bindWithProvider() 的语法或参数是否正确","  2 - 此账户是否已经绑定此第三方","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[Object]),a("design:returntype",Promise)],t.prototype,"bindWithProvider",null),u([(0,o.Dz)({title:"获取身份源类型",messages:["请确认以下各项：","  1 - 调用 auth().getProviderSubType() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(o.vM)]}),a("design:type",Function),a("design:paramtypes",[]),a("design:returntype",Promise)],t.prototype,"getProviderSubType",null),t}(),g={name:"auth",namespace:"auth",entity:function(t){if(void 0===t&&(t={region:"",persistence:"local"}),this.authInstance)return(0,o.xe)(o.Sg.INVALID_OPERATION,"every cloudbase instance should has only one auth object"),this.authInstance;var e,n,u,a,c,h,d,p,f,l,g,y,I,m,b=this.platform.adapter,w=t.persistence||b.primaryStorage;w&&w!==this.config.persistence&&this.updateConfig({persistence:w});var _=(e=s(s({wxCloud:this.config.wxCloud,storage:this.config.storage},t),{persistence:this.config.persistence}),n={env:this.config.env,clientId:this.config.clientId,apiOrigin:this.request.getBaseEndPoint(),platform:this.platform,cache:this.cache,app:this,debug:this.config.debug},a=void 0===(u=e.region)?"ap-shanghai":u,h=(c=(null==n?void 0:n.platform)||(0,o.xY)()).runtime,p=(d=n||{}).env,f=d.clientId,l=d.debug,g=d.cache,y=d.app,(I=(n||{}).apiOrigin)||(I="https://".concat(p,".").concat(a,".tcb-api.tencentcloudapi.com")),m=new i.hW((0,r.X)({env:p,clientId:f,apiOrigin:I,storage:null==e?void 0:e.storage,baseRequest:null==e?void 0:e.baseRequest,request:null==e?void 0:e.request,anonymousSignInFunc:null==e?void 0:e.anonymousSignInFunc,captchaOptions:null==e?void 0:e.captchaOptions,wxCloud:null==e?void 0:e.wxCloud})),{authInstance:new v({env:p,clientId:f,region:a,persistence:e.persistence,debug:l,cache:g||new o.qU({persistence:e.persistence,keys:{userInfoKey:"user_info_".concat(p)},platformInfo:c}),runtime:h||"web",_fromApp:y,oauthInstance:m}),oauthInstance:m}),A=_.authInstance,P=_.oauthInstance;return this.oauthInstance=P,this.authInstance=A,this.authInstance}};try{cloudbase.registerComponent(g)}catch(t){}function y(t){try{t.registerComponent(g)}catch(t){console.warn(t)}}}}]);