import type { Metadata } from 'next';
import { AuthProvider } from '@/components/auth/AuthProvider';
import { ToastProvider } from '@/components/ui/Toast';
import BottomNav from '@/components/layout/BottomNav';
import './globals.css';

export const metadata: Metadata = {
  title: '宠物交易平台 - TikTok风格的现代化宠物交易平台',
  description: '一个基于腾讯云开发的现代化宠物交易平台，采用TikTok风格的UI设计和完整的社交功能',
  keywords: ['宠物', '交易', '平台', 'TikTok', '社交', '云开发'],
  authors: [{ name: 'Pet Trading Platform Team' }],
  manifest: '/pet-platform-v2/manifest.json',
  icons: {
    icon: '/pet-platform-v2/favicon.ico',
    apple: '/pet-platform-v2/apple-touch-icon.png',
  },
  openGraph: {
    title: '宠物交易平台',
    description: 'TikTok风格的现代化宠物交易平台',
    type: 'website',
    locale: 'zh_CN',
    siteName: '宠物交易平台',
  },
  twitter: {
    card: 'summary_large_image',
    title: '宠物交易平台',
    description: 'TikTok风格的现代化宠物交易平台',
  },
};

export function generateViewport() {
  return {
    width: 'device-width',
    initialScale: 1,
    themeColor: '#3b82f6',
  };
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <head>
        {/* CloudBase SDK 将通过NPM包加载，不再使用CDN */}
      </head>
      <body className="font-sans">
        <AuthProvider>
          <div className="pb-16 md:pb-0">
            {children}
          </div>
          <BottomNav />
          <ToastProvider />
        </AuthProvider>
      </body>
    </html>
  );
}
