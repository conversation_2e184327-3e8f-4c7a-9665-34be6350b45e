# 🚨 性能优化问题排查和回滚指南

## 🔍 **常见问题诊断**

### 1️⃣ **缓存相关问题**

#### **问题：缓存不生效**
**症状**：
- 每次查询都很慢
- 性能监控显示缓存命中率为0%
- 控制台显示 `"fromCache": false`

**排查步骤**：
```javascript
// 1. 检查缓存管理器状态
console.log('数据缓存状态:', dataCache.getStats());
console.log('图片缓存状态:', imageCache.getStats());

// 2. 检查云函数缓存
// 调用云函数查看缓存统计
{
  "action": "getCacheStats"
}

// 3. 检查缓存TTL配置
console.log('缓存配置:', CACHE_TTL);
```

**解决方案**：
```javascript
// 方案1：重新初始化缓存
dataCache.clear();
imageCache.clear();

// 方案2：检查缓存键生成
const key = `posts_${JSON.stringify(params)}`;
console.log('缓存键:', key);

// 方案3：调整缓存TTL
const CACHE_TTL = {
  posts: 5 * 60 * 1000,  // 增加到5分钟
  users: 15 * 60 * 1000, // 增加到15分钟
};
```

#### **问题：内存泄漏**
**症状**：
- 页面使用时间长后变慢
- 浏览器内存占用持续增长
- 缓存大小超出限制

**排查步骤**：
```javascript
// 1. 检查内存使用
console.log('内存使用:', performance.memory);

// 2. 检查缓存大小
console.log('缓存统计:', {
  dataCache: dataCache.getStats(),
  imageCache: imageCache.getStats()
});

// 3. 检查是否有未清理的定时器
console.log('活跃定时器数量:', window.activeTimers?.length || 0);
```

**解决方案**：
```javascript
// 方案1：强制清理缓存
dataCache.clear();
imageCache.clear();

// 方案2：调整缓存大小限制
const cacheConfig = {
  maxSize: 50,  // 减少到50条
  ttl: 2 * 60 * 1000  // 减少到2分钟
};

// 方案3：手动触发垃圾回收（开发环境）
if (window.gc) {
  window.gc();
}
```

### 2️⃣ **图片优化问题**

#### **问题：图片优化失败**
**症状**：
- 控制台显示优化错误
- 图片大小没有减少
- 上传时间过长

**排查步骤**：
```javascript
// 1. 检查浏览器支持
console.log('WebP支持:', getSupportedImageFormat());

// 2. 检查图片文件
console.log('文件信息:', {
  name: file.name,
  size: file.size,
  type: file.type
});

// 3. 测试优化功能
try {
  const result = await optimizeImage(file, {
    maxWidth: 800,
    quality: 0.8,
    outputFormat: 'auto'
  });
  console.log('优化结果:', result);
} catch (error) {
  console.error('优化失败:', error);
}
```

**解决方案**：
```javascript
// 方案1：降级到原始上传
const uploadWithFallback = async (file) => {
  try {
    const optimized = await optimizeImage(file);
    return await uploadFile(optimized.compressed);
  } catch (error) {
    console.warn('优化失败，使用原图:', error);
    return await uploadFile(file);
  }
};

// 方案2：调整优化参数
const safeOptimizeOptions = {
  maxWidth: 1200,
  quality: 0.9,  // 提高质量
  outputFormat: 'jpeg'  // 强制使用JPEG
};

// 方案3：分批处理大文件
const batchUpload = async (files) => {
  const results = [];
  for (const file of files) {
    try {
      const result = await uploadWithOptimization(file);
      results.push(result);
    } catch (error) {
      console.error(`文件 ${file.name} 上传失败:`, error);
    }
  }
  return results;
};
```

### 3️⃣ **性能监控问题**

#### **问题：监控数据不准确**
**症状**：
- 性能监控页面显示异常数据
- Core Web Vitals指标为0
- API统计数据缺失

**排查步骤**：
```javascript
// 1. 检查监控器初始化
console.log('监控器状态:', performanceMonitor.getMetrics());

// 2. 检查浏览器支持
console.log('PerformanceObserver支持:', 'PerformanceObserver' in window);

// 3. 手动记录性能数据
const startTime = performance.now();
// ... 执行操作
const duration = performance.now() - startTime;
performanceMonitor.recordApiResponse('test', duration);
```

**解决方案**：
```javascript
// 方案1：重新初始化监控器
performanceMonitor.cleanup();
const newMonitor = new PerformanceMonitor();

// 方案2：手动收集关键指标
const manualMetrics = {
  pageLoadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
  domContentLoaded: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart
};

// 方案3：降级到简单统计
const simpleStats = {
  apiCalls: 0,
  totalTime: 0,
  avgTime: 0
};
```

## 🔄 **回滚方案**

### 📋 **回滚检查清单**

#### **何时需要回滚**：
- [ ] 缓存命中率 < 30%
- [ ] API响应时间增加 > 50%
- [ ] 图片上传失败率 > 10%
- [ ] 用户反馈页面变慢
- [ ] 内存泄漏严重
- [ ] 关键功能异常

### 🚨 **紧急回滚步骤**

#### **1. 前端代码回滚**
```bash
# 1. 备份当前代码
git stash push -m "性能优化版本备份"

# 2. 回滚到优化前版本
git checkout HEAD~1  # 或指定commit hash

# 3. 重新部署
npm run build
npm run deploy
```

#### **2. 云函数回滚**
```javascript
// 1. 恢复原始云函数代码
// 移除缓存相关代码，恢复直接数据库查询

// 2. 重新部署云函数
// 使用原始的 optimizedPostQuery 版本

// 3. 清除所有缓存
{
  "action": "clearCache"
}
```

#### **3. 数据库回滚**
```javascript
// 如果有数据结构变更，执行回滚脚本
// 通常性能优化不涉及数据结构变更，无需回滚
```

### 🛠️ **渐进式回滚**

#### **部分功能回滚**：
```javascript
// 1. 只回滚缓存功能
const disableCache = true;
if (disableCache) {
  // 跳过缓存，直接查询
  return await directDatabaseQuery(params);
}

// 2. 只回滚图片优化
const disableImageOptimization = true;
if (disableImageOptimization) {
  // 直接上传原图
  return await uploadFile(originalFile);
}

// 3. 只回滚性能监控
const disableMonitoring = true;
if (disableMonitoring) {
  // 跳过性能监控
  return;
}
```

## 🔧 **配置开关**

### 📱 **功能开关配置**

创建功能开关文件：
```javascript
// src/config/featureFlags.ts
export const FEATURE_FLAGS = {
  // 缓存功能开关
  enableCache: true,
  enableMemoryCache: true,
  enableBrowserCache: true,
  
  // 图片优化开关
  enableImageOptimization: true,
  enableWebPConversion: true,
  enableThumbnailGeneration: true,
  
  // 性能监控开关
  enablePerformanceMonitoring: true,
  enableCoreWebVitals: true,
  enableApiTracking: true,
  
  // 预加载功能开关
  enablePreloading: true,
  enableNextPagePreload: true,
  enableImagePreload: true
};

// 环境相关配置
export const getFeatureFlags = () => {
  const isDev = process.env.NODE_ENV === 'development';
  const isProd = process.env.NODE_ENV === 'production';
  
  if (isDev) {
    return {
      ...FEATURE_FLAGS,
      enablePerformanceMonitoring: true,  // 开发环境启用监控
      enableCache: true  // 开发环境启用缓存测试
    };
  }
  
  return FEATURE_FLAGS;
};
```

### 🎛️ **运行时开关**

```javascript
// 运行时动态控制
window.performanceFlags = {
  disableCache: () => {
    window.DISABLE_CACHE = true;
    dataCache.clear();
    console.log('缓存已禁用');
  },
  
  enableCache: () => {
    window.DISABLE_CACHE = false;
    console.log('缓存已启用');
  },
  
  disableImageOptimization: () => {
    window.DISABLE_IMAGE_OPT = true;
    console.log('图片优化已禁用');
  },
  
  enableImageOptimization: () => {
    window.DISABLE_IMAGE_OPT = false;
    console.log('图片优化已启用');
  }
};
```

## 📊 **监控告警**

### 🚨 **关键指标阈值**

```javascript
// 性能告警阈值
const PERFORMANCE_THRESHOLDS = {
  // API响应时间告警
  apiResponseTime: {
    warning: 1000,  // 1秒
    critical: 3000   // 3秒
  },
  
  // 缓存命中率告警
  cacheHitRate: {
    warning: 50,     // 50%
    critical: 30     // 30%
  },
  
  // 内存使用告警
  memoryUsage: {
    warning: 100,    // 100MB
    critical: 200    // 200MB
  },
  
  // 错误率告警
  errorRate: {
    warning: 5,      // 5%
    critical: 10     // 10%
  }
};

// 自动告警检查
const checkPerformanceThresholds = () => {
  const metrics = performanceMonitor.getPerformanceReport();
  const alerts = [];
  
  // 检查API响应时间
  Object.entries(metrics.apiPerformance).forEach(([api, stats]) => {
    if (stats.avgResponseTime > PERFORMANCE_THRESHOLDS.apiResponseTime.critical) {
      alerts.push(`🚨 ${api} 响应时间过长: ${stats.avgResponseTime}ms`);
    }
  });
  
  // 检查缓存命中率
  const cacheStats = dataCache.getStats();
  if (cacheStats.hitRate < PERFORMANCE_THRESHOLDS.cacheHitRate.critical) {
    alerts.push(`🚨 缓存命中率过低: ${cacheStats.hitRate}%`);
  }
  
  return alerts;
};
```

## 📞 **应急联系**

### 🆘 **问题升级流程**

1. **Level 1 - 自助排查**（5分钟）
   - 查看本文档的常见问题
   - 检查浏览器控制台错误
   - 尝试刷新页面或清除缓存

2. **Level 2 - 功能回滚**（10分钟）
   - 使用功能开关禁用问题功能
   - 执行部分回滚操作
   - 监控系统恢复情况

3. **Level 3 - 完全回滚**（30分钟）
   - 执行完整回滚流程
   - 恢复到优化前版本
   - 通知相关人员

### 📋 **问题报告模板**

```markdown
## 🚨 性能优化问题报告

**问题描述**：
- 问题现象：
- 影响范围：
- 发生时间：

**环境信息**：
- 浏览器版本：
- 设备类型：
- 网络环境：

**错误信息**：
```javascript
// 控制台错误信息
```

**已尝试的解决方案**：
- [ ] 刷新页面
- [ ] 清除缓存
- [ ] 禁用相关功能
- [ ] 其他：

**期望结果**：

**实际结果**：
```

---

**文档维护人**: AI Assistant  
**最后更新**: 2025-07-19  
**紧急联系**: 查看项目README.md
