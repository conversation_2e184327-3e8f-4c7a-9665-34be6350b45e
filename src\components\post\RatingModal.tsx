import React, { useState, useEffect } from 'react';
import { Star } from 'lucide-react';
import { Modal, ModalBody } from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import { Post, RATING_LEVELS } from '@/types';
import { petAPI } from '@/lib/cloudbase';
import { showToast } from '@/components/ui/Toast';
import { cn } from '@/utils';

interface RatingModalProps {
  isOpen: boolean;
  onClose: () => void;
  post: Post;
  onSuccess: () => void;
}

const RatingModal: React.FC<RatingModalProps> = ({
  isOpen,
  onClose,
  post,
  onSuccess,
}) => {
  const [selectedRating, setSelectedRating] = useState<number>(0);
  const [loading, setLoading] = useState(false);

  // 初始化用户已有的评分
  useEffect(() => {
    if (post.user_interactions?.hasRated && post.user_interactions?.userRating) {
      setSelectedRating(post.user_interactions.userRating);
    }
  }, [post.user_interactions]);

  // 重置状态
  const resetState = () => {
    // 如果用户已经评分过，保持显示用户的评分，否则重置为0
    if (post.user_interactions?.hasRated && post.user_interactions?.userRating) {
      setSelectedRating(post.user_interactions.userRating);
    } else {
      setSelectedRating(0);
    }
    setLoading(false);
  };

  // 处理关闭
  const handleClose = () => {
    resetState();
    onClose();
  };

  // 提交评分
  const handleSubmit = async () => {
    if (selectedRating === 0) {
      showToast.warning('请选择评分');
      return;
    }

    // 检查是否已经评分过
    if (post.user_interactions?.hasRated) {
      showToast.warning('您已经评分过了');
      return;
    }

    try {
      setLoading(true);
      const result = await petAPI.ratePet({
        postId: post._id,
        rating: selectedRating,
      });

      if (result.success) {
        showToast.success('评分成功！');

        // 保存评分记录到本地存储
        try {
          const existingRatedPosts = JSON.parse(localStorage.getItem('userRatedPosts') || '[]');
          if (!existingRatedPosts.includes(post._id)) {
            existingRatedPosts.unshift(post._id); // 添加到开头
            // 限制最多保存100个评分记录
            if (existingRatedPosts.length > 100) {
              existingRatedPosts.splice(100);
            }
            localStorage.setItem('userRatedPosts', JSON.stringify(existingRatedPosts));
          }
        } catch (error) {
          console.warn('保存评分记录到本地存储失败:', error);
        }

        // 调用onSuccess回调，让父组件更新数据
        onSuccess();

        // 评分成功后不自动关闭，让用户看到结果并手动关闭
      } else {
        showToast.error(result.message || '评分失败');
      }
    } catch (error: any) {
      showToast.error(error.message || '评分失败');
    } finally {
      setLoading(false);
    }
  };

  // 如果已经评分过，显示评分统计
  if (post.user_interactions?.hasRated) {
    return (
      <Modal
        isOpen={isOpen}
        onClose={handleClose}
        title="评分统计"
        size="sm"
      >
        <ModalBody>
          <div className="space-y-6">
            {/* 平均评分 */}
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 mb-2">
                {post.avg_rating.toFixed(1)}
              </div>
              <div className="flex items-center justify-center space-x-1 mb-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={cn(
                      'h-6 w-6',
                      star <= Math.round(post.avg_rating)
                        ? 'text-yellow-500 fill-current'
                        : 'text-gray-300'
                    )}
                  />
                ))}
              </div>
              <p className="text-sm text-gray-500">
                基于 {post.ratings_count} 个评分
              </p>
            </div>

            {/* 评分分布 */}
            <div className="space-y-2">
              {[5, 4, 3, 2, 1].map((rating) => {
                const count = post.rating_stats?.[rating as keyof typeof post.rating_stats] || 0;
                const percentage = post.ratings_count > 0 ? (count / post.ratings_count) * 100 : 0;
                
                return (
                  <div key={rating} className="flex items-center space-x-3">
                    <div className="flex items-center space-x-1 w-16">
                      <span className="text-sm font-medium">{rating}</span>
                      <Star className="h-3 w-3 text-yellow-500 fill-current" />
                    </div>
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-xs text-gray-500 w-8">{count}</span>
                  </div>
                );
              })}
            </div>

            {/* 您的评分 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600 mb-2">您的评分</p>
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={cn(
                        'h-5 w-5',
                        star <= (post.user_interactions?.userRating || 0)
                          ? 'text-yellow-500 fill-current'
                          : 'text-gray-300'
                      )}
                    />
                  ))}
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {RATING_LEVELS[post.user_interactions?.userRating as keyof typeof RATING_LEVELS]}
                </span>
              </div>
            </div>
          </div>
        </ModalBody>
      </Modal>
    );
  }

  // 评分界面
  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="为这只宠物评分"
      size="sm"
    >
      <ModalBody>
        <div className="space-y-6">
          {/* 宠物信息 */}
          <div className="text-center">
            <h3 className="font-medium text-gray-900">{post.title}</h3>
          </div>

          {/* 评分选择 */}
          <div className="text-center">
            <p className="text-sm text-gray-600 mb-4">
              点击星星为这只宠物评分
            </p>
            
            <div className="flex items-center justify-center space-x-2 mb-4">
              {[1, 2, 3, 4, 5].map((rating) => (
                <button
                  key={rating}
                  onClick={() => {
                    if (!post.user_interactions?.hasRated) {
                      setSelectedRating(rating);
                    }
                  }}
                  className={cn(
                    "transition-transform duration-200 focus:outline-none",
                    post.user_interactions?.hasRated
                      ? "cursor-not-allowed"
                      : "hover:scale-105 active:scale-95 cursor-pointer"
                  )}
                  disabled={loading || post.user_interactions?.hasRated}
                >
                  <Star
                    className={cn(
                      'h-8 w-8 transition-colors duration-200',
                      rating <= selectedRating
                        ? 'text-yellow-500 fill-current'
                        : 'text-gray-300'
                    )}
                  />
                </button>
              ))}
            </div>

            {/* 评分等级显示 */}
            {selectedRating > 0 && (
              <div className="text-center">
                <p className="text-lg font-medium text-gray-900">
                  {RATING_LEVELS[selectedRating as keyof typeof RATING_LEVELS]}
                </p>
                <p className="text-sm text-gray-500">
                  {post.user_interactions?.hasRated ? '您的评价：' : ''}{selectedRating} 星评分
                </p>
              </div>
            )}
          </div>

          {/* 评分说明 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">
              {post.user_interactions?.hasRated ? '评分统计' : '评分标准'}
            </h4>
            <div className="space-y-1 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>⭐ 普通</span>
                {post.user_interactions?.hasRated && <span>{post.rating_stats?.[1] || 0}人</span>}
              </div>
              <div className="flex justify-between">
                <span>⭐⭐ 极品</span>
                {post.user_interactions?.hasRated && <span>{post.rating_stats?.[2] || 0}人</span>}
              </div>
              <div className="flex justify-between">
                <span>⭐⭐⭐ 神品</span>
                {post.user_interactions?.hasRated && <span>{post.rating_stats?.[3] || 0}人</span>}
              </div>
              <div className="flex justify-between">
                <span>⭐⭐⭐⭐ 仙品</span>
                {post.user_interactions?.hasRated && <span>{post.rating_stats?.[4] || 0}人</span>}
              </div>
              <div className="flex justify-between">
                <span>⭐⭐⭐⭐⭐ 圣品</span>
                {post.user_interactions?.hasRated && <span>{post.rating_stats?.[5] || 0}人</span>}
              </div>
            </div>
          </div>

          {/* 提交按钮 */}
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleClose}
              className="flex-1"
              disabled={loading}
            >
              {post.user_interactions?.hasRated ? '关闭' : '取消'}
            </Button>
            {!post.user_interactions?.hasRated && (
              <Button
                onClick={handleSubmit}
                loading={loading}
                className="flex-1"
                disabled={selectedRating === 0}
              >
                提交评分
              </Button>
            )}
          </div>
        </div>
      </ModalBody>
    </Modal>
  );
};

export default RatingModal;
