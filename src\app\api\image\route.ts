import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const fileId = searchParams.get('fileId');

    if (!fileId) {
      return NextResponse.json({ error: 'Missing fileId parameter' }, { status: 400 });
    }

    // 调用云函数获取图片
    const { initCloudBase } = await import('@/lib/cloudbase');
    const cloudbaseApp = await initCloudBase();
    
    if (!cloudbaseApp) {
      return NextResponse.json({ error: 'CloudBase not initialized' }, { status: 500 });
    }

    const result = await cloudbaseApp.callFunction({
      name: 'pet-api',
      data: {
        action: 'getImage',
        data: { fileId }
      }
    });

    if (result.result && result.result.success && result.result.data) {
      const imageData = result.result.data;
      
      // 返回图片数据
      const buffer = Buffer.from(imageData.content, 'base64');
      
      return new NextResponse(buffer, {
        headers: {
          'Content-Type': imageData.contentType || 'image/jpeg',
          'Cache-Control': 'public, max-age=86400', // 缓存1天
        },
      });
    } else {
      return NextResponse.json({ error: 'Image not found' }, { status: 404 });
    }
  } catch (error) {
    console.error('Image proxy error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
