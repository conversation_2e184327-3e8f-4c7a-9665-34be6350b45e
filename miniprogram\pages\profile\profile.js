// 个人中心页面
const app = getApp()

Page({
  data: {
    userInfo: {},
    hasUserInfo: false
  },

  onLoad: function () {
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        hasUserInfo: true
      })
    }
  },

  onShow: function () {
    // 每次显示页面时检查登录状态
    this.checkLoginStatus()
  },

  checkLoginStatus: function() {
    wx.cloud.callFunction({
      name: 'user-auth',
      data: {
        action: 'checkLogin'
      },
      success: res => {
        if (res.result.success) {
          this.setData({
            userInfo: res.result.userInfo,
            hasUserInfo: true
          })
        }
      },
      fail: err => {
        console.error('检查登录状态失败', err)
      }
    })
  },

  // 查看我的发布
  viewMyPosts: function() {
    wx.navigateTo({
      url: '/pages/my-posts/my-posts'
    })
  },

  // 查看我的收藏
  viewMyFavorites: function() {
    wx.navigateTo({
      url: '/pages/my-favorites/my-favorites'
    })
  },

  // 设置页面
  goToSettings: function() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  // 联系客服
  contactService: function() {
    wx.showModal({
      title: '联系客服',
      content: '请添加微信：petservice123',
      showCancel: false
    })
  }
})
