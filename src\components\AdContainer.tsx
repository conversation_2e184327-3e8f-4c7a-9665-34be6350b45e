'use client';

import { useState, useEffect } from 'react';
import { XMarkIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

interface Ad {
  _id: string;
  title: string;
  content: string;
  image_url?: string;
  video_url?: string;
  target_url: string;
  ad_type: 'banner' | 'feed' | 'popup' | 'video';
  priority: number;
}

interface AdPosition {
  position_id: string;
  name: string;
  page: string;
  location: string;
  width: number;
  height: number;
  ad_type: string;
  max_ads: number;
  rotation_interval: number;
  status: 'active' | 'inactive';
}

interface AdContainerProps {
  positionId: string;
  className?: string;
  fallbackContent?: React.ReactNode;
}

export default function AdContainer({ positionId, className = '', fallbackContent }: AdContainerProps) {
  const [ads, setAds] = useState<Ad[]>([]);
  const [position, setPosition] = useState<AdPosition | null>(null);
  const [currentAdIndex, setCurrentAdIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [isUserHidden, setIsUserHidden] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadAdsForPosition();
  }, [positionId]);

  useEffect(() => {
    // 广告轮播
    if (ads.length > 1 && position?.rotation_interval && position.rotation_interval > 0) {
      const interval = setInterval(() => {
        setCurrentAdIndex((prev) => (prev + 1) % ads.length);
      }, position.rotation_interval);

      return () => clearInterval(interval);
    }
  }, [ads, position]);

  const loadAdsForPosition = async () => {
    try {
      setLoading(true);

      // 暂时使用模拟数据，直到云函数完全配置好
      const mockData = getMockAdData(positionId);
      setPosition(mockData.position);
      setAds(mockData.ads || []);

    } catch (error) {
      console.error('加载广告失败:', error);
    } finally {
      // 模拟加载完成
      setTimeout(() => setLoading(false), 500);
    }
  };

  // 获取模拟广告数据
  const getMockAdData = (positionId: string) => {
    const mockPositions: Record<string, any> = {
      'home_banner': {
        position: {
          position_id: 'home_banner',
          name: '首页横幅广告',
          page: 'home',
          location: 'top',
          width: 728,
          height: 90,
          ad_type: 'banner',
          max_ads: 3,
          rotation_interval: 5000,
          status: 'active'
        },
        ads: [
          {
            _id: 'banner_ad_1',
            title: '优质宠物用品推荐',
            content: '为您的爱宠提供最好的生活用品，健康快乐每一天！',
            image_url: '',
            target_url: '#',
            ad_type: 'banner',
            priority: 1
          }
        ]
      },
      'home_feed': {
        position: {
          position_id: 'home_feed',
          name: '首页信息流广告',
          page: 'home',
          location: 'feed',
          width: 300,
          height: 200,
          ad_type: 'feed',
          max_ads: 5,
          rotation_interval: 0,
          status: 'active'
        },
        ads: [
          {
            _id: 'feed_ad_1',
            title: '专业宠物医院',
            content: '24小时宠物医疗服务，专业医师团队，让您的爱宠健康无忧。',
            image_url: '',
            target_url: '#',
            ad_type: 'feed',
            priority: 1
          },
          {
            _id: 'feed_ad_2',
            title: '宠物美容服务',
            content: '专业宠物美容，让您的爱宠更加美丽动人。',
            image_url: '',
            target_url: '#',
            ad_type: 'feed',
            priority: 2
          }
        ]
      }
    };

    return mockPositions[positionId] || { position: null, ads: [] };
  };

  const handleAdClick = (ad: Ad) => {
    // 记录点击统计
    recordAdClick(ad._id);
    
    // 跳转到目标链接
    if (ad.target_url) {
      window.open(ad.target_url, '_blank');
    }
  };

  const recordAdClick = async (adId: string) => {
    try {
      // 暂时只记录到控制台，后续可以调用云函数记录
      console.log('广告点击记录:', { ad_id: adId, timestamp: new Date() });

      // TODO: 后续调用云函数记录点击统计
      // await petAPI.recordAdClick({ ad_id: adId });
    } catch (error) {
      console.error('记录广告点击失败:', error);
    }
  };

  const handleUserHide = () => {
    setIsUserHidden(true);
    // 记录用户隐藏广告的行为
    localStorage.setItem(`ad_hidden_${positionId}`, Date.now().toString());
  };

  const shouldShowAd = () => {
    // 检查广告位是否启用
    if (!position || position.status !== 'active') return false;
    
    // 检查是否有可用广告
    if (!ads || ads.length === 0) return false;
    
    // 检查用户是否手动隐藏
    if (isUserHidden) return false;
    
    // 检查用户隐藏的时间限制（24小时后重新显示）
    const hiddenTime = localStorage.getItem(`ad_hidden_${positionId}`);
    if (hiddenTime) {
      const hideTime = parseInt(hiddenTime);
      const now = Date.now();
      const hoursPassed = (now - hideTime) / (1000 * 60 * 60);
      if (hoursPassed < 24) return false;
    }
    
    return true;
  };

  // 加载中状态
  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="bg-gray-200 rounded-lg h-20"></div>
      </div>
    );
  }

  // 不显示广告的情况
  if (!shouldShowAd()) {
    // 如果有备用内容，显示备用内容
    if (fallbackContent) {
      return <div className={className}>{fallbackContent}</div>;
    }
    // 否则完全隐藏
    return null;
  }

  const currentAd = ads[currentAdIndex];

  // 根据广告类型渲染不同样式
  const renderAd = () => {
    switch (position?.ad_type) {
      case 'banner':
        return (
          <div className="relative bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg overflow-hidden">
            <button 
              onClick={handleUserHide}
              className="absolute top-2 right-2 z-10 bg-white/80 hover:bg-white rounded-full p-1 transition-colors"
              title="隐藏广告"
            >
              <XMarkIcon className="h-4 w-4 text-gray-600" />
            </button>
            
            <div 
              className="flex items-center p-4 cursor-pointer hover:bg-black/5 transition-colors"
              onClick={() => handleAdClick(currentAd)}
            >
              {currentAd.image_url && (
                <img 
                  src={currentAd.image_url} 
                  alt={currentAd.title}
                  className="w-16 h-16 rounded-lg object-cover mr-4"
                />
              )}
              <div className="flex-1">
                <h4 className="font-medium text-gray-900 mb-1">{currentAd.title}</h4>
                <p className="text-sm text-gray-600 line-clamp-2">{currentAd.content}</p>
              </div>
              <div className="ml-4">
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">广告</span>
              </div>
            </div>
          </div>
        );

      case 'feed':
        return (
          <div className="bg-white rounded-lg shadow-sm border border-gray-100">
            <div className="flex items-center justify-between p-3 border-b border-gray-100">
              <span className="text-xs text-gray-500 bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                推广内容
              </span>
              <button 
                onClick={handleUserHide}
                className="text-gray-400 hover:text-gray-600 transition-colors"
                title="隐藏此广告"
              >
                <EyeSlashIcon className="h-4 w-4" />
              </button>
            </div>
            
            <div 
              className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
              onClick={() => handleAdClick(currentAd)}
            >
              {currentAd.image_url && (
                <img 
                  src={currentAd.image_url} 
                  alt={currentAd.title}
                  className="w-full h-40 object-cover rounded-lg mb-3"
                />
              )}
              <h3 className="font-medium text-gray-900 mb-2">{currentAd.title}</h3>
              <p className="text-sm text-gray-600 mb-3 line-clamp-3">{currentAd.content}</p>
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500">点击了解更多</span>
                <div className="flex items-center space-x-2">
                  {ads.length > 1 && (
                    <div className="flex space-x-1">
                      {ads.map((_, index) => (
                        <div
                          key={index}
                          className={`w-2 h-2 rounded-full ${
                            index === currentAdIndex ? 'bg-blue-500' : 'bg-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );

      case 'popup':
        return (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-sm w-full overflow-hidden">
              <div className="relative">
                <button 
                  onClick={handleUserHide}
                  className="absolute top-2 right-2 z-10 bg-black/20 hover:bg-black/40 text-white rounded-full p-1 transition-colors"
                >
                  <XMarkIcon className="h-5 w-5" />
                </button>
                
                {currentAd.image_url && (
                  <img 
                    src={currentAd.image_url} 
                    alt={currentAd.title}
                    className="w-full h-48 object-cover"
                  />
                )}
              </div>
              
              <div className="p-4">
                <h3 className="font-bold text-lg text-gray-900 mb-2">{currentAd.title}</h3>
                <p className="text-gray-600 mb-4">{currentAd.content}</p>
                <div className="flex space-x-2">
                  <button 
                    onClick={() => handleAdClick(currentAd)}
                    className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    了解更多
                  </button>
                  <button 
                    onClick={handleUserHide}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    跳过
                  </button>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={className}>
      {renderAd()}
    </div>
  );
}
