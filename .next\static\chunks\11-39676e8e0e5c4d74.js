"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[11],{98011:function(e,t,o){o.r(t),o.d(t,{activityAPI:function(){return d},app:function(){return s},auth:function(){return n},authAPI:function(){return y},db:function(){return i},getImageUrl:function(){return h},initCloudBase:function(){return r},petAPI:function(){return u},uploadFile:function(){return f},uploadFileToStatic:function(){return v}});var a=o(83393);let s=null,n=null,i=null,l=null,r=async()=>s?(console.log("CloudBase已初始化，直接返回"),s):l?(console.log("CloudBase正在初始化，等待完成..."),l):l=(async()=>{try{console.log("开始CloudBase NPM包初始化..."),console.log("CloudBase SDK版本:",a.Z),s=a.Z.init({env:"yichongyuzhou-3g9112qwf5f3487b",region:"ap-shanghai"}),console.log("CloudBase应用初始化完成，初始化服务..."),n=s.auth(),i=s.database(),console.log("检查登录状态...");let e=await n.getLoginState();if(console.log("当前登录状态:",e),e&&e.isLoggedIn)console.log("已登录，跳过匿名登录");else{console.log("未登录，开始匿名登录...");try{await n.signInAnonymously(),console.log("匿名登录成功！");let e=await n.getLoginState();console.log("新登录状态:",e)}catch(e){throw console.error("匿名登录失败:",e),Error("匿名登录失败: ".concat((null==e?void 0:e.message)||"未知错误"))}}return console.log("CloudBase NPM包初始化成功！"),console.log("应用实例:",s),console.log("认证服务:",n),console.log("数据库服务:",i),s}catch(e){throw console.error("CloudBase NPM包初始化失败:",e),console.error("错误详情:",null==e?void 0:e.message),l=null,Error("CloudBase初始化失败: ".concat((null==e?void 0:e.message)||"未知错误"))}})(),c=()=>{try{let e=localStorage.getItem("pet_platform_user"),t="true"===localStorage.getItem("pet_platform_logged_in");return e&&t}catch(e){return!1}},p=["toggleLike","toggleDislike","toggleBookmark","ratePet","exchangeContact","toggleFollow","reportPost","reportUser","wantPet","submitAppeal","getUserBookmarks","getUserPosts","getUserFollowing","getUserPermissions"],g=async(e,t,o)=>{try{if(console.log("\uD83D\uDE80 开始调用云函数: ".concat(e,".").concat(t),o),p.includes(t)){if(!c())throw console.log("❌ 操作需要登录，但用户未登录"),Error("请先登录后再进行此操作");console.log("✅ 用户已登录，可以执行操作")}console.log("\uD83D\uDD0D 检查CloudBase初始化状态...");let a=await r();if(!a)throw console.error("❌ CloudBase未初始化"),Error("CloudBase未初始化");console.log("\uD83D\uDD10 检查用户登录状态..."),n||(n=a.auth()),console.log("✅ CloudBase已初始化，准备调用云函数");let s=null,i=null;try{let e=localStorage.getItem("pet_platform_user");if(e){let t=JSON.parse(e);s=t._id,i=t._id,console.log("✅ 使用注册用户ID:",s)}else console.log("❌ 未找到注册用户信息")}catch(e){console.error("获取注册用户ID失败:",e)}let l=!["getUserInfo","getUserPosts","getUserBookmarks","getUserFollowing"].includes(t),g={name:e,data:{action:t,data:l?{...o,userId:s,openId:i}:{...o,currentUserId:s,openId:i}}};console.log("\uD83D\uDCCB 云函数调用参数:",g),console.log("\uD83D\uDCE1 正在调用云函数...");let u=await a.callFunction(g);if(console.log("✅ ".concat(e,".").concat(t," 调用成功:"),u),u.result)return console.log("\uD83D\uDCE6 返回result字段:",u.result),u.result;return console.log("\uD83D\uDCE6 返回完整结果:",u),u}catch(o){var a;console.error("❌ ".concat(e,".").concat(t," 调用失败:"),o),console.error("❌ 错误类型:",typeof o),console.error("❌ 错误构造函数:",null==o?void 0:null===(a=o.constructor)||void 0===a?void 0:a.name),(null==o?void 0:o.message)&&console.error("❌ 错误消息: ".concat(o.message)),(null==o?void 0:o.code)&&console.error("❌ 错误代码: ".concat(o.code)),(null==o?void 0:o.name)&&console.error("❌ 错误名称: ".concat(o.name)),(null==o?void 0:o.stack)&&console.error("❌ 错误堆栈:",o.stack);try{console.error("❌ 错误对象JSON:",JSON.stringify(o,null,2))}catch(e){console.error("❌ 无法序列化错误对象:",e),console.error("❌ 错误对象属性:",Object.keys(o)),console.error("❌ 错误对象值:",Object.values(o))}throw(null==o?void 0:o.response)&&console.error("❌ 响应错误:",o.response),(null==o?void 0:o.request)&&console.error("❌ 请求错误:",o.request),o}},u={async getPosts(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getPosts",e)},async getOptimizedPosts(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("optimizedPostQuery","query",e)},getPostDetail:async e=>g("pet-api","getPostDetail",e),createPost:async e=>g("pet-api","createPost",e),likePost:async e=>g("pet-api","likePost",e),toggleLike:async e=>g("pet-api","toggleLike",e),addLike:async e=>g("pet-api","addLike",e),toggleDislike:async e=>g("pet-api","toggleDislike",e),addDislike:async e=>g("pet-api","addDislike",e),toggleBookmark:async e=>g("pet-api","toggleBookmark",e),bookmarkPost:async e=>g("pet-api","bookmarkPost",e),exchangeContact:async e=>g("pet-api","exchangeContact",e),ratePost:async e=>g("pet-api","ratePost",e),toggleFollow:async e=>g("pet-api","toggleFollow",e),getCategories:async()=>g("pet-api","getCategories"),ratePet:async e=>g("pet-api","ratePet",e),reportPost:async e=>g("pet-api","reportPost",e),async getUserBookmarks(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getUserBookmarks",e)},async getUserPosts(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getUserPosts",e)},async getUserFollowing(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getUserFollowing",e)},async getUserFollowers(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getUserFollowers",e)},async getUserNotifications(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getUserNotifications",e)},markNotificationRead:async e=>g("pet-api","markNotificationRead",e),deleteNotification:async e=>g("pet-api","deleteNotification",e),bulkDeleteNotifications:async e=>g("pet-api","bulkDeleteNotifications",e),updateProfile:async e=>g("pet-api","updateProfile",e),sendContactNotification:async e=>g("pet-api","sendContactNotification",e),async getReports(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getReports",e)},handleReport:async e=>g("pet-api","handleReport",e),banUser:async e=>g("pet-api","banUser",e),getUserInfo:async e=>g("pet-api","getUserInfo",e),uploadToStatic:async e=>g("pet-api","uploadToStatic",e),updateAvatar:async e=>g("pet-api","updateAvatar",e),getImage:async e=>g("pet-api","getImage",e),blockUser:async e=>g("pet-api","blockUser",e),unblockUser:async e=>g("pet-api","unblockUser",e),unblockUser:async e=>g("pet-api","unblockUser",e),async getBlockedUsers(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getBlockedUsers",e)},reportUser:async e=>g("pet-api","reportUser",e),submitAppeal:async e=>g("pet-api","submitAppeal",e),getUserPermissions:async e=>g("pet-api","getUserPermissions",e),async getAppeals(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getAppeals",e)},handleAppeal:async e=>g("pet-api","handleAppeal",e),adminLogin:async e=>g("pet-api","adminLogin",e),async getAdmins(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getAdmins",e)},createAdmin:async e=>g("pet-api","createAdmin",e),updateAdmin:async e=>g("pet-api","updateAdmin",e),deleteAdmin:async e=>g("pet-api","deleteAdmin",e),getAds:async e=>g("pet-api","getAds",e||{}),getAdPositions:async e=>g("pet-api","getAdPositions",e||{}),createAd:async e=>g("pet-api","createAd",e),updateAd:async e=>g("pet-api","updateAd",e),deleteAd:async e=>g("pet-api","deleteAd",e),getAdStatistics:async e=>g("pet-api","getAdStatistics",e||{}),updatePostPriority:async e=>g("pet-api","updatePostPriority",e),getPostQualityScore:async e=>g("pet-api","getPostQualityScore",e),batchUpdatePostPriority:async e=>g("pet-api","batchUpdatePostPriority",e),getPostsByPriority:async e=>g("pet-api","getPostsByPriority",e||{}),batchUpdateAllPostsQuality:async()=>g("pet-api","batchUpdateAllPostsQuality",{}),createActivity:async e=>g("pet-api","createActivity",e),getActivities:async e=>g("pet-api","getActivities",e||{}),getActivityDetail:async e=>g("pet-api","getActivityDetail",e),updateActivity:async e=>g("pet-api","updateActivity",e),deleteActivity:async e=>g("pet-api","deleteActivity",e),voteInActivity:async e=>g("pet-api","voteInActivity",e),addActivityComment:async e=>g("pet-api","addActivityComment",e),getActivityComments:async e=>g("pet-api","getActivityComments",e),getSystemConfig:async()=>g("pet-api","getSystemConfig",{}),updateSystemConfig:async e=>g("pet-api","updateSystemConfig",e),getPermissions:async()=>g("pet-api","getPermissions",{}),getUserRatedPosts:async e=>g("pet-api","getUserRatedPosts",e),updatePostStatus:async e=>g("pet-api","updatePostStatus",e),deletePost:async e=>g("pet-api","deleteMyPost",e),adminDeletePost:async e=>g("pet-api","adminDeletePost",e),deleteCloudFile:async e=>g("pet-api","deleteCloudFile",e),async getPostsForAdmin(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getPostsForAdmin",e)}},d={getActiveActivities:async()=>g("pet-api","getActiveActivities"),async getActivities(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getActivities",e)},getSystemConfig:async()=>g("pet-api","getSystemConfig"),getDashboardStats:async()=>g("pet-api","getDashboardStats")},y={async sendVerificationCode(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"register";return g("email-auth","sendVerificationCode",{email:e,type:t})},async verifyCode(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"register";return g("email-auth","verifyCode",{email:e,code:t,type:o})},registerWithEmail:async(e,t,o,a)=>g("email-auth","registerWithEmail",{email:e,password:t,nickname:o,verificationCode:a}),loginWithEmail:async(e,t)=>g("email-auth","loginWithEmail",{email:e,password:t}),resetPassword:async(e,t,o)=>g("email-auth","resetPassword",{email:e,verificationCode:t,newPassword:o}),changePassword:async(e,t,o,a)=>g("email-auth","changePassword",{email:e,verificationCode:t,oldPassword:o,newPassword:a}),getCurrentUser:async e=>g("user-auth","getCurrentUser",e?{email:e}:{}),logout:async()=>g("user-auth","logout")},m=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:800,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.8;return new Promise(a=>{let s=document.createElement("canvas"),n=s.getContext("2d"),i=new Image;i.onload=()=>{let{width:l,height:r}=i,c=l,p=r,g=o;l>2e3||r>2e3?(p=r*(c=Math.min(l,t))/l,g=.8):l>t?(p=r*t/l,c=t,g=o):g=Math.max(.9,o),s.width=c,s.height=p,null==n||n.drawImage(i,0,0,c,p),s.toBlob(t=>{if(t){let o=new File([t],e.name,{type:"image/jpeg",lastModified:Date.now()}),s=Math.round((1-o.size/e.size)*100);console.log("图片压缩完成: ".concat(e.size," -> ").concat(o.size," (").concat(s,"% 减少)")),console.log("压缩参数: ".concat(l,"x").concat(r," -> ").concat(c,"x").concat(p,", 质量: ").concat(Math.round(100*g),"%")),a(o)}else a(e)},"image/jpeg",g)},i.src=URL.createObjectURL(e)})},h=async e=>{try{var t,o,a,s;let n=await r();if(!n)throw Error("CloudBase未初始化");let i=await n.callFunction({name:"pet-api",data:{action:"getImage",data:{fileId:e}}});if((null===(t=i.result)||void 0===t?void 0:t.success)&&(null===(a=i.result)||void 0===a?void 0:null===(o=a.data)||void 0===o?void 0:o.url))return i.result.data.url;return console.error("获取图片URL失败:",null===(s=i.result)||void 0===s?void 0:s.message),"/placeholder-image.png"}catch(e){return console.error("获取图片URL失败:",e),"/placeholder-image.png"}},v=async e=>{try{var t,o,a,s;console.log("开始上传文件到静态托管:",e.name,"原始大小:",e.size);let n=e;e.type.startsWith("image/")&&(n=await m(e));let i=Date.now(),l=Math.random().toString(36).substring(2),c=e.name.split(".").pop(),p="".concat(i,"_").concat(l,".").concat(c);console.log("压缩后大小:",n.size);let g=await P(n),u=await r();if(!u)throw Error("CloudBase未初始化");let d=await u.callFunction({name:"pet-api",data:{action:"uploadToStatic",data:{fileName:p,fileData:g,contentType:n.type}}});if((null===(t=d.result)||void 0===t?void 0:t.success)&&(null===(a=d.result)||void 0===a?void 0:null===(o=a.data)||void 0===o?void 0:o.url))return console.log("文件上传成功，URL:",d.result.data.url),d.result.data.url;throw Error((null===(s=d.result)||void 0===s?void 0:s.message)||"上传失败")}catch(e){throw console.error("文件上传失败:",e),e}},P=e=>new Promise((t,o)=>{let a=new FileReader;a.readAsDataURL(e),a.onload=()=>{t(a.result.split(",")[1])},a.onerror=e=>o(e)}),f=async e=>await v(e);t.default={petAPI:u,authAPI:y,activityAPI:d,uploadFile:f}}}]);