"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[734],{9356:function(e,t,r){r.d(t,{C:function(){return g},ToastProvider:function(){return f}});var s=r(57437);r(2265);var a=r(69064),o=r(65302),l=r(45131),n=r(22252),c=r(33245),i=r(32489),u=r(68661);let d={duration:4e3,position:"top-center",style:{background:"#fff",color:"#374151",border:"1px solid #e5e7eb",borderRadius:"8px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",padding:"12px 16px",fontSize:"14px",maxWidth:"400px"}},m=e=>{let{message:t,type:r,onDismiss:a}=e,d={success:(0,s.jsx)(o.Z,{className:"h-5 w-5 text-green-500"}),error:(0,s.jsx)(l.Z,{className:"h-5 w-5 text-red-500"}),warning:(0,s.jsx)(n.Z,{className:"h-5 w-5 text-yellow-500"}),info:(0,s.jsx)(c.Z,{className:"h-5 w-5 text-blue-500"})};return(0,s.jsxs)("div",{className:(0,u.cn)("flex items-center space-x-3 p-3 rounded-lg border shadow-lg",{success:"bg-green-50 border-green-200",error:"bg-red-50 border-red-200",warning:"bg-yellow-50 border-yellow-200",info:"bg-blue-50 border-blue-200"}[r]),children:[d[r],(0,s.jsx)("span",{className:"flex-1 text-sm font-medium",children:t}),(0,s.jsx)("button",{onClick:a,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,s.jsx)(i.Z,{className:"h-4 w-4"})})]})},g={success:e=>{a.Am.custom(t=>(0,s.jsx)(m,{message:e,type:"success",onDismiss:()=>a.Am.dismiss(t.id)}),d)},error:e=>{a.Am.custom(t=>(0,s.jsx)(m,{message:e,type:"error",onDismiss:()=>a.Am.dismiss(t.id)}),{...d,duration:6e3})},warning:e=>{a.Am.custom(t=>(0,s.jsx)(m,{message:e,type:"warning",onDismiss:()=>a.Am.dismiss(t.id)}),d)},info:e=>{a.Am.custom(t=>(0,s.jsx)(m,{message:e,type:"info",onDismiss:()=>a.Am.dismiss(t.id)}),d)},loading:e=>a.Am.loading(e,{style:d.style,position:d.position}),dismiss:e=>{a.Am.dismiss(e)},promise:(e,t)=>a.Am.promise(e,t,{style:d.style,position:d.position})},f=()=>(0,s.jsx)(a.x7,{position:"top-center",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"transparent",boxShadow:"none",padding:0}}})},98734:function(e,t,r){r.d(t,{a:function(){return l}});var s=r(2265),a=r(98011),o=r(9356);let l=()=>{let[e,t]=(0,s.useState)(null),[r,l]=(0,s.useState)(!0),[n,c]=(0,s.useState)(!1),i=(0,s.useCallback)(e=>{try{e?(localStorage.setItem("pet_platform_user",JSON.stringify(e)),localStorage.setItem("pet_platform_logged_in","true")):(localStorage.removeItem("pet_platform_user"),localStorage.removeItem("pet_platform_logged_in"))}catch(e){console.error("保存用户状态失败:",e)}},[]),u=(0,s.useCallback)(()=>{try{let e=localStorage.getItem("pet_platform_user"),r=localStorage.getItem("pet_platform_logged_in");if(e&&"true"===r){let r=JSON.parse(e);return t(r),c(!0),r}}catch(e){console.error("恢复用户状态失败:",e)}return null},[]),d=(0,s.useCallback)(async()=>{try{l(!0);let e=u();if(e)try{if(e.email){let r=await a.authAPI.getCurrentUser(e.email);r.success&&r.data?(t(r.data),c(!0),i(r.data)):(t(null),c(!1),i(null))}else console.log("使用本地保存的用户信息（匿名用户）"),t(e),c(!0)}catch(r){console.error("验证用户状态失败:",r),e?(console.log("验证失败，使用本地用户信息"),t(e),c(!0)):(t(null),c(!1),i(null))}else{console.log("没有保存的用户信息，尝试匿名登录");try{let e=await a.authAPI.getCurrentUser();e.success&&e.data?(t(e.data),c(!0),i(e.data)):(t(null),c(!1))}catch(e){console.error("匿名登录失败:",e),t(null),c(!1)}}}catch(e){console.error("检查登录状态失败:",e),t(null),c(!1)}finally{l(!1)}},[u,i]),m=(0,s.useCallback)(async(e,r)=>{try{if(l(!0),!e||!r)return o.C.error("请输入邮箱和密码"),!1;let s=await a.authAPI.loginWithEmail(e,r);if(s.success)return t(s.data),c(!0),i(s.data),s.data.isNewUser?o.C.success("欢迎加入宠物交易平台！"):o.C.success("登录成功！"),!0;return o.C.error(s.message||"登录失败"),!1}catch(e){return console.error("登录失败:",e),o.C.error(e.message||"登录失败，请重试"),!1}finally{l(!1)}},[i]),g=(0,s.useCallback)(async()=>{try{return l(!0),await a.authAPI.logout(),t(null),c(!1),i(null),o.C.success("已退出登录"),!0}catch(e){return console.error("退出登录失败:",e),o.C.error("退出登录失败"),!1}finally{l(!1)}},[i]),f=(0,s.useCallback)(async r=>{if(!e)return!1;try{l(!0),console.log("更新用户资料:",r);let s={...e,...r};return t(s),i(s),o.C.success("资料更新成功"),!0}catch(e){return console.error("更新资料失败:",e),o.C.error("更新失败"),!1}finally{l(!1)}},[e,i]),p=(0,s.useCallback)(async()=>{if(n)try{let e=await a.authAPI.getCurrentUser();e.success&&e.data&&(t(e.data),i(e.data))}catch(e){console.error("刷新用户信息失败:",e)}},[n,i]),x=(0,s.useCallback)(async()=>{try{let e=u(),r=await a.authAPI.getCurrentUser(null==e?void 0:e.email);r.success&&r.data?(t(r.data),c(!0),i(r.data)):(t(null),c(!1),i(null))}catch(e){console.error("刷新登录状态失败:",e),t(null),c(!1),i(null)}},[i,u]);return(0,s.useEffect)(()=>{(async()=>{let e=u();if(e){t(e),c(!0),l(!1);try{console.log("后台验证用户状态...");let r=await a.authAPI.getCurrentUser(e.email);r.success&&r.data?(t(r.data),i(r.data),console.log("用户状态验证成功")):console.warn("云端验证失败，但保持本地状态")}catch(e){console.error("后台验证失败:",e)}}else await d()})()},[d,u,i]),(0,s.useEffect)(()=>{},[]),{user:e,isLoading:r,isLoggedIn:n,login:m,logout:g,updateProfile:f,refreshUser:p,refreshLoginState:x}}},68661:function(e,t,r){r.d(t,{cn:function(){return o},uf:function(){return l},vV:function(){return n}});var s=r(61994),a=r(53335);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,s.W)(t))}function l(e){return e<1e3?e.toString():e<1e4?"".concat((e/1e3).toFixed(1),"k"):e<1e5?"".concat((e/1e4).toFixed(1),"w"):"".concat(Math.floor(e/1e4),"w")}function n(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}}}]);