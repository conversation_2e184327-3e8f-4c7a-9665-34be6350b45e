(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931,882],{88342:function(e,t,s){Promise.resolve().then(s.bind(s,55982))},55982:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return A}});var r=s(57437),a=s(2265),i=s(86595),l=s(91723),n=s(88997),o=s(70525),c=s(40875),d=s(47465),u=s(32489),h=s(18930),m=s(98011),g=s(59604),x=s(68661),p=s(60719),y=e=>{let{selectedCategory:t,onCategoryChange:s}=e,[i,n]=(0,a.useState)([]),[o,d]=(0,a.useState)(!0),[y,f]=(0,a.useState)(!1),[v,b]=(0,a.useState)([]);(0,a.useEffect)(()=>{(async()=>{try{let e=await m.petAPI.getCategories();e.success&&n(e.data)}catch(e){console.error("获取分类失败:",e)}finally{d(!1)}})(),b(g.Fd.getHistory())},[]);let j=(e,t)=>{s(e),e&&t&&(g.Fd.addItem(e,t),b(g.Fd.getHistory()))},w=(e,t)=>{s(e),f(!1),g.Fd.addItem(e,t),b(g.Fd.getHistory())},N=(e,t)=>{t.stopPropagation(),g.Fd.removeItem(e),b(g.Fd.getHistory())},C=()=>{g.Fd.clearHistory(),b([]),f(!1)},S=(0,p.O)(()=>{f(!1)});if(o)return(0,r.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsx)("div",{className:"animate-pulse",children:(0,r.jsx)("div",{className:"flex space-x-4",children:[1,2,3,4,5,6,7,8,9].map(e=>(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16"},e))})})})});let k=i.filter(e=>1===e.level).sort((e,t)=>e.order-t.order).map(e=>({...e,children:i.filter(t=>2===t.level&&t.parent_id===e.id).sort((e,t)=>e.order-t.order)}));return(0,r.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"md:hidden py-4",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("select",{value:t,onChange:e=>{let t=i.find(t=>t.id===e.target.value);j(e.target.value,null==t?void 0:t.name)},className:"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500",children:[(0,r.jsx)("option",{value:"",children:"全部分类"}),k.map(e=>(0,r.jsxs)("optgroup",{label:e.name,children:[(0,r.jsx)("option",{value:e.id,children:e.name}),e.children.map(t=>(0,r.jsxs)("option",{value:t.id,children:[e.name," > ",t.name]},t.id))]},e.id))]}),v.length>0&&(0,r.jsx)("button",{onClick:()=>f(!y),className:"absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600",children:(0,r.jsx)(l.Z,{className:"h-4 w-4"})}),y&&v.length>0&&(0,r.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-40 overflow-y-auto",children:(0,r.jsxs)("div",{className:"p-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"分类历史"}),(0,r.jsx)("button",{onClick:C,className:"text-xs text-gray-400 hover:text-gray-600",children:"清空"})]}),v.map((e,t)=>(0,r.jsxs)("button",{onClick:()=>w(e.value,e.label),className:"w-full px-2 py-1 text-left text-sm hover:bg-gray-50 rounded flex items-center justify-between group",children:[(0,r.jsx)("span",{children:e.label}),(0,r.jsx)("button",{onClick:t=>N(e.value,t),className:"opacity-0 group-hover:opacity-100 text-gray-400 hover:text-gray-600",children:(0,r.jsx)(u.Z,{className:"h-3 w-3"})})]},t))]})})]})}),(0,r.jsx)("div",{className:"hidden md:block py-4",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-2 items-center",children:[(0,r.jsx)("button",{onClick:()=>j(""),className:(0,x.cn)("px-4 py-2 rounded-lg text-sm font-medium transition-colors",""===t?"bg-primary-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"全部分类"}),k.map(e=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsxs)("button",{onClick:()=>j(e.id,e.name),className:(0,x.cn)("flex items-center space-x-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors",t===e.id?"bg-primary-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:[(0,r.jsx)("span",{children:e.name}),e.children.length>0&&(0,r.jsx)(c.Z,{className:"h-3 w-3"})]}),e.children.length>0&&(0,r.jsx)("div",{className:"absolute top-full left-0 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10",children:(0,r.jsx)("div",{className:"p-2",children:e.children.map(e=>(0,r.jsx)("button",{onClick:()=>j(e.id,e.name),className:(0,x.cn)("block w-full text-left px-3 py-2 text-sm rounded-md transition-colors",t===e.id?"bg-primary-50 text-primary-700":"text-gray-700 hover:bg-gray-100"),children:e.name},e.id))})})]},e.id)),v.length>0&&(0,r.jsxs)("div",{className:"relative",ref:S,children:[(0,r.jsxs)("button",{onClick:()=>f(!y),className:(0,x.cn)("px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1",y?"bg-blue-100 text-blue-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:[(0,r.jsx)(l.Z,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:"历史"})]}),y&&(0,r.jsx)("div",{className:"absolute top-full right-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto",children:(0,r.jsxs)("div",{className:"p-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("span",{className:"text-xs text-gray-500 font-medium",children:"分类历史"}),(0,r.jsxs)("button",{onClick:C,className:"text-xs text-gray-400 hover:text-gray-600 flex items-center space-x-1",children:[(0,r.jsx)(h.Z,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:"清空"})]})]}),v.map((e,t)=>(0,r.jsxs)("button",{onClick:()=>w(e.value,e.label),className:"w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none text-sm rounded-md group flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-gray-700",children:e.label}),(0,r.jsx)("button",{onClick:t=>N(e.value,t),className:"opacity-0 group-hover:opacity-100 text-gray-400 hover:text-gray-600 transition-opacity p-1",children:(0,r.jsx)(u.Z,{className:"h-3 w-3"})})]},t))]})})]})]})})]})})},f=s(28819),v=s(89841),b=s(31215),j=s(77165),w=s(96502);function N(e){let{positionId:t,className:s="",fallbackContent:i}=e,[l,n]=(0,a.useState)([]),[o,c]=(0,a.useState)(null),[d,u]=(0,a.useState)(0),[h,m]=(0,a.useState)(!0),[g,x]=(0,a.useState)(!1),[p,y]=(0,a.useState)(!0);(0,a.useEffect)(()=>{f()},[t]),(0,a.useEffect)(()=>{if(l.length>1&&(null==o?void 0:o.rotation_interval)&&o.rotation_interval>0){let e=setInterval(()=>{u(e=>(e+1)%l.length)},o.rotation_interval);return()=>clearInterval(e)}},[l,o]);let f=async()=>{try{y(!0);let e=v(t);c(e.position),n(e.ads||[])}catch(e){console.error("加载广告失败:",e)}finally{setTimeout(()=>y(!1),500)}},v=e=>({home_banner:{position:{position_id:"home_banner",name:"首页横幅广告",page:"home",location:"top",width:728,height:90,ad_type:"banner",max_ads:3,rotation_interval:5e3,status:"active"},ads:[{_id:"banner_ad_1",title:"优质宠物用品推荐",content:"为您的爱宠提供最好的生活用品，健康快乐每一天！",image_url:"",target_url:"#",ad_type:"banner",priority:1}]},home_feed:{position:{position_id:"home_feed",name:"首页信息流广告",page:"home",location:"feed",width:300,height:200,ad_type:"feed",max_ads:5,rotation_interval:0,status:"active"},ads:[{_id:"feed_ad_1",title:"专业宠物医院",content:"24小时宠物医疗服务，专业医师团队，让您的爱宠健康无忧。",image_url:"",target_url:"#",ad_type:"feed",priority:1},{_id:"feed_ad_2",title:"宠物美容服务",content:"专业宠物美容，让您的爱宠更加美丽动人。",image_url:"",target_url:"#",ad_type:"feed",priority:2}]}})[e]||{position:null,ads:[]},b=e=>{N(e._id),e.target_url&&window.open(e.target_url,"_blank")},N=async e=>{try{console.log("广告点击记录:",{ad_id:e,timestamp:new Date})}catch(e){console.error("记录广告点击失败:",e)}},C=()=>{x(!0),localStorage.setItem("ad_hidden_".concat(t),Date.now().toString())};if(p)return(0,r.jsx)("div",{className:"animate-pulse ".concat(s),children:(0,r.jsx)("div",{className:"bg-gray-200 rounded-lg h-20"})});if(!(()=>{if(!o||"active"!==o.status||!l||0===l.length||g)return!1;let e=localStorage.getItem("ad_hidden_".concat(t));if(e){let t=parseInt(e);if((Date.now()-t)/36e5<24)return!1}return!0})())return i?(0,r.jsx)("div",{className:s,children:i}):null;let S=l[d];return(0,r.jsx)("div",{className:s,children:(()=>{switch(null==o?void 0:o.ad_type){case"banner":return(0,r.jsxs)("div",{className:"relative bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg overflow-hidden",children:[(0,r.jsx)("button",{onClick:C,className:"absolute top-2 right-2 z-10 bg-white/80 hover:bg-white rounded-full p-1 transition-colors",title:"隐藏广告",children:(0,r.jsx)(j.Z,{className:"h-4 w-4 text-gray-600"})}),(0,r.jsxs)("div",{className:"flex items-center p-4 cursor-pointer hover:bg-black/5 transition-colors",onClick:()=>b(S),children:[S.image_url&&(0,r.jsx)("img",{src:S.image_url,alt:S.title,className:"w-16 h-16 rounded-lg object-cover mr-4"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:S.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2",children:S.content})]}),(0,r.jsx)("div",{className:"ml-4",children:(0,r.jsx)("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:"广告"})})]})]});case"feed":return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border-b border-gray-100",children:[(0,r.jsx)("span",{className:"text-xs text-gray-500 bg-yellow-100 text-yellow-800 px-2 py-1 rounded",children:"推广内容"}),(0,r.jsx)("button",{onClick:C,className:"text-gray-400 hover:text-gray-600 transition-colors",title:"隐藏此广告",children:(0,r.jsx)(w.Z,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"p-4 cursor-pointer hover:bg-gray-50 transition-colors",onClick:()=>b(S),children:[S.image_url&&(0,r.jsx)("img",{src:S.image_url,alt:S.title,className:"w-full h-40 object-cover rounded-lg mb-3"}),(0,r.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:S.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-3 line-clamp-3",children:S.content}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"点击了解更多"}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:l.length>1&&(0,r.jsx)("div",{className:"flex space-x-1",children:l.map((e,t)=>(0,r.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(t===d?"bg-blue-500":"bg-gray-300")},t))})})]})]})]});case"popup":return(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg max-w-sm w-full overflow-hidden",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("button",{onClick:C,className:"absolute top-2 right-2 z-10 bg-black/20 hover:bg-black/40 text-white rounded-full p-1 transition-colors",children:(0,r.jsx)(j.Z,{className:"h-5 w-5"})}),S.image_url&&(0,r.jsx)("img",{src:S.image_url,alt:S.title,className:"w-full h-48 object-cover"})]}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)("h3",{className:"font-bold text-lg text-gray-900 mb-2",children:S.title}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:S.content}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>b(S),className:"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors",children:"了解更多"}),(0,r.jsx)("button",{onClick:C,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"跳过"})]})]})]})});default:return null}})()})}var C=s(9356),S=s(80240),k=s(33882);class _{static getInstance(){return _.instance||(_.instance=new _),_.instance}async optimizedPostQuery(e){let t=Date.now(),r="posts_".concat(JSON.stringify(e));if(!1!==e.useCache){let e=k.dataCache.get(r);if(e)return this.recordQueryStats("optimizedPostQuery",Date.now()-t),e}try{let a=this.buildOptimizedQuery(e),{petAPI:i}=await Promise.resolve().then(s.bind(s,98011)),l=await i.getOptimizedPosts(a);return l.success&&!1!==e.useCache&&k.dataCache.set(r,l,12e4),this.recordQueryStats("optimizedPostQuery",Date.now()-t),l}catch(e){throw console.error("查询优化失败:",e),e}}buildOptimizedQuery(e){let t={action:"query",page:e.page||1,limit:Math.min(e.limit||10,20),includeUserInfo:e.includeUserInfo||!1,includeCategoryInfo:e.includeCategoryInfo||!1};return e.category&&(t.category=e.category),e.type&&(t.type=e.type),e.location&&(t.location=e.location),e.userId&&(t.userId=e.userId),e.sortBy&&(t.sortBy=e.sortBy),t}async batchQuery(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5,s=[];for(let r=0;r<e.length;r+=t){let a=e.slice(r,r+t);(await Promise.allSettled(a.map(e=>e()))).forEach(e=>{"fulfilled"===e.status?s.push(e.value):console.error("批量查询失败:",e.reason)})}return s}async preloadRelatedData(e){if(!e.length)return;let t=Array.from(new Set(e.map(e=>e.user_id||e.author_id).filter(Boolean))),s=Array.from(new Set(e.map(e=>e.category).filter(Boolean)));t.length>0&&this.preloadUsers(t),s.length>0&&this.preloadCategories(s)}async preloadUsers(e){e.filter(e=>!k.dataCache.get("user_".concat(e))).length}async preloadCategories(e){e.filter(e=>!k.dataCache.get("category_".concat(e))).length}recordQueryStats(e,t){let s=this.queryStats.get(e)||{count:0,avgTime:0};s.count++,s.avgTime=(s.avgTime*(s.count-1)+t)/s.count,this.queryStats.set(e,s)}getQueryStats(){return new Map(this.queryStats)}clearStats(){this.queryStats.clear()}constructor(){this.queryStats=new Map}}let I=_.getInstance();class T{generateKey(e,t){let{limit:s,...r}=e;return JSON.stringify({...r,page:t,limit:s})}get(e,t){let s=this.generateKey(e,t),r=this.cache.get(s);return r&&Date.now()-r.timestamp<this.TTL?r:null}set(e,t,s,r,a){let i=this.generateKey(e,t);this.cache.set(i,{data:s,timestamp:Date.now(),hasMore:r,totalCount:a})}clear(){this.cache.clear(),console.log("帖子缓存已清理")}clearByFilters(e){let t=[];this.cache.forEach((s,r)=>{try{let s=JSON.parse(r),a=!1;if(0===Object.keys(e).length)a=!0;else for(let[t,r]of Object.entries(e))if(s[t]===r){a=!0;break}a&&t.push(r)}catch(e){}}),t.forEach(e=>this.cache.delete(e)),console.log("清理了 ".concat(t.length," 个缓存项"))}async preloadNextPage(e,t){let s=t+1,r=this.generateKey(e,s);if(!this.cache.get(r))try{let t=await I.optimizedPostQuery({page:s,limit:e.limit,sortBy:e.sortBy,category:e.category||void 0,includeUserInfo:!0,includeCategoryInfo:!0,useCache:!0});if(t.success&&t.data){var a,i;let r=Array.isArray(t.data)?t.data:t.data.posts||[],l=(null===(a=t.pagination)||void 0===a?void 0:a.hasMore)||!1,n=(null===(i=t.pagination)||void 0===i?void 0:i.total)||r.length;this.set(e,s,r,l,n),console.log("预加载第".concat(s,"页成功，共").concat(r.length,"条数据"))}}catch(e){console.warn("预加载下一页失败:",e)}}constructor(){this.cache=new Map,this.TTL=3e4}}let E=new T,O=e=>{let{filters:t,enabled:s=!0,debounceMs:r=300}=e,[i,l]=(0,a.useState)([]),[n,o]=(0,a.useState)(!1),[c,d]=(0,a.useState)(!1),[u,h]=(0,a.useState)(null),[m,g]=(0,a.useState)(!0),[x,p]=(0,a.useState)(0),y=(0,a.useRef)(null),f=(0,a.useRef)(null),v=(0,a.useRef)(t),b=(0,a.useCallback)((e,t)=>{if(!t.trim())return e;let s=t.toLowerCase().trim();return e.filter(e=>!!(e.breed&&e.breed.toLowerCase().includes(s)||e.title&&e.title.toLowerCase().includes(s)||e.tags&&e.tags.some(e=>e.toLowerCase().includes(s))))},[]),j=(0,a.useCallback)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=arguments.length>2?arguments[2]:void 0;try{let i;let n=t?e.page:1;if(!t){let t=E.get(e,n);if(t){l(t.data),g(t.hasMore),p(t.totalCount),h(null);return}}t?d(!0):o(!0);let c=Date.now();i=await I.optimizedPostQuery({page:n,limit:e.limit,sortBy:e.sortBy,category:e.category||void 0,type:"all"!==e.petType?e.petType:void 0,location:e.location||void 0,includeUserInfo:!0,includeCategoryInfo:!0,useCache:!0});let u=Date.now()-c;if(void 0!==S.Bm&&S.Bm.recordApiResponse("optimizedPostQuery",u),null==s?void 0:s.aborted)return;if(i.success){var r,a;let s=i.data||[];e.breed&&(s=b(s,e.breed)),t?l(e=>[...e,...s]):l(s);let o=(null===(r=i.pagination)||void 0===r?void 0:r.hasMore)||!1,c=(null===(a=i.pagination)||void 0===a?void 0:a.total)||s.length;g(o),p(c),h(null),!t&&(E.set(e,n,s,o,c),o&&1===n&&setTimeout(()=>{E.preloadNextPage(e,n)},1e3))}else throw Error(i.message||"获取数据失败")}catch(e){if(null==s?void 0:s.aborted)return;console.error("获取宠物列表失败:",e),h(e.message||"获取数据失败"),t||C.C.error("获取宠物列表失败，请重试")}finally{(null==s?void 0:s.aborted)||(o(!1),d(!1))}},[b]),w=(0,a.useCallback)(function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(y.current&&y.current.abort(),f.current&&clearTimeout(f.current),t){let t=new AbortController;y.current=t,j(e,!0,t.signal);return}f.current=setTimeout(()=>{let t=new AbortController;y.current=t,j(e,!1,t.signal)},r)},[j,r]),N=(0,a.useCallback)(()=>{if(!c&&m&&s){let e={...v.current,page:v.current.page+1};v.current=e,w(e,!0)}},[c,m,s,w]),k=(0,a.useCallback)(()=>{if(E.clear(),l([]),h(null),g(!0),p(0),s){let e={...t,page:1};v.current=e,w(e)}},[t,s,w]);return(0,a.useEffect)(()=>{if(s)return(v.current.category!==t.category||v.current.petType!==t.petType||v.current.location!==t.location||v.current.breed!==t.breed||v.current.sortBy!==t.sortBy||1===t.page)&&(l([]),h(null),g(!0),p(0)),v.current=t,w(t),()=>{f.current&&clearTimeout(f.current)}},[t,s,w]),(0,a.useEffect)(()=>()=>{y.current&&y.current.abort(),f.current&&clearTimeout(f.current)},[]),{posts:i,loading:n,loadingMore:c,error:u,hasMore:m,loadMore:N,refresh:k,totalCount:x}};var L=e=>{let{filters:t}=e,{posts:s,loading:i,loadingMore:l,error:n,hasMore:o,loadMore:c,refresh:d,totalCount:u}=O({filters:t}),{ref:h,inView:m}=(0,f.YD)({threshold:0,rootMargin:"100px"});return((0,a.useEffect)(()=>{m&&o&&!i&&!l&&c()},[m,o,i,l,c]),n&&0===s.length)?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsxs)("div",{className:"text-gray-500 mb-4",children:[(0,r.jsx)("p",{className:"text-lg font-medium",children:"加载失败"}),(0,r.jsx)("p",{className:"text-sm",children:n})]}),(0,r.jsx)("button",{onClick:()=>{d()},className:"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors",children:"重试"})]}):i&&0===s.length?(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:Array.from({length:20}).map((e,t)=>(0,r.jsx)(b.gG,{},t))}):i||0!==s.length?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:(()=>{let e=[];for(let t=0;t<s.length;t++)e.push((0,r.jsx)(v.Z,{post:s[t]},s[t]._id)),(t+1)%6==0&&t<s.length-1&&e.push((0,r.jsx)(N,{positionId:"home_feed",className:"col-span-2 md:col-span-1"},"ad-".concat(t)));return e})()}),o&&(0,r.jsx)("div",{ref:h,className:"flex justify-center py-8",children:l?(0,r.jsx)(b.gb,{size:"sm",text:"加载更多..."}):(0,r.jsx)("div",{className:"text-gray-400 text-sm",children:"滑动到底部加载更多"})}),!o&&s.length>0&&(0,r.jsx)("div",{className:"text-center py-8 text-gray-400 text-sm",children:"已显示全部内容"})]}):(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsxs)("div",{className:"text-gray-500",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDC3E"}),(0,r.jsx)("p",{className:"text-lg font-medium mb-2",children:"暂无宠物信息"}),(0,r.jsx)("p",{className:"text-sm",children:t.category?"该分类下暂无宠物，试试其他分类吧":"还没有人发布宠物，快来成为第一个吧！"})]})})},P=s(56334),D=s(83774);let B="location_filter_history";var R=e=>{let{selectedLocation:t,onLocationChange:s}=e,[i,l]=(0,a.useState)(t),[n,o]=(0,a.useState)([]),[c,d]=(0,a.useState)(!1),u=()=>{try{let e=localStorage.getItem(B);return e?JSON.parse(e):[]}catch(e){return[]}},h=e=>{try{localStorage.setItem(B,JSON.stringify(e))}catch(e){}},m=e=>{if(!e.trim())return;let t=[e,...u().filter(t=>t!==e)].slice(0,10);o(t),h(t)};(0,a.useEffect)(()=>{o(u())},[]);let g=e=>{l(e),s(e),d(!1)},x=()=>{i&&i.trim()&&m(i.trim())},y=(0,p.O)(()=>{d(!1)});return(0,a.useEffect)(()=>{l(t)},[t]),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("div",{className:"flex-1 relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(D.Z,{className:"h-4 w-4 text-gray-400"})}),(0,r.jsx)("input",{type:"text",value:i,onChange:e=>{let t=e.target.value;l(t),s(t)},onKeyDown:e=>{"Enter"===e.key&&(e.preventDefault(),x())},onBlur:x,placeholder:"输入地区筛选，如：北京、上海等",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"})]}),(0,r.jsxs)("div",{className:"relative",ref:y,children:[(0,r.jsx)("button",{type:"button",onClick:()=>{d(!c)},className:"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",title:"查看地区历史",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),c&&n.length>0&&(0,r.jsxs)("div",{className:"absolute right-0 top-full mt-1 w-64 bg-white border border-gray-300 rounded-lg shadow-lg z-10",children:[(0,r.jsxs)("div",{className:"px-4 py-2 bg-gray-50 border-b border-gray-200 flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-600 flex items-center",children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"地区历史"]}),(0,r.jsx)("button",{onClick:()=>{o([]),h([]),d(!1)},className:"text-xs text-red-500 hover:text-red-700 transition-colors",children:"清除"})]}),(0,r.jsx)("div",{className:"max-h-48 overflow-y-auto",children:n.map((e,t)=>(0,r.jsxs)("div",{onClick:()=>g(e),className:"px-4 py-2 cursor-pointer hover:bg-gray-50 transition-colors flex items-center",children:[(0,r.jsx)(D.Z,{className:"h-3 w-3 text-gray-400 mr-2 flex-shrink-0"}),(0,r.jsx)("span",{className:"text-sm",children:e})]},"".concat(e,"-").concat(t)))})]})]})]}),i&&(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)("button",{onClick:()=>{l(""),s(""),d(!1)},className:"text-xs text-gray-500 hover:text-gray-700 transition-colors",children:"清空筛选"})})]})};let z="breed_filter_history";var M=e=>{let{selectedBreed:t,onBreedChange:s}=e,[i,l]=(0,a.useState)(t),[n,o]=(0,a.useState)([]),[c,d]=(0,a.useState)(!1),u=()=>{try{let e=localStorage.getItem(z);return e?JSON.parse(e):[]}catch(e){return[]}},h=e=>{try{localStorage.setItem(z,JSON.stringify(e))}catch(e){}},m=e=>{if(!e.trim())return;let t=[e,...u().filter(t=>t!==e)].slice(0,10);o(t),h(t)};(0,a.useEffect)(()=>{o(u())},[]);let g=e=>{l(e),s(e),d(!1)},x=()=>{i&&i.trim()&&m(i.trim())},y=(0,p.O)(()=>{d(!1)});return(0,a.useEffect)(()=>{l(t)},[t]),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("input",{type:"text",value:i,onChange:e=>{let t=e.target.value;l(t),s(t)},onKeyDown:e=>{"Enter"===e.key&&(e.preventDefault(),x())},onBlur:x,placeholder:"输入品种筛选，如：柴犬、英短等",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"})}),(0,r.jsxs)("div",{className:"relative",ref:y,children:[(0,r.jsx)("button",{type:"button",onClick:()=>{d(!c)},className:"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",title:"查看品种历史",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),c&&n.length>0&&(0,r.jsxs)("div",{className:"absolute right-0 top-full mt-1 w-64 bg-white border border-gray-300 rounded-lg shadow-lg z-10",children:[(0,r.jsxs)("div",{className:"px-4 py-2 bg-gray-50 border-b border-gray-200 flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-600 flex items-center",children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"品种历史"]}),(0,r.jsx)("button",{onClick:()=>{o([]),h([]),d(!1)},className:"text-xs text-red-500 hover:text-red-700 transition-colors",children:"清除"})]}),(0,r.jsx)("div",{className:"max-h-48 overflow-y-auto",children:n.map((e,t)=>(0,r.jsx)("div",{onClick:()=>g(e),className:"px-4 py-2 cursor-pointer hover:bg-gray-50 transition-colors",children:(0,r.jsx)("span",{className:"text-sm",children:e})},"".concat(e,"-").concat(t)))})]})]})]}),i&&(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)("button",{onClick:()=>{l(""),s(""),d(!1)},className:"text-xs text-gray-500 hover:text-gray-700 transition-colors",children:"清空筛选"})})]})},H=s(69076),Z=e=>{let{filters:t,onRemoveFilter:s,onClearAll:a,hasActiveFilters:i,totalCount:l}=e;if(!i)return null;let n=(e,t)=>{switch(e){case"category":return"分类: ".concat(t);case"petType":return"类型: ".concat({breeding:"配种",selling:"出售",lost:"寻回"}[t]||t);case"location":return"地区: ".concat(t);case"breed":return"品种: ".concat(t);case"sortBy":return"排序: ".concat({created_at:"最新发布",likes_count:"最多点赞",wants_count:"最想要",avg_rating:"最高评分",priority:"智能推荐"}[t]||t);default:return"".concat(e,": ").concat(t)}},o=Object.entries(t).filter(e=>{let[t,s]=e;return"page"!==t&&"limit"!==t&&("petType"!==t||"all"!==s)&&("sortBy"!==t||"priority"!==s)&&s&&""!==s});return(0,r.jsx)("div",{className:"bg-white border-b border-gray-200 px-4 py-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 flex-wrap",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600 font-medium",children:"筛选条件:"}),o.map(e=>{let[t,a]=e;return(0,r.jsxs)("div",{className:"inline-flex items-center bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-1 rounded-full",children:[(0,r.jsx)("span",{children:n(t,a)}),(0,r.jsx)("button",{onClick:()=>s(t),className:"ml-1.5 inline-flex items-center justify-center w-3 h-3 text-blue-600 hover:text-blue-800 hover:bg-blue-200 rounded-full transition-colors","aria-label":"移除".concat(t,"筛选条件"),children:(0,r.jsx)(u.Z,{className:"w-2 h-2"})})]},t)}),void 0!==l&&(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:["共找到 ",l," 个结果"]})]}),(0,r.jsxs)("button",{onClick:a,className:"inline-flex items-center text-sm text-gray-600 hover:text-gray-800 transition-colors",children:[(0,r.jsx)(H.Z,{className:"w-4 h-4 mr-1"}),"清除全部"]})]})})},K=s(99376);let F={category:"",petType:"all",location:"",breed:"",sortBy:"priority",page:1,limit:20},U=()=>{let e=(0,K.useRouter)(),t=(0,K.useSearchParams)(),s=(0,a.useCallback)(()=>({category:t.get("category")||F.category,petType:t.get("petType")||F.petType,location:t.get("location")||F.location,breed:t.get("breed")||F.breed,sortBy:t.get("sortBy")||F.sortBy,page:parseInt(t.get("page")||"1"),limit:parseInt(t.get("limit")||"20")}),[t]),[r,i]=(0,a.useState)(s),l=(0,a.useCallback)(t=>{let s=new URLSearchParams;t.category&&t.category!==F.category&&s.set("category",t.category),t.petType!==F.petType&&s.set("petType",t.petType),t.location&&t.location!==F.location&&s.set("location",t.location),t.breed&&t.breed!==F.breed&&s.set("breed",t.breed),t.sortBy!==F.sortBy&&s.set("sortBy",t.sortBy),t.page!==F.page&&s.set("page",t.page.toString());let r=s.toString()?"?".concat(s.toString()):"/";e.replace(r,{scroll:!1})},[e]),n=(0,a.useCallback)((e,t)=>{i(s=>{let r={...s,[e]:t,page:"page"===e?t:1};return l(r),r})},[l]),o=(0,a.useCallback)(()=>{i(F),l(F)},[l]),c=(0,a.useCallback)(()=>{n("page",1)},[n]),d=(0,a.useCallback)(()=>{let e=new URLSearchParams;return Object.entries(r).forEach(t=>{let[s,r]=t;r&&r!==F[s]&&e.set(s,r.toString())}),e},[r]),u=(0,a.useCallback)(()=>Object.entries(r).some(e=>{let[t,s]=e;return"page"!==t&&"limit"!==t&&s!==F[t]}),[r]);return(0,a.useEffect)(()=>{i(s())},[t,s]),{filters:r,updateFilter:n,resetFilters:o,resetPage:c,getQueryParams:d,hasActiveFilters:u()}};var A=()=>{let{filters:e,updateFilter:t,resetFilters:s,hasActiveFilters:u}=U(),[h,m]=(0,a.useState)(!1),[g,p]=(0,a.useState)(!1),f=[{value:"priority",label:"智能推荐",icon:i.Z},{value:"created_at",label:"最新发布",icon:l.Z},{value:"likes_count",label:"最多点赞",icon:n.Z},{value:"wants_count",label:"最想要",icon:o.Z},{value:"avg_rating",label:"最高评分",icon:i.Z}],v=[{value:"all",label:"全部",color:"bg-gray-500"},{value:"breeding",label:"配种",color:"bg-pink-500"},{value:"selling",label:"出售",color:"bg-green-500"},{value:"lost",label:"寻回",color:"bg-orange-500"}],b=f.find(t=>t.value===e.sortBy),j=v.find(t=>t.value===e.petType);return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(d.Z,{}),(0,r.jsx)(y,{selectedCategory:e.category,onCategoryChange:e=>t("category",e)}),(0,r.jsx)(Z,{filters:e,onRemoveFilter:e=>{switch(e){case"category":t("category","");break;case"petType":t("petType","all");break;case"location":t("location","");break;case"breed":t("breed","");break;case"sortBy":t("sortBy","priority")}},onClearAll:s,hasActiveFilters:u}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,r.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"发现宠物"})}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)(P.Z,{variant:"outline",onClick:()=>m(!h),className:"flex items-center space-x-2",children:[b&&(0,r.jsx)(b.icon,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:(null==b?void 0:b.label)||"排序"}),(0,r.jsx)(c.Z,{className:"w-4 h-4"})]}),h&&(0,r.jsx)("div",{className:"absolute top-full left-0 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10",children:(0,r.jsx)("div",{className:"py-1",children:f.map(s=>(0,r.jsxs)("button",{onClick:()=>{t("sortBy",s.value),m(!1)},className:(0,x.cn)("w-full px-4 py-2 text-left flex items-center space-x-2 hover:bg-gray-50",e.sortBy===s.value?"bg-primary-50 text-primary-600":"text-gray-700"),children:[(0,r.jsx)(s.icon,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:s.label})]},s.value))})})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)(P.Z,{variant:"outline",onClick:()=>p(!g),className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:(0,x.cn)("w-3 h-3 rounded-full",null==j?void 0:j.color)}),(0,r.jsx)("span",{children:(null==j?void 0:j.label)||"类型"}),(0,r.jsx)(c.Z,{className:"w-4 h-4"})]}),g&&(0,r.jsx)("div",{className:"absolute top-full left-0 mt-1 w-32 bg-white rounded-lg shadow-lg border border-gray-200 z-10",children:(0,r.jsx)("div",{className:"py-1",children:v.map(s=>(0,r.jsxs)("button",{onClick:()=>{t("petType",s.value),p(!1)},className:(0,x.cn)("w-full px-4 py-2 text-left flex items-center space-x-2 hover:bg-gray-50",e.petType===s.value?"bg-primary-50 text-primary-600":"text-gray-700"),children:[(0,r.jsx)("div",{className:(0,x.cn)("w-3 h-3 rounded-full",s.color)}),(0,r.jsx)("span",{children:s.label})]},s.value))})})]}),(0,r.jsx)("div",{className:"flex-1 min-w-[200px]",children:(0,r.jsx)(R,{selectedLocation:e.location,onLocationChange:e=>t("location",e)})}),(0,r.jsx)("div",{className:"flex-1 min-w-[200px]",children:(0,r.jsx)(M,{selectedBreed:e.breed,onBreedChange:e=>t("breed",e)})})]})]}),(0,r.jsx)(N,{positionId:"home_banner",className:"mb-6",fallbackContent:(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 text-center",children:(0,r.jsx)("p",{className:"text-gray-600",children:"\uD83D\uDC3E 发现更多可爱的宠物朋友"})})}),(0,r.jsx)(L,{filters:e})]})]})}},60719:function(e,t,s){"use strict";s.d(t,{O:function(){return a}});var r=s(2265);function a(e){let t=(0,r.useRef)(null);return(0,r.useEffect)(()=>{let s=s=>{t.current&&!t.current.contains(s.target)&&e()};return document.addEventListener("mousedown",s),()=>{document.removeEventListener("mousedown",s)}},[e]),t}},33882:function(e,t,s){"use strict";s.d(t,{U_:function(){return i},dataCache:function(){return l}});class r{set(e,t,s){let r=Date.now(),a=s||this.config.ttl;this.cache.size>=this.config.maxSize&&this.evict(),this.cache.set(e,{data:t,timestamp:r,ttl:a,accessCount:0,lastAccess:r})}get(e){let t=this.cache.get(e);if(!t)return null;let s=Date.now();return s-t.timestamp>t.ttl?(this.cache.delete(e),null):(t.accessCount++,t.lastAccess=s,t.data)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}evict(){let e;if(0!==this.cache.size){switch(this.config.strategy){case"LRU":e=this.findLRUKey();break;case"FIFO":e=this.findFIFOKey();break;case"TTL":e=this.findExpiredKey();break;default:e=this.cache.keys().next().value}e&&this.cache.delete(e)}}findLRUKey(){let e;let t=Date.now();return this.cache.forEach((s,r)=>{s.lastAccess<t&&(t=s.lastAccess,e=r)}),e}findFIFOKey(){let e;let t=Date.now();return this.cache.forEach((s,r)=>{s.timestamp<t&&(t=s.timestamp,e=r)}),e}findExpiredKey(){let e;let t=Date.now();return(this.cache.forEach((s,r)=>{if(t-s.timestamp>s.ttl){e=r;return}}),e)?e:this.findLRUKey()}getStats(){let e=Date.now(),t=0,s=0;return this.cache.forEach(r=>{e-r.timestamp>r.ttl&&t++,s+=JSON.stringify(r.data).length}),{size:this.cache.size,maxSize:this.config.maxSize,expired:t,totalSize:s,hitRate:this.calculateHitRate()}}calculateHitRate(){let e=0;return this.cache.forEach(t=>{e+=t.accessCount}),e>0?this.cache.size/e*100:0}constructor(e={}){this.cache=new Map,this.config={ttl:3e5,maxSize:100,strategy:"LRU",...e}}}class a{set(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:864e5;if(!this.storage)return;let r={data:t,timestamp:Date.now(),ttl:s};try{this.storage.setItem(this.prefix+e,JSON.stringify(r))}catch(t){console.warn("浏览器存储空间不足，清理过期缓存"),this.cleanup();try{this.storage.setItem(this.prefix+e,JSON.stringify(r))}catch(e){console.error("缓存设置失败:",e)}}}get(e){if(!this.storage)return null;try{let t=this.storage.getItem(this.prefix+e);if(!t)return null;let s=JSON.parse(t);if(Date.now()-s.timestamp>s.ttl)return this.storage.removeItem(this.prefix+e),null;return s.data}catch(e){return console.error("缓存读取失败:",e),null}}delete(e){this.storage&&this.storage.removeItem(this.prefix+e)}cleanup(){if(!this.storage)return;let e=Date.now(),t=[];for(let s=0;s<this.storage.length;s++){let r=this.storage.key(s);if(r&&r.startsWith(this.prefix))try{let s=this.storage.getItem(r);if(s){let a=JSON.parse(s);e-a.timestamp>a.ttl&&t.push(r)}}catch(e){t.push(r)}}t.forEach(e=>this.storage.removeItem(e))}clear(){if(!this.storage)return;let e=[];for(let t=0;t<this.storage.length;t++){let s=this.storage.key(t);s&&s.startsWith(this.prefix)&&e.push(s)}e.forEach(e=>this.storage.removeItem(e))}constructor(e="pet_cache_",t=!1){this.prefix=e,this.storage=t?sessionStorage:localStorage}}let i=new r({ttl:18e5,maxSize:50,strategy:"LRU"}),l=new r({ttl:3e5,maxSize:100,strategy:"LRU"});new a("pet_app_")},59604:function(e,t,s){"use strict";s.d(t,{Fd:function(){return i},KX:function(){return l}});class r{getHistory(){try{let e=localStorage.getItem(this.storageKey);if(e)return JSON.parse(e).sort((e,t)=>t.timestamp-e.timestamp)}catch(e){console.error("获取历史记录失败:",e)}return[]}addItem(e,t){if(!e.trim())return;let s={value:e.trim(),label:t||e.trim(),timestamp:Date.now()},r=this.getHistory();(r=r.filter(e=>e.value!==s.value)).unshift(s),r.length>this.maxItems&&(r=r.slice(0,this.maxItems)),this.saveHistory(r)}removeItem(e){let t=this.getHistory().filter(t=>t.value!==e);this.saveHistory(t)}clearHistory(){localStorage.removeItem(this.storageKey)}getHistoryValues(){return this.getHistory().map(e=>e.value)}hasItem(e){return this.getHistoryValues().includes(e)}saveHistory(e){try{localStorage.setItem(this.storageKey,JSON.stringify(e))}catch(e){console.error("保存历史记录失败:",e)}}constructor(e,t=10){this.storageKey=e,this.maxItems=t}}class a{getHistory(){try{let e=localStorage.getItem(this.storageKey);if(e){let t=JSON.parse(e),s=Date.now()-864e5*this.maxDays,r=t.filter(e=>e.timestamp>s);return r.length!==t.length&&this.saveHistory(r),r.sort((e,t)=>t.timestamp-e.timestamp)}}catch(e){console.error("获取浏览历史记录失败:",e)}return[]}addBrowseRecord(e,t,s,r,a){if(!e||!t)return;let i={postId:e,title:t.trim(),author:s.trim(),authorId:r,image:a,timestamp:Date.now()},l=this.getHistory();(l=l.filter(e=>e.postId!==i.postId)).unshift(i),l.length>this.maxItems&&(l=l.slice(0,this.maxItems)),this.saveHistory(l)}removeRecord(e){let t=this.getHistory().filter(t=>t.postId!==e);this.saveHistory(t)}clearHistory(){localStorage.removeItem(this.storageKey)}getRecentPostIds(){return this.getHistory().map(e=>e.postId)}hasBrowsed(e){return this.getRecentPostIds().includes(e)}saveHistory(e){try{localStorage.setItem(this.storageKey,JSON.stringify(e))}catch(e){console.error("保存浏览历史记录失败:",e)}}constructor(){this.storageKey="browse_history",this.maxDays=3,this.maxItems=50}}new r("location_history",10);let i=new r("category_history",8),l=new a},80240:function(e,t,s){"use strict";s.d(t,{Bm:function(){return a}});class r{static getInstance(){return r.instance||(r.instance=new r),r.instance}initializeObservers(){try{this.observeNavigation(),this.observePaint(),this.observeLayoutShift(),this.observeFirstInputDelay(),this.observeResources()}catch(e){console.warn("性能监控初始化失败:",e)}}observeNavigation(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{"navigation"===e.entryType&&(this.metrics.pageLoadTime=e.loadEventEnd-e.fetchStart)})});e.observe({entryTypes:["navigation"]}),this.observers.push(e)}}observePaint(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{"first-contentful-paint"===e.name&&(this.metrics.firstContentfulPaint=e.startTime)})});e.observe({entryTypes:["paint"]}),this.observers.push(e)}if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{let t=e.getEntries(),s=t[t.length-1];this.metrics.largestContentfulPaint=s.startTime});e.observe({entryTypes:["largest-contentful-paint"]}),this.observers.push(e)}}observeLayoutShift(){if("PerformanceObserver"in window){let e=0,t=new PerformanceObserver(t=>{t.getEntries().forEach(t=>{t.hadRecentInput||(e+=t.value,this.metrics.cumulativeLayoutShift=e)})});t.observe({entryTypes:["layout-shift"]}),this.observers.push(t)}}observeFirstInputDelay(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{this.metrics.firstInputDelay=e.processingStart-e.startTime})});e.observe({entryTypes:["first-input"]}),this.observers.push(e)}}observeResources(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{if("img"===e.initiatorType){let t=e.responseEnd-e.startTime;this.metrics.imageLoadTimes.push(t)}})});e.observe({entryTypes:["resource"]}),this.observers.push(e)}}recordApiResponse(e,t){this.metrics.apiResponseTimes.has(e)||this.metrics.apiResponseTimes.set(e,[]),this.metrics.apiResponseTimes.get(e).push(t)}recordImageLoad(e){this.metrics.imageLoadTimes.push(e)}getMemoryUsage(){return"memory"in performance?performance.memory:null}getMetrics(){return{...this.metrics,memoryUsage:this.getMemoryUsage()||void 0}}getPerformanceReport(){let e={};this.metrics.apiResponseTimes.forEach((t,s)=>{let r=t.reduce((e,t)=>e+t,0)/t.length;e[s]={avgResponseTime:Math.round(r),callCount:t.length,maxResponseTime:Math.round(Math.max(...t)),minResponseTime:Math.round(Math.min(...t))}});let t=this.metrics.imageLoadTimes.length>0?this.metrics.imageLoadTimes.reduce((e,t)=>e+t,0)/this.metrics.imageLoadTimes.length:0;return{coreWebVitals:{lcp:Math.round(this.metrics.largestContentfulPaint),fid:Math.round(this.metrics.firstInputDelay),cls:Math.round(1e3*this.metrics.cumulativeLayoutShift)/1e3},loadingPerformance:{pageLoadTime:Math.round(this.metrics.pageLoadTime),fcp:Math.round(this.metrics.firstContentfulPaint),avgImageLoadTime:Math.round(t)},apiPerformance:e,memoryUsage:this.getMemoryUsage()||void 0}}getPerformanceScore(){let e=this.metrics.largestContentfulPaint<=2500?100:this.metrics.largestContentfulPaint<=4e3?50:0,t=this.metrics.firstInputDelay<=100?100:this.metrics.firstInputDelay<=300?50:0,s=this.metrics.cumulativeLayoutShift<=.1?100:this.metrics.cumulativeLayoutShift<=.25?50:0;return{overall:Math.round((e+t+s)/3),breakdown:{loading:e,interactivity:t,visualStability:s}}}cleanup(){this.observers.forEach(e=>e.disconnect()),this.observers=[]}exportData(){return JSON.stringify({timestamp:new Date().toISOString(),url:window.location.href,userAgent:navigator.userAgent,metrics:this.getMetrics(),report:this.getPerformanceReport(),score:this.getPerformanceScore()},null,2)}constructor(){this.observers=[],this.metrics={pageLoadTime:0,firstContentfulPaint:0,largestContentfulPaint:0,firstInputDelay:0,cumulativeLayoutShift:0,imageLoadTimes:[],apiResponseTimes:new Map},this.initializeObservers()}}let a=r.getInstance()}},function(e){e.O(0,[649,19,347,554,721,319,44,11,734,825,465,971,117,744],function(){return e(e.s=88342)}),_N_E=e.O()}]);