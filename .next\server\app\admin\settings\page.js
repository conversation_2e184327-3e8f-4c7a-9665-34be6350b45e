(()=>{var e={};e.id=140,e.ids=[140],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},94320:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>g,tree:()=>o}),a(92632),a(9457),a(16953),a(35866);var t=a(23191),r=a(88716),l=a(37922),i=a.n(l),n=a(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let o=["",{children:["admin",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,92632)),"D:\\web-cloudbase-project\\src\\app\\admin\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,9457)),"D:\\web-cloudbase-project\\src\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\web-cloudbase-project\\src\\app\\admin\\settings\\page.tsx"],m="/admin/settings/page",x={require:a,loadChunk:()=>Promise.resolve()},g=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/settings/page",pathname:"/admin/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},64271:(e,s,a)=>{Promise.resolve().then(a.bind(a,5264))},83476:(e,s,a)=>{Promise.resolve().then(a.bind(a,58874))},86333:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},71709:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(76557).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},21405:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(76557).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},31215:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(76557).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},88378:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5264:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>l});var t=a(10326);a(17577);var r=a(20603);function l({children:e}){return(0,t.jsxs)("div",{className:"admin-layout",children:[t.jsx(r.ToastProvider,{}),e]})}a(23824)},58874:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>g});var t=a(10326),r=a(17577),l=a(35047),i=a(20603),n=a(99837),c=a(88378),o=a(86333),d=a(71709),m=a(21405),x=a(31215);function g(){let e=(0,l.useRouter)(),[s,a]=(0,r.useState)(!0),[g,p]=(0,r.useState)(!1),[u,h]=(0,r.useState)({maxImageSize:5,maxImagesPerPost:9,allowedImageTypes:["image/jpeg","image/png","image/webp"]}),y=async()=>{try{if(p(!0),u.maxImageSize<=0||u.maxImageSize>100){i.C.error("图片大小限制必须在1-100MB之间");return}if(u.maxImagesPerPost<=0||u.maxImagesPerPost>20){i.C.error("每帖图片数量必须在1-20张之间");return}localStorage.setItem("systemSettings",JSON.stringify(u)),i.C.success("设置保存成功")}catch(e){console.error("保存设置失败:",e),i.C.error("保存设置失败")}finally{p(!1)}};return s?t.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx(c.Z,{className:"w-16 h-16 text-blue-600 mx-auto mb-4 animate-pulse"}),t.jsx("p",{className:"text-gray-600",children:"加载设置中..."})]})}):(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[t.jsx("div",{className:"bg-white shadow-sm border-b",children:t.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:t.jsx("div",{className:"flex items-center justify-between h-16",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("button",{onClick:()=>e.push("/admin/dashboard"),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900",children:[t.jsx(o.Z,{className:"w-5 h-5"}),t.jsx("span",{children:"返回控制台"})]}),t.jsx("div",{className:"h-6 w-px bg-gray-300"}),t.jsx("h1",{className:"text-xl font-semibold text-gray-900",children:"系统设置"})]})})})}),t.jsx("div",{className:"max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(d.Z,{className:"w-6 h-6 text-blue-600"}),t.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"图片上传设置"})]}),t.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"配置用户上传图片的限制和规则"})]}),(0,t.jsxs)("div",{className:"px-6 py-6 space-y-6",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"单张图片大小限制 (MB)"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[t.jsx("input",{type:"number",min:"1",max:"100",step:"1",value:u.maxImageSize,onChange:e=>h(s=>({...s,maxImageSize:parseInt(e.target.value)||1})),className:"block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["当前限制：",u.maxImageSize,"MB"]})]}),t.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"建议设置在5-30MB之间，过大会影响上传速度"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"每帖最大图片数量"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[t.jsx("input",{type:"number",min:"1",max:"20",step:"1",value:u.maxImagesPerPost,onChange:e=>h(s=>({...s,maxImagesPerPost:parseInt(e.target.value)||1})),className:"block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["当前限制：",u.maxImagesPerPost,"张"]})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"支持的图片格式"}),t.jsx("div",{className:"space-y-2",children:["image/jpeg","image/png","image/webp","image/gif"].map(e=>(0,t.jsxs)("label",{className:"flex items-center",children:[t.jsx("input",{type:"checkbox",checked:u.allowedImageTypes.includes(e),onChange:s=>{s.target.checked?h(s=>({...s,allowedImageTypes:[...s.allowedImageTypes,e]})):h(s=>({...s,allowedImageTypes:s.allowedImageTypes.filter(s=>s!==e)}))},className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),t.jsx("span",{className:"ml-2 text-sm text-gray-700",children:e.replace("image/","").toUpperCase()})]},e))})]})]}),(0,t.jsxs)("div",{className:"px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between",children:[(0,t.jsxs)(n.Z,{variant:"outline",onClick:()=>{confirm("确定要重置为默认设置吗？")&&h({maxImageSize:5,maxImagesPerPost:9,allowedImageTypes:["image/jpeg","image/png","image/webp"]})},className:"flex items-center space-x-2",children:[t.jsx(m.Z,{className:"w-4 h-4"}),t.jsx("span",{children:"重置默认"})]}),(0,t.jsxs)(n.Z,{onClick:y,loading:g,disabled:g,className:"flex items-center space-x-2",children:[t.jsx(x.Z,{className:"w-4 h-4"}),t.jsx("span",{children:g?"保存中...":"保存设置"})]})]})]})})]})}},99837:(e,s,a)=>{"use strict";a.d(s,{Z:()=>c});var t=a(10326),r=a(17577),l=a.n(r),i=a(28295);let n=l().forwardRef(({className:e,variant:s="primary",size:a="md",loading:r=!1,icon:l,children:n,disabled:c,...o},d)=>(0,t.jsxs)("button",{className:(0,i.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[s],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[a],e),ref:d,disabled:c||r,...o,children:[r&&(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[t.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),t.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!r&&l&&t.jsx("span",{className:"mr-2",children:l}),n]}));n.displayName="Button";let c=n},9457:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\admin\layout.tsx#default`)},92632:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\admin\settings\page.tsx#default`)},23824:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[276,201,240],()=>a(94320));module.exports=t})();