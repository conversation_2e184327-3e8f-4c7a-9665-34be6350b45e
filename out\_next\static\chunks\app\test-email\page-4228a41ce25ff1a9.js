(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[207],{78481:function(e,s,l){Promise.resolve().then(l.bind(l,94362))},94362:function(e,s,l){"use strict";l.r(s),l.d(s,{default:function(){return d}});var r=l(57437),n=l(2265),t=l(98011);function d(){let[e,s]=(0,n.useState)(""),[l,d]=(0,n.useState)(null),[i,a]=(0,n.useState)(!1),[c,o]=(0,n.useState)(""),u=async()=>{if(!e){o("请输入邮箱地址");return}a(!0),o(""),d(null);try{console.log("开始测试发送验证码...");let s=await t.authAPI.sendVerificationCode(e,"register");console.log("发送验证码响应:",s),d(s)}catch(e){console.error("发送验证码错误:",e),o(e.message||"发送失败")}finally{a(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4",children:(0,r.jsxs)("div",{className:"max-w-md mx-auto bg-white rounded-lg shadow-md p-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-center mb-6",children:"邮箱验证码测试"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),(0,r.jsx)("input",{type:"email",value:e,onChange:e=>s(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"请输入邮箱地址"})]}),(0,r.jsx)("button",{onClick:u,disabled:i,className:"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed",children:i?"发送中...":"发送验证码"}),c&&(0,r.jsxs)("div",{className:"p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:[(0,r.jsx)("strong",{children:"错误:"})," ",c]}),l&&(0,r.jsxs)("div",{className:"p-3 bg-green-100 border border-green-400 text-green-700 rounded",children:[(0,r.jsx)("strong",{children:"成功:"}),(0,r.jsx)("pre",{className:"mt-2 text-sm overflow-auto",children:JSON.stringify(l,null,2)})]})]}),(0,r.jsxs)("div",{className:"mt-6 text-sm text-gray-600",children:[(0,r.jsx)("p",{children:(0,r.jsx)("strong",{children:"测试说明:"})}),(0,r.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[(0,r.jsx)("li",{children:"输入真实邮箱地址"}),(0,r.jsx)("li",{children:"点击发送验证码"}),(0,r.jsx)("li",{children:"查看控制台日志"}),(0,r.jsx)("li",{children:"检查邮箱是否收到验证码"})]})]})]})})}}},function(e){e.O(0,[649,19,347,11,971,117,744],function(){return e(e.s=78481)}),_N_E=e.O()}]);