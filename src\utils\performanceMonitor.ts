// 性能监控工具
interface MemoryInfo {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
}

export interface PerformanceMetrics {
  pageLoadTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  firstInputDelay: number;
  cumulativeLayoutShift: number;
  imageLoadTimes: number[];
  apiResponseTimes: Map<string, number[]>;
  memoryUsage?: MemoryInfo;
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetrics;
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.metrics = {
      pageLoadTime: 0,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      firstInputDelay: 0,
      cumulativeLayoutShift: 0,
      imageLoadTimes: [],
      apiResponseTimes: new Map()
    };

    this.initializeObservers();
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // 初始化性能观察器
  private initializeObservers(): void {
    if (typeof window === 'undefined') return;

    try {
      // 观察页面加载性能
      this.observeNavigation();
      
      // 观察绘制性能
      this.observePaint();
      
      // 观察布局偏移
      this.observeLayoutShift();
      
      // 观察首次输入延迟
      this.observeFirstInputDelay();

      // 观察资源加载
      this.observeResources();

    } catch (error) {
      console.warn('性能监控初始化失败:', error);
    }
  }

  // 观察导航性能
  private observeNavigation(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            this.metrics.pageLoadTime = navEntry.loadEventEnd - navEntry.fetchStart;
          }
        });
      });

      observer.observe({ entryTypes: ['navigation'] });
      this.observers.push(observer);
    }
  }

  // 观察绘制性能
  private observePaint(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name === 'first-contentful-paint') {
            this.metrics.firstContentfulPaint = entry.startTime;
          }
        });
      });

      observer.observe({ entryTypes: ['paint'] });
      this.observers.push(observer);
    }

    // 观察LCP
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.largestContentfulPaint = lastEntry.startTime;
      });

      observer.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(observer);
    }
  }

  // 观察布局偏移
  private observeLayoutShift(): void {
    if ('PerformanceObserver' in window) {
      let clsValue = 0;
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
            this.metrics.cumulativeLayoutShift = clsValue;
          }
        });
      });

      observer.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(observer);
    }
  }

  // 观察首次输入延迟
  private observeFirstInputDelay(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          this.metrics.firstInputDelay = entry.processingStart - entry.startTime;
        });
      });

      observer.observe({ entryTypes: ['first-input'] });
      this.observers.push(observer);
    }
  }

  // 观察资源加载
  private observeResources(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          const resourceEntry = entry as any;
          if (resourceEntry.initiatorType === 'img') {
            const loadTime = resourceEntry.responseEnd - resourceEntry.startTime;
            this.metrics.imageLoadTimes.push(loadTime);
          }
        });
      });

      observer.observe({ entryTypes: ['resource'] });
      this.observers.push(observer);
    }
  }

  // 记录API响应时间
  recordApiResponse(apiName: string, responseTime: number): void {
    if (!this.metrics.apiResponseTimes.has(apiName)) {
      this.metrics.apiResponseTimes.set(apiName, []);
    }
    this.metrics.apiResponseTimes.get(apiName)!.push(responseTime);
  }

  // 记录图片加载时间
  recordImageLoad(loadTime: number): void {
    this.metrics.imageLoadTimes.push(loadTime);
  }

  // 获取内存使用情况
  getMemoryUsage(): MemoryInfo | null {
    if ('memory' in performance) {
      return (performance as any).memory;
    }
    return null;
  }

  // 获取当前性能指标
  getMetrics(): PerformanceMetrics {
    return {
      ...this.metrics,
      memoryUsage: this.getMemoryUsage() || undefined
    };
  }

  // 获取性能报告
  getPerformanceReport(): {
    coreWebVitals: {
      lcp: number;
      fid: number;
      cls: number;
    };
    loadingPerformance: {
      pageLoadTime: number;
      fcp: number;
      avgImageLoadTime: number;
    };
    apiPerformance: {
      [key: string]: {
        avgResponseTime: number;
        callCount: number;
        maxResponseTime: number;
        minResponseTime: number;
      };
    };
    memoryUsage?: MemoryInfo;
  } {
    const apiPerformance: any = {};
    
    this.metrics.apiResponseTimes.forEach((times, apiName) => {
      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const maxTime = Math.max(...times);
      const minTime = Math.min(...times);
      
      apiPerformance[apiName] = {
        avgResponseTime: Math.round(avgTime),
        callCount: times.length,
        maxResponseTime: Math.round(maxTime),
        minResponseTime: Math.round(minTime)
      };
    });

    const avgImageLoadTime = this.metrics.imageLoadTimes.length > 0
      ? this.metrics.imageLoadTimes.reduce((sum, time) => sum + time, 0) / this.metrics.imageLoadTimes.length
      : 0;

    return {
      coreWebVitals: {
        lcp: Math.round(this.metrics.largestContentfulPaint),
        fid: Math.round(this.metrics.firstInputDelay),
        cls: Math.round(this.metrics.cumulativeLayoutShift * 1000) / 1000
      },
      loadingPerformance: {
        pageLoadTime: Math.round(this.metrics.pageLoadTime),
        fcp: Math.round(this.metrics.firstContentfulPaint),
        avgImageLoadTime: Math.round(avgImageLoadTime)
      },
      apiPerformance,
      memoryUsage: this.getMemoryUsage() || undefined
    };
  }

  // 性能评分
  getPerformanceScore(): {
    overall: number;
    breakdown: {
      loading: number;
      interactivity: number;
      visualStability: number;
    };
  } {
    // 基于Core Web Vitals的评分算法
    const lcpScore = this.metrics.largestContentfulPaint <= 2500 ? 100 : 
                     this.metrics.largestContentfulPaint <= 4000 ? 50 : 0;
    
    const fidScore = this.metrics.firstInputDelay <= 100 ? 100 : 
                     this.metrics.firstInputDelay <= 300 ? 50 : 0;
    
    const clsScore = this.metrics.cumulativeLayoutShift <= 0.1 ? 100 : 
                     this.metrics.cumulativeLayoutShift <= 0.25 ? 50 : 0;

    const loading = lcpScore;
    const interactivity = fidScore;
    const visualStability = clsScore;
    const overall = Math.round((loading + interactivity + visualStability) / 3);

    return {
      overall,
      breakdown: {
        loading,
        interactivity,
        visualStability
      }
    };
  }

  // 清理观察器
  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }

  // 导出性能数据
  exportData(): string {
    return JSON.stringify({
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      metrics: this.getMetrics(),
      report: this.getPerformanceReport(),
      score: this.getPerformanceScore()
    }, null, 2);
  }
}

// 性能监控装饰器
export function monitorPerformance(apiName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const monitor = PerformanceMonitor.getInstance();

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      
      try {
        const result = await method.apply(this, args);
        const endTime = Date.now();
        monitor.recordApiResponse(apiName, endTime - startTime);
        return result;
      } catch (error) {
        const endTime = Date.now();
        monitor.recordApiResponse(`${apiName}_error`, endTime - startTime);
        throw error;
      }
    };

    return descriptor;
  };
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance();
