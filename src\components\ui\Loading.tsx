import React from 'react';
import { cn } from '@/utils';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'spinner' | 'dots' | 'pulse';
  className?: string;
  text?: string;
}

const Loading: React.FC<LoadingProps> = ({
  size = 'md',
  variant = 'spinner',
  className,
  text,
}) => {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  const textSizes = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  if (variant === 'spinner') {
    return (
      <div className={cn('flex items-center justify-center', className)}>
        <div className="flex flex-col items-center space-y-2">
          <svg
            className={cn('animate-spin text-primary-600', sizes[size])}
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          {text && (
            <p className={cn('text-gray-500', textSizes[size])}>{text}</p>
          )}
        </div>
      </div>
    );
  }

  if (variant === 'dots') {
    const dotSize = size === 'sm' ? 'w-2 h-2' : size === 'md' ? 'w-3 h-3' : 'w-4 h-4';
    
    return (
      <div className={cn('flex items-center justify-center', className)}>
        <div className="flex flex-col items-center space-y-2">
          <div className="flex space-x-1">
            <div className={cn('bg-primary-600 rounded-full animate-bounce', dotSize)} style={{ animationDelay: '0ms' }} />
            <div className={cn('bg-primary-600 rounded-full animate-bounce', dotSize)} style={{ animationDelay: '150ms' }} />
            <div className={cn('bg-primary-600 rounded-full animate-bounce', dotSize)} style={{ animationDelay: '300ms' }} />
          </div>
          {text && (
            <p className={cn('text-gray-500', textSizes[size])}>{text}</p>
          )}
        </div>
      </div>
    );
  }

  if (variant === 'pulse') {
    return (
      <div className={cn('flex items-center justify-center', className)}>
        <div className="flex flex-col items-center space-y-2">
          <div className={cn('bg-primary-600 rounded-full animate-pulse', sizes[size])} />
          {text && (
            <p className={cn('text-gray-500', textSizes[size])}>{text}</p>
          )}
        </div>
      </div>
    );
  }

  return null;
};

// 页面级加载组件
interface PageLoadingProps {
  text?: string;
}

const PageLoading: React.FC<PageLoadingProps> = ({ text = '加载中...' }) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Loading size="lg" text={text} />
    </div>
  );
};

// 卡片加载骨架
interface SkeletonProps {
  className?: string;
  rows?: number;
}

const Skeleton: React.FC<SkeletonProps> = ({ className, rows = 1 }) => {
  return (
    <div className={cn('animate-pulse', className)}>
      {Array.from({ length: rows }).map((_, index) => (
        <div
          key={index}
          className={cn(
            'bg-gray-200 rounded',
            index === 0 ? 'h-4' : 'h-3 mt-2'
          )}
        />
      ))}
    </div>
  );
};

// 宠物卡片加载骨架
const PetCardSkeleton: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden animate-pulse">
      {/* 图片骨架 */}
      <div className="aspect-square bg-gray-200" />
      
      {/* 内容骨架 */}
      <div className="p-3 space-y-2">
        <div className="h-4 bg-gray-200 rounded w-3/4" />
        <div className="h-3 bg-gray-200 rounded w-1/2" />
        <div className="flex items-center justify-between">
          <div className="h-3 bg-gray-200 rounded w-1/4" />
          <div className="h-3 bg-gray-200 rounded w-1/4" />
        </div>
      </div>
    </div>
  );
};

// 用户资料加载骨架
const UserProfileSkeleton: React.FC = () => {
  return (
    <div className="animate-pulse">
      <div className="flex flex-col items-center space-y-4 p-6">
        {/* 头像骨架 */}
        <div className="w-24 h-24 bg-gray-200 rounded-full" />
        
        {/* 用户名骨架 */}
        <div className="h-6 bg-gray-200 rounded w-32" />
        
        {/* 简介骨架 */}
        <div className="h-4 bg-gray-200 rounded w-48" />
        
        {/* 统计数据骨架 */}
        <div className="flex space-x-8">
          <div className="text-center">
            <div className="h-6 bg-gray-200 rounded w-12 mb-1" />
            <div className="h-4 bg-gray-200 rounded w-8" />
          </div>
          <div className="text-center">
            <div className="h-6 bg-gray-200 rounded w-12 mb-1" />
            <div className="h-4 bg-gray-200 rounded w-8" />
          </div>
          <div className="text-center">
            <div className="h-6 bg-gray-200 rounded w-12 mb-1" />
            <div className="h-4 bg-gray-200 rounded w-8" />
          </div>
        </div>
        
        {/* 按钮骨架 */}
        <div className="h-10 bg-gray-200 rounded w-24" />
      </div>
    </div>
  );
};

export { Loading, PageLoading, Skeleton, PetCardSkeleton, UserProfileSkeleton };
