(()=>{var e={};e.id=427,e.ids=[427],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},32868:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>o,originalPathname:()=>m,pages:()=>x,routeModule:()=>h,tree:()=>n}),t(13624),t(9457),t(16953),t(35866);var a=t(23191),l=t(88716),r=t(37922),i=t.n(r),d=t(95231),c={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>d[e]);t.d(s,c);let n=["",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,13624)),"D:\\web-cloudbase-project\\src\\app\\admin\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,9457)),"D:\\web-cloudbase-project\\src\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],x=["D:\\web-cloudbase-project\\src\\app\\admin\\dashboard\\page.tsx"],m="/admin/dashboard/page",o={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/admin/dashboard/page",pathname:"/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},85787:(e,s,t)=>{Promise.resolve().then(t.bind(t,80907))},64271:(e,s,t)=>{Promise.resolve().then(t.bind(t,5264))},66697:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},86333:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},48998:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},88319:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},12714:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},82200:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]])},71709:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},70003:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},21405:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},31215:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},88378:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},58038:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},98091:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},74975:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},80907:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var a=t(10326),l=t(17577),r=t(35047),i=t(41828),d=t(20603),c=t(99837),n=t(86333),x=t(58038),m=t(76557);let o=(0,m.Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),h=(0,m.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),p=(0,m.Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);var u=t(74975);let g=(0,m.Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]);var j=t(88378),y=t(83855),N=t(82200),b=t(66697);let v=(0,m.Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var f=t(98091),w=t(54659),k=t(91470),C=t(12714),Z=t(88319),S=t(70003),_=t(48998),I=t(71709),M=t(21405),D=t(31215);function P(){let e=(0,r.useRouter)(),[s,t]=(0,l.useState)("dashboard"),[m,P]=(0,l.useState)(null),[T,z]=(0,l.useState)(null),[L,A]=(0,l.useState)([]),[V,R]=(0,l.useState)([]),[$,q]=(0,l.useState)(!0),[E,H]=(0,l.useState)(null),[F,B]=(0,l.useState)(null),[U,O]=(0,l.useState)(""),[G,W]=(0,l.useState)(!1),[J,X]=(0,l.useState)("approved"),[K,Q]=(0,l.useState)(!1),[Y,ee]=(0,l.useState)({username:"",password:"",role:"admin",level:1,permissions:[]}),[es,et]=(0,l.useState)([]),[ea,el]=(0,l.useState)(!1),[er,ei]=(0,l.useState)(!1),[ed,ec]=(0,l.useState)(null),[en,ex]=(0,l.useState)(""),[em,eo]=(0,l.useState)([]),[eh,ep]=(0,l.useState)([]),[eu,eg]=(0,l.useState)(!1),[ej,ey]=(0,l.useState)("ads"),[eN,eb]=(0,l.useState)(!1),[ev,ef]=(0,l.useState)([]),[ew,ek]=(0,l.useState)(!1),[eC,eZ]=(0,l.useState)(null),[eS,e_]=(0,l.useState)(!1),[eI,eM]=(0,l.useState)(!1),[eD,eP]=(0,l.useState)("all"),[eT,ez]=(0,l.useState)("all"),[eL,eA]=(0,l.useState)({maxImageSize:5,maxImagesPerPost:9,allowedImageTypes:["image/jpeg","image/png","image/webp"],autoReportThreshold:10}),[eV,eR]=(0,l.useState)(!1),[e$,eq]=(0,l.useState)([]),[eE,eH]=(0,l.useState)(!1),[eF,eB]=(0,l.useState)(""),[eU,eO]=(0,l.useState)(null),[eG,eW]=(0,l.useState)(!1),[eJ,eX]=(0,l.useState)(null),[eK,eQ]=(0,l.useState)(!1),[eY,e0]=(0,l.useState)(null),[e2,e1]=(0,l.useState)(!1),e4=e=>{e0(e),eQ(!0)},e6=async()=>{try{let e=await i.petAPI.getAdmins({limit:50,offset:0});e.success&&A(e.data||[])}catch(e){console.error("加载管理员列表失败:",e)}},e3=async()=>{el(!0);try{let e=await i.petAPI.getPostsForAdmin({limit:50});e.success&&et(e.data)}catch(e){console.error("加载帖子列表失败:",e),d.C.error("加载帖子列表失败")}finally{el(!1)}},e5=async()=>{try{let e=await i.petAPI.getAppeals({status:"all",limit:50,offset:0});e.success&&R(e.data||[])}catch(e){console.error("加载申诉列表失败:",e)}},e7=async()=>{if(!Y.username||!Y.password){d.C.error("请填写用户名和密码");return}try{let e=await i.petAPI.createAdmin({...Y,permissions:["*"]});e.success?(d.C.success("管理员创建成功"),Q(!1),ee({username:"",password:"",role:"admin",level:1,permissions:[]}),e6()):d.C.error(e.message||"创建失败")}catch(e){console.error("创建管理员失败:",e),d.C.error(e.message||"创建管理员失败")}},e8=async e=>{e4({title:"删除管理员",message:"确定要删除这个管理员吗？此操作不可恢复。",confirmText:"删除",cancelText:"取消",type:"danger",onConfirm:async()=>{try{let s=await i.petAPI.deleteAdmin({adminId:e});s.success?(d.C.success("管理员删除成功"),e6()):d.C.error(s.message||"删除失败")}catch(e){console.error("删除管理员失败:",e),d.C.error(e.message||"删除管理员失败")}}})},e9=async()=>{if(F)try{H(F._id);let e=await i.petAPI.handleAppeal({appealId:F._id,action:J,adminReason:U});e.success?(d.C.success("申诉处理成功"),W(!1),B(null),O(""),e5()):d.C.error(e.message||"处理失败")}catch(e){console.error("处理申诉失败:",e),d.C.error(e.message||"处理申诉失败")}finally{H(null)}},se=e=>new Date(e).toLocaleString("zh-CN"),ss=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},st=e=>{switch(e){case"pending":return"待处理";case"approved":return"已通过";case"rejected":return"已驳回";default:return"未知"}},sa=async()=>{if(!ed||!en.trim()){d.C.error("请填写删除原因");return}try{let e=await i.petAPI.adminDeletePost({postId:ed._id,reason:en});e.success?(d.C.success("帖子删除成功"),ei(!1),ec(null),ex(""),e3()):d.C.error(e.message||"删除失败")}catch(e){console.error("删除帖子失败:",e),d.C.error(e.message||"删除帖子失败")}},sl=e=>{let s={active:{label:"投放中",color:"bg-green-100 text-green-800"},paused:{label:"已暂停",color:"bg-yellow-100 text-yellow-800"},expired:{label:"已过期",color:"bg-red-100 text-red-800"},pending:{label:"待审核",color:"bg-blue-100 text-blue-800"},inactive:{label:"未启用",color:"bg-gray-100 text-gray-800"}},t=s[e]||s.pending;return a.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${t.color}`,children:t.label})},sr=e=>`\xa5${e.toFixed(2)}`,si=e=>`${(100*e).toFixed(2)}%`,sd=e=>{switch(e){case"CONTEST":default:return"\uD83C\uDFC6";case"VOTING":return"\uD83D\uDDF3️";case"DISCUSSION":return"\uD83D\uDCAC"}},sc=e=>{switch(e){case"CONTEST":return"评选竞赛";case"VOTING":return"投票话题";case"DISCUSSION":return"讨论活动";default:return"未知类型"}},sn=e=>{let s={DRAFT:{label:"草稿",color:"bg-yellow-100 text-yellow-800"},ACTIVE:{label:"进行中",color:"bg-green-100 text-green-800"},ENDED:{label:"已结束",color:"bg-blue-100 text-blue-800"},ARCHIVED:{label:"已归档",color:"bg-gray-100 text-gray-800"}},t=s[e]||s.DRAFT;return a.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${t.color}`,children:t.label})},sx=e=>new Date(e).toLocaleDateString("zh-CN"),sm=async()=>{try{if(e1(!0),eL.maxImageSize<=0||eL.maxImageSize>100){d.C.error("图片大小限制必须在1-100MB之间");return}if(eL.maxImagesPerPost<=0||eL.maxImagesPerPost>20){d.C.error("每帖图片数量必须在1-20张之间");return}if(eL.autoReportThreshold<=0||eL.autoReportThreshold>50){d.C.error("自动处罚阈值必须在1-50之间");return}localStorage.setItem("systemSettings",JSON.stringify(eL)),d.C.success("设置保存成功")}catch(e){console.error("保存设置失败:",e),d.C.error("保存设置失败")}finally{e1(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("div",{className:"bg-white border-b border-gray-200 shadow-sm",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{onClick:()=>e.push("/admin"),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(n.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(x.Z,{className:"w-8 h-8 text-blue-600"}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"超级管理员控制台"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["欢迎，",m?.username||"管理员"]})]})]})]}),a.jsx(c.Z,{variant:"outline",onClick:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),e.push("/admin")},children:"退出登录"})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-6",children:[(0,a.jsxs)("div",{className:"flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1 overflow-x-auto",children:[(0,a.jsxs)("button",{onClick:()=>t("dashboard"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"dashboard"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(o,{className:"w-4 h-4"}),a.jsx("span",{children:"数据概览"})]}),(0,a.jsxs)("button",{onClick:()=>t("admins"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"admins"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(h,{className:"w-4 h-4"}),a.jsx("span",{children:"管理员管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("appeals"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"appeals"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(p,{className:"w-4 h-4"}),a.jsx("span",{children:"申诉管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("users"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"users"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(u.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"用户管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("ads"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"ads"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})}),a.jsx("span",{children:"广告管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("posts"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"posts"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"})}),a.jsx("span",{children:"帖子管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("activities"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"activities"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),a.jsx("span",{children:"活动管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("posts"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"posts"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(g,{className:"w-4 h-4"}),a.jsx("span",{children:"内容管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("settings"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"settings"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(j.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"系统设置"})]})]}),"dashboard"===s&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:a.jsx(h,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"总用户数"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:T?.totalUsers||0})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:a.jsx(g,{className:"w-6 h-6 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"总宝贝数"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:T?.totalPosts||0})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:a.jsx(u.Z,{className:"w-6 h-6 text-orange-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"昨日新增用户"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:T?.yesterdayNewUsers||0})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-teal-100 rounded-lg",children:a.jsx(y.Z,{className:"w-6 h-6 text-teal-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"昨日新增宝贝"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:T?.yesterdayNewPosts||0})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-red-100 rounded-lg",children:a.jsx(N.Z,{className:"w-6 h-6 text-red-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"待处理举报"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:T?.totalReports||0})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-yellow-100 rounded-lg",children:a.jsx(p,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"待处理申诉"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:T?.totalAppeals||0})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-indigo-100 rounded-lg",children:a.jsx(b.Z,{className:"w-6 h-6 text-indigo-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"活跃用户"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:T?.activeUsers||0})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-pink-100 rounded-lg",children:a.jsx(v,{className:"w-6 h-6 text-pink-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"在线用户"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:T?.onlineUsers||0})]})]})})]})]}),"admins"===s&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 flex items-center justify-between",children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"管理员列表"}),m?.role==="super_admin"&&(0,a.jsxs)(c.Z,{onClick:()=>Q(!0),className:"flex items-center space-x-2",children:[a.jsx(y.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"创建管理员"})]})]}),$?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),a.jsx("p",{className:"text-gray-500 mt-4",children:"加载中..."})]}):0===L.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx(h,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无管理员"}),a.jsx("p",{className:"text-gray-500",children:"点击上方按钮创建第一个管理员"})]}):a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"管理员信息"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"角色权限"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"创建时间"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:L.filter(e=>"superadminTT"!==e.username).map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.username}),a.jsx("div",{className:"text-sm text-gray-500",children:"管理员账号"})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[a.jsx("div",{className:"text-sm text-gray-900",children:"super_admin"===e.role?"超级管理员":"普通管理员"}),a.jsx("div",{className:"text-sm text-gray-500",children:"super_admin"===e.role?"拥有所有权限":"拥有所有业务权限"})]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"active"===e.status?"bg-green-100 text-green-800":"suspended"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:"active"===e.status?"正常":"suspended"===e.status?"暂停":"禁用"})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString("zh-CN")}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[m?.role==="super_admin"&&!e.is_system_account&&a.jsx("button",{onClick:()=>e8(e._id),className:"text-red-600 hover:text-red-900",title:"删除管理员",children:a.jsx(f.Z,{className:"w-4 h-4"})}),!(m?.role==="super_admin"&&!e.is_system_account)&&a.jsx("span",{className:"text-gray-400 text-sm",children:"无操作权限"})]})})]},e._id))})]})})]}),"appeals"===s&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[a.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"申诉列表"})}),$?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),a.jsx("p",{className:"text-gray-500 mt-4",children:"加载中..."})]}):0===V.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx(p,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无申诉"}),a.jsx("p",{className:"text-gray-500",children:"目前没有需要处理的申诉"})]}):a.jsx("div",{className:"divide-y divide-gray-200",children:V.map(e=>a.jsx("div",{className:"p-6 hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[a.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${ss(e.status)}`,children:st(e.status)}),a.jsx("span",{className:"text-sm text-gray-500",children:"post"===e.type?"帖子申诉":"用户申诉"}),a.jsx("span",{className:"text-sm text-gray-500",children:se(e.created_at)})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-900 mb-2",children:[a.jsx("span",{className:"font-medium",children:"申诉理由："}),e.reason]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["举报ID: ",e.report_id," | 申诉人ID: ",e.appellant_id]}),e.admin_reason&&a.jsx("div",{className:"mt-3 p-3 bg-blue-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-900",children:[a.jsx("span",{className:"font-medium",children:"管理员回复："}),e.admin_reason]})})]}),"pending"===e.status&&(0,a.jsxs)("div",{className:"flex space-x-2 ml-4",children:[(0,a.jsxs)(c.Z,{size:"sm",variant:"outline",onClick:()=>{B(e),X("approved"),W(!0)},disabled:E===e._id,children:[a.jsx(w.Z,{className:"w-4 h-4 mr-1"}),"通过"]}),(0,a.jsxs)(c.Z,{size:"sm",variant:"outline",onClick:()=>{B(e),X("rejected"),W(!0)},disabled:E===e._id,children:[a.jsx(k.Z,{className:"w-4 h-4 mr-1"}),"驳回"]})]})]})},e._id))})]}),"users"===s&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[a.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"用户管理"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("div",{className:"relative",children:a.jsx("input",{type:"text",placeholder:"搜索用户昵称或ID...",value:eF,onChange:e=>eB(e.target.value),className:"w-64 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("label",{className:"text-sm text-gray-600",children:"自动处罚阈值:"}),a.jsx("input",{type:"number",min:"1",max:"50",value:eL.autoReportThreshold,onChange:e=>eA(s=>({...s,autoReportThreshold:parseInt(e.target.value)||10})),className:"w-16 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),a.jsx("span",{className:"text-sm text-gray-500",children:"人"})]})]})]})}),eE?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),a.jsx("p",{className:"text-gray-500 mt-4",children:"加载中..."})]}):a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"用户信息"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"权限状态"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"统计信息"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"最后登录"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:e$.filter(e=>!eF||e.nickname.toLowerCase().includes(eF.toLowerCase())||e._id.toLowerCase().includes(eF.toLowerCase())).map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("img",{src:e.avatar_url,alt:e.nickname,className:"h-10 w-10 rounded-full mr-4"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.nickname}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["ID: ",e._id]})]})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"space-y-1",children:[e.permissions.canPublishPost&&e.permissions.canContact?a.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"正常"}):(0,a.jsxs)("div",{children:[a.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:"受限"}),e.permissions.banReason&&a.jsx("div",{className:"text-xs text-gray-500 mt-1 max-w-xs truncate",children:e.permissions.banReason})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["发布: ",e.permissions.canPublishPost?"✓":"✗"," | 联系: ",e.permissions.canContact?"✓":"✗"]})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{children:["帖子: ",e.posts_count]}),(0,a.jsxs)("div",{className:e.reports_count>5?"text-red-600":"",children:["举报: ",e.reports_count]})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.last_login).toLocaleDateString("zh-CN")}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:a.jsx("button",{onClick:()=>{eO(e),eX(e.permissions),eW(!0)},className:"text-blue-600 hover:text-blue-900 mr-3",children:"管理权限"})})]},e._id))})]})})]}),"posts"===s&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[a.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"帖子管理"})}),ea?(0,a.jsxs)("div",{className:"p-6 text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),a.jsx("p",{className:"mt-2 text-gray-500",children:"加载中..."})]}):0===es.length?(0,a.jsxs)("div",{className:"p-6 text-center",children:[a.jsx(g,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无帖子"}),a.jsx("p",{className:"text-gray-500",children:"还没有任何帖子"})]}):a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"帖子信息"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"作者"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"统计"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"发布时间"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:es.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.images&&e.images[0]&&a.jsx("img",{src:e.images[0],alt:e.title,className:"h-12 w-12 rounded-lg object-cover mr-4"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-gray-900 max-w-xs truncate",children:e.title}),a.jsx("div",{className:"text-sm text-gray-500 max-w-xs truncate",children:e.description}),(0,a.jsxs)("div",{className:"text-xs text-gray-400",children:[e.category," • \xa5",e.price]})]})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("img",{src:e.author_info?.avatar_url||"/default-avatar.png",alt:e.author_info?.nickname,className:"h-8 w-8 rounded-full mr-2"}),a.jsx("div",{className:"text-sm text-gray-900",children:e.author_info?.nickname||"未知用户"})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{children:["\uD83D\uDC4D ",e.likes_count]}),(0,a.jsxs)("div",{children:["\uD83D\uDC96 ",e.wants_count]}),(0,a.jsxs)("div",{children:["⭐ ",e.avg_rating.toFixed(1)]}),e.reports_count>0&&(0,a.jsxs)("div",{className:"text-red-600",children:["\uD83D\uDEA8 ",e.reports_count]})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.timeAgo}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:a.jsx("button",{onClick:()=>{ec(e),ei(!0)},className:"text-red-600 hover:text-red-900 mr-3",children:"删除"})})]},e._id))})]})})]}),"ads"===s&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[a.jsx("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:a.jsx(C.Z,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"总展示量"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:em.reduce((e,s)=>e+s.impressions,0).toLocaleString()})]})]})}),a.jsx("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:a.jsx(o,{className:"w-6 h-6 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"总点击量"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:em.reduce((e,s)=>e+s.clicks,0).toLocaleString()})]})]})}),a.jsx("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-yellow-100 rounded-lg",children:a.jsx(Z.Z,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"总收益"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:sr(em.reduce((e,s)=>e+s.spent,0))})]})]})}),a.jsx("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:a.jsx(b.Z,{className:"w-6 h-6 text-purple-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"活跃广告"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:em.filter(e=>"active"===e.status).length})]})]})})]}),a.jsx("div",{className:"border-b border-gray-200",children:a.jsx("nav",{className:"-mb-px flex space-x-8",children:[{key:"ads",label:"广告列表",icon:C.Z},{key:"positions",label:"广告位管理",icon:o},{key:"statistics",label:"数据统计",icon:Z.Z}].map(e=>(0,a.jsxs)("button",{onClick:()=>ey(e.key),className:`${ej===e.key?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`,children:[a.jsx(e.icon,{className:"w-5 h-5"}),a.jsx("span",{children:e.label})]},e.key))})}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("div",{children:a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"ads"===ej?"广告列表":"positions"===ej?"广告位管理":"数据统计"})}),"ads"===ej&&(0,a.jsxs)(c.Z,{onClick:()=>eb(!0),className:"flex items-center space-x-2",children:[a.jsx(y.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"创建广告"})]})]}),"ads"===ej&&a.jsx("div",{className:"bg-white shadow rounded-lg",children:eu?(0,a.jsxs)("div",{className:"p-6 text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),a.jsx("p",{className:"mt-2 text-gray-500",children:"加载中..."})]}):a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"广告信息"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"广告位"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"数据"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"收益"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:0===em.length?a.jsx("tr",{children:a.jsx("td",{colSpan:6,className:"px-6 py-12 text-center text-gray-500",children:"暂无广告数据"})}):em.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.image_url&&a.jsx("img",{src:e.image_url,alt:e.title,className:"h-10 w-10 rounded object-cover mr-3"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.title}),a.jsx("div",{className:"text-sm text-gray-500",children:e.advertiser_name})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[a.jsx("div",{className:"text-sm text-gray-900",children:e.position_name}),a.jsx("div",{className:"text-sm text-gray-500",children:e.ad_type})]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:sl(e.status)}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["展示: ",e.impressions.toLocaleString()]}),(0,a.jsxs)("div",{children:["点击: ",e.clicks.toLocaleString()]}),(0,a.jsxs)("div",{children:["CTR: ",si(e.ctr)]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["预算: ",sr(e.budget)]}),(0,a.jsxs)("div",{children:["已花费: ",sr(e.spent)]})]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{className:"text-blue-600 hover:text-blue-900",children:a.jsx(C.Z,{className:"w-4 h-4"})}),a.jsx("button",{className:"text-green-600 hover:text-green-900",children:a.jsx(S.Z,{className:"w-4 h-4"})}),a.jsx("button",{className:"text-yellow-600 hover:text-yellow-900",children:"active"===e.status?a.jsx(_.Z,{className:"w-4 h-4"}):a.jsx(b.Z,{className:"w-4 h-4"})}),a.jsx("button",{className:"text-red-600 hover:text-red-900",children:a.jsx(f.Z,{className:"w-4 h-4"})})]})})]},e._id))})]})})}),"positions"===ej&&a.jsx("div",{className:"bg-white shadow rounded-lg",children:a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"广告位信息"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"位置"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"尺寸"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"类型"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:eh.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name}),a.jsx("div",{className:"text-sm text-gray-500",children:e.description})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[a.jsx("div",{className:"text-sm text-gray-900",children:e.page}),a.jsx("div",{className:"text-sm text-gray-500",children:e.location})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.width," \xd7 ",e.height]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.ad_type}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:sl(e.status)}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{className:"text-green-600 hover:text-green-900",children:a.jsx(S.Z,{className:"w-4 h-4"})}),a.jsx("button",{className:"text-yellow-600 hover:text-yellow-900",children:"active"===e.status?a.jsx(_.Z,{className:"w-4 h-4"}):a.jsx(b.Z,{className:"w-4 h-4"})})]})})]},e.position_id))})]})})}),"statistics"===ej&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"用户体验策略"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h4",{className:"font-medium text-gray-900",children:"广告频率控制"}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"每日最大展示次数"}),a.jsx("input",{type:"number",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",defaultValue:10,min:1,max:50})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最小展示间隔（分钟）"}),a.jsx("input",{type:"number",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",defaultValue:30,min:5,max:120})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",id:"respectUserChoice",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",defaultChecked:!0}),a.jsx("label",{htmlFor:"respectUserChoice",className:"ml-2 block text-sm text-gray-900",children:"尊重用户隐藏选择"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",id:"adaptiveFrequency",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",defaultChecked:!0}),a.jsx("label",{htmlFor:"adaptiveFrequency",className:"ml-2 block text-sm text-gray-900",children:"自适应展示频率"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h4",{className:"font-medium text-gray-900",children:"广告位策略"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-gray-900",children:"首页横幅"}),a.jsx("div",{className:"text-sm text-gray-600",children:"用户友好度：高"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"启用"}),a.jsx("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-gray-900",children:"信息流广告"}),a.jsx("div",{className:"text-sm text-gray-600",children:"用户友好度：中"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"启用"}),a.jsx("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-gray-900",children:"详情页底部"}),a.jsx("div",{className:"text-sm text-gray-600",children:"用户友好度：高"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"启用"}),a.jsx("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-red-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-gray-900",children:"启动弹窗"}),a.jsx("div",{className:"text-sm text-red-600",children:"用户友好度：低"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"禁用"}),a.jsx("input",{type:"checkbox",className:"h-4 w-4 text-blue-600"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium text-gray-900",children:"用户体验建议"}),a.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"基于用户行为数据的智能推荐"})]}),a.jsx(c.Z,{className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:"保存设置"})]}),(0,a.jsxs)("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),a.jsx("span",{className:"text-sm font-medium text-green-800",children:"推荐"})]}),a.jsx("p",{className:"text-sm text-green-700 mt-1",children:"原生信息流广告，用户接受度高"})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-2 h-2 bg-yellow-500 rounded-full mr-2"}),a.jsx("span",{className:"text-sm font-medium text-yellow-800",children:"谨慎"})]}),a.jsx("p",{className:"text-sm text-yellow-700 mt-1",children:"横幅广告需要控制频率"})]}),(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),a.jsx("span",{className:"text-sm font-medium text-red-800",children:"避免"})]}),a.jsx("p",{className:"text-sm text-red-700 mt-1",children:"弹窗广告容易引起用户反感"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"广告效果分析"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[a.jsx("div",{className:"text-2xl font-bold text-blue-600",children:"92%"}),a.jsx("div",{className:"text-sm text-gray-600",children:"用户满意度"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[a.jsx("div",{className:"text-2xl font-bold text-green-600",children:"3.2%"}),a.jsx("div",{className:"text-sm text-gray-600",children:"平均点击率"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-yellow-50 rounded-lg",children:[a.jsx("div",{className:"text-2xl font-bold text-yellow-600",children:"15s"}),a.jsx("div",{className:"text-sm text-gray-600",children:"平均停留时间"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[a.jsx("div",{className:"text-2xl font-bold text-purple-600",children:"8%"}),a.jsx("div",{className:"text-sm text-gray-600",children:"广告隐藏率"})]})]})]})]})]}),"activities"===s&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"活动管理"}),a.jsx("p",{className:"text-gray-600",children:"管理社区活动和系统配置"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)(c.Z,{onClick:()=>e_(!0),variant:"outline",className:"flex items-center space-x-2",children:[a.jsx(j.Z,{className:"h-4 w-4"}),a.jsx("span",{children:"系统设置"})]}),(0,a.jsxs)(c.Z,{onClick:()=>eM(!0),className:"flex items-center space-x-2",children:[a.jsx(y.Z,{className:"h-4 w-4"}),a.jsx("span",{children:"创建活动"})]})]})]}),a.jsx("div",{className:`p-4 rounded-lg ${eC?.enabled?"bg-green-50 border border-green-200":"bg-yellow-50 border border-yellow-200"}`,children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:`h-3 w-3 rounded-full mr-3 ${eC?.enabled?"bg-green-500":"bg-yellow-500"}`}),(0,a.jsxs)("span",{className:`font-medium ${eC?.enabled?"text-green-800":"text-yellow-800"}`,children:["活动系统状态：",eC?.enabled?"已启用":"已禁用"]}),!eC?.enabled&&a.jsx("span",{className:"ml-2 text-yellow-700",children:"（用户端不显示活动入口）"})]})}),(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow flex space-x-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"状态筛选"}),(0,a.jsxs)("select",{value:eD,onChange:e=>eP(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 text-sm",children:[a.jsx("option",{value:"all",children:"全部状态"}),a.jsx("option",{value:"DRAFT",children:"草稿"}),a.jsx("option",{value:"ACTIVE",children:"进行中"}),a.jsx("option",{value:"ENDED",children:"已结束"}),a.jsx("option",{value:"ARCHIVED",children:"已归档"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"类型筛选"}),(0,a.jsxs)("select",{value:eT,onChange:e=>ez(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 text-sm",children:[a.jsx("option",{value:"all",children:"全部类型"}),a.jsx("option",{value:"CONTEST",children:"评选竞赛"}),a.jsx("option",{value:"VOTING",children:"投票话题"}),a.jsx("option",{value:"DISCUSSION",children:"讨论活动"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[a.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"活动列表"})}),ew?(0,a.jsxs)("div",{className:"p-6 text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),a.jsx("p",{className:"mt-2 text-gray-500",children:"加载中..."})]}):a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"活动信息"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"类型"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"时间"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"参与数据"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:0===ev.length?a.jsx("tr",{children:a.jsx("td",{colSpan:6,className:"px-6 py-12 text-center text-gray-500",children:"暂无活动数据"})}):ev.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-gray-900 max-w-xs truncate",children:e.title}),a.jsx("div",{className:"text-sm text-gray-500 max-w-xs truncate",children:e.description})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("span",{className:"text-lg mr-2",children:sd(e.type)}),a.jsx("span",{className:"text-sm text-gray-900",children:sc(e.type)})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:sn(e.status)}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["开始：",sx(e.start_time)]}),(0,a.jsxs)("div",{children:["结束：",sx(e.end_time)]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["持续 ",e.duration_days," 天"]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["投票：",e.statistics_summary?.total_votes||0]}),(0,a.jsxs)("div",{children:["评论：",e.statistics_summary?.total_comments||0]})]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{onClick:()=>window.open(`/activities/${e._id}`,"_blank"),className:"text-blue-600 hover:text-blue-900",title:"查看活动",children:a.jsx(C.Z,{className:"h-4 w-4"})}),a.jsx("button",{onClick:()=>d.C.info("编辑功能开发中"),className:"text-green-600 hover:text-green-900",title:"编辑活动",children:a.jsx(S.Z,{className:"h-4 w-4"})}),a.jsx("button",{onClick:()=>{e4({title:"删除活动",message:"确定要删除这个活动吗？此操作不可恢复。",confirmText:"删除",cancelText:"取消",type:"danger",onConfirm:()=>{d.C.info("删除功能开发中")}})},className:"text-red-600 hover:text-red-900",title:"删除活动",children:a.jsx(f.Z,{className:"h-4 w-4"})})]})})]},e._id))})]})})]})]}),"settings"===s&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"系统设置"}),a.jsx("p",{className:"text-gray-600",children:"配置系统参数和规则"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(I.Z,{className:"w-6 h-6 text-blue-600"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"图片上传设置"})]}),a.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"配置用户上传图片的限制和规则"})]}),eV?(0,a.jsxs)("div",{className:"p-6 text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),a.jsx("p",{className:"mt-2 text-gray-500",children:"加载中..."})]}):(0,a.jsxs)("div",{className:"px-6 py-6 space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"单张图片大小限制 (MB)"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("input",{type:"number",min:"1",max:"100",step:"1",value:eL.maxImageSize,onChange:e=>eA(s=>({...s,maxImageSize:parseInt(e.target.value)||1})),className:"block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["当前限制：",eL.maxImageSize,"MB"]})]}),a.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"建议设置在5-30MB之间，过大会影响上传速度"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"每帖最大图片数量"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("input",{type:"number",min:"1",max:"20",step:"1",value:eL.maxImagesPerPost,onChange:e=>eA(s=>({...s,maxImagesPerPost:parseInt(e.target.value)||1})),className:"block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["当前限制：",eL.maxImagesPerPost,"张"]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"支持的图片格式"}),a.jsx("div",{className:"space-y-2",children:["image/jpeg","image/png","image/webp","image/gif"].map(e=>(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:eL.allowedImageTypes.includes(e),onChange:s=>{s.target.checked?eA(s=>({...s,allowedImageTypes:[...s.allowedImageTypes,e]})):eA(s=>({...s,allowedImageTypes:s.allowedImageTypes.filter(s=>s!==e)}))},className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),a.jsx("span",{className:"ml-2 text-sm text-gray-700",children:e.replace("image/","").toUpperCase()})]},e))})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"自动处罚设置"}),a.jsx("p",{className:"text-sm text-gray-500",children:"配置自动处罚的触发条件"})]}),eV?(0,a.jsxs)("div",{className:"px-6 py-8 text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"}),a.jsx("p",{className:"text-gray-500 mt-2",children:"加载中..."})]}):(0,a.jsxs)("div",{className:"px-6 py-4 space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"疑似骗子举报阈值"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("input",{type:"number",min:"1",max:"50",value:eL.autoReportThreshold,onChange:e=>eA(s=>({...s,autoReportThreshold:parseInt(e.target.value)||10})),className:"w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),a.jsx("span",{className:"text-sm text-gray-600",children:"人举报后自动禁止发布和联系权限"})]}),a.jsx("p",{className:"text-xs text-gray-500 mt-1",children:'当用户被举报"疑似骗子"达到此数量时，系统将自动禁止其发布宝贝和联系功能'})]}),a.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx(AlertTriangle,{className:"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-yellow-800",children:[a.jsx("p",{className:"font-medium mb-1",children:"注意事项"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[a.jsx("li",{children:"阈值设置过低可能导致误封，建议设置为5-15人"}),a.jsx("li",{children:"被处罚用户可以通过申诉系统申请恢复权限"}),a.jsx("li",{children:"管理员可以在用户管理中手动调整用户权限"})]})]})]})})]}),(0,a.jsxs)("div",{className:"px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between",children:[(0,a.jsxs)(c.Z,{variant:"outline",onClick:()=>{e4({title:"重置设置",message:"确定要重置为默认设置吗？当前的自定义设置将会丢失。",confirmText:"重置",cancelText:"取消",type:"warning",onConfirm:()=>{eA({maxImageSize:5,maxImagesPerPost:9,allowedImageTypes:["image/jpeg","image/png","image/webp"],autoReportThreshold:10}),d.C.success("已重置为默认设置")}})},className:"flex items-center space-x-2",children:[a.jsx(M.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"重置默认"})]}),(0,a.jsxs)(c.Z,{onClick:sm,loading:e2,disabled:e2,className:"flex items-center space-x-2",children:[a.jsx(D.Z,{className:"w-4 h-4"}),a.jsx("span",{children:e2?"保存中...":"保存设置"})]})]})]})]})]}),K&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"创建管理员"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["用户名 ",a.jsx("span",{className:"text-red-500",children:"*"})]}),a.jsx("input",{type:"text",value:Y.username,onChange:e=>ee(s=>({...s,username:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入用户名"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["密码 ",a.jsx("span",{className:"text-red-500",children:"*"})]}),a.jsx("input",{type:"password",value:Y.password,onChange:e=>ee(s=>({...s,password:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入密码"})]}),a.jsx("div",{children:(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg",children:[a.jsx("div",{className:"text-sm font-medium text-gray-700",children:"角色权限"}),a.jsx("div",{className:"text-sm text-gray-500 mt-1",children:"普通管理员 - 拥有所有业务权限（除删除其他管理员外）"})]})})]}),(0,a.jsxs)("div",{className:"flex space-x-3 mt-6",children:[a.jsx(c.Z,{variant:"outline",onClick:()=>{Q(!1),ee({username:"",password:"",role:"admin",level:1,permissions:[]})},className:"flex-1",children:"取消"}),a.jsx(c.Z,{onClick:e7,className:"flex-1",children:"创建管理员"})]})]})}),G&&F&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"approved"===J?"通过申诉":"驳回申诉"}),(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"申诉内容："}),a.jsx("p",{className:"text-sm bg-gray-100 p-3 rounded-lg",children:F.reason})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"处理说明"}),a.jsx("textarea",{value:U,onChange:e=>O(e.target.value),placeholder:`请说明${"approved"===J?"通过":"驳回"}的理由...`,className:"w-full h-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx(c.Z,{variant:"outline",onClick:()=>{W(!1),B(null),O("")},className:"flex-1",children:"取消"}),a.jsx(c.Z,{onClick:e9,disabled:E===F._id,className:"flex-1",children:E===F._id?"处理中...":"确认"})]})]})}),er&&ed&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"删除帖子"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center mb-3",children:[ed.images&&ed.images[0]&&a.jsx("img",{src:ed.images[0],alt:ed.title,className:"h-16 w-16 rounded-lg object-cover mr-4"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-gray-900",children:ed.title}),a.jsx("div",{className:"text-sm text-gray-500",children:ed.description})]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["删除原因 ",a.jsx("span",{className:"text-red-500",children:"*"})]}),a.jsx("textarea",{value:en,onChange:e=>ex(e.target.value),placeholder:"请输入删除原因...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none",rows:3})]}),a.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[a.jsx("h3",{className:"text-sm font-medium text-red-800",children:"警告"}),(0,a.jsxs)("div",{className:"mt-2 text-sm text-red-700",children:[a.jsx("p",{children:"删除帖子将同时删除："}),(0,a.jsxs)("ul",{className:"list-disc list-inside mt-1",children:[a.jsx("li",{children:"帖子的所有图片文件"}),a.jsx("li",{children:"所有点赞、收藏、评分记录"}),a.jsx("li",{children:"所有相关的举报和联系记录"})]}),a.jsx("p",{className:"mt-2 font-medium",children:"此操作不可撤销！"})]})]})]})})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx(c.Z,{variant:"outline",onClick:()=>{ei(!1),ec(null),ex("")},className:"flex-1",children:"取消"}),a.jsx(c.Z,{onClick:sa,className:"flex-1 bg-red-600 hover:bg-red-700 text-white",disabled:!en.trim(),children:"确认删除"})]})]})}),eK&&eY&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsxs)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${"danger"===eY.type?"bg-red-100":"warning"===eY.type?"bg-yellow-100":"bg-blue-100"}`,children:["danger"===eY.type&&a.jsx("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),"warning"===eY.type&&a.jsx("svg",{className:"w-5 h-5 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),(!eY.type||"info"===eY.type)&&a.jsx("svg",{className:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})]}),a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:eY.title})]}),a.jsx("div",{className:"mb-6",children:a.jsx("p",{className:"text-gray-600",children:eY.message})}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx(c.Z,{variant:"outline",onClick:()=>{eY?.onCancel&&eY.onCancel(),eQ(!1),e0(null)},className:"flex-1",children:eY.cancelText||"取消"}),a.jsx(c.Z,{onClick:()=>{eY?.onConfirm&&eY.onConfirm(),eQ(!1),e0(null)},className:`flex-1 ${"danger"===eY.type?"bg-red-600 hover:bg-red-700 text-white":"warning"===eY.type?"bg-yellow-600 hover:bg-yellow-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white"}`,children:eY.confirmText||"确认"})]})]})})]})}},5264:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(10326);t(17577);var l=t(20603);function r({children:e}){return(0,a.jsxs)("div",{className:"admin-layout",children:[a.jsx(l.ToastProvider,{}),e]})}t(23824)},99837:(e,s,t)=>{"use strict";t.d(s,{Z:()=>c});var a=t(10326),l=t(17577),r=t.n(l),i=t(28295);let d=r().forwardRef(({className:e,variant:s="primary",size:t="md",loading:l=!1,icon:r,children:d,disabled:c,...n},x)=>(0,a.jsxs)("button",{className:(0,i.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[s],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[t],e),ref:x,disabled:c||l,...n,children:[l&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[a.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),a.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!l&&r&&a.jsx("span",{className:"mr-2",children:r}),d]}));d.displayName="Button";let c=d},13624:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\admin\dashboard\page.tsx#default`)},9457:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\admin\layout.tsx#default`)},23824:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[276,201,240],()=>t(32868));module.exports=a})();