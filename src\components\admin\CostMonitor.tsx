'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { DollarSign, HardDrive, Download, TrendingUp, AlertTriangle } from 'lucide-react';

interface CostData {
  storage: {
    used: number;
    limit: number;
    cost: number;
  };
  traffic: {
    used: number;
    limit: number;
    cost: number;
  };
  requests: {
    used: number;
    limit: number;
    cost: number;
  };
  totalCost: number;
  projectedCost: number;
}

const CostMonitor: React.FC = () => {
  const [costData, setCostData] = useState<CostData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCostData();
    const interval = setInterval(loadCostData, 60000); // 每分钟更新
    return () => clearInterval(interval);
  }, []);

  const loadCostData = async () => {
    try {
      // 模拟数据，实际应该从API获取
      const mockData: CostData = {
        storage: {
          used: 3.2, // GB
          limit: 5,  // GB
          cost: 0    // 免费额度内
        },
        traffic: {
          used: 12.5, // GB
          limit: 5,   // GB
          cost: (12.5 - 5) * 0.18 // ¥1.35
        },
        requests: {
          used: 850000,  // 次
          limit: 10000000, // 次
          cost: 0        // 免费额度内
        },
        totalCost: 1.35,
        projectedCost: 4.05 // 按当前增长率预测月底成本
      };

      setCostData(mockData);
      setLoading(false);
    } catch (error) {
      console.error('加载成本数据失败:', error);
      setLoading(false);
    }
  };

  const formatSize = (bytes: number): string => {
    if (bytes >= 1024 * 1024 * 1024) {
      return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
    }
    if (bytes >= 1024 * 1024) {
      return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
    }
    return `${(bytes / 1024).toFixed(2)} KB`;
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const getUsageColor = (used: number, limit: number): string => {
    const percentage = (used / limit) * 100;
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 70) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getProgressColor = (used: number, limit: number): string => {
    const percentage = (used / limit) * 100;
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
            <div className="h-3 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!costData) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="text-center text-gray-500">
          <AlertTriangle className="w-8 h-8 mx-auto mb-2" />
          <p>无法加载成本数据</p>
        </div>
      </div>
    );
  }

  const chartData = [
    { name: '存储', 免费额度: costData.storage.limit, 已使用: costData.storage.used },
    { name: '流量', 免费额度: costData.traffic.limit, 已使用: costData.traffic.used },
    { name: '请求', 免费额度: costData.requests.limit / 1000000, 已使用: costData.requests.used / 1000000 }
  ];

  const costBreakdown = [
    { name: '存储费用', value: costData.storage.cost, color: '#8884d8' },
    { name: '流量费用', value: costData.traffic.cost, color: '#82ca9d' },
    { name: '请求费用', value: costData.requests.cost, color: '#ffc658' }
  ];

  return (
    <div className="space-y-6">
      {/* 总览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <DollarSign className="w-8 h-8 text-green-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">本月成本</p>
              <p className="text-2xl font-bold text-gray-900">¥{costData.totalCost.toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <TrendingUp className="w-8 h-8 text-blue-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">预测成本</p>
              <p className="text-2xl font-bold text-gray-900">¥{costData.projectedCost.toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <HardDrive className="w-8 h-8 text-purple-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">存储使用</p>
              <p className={`text-2xl font-bold ${getUsageColor(costData.storage.used, costData.storage.limit)}`}>
                {((costData.storage.used / costData.storage.limit) * 100).toFixed(1)}%
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <Download className="w-8 h-8 text-orange-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">流量使用</p>
              <p className={`text-2xl font-bold ${getUsageColor(costData.traffic.used, costData.traffic.limit)}`}>
                {((costData.traffic.used / costData.traffic.limit) * 100).toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 使用量详情 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4">资源使用情况</h3>
          
          <div className="space-y-4">
            {/* 存储使用 */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">存储空间</span>
                <span className="text-sm text-gray-600">
                  {costData.storage.used.toFixed(2)}GB / {costData.storage.limit}GB
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${getProgressColor(costData.storage.used, costData.storage.limit)}`}
                  style={{ width: `${Math.min((costData.storage.used / costData.storage.limit) * 100, 100)}%` }}
                ></div>
              </div>
              {costData.storage.cost > 0 && (
                <p className="text-xs text-red-600 mt-1">超出免费额度，费用: ¥{costData.storage.cost.toFixed(2)}</p>
              )}
            </div>

            {/* 流量使用 */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">CDN流量</span>
                <span className="text-sm text-gray-600">
                  {costData.traffic.used.toFixed(2)}GB / {costData.traffic.limit}GB
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${getProgressColor(costData.traffic.used, costData.traffic.limit)}`}
                  style={{ width: `${Math.min((costData.traffic.used / costData.traffic.limit) * 100, 100)}%` }}
                ></div>
              </div>
              {costData.traffic.cost > 0 && (
                <p className="text-xs text-red-600 mt-1">超出免费额度，费用: ¥{costData.traffic.cost.toFixed(2)}</p>
              )}
            </div>

            {/* 请求使用 */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">API请求</span>
                <span className="text-sm text-gray-600">
                  {formatNumber(costData.requests.used)} / {formatNumber(costData.requests.limit)}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${getProgressColor(costData.requests.used, costData.requests.limit)}`}
                  style={{ width: `${(costData.requests.used / costData.requests.limit) * 100}%` }}
                ></div>
              </div>
              {costData.requests.cost > 0 && (
                <p className="text-xs text-red-600 mt-1">超出免费额度，费用: ¥{costData.requests.cost.toFixed(2)}</p>
              )}
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4">使用量对比</h3>
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="免费额度" fill="#e5e7eb" />
              <Bar dataKey="已使用" fill="#3b82f6" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* 成本分析 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4">成本构成</h3>
          <ResponsiveContainer width="100%" height={200}>
            <PieChart>
              <Pie
                data={costBreakdown.filter(item => item.value > 0)}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                label={({ name, value }) => `${name}: ¥${(value || 0).toFixed(2)}`}
              >
                {costBreakdown.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4">优化建议</h3>
          <div className="space-y-3">
            {costData.storage.used / costData.storage.limit > 0.8 && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded">
                <p className="text-sm text-yellow-800">
                  <strong>存储空间即将用完</strong><br />
                  建议启用图片压缩或迁移到云存储
                </p>
              </div>
            )}
            
            {costData.traffic.used > costData.traffic.limit && (
              <div className="p-3 bg-red-50 border border-red-200 rounded">
                <p className="text-sm text-red-800">
                  <strong>流量已超出免费额度</strong><br />
                  建议启用CDN缓存或图片懒加载
                </p>
              </div>
            )}
            
            {costData.totalCost === 0 && (
              <div className="p-3 bg-green-50 border border-green-200 rounded">
                <p className="text-sm text-green-800">
                  <strong>当前在免费额度内</strong><br />
                  继续保持当前的使用模式
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CostMonitor;
