(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[601],{8775:function(e,t,s){Promise.resolve().then(s.bind(s,98426))},98426:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return E}});var a=s(57437),r=s(2265),i=s(99376),n=s(32660),l=s(87769),c=s(42208),o=s(88997),d=s(86595),u=s(82718),x=s(56266),h=s(68919),m=s(46211),g=s(92369),v=s(91723),f=s(83774),p=s(19764),j=s(10476),y=s(68661),b=s(9356),w=s(98011),N=s(98702),_=s(56334);let C={1:"普通",2:"极品",3:"神品",4:"仙品",5:"圣品"},k={1:"疑似虐待动物内容",2:"图片与描述严重不符",3:"重复发布相同内容",4:"图片质量极差无法识别",5:"不当内容（暴力色情等）",6:"广告或垃圾信息",7:"其他问题"};var R=e=>{var t,s,i,n,l,c,o,u,x,h,m,g,v,f,p,j;let{isOpen:k,onClose:R,post:S,onSuccess:I}=e,[P,Z]=(0,r.useState)(0),[O,z]=(0,r.useState)(!1);(0,r.useEffect)(()=>{var e,t;(null===(e=S.user_interactions)||void 0===e?void 0:e.hasRated)&&(null===(t=S.user_interactions)||void 0===t?void 0:t.userRating)&&Z(S.user_interactions.userRating)},[S.user_interactions]);let E=()=>{var e,t;(null===(e=S.user_interactions)||void 0===e?void 0:e.hasRated)&&(null===(t=S.user_interactions)||void 0===t?void 0:t.userRating)?Z(S.user_interactions.userRating):Z(0),z(!1)},A=()=>{E(),R()},J=async()=>{var e;if(0===P){b.C.warning("请选择评分");return}if(null===(e=S.user_interactions)||void 0===e?void 0:e.hasRated){b.C.warning("您已经评分过了");return}try{z(!0);let e=await w.petAPI.ratePet({postId:S._id,rating:P});if(e.success){b.C.success("评分成功！");try{let e=JSON.parse(localStorage.getItem("userRatedPosts")||"[]");e.includes(S._id)||(e.unshift(S._id),e.length>100&&e.splice(100),localStorage.setItem("userRatedPosts",JSON.stringify(e)))}catch(e){console.warn("保存评分记录到本地存储失败:",e)}I()}else b.C.error(e.message||"评分失败")}catch(e){b.C.error(e.message||"评分失败")}finally{z(!1)}};return(null===(t=S.user_interactions)||void 0===t?void 0:t.hasRated)?(0,a.jsx)(N.u_,{isOpen:k,onClose:A,title:"评分统计",size:"sm",children:(0,a.jsx)(N.fe,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-gray-900 mb-2",children:S.avg_rating.toFixed(1)}),(0,a.jsx)("div",{className:"flex items-center justify-center space-x-1 mb-2",children:[1,2,3,4,5].map(e=>(0,a.jsx)(d.Z,{className:(0,y.cn)("h-6 w-6",e<=Math.round(S.avg_rating)?"text-yellow-500 fill-current":"text-gray-300")},e))}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["基于 ",S.ratings_count," 个评分"]})]}),(0,a.jsx)("div",{className:"space-y-2",children:[5,4,3,2,1].map(e=>{var t;let s=(null===(t=S.rating_stats)||void 0===t?void 0:t[e])||0,r=S.ratings_count>0?s/S.ratings_count*100:0;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1 w-16",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:e}),(0,a.jsx)(d.Z,{className:"h-3 w-3 text-yellow-500 fill-current"})]}),(0,a.jsx)("div",{className:"flex-1 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-yellow-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(r,"%")}})}),(0,a.jsx)("span",{className:"text-xs text-gray-500 w-8",children:s})]},e)})}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"您的评分"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"flex items-center space-x-1",children:[1,2,3,4,5].map(e=>{var t;return(0,a.jsx)(d.Z,{className:(0,y.cn)("h-5 w-5",e<=((null===(t=S.user_interactions)||void 0===t?void 0:t.userRating)||0)?"text-yellow-500 fill-current":"text-gray-300")},e)})}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:C[null===(j=S.user_interactions)||void 0===j?void 0:j.userRating]})]})]})]})})}):(0,a.jsx)(N.u_,{isOpen:k,onClose:A,title:"为这只宠物评分",size:"sm",children:(0,a.jsx)(N.fe,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:S.title})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"点击星星为这只宠物评分"}),(0,a.jsx)("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[1,2,3,4,5].map(e=>{var t,s;return(0,a.jsx)("button",{onClick:()=>{var t;(null===(t=S.user_interactions)||void 0===t?void 0:t.hasRated)||Z(e)},className:(0,y.cn)("transition-transform duration-200 focus:outline-none",(null===(t=S.user_interactions)||void 0===t?void 0:t.hasRated)?"cursor-not-allowed":"hover:scale-105 active:scale-95 cursor-pointer"),disabled:O||(null===(s=S.user_interactions)||void 0===s?void 0:s.hasRated),children:(0,a.jsx)(d.Z,{className:(0,y.cn)("h-8 w-8 transition-colors duration-200",e<=P?"text-yellow-500 fill-current":"text-gray-300")})},e)})}),P>0&&(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-lg font-medium text-gray-900",children:C[P]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[(null===(s=S.user_interactions)||void 0===s?void 0:s.hasRated)?"您的评价：":"",P," 星评分"]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:(null===(i=S.user_interactions)||void 0===i?void 0:i.hasRated)?"评分统计":"评分标准"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"⭐ 普通"}),(null===(n=S.user_interactions)||void 0===n?void 0:n.hasRated)&&(0,a.jsxs)("span",{children:[(null===(l=S.rating_stats)||void 0===l?void 0:l[1])||0,"人"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"⭐⭐ 极品"}),(null===(c=S.user_interactions)||void 0===c?void 0:c.hasRated)&&(0,a.jsxs)("span",{children:[(null===(o=S.rating_stats)||void 0===o?void 0:o[2])||0,"人"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"⭐⭐⭐ 神品"}),(null===(u=S.user_interactions)||void 0===u?void 0:u.hasRated)&&(0,a.jsxs)("span",{children:[(null===(x=S.rating_stats)||void 0===x?void 0:x[3])||0,"人"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"⭐⭐⭐⭐ 仙品"}),(null===(h=S.user_interactions)||void 0===h?void 0:h.hasRated)&&(0,a.jsxs)("span",{children:[(null===(m=S.rating_stats)||void 0===m?void 0:m[4])||0,"人"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"⭐⭐⭐⭐⭐ 圣品"}),(null===(g=S.user_interactions)||void 0===g?void 0:g.hasRated)&&(0,a.jsxs)("span",{children:[(null===(v=S.rating_stats)||void 0===v?void 0:v[5])||0,"人"]})]})]})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(_.Z,{variant:"outline",onClick:A,className:"flex-1",disabled:O,children:(null===(f=S.user_interactions)||void 0===f?void 0:f.hasRated)?"关闭":"取消"}),!(null===(p=S.user_interactions)||void 0===p?void 0:p.hasRated)&&(0,a.jsx)(_.Z,{onClick:J,loading:O,className:"flex-1",disabled:0===P,children:"提交评分"})]})]})})})},S=e=>{let{isOpen:t,onClose:s,postId:i,onSuccess:n}=e,[l,c]=(0,r.useState)(0),[o,d]=(0,r.useState)(!1),u=()=>{c(0),d(!1)},x=()=>{u(),s()},h=async()=>{if(0===l){b.C.warning("请选择举报原因");return}try{d(!0);let e=await w.petAPI.reportPost({postId:i,reason:k[l]});e.success?n():b.C.error(e.message||"举报失败")}catch(e){b.C.error(e.message||"举报失败")}finally{d(!1)}},m=Object.entries(k).map(e=>{let[t,s]=e;return{id:parseInt(t),label:s}});return(0,a.jsx)(N.u_,{isOpen:t,onClose:x,title:"举报帖子",size:"sm",children:(0,a.jsx)(N.fe,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-sm text-yellow-800",children:"请选择举报原因，我们会认真处理每一个举报。恶意举报可能会影响您的账户信誉。"})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"请选择举报原因："}),(0,a.jsx)("div",{className:"space-y-2",children:m.map(e=>(0,a.jsxs)("label",{className:(0,y.cn)("flex items-center p-3 rounded-lg border cursor-pointer transition-colors",l===e.id?"border-red-500 bg-red-50":"border-gray-200 hover:bg-gray-50"),children:[(0,a.jsx)("input",{type:"radio",name:"reportReason",value:e.id,checked:l===e.id,onChange:()=>c(e.id),className:"sr-only"}),(0,a.jsx)("div",{className:(0,y.cn)("w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center",l===e.id?"border-red-500 bg-red-500":"border-gray-300"),children:l===e.id&&(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-white"})}),(0,a.jsx)("span",{className:(0,y.cn)("text-sm",l===e.id?"text-red-700 font-medium":"text-gray-700"),children:e.label})]},e.id))})]}),1===l&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-sm text-red-800 font-medium",children:"⚠️ 非法野生动物交易是违法行为，我们将严肃处理此类举报。"})}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(_.Z,{variant:"outline",onClick:x,className:"flex-1",disabled:o,children:"取消"}),(0,a.jsx)(_.Z,{variant:"danger",onClick:h,loading:o,className:"flex-1",disabled:0===l,children:"提交举报"})]})]})})})},I=s(95578),P=s(59604),Z=s(98734),O=s(44995);s(7354),s(76889),s(86968),s(23469);let z=e=>{let t=new Date(e),s=t.getFullYear(),a=t.getMonth()+1,r=t.getDate();return"".concat(s,"/").concat(a,"/").concat(r)};function E(){var e,t,s,N;let _=(0,i.useRouter)(),C=(0,i.useSearchParams)().get("id"),{isLoggedIn:k,user:E}=(0,Z.a)(),[A,J]=(0,r.useState)(null),[L,D]=(0,r.useState)(!0),[B,M]=(0,r.useState)(!1),[T,F]=(0,r.useState)(!1),[U,W]=(0,r.useState)(!1),[q,G]=(0,r.useState)(0),[H,K]=(0,r.useState)(!1),[V,X]=(0,r.useState)(!1),[Y,Q]=(0,r.useState)(!1);(0,r.useEffect)(()=>{if(!C){_.push("/");return}$()},[C]);let $=async()=>{try{D(!0);let i=await w.petAPI.getPostDetail({postId:C});if(i.success){var e,t,s,a,r;if(J(i.data),F((null===(e=i.data.user_interactions)||void 0===e?void 0:e.hasLiked)||!1),G((null===(t=i.data.user_interactions)||void 0===t?void 0:t.userRating)||0),(null===(s=i.data.user_interactions)||void 0===s?void 0:s.hasRated)&&(null===(a=i.data.user_interactions)||void 0===a?void 0:a.userRating)>0)try{let e=JSON.parse(localStorage.getItem("userRatedPosts")||"[]");e.includes(i.data._id)||(e.unshift(i.data._id),e.length>100&&e.splice(100),localStorage.setItem("userRatedPosts",JSON.stringify(e)))}catch(e){console.warn("同步评分记录到本地存储失败:",e)}i.data.author&&P.KX.addBrowseRecord(i.data._id,i.data.breed||"宠物发布",i.data.author.nickname||"匿名用户",i.data.author._id,null===(r=i.data.images)||void 0===r?void 0:r[0])}else b.C.error("加载失败："+i.message),_.push("/")}catch(e){console.error("加载帖子详情失败:",e),b.C.error("加载失败，请重试"),_.push("/")}finally{D(!1)}},ee=async()=>{try{let r=await w.petAPI.getPostDetail({postId:C});if(r.success){var e,t,s,a;if(J(r.data),F((null===(e=r.data.user_interactions)||void 0===e?void 0:e.hasLiked)||!1),G((null===(t=r.data.user_interactions)||void 0===t?void 0:t.userRating)||0),(null===(s=r.data.user_interactions)||void 0===s?void 0:s.hasRated)&&(null===(a=r.data.user_interactions)||void 0===a?void 0:a.userRating)>0)try{let e=JSON.parse(localStorage.getItem("userRatedPosts")||"[]");e.includes(r.data._id)||(e.unshift(r.data._id),e.length>100&&e.splice(100),localStorage.setItem("userRatedPosts",JSON.stringify(e)))}catch(e){console.warn("同步评分记录到本地存储失败:",e)}}}catch(e){console.error("刷新帖子数据失败:",e)}},et=()=>{M(!B)},es=async()=>{if(A){if(!k||!E){b.C.error("请先登录");return}try{let e=await w.petAPI.toggleLike({postId:A._id});e.success?(F(!T),J(e=>e?{...e,likes_count:(e.likes_count||0)+(T?-1:1)}:null),b.C.success(T?"取消点赞":"点赞成功")):b.C.error(e.message||"操作失败")}catch(e){console.error("点赞操作失败:",e),b.C.error("操作失败，请重试")}}},ea=()=>{let e=window.location.href,t=(null==A?void 0:A.breed)||"宠物交易平台",s="".concat(t," - 来看看这个可爱的宠物吧！");navigator.share?navigator.share({title:t,text:s,url:e}).then(()=>{b.C.success("分享成功")}).catch(t=>{"AbortError"!==t.name&&er(e,s)}):er(e,s)},er=(e,t)=>{let s="".concat(t,"\n").concat(e);navigator.clipboard.writeText(s).then(()=>{b.C.success("分享内容已复制到剪贴板")}).catch(()=>{b.C.error("复制失败，请手动复制链接")})},ei=async e=>{try{let t=await w.petAPI.updateProfile(e);if(t.success)b.C.success("资料更新成功"),e.contactInfo&&E&&localStorage.setItem("contact_".concat(E._id),JSON.stringify(e.contactInfo)),void 0!==e.address&&E&&localStorage.setItem("address_".concat(E._id),e.address);else throw Error(t.message||"更新失败")}catch(e){throw b.C.error(e.message||"更新失败，请重试"),e}},en=async()=>{if(!k||!E){b.C.error("请先登录");return}let e=null;try{let t=localStorage.getItem("contact_".concat(E._id));t&&(e=JSON.parse(t))}catch(e){console.log("获取联系方式失败")}if(!e||!e.value){b.C.warning("请先完善联系方式才能使用联系功能"),Q(!0);return}if(A&&A.contact_info)try{var t;let s=await w.petAPI.sendContactNotification({postId:A._id,authorId:(null===(t=A.author)||void 0===t?void 0:t._id)||"",userContact:e,authorContact:A.contact_info});s.success?b.C.success("联系方式已发送至通知"):b.C.error(s.message||"发送失败")}catch(e){console.error("发送联系通知失败:",e),b.C.error("发送失败，请重试")}else b.C.error("对方未提供联系方式")};return L?(0,a.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-gray-500",children:"加载中..."})}):A?(0,a.jsxs)("div",{className:"min-h-screen bg-white relative",onClick:()=>W(!1),children:[(0,a.jsxs)("div",{className:(0,y.cn)("fixed top-4 left-4 z-30 flex space-x-2 transition-opacity duration-300",B?"opacity-0 pointer-events-none":"opacity-100"),children:[(0,a.jsx)("button",{onClick:()=>_.back(),className:"w-10 h-10 rounded-full bg-white/90 backdrop-blur-sm border border-gray-200 text-gray-700 flex items-center justify-center hover:bg-gray-100 transition-all duration-200 shadow-sm",children:(0,a.jsx)(n.Z,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:et,className:"w-10 h-10 rounded-full bg-white/90 backdrop-blur-sm border border-gray-200 text-gray-700 flex items-center justify-center hover:bg-gray-100 transition-all duration-200 shadow-sm",title:B?"退出清屏":"清屏模式",children:(0,a.jsx)(l.Z,{className:"h-5 w-5"})})]}),B&&(0,a.jsx)("div",{className:"fixed top-4 left-16 z-30",children:(0,a.jsx)("button",{onClick:et,className:"w-10 h-10 rounded-full bg-black/50 backdrop-blur-sm border border-white/20 text-white flex items-center justify-center hover:bg-black/70 transition-all duration-200 shadow-sm",title:"退出清屏",children:(0,a.jsx)(c.Z,{className:"h-5 w-5"})})}),(0,a.jsx)("div",{className:"relative h-screen",children:A.images&&A.images.length>0?(0,a.jsx)(p.tq,{modules:[j.tl,j.W_,j.LG],pagination:{clickable:!0},navigation:!0,zoom:{maxRatio:3,minRatio:1,toggle:!0},className:"h-full",children:A.images.map((e,t)=>(0,a.jsx)(p.o5,{children:(0,a.jsxs)("div",{className:"swiper-zoom-container relative h-full",children:[(0,a.jsx)("img",{src:e,alt:"".concat(A.breed||"宠物"," - ").concat(t+1),className:"w-full h-full object-cover"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent pointer-events-none"})]})},t))}):(0,a.jsx)("div",{className:"h-full bg-gray-100 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500 text-lg",children:"暂无图片"})})}),(0,a.jsxs)("div",{className:(0,y.cn)("fixed right-4 bottom-20 z-20 flex flex-col space-y-6 transition-opacity duration-300",B?"opacity-0 pointer-events-none":"opacity-100"),children:[(0,a.jsxs)("button",{onClick:es,className:"flex flex-col items-center justify-center transition-all duration-200 group",children:[(0,a.jsx)(o.Z,{className:(0,y.cn)("h-8 w-8 transition-all duration-200 drop-shadow-lg",T?"text-red-500 fill-current scale-110":"text-white hover:scale-110")}),(0,a.jsx)("span",{className:"text-xs mt-1 text-white font-bold drop-shadow-lg",children:A.likes_count||0})]}),(0,a.jsxs)("button",{onClick:()=>{ee(),K(!0)},className:"flex flex-col items-center justify-center transition-all duration-200 group",children:[(0,a.jsx)(d.Z,{className:(0,y.cn)("h-8 w-8 transition-all duration-200 drop-shadow-lg",q>0?"text-orange-400 fill-current scale-110":"text-white hover:scale-110")}),q>0&&(0,a.jsx)("span",{className:"text-xs mt-1 text-white font-bold drop-shadow-lg",children:q})]}),A&&("breeding"===A.type||"selling"===A.type||"lost"===A.type||"wanted"===A.type)&&(0,a.jsx)("button",{onClick:en,className:"flex flex-col items-center justify-center transition-all duration-200 group",children:(0,a.jsx)(u.Z,{className:"h-8 w-8 text-blue-400 transition-all duration-200 drop-shadow-lg hover:scale-110"})}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("button",{onClick:e=>{e.stopPropagation(),W(!U)},className:"flex flex-col items-center justify-center transition-all duration-200 group",children:(0,a.jsx)(x.Z,{className:"h-8 w-8 text-white transition-all duration-200 drop-shadow-lg hover:scale-110"})}),U&&(0,a.jsxs)("div",{className:"absolute right-14 bottom-0 w-32 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-30",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("button",{onClick:()=>{ea(),W(!1)},className:"w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"分享"})]}),(0,a.jsxs)("button",{onClick:()=>{W(!1),X(!0)},className:"w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[(0,a.jsx)(m.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"举报"})]})]})]})]}),(0,a.jsxs)("div",{className:(0,y.cn)("absolute bottom-4 left-4 z-20 bg-black/50 backdrop-blur-md rounded-xl p-4 text-white max-w-sm transition-opacity duration-300 border border-white/10",B?"opacity-0 pointer-events-none":"opacity-100"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(null===(e=A.author)||void 0===e?void 0:e.avatar_url)?(0,a.jsx)("img",{src:A.author.avatar_url,alt:A.author.nickname,className:"w-12 h-12 rounded-full object-cover border-2 border-white/30 shadow-lg"}):(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-600/80 border-2 border-white/30 flex items-center justify-center shadow-lg",children:(0,a.jsx)(g.Z,{className:"h-6 w-6 text-white"})}),(0,a.jsx)("div",{className:"flex-1 min-w-0",children:(null===(t=A.author)||void 0===t?void 0:t._id)&&"anonymous"!==A.author._id?(0,a.jsx)("button",{onClick:()=>{let e=null;try{let t=localStorage.getItem("pet_platform_user");t&&(e=JSON.parse(t)._id)}catch(e){console.log("获取当前用户信息失败")}e&&A.author&&e===A.author._id?_.push("/profile"):A.author&&_.push("/profile/".concat(A.author._id))},className:"font-semibold text-white hover:text-white/80 transition-colors block truncate text-base",children:(null===(s=A.author)||void 0===s?void 0:s.nickname)||"匿名用户"}):(0,a.jsx)("span",{className:"font-semibold text-white block truncate text-base",children:(null===(N=A.author)||void 0===N?void 0:N.nickname)||"匿名用户"})})]}),(0,a.jsxs)("div",{className:"space-y-1 mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-white/90",children:[(0,a.jsx)(v.Z,{className:"h-4 w-4 flex-shrink-0"}),(0,a.jsx)("span",{children:z(A.created_at)})]}),A.location&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-white/90",children:[(0,a.jsx)(f.Z,{className:"h-4 w-4 flex-shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:(0,O.Ed)(A.location)})]}),"breeding"===A.postType&&A.gender&&(0,a.jsx)("div",{className:"flex items-center space-x-2 text-sm text-white/90",children:(0,a.jsx)("span",{className:(0,y.cn)("px-2 py-1 rounded-full text-xs font-medium","male"===A.gender?"bg-blue-500/80 text-white":"bg-pink-500/80 text-white"),children:"male"===A.gender?"雄性":"雌性"})})]}),A.description&&(0,a.jsx)("div",{className:"border-t border-white/20 pt-3",children:(0,a.jsx)("div",{className:"text-sm text-white leading-relaxed",children:(0,a.jsx)("p",{className:"line-clamp-4",children:A.description})})})]}),A&&(0,a.jsx)(R,{isOpen:H,onClose:()=>K(!1),post:A,onSuccess:()=>{ee()}}),A&&(0,a.jsx)(S,{isOpen:V,onClose:()=>X(!1),postId:A._id,onSuccess:()=>{X(!1),b.C.success("举报已提交，我们会尽快处理")}}),E&&(0,a.jsx)(I.Z,{isOpen:Y,onClose:()=>Q(!1),currentUser:E,onUpdate:ei,forceContactTab:!0})]}):(0,a.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-gray-500",children:"宝贝不存在"})})}},56334:function(e,t,s){"use strict";var a=s(57437),r=s(2265),i=s(68661);let n=r.forwardRef((e,t)=>{let{className:s,variant:r="primary",size:n="md",loading:l=!1,icon:c,children:o,disabled:d,...u}=e;return(0,a.jsxs)("button",{className:(0,i.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[r],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[n],s),ref:t,disabled:d||l,...u,children:[l&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!l&&c&&(0,a.jsx)("span",{className:"mr-2",children:c}),o]})});n.displayName="Button",t.Z=n},98702:function(e,t,s){"use strict";s.d(t,{fe:function(){return d},u_:function(){return o}});var a=s(57437),r=s(2265),i=s(54887),n=s(32489),l=s(68661),c=s(56334);let o=e=>{let{isOpen:t,onClose:s,title:o,children:d,size:u="md",showCloseButton:x=!0,closeOnOverlayClick:h=!0,className:m}=e;if((0,r.useEffect)(()=>{let e=e=>{"Escape"===e.key&&s()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[t,s]),!t)return null;let g=(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:h?s:void 0}),(0,a.jsxs)("div",{className:(0,l.cn)("relative bg-white rounded-lg shadow-xl w-full mx-4 max-h-[90vh] overflow-hidden",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[u],m),onClick:e=>e.stopPropagation(),children:[(o||x)&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[o&&(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:o}),x&&(0,a.jsx)(c.Z,{variant:"ghost",size:"sm",onClick:s,className:"p-1 hover:bg-gray-100 rounded-full",children:(0,a.jsx)(n.Z,{className:"h-5 w-5"})})]}),(0,a.jsx)("div",{className:"overflow-y-auto max-h-[calc(90vh-80px)]",children:d})]})]});return(0,i.createPortal)(g,document.body)},d=e=>{let{children:t,className:s}=e;return(0,a.jsx)("div",{className:(0,l.cn)("p-4",s),children:t})}},44995:function(e,t,s){"use strict";function a(e){if(!e||"string"!=typeof e)return"";let t=e.replace(/^#/,"").trim();if(!t)return"";try{let s="",a="",r="",i=t.match(/(.*?(?:省|自治区|特别行政区))/);i&&(s=i[1],t=t.replace(s,""));let n=t.match(/(.*?市)/);n&&(a=n[1],t=t.replace(a,""));let l=t.match(/(.*?(?:县|区|市|旗|自治县|林区|特区))/);l&&(r=l[1]);let c="",o=["北京","上海","天津","重庆"].some(e=>s&&s.includes(e)||a&&a.includes(e)),d=r&&["浦东新区","滨海新区","两江新区"].some(e=>r.includes(e));if(o?d&&a?c=a+r:r?c=r:a?c=a:s&&(c=s):a&&r?c=a+r:a?c=a:r?c=r:s&&(c=s),!c){let t=e.replace(/^#/,"").trim(),s=t.match(/(.*?(?:镇|乡|街道|办事处))/);if(s)c=s[1];else{let e=t.match(/(.*?(?:村|社区|小区|路|街|巷|弄|号))/);if(e){let t=e[1];c=t.length>8?t.substring(0,8)+"...":t}else c=t.length>10?t.substring(0,10)+"...":t}}return c}catch(e){return console.error("地址格式化出错:",e),t.length>10?t.substring(0,10)+"...":t}}function r(e){return e&&"string"==typeof e?e.replace(/^#/,"").trim():""}s.d(t,{Ed:function(){return r},lx:function(){return a}})}},function(e){e.O(0,[35,649,19,347,554,721,399,11,734,136,971,117,744],function(){return e(e.s=8775)}),_N_E=e.O()}]);