"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[465],{88941:function(e,s,t){t.d(s,{AuthProvider:function(){return c},E:function(){return d},Y:function(){return o}});var a=t(57437),r=t(2265),l=t(98734),n=t(31215);let i=(0,r.createContext)(void 0),c=e=>{let{children:s}=e,t=(0,l.a)();return t.isLoading&&!t.user?(0,a.jsx)(n.SX,{text:"正在初始化..."}):(0,a.jsx)(i.Provider,{value:t,children:s})},d=()=>{let e=(0,r.useContext)(i);if(void 0===e)throw Error("useAuthContext must be used within an AuthProvider");return e},o=e=>{let{children:s,fallback:t}=e,{isLoggedIn:r,isLoading:l}=d();return l?(0,a.jsx)(n.SX,{text:"验证登录状态..."}):r?(0,a.jsx)(a.Fragment,{children:s}):t||(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"需要登录"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"请先登录后再访问此页面"})]})})}},47465:function(e,s,t){t.d(s,{Z:function(){return A}});var a=t(57437),r=t(2265),l=t(82718),n=t(92369),i=t(47692),c=t(32489),d=t(58293),o=t(56334),m=t(88941),x=t(98702),u=t(92827),h=t(89345),p=t(66337),f=t(98734),g=t(98011),j=t(68661),v=e=>{let{isOpen:s,onClose:t,onSuccess:l}=e,{login:n,isLoading:i}=(0,f.a)(),[d,m]=(0,r.useState)("main"),[v,b]=(0,r.useState)({email:"",password:"",nickname:"",confirmPassword:"",verificationCode:""}),[y,N]=(0,r.useState)({}),[w,k]=(0,r.useState)(!1),[C,I]=(0,r.useState)(0),Z=()=>{b({email:"",password:"",nickname:"",confirmPassword:"",verificationCode:""}),N({}),m("main"),k(!1),I(0)},S=()=>{Z(),t()},P=()=>{let e={};return("email"===d||"register"===d)&&(v.email.trim()?(0,j.vV)(v.email)||(e.email="请输入有效的邮箱地址"):e.email="请输入邮箱地址",v.password.trim()?v.password.length<6&&(e.password="密码至少6位"):e.password="请输入密码"),"register"===d&&(v.nickname.trim()?v.nickname.length<2?e.nickname="昵称至少需要2个字符":v.nickname.length>20&&(e.nickname="昵称不能超过20个字符"):e.nickname="请输入昵称",v.confirmPassword.trim()?v.password!==v.confirmPassword&&(e.confirmPassword="两次密码输入不一致"):e.confirmPassword="请确认密码"),N(e),0===Object.keys(e).length},_=async()=>{console.log("微信登录")},z=async()=>{if(!v.email.trim()){N({email:"请输入邮箱地址"});return}if(!(0,j.vV)(v.email)){N({email:"请输入有效的邮箱地址"});return}try{k(!0);let e=await g.authAPI.sendVerificationCode(v.email,"register");if(e.success){I(60);let e=setInterval(()=>{I(s=>s<=1?(clearInterval(e),0):s-1)},1e3)}else N({verificationCode:e.message})}catch(e){N({verificationCode:e.message||"发送验证码失败"})}finally{k(!1)}},A=async()=>{P()&&await n(v.email,v.password)&&(null==l||l(),S())},D=async()=>{if(P())try{let e=await g.authAPI.registerWithEmail(v.email,v.password,v.nickname,v.verificationCode);e.success?await n(v.email,v.password)&&(null==l||l(),S()):N({verificationCode:e.message})}catch(e){N({verificationCode:e.message||"注册失败"})}},L=(e,s)=>{b(t=>({...t,[e]:s})),y[e]&&N(s=>({...s,[e]:""}))};return(0,a.jsx)(x.u_,{isOpen:s,onClose:S,size:"sm",children:(0,a.jsxs)(x.fe,{children:[(0,a.jsx)("button",{onClick:S,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(c.Z,{className:"w-5 h-5"})}),"main"===d&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"欢迎回来"}),(0,a.jsx)("p",{className:"text-gray-600",children:"选择登录方式继续使用"})]}),(0,a.jsxs)(o.Z,{onClick:_,className:"w-full bg-green-500 hover:bg-green-600 text-white py-3 rounded-lg flex items-center justify-center space-x-2",disabled:i,children:[(0,a.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",fill:"currentColor",children:[(0,a.jsx)("path",{d:"M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.932 7.621-.55-.302-2.676-2.91-4.624-6.364-4.624zm-2.44 5.738a.868.868 0 0 1-.869-.855c0-.472.39-.856.869-.856s.868.384.868.856a.868.868 0 0 1-.868.855zm4.928 0a.868.868 0 0 1-.868-.855c0-.472.39-.856.868-.856s.869.384.869.856a.868.868 0 0 1-.869.855z"}),(0,a.jsx)("path",{d:"M24 14.388c0-3.14-2.956-5.69-6.594-5.69-3.638 0-6.594 2.55-6.594 5.69 0 3.14 2.956 5.69 6.594 5.69a7.842 7.842 0 0 0 2.208-.32.671.671 0 0 1 .556.075l1.462.855a.25.25 0 0 0 .128.042.226.226 0 0 0 .223-.227.166.166 0 0 0-.037-.164l-.3-1.14a.454.454 0 0 1 .164-.512C22.84 17.64 24 16.125 24 14.388zm-8.738-1.14a.67.67 0 0 1-.669-.66c0-.365.3-.66.67-.66a.67.67 0 0 1 .668.66c0 .365-.3.66-.669.66zm3.348 0a.67.67 0 0 1-.668-.66c0-.365.3-.66.668-.66a.67.67 0 0 1 .669.66c0 .365-.3.66-.669.66z"})]}),(0,a.jsx)("span",{children:"微信登录"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,a.jsxs)(o.Z,{onClick:()=>m("email"),variant:"outline",className:"w-full py-3 rounded-lg flex items-center justify-center space-x-2",children:[(0,a.jsx)(h.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"邮箱登录"})]})]}),"email"===d&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("button",{onClick:()=>m("main"),className:"absolute left-4 top-4 text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"邮箱登录"}),(0,a.jsx)("p",{className:"text-gray-600",children:"使用邮箱和密码登录"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(u.I,{label:"邮箱地址",type:"email",placeholder:"请输入邮箱地址",value:v.email,onChange:e=>L("email",e.target.value),error:y.email,leftIcon:(0,a.jsx)(h.Z,{className:"h-4 w-4"}),disabled:i}),(0,a.jsx)(u.I,{label:"密码",type:"password",placeholder:"请输入密码",value:v.password,onChange:e=>L("password",e.target.value),error:y.password,leftIcon:(0,a.jsx)(p.Z,{className:"h-4 w-4"}),disabled:i})]}),(0,a.jsx)(o.Z,{onClick:A,className:"w-full py-3 rounded-lg",disabled:i,children:i?"登录中...":"登录"}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"还没有账号？"}),(0,a.jsx)("button",{onClick:()=>m("register"),className:"text-blue-600 hover:text-blue-700 ml-1",children:"立即注册"})]})]}),"register"===d&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("button",{onClick:()=>m("email"),className:"absolute left-4 top-4 text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"注册账号"}),(0,a.jsx)("p",{className:"text-gray-600",children:"创建您的宠物交易账号"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(u.I,{label:"邮箱地址",type:"email",placeholder:"请输入邮箱地址",value:v.email,onChange:e=>L("email",e.target.value),error:y.email,leftIcon:(0,a.jsx)(h.Z,{className:"h-4 w-4"}),disabled:i}),(0,a.jsx)(u.I,{label:"昵称",type:"text",placeholder:"请输入昵称",value:v.nickname,onChange:e=>L("nickname",e.target.value),error:y.nickname,disabled:i,maxLength:20}),(0,a.jsx)(u.I,{label:"密码",type:"password",placeholder:"请输入密码（至少6位）",value:v.password,onChange:e=>L("password",e.target.value),error:y.password,leftIcon:(0,a.jsx)(p.Z,{className:"h-4 w-4"}),disabled:i}),(0,a.jsx)(u.I,{label:"确认密码",type:"password",placeholder:"请再次输入密码",value:v.confirmPassword,onChange:e=>L("confirmPassword",e.target.value),error:y.confirmPassword,leftIcon:(0,a.jsx)(p.Z,{className:"h-4 w-4"}),disabled:i}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"邮箱验证码"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(u.I,{type:"text",placeholder:"请输入6位验证码",value:v.verificationCode,onChange:e=>L("verificationCode",e.target.value),error:y.verificationCode,disabled:i,maxLength:6,className:"flex-1"}),(0,a.jsx)(o.Z,{onClick:z,disabled:w||C>0||!v.email||!(0,j.vV)(v.email),variant:"outline",className:"whitespace-nowrap",children:w?"发送中...":C>0?"".concat(C,"s"):"发送验证码"})]})]})]}),(0,a.jsx)(o.Z,{onClick:D,className:"w-full py-3 rounded-lg",disabled:i,children:i?"注册中...":"注册"}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"已有账号？"}),(0,a.jsx)("button",{onClick:()=>m("email"),className:"text-blue-600 hover:text-blue-700 ml-1",children:"立即登录"})]})]})]})})},b=t(27648),y=t(82023),N=()=>{var e;let[s,t]=(0,r.useState)([]),[l,n]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{(async()=>{try{let e=await g.activityAPI.getActiveActivities();e.success&&e.data.length>0&&(t(e.data),n(!0))}catch(e){console.error("获取活动失败:",e)}})()},[]),l&&0!==s.length)?(0,a.jsx)("div",{className:"hidden md:flex items-center",children:(0,a.jsxs)("button",{onClick:()=>{window.location.href="/activities"},className:"flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-sm hover:shadow-md",children:[(0,a.jsx)(y.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"max-w-32 truncate",children:s.length>1?"".concat(s.length,"个活动"):null===(e=s[0])||void 0===e?void 0:e.title})]})}):null};t(13041);var w=t(42449),k=t(88997),C=t(73247),I=t(98728),Z=t(63639),S=t(65302);let P=e=>{let{isOpen:s,onClose:t}=e,{user:n,isLoggedIn:i}=(0,m.E)(),[d,o]=(0,r.useState)([]),[x,u]=(0,r.useState)(!1),[h,p]=(0,r.useState)("trade"),[f,j]=(0,r.useState)(null),v=async()=>{if(i&&s)try{u(!0);let e=await g.petAPI.getUserNotifications({limit:50,type:"system"===h?void 0:"contact"});if(e.success){let s=(e.data||[]).map(e=>{var s,t,a,r,l,i,c,d,o,m,x,u,h,p;return"contact"===e.type?{id:e._id,type:"contact",fromUser:{id:e.sender_id,nickname:(null===(s=e.data)||void 0===s?void 0:s.sender_nickname)||(null===(t=e.data)||void 0===t?void 0:t.author_nickname)||"用户",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"},toUser:{id:e.recipient_id,nickname:(null==n?void 0:n.nickname)||"",avatar:(null==n?void 0:n.avatar_url)||""},postInfo:{id:e.post_id,title:(null===(a=e.data)||void 0===a?void 0:a.post_title)||"宠物帖子",image:"https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=300&h=300&fit=crop",postType:(null===(r=e.data)||void 0===r?void 0:r.post_type)||"selling"},contactInfo:{method:(null===(i=e.data)||void 0===i?void 0:null===(l=i.sender_contact)||void 0===l?void 0:l.type)||(null===(d=e.data)||void 0===d?void 0:null===(c=d.author_contact)||void 0===c?void 0:c.type)||"wechat",value:(null===(m=e.data)||void 0===m?void 0:null===(o=m.sender_contact)||void 0===o?void 0:o.value)||(null===(u=e.data)||void 0===u?void 0:null===(x=u.author_contact)||void 0===x?void 0:x.value)||"未提供"},timestamp:e.created_at,read:e.read||!1}:"system"===e.type||"punishment"===e.type||"appeal"===e.type?{id:e._id,type:"system",title:e.message,content:e.message,timestamp:e.created_at,read:e.read||!1,severity:"punishment"===e.type?"warning":"info",canAppeal:"punishment"===e.type,reportId:null===(h=e.data)||void 0===h?void 0:h.reportId,appealStatus:(null===(p=e.data)||void 0===p?void 0:p.appealStatus)||"none"}:null}).filter(Boolean);o(s)}else o([])}catch(e){console.error("加载通知失败:",e),o([])}finally{u(!1)}};(0,r.useEffect)(()=>{s&&v()},[s,i,h]);let b=async e=>{try{await g.petAPI.markNotificationRead({notificationId:e}),o(s=>s.map(s=>s.id===e?{...s,read:!0}:s))}catch(e){console.error("标记已读失败:",e)}},y=async e=>{if(!f){j(e);try{await g.petAPI.deleteNotification({notificationId:e}),o(s=>s.filter(s=>s.id!==e))}catch(e){console.error("删除失败:",e),alert("删除失败，请稍后重试")}finally{j(null)}}},N=async()=>{if(confirm({trade:"确定清空所有买卖通知吗？",breeding:"确定清空所有配种通知吗？",lost:"确定清空所有寻回通知吗？",system:"确定清空所有系统通知吗？"}[h]))try{let e=Z.map(e=>e.id);if(0===e.length)return;await g.petAPI.bulkDeleteNotifications({notificationIds:e}),o(s=>s.filter(s=>!e.includes(s.id))),console.log("已清空".concat(Z.length,"条通知"))}catch(e){console.error("清空失败:",e),alert("清空失败，请稍后重试")}},Z=d.filter(e=>"system"===h?"system"===e.type:"breeding"===h?"contact"===e.type&&"breeding"===e.postInfo.postType:"lost"===h?"contact"===e.type&&"lost"===e.postInfo.postType:"trade"!==h||"contact"===e.type&&("selling"===e.postInfo.postType||"wanted"===e.postInfo.postType));return s?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"通知中心"}),(0,a.jsx)("button",{onClick:t,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(c.Z,{className:"h-5 w-5 text-gray-500"})})]}),(0,a.jsxs)("div",{className:"flex space-x-1 bg-gray-100 rounded-lg p-1 m-4 flex-shrink-0",children:[(0,a.jsxs)("button",{onClick:()=>p("trade"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("trade"===h?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(w.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"买卖通知"})]}),(0,a.jsxs)("button",{onClick:()=>p("breeding"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("breeding"===h?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(k.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"配种通知"})]}),(0,a.jsxs)("button",{onClick:()=>p("lost"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("lost"===h?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(C.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"寻回通知"})]}),(0,a.jsxs)("button",{onClick:()=>p("system"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("system"===h?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(I.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"系统通知"})]})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-6",children:x?(0,a.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"}),(0,a.jsx)("span",{className:"ml-2 text-gray-600",children:"加载中..."})]}):0===Z.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(l.Z,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"暂无通知"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"flex justify-end mb-4",children:(0,a.jsx)("button",{onClick:N,className:"px-4 py-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors",children:"清空当前分类"})}),(0,a.jsx)("div",{className:"space-y-4",children:Z.map(e=>(0,a.jsx)("div",{className:"bg-white rounded-lg border p-4 hover:shadow-md transition-shadow cursor-pointer ".concat(e.read?"border-gray-200":"border-blue-200 bg-blue-50"),onClick:()=>b(e.id),children:"system"===e.type?(0,a.jsx)(z,{message:e,onDelete:y,deletingId:f}):(0,a.jsx)(_,{message:e,onDelete:y,deletingId:f})},e.id))})]})})]})}):null},_=e=>{let{message:s,onDelete:t,deletingId:r}=e;return(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("img",{src:s.fromUser.avatar,alt:s.fromUser.nickname,className:"w-10 h-10 rounded-full object-cover"}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:(()=>{switch(s.postInfo.postType){case"selling":return"有用户想购买您的宠物";case"wanted":return"有卖家回应了您的求购";case"breeding":return"有用户需要您的配种服务";case"lost":return"有用户提供了走失线索";default:return"有用户联系了您"}})()}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{let s=new Date(e),t=(new Date().getTime()-s.getTime())/36e5;return t<24?s.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}):t<168?"".concat(Math.floor(t/24),"天前"):s.toLocaleDateString("zh-CN")})(s.timestamp)}),!s.read&&(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,a.jsx)("button",{onClick:e=>{e.stopPropagation(),t(s.id)},disabled:r===s.id,className:"p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50",children:r===s.id?(0,a.jsx)("div",{className:"w-4 h-4 animate-spin rounded-full border-2 border-red-500 border-t-transparent"}):(0,a.jsx)(c.Z,{className:"w-4 h-4"})})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,a.jsx)("img",{src:s.postInfo.image,alt:s.postInfo.title,className:"w-12 h-12 rounded-lg object-cover"}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm text-gray-900 truncate",children:s.postInfo.title}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:(()=>{switch(s.postInfo.postType){case"selling":return"买家：".concat(s.fromUser.nickname);case"wanted":return"卖家：".concat(s.fromUser.nickname);case"breeding":return"需求方：".concat(s.fromUser.nickname);case"lost":return"提供者：".concat(s.fromUser.nickname);default:return"联系人：".concat(s.fromUser.nickname)}})()})]}),(0,a.jsx)("button",{className:"text-xs text-blue-600 hover:text-blue-800 transition-colors",children:"查看帖子"})]}),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-3 border border-blue-200",children:[(0,a.jsx)("p",{className:"text-xs text-blue-600 mb-2 font-medium",children:"联系方式"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:["wechat"===s.contactInfo.method&&"微信：","phone"===s.contactInfo.method&&"电话：",s.contactInfo.value]}),(0,a.jsx)("button",{className:"text-xs bg-blue-500 text-white px-3 py-1 rounded-full hover:bg-blue-600 transition-colors",onClick:e=>{e.stopPropagation(),navigator.clipboard.writeText(s.contactInfo.value)},children:"复制"})]})]})]})]})},z=e=>{let{message:s,onDelete:t,deletingId:r}=e;return(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-1",children:(e=>{switch(e){case"warning":return(0,a.jsx)(Z.Z,{className:"w-5 h-5 text-orange-500"});case"error":return(0,a.jsx)(Z.Z,{className:"w-5 h-5 text-red-500"});default:return(0,a.jsx)(S.Z,{className:"w-5 h-5 text-blue-500"})}})(s.severity)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900 truncate",children:s.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{let s=new Date(e),t=(new Date().getTime()-s.getTime())/36e5;return t<24?s.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}):t<168?"".concat(Math.floor(t/24),"天前"):s.toLocaleDateString("zh-CN")})(s.timestamp)}),!s.read&&(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,a.jsx)("button",{onClick:e=>{e.stopPropagation(),t(s.id)},disabled:r===s.id,className:"p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50",children:r===s.id?(0,a.jsx)("div",{className:"w-4 h-4 animate-spin rounded-full border-2 border-red-500 border-t-transparent"}):(0,a.jsx)(c.Z,{className:"w-4 h-4"})})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2",children:s.content}),s.canAppeal&&(0,a.jsxs)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:["none"===s.appealStatus&&(0,a.jsx)("button",{onClick:e=>{e.stopPropagation()},className:"text-sm bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors",children:"申诉"}),"pending"===s.appealStatus&&(0,a.jsx)("span",{className:"text-sm text-orange-600 bg-orange-100 px-3 py-1 rounded-full",children:"申诉处理中..."}),"approved"===s.appealStatus&&(0,a.jsx)("span",{className:"text-sm text-green-600 bg-green-100 px-3 py-1 rounded-full",children:"申诉已通过，处罚已撤销"}),"rejected"===s.appealStatus&&(0,a.jsx)("span",{className:"text-sm text-red-600 bg-red-100 px-3 py-1 rounded-full",children:"申诉已驳回，处罚维持"})]})]})]})};var A=()=>{let{user:e,isLoggedIn:s,logout:t,refreshLoginState:x}=(0,m.E)(),[u,h]=(0,r.useState)(!1),[p,f]=(0,r.useState)(!1),[g,j]=(0,r.useState)(!1),y=()=>{h(!0)},w=async()=>{await t(),f(!1)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("header",{className:"bg-white border-b border-gray-200 sticky top-0 z-40",children:[(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsxs)(b.default,{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:"\uD83D\uDC3E"})}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900 hidden sm:block",children:"宠物交易平台"})]}),(0,a.jsx)(N,{}),(0,a.jsx)("div",{className:"hidden md:flex items-center space-x-3",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.Z,{variant:"outline",icon:(0,a.jsx)(l.Z,{className:"h-4 w-4"}),onClick:()=>j(!0),children:"通知"}),(0,a.jsx)(b.default,{href:"/profile",children:(0,a.jsx)(o.Z,{variant:"outline",icon:(0,a.jsx)(n.Z,{className:"h-4 w-4"}),children:"我"})}),(0,a.jsx)(o.Z,{variant:"outline",icon:(0,a.jsx)(i.Z,{className:"h-4 w-4"}),onClick:w,className:"text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300",children:"退出"})]}):(0,a.jsx)(o.Z,{onClick:y,children:"登录"})}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsx)("button",{onClick:()=>f(!p),className:"p-2 rounded-lg hover:bg-gray-100",children:p?(0,a.jsx)(c.Z,{className:"h-6 w-6"}):(0,a.jsx)(d.Z,{className:"h-6 w-6"})})})]})}),p&&(0,a.jsx)("div",{className:"md:hidden border-t border-gray-200 bg-white",children:(0,a.jsx)("div",{className:"px-4 py-4 space-y-3",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"pb-3 border-b border-gray-200",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:null==e?void 0:e.nickname}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:null==e?void 0:e.email})]})}),(0,a.jsxs)("button",{className:"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 w-full text-left",onClick:()=>{j(!0),f(!1)},children:[(0,a.jsx)(l.Z,{className:"h-5 w-5 text-gray-600"}),(0,a.jsx)("span",{children:"通知"})]}),(0,a.jsxs)(b.default,{href:"/profile",className:"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100",onClick:()=>f(!1),children:[(0,a.jsx)(n.Z,{className:"h-5 w-5 text-gray-600"}),(0,a.jsx)("span",{children:"我的主页"})]}),(0,a.jsxs)("button",{onClick:w,className:"flex items-center space-x-3 p-3 rounded-lg hover:bg-red-50 w-full text-left",children:[(0,a.jsx)(i.Z,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("span",{className:"text-red-600",children:"退出"})]})]}):(0,a.jsx)(o.Z,{onClick:()=>{y(),f(!1)},className:"w-full",children:"登录"})})})]}),(0,a.jsx)(v,{isOpen:u,onClose:()=>h(!1),onSuccess:async()=>{f(!1),await x()}}),(0,a.jsx)(P,{isOpen:g,onClose:()=>j(!1)})]})}},92827:function(e,s,t){t.d(s,{I:function(){return n},g:function(){return i}});var a=t(57437),r=t(2265),l=t(68661);let n=r.forwardRef((e,s)=>{let{className:t,type:r="text",label:n,error:i,helperText:c,leftIcon:d,rightIcon:o,variant:m="default",...x}=e,u=i?"border-red-500 focus:ring-red-500 focus:border-red-500":"";return(0,a.jsxs)("div",{className:"w-full",children:[n&&(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:n}),(0,a.jsxs)("div",{className:"relative",children:[d&&(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("span",{className:"text-gray-400",children:d})}),(0,a.jsx)("input",{type:r,className:(0,l.cn)("w-full px-3 py-2 text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{default:"border border-gray-300 rounded-md bg-white focus:ring-primary-500 focus:border-primary-500",filled:"border-0 bg-gray-100 rounded-md focus:ring-primary-500 focus:bg-white",outline:"border-2 border-gray-200 rounded-md bg-transparent focus:ring-primary-500 focus:border-primary-500"}[m],u,d&&"pl-10",o&&"pr-10",t),ref:s,...x}),o&&(0,a.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,a.jsx)("span",{className:"text-gray-400",children:o})})]}),(i||c)&&(0,a.jsx)("p",{className:(0,l.cn)("mt-1 text-xs",i?"text-red-600":"text-gray-500"),children:i||c})]})});n.displayName="Input";let i=r.forwardRef((e,s)=>{let{className:t,label:r,error:n,helperText:i,variant:c="default",...d}=e,o=n?"border-red-500 focus:ring-red-500 focus:border-red-500":"";return(0,a.jsxs)("div",{className:"w-full",children:[r&&(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:r}),(0,a.jsx)("textarea",{className:(0,l.cn)("w-full px-3 py-2 text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed resize-none",{default:"border border-gray-300 rounded-md bg-white focus:ring-primary-500 focus:border-primary-500",filled:"border-0 bg-gray-100 rounded-md focus:ring-primary-500 focus:bg-white",outline:"border-2 border-gray-200 rounded-md bg-transparent focus:ring-primary-500 focus:border-primary-500"}[c],o,t),ref:s,...d}),(n||i)&&(0,a.jsx)("p",{className:(0,l.cn)("mt-1 text-xs",n?"text-red-600":"text-gray-500"),children:n||i})]})});i.displayName="Textarea"},31215:function(e,s,t){t.d(s,{LL:function(){return c},SX:function(){return n},gG:function(){return i},gb:function(){return l}});var a=t(57437);t(2265);var r=t(68661);let l=e=>{let{size:s="md",variant:t="spinner",className:l,text:n}=e,i={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"},c={sm:"text-sm",md:"text-base",lg:"text-lg"};if("spinner"===t)return(0,a.jsx)("div",{className:(0,r.cn)("flex items-center justify-center",l),children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsxs)("svg",{className:(0,r.cn)("animate-spin text-primary-600",i[s]),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),n&&(0,a.jsx)("p",{className:(0,r.cn)("text-gray-500",c[s]),children:n})]})});if("dots"===t){let e="sm"===s?"w-2 h-2":"md"===s?"w-3 h-3":"w-4 h-4";return(0,a.jsx)("div",{className:(0,r.cn)("flex items-center justify-center",l),children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:(0,r.cn)("bg-primary-600 rounded-full animate-bounce",e),style:{animationDelay:"0ms"}}),(0,a.jsx)("div",{className:(0,r.cn)("bg-primary-600 rounded-full animate-bounce",e),style:{animationDelay:"150ms"}}),(0,a.jsx)("div",{className:(0,r.cn)("bg-primary-600 rounded-full animate-bounce",e),style:{animationDelay:"300ms"}})]}),n&&(0,a.jsx)("p",{className:(0,r.cn)("text-gray-500",c[s]),children:n})]})})}return"pulse"===t?(0,a.jsx)("div",{className:(0,r.cn)("flex items-center justify-center",l),children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsx)("div",{className:(0,r.cn)("bg-primary-600 rounded-full animate-pulse",i[s])}),n&&(0,a.jsx)("p",{className:(0,r.cn)("text-gray-500",c[s]),children:n})]})}):null},n=e=>{let{text:s="加载中..."}=e;return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsx)(l,{size:"lg",text:s})})},i=()=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden animate-pulse",children:[(0,a.jsx)("div",{className:"aspect-square bg-gray-200"}),(0,a.jsxs)("div",{className:"p-3 space-y-2",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/4"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/4"})]})]})]}),c=()=>(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4 p-6",children:[(0,a.jsx)("div",{className:"w-24 h-24 bg-gray-200 rounded-full"}),(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48"}),(0,a.jsxs)("div",{className:"flex space-x-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-8"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-8"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-8"})]})]}),(0,a.jsx)("div",{className:"h-10 bg-gray-200 rounded w-24"})]})})},98702:function(e,s,t){t.d(s,{fe:function(){return o},u_:function(){return d}});var a=t(57437),r=t(2265),l=t(54887),n=t(32489),i=t(68661),c=t(56334);let d=e=>{let{isOpen:s,onClose:t,title:d,children:o,size:m="md",showCloseButton:x=!0,closeOnOverlayClick:u=!0,className:h}=e;if((0,r.useEffect)(()=>{let e=e=>{"Escape"===e.key&&t()};return s&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[s,t]),!s)return null;let p=(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:u?t:void 0}),(0,a.jsxs)("div",{className:(0,i.cn)("relative bg-white rounded-lg shadow-xl w-full mx-4 max-h-[90vh] overflow-hidden",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[m],h),onClick:e=>e.stopPropagation(),children:[(d||x)&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[d&&(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:d}),x&&(0,a.jsx)(c.Z,{variant:"ghost",size:"sm",onClick:t,className:"p-1 hover:bg-gray-100 rounded-full",children:(0,a.jsx)(n.Z,{className:"h-5 w-5"})})]}),(0,a.jsx)("div",{className:"overflow-y-auto max-h-[calc(90vh-80px)]",children:o})]})]});return(0,l.createPortal)(p,document.body)},o=e=>{let{children:s,className:t}=e;return(0,a.jsx)("div",{className:(0,i.cn)("p-4",t),children:s})}}}]);