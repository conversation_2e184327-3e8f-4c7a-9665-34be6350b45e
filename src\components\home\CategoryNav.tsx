import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight, Clock, X, Trash2 } from 'lucide-react';
import { Category } from '@/types';
import { petAPI } from '@/lib/cloudbase';
import { categoryHistoryManager, HistoryItem } from '@/utils/historyManager';
import { cn } from '@/utils';
import { useClickOutside } from '@/hooks/useClickOutside';

interface CategoryNavProps {
  selectedCategory: string;
  onCategoryChange: (categoryId: string) => void;
}

const CategoryNav: React.FC<CategoryNavProps> = ({
  selectedCategory,
  onCategoryChange,
}) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [showHistory, setShowHistory] = useState(false);
  const [history, setHistory] = useState<HistoryItem[]>([]);

  // 获取分类数据和历史记录
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const result = await petAPI.getCategories();
        if (result.success) {
          setCategories(result.data);
        }
      } catch (error) {
        console.error('获取分类失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
    setHistory(categoryHistoryManager.getHistory());
  }, []);

  // 构建分类树
  const buildCategoryTree = () => {
    const level1 = categories.filter(cat => cat.level === 1).sort((a, b) => a.order - b.order);
    return level1.map(l1 => ({
      ...l1,
      children: categories
        .filter(cat => cat.level === 2 && cat.parent_id === l1.id)
        .sort((a, b) => a.order - b.order)
    }));
  };

  // 选择分类
  const selectCategory = (categoryId: string, categoryName?: string) => {
    onCategoryChange(categoryId);

    // 添加到历史记录（除了"全部分类"）
    if (categoryId && categoryName) {
      categoryHistoryManager.addItem(categoryId, categoryName);
      setHistory(categoryHistoryManager.getHistory());
    }
  };

  // 选择历史记录
  const selectHistoryCategory = (categoryId: string, categoryName: string) => {
    onCategoryChange(categoryId);
    setShowHistory(false);
    // 重新添加到历史记录（更新时间戳）
    categoryHistoryManager.addItem(categoryId, categoryName);
    setHistory(categoryHistoryManager.getHistory());
  };

  // 删除历史记录项
  const removeHistoryItem = (categoryId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    categoryHistoryManager.removeItem(categoryId);
    setHistory(categoryHistoryManager.getHistory());
  };

  // 清空历史记录
  const clearHistory = () => {
    categoryHistoryManager.clearHistory();
    setHistory([]);
    setShowHistory(false);
  };

  // 点击外部关闭历史记录
  const historyRef = useClickOutside<HTMLDivElement>(() => {
    setShowHistory(false);
  });

  if (loading) {
    return (
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="animate-pulse">
            <div className="flex space-x-4">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9].map(i => (
                <div key={i} className="h-8 bg-gray-200 rounded w-16" />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  const categoryTree = buildCategoryTree();

  return (
    <div className="bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 移动端分类选择器 */}
        <div className="md:hidden py-4">
          <div className="relative">
            <select
              value={selectedCategory}
              onChange={(e) => {
                const category = categories.find(c => c.id === e.target.value);
                selectCategory(e.target.value, category?.name);
              }}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">全部分类</option>
              {categoryTree.map(l1 => (
                <optgroup key={l1.id} label={l1.name}>
                  <option value={l1.id}>{l1.name}</option>
                  {l1.children.map(l2 => (
                    <option key={l2.id} value={l2.id}>
                      {l1.name} &gt; {l2.name}
                    </option>
                  ))}
                </optgroup>
              ))}
            </select>

            {/* 移动端历史记录按钮 */}
            {history.length > 0 && (
              <button
                onClick={() => setShowHistory(!showHistory)}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600"
              >
                <Clock className="h-4 w-4" />
              </button>
            )}

            {/* 移动端历史记录下拉 */}
            {showHistory && history.length > 0 && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-40 overflow-y-auto">
                <div className="p-2">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-gray-500">分类历史</span>
                    <button onClick={clearHistory} className="text-xs text-gray-400 hover:text-gray-600">
                      清空
                    </button>
                  </div>
                  {history.map((item, index) => (
                    <button
                      key={index}
                      onClick={() => selectHistoryCategory(item.value, item.label)}
                      className="w-full px-2 py-1 text-left text-sm hover:bg-gray-50 rounded flex items-center justify-between group"
                    >
                      <span>{item.label}</span>
                      <button
                        onClick={(e) => removeHistoryItem(item.value, e)}
                        className="opacity-0 group-hover:opacity-100 text-gray-400 hover:text-gray-600"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 桌面端分类导航 */}
        <div className="hidden md:block py-4">
          <div className="flex flex-wrap gap-2 items-center">
            {/* 全部分类 */}
            <button
              onClick={() => selectCategory('')}
              className={cn(
                'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
                selectedCategory === ''
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              )}
            >
              全部分类
            </button>

            {/* 9个分类按钮（带下拉菜单） */}
            {categoryTree.map(l1 => (
              <div key={l1.id} className="relative group">
                <button
                  onClick={() => selectCategory(l1.id, l1.name)}
                  className={cn(
                    'flex items-center space-x-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors',
                    selectedCategory === l1.id
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  )}
                >
                  <span>{l1.name}</span>
                  {l1.children.length > 0 && (
                    <ChevronDown className="h-3 w-3" />
                  )}
                </button>

                {/* 二级分类下拉菜单 */}
                {l1.children.length > 0 && (
                  <div className="absolute top-full left-0 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                    <div className="p-2">
                      {l1.children.map(l2 => (
                        <button
                          key={l2.id}
                          onClick={() => selectCategory(l2.id, l2.name)}
                          className={cn(
                            'block w-full text-left px-3 py-2 text-sm rounded-md transition-colors',
                            selectedCategory === l2.id
                              ? 'bg-primary-50 text-primary-700'
                              : 'text-gray-700 hover:bg-gray-100'
                          )}
                        >
                          {l2.name}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}

            {/* 历史记录按钮 */}
            {history.length > 0 && (
              <div className="relative" ref={historyRef}>
                <button
                  onClick={() => setShowHistory(!showHistory)}
                  className={cn(
                    'px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1',
                    showHistory
                      ? 'bg-blue-100 text-blue-700'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  )}
                >
                  <Clock className="h-3 w-3" />
                  <span>历史</span>
                </button>

                {/* 历史记录下拉菜单 */}
                {showHistory && (
                  <div className="absolute top-full right-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                    <div className="p-2">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs text-gray-500 font-medium">分类历史</span>
                        <button
                          onClick={clearHistory}
                          className="text-xs text-gray-400 hover:text-gray-600 flex items-center space-x-1"
                        >
                          <Trash2 className="h-3 w-3" />
                          <span>清空</span>
                        </button>
                      </div>

                      {history.map((item, index) => (
                        <button
                          key={index}
                          onClick={() => selectHistoryCategory(item.value, item.label)}
                          className="w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none text-sm rounded-md group flex items-center justify-between"
                        >
                          <span className="text-gray-700">{item.label}</span>
                          <button
                            onClick={(e) => removeHistoryItem(item.value, e)}
                            className="opacity-0 group-hover:opacity-100 text-gray-400 hover:text-gray-600 transition-opacity p-1"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryNav;
