import React from 'react';
import ProfilePageClient from './ProfilePageClient';

// 为静态导出提供一些示例参数，实际路由将动态处理
export async function generateStaticParams() {
  return [
    { id: 'eea1754d6873ce81053b899e0f3aa469' },
    { id: '2ed3518f6875a7f905582dfd0fc94e98' },
    { id: 'c611b94668776114057326f5172f0bc5' }, // 牛牛
    { id: 'd77d384f6877626a0574989b7124319b' }  // 大力
  ];
}

// 允许动态参数
export const dynamicParams = true;

export default function ProfilePage({ params }: { params: { id: string } }) {
  return <ProfilePageClient userId={params.id} />;
}

