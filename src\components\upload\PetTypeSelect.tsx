'use client';

import React from 'react';
import { Heart, DollarSign, Search, HelpCircle, ShoppingCart } from 'lucide-react';
import { cn } from '@/utils';

type PetType = 'breeding' | 'selling' | 'lost' | 'wanted' | '';

interface PetTypeSelectProps {
  value: PetType;
  onChange: (type: PetType) => void;
  gender?: 'male' | 'female' | '';
  onGenderChange?: (gender: 'male' | 'female' | '') => void;
  error?: string;
  className?: string;
}

const PetTypeSelect: React.FC<PetTypeSelectProps> = ({
  value,
  onChange,
  gender = '',
  onGenderChange,
  error,
  className
}) => {
  // 宝贝类型选项
  const typeOptions: Array<{
    value: PetType;
    label: string;
    description: string;
    icon: any;
    color: string;
  }> = [
    {
      value: '',
      label: '展示分享',
      description: '分享宝贝照片，不涉及交易',
      icon: HelpCircle,
      color: 'gray'
    },
    {
      value: 'selling',
      label: '出售',
      description: '出售宝贝，需要联系方式',
      icon: DollarSign,
      color: 'green'
    },
    {
      value: 'breeding',
      label: '配种',
      description: '寻找配种对象，需要联系方式和性别',
      icon: Heart,
      color: 'pink'
    },
    {
      value: 'lost',
      label: '寻回',
      description: '寻找走失宝贝，需要联系方式',
      icon: Search,
      color: 'orange'
    },
    {
      value: 'wanted',
      label: '求购',
      description: '发布求购信息，需要联系方式',
      icon: ShoppingCart,
      color: 'purple'
    }
  ];

  // 性别选项
  const genderOptions = [
    { value: 'male', label: '雄性 ♂', color: 'blue' },
    { value: 'female', label: '雌性 ♀', color: 'pink' }
  ];

  // 获取颜色类名
  const getColorClasses = (color: string, isSelected: boolean) => {
    const colorMap = {
      gray: isSelected 
        ? 'bg-gray-100 border-gray-300 text-gray-900' 
        : 'border-gray-200 text-gray-600 hover:border-gray-300',
      green: isSelected 
        ? 'bg-green-50 border-green-300 text-green-900' 
        : 'border-gray-200 text-gray-600 hover:border-green-200',
      pink: isSelected 
        ? 'bg-pink-50 border-pink-300 text-pink-900' 
        : 'border-gray-200 text-gray-600 hover:border-pink-200',
      orange: isSelected
        ? 'bg-orange-50 border-orange-300 text-orange-900'
        : 'border-gray-200 text-gray-600 hover:border-orange-200',
      blue: isSelected
        ? 'bg-blue-50 border-blue-300 text-blue-900'
        : 'border-gray-200 text-gray-600 hover:border-blue-200',
      purple: isSelected
        ? 'bg-purple-50 border-purple-300 text-purple-900'
        : 'border-gray-200 text-gray-600 hover:border-purple-200'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.gray;
  };

  return (
    <div className={cn('space-y-4', className)}>
      <label className="block text-sm font-medium text-gray-700">
        发布类型
      </label>

      {/* 类型选择 */}
      <div className="grid grid-cols-2 gap-3">
        {typeOptions.map((option) => {
          const Icon = option.icon;
          const isSelected = value === option.value;
          
          return (
            <button
              key={option.value}
              type="button"
              onClick={() => {
                onChange(option.value);
                // 如果不是配种类型，清除性别选择
                if (option.value !== 'breeding' && onGenderChange) {
                  onGenderChange('');
                }
              }}
              className={cn(
                'p-4 border-2 rounded-lg text-left transition-all duration-200',
                getColorClasses(option.color, isSelected),
                'hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2'
              )}
            >
              <div className="flex items-start space-x-3">
                <Icon className="h-5 w-5 mt-0.5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm">{option.label}</div>
                  <div className="text-xs mt-1 opacity-75">{option.description}</div>
                </div>
              </div>
            </button>
          );
        })}
      </div>

      {/* 配种性别选择 */}
      {value === 'breeding' && onGenderChange && (
        <div className="space-y-3">
          <label className="block text-sm font-medium text-gray-700">
            宝贝性别 <span className="text-red-500">*</span>
          </label>
          <div className="flex space-x-3">
            {genderOptions.map((option) => {
              const isSelected = gender === option.value;
              
              return (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => onGenderChange(option.value as 'male' | 'female')}
                  className={cn(
                    'flex-1 py-3 px-4 border-2 rounded-lg text-center transition-all duration-200',
                    getColorClasses(option.color, isSelected),
                    'hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2'
                  )}
                >
                  <div className="font-medium text-sm">{option.label}</div>
                </button>
              );
            })}
          </div>
          {value === 'breeding' && !gender && (
            <p className="text-sm text-red-600">配种信息需要选择宝贝性别</p>
          )}
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}

      {/* 说明文字 */}
      <div className="text-xs text-gray-500">
        <p>• 除展示分享外其他发布类型都需填写联系方式</p>
      </div>
    </div>
  );
};

export default PetTypeSelect;
