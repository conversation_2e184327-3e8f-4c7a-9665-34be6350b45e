'use client';

import React, { useState } from 'react';
import { UserX } from 'lucide-react';
import { petAPI } from '@/lib/cloudbase';
import { showToast } from '@/components/ui/Toast';
import Button from '@/components/ui/Button';
import { Modal, ModalBody } from '@/components/ui/Modal';

interface BlockUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  targetUserId: string;
  targetUserName: string;
  onSuccess?: () => void;
}

const BlockUserModal: React.FC<BlockUserModalProps> = ({
  isOpen,
  onClose,
  targetUserId,
  targetUserName,
  onSuccess
}) => {
  const [loading, setLoading] = useState(false);

  const handleClose = () => {
    onClose();
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const result = await petAPI.blockUser({
        targetUserId
      });

      if (result.success) {
        showToast.success('已拉黑该用户');
        onSuccess?.();
        handleClose();
      } else {
        showToast.error(result.message || '拉黑失败');
      }
    } catch (error: any) {
      console.error('拉黑用户失败:', error);
      showToast.error(error.message || '拉黑失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} showCloseButton={false}>
      <ModalBody>
        <div className="space-y-6">
          {/* 标题 */}
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-gray-100 rounded-lg">
              <UserX className="h-5 w-5 text-gray-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">拉黑用户</h3>
              <p className="text-sm text-gray-500">拉黑 {targetUserName}</p>
            </div>
          </div>

          {/* 说明 */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <p className="text-sm text-yellow-700">
              您将不会再看到该用户发布的内容，该用户也不能再联系您
            </p>
          </div>

          {/* 按钮 */}
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleClose}
              className="flex-1"
              disabled={loading}
            >
              取消
            </Button>
            <Button
              variant="danger"
              onClick={handleSubmit}
              loading={loading}
              className="flex-1"
            >
              确认拉黑
            </Button>
          </div>
        </div>
      </ModalBody>
    </Modal>
  );
};

export default BlockUserModal;
