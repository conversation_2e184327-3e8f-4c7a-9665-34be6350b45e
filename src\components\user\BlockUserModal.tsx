'use client';

import React, { useState } from 'react';
import { X, UserX } from 'lucide-react';
import { petAPI } from '@/lib/cloudbase';
import { showToast } from '@/components/ui/Toast';
import Button from '@/components/ui/Button';
import { Modal, ModalBody } from '@/components/ui/Modal';

interface BlockUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  targetUserId: string;
  targetUserName: string;
  onSuccess?: () => void;
}

const BlockUserModal: React.FC<BlockUserModalProps> = ({
  isOpen,
  onClose,
  targetUserId,
  targetUserName,
  onSuccess
}) => {
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);

  const handleClose = () => {
    setReason('');
    onClose();
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const result = await petAPI.blockUser({
        targetUserId,
        reason: reason || '用户选择拉黑'
      });

      if (result.success) {
        showToast.success('已拉黑该用户');
        onSuccess?.();
        handleClose();
      } else {
        showToast.error(result.message || '拉黑失败');
      }
    } catch (error: any) {
      console.error('拉黑用户失败:', error);
      showToast.error(error.message || '拉黑失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <ModalBody>
        <div className="space-y-6">
          {/* 标题 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gray-100 rounded-lg">
                <UserX className="h-5 w-5 text-gray-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">拉黑用户</h3>
                <p className="text-sm text-gray-500">拉黑 {targetUserName}</p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="h-5 w-5 text-gray-500" />
            </button>
          </div>

          {/* 说明 */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-medium text-yellow-800 mb-2">拉黑后的效果：</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• 您将不会再看到该用户发布的内容</li>
              <li>• 该用户无法查看您的个人资料</li>
              <li>• 您可以随时在设置中取消拉黑</li>
            </ul>
          </div>

          {/* 拉黑原因（可选） */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              拉黑原因（可选）
            </label>
            <textarea
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="请输入拉黑原因..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
              rows={3}
              maxLength={200}
            />
            <div className="text-xs text-gray-500 mt-1">
              {reason.length}/200
            </div>
          </div>

          {/* 按钮 */}
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleClose}
              className="flex-1"
              disabled={loading}
            >
              取消
            </Button>
            <Button
              variant="danger"
              onClick={handleSubmit}
              loading={loading}
              className="flex-1"
            >
              确认拉黑
            </Button>
          </div>
        </div>
      </ModalBody>
    </Modal>
  );
};

export default BlockUserModal;
