'use client';

import { useState, useEffect } from 'react';
import { petAPI } from '@/lib/cloudbase';
import { 
  TrophyIcon,
  ChatBubbleLeftRightIcon,
  ClockIcon,
  UserGroupIcon,
  FireIcon,
  CalendarIcon,
  EyeIcon,
  StarIcon
} from '@heroicons/react/24/outline';

interface Activity {
  _id: string;
  title: string;
  description: string;
  type: 'CONTEST' | 'VOTING' | 'DISCUSSION';
  status: 'DRAFT' | 'ACTIVE' | 'ENDED' | 'ARCHIVED';
  start_time: string;
  end_time: string;
  result_display_end_time: string;
  duration_days: number;
  result_display_days: number;
  config: any;
  statistics_summary?: {
    total_votes: number;
    total_comments: number;
    votes_by_option: Record<string, number>;
  };
  created_at: string;
}

export default function ActivitiesPage() {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [systemConfig, setSystemConfig] = useState<any>(null);
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  useEffect(() => {
    loadSystemConfig();
    loadActivities();
  }, []);

  const loadSystemConfig = async () => {
    try {
      const result = await petAPI.getSystemConfig();
      if (result.success) {
        setSystemConfig(result.data);
        
        // 如果活动系统未启用，显示提示
        if (!result.data.enabled) {
          setLoading(false);
          return;
        }
      }
    } catch (error) {
      console.error('加载系统配置失败:', error);
    }
  };

  const loadActivities = async () => {
    try {
      const result = await petAPI.getActivities({
        status: 'all',
        limit: 50,
        includeArchived: false
      });
      
      if (result.success) {
        setActivities(result.data || []);
      }
    } catch (error) {
      console.error('加载活动失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActivityTypeIcon = (type: string) => {
    switch (type) {
      case 'CONTEST':
        return TrophyIcon;
      case 'VOTING':
        return ChatBubbleLeftRightIcon;
      case 'DISCUSSION':
        return UserGroupIcon;
      default:
        return StarIcon;
    }
  };

  const getActivityTypeLabel = (type: string) => {
    switch (type) {
      case 'CONTEST':
        return '评选竞赛';
      case 'VOTING':
        return '投票话题';
      case 'DISCUSSION':
        return '讨论活动';
      default:
        return '未知类型';
    }
  };

  const getStatusBadge = (status: string, endTime: string) => {
    const now = new Date();
    const end = new Date(endTime);
    
    switch (status) {
      case 'ACTIVE':
        const timeLeft = Math.ceil((end.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <FireIcon className="h-3 w-3 mr-1" />
            进行中 (剩余{timeLeft}天)
          </span>
        );
      case 'ENDED':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <EyeIcon className="h-3 w-3 mr-1" />
            查看结果
          </span>
        );
      case 'ARCHIVED':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            已归档
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            草稿
          </span>
        );
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // 如果活动系统未启用
  if (!systemConfig?.enabled) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <TrophyIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">活动功能暂未开放</h3>
          <p className="mt-1 text-sm text-gray-500">
            敬请期待精彩的社区活动！
          </p>
        </div>
      </div>
    );
  }

  // 分类活动
  const activeActivities = activities.filter(a => a.status === 'ACTIVE');
  const endedActivities = activities.filter(a => a.status === 'ENDED');
  const upcomingActivities = activities.filter(a => a.status === 'DRAFT');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面头部 */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center">
              <TrophyIcon className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">活动中心</h1>
                <p className="text-gray-600">参与社区活动，展示你的宠物，分享你的观点</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 正在进行的活动 */}
        {activeActivities.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <FireIcon className="h-6 w-6 text-red-500 mr-2" />
              <h2 className="text-xl font-semibold text-gray-900">🔥 热门活动</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {activeActivities.map((activity) => (
                <ActivityCard
                  key={activity._id}
                  activity={activity}
                  onClick={(activity) => {
                    setSelectedActivity(activity);
                    setShowDetailModal(true);
                  }}
                />
              ))}
            </div>
          </div>
        )}

        {/* 查看结果的活动 */}
        {endedActivities.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <EyeIcon className="h-6 w-6 text-blue-500 mr-2" />
              <h2 className="text-xl font-semibold text-gray-900">📊 查看结果</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {endedActivities.map((activity) => (
                <ActivityCard
                  key={activity._id}
                  activity={activity}
                  onClick={(activity) => {
                    setSelectedActivity(activity);
                    setShowDetailModal(true);
                  }}
                />
              ))}
            </div>
          </div>
        )}

        {/* 即将开始的活动 */}
        {upcomingActivities.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <CalendarIcon className="h-6 w-6 text-green-500 mr-2" />
              <h2 className="text-xl font-semibold text-gray-900">📅 即将开始</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {upcomingActivities.map((activity) => (
                <ActivityCard
                  key={activity._id}
                  activity={activity}
                  onClick={(activity) => {
                    setSelectedActivity(activity);
                    setShowDetailModal(true);
                  }}
                />
              ))}
            </div>
          </div>
        )}

        {/* 无活动时的提示 */}
        {activities.length === 0 && (
          <div className="text-center py-12">
            <TrophyIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">暂无活动</h3>
            <p className="mt-1 text-sm text-gray-500">
              敬请期待精彩的社区活动！
            </p>
          </div>
        )}
      </div>

      {/* 活动详情弹窗 */}
      {showDetailModal && selectedActivity && (
        <ActivityDetailModal
          activity={selectedActivity}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedActivity(null);
          }}
          onActivityUpdate={() => {
            loadActivities(); // 重新加载活动列表
          }}
        />
      )}
    </div>
  );
}

// 活动卡片组件
const ActivityCard = ({ activity, onClick }: { activity: Activity; onClick: (activity: Activity) => void }) => {
  const IconComponent = getActivityTypeIcon(activity.type);

  const handleClick = () => {
    onClick(activity);
  };

  return (
    <div 
      onClick={handleClick}
      className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer"
    >
      <div className="p-6">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center">
            <IconComponent className="h-5 w-5 text-blue-600 mr-2" />
            <span className="text-sm text-gray-600">{getActivityTypeLabel(activity.type)}</span>
          </div>
          {getStatusBadge(activity.status, activity.end_time)}
        </div>
        
        <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
          {activity.title}
        </h3>
        
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
          {activity.description}
        </p>
        
        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center">
            <ClockIcon className="h-4 w-4 mr-1" />
            <span>{new Date(activity.start_time).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })} - {new Date(activity.end_time).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}</span>
          </div>
          
          {activity.statistics_summary && (
            <div className="flex items-center space-x-3">
              <div className="flex items-center">
                <UserGroupIcon className="h-4 w-4 mr-1" />
                <span>{activity.statistics_summary.total_votes}</span>
              </div>
              {activity.statistics_summary.total_comments > 0 && (
                <div className="flex items-center">
                  <ChatBubbleLeftRightIcon className="h-4 w-4 mr-1" />
                  <span>{activity.statistics_summary.total_comments}</span>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

function getActivityTypeIcon(type: string) {
  switch (type) {
    case 'CONTEST':
      return TrophyIcon;
    case 'VOTING':
      return ChatBubbleLeftRightIcon;
    case 'DISCUSSION':
      return UserGroupIcon;
    default:
      return StarIcon;
  }
}

function getActivityTypeLabel(type: string) {
  switch (type) {
    case 'CONTEST':
      return '评选竞赛';
    case 'VOTING':
      return '投票话题';
    case 'DISCUSSION':
      return '讨论活动';
    default:
      return '未知类型';
  }
}

function getStatusBadge(status: string, endTime: string) {
  const now = new Date();
  const end = new Date(endTime);
  
  switch (status) {
    case 'ACTIVE':
      const timeLeft = Math.ceil((end.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <FireIcon className="h-3 w-3 mr-1" />
          进行中 (剩余{timeLeft}天)
        </span>
      );
    case 'ENDED':
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          <EyeIcon className="h-3 w-3 mr-1" />
          查看结果
        </span>
      );
    case 'ARCHIVED':
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          已归档
        </span>
      );
    default:
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          草稿
        </span>
      );
  }
}

// 活动详情弹窗组件
const ActivityDetailModal = ({ activity, onClose, onActivityUpdate }: any) => {
  const [userParticipation, setUserParticipation] = useState<any>(null);
  const [comments, setComments] = useState<any[]>([]);
  const [newComment, setNewComment] = useState('');
  const [commenting, setCommenting] = useState(false);
  const [commentCooldown, setCommentCooldown] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadActivityDetail();
    loadComments();
  }, [activity._id]);

  // 评论冷却倒计时
  useEffect(() => {
    if (commentCooldown > 0) {
      const timer = setTimeout(() => {
        setCommentCooldown(commentCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [commentCooldown]);

  const loadActivityDetail = async () => {
    try {
      const result = await petAPI.getActivityDetail({ activity_id: activity._id });
      if (result.success) {
        setUserParticipation(result.data.user_participation);
      }
    } catch (error) {
      console.error('加载活动详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadComments = async () => {
    try {
      const result = await petAPI.getActivityComments({
        activity_id: activity._id,
        page: 1,
        limit: 50
      });
      if (result.success) {
        setComments(result.data || []);
      }
    } catch (error) {
      console.error('加载评论失败:', error);
    }
  };

  const handleVote = async (optionId: string) => {
    try {
      const result = await petAPI.voteInActivity({
        activity_id: activity._id,
        option_id: optionId
      });

      if (result.success) {
        alert('投票成功！');
        await loadActivityDetail(); // 重新加载活动详情
        onActivityUpdate(); // 更新父组件的活动列表
      }
    } catch (error: any) {
      console.error('投票失败:', error);
      alert('投票失败：' + (error?.message || '未知错误'));
    }
  };

  const handleComment = async () => {
    if (!newComment.trim() || commenting || commentCooldown > 0) return;

    setCommenting(true);
    try {
      const result = await petAPI.addActivityComment({
        activity_id: activity._id,
        content: newComment.trim()
      });

      if (result.success) {
        setNewComment('');
        setCommentCooldown(10); // 10秒冷却时间
        await loadComments(); // 重新加载评论
      }
    } catch (error: any) {
      console.error('评论失败:', error);
      alert('评论失败：' + (error?.message || '未知错误'));
    } finally {
      setCommenting(false);
    }
  };

  const getActivityTypeIcon = (type: string) => {
    switch (type) {
      case 'CONTEST':
        return TrophyIcon;
      case 'VOTING':
        return ChatBubbleLeftRightIcon;
      case 'DISCUSSION':
        return UserGroupIcon;
      default:
        return TrophyIcon;
    }
  };

  const getActivityTypeLabel = (type: string) => {
    switch (type) {
      case 'CONTEST':
        return '评选竞赛';
      case 'VOTING':
        return '投票话题';
      case 'DISCUSSION':
        return '讨论活动';
      default:
        return '未知类型';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const getRemainingTime = (endTime: string) => {
    const now = new Date();
    const end = new Date(endTime);
    const diff = end.getTime() - now.getTime();

    if (diff <= 0) return '已结束';

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (days > 0) return `剩余 ${days} 天 ${hours} 小时`;
    return `剩余 ${hours} 小时`;
  };

  const IconComponent = getActivityTypeIcon(activity.type);
  const hasVoted = userParticipation !== null;
  const canComment = activity.config.comments_enabled &&
                    (!activity.config.comments_after_vote || hasVoted) &&
                    activity.status === 'ACTIVE';

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">活动详情</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-6">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <>
              {/* 活动头部信息 */}
              <div className="mb-6">
                <div className="flex items-center mb-4">
                  <IconComponent className="h-8 w-8 text-blue-600 mr-3" />
                  <div>
                    <div className="flex items-center">
                      <span className="text-sm text-gray-600 mr-3">{getActivityTypeLabel(activity.type)}</span>
                      {activity.status === 'ACTIVE' && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <FireIcon className="h-3 w-3 mr-1" />
                          进行中
                        </span>
                      )}
                      {activity.status === 'ENDED' && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          查看结果
                        </span>
                      )}
                    </div>
                    <h1 className="text-2xl font-bold text-gray-900 mt-1">{activity.title}</h1>
                  </div>
                </div>

                <p className="text-gray-600 mb-4">{activity.description}</p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                  <div className="flex items-center">
                    <ClockIcon className="h-4 w-4 mr-2" />
                    <span>开始时间：{formatDate(activity.start_time)}</span>
                  </div>
                  <div className="flex items-center">
                    <ClockIcon className="h-4 w-4 mr-2" />
                    <span>结束时间：{formatDate(activity.end_time)}</span>
                  </div>
                  <div className="flex items-center">
                    <UserGroupIcon className="h-4 w-4 mr-2" />
                    <span>参与人数：{activity.statistics_summary?.total_votes || 0}</span>
                  </div>
                </div>

                {activity.status === 'ACTIVE' && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center">
                      <FireIcon className="h-5 w-5 text-blue-600 mr-2" />
                      <span className="text-blue-800 font-medium">
                        {getRemainingTime(activity.end_time)}
                      </span>
                    </div>
                  </div>
                )}
              </div>

              {/* 投票区域 */}
              {activity.type === 'VOTING' && (
                <VotingSection
                  activity={activity}
                  hasVoted={hasVoted}
                  userParticipation={userParticipation}
                  onVote={handleVote}
                />
              )}

              {/* 评选竞赛展示区域 */}
              {activity.type === 'CONTEST' && (
                <ContestSection
                  activity={activity}
                  hasVoted={hasVoted}
                  userParticipation={userParticipation}
                />
              )}

              {/* 评论区域 */}
              <CommentSection
                activity={activity}
                comments={comments}
                canComment={canComment}
                newComment={newComment}
                setNewComment={setNewComment}
                commenting={commenting}
                commentCooldown={commentCooldown}
                onComment={handleComment}
                hasVoted={hasVoted}
              />
            </>
          )}
        </div>
      </div>
    </div>
  );
};

// 投票区域组件
const VotingSection = ({ activity, hasVoted, userParticipation, onVote }: any) => (
  <div className="bg-white rounded-lg border p-6 mb-6">
    <h2 className="text-lg font-semibold text-gray-900 mb-4">投票选择</h2>

    {hasVoted ? (
      <div className="space-y-3">
        {activity.config.options?.map((option: any) => {
          const voteCount = activity.statistics_summary?.votes_by_option?.[option.id] || 0;
          const totalVotes = activity.statistics_summary?.total_votes || 0;
          const percentage = totalVotes > 0 ? Math.round((voteCount / totalVotes) * 100) : 0;
          const isUserChoice = userParticipation?.option_id === option.id;

          return (
            <div key={option.id} className={`border rounded-lg p-4 ${isUserChoice ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}>
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-gray-900 flex items-center">
                  {option.title}
                  {isUserChoice && (
                    <svg className="h-5 w-5 text-blue-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                </h3>
                <span className="text-sm text-gray-600">{percentage}%</span>
              </div>
              <p className="text-gray-600 text-sm mb-3">{option.description}</p>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${percentage}%` }}
                />
              </div>
              <div className="text-xs text-gray-500 mt-1">{voteCount} 票</div>
            </div>
          );
        })}
      </div>
    ) : (
      <div className="space-y-3">
        {activity.config.options?.map((option: any) => (
          <div key={option.id} className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer transition-colors"
               onClick={() => activity.status === 'ACTIVE' && onVote(option.id)}>
            <h3 className="font-medium text-gray-900">{option.title}</h3>
            <p className="text-gray-600 text-sm">{option.description}</p>
            {activity.status === 'ACTIVE' && (
              <button className="mt-2 text-blue-600 text-sm hover:text-blue-800">
                选择此项
              </button>
            )}
          </div>
        ))}
      </div>
    )}
  </div>
);

// 评选竞赛区域组件
const ContestSection = ({ activity, hasVoted, userParticipation }: any) => (
  <div className="bg-white rounded-lg border p-6 mb-6">
    <h2 className="text-lg font-semibold text-gray-900 mb-4">评选规则</h2>
    <div className="space-y-3 text-gray-600">
      <p>• 5星评价前{activity.config.rules?.qualificationThreshold?.topCount || 20}名自动入选</p>
      <p>• 管理员从入选作品中评选出最终前{activity.config.rules?.finalSelection?.finalCount || 10}名</p>
      <p>• 活动时间：{activity.duration_days}天</p>
    </div>

    {activity.status === 'ENDED' && activity.final_results && (
      <div className="mt-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">🏆 获奖名单</h3>
        <div className="space-y-3">
          {activity.final_results.map((result: any, index: number) => (
            <div key={result.id} className="flex items-center p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-center w-8 h-8 bg-yellow-100 text-yellow-800 rounded-full mr-3">
                {index + 1}
              </div>
              <div>
                <h4 className="font-medium text-gray-900">{result.participant_name}</h4>
                <p className="text-sm text-gray-600">{result.award_title}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    )}
  </div>
);

// 评论区域组件
const CommentSection = ({
  activity,
  comments,
  canComment,
  newComment,
  setNewComment,
  commenting,
  commentCooldown,
  onComment,
  hasVoted
}: any) => (
  <div className="bg-white rounded-lg border p-6">
    <h2 className="text-lg font-semibold text-gray-900 mb-4">
      讨论区 ({comments.length})
    </h2>

    {/* 评论输入 */}
    {canComment ? (
      <div className="mb-6">
        <div className="flex space-x-2 mb-3">
          {['太可爱了！', '好漂亮', '想要同款', '萌化了'].map(phrase => (
            <button
              key={phrase}
              onClick={() => setNewComment(phrase)}
              className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200"
            >
              {phrase}
            </button>
          ))}
        </div>
        <textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder="分享你的想法... (2-100字)"
          maxLength={100}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          rows={3}
          disabled={commentCooldown > 0}
        />
        <div className="flex justify-between items-center mt-2">
          <span className="text-sm text-gray-500">{newComment.length}/100</span>
          <button
            onClick={onComment}
            disabled={commenting || newComment.trim().length < 2 || commentCooldown > 0}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {commenting ? '发表中...' : commentCooldown > 0 ? `等待 ${commentCooldown}s` : '发表评论'}
          </button>
        </div>
        {commentCooldown > 0 && (
          <div className="mt-2 text-sm text-orange-600">
            ⏰ 为了维护良好的讨论环境，请等待 {commentCooldown} 秒后再发表评论
          </div>
        )}
      </div>
    ) : (
      <div className="mb-6 p-4 bg-gray-50 rounded-lg text-center">
        <p className="text-gray-600">
          {!hasVoted ? '投票后即可参与讨论 🗳️' :
           activity.status !== 'ACTIVE' ? '活动已结束，评论功能已关闭' :
           '评论功能暂未开放'}
        </p>
      </div>
    )}

    {/* 评论列表 */}
    <div className="space-y-4">
      {comments.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          暂无评论，快来发表第一条评论吧！
        </div>
      ) : (
        comments.map((comment: any) => (
          <div key={comment._id} className="border-b border-gray-200 pb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium text-gray-900">{comment.username}</span>
              <span className="text-sm text-gray-500">
                {new Date(comment.created_at).toLocaleString('zh-CN')}
              </span>
            </div>
            <p className="text-gray-700">{comment.content}</p>
            {comment.like_count > 0 && (
              <div className="mt-2 text-sm text-gray-500">
                👍 {comment.like_count}
              </div>
            )}
          </div>
        ))
      )}
    </div>
  </div>
);
