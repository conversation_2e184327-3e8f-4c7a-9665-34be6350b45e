'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Home, Plus, User, Search, Bell } from 'lucide-react';
import { useAuthContext } from '@/components/auth/AuthProvider';
import { cn } from '@/utils';

const BottomNav: React.FC = () => {
  const pathname = usePathname();
  const { user, isLoggedIn } = useAuthContext();

  const navItems = [
    {
      href: '/',
      icon: Home,
      label: '首页',
      active: pathname === '/',
    },
    {
      href: '/search',
      icon: Search,
      label: '搜索',
      active: pathname === '/search',
    },
    {
      href: '/upload',
      icon: Plus,
      label: '发布',
      active: pathname === '/upload',
      requireAuth: true,
    },
    {
      href: '/notifications',
      icon: Bell,
      label: '通知',
      active: pathname === '/notifications',
      requireAuth: true,
    },
    {
      href: isLoggedIn ? '/profile' : '/login',
      icon: User,
      label: '我',
      active: pathname === '/profile',
    },
  ];

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 md:hidden">
      <div className="flex items-center justify-around h-16">
        {navItems.map((item) => {
          const Icon = item.icon;
          
          // 如果需要登录但用户未登录，显示但点击时跳转到登录
          const href = item.requireAuth && !isLoggedIn ? '/login' : item.href;
          const isActive = item.active;
          
          return (
            <Link
              key={item.label}
              href={href}
              className={cn(
                'flex flex-col items-center justify-center flex-1 h-full transition-colors',
                isActive
                  ? 'text-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              )}
            >
              <Icon className={cn(
                'h-5 w-5 mb-1',
                isActive && 'text-primary-600'
              )} />
              <span className={cn(
                'text-xs font-medium',
                isActive && 'text-primary-600'
              )}>
                {item.label}
              </span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
};

export default BottomNav;
