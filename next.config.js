/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    unoptimized: true,
    domains: [
      '7969-yichongyuzhou-3g9112qwf5f3487b-1368816056.tcb.qcloud.la',
      'images.unsplash.com',
      'plus.unsplash.com'
    ]
  },
  // 设置正确的basePath和assetPrefix用于静态托管
  // basePath: '/pet-platform-final',
  // assetPrefix: '/pet-platform-final/',
  // 添加缓存控制
  generateBuildId: async () => {
    return `build-${Date.now()}`
  }
}

module.exports = nextConfig
