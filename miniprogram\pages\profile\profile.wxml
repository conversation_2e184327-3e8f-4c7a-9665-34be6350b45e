<!--个人中心模板-->
<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-info">
      <image src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" class="avatar"></image>
      <view class="user-details">
        <view class="nickname">{{userInfo.nickName || '未登录'}}</view>
        <view class="user-id">ID: {{userInfo.openid || '---'}}</view>
      </view>
    </view>
    <view class="user-stats">
      <view class="stat-item">
        <view class="stat-number">0</view>
        <view class="stat-label">发布</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">0</view>
        <view class="stat-label">收藏</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">0</view>
        <view class="stat-label">关注</view>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-item" bindtap="viewMyPosts">
      <view class="menu-icon">📝</view>
      <view class="menu-text">我的发布</view>
      <view class="menu-arrow">></view>
    </view>
    <view class="menu-item" bindtap="viewMyFavorites">
      <view class="menu-icon">❤️</view>
      <view class="menu-text">我的收藏</view>
      <view class="menu-arrow">></view>
    </view>
    <view class="menu-item" bindtap="goToSettings">
      <view class="menu-icon">⚙️</view>
      <view class="menu-text">设置</view>
      <view class="menu-arrow">></view>
    </view>
    <view class="menu-item" bindtap="contactService">
      <view class="menu-icon">📞</view>
      <view class="menu-text">联系客服</view>
      <view class="menu-arrow">></view>
    </view>
  </view>
</view>
