'use client';

import React, { useState, useEffect } from 'react';
import { Heart, Grid, List } from 'lucide-react';
import Header from '@/components/layout/Header';
import PetCard from '@/components/home/<USER>';
import Button from '@/components/ui/Button';
import { useAuthContext } from '@/components/auth/AuthProvider';
import { Post } from '@/types';
import { petAPI } from '@/lib/cloudbase';
import { PetCardSkeleton } from '@/components/ui/Loading';
import { showToast } from '@/components/ui/Toast';
import { cn } from '@/utils';

type ViewMode = 'grid' | 'list';

const FavoritesPage: React.FC = () => {
  const { user, isLoggedIn } = useAuthContext();
  const [favorites, setFavorites] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<ViewMode>('grid');

  // 获取收藏列表
  const fetchFavorites = async () => {
    try {
      setLoading(true);
      const result = await petAPI.getUserBookmarks({ limit: 50 });
      if (result.success) {
        setFavorites(result.data || []);
      }
    } catch (error: any) {
      console.error('获取收藏列表失败:', error);
      showToast.error('获取收藏列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFavorites();
  }, []);

  // 移除登录检查，直接显示收藏页面

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* 页面标题和控制 */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Heart className="h-6 w-6 text-red-500 mr-2" />
              我的收藏
            </h1>
            <span className="text-sm text-gray-500">
              {favorites.length} 个收藏
            </span>
          </div>
          
          {/* 视图切换 */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={cn(
                'p-2 rounded-lg transition-colors',
                viewMode === 'grid'
                  ? 'bg-primary-600 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-100'
              )}
            >
              <Grid className="h-4 w-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={cn(
                'p-2 rounded-lg transition-colors',
                viewMode === 'list'
                  ? 'bg-primary-600 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-100'
              )}
            >
              <List className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* 收藏内容 */}
        {loading ? (
          <div className={cn(
            viewMode === 'grid'
              ? 'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4'
              : 'space-y-4'
          )}>
            {Array.from({ length: 8 }).map((_, index) => (
              <PetCardSkeleton key={index} />
            ))}
          </div>
        ) : favorites.length > 0 ? (
          <div className={cn(
            viewMode === 'grid'
              ? 'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4'
              : 'space-y-4'
          )}>
            {favorites.map((post) => (
              <PetCard 
                key={post._id} 
                post={post}
                showRemoveFromFavorites
                onRemoveFromFavorites={() => {
                  setFavorites(prev => prev.filter(p => p._id !== post._id));
                  showToast.success('已从收藏中移除');
                }}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-500">
              <div className="text-6xl mb-4">💝</div>
              <p className="text-lg font-medium mb-2">还没有收藏任何宠物</p>
              <p className="text-sm mb-6">去首页看看有什么喜欢的宠物吧</p>
              <Button onClick={() => window.location.href = '/'}>
                去首页逛逛
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FavoritesPage;
