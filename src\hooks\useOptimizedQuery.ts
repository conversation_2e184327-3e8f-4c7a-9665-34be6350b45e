import { useState, useEffect, useCallback, useRef } from 'react';
import { queryOptimizer } from '@/utils/queryOptimizer';
import { performanceMonitor } from '@/utils/performanceMonitor';
import { dataCache } from '@/utils/cacheManager';

export interface UseOptimizedQueryOptions {
  enabled?: boolean;
  refetchOnWindowFocus?: boolean;
  refetchInterval?: number;
  staleTime?: number;
  cacheTime?: number;
  retry?: number;
  retryDelay?: number;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
}

export interface UseOptimizedQueryResult<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
  isStale: boolean;
  lastUpdated: number | null;
}

// 优化的数据查询Hook
export function useOptimizedQuery<T>(
  queryKey: string,
  queryFn: () => Promise<T>,
  options: UseOptimizedQueryOptions = {}
): UseOptimizedQueryResult<T> {
  const {
    enabled = true,
    refetchOnWindowFocus = false,
    refetchInterval,
    staleTime = 5 * 60 * 1000, // 5分钟
    cacheTime = 10 * 60 * 1000, // 10分钟
    retry = 3,
    retryDelay = 1000,
    onSuccess,
    onError
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [lastUpdated, setLastUpdated] = useState<number | null>(null);

  const retryCountRef = useRef(0);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 检查数据是否过期
  const isStale = lastUpdated ? Date.now() - lastUpdated > staleTime : true;

  // 执行查询
  const executeQuery = useCallback(async (isRetry = false) => {
    if (!enabled) return;

    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    if (!isRetry) {
      setLoading(true);
      setError(null);
    }

    const startTime = Date.now();

    try {
      // 先尝试从缓存获取
      const cachedData = dataCache.get(queryKey);
      if (cachedData && !isStale) {
        setData(cachedData);
        setLoading(false);
        onSuccess?.(cachedData);
        return;
      }

      // 执行查询
      const result = await queryFn();
      
      // 检查请求是否被取消
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      // 更新状态
      setData(result);
      setError(null);
      setLastUpdated(Date.now());
      retryCountRef.current = 0;

      // 缓存结果
      dataCache.set(queryKey, result, cacheTime);

      // 记录性能
      const duration = Date.now() - startTime;
      performanceMonitor.recordApiResponse(queryKey, duration);

      onSuccess?.(result);

    } catch (err) {
      const error = err as Error;
      
      // 检查请求是否被取消
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      // 重试逻辑
      if (retryCountRef.current < retry) {
        retryCountRef.current++;
        const delay = retryDelay * Math.pow(2, retryCountRef.current - 1); // 指数退避
        
        setTimeout(() => {
          executeQuery(true);
        }, delay);
        
        return;
      }

      setError(error);
      onError?.(error);

      // 记录错误性能
      const duration = Date.now() - startTime;
      performanceMonitor.recordApiResponse(`${queryKey}_error`, duration);
    } finally {
      if (!isRetry) {
        setLoading(false);
      }
    }
  }, [queryKey, queryFn, enabled, isStale, staleTime, cacheTime, retry, retryDelay, onSuccess, onError]);

  // 手动刷新
  const refetch = useCallback(async () => {
    retryCountRef.current = 0;
    await executeQuery();
  }, [executeQuery]);

  // 初始查询
  useEffect(() => {
    if (enabled) {
      executeQuery();
    }

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [executeQuery, enabled]);

  // 窗口焦点重新获取
  useEffect(() => {
    if (!refetchOnWindowFocus) return;

    const handleFocus = () => {
      if (isStale) {
        refetch();
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [refetchOnWindowFocus, isStale, refetch]);

  // 定时刷新
  useEffect(() => {
    if (!refetchInterval) return;

    const interval = setInterval(() => {
      if (enabled && !loading) {
        refetch();
      }
    }, refetchInterval);

    return () => clearInterval(interval);
  }, [refetchInterval, enabled, loading, refetch]);

  return {
    data,
    loading,
    error,
    refetch,
    isStale,
    lastUpdated
  };
}

// 优化的帖子查询Hook
export function useOptimizedPosts(params: {
  page?: number;
  limit?: number;
  category?: string;
  userId?: string;
  sortBy?: string;
} = {}) {
  const queryKey = `posts_${JSON.stringify(params)}`;
  
  const queryFn = useCallback(async () => {
    return await queryOptimizer.optimizedPostQuery({
      ...params,
      includeUserInfo: true,
      includeCategoryInfo: true,
      useCache: true
    });
  }, [params]);

  return useOptimizedQuery(queryKey, queryFn, {
    staleTime: 2 * 60 * 1000, // 帖子数据2分钟过期
    cacheTime: 5 * 60 * 1000, // 缓存5分钟
    refetchOnWindowFocus: true
  });
}

// 无限滚动Hook
export function useInfiniteQuery<T>(
  queryKey: string,
  queryFn: (page: number) => Promise<{ data: T[]; hasMore: boolean }>,
  options: UseOptimizedQueryOptions = {}
) {
  const [allData, setAllData] = useState<T[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const loadMore = useCallback(async () => {
    if (loading || !hasMore) return;

    setLoading(true);
    setError(null);

    try {
      const result = await queryFn(page);
      
      setAllData(prev => [...prev, ...result.data]);
      setHasMore(result.hasMore);
      setPage(prev => prev + 1);

    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [queryFn, page, loading, hasMore]);

  const reset = useCallback(() => {
    setAllData([]);
    setHasMore(true);
    setPage(1);
    setError(null);
  }, []);

  // 初始加载
  useEffect(() => {
    if (allData.length === 0 && hasMore) {
      loadMore();
    }
  }, []);

  return {
    data: allData,
    loading,
    error,
    hasMore,
    loadMore,
    reset
  };
}

// 预加载Hook
export function usePreload() {
  const preloadedQueries = useRef(new Set<string>());

  const preload = useCallback(async (queryKey: string, queryFn: () => Promise<any>) => {
    if (preloadedQueries.current.has(queryKey)) {
      return;
    }

    try {
      const result = await queryFn();
      dataCache.set(queryKey, result, 5 * 60 * 1000); // 预加载数据缓存5分钟
      preloadedQueries.current.add(queryKey);
    } catch (error) {
      console.warn('预加载失败:', queryKey, error);
    }
  }, []);

  return { preload };
}
