import React, { useState } from 'react';
import { Plus, User, Menu, X, LogOut, Camera, MessageCircle } from 'lucide-react';
import Button from '@/components/ui/Button';
import { useAuthContext } from '@/components/auth/AuthProvider';
import LoginModal from '@/components/auth/LoginModal';
import Link from 'next/link';
import ActivityBanner from '@/components/activity/ActivityBanner';
import { NotificationModal } from '@/components/notifications/NotificationModal';

const Header: React.FC = () => {
  const { user, isLoggedIn, logout, refreshLoginState } = useAuthContext();
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);

  const handleLoginClick = () => {
    setShowLoginModal(true);
  };

  const handleLogout = async () => {
    await logout();
    setShowMobileMenu(false);
  };

  return (
    <>
      <header className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">🐾</span>
              </div>
              <span className="text-xl font-bold text-gray-900 hidden sm:block">
                宠物交易平台
              </span>
            </Link>

            {/* 活动入口 - 小型横幅 */}
            <ActivityBanner />

            {/* 右侧按钮 - 桌面端 */}
            <div className="hidden md:flex items-center space-x-3">
              {isLoggedIn ? (
                <>
                  {/* 通知按钮 */}
                  <Button
                    variant="outline"
                    icon={<MessageCircle className="h-4 w-4" />}
                    onClick={() => setShowNotifications(true)}
                  >
                    通知
                  </Button>

                  {/* 我的按钮 */}
                  <Link href="/profile">
                    <Button variant="outline" icon={<User className="h-4 w-4" />}>
                      我
                    </Button>
                  </Link>

                  {/* 退出按钮 */}
                  <Button
                    variant="outline"
                    icon={<LogOut className="h-4 w-4" />}
                    onClick={handleLogout}
                    className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300"
                  >
                    退出
                  </Button>
                </>
              ) : (
                <Button onClick={handleLoginClick}>
                  登录
                </Button>
              )}
            </div>

            {/* 移动端菜单按钮 */}
            <div className="md:hidden">
              <button
                onClick={() => setShowMobileMenu(!showMobileMenu)}
                className="p-2 rounded-lg hover:bg-gray-100"
              >
                {showMobileMenu ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </button>
            </div>
          </div>

          {/* 移除移动端搜索框 - 只保留筛选功能 */}
        </div>

        {/* 移动端菜单 */}
        {showMobileMenu && (
          <div className="md:hidden border-t border-gray-200 bg-white">
            <div className="px-4 py-4 space-y-3">
              {isLoggedIn ? (
                <>
                  <div className="pb-3 border-b border-gray-200">
                    <div>
                      <p className="font-medium text-gray-900">{user?.nickname}</p>
                      <p className="text-sm text-gray-500">{user?.email}</p>
                    </div>
                  </div>
                  
                  <button
                    className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 w-full text-left"
                    onClick={() => {
                      setShowNotifications(true);
                      setShowMobileMenu(false);
                    }}
                  >
                    <MessageCircle className="h-5 w-5 text-gray-600" />
                    <span>通知</span>
                  </button>
                  
                  <Link
                    href="/profile"
                    className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100"
                    onClick={() => setShowMobileMenu(false)}
                  >
                    <User className="h-5 w-5 text-gray-600" />
                    <span>我的主页</span>
                  </Link>
                  
                  <button
                    onClick={handleLogout}
                    className="flex items-center space-x-3 p-3 rounded-lg hover:bg-red-50 w-full text-left"
                  >
                    <LogOut className="h-5 w-5 text-red-600" />
                    <span className="text-red-600">退出</span>
                  </button>
                </>
              ) : (
                <Button
                  onClick={() => {
                    handleLoginClick();
                    setShowMobileMenu(false);
                  }}
                  className="w-full"
                >
                  登录
                </Button>
              )}
            </div>
          </div>
        )}
      </header>

      {/* 登录模态框 */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        onSuccess={async () => {
          // 关闭移动菜单
          setShowMobileMenu(false);
          // 强制刷新登录状态，确保UI立即更新
          await refreshLoginState();
          // 不立即关闭登录弹窗，让LoginModal自己处理
        }}
      />

      {/* 通知模态框 */}
      <NotificationModal
        isOpen={showNotifications}
        onClose={() => setShowNotifications(false)}
      />
    </>
  );
};

export default Header;
