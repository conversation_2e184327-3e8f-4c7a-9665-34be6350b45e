(()=>{var e={};e.id=495,e.ids=[495],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},89313:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>d}),t(20914),t(16953),t(35866);var r=t(23191),a=t(88716),l=t(37922),i=t.n(l),c=t(95231),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);t.d(s,n);let d=["",{children:["profile",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,20914)),"D:\\web-cloudbase-project\\src\\app\\profile\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\web-cloudbase-project\\src\\app\\profile\\[id]\\page.tsx"],x="/profile/[id]/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/profile/[id]/page",pathname:"/profile/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},96182:(e,s,t)=>{Promise.resolve().then(t.bind(t,69216))},86333:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},82200:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]])},17069:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},74975:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},69216:(e,s,t)=>{"use strict";t.d(s,{default:()=>I});var r=t(10326),a=t(17577),l=t(35047),i=t(86333),c=t(924),n=t(67427),d=t(17069),o=t(88378),x=t(76557);let m=(0,x.Z)("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);var u=t(82200),h=t(67187);let g=(0,x.Z)("UserMinus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var p=t(74975),y=t(41828),f=t(35659),j=t(99837),v=t(22502),b=t(16545),N=t(20603),w=t(28295),k=t(28676);let C=[{id:1,label:"疑似骗子",description:"该用户存在欺诈行为或疑似诈骗"}],_=({isOpen:e,onClose:s,targetUserId:t,targetUserName:l,onSuccess:i})=>{let[c,n]=(0,a.useState)(1),[d,o]=(0,a.useState)(!1),x=()=>{n(1),s()},m=async()=>{try{o(!0);let e=await y.petAPI.reportUser({targetUserId:t,reason:"疑似骗子"});e.success?(N.C.success("举报提交成功，我们会尽快处理"),i?.(),x()):N.C.error(e.message||"举报失败")}catch(e){console.error("举报用户失败:",e),N.C.error(e.message||"举报失败")}finally{o(!1)}};return r.jsx(k.u_,{isOpen:e,onClose:x,showCloseButton:!1,children:r.jsx(k.fe,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"p-2 bg-red-100 rounded-lg",children:r.jsx(u.Z,{className:"h-5 w-5 text-red-600"})}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"举报用户"}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["举报 ",l]})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"请选择举报原因："}),r.jsx("div",{className:"space-y-2",children:C.map(e=>(0,r.jsxs)("label",{className:`flex items-start space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${c===e.id?"border-red-500 bg-red-50":"border-gray-200 hover:bg-gray-50"}`,children:[r.jsx("input",{type:"radio",name:"reason",value:e.id,checked:c===e.id,onChange:()=>n(e.id),className:"mt-1 text-red-600 focus:ring-red-500"}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("div",{className:"font-medium text-gray-900",children:e.label}),r.jsx("div",{className:"text-sm text-gray-500",children:e.description})]})]},e.id))})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[r.jsx(j.Z,{variant:"outline",onClick:x,className:"flex-1",disabled:d,children:"取消"}),r.jsx(j.Z,{variant:"danger",onClick:m,loading:d,className:"flex-1",disabled:!1,children:"提交举报"})]})]})})})},Z=({isOpen:e,onClose:s,targetUserId:t,targetUserName:l,onSuccess:i})=>{let[c,n]=(0,a.useState)(!1),d=()=>{s()},o=async()=>{try{n(!0);let e=await y.petAPI.blockUser({targetUserId:t});e.success?(N.C.success("已拉黑该用户"),i?.(),d()):N.C.error(e.message||"拉黑失败")}catch(e){console.error("拉黑用户失败:",e),N.C.error(e.message||"拉黑失败")}finally{n(!1)}};return r.jsx(k.u_,{isOpen:e,onClose:d,showCloseButton:!1,children:r.jsx(k.fe,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"p-2 bg-gray-100 rounded-lg",children:r.jsx(h.Z,{className:"h-5 w-5 text-gray-600"})}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"拉黑用户"}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["拉黑 ",l]})]})]}),r.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:r.jsx("p",{className:"text-sm text-yellow-700",children:"您将不会再看到该用户发布的内容，该用户也不能再联系您"})}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[r.jsx(j.Z,{variant:"outline",onClick:d,className:"flex-1",disabled:c,children:"取消"}),r.jsx(j.Z,{variant:"danger",onClick:o,loading:c,className:"flex-1",children:"确认拉黑"})]})]})})})};var P=t(68197);let I=({userId:e})=>{let s=(0,l.useParams)(),t=(0,l.useRouter)(),{user:x,isLoggedIn:k}=(0,f.E)(),C=e||s.id,[I,U]=(0,a.useState)(null),[S,A]=(0,a.useState)([]),[E,q]=(0,a.useState)(!0),[M,D]=(0,a.useState)(!1),[z,O]=(0,a.useState)(!1),[G,L]=(0,a.useState)("created_at"),[F,T]=(0,a.useState)(!1),[H,B]=(0,a.useState)(!1),[R,V]=(0,a.useState)(!1),[X,$]=(0,a.useState)(!1),J=x?._id===C,K=async()=>{try{if(q(!0),!C){N.C.error("用户ID无效"),t.push("/");return}let e=await y.petAPI.getUserInfo({userId:C});if(e.success){if(U(e.data),k&&x&&x._id!==C)try{let e=await y.petAPI.getUserFollowing({targetUserId:x._id,limit:1e3});if(e.success){let s=e.data.some(e=>e.following_id===C);O(s)}}catch(e){console.warn("检查关注状态失败:",e)}}else N.C.error("用户不存在"),t.push("/")}catch(e){console.error("获取用户信息失败:",e),N.C.error("获取用户信息失败"),t.push("/")}finally{q(!1)}},Q=async()=>{try{D(!0);let e=await y.petAPI.getUserPosts({targetUserId:C,limit:50,offset:0});e.success&&A(e.data||[])}catch(e){console.error("获取用户帖子失败:",e)}finally{D(!1)}},W=async()=>{if(!k){N.C.warning("请先登录");return}try{let e=await y.petAPI.toggleFollow({targetUserId:C});e.success?(O("followed"===e.action),N.C.success(e.message),I&&U(s=>s?{...s,followers_count:(s.followers_count||0)+("followed"===e.action?1:-1)}:null)):N.C.error(e.message||"操作失败")}catch(e){N.C.error(e.message||"操作失败")}};if((0,a.useEffect)(()=>{C&&K()},[C]),(0,a.useEffect)(()=>{C&&Q()},[C,G]),(0,a.useEffect)(()=>{if(C&&k&&x&&I){let e=async()=>{try{let e=await y.petAPI.getUserFollowing({targetUserId:x._id,limit:1e3});if(e.success){let s=e.data.some(e=>e.following_id===C);O(s)}}catch(e){console.warn("检查关注状态失败:",e)}};x._id!==C&&e()}},[k,x,C,I]),E)return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[r.jsx("div",{className:"bg-white border-b border-gray-200",children:r.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:r.jsx("div",{className:"flex items-center h-16",children:r.jsx("button",{onClick:()=>t.push("/"),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:r.jsx(i.Z,{className:"h-5 w-5"})})})})}),r.jsx(b.LL,{})]});if(!I)return r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"用户不存在"}),r.jsx(j.Z,{onClick:()=>t.push("/"),children:"返回首页"})]})});let Y=[{value:"created_at",label:"最新",icon:c.Z},{value:"likes_count",label:"最受欢迎",icon:n.Z},{value:"wants_count",label:"收藏最多",icon:d.Z}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[r.jsx("div",{className:"bg-white border-b border-gray-200 sticky top-0 z-10",children:r.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("button",{onClick:()=>t.push("/"),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:r.jsx(i.Z,{className:"h-5 w-5"})}),r.jsx("h1",{className:"text-lg font-semibold text-gray-900",children:I.nickname})]}),J?r.jsx(j.Z,{variant:"outline",icon:r.jsx(o.Z,{className:"h-4 w-4"}),onClick:()=>t.push("/profile"),children:"个人设置"}):k&&(0,r.jsxs)("div",{className:"relative",children:[r.jsx("button",{onClick:()=>T(!F),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:r.jsx(m,{className:"h-5 w-5 text-gray-600"})}),F&&(0,r.jsxs)("div",{className:"absolute right-0 top-full mt-2 w-40 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-20",children:[(0,r.jsxs)("button",{onClick:()=>{B(!0),T(!1)},className:"flex items-center space-x-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-gray-100",children:[r.jsx(u.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"举报用户"})]}),(0,r.jsxs)("button",{onClick:()=>{V(!0),T(!1)},className:"flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[r.jsx(h.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"拉黑用户"})]})]})]})]})})}),r.jsx("div",{className:"bg-white border-b border-gray-200",children:r.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-6",children:[r.jsx("div",{className:"relative",children:I.avatar_url?r.jsx("img",{src:I.avatar_url,alt:I.nickname,className:"w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg"}):r.jsx("div",{className:"w-24 h-24 rounded-full bg-gray-300 border-4 border-white shadow-lg flex items-center justify-center",children:r.jsx("span",{className:"text-2xl font-bold text-gray-600",children:I.nickname.charAt(0).toUpperCase()})})}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:I.nickname}),I.bio?r.jsx("p",{className:"text-gray-600 max-w-md",children:I.bio}):r.jsx("p",{className:"text-gray-400 italic",children:J?"点击添加简介，让大家了解你":"这个人很懒，什么都没留下"})]}),(0,r.jsxs)("div",{className:"flex space-x-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-2xl font-bold text-gray-900",children:(0,w.uf)(I.total_likes||0)}),r.jsx("div",{className:"text-sm text-gray-500",children:"获赞"})]}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-2xl font-bold text-gray-900",children:(0,w.uf)(I.followers_count||0)}),r.jsx("div",{className:"text-sm text-gray-500",children:"粉丝"})]}),(0,r.jsxs)("div",{className:(0,w.cn)("text-center",J&&"cursor-pointer hover:bg-gray-50 rounded-lg p-2 transition-colors"),onClick:()=>J&&$(!0),title:J?"点击查看关注列表":void 0,children:[r.jsx("div",{className:"text-2xl font-bold text-gray-900",children:(0,w.uf)(I.following_count||0)}),r.jsx("div",{className:"text-sm text-gray-500",children:"关注"})]})]}),!J&&k&&r.jsx(j.Z,{onClick:W,variant:z?"outline":"primary",icon:z?r.jsx(g,{className:"h-4 w-4"}):r.jsx(p.Z,{className:"h-4 w-4"}),children:z?"已关注":"关注"})]})})}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["发布的宠物 (",I.posts_count||0,")"]}),r.jsx("div",{className:"flex space-x-2",children:Y.map(e=>{let s=e.icon;return(0,r.jsxs)("button",{onClick:()=>L(e.value),className:(0,w.cn)("flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors",G===e.value?"bg-primary-600 text-white":"bg-white text-gray-700 hover:bg-gray-100 border border-gray-200"),children:[r.jsx(s,{className:"h-4 w-4"}),r.jsx("span",{children:e.label})]},e.value)})})]}),M?r.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:Array.from({length:8}).map((e,s)=>r.jsx(b.gG,{},s))}):S.length>0?r.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:S.map(e=>r.jsx(v.Z,{post:e},e._id))}):r.jsx("div",{className:"text-center py-12",children:(0,r.jsxs)("div",{className:"text-gray-500",children:[r.jsx("div",{className:"text-6xl mb-4",children:"\uD83D\uDC3E"}),r.jsx("p",{className:"text-lg font-medium mb-2",children:J?"还没有发布宠物":"TA还没有发布宠物"}),r.jsx("p",{className:"text-sm",children:J?"快去发布第一只宠物吧！":"期待TA的第一次分享"}),J&&r.jsx(j.Z,{className:"mt-4",onClick:()=>t.push("/upload"),children:"发布宠物"})]})})]}),r.jsx(_,{isOpen:H,onClose:()=>B(!1),targetUserId:C,targetUserName:I?.nickname||"",onSuccess:()=>{N.C.success("举报提交成功"),B(!1)}}),r.jsx(Z,{isOpen:R,onClose:()=>V(!1),targetUserId:C,targetUserName:I?.nickname||"",onSuccess:()=>{N.C.success("已拉黑该用户"),O(!1),V(!1),t.push("/")}}),r.jsx(P.Z,{isOpen:X,onClose:()=>$(!1),userId:x?._id||""})]})}},28676:(e,s,t)=>{"use strict";t.d(s,{fe:()=>o,u_:()=>d});var r=t(10326),a=t(17577),l=t(60962),i=t(94019),c=t(28295),n=t(99837);let d=({isOpen:e,onClose:s,title:t,children:d,size:o="md",showCloseButton:x=!0,closeOnOverlayClick:m=!0,className:u})=>{if((0,a.useEffect)(()=>{let t=e=>{"Escape"===e.key&&s()};return e&&(document.addEventListener("keydown",t),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",t),document.body.style.overflow="unset"}},[e,s]),!e)return null;let h=(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:m?s:void 0}),(0,r.jsxs)("div",{className:(0,c.cn)("relative bg-white rounded-lg shadow-xl w-full mx-4 max-h-[90vh] overflow-hidden",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[o],u),onClick:e=>e.stopPropagation(),children:[(t||x)&&(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[t&&r.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:t}),x&&r.jsx(n.Z,{variant:"ghost",size:"sm",onClick:s,className:"p-1 hover:bg-gray-100 rounded-full",children:r.jsx(i.Z,{className:"h-5 w-5"})})]}),r.jsx("div",{className:"overflow-y-auto max-h-[calc(90vh-80px)]",children:d})]})]});return(0,l.createPortal)(h,document.body)},o=({children:e,className:s})=>r.jsx("div",{className:(0,c.cn)("p-4",s),children:e})},20914:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c,dynamicParams:()=>i,generateStaticParams:()=>l});var r=t(19510);t(71159);let a=(0,t(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\profile\[id]\ProfilePageClient.tsx#default`);async function l(){return[{id:"eea1754d6873ce81053b899e0f3aa469"},{id:"2ed3518f6875a7f905582dfd0fc94e98"},{id:"c611b94668776114057326f5172f0bc5"},{id:"d77d384f6877626a0574989b7124319b"}]}let i=!0;function c({params:e}){return r.jsx(a,{userId:e.id})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[276,201,240,535,203],()=>t(89313));module.exports=r})();