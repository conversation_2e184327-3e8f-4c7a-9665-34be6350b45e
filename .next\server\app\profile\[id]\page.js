(()=>{var e={};e.id=495,e.ids=[495],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},63892:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d}),r(20914),r(16953),r(35866);var a=r(23191),t=r(88716),l=r(37922),i=r.n(l),c=r(95231),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);r.d(s,n);let d=["",{children:["profile",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,20914)),"D:\\web-cloudbase-project\\src\\app\\profile\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\web-cloudbase-project\\src\\app\\profile\\[id]\\page.tsx"],x="/profile/[id]/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/profile/[id]/page",pathname:"/profile/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},96182:(e,s,r)=>{Promise.resolve().then(r.bind(r,69216))},86333:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});let a=(0,r(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},82200:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});let a=(0,r(76557).Z)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]])},924:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});let a=(0,r(76557).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},17069:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});let a=(0,r(76557).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},74975:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});let a=(0,r(76557).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},67187:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});let a=(0,r(76557).Z)("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]])},69216:(e,s,r)=>{"use strict";r.d(s,{default:()=>S});var a=r(10326),t=r(17577),l=r(35047),i=r(86333),c=r(924),n=r(67427),d=r(17069),o=r(88378),x=r(76557);let m=(0,x.Z)("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);var h=r(82200),u=r(67187);let p=(0,x.Z)("UserMinus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var g=r(74975),y=r(41828),j=r(35659),f=r(99837),b=r(22502),v=r(16545),N=r(20603),w=r(28295),k=r(94019),C=r(28676);let Z=[{id:1,label:"发布非法野生动物",description:"多次发布违法动物交易信息"},{id:2,label:"虚假交易信息",description:"经常发布虚假信息或进行欺诈"},{id:3,label:"恶意用户",description:"恶意骚扰、辱骂或攻击其他用户"},{id:4,label:"不当行为",description:"其他违反社区规定的行为"},{id:5,label:"其他原因",description:"其他需要举报的问题"}],_=({isOpen:e,onClose:s,targetUserId:r,targetUserName:l,onSuccess:i})=>{let[c,n]=(0,t.useState)(0),[d,o]=(0,t.useState)(!1),x=()=>{n(0),s()},m=async()=>{if(0===c){N.C.warning("请选择举报原因");return}try{o(!0);let e=await y.petAPI.reportUser({targetUserId:r,reason:c});e.success?(N.C.success("举报提交成功，我们会尽快处理"),i?.(),x()):N.C.error(e.message||"举报失败")}catch(e){console.error("举报用户失败:",e),N.C.error(e.message||"举报失败")}finally{o(!1)}};return a.jsx(C.u_,{isOpen:e,onClose:x,children:a.jsx(C.fe,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-red-100 rounded-lg",children:a.jsx(h.Z,{className:"h-5 w-5 text-red-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"举报用户"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["举报 ",l]})]})]}),a.jsx("button",{onClick:x,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(k.Z,{className:"h-5 w-5 text-gray-500"})})]}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"请选择举报原因："}),a.jsx("div",{className:"space-y-2",children:Z.map(e=>(0,a.jsxs)("label",{className:`flex items-start space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${c===e.id?"border-red-500 bg-red-50":"border-gray-200 hover:bg-gray-50"}`,children:[a.jsx("input",{type:"radio",name:"reason",value:e.id,checked:c===e.id,onChange:()=>n(e.id),className:"mt-1 text-red-600 focus:ring-red-500"}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("div",{className:"font-medium text-gray-900",children:e.label}),a.jsx("div",{className:"text-sm text-gray-500",children:e.description})]})]},e.id))})]}),1===c&&a.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:a.jsx("p",{className:"text-sm text-red-800 font-medium",children:"⚠️ 非法野生动物交易是违法行为，我们将严肃处理此类举报。"})}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx(f.Z,{variant:"outline",onClick:x,className:"flex-1",disabled:d,children:"取消"}),a.jsx(f.Z,{variant:"danger",onClick:m,loading:d,className:"flex-1",disabled:0===c,children:"提交举报"})]})]})})})},P=({isOpen:e,onClose:s,targetUserId:r,targetUserName:l,onSuccess:i})=>{let[c,n]=(0,t.useState)(""),[d,o]=(0,t.useState)(!1),x=()=>{n(""),s()},m=async()=>{try{o(!0);let e=await y.petAPI.blockUser({targetUserId:r,reason:c||"用户选择拉黑"});e.success?(N.C.success("已拉黑该用户"),i?.(),x()):N.C.error(e.message||"拉黑失败")}catch(e){console.error("拉黑用户失败:",e),N.C.error(e.message||"拉黑失败")}finally{o(!1)}};return a.jsx(C.u_,{isOpen:e,onClose:x,children:a.jsx(C.fe,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-gray-100 rounded-lg",children:a.jsx(u.Z,{className:"h-5 w-5 text-gray-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"拉黑用户"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["拉黑 ",l]})]})]}),a.jsx("button",{onClick:x,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(k.Z,{className:"h-5 w-5 text-gray-500"})})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[a.jsx("h4",{className:"font-medium text-yellow-800 mb-2",children:"拉黑后的效果："}),(0,a.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[a.jsx("li",{children:"• 您将不会再看到该用户发布的内容"}),a.jsx("li",{children:"• 该用户无法查看您的个人资料"}),a.jsx("li",{children:"• 您可以随时在设置中取消拉黑"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"拉黑原因（可选）"}),a.jsx("textarea",{value:c,onChange:e=>n(e.target.value),placeholder:"请输入拉黑原因...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none",rows:3,maxLength:200}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[c.length,"/200"]})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx(f.Z,{variant:"outline",onClick:x,className:"flex-1",disabled:d,children:"取消"}),a.jsx(f.Z,{variant:"danger",onClick:m,loading:d,className:"flex-1",children:"确认拉黑"})]})]})})})},S=({userId:e})=>{let s=(0,l.useParams)(),r=(0,l.useRouter)(),{user:x,isLoggedIn:k}=(0,j.E)(),C=e||s.id,[Z,S]=(0,t.useState)(null),[U,A]=(0,t.useState)([]),[M,q]=(0,t.useState)(!0),[I,E]=(0,t.useState)(!1),[z,D]=(0,t.useState)(!1),[G,L]=(0,t.useState)("created_at"),[O,H]=(0,t.useState)(!1),[T,F]=(0,t.useState)(!1),[R,V]=(0,t.useState)(!1),X=x?._id===C,$=async()=>{try{if(q(!0),!C){N.C.error("用户ID无效"),r.push("/");return}let e=await y.petAPI.getUserInfo({userId:C});e.success?S(e.data):(N.C.error("用户不存在"),r.push("/"))}catch(e){console.error("获取用户信息失败:",e),N.C.error("获取用户信息失败"),r.push("/")}finally{q(!1)}},B=async()=>{try{E(!0);let e=await y.petAPI.getUserPosts({targetUserId:C,limit:50,offset:0});e.success&&A(e.data||[])}catch(e){console.error("获取用户帖子失败:",e)}finally{E(!1)}},J=async()=>{if(!k){N.C.warning("请先登录");return}try{let e=await y.petAPI.toggleFollow({targetUserId:C});e.success?(D("followed"===e.action),N.C.success(e.message),Z&&S(s=>s?{...s,followers_count:(s.followers_count||0)+("followed"===e.action?1:-1)}:null)):N.C.error(e.message||"操作失败")}catch(e){N.C.error(e.message||"操作失败")}};if((0,t.useEffect)(()=>{C&&$()},[C]),(0,t.useEffect)(()=>{C&&B()},[C,G]),M)return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("div",{className:"bg-white border-b border-gray-200",children:a.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsx("div",{className:"flex items-center h-16",children:a.jsx("button",{onClick:()=>r.push("/"),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(i.Z,{className:"h-5 w-5"})})})})}),a.jsx(v.LL,{})]});if(!Z)return a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"用户不存在"}),a.jsx(f.Z,{onClick:()=>r.push("/"),children:"返回首页"})]})});let K=[{value:"created_at",label:"最新",icon:c.Z},{value:"likes_count",label:"最受欢迎",icon:n.Z},{value:"wants_count",label:"收藏最多",icon:d.Z}];return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("div",{className:"bg-white border-b border-gray-200 sticky top-0 z-10",children:a.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{onClick:()=>r.push("/"),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(i.Z,{className:"h-5 w-5"})}),a.jsx("h1",{className:"text-lg font-semibold text-gray-900",children:Z.nickname})]}),X?a.jsx(f.Z,{variant:"outline",icon:a.jsx(o.Z,{className:"h-4 w-4"}),onClick:()=>r.push("/profile"),children:"个人设置"}):k&&(0,a.jsxs)("div",{className:"relative",children:[a.jsx("button",{onClick:()=>H(!O),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(m,{className:"h-5 w-5 text-gray-600"})}),O&&(0,a.jsxs)("div",{className:"absolute right-0 top-full mt-2 w-40 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-20",children:[(0,a.jsxs)("button",{onClick:()=>{F(!0),H(!1)},className:"flex items-center space-x-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-gray-100",children:[a.jsx(h.Z,{className:"h-4 w-4"}),a.jsx("span",{children:"举报用户"})]}),(0,a.jsxs)("button",{onClick:()=>{V(!0),H(!1)},className:"flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[a.jsx(u.Z,{className:"h-4 w-4"}),a.jsx("span",{children:"拉黑用户"})]})]})]})]})})}),a.jsx("div",{className:"bg-white border-b border-gray-200",children:a.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-6",children:[a.jsx("div",{className:"relative",children:Z.avatar_url?a.jsx("img",{src:Z.avatar_url,alt:Z.nickname,className:"w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg"}):a.jsx("div",{className:"w-24 h-24 rounded-full bg-gray-300 border-4 border-white shadow-lg flex items-center justify-center",children:a.jsx("span",{className:"text-2xl font-bold text-gray-600",children:Z.nickname.charAt(0).toUpperCase()})})}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:Z.nickname}),Z.bio?a.jsx("p",{className:"text-gray-600 max-w-md",children:Z.bio}):a.jsx("p",{className:"text-gray-400 italic",children:X?"点击添加简介，让大家了解你":"这个人很懒，什么都没留下"})]}),(0,a.jsxs)("div",{className:"flex space-x-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-gray-900",children:(0,w.uf)(Z.total_likes||0)}),a.jsx("div",{className:"text-sm text-gray-500",children:"获赞"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-gray-900",children:(0,w.uf)(Z.followers_count||0)}),a.jsx("div",{className:"text-sm text-gray-500",children:"粉丝"})]})]}),!X&&k&&a.jsx(f.Z,{onClick:J,variant:z?"outline":"primary",icon:z?a.jsx(p,{className:"h-4 w-4"}):a.jsx(g.Z,{className:"h-4 w-4"}),children:z?"已关注":"关注"})]})})}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["发布的宠物 (",Z.posts_count||0,")"]}),a.jsx("div",{className:"flex space-x-2",children:K.map(e=>{let s=e.icon;return(0,a.jsxs)("button",{onClick:()=>L(e.value),className:(0,w.cn)("flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors",G===e.value?"bg-primary-600 text-white":"bg-white text-gray-700 hover:bg-gray-100 border border-gray-200"),children:[a.jsx(s,{className:"h-4 w-4"}),a.jsx("span",{children:e.label})]},e.value)})})]}),I?a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:Array.from({length:8}).map((e,s)=>a.jsx(v.gG,{},s))}):U.length>0?a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:U.map(e=>a.jsx(b.Z,{post:e},e._id))}):a.jsx("div",{className:"text-center py-12",children:(0,a.jsxs)("div",{className:"text-gray-500",children:[a.jsx("div",{className:"text-6xl mb-4",children:"\uD83D\uDC3E"}),a.jsx("p",{className:"text-lg font-medium mb-2",children:X?"还没有发布宠物":"TA还没有发布宠物"}),a.jsx("p",{className:"text-sm",children:X?"快去发布第一只宠物吧！":"期待TA的第一次分享"}),X&&a.jsx(f.Z,{className:"mt-4",onClick:()=>r.push("/upload"),children:"发布宠物"})]})})]}),a.jsx(_,{isOpen:T,onClose:()=>F(!1),targetUserId:C,targetUserName:Z?.nickname||"",onSuccess:()=>{N.C.success("举报提交成功"),F(!1)}}),a.jsx(P,{isOpen:R,onClose:()=>V(!1),targetUserId:C,targetUserName:Z?.nickname||"",onSuccess:()=>{N.C.success("已拉黑该用户"),V(!1),r.push("/")}})]})}},28676:(e,s,r)=>{"use strict";r.d(s,{fe:()=>o,u_:()=>d});var a=r(10326),t=r(17577),l=r(60962),i=r(94019),c=r(28295),n=r(99837);let d=({isOpen:e,onClose:s,title:r,children:d,size:o="md",showCloseButton:x=!0,closeOnOverlayClick:m=!0,className:h})=>{if((0,t.useEffect)(()=>{let r=e=>{"Escape"===e.key&&s()};return e&&(document.addEventListener("keydown",r),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",r),document.body.style.overflow="unset"}},[e,s]),!e)return null;let u=(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:m?s:void 0}),(0,a.jsxs)("div",{className:(0,c.cn)("relative bg-white rounded-lg shadow-xl w-full mx-4 max-h-[90vh] overflow-hidden",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[o],h),onClick:e=>e.stopPropagation(),children:[(r||x)&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[r&&a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:r}),x&&a.jsx(n.Z,{variant:"ghost",size:"sm",onClick:s,className:"p-1 hover:bg-gray-100 rounded-full",children:a.jsx(i.Z,{className:"h-5 w-5"})})]}),a.jsx("div",{className:"overflow-y-auto max-h-[calc(90vh-80px)]",children:d})]})]});return(0,l.createPortal)(u,document.body)},o=({children:e,className:s})=>a.jsx("div",{className:(0,c.cn)("p-4",s),children:e})},20914:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c,dynamicParams:()=>i,generateStaticParams:()=>l});var a=r(19510);r(71159);let t=(0,r(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\profile\[id]\ProfilePageClient.tsx#default`);async function l(){return[{id:"eea1754d6873ce81053b899e0f3aa469"},{id:"2ed3518f6875a7f905582dfd0fc94e98"},{id:"c611b94668776114057326f5172f0bc5"},{id:"d77d384f6877626a0574989b7124319b"}]}let i=!0;function c({params:e}){return a.jsx(t,{userId:e.id})}}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[276,201,240,535],()=>r(63892));module.exports=a})();