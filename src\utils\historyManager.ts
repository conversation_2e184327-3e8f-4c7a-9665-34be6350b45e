/**
 * 通用历史记录管理工具
 * 支持地理位置、分类等各种筛选器的历史记录
 */

export interface HistoryItem {
  value: string;
  label: string;
  timestamp: number;
}

export class HistoryManager {
  private storageKey: string;
  private maxItems: number;

  constructor(storageKey: string, maxItems: number = 10) {
    this.storageKey = storageKey;
    this.maxItems = maxItems;
  }

  /**
   * 获取历史记录列表
   */
  getHistory(): HistoryItem[] {
    try {
      const history = localStorage.getItem(this.storageKey);
      if (history) {
        const items = JSON.parse(history) as HistoryItem[];
        // 按时间戳降序排列（最新的在前面）
        return items.sort((a, b) => b.timestamp - a.timestamp);
      }
    } catch (error) {
      console.error('获取历史记录失败:', error);
    }
    return [];
  }

  /**
   * 添加新的历史记录
   */
  addItem(value: string, label?: string) {
    if (!value.trim()) return;

    const item: HistoryItem = {
      value: value.trim(),
      label: label || value.trim(),
      timestamp: Date.now()
    };

    let history = this.getHistory();

    // 移除重复项（基于value）
    history = history.filter(h => h.value !== item.value);

    // 添加到开头
    history.unshift(item);

    // 限制数量
    if (history.length > this.maxItems) {
      history = history.slice(0, this.maxItems);
    }

    // 保存
    this.saveHistory(history);
  }

  /**
   * 删除单个历史记录
   */
  removeItem(value: string) {
    const history = this.getHistory().filter(item => item.value !== value);
    this.saveHistory(history);
  }

  /**
   * 清空所有历史记录
   */
  clearHistory() {
    localStorage.removeItem(this.storageKey);
  }

  /**
   * 获取历史记录的值列表（用于快速匹配）
   */
  getHistoryValues(): string[] {
    return this.getHistory().map(item => item.value);
  }

  /**
   * 检查是否存在某个历史记录
   */
  hasItem(value: string): boolean {
    return this.getHistoryValues().includes(value);
  }

  /**
   * 保存历史记录到localStorage
   */
  private saveHistory(history: HistoryItem[]) {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(history));
    } catch (error) {
      console.error('保存历史记录失败:', error);
    }
  }
}

/**
 * 浏览历史记录管理器
 * 专门用于管理用户浏览的帖子历史记录
 */
export interface BrowseHistoryItem {
  postId: string;
  title: string;
  author: string;
  authorId: string;
  image?: string;
  timestamp: number;
}

export class BrowseHistoryManager {
  private storageKey = 'browse_history';
  private maxDays = 3; // 保留3天
  private maxItems = 50; // 最多保留50条记录

  /**
   * 获取浏览历史记录
   */
  getHistory(): BrowseHistoryItem[] {
    try {
      const history = localStorage.getItem(this.storageKey);
      if (history) {
        const items = JSON.parse(history) as BrowseHistoryItem[];
        // 过滤过期记录（超过3天）
        const threeDaysAgo = Date.now() - (this.maxDays * 24 * 60 * 60 * 1000);
        const validItems = items.filter(item => item.timestamp > threeDaysAgo);

        // 如果有过期记录被过滤，更新存储
        if (validItems.length !== items.length) {
          this.saveHistory(validItems);
        }

        // 按时间戳降序排列（最新的在前面）
        return validItems.sort((a, b) => b.timestamp - a.timestamp);
      }
    } catch (error) {
      console.error('获取浏览历史记录失败:', error);
    }
    return [];
  }

  /**
   * 添加浏览记录
   */
  addBrowseRecord(postId: string, title: string, author: string, authorId: string, image?: string) {
    if (!postId || !title) return;

    const item: BrowseHistoryItem = {
      postId,
      title: title.trim(),
      author: author.trim(),
      authorId,
      image,
      timestamp: Date.now()
    };

    let history = this.getHistory();

    // 移除重复项（基于postId）
    history = history.filter(h => h.postId !== item.postId);

    // 添加到开头
    history.unshift(item);

    // 限制数量
    if (history.length > this.maxItems) {
      history = history.slice(0, this.maxItems);
    }

    // 保存
    this.saveHistory(history);
  }

  /**
   * 删除单个浏览记录
   */
  removeRecord(postId: string) {
    const history = this.getHistory().filter(item => item.postId !== postId);
    this.saveHistory(history);
  }

  /**
   * 清空所有浏览历史
   */
  clearHistory() {
    localStorage.removeItem(this.storageKey);
  }

  /**
   * 获取最近浏览的帖子ID列表
   */
  getRecentPostIds(): string[] {
    return this.getHistory().map(item => item.postId);
  }

  /**
   * 检查是否浏览过某个帖子
   */
  hasBrowsed(postId: string): boolean {
    return this.getRecentPostIds().includes(postId);
  }

  /**
   * 保存历史记录到localStorage
   */
  private saveHistory(history: BrowseHistoryItem[]) {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(history));
    } catch (error) {
      console.error('保存浏览历史记录失败:', error);
    }
  }
}

// 预定义的历史记录管理器实例
export const locationHistoryManager = new HistoryManager('location_history', 10);
export const categoryHistoryManager = new HistoryManager('category_history', 8);
export const browseHistoryManager = new BrowseHistoryManager();
