/**
 * 地址工具函数测试
 */

import { formatAddressForDisplay, getFullAddress, isValidAddress, addressExamples } from '../addressUtils';

describe('地址工具函数测试', () => {
  describe('formatAddressForDisplay', () => {
    // 使用预定义的测试用例
    addressExamples.testCases.forEach(({ input, expected }) => {
      test(`应该将 "${input}" 格式化为 "${expected}"`, () => {
        expect(formatAddressForDisplay(input)).toBe(expected);
      });
    });

    // 边界情况测试
    test('应该处理空字符串', () => {
      expect(formatAddressForDisplay('')).toBe('');
    });

    test('应该处理null和undefined', () => {
      expect(formatAddressForDisplay(null as any)).toBe('');
      expect(formatAddressForDisplay(undefined as any)).toBe('');
    });

    test('应该处理只有省份的地址', () => {
      expect(formatAddressForDisplay('湖南省')).toBe('湖南省');
    });

    test('应该处理只有市的地址', () => {
      expect(formatAddressForDisplay('长沙市')).toBe('长沙市');
    });

    test('应该处理只有县的地址', () => {
      expect(formatAddressForDisplay('临武县')).toBe('临武县');
    });

    test('应该处理带#号的地址', () => {
      expect(formatAddressForDisplay('#湖南省郴州市临武县')).toBe('郴州市临武县');
    });

    test('应该处理超长地址', () => {
      const longAddress = '湖南省郴州市临武县南强镇渣塘村第一组第二小组第三户';
      const result = formatAddressForDisplay(longAddress);
      expect(result).toBe('郴州市临武县');
    });

    test('应该处理直辖市地址', () => {
      expect(formatAddressForDisplay('北京市朝阳区')).toBe('朝阳区');
      expect(formatAddressForDisplay('上海市浦东新区')).toBe('上海市浦东新区');
    });

    test('应该处理特殊行政区', () => {
      expect(formatAddressForDisplay('香港特别行政区中西区')).toBe('香港特别行政区中西区');
    });
  });

  describe('getFullAddress', () => {
    test('应该移除#号并返回完整地址', () => {
      expect(getFullAddress('#湖南省郴州市临武县南强镇渣塘村')).toBe('湖南省郴州市临武县南强镇渣塘村');
    });

    test('应该处理没有#号的地址', () => {
      expect(getFullAddress('湖南省郴州市临武县南强镇渣塘村')).toBe('湖南省郴州市临武县南强镇渣塘村');
    });

    test('应该处理空字符串', () => {
      expect(getFullAddress('')).toBe('');
    });
  });

  describe('isValidAddress', () => {
    test('应该验证有效地址', () => {
      expect(isValidAddress('湖南省郴州市临武县')).toBe(true);
      expect(isValidAddress('北京市朝阳区')).toBe(true);
    });

    test('应该拒绝无效地址', () => {
      expect(isValidAddress('')).toBe(false);
      expect(isValidAddress(null as any)).toBe(false);
      expect(isValidAddress(undefined as any)).toBe(false);
    });

    test('应该拒绝过长的地址', () => {
      const tooLongAddress = 'a'.repeat(201);
      expect(isValidAddress(tooLongAddress)).toBe(false);
    });

    test('应该接受带#号的地址', () => {
      expect(isValidAddress('#湖南省郴州市临武县')).toBe(true);
    });
  });
});

// 手动测试函数（用于开发时调试）
export function manualTest() {
  console.log('=== 地址格式化测试 ===');
  
  const testAddresses = [
    '湖南省郴州市临武县南强镇渣塘村',
    '北京市朝阳区',
    '上海市浦东新区',
    '广东省深圳市南山区',
    '江苏省南京市',
    '浙江省杭州市西湖区文三路',
    '#湖南省长沙市',
    '郴州市临武县',
    '临武县',
    '湖南省',
    ''
  ];

  testAddresses.forEach(address => {
    const formatted = formatAddressForDisplay(address);
    console.log(`"${address}" -> "${formatted}"`);
  });
}
