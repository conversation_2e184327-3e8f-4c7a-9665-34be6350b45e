import React, { useState, useRef, useEffect } from 'react';
import { Plus, X, Camera } from 'lucide-react';
import { getSystemSettings, validateFile, formatFileSize, getFileTypesText } from '@/utils/systemSettings';
import { cn } from '@/utils';

// 扩展图片数据类型，支持 File 对象和 base64 字符串
export interface ImageData {
  file?: File;
  base64?: string;
  url?: string;
  name?: string;
}

interface ImageUploadProps {
  images: File[];
  onImagesChange: (images: File[]) => void;
  maxImages?: number;
  className?: string;
  // 新增：支持显示草稿图片的 base64 数据
  draftImages?: string[];
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  images,
  onImagesChange,
  maxImages = 9,
  className,
  draftImages = [],
}) => {
  // 获取系统设置
  const [systemSettings, setSystemSettings] = useState(() => getSystemSettings());

  // 调试日志
  console.log('ImageUpload 组件渲染:', {
    imagesCount: images.length,
    draftImagesCount: draftImages.length,
    draftImages: draftImages.map(img => img ? img.substring(0, 50) + '...' : 'null'),
    systemSettings
  });
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 监听 draftImages 变化
  useEffect(() => {
    if (draftImages.length > 0) {
      console.log('draftImages 更新:', draftImages.length, '张图片');
      // 强制重新渲染以确保图片预览更新
      setDragOver(prev => prev);
    }
  }, [draftImages]);

  // 监听系统设置变化
  useEffect(() => {
    const handleStorageChange = () => {
      setSystemSettings(getSystemSettings());
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // 处理文件选择
  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const validFiles: File[] = [];
    const errors: string[] = [];

    Array.from(files).forEach(file => {
      const validation = validateFile(file, systemSettings);
      if (validation.valid) {
        validFiles.push(file);
      } else {
        errors.push(`${file.name}: ${validation.error}`);
      }
    });

    // 显示错误信息
    if (errors.length > 0) {
      console.warn('文件验证失败:', errors);
      // 这里可以添加toast提示
    }

    // 检查数量限制
    const maxAllowedImages = Math.min(maxImages, systemSettings.maxImagesPerPost);
    const remainingSlots = maxAllowedImages - images.length;
    const filesToAdd = validFiles.slice(0, remainingSlots);

    if (filesToAdd.length > 0) {
      onImagesChange([...images, ...filesToAdd]);
    }

    // 如果有文件被数量限制截断，显示提示
    if (validFiles.length > remainingSlots) {
      console.warn(`只能上传 ${remainingSlots} 张图片，已忽略多余的文件`);
    }
  };

  // 处理拖拽
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  // 获取图片预览URL
  const getImagePreviewUrl = (file: File, index: number): string => {
    try {
      // 优先检查是否有草稿图片数据
      if (draftImages[index]) {
        console.log(`使用草稿图片数据 ${index}:`, draftImages[index].substring(0, 50) + '...');
        return draftImages[index];
      }

      // 如果没有草稿数据，检查是否是有效的 File 对象
      if (file instanceof File && file.size > 0) {
        console.log(`使用File对象创建预览 ${index}:`, file.name, file.size);
        return URL.createObjectURL(file);
      }

      // 如果都没有，返回默认图片
      console.log(`使用默认图片 ${index}`);
      return 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center';
    } catch (error) {
      console.error('获取图片预览URL失败:', error);
      // 尝试使用草稿数据
      if (draftImages[index]) {
        return draftImages[index];
      }
      return 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center';
    }
  };

  // 删除图片
  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index);
    onImagesChange(newImages);
  };

  // 点击上传
  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* 图片预览网格 */}
      <div className="grid grid-cols-3 gap-3">
        {/* 已上传的图片 */}
        {images.map((file, index) => (
          <div key={index} className="relative aspect-square">
            <img
              src={getImagePreviewUrl(file, index)}
              alt={`预览 ${index + 1}`}
              className="w-full h-full object-cover rounded-lg border border-gray-200"
              onError={(e) => {
                // 图片加载失败时使用默认图片
                e.currentTarget.src = 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center';
              }}
            />
            <button
              onClick={() => removeImage(index)}
              className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
            >
              <X className="h-3 w-3" />
            </button>
            {/* 图片序号 */}
            <div className="absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
              {index + 1}
            </div>
          </div>
        ))}

        {/* 上传按钮 */}
        {images.length < maxImages && (
          <div
            onClick={handleUploadClick}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            className={cn(
              'aspect-square border-2 border-dashed rounded-lg flex flex-col items-center justify-center cursor-pointer transition-colors',
              dragOver
                ? 'border-primary-500 bg-primary-50'
                : 'border-gray-300 hover:border-primary-400 hover:bg-gray-50'
            )}
          >
            <Camera className="h-8 w-8 text-gray-400 mb-2" />
            <span className="text-sm text-gray-500 text-center">
              {images.length === 0 ? '添加图片' : '继续添加'}
            </span>
            <span className="text-xs text-gray-400 mt-1">
              {images.length}/{maxImages}
            </span>
          </div>
        )}
      </div>

      {/* 上传提示 */}
      <div className="text-sm text-gray-500 space-y-1">
        <p>• 最多可上传 {Math.min(maxImages, systemSettings.maxImagesPerPost)} 张图片</p>
        <p>• 支持 {getFileTypesText(systemSettings.allowedImageTypes)} 格式，单张图片不超过 {formatFileSize(systemSettings.maxImageSize)}</p>
        <p>• 第一张图片将作为封面显示</p>
        <p>• 可拖拽图片到上传区域</p>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept={systemSettings.allowedImageTypes.join(',')}
        multiple
        onChange={(e) => handleFileSelect(e.target.files)}
        className="hidden"
      />
    </div>
  );
};

export default ImageUpload;
