(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[347],{43077:function(e,t,r){"use strict";r.r(t),r.d(t,{AbstractSDKRequest:function(){return i},AbstractStorage:function(){return a},StorageType:function(){return o},formatUrl:function(){return s}}),(n=o||(o={})).local="local",n.none="none",n.session="session";var n,o,i=function(){},a=function(){};function s(e,t,r){void 0===r&&(r={});var n=/\?/.test(t),o="";for(var i in r)""===o?n||(t+="?"):o+="&",o+=i+"="+encodeURIComponent(r[i]);return(t+=o,/^http(s)?\:\/\//.test(t))?t:""+e+t}},47857:function(e,t,r){"use strict";var n,o=r(43077),i=r(41822),a=(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),s=function(){return(s=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},u=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},c="Error when aborting requestTask",l=function(e){function t(t){void 0===t&&(t={});var r=e.call(this)||this,n=t.timeout,o=t.timeoutMsg,i=t.restrictedMethods;return r._timeout=n||0,r._timeoutMsg=o||"请求超时",r._restrictedMethods=i||["get","post","upload","download"],r}return a(t,e),t.prototype.post=function(e){var t=this;return new Promise(function(r,n){var i=e.url,a=e.data,s=e.headers,u=wx.request({url:(0,o.formatUrl)("https:",i),data:a,timeout:t._timeout,method:"POST",header:s,success:function(e){r(e)},fail:function(e){n(e)},complete:function(e){if(e&&e.errMsg&&t._timeout&&-1!==t._restrictedMethods.indexOf("post")&&"request:fail timeout"===e.errMsg){console.warn(t._timeoutMsg);try{u.abort()}catch(e){}}}})})},t.prototype.upload=function(e){var t=this,r=this;return new Promise(function(n){var o,i,a;return o=void 0,i=void 0,a=function(){var t,o,i,a,c,l;return u(this,function(u){return t=e.url,o=e.file,i=e.data,a=e.headers,c=e.onUploadProgress,l=wx.uploadFile({url:t,filePath:o,name:"file",formData:s({},i),header:a,timeout:this._timeout,success:function(e){var t={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&i.success_action_status&&(t.statusCode=parseInt(i.success_action_status,10)),n(t)},fail:function(e){n(e)},complete:function(e){if(e&&e.errMsg&&r._timeout&&-1!==r._restrictedMethods.indexOf("upload")&&"request:fail timeout"===e.errMsg){console.warn(r._timeoutMsg);try{l.abort()}catch(e){}}}}),c&&l.onProgressUpdate(function(e){c(e)}),[2]})},new(i||(i=Promise))(function(e,r){function n(e){try{u(a.next(e))}catch(e){r(e)}}function s(e){try{u(a.throw(e))}catch(e){r(e)}}function u(t){var r;t.done?e(t.value):((r=t.value)instanceof i?r:new i(function(e){e(r)})).then(n,s)}u((a=a.apply(t,o||[])).next())})})},t.prototype.download=function(e){var t=this,r=this;return new Promise(function(n,i){var a=e.url,s=e.headers,u=wx.downloadFile({url:(0,o.formatUrl)("https:",a),header:s,timeout:t._timeout,success:function(e){200===e.statusCode&&e.tempFilePath?n({statusCode:200,tempFilePath:e.tempFilePath}):n(e)},fail:function(e){i(e)},complete:function(e){if(e&&e.errMsg&&r._timeout&&-1!==r._restrictedMethods.indexOf("download")&&"request:fail timeout"===e.errMsg){console.warn(r._timeoutMsg);try{u.abort()}catch(e){}}}})})},t.prototype.fetch=function(e){var t=e.url,r=e.body,n=e.enableAbort,a=e.headers,s=e.method,u=e.stream,l=void 0!==u&&u,f=e.signal,h=e.timeout,d=this,p=null!=h?h:this._timeout,y=null,m=new i.Pz({start:function(e){y=e},cancel:function(){y=null}});return new Promise(function(e,i){l&&e({data:m});var u=wx.request({url:(0,o.formatUrl)("https:",t),data:r,timeout:p,method:s.toUpperCase(),header:a,success:function(t){var r;null===(r=y)||void 0===r||r.close(),l||e(t)},fail:function(e){var t;if(null===(t=y)||void 0===t||t.close(),i(e),l)throw e},complete:function(e){if(e&&e.errMsg&&p&&-1!==d._restrictedMethods.indexOf("post")&&n&&"request:fail timeout"===e.errMsg){console.warn(d._timeoutMsg);try{u.abort()}catch(e){console.warn(c,e)}}},enableChunked:l});if(u.onChunkReceived(function(e){var t;null===(t=y)||void 0===t||t.enqueue(new Uint8Array(e.data))}),f){var h=function(){try{u.abort()}catch(e){console.warn(c,e)}};f.aborted?h():f.addEventListener("abort",function(){return h()})}})},t}(o.AbstractSDKRequest),f={setItem:function(e,t){wx.setStorageSync(e,t)},getItem:function(e){return wx.getStorageSync(e)},removeItem:function(e){wx.removeStorageSync(e)},clear:function(){wx.clearStorageSync()}},h=function(e,t){void 0===t&&(t={});var r=wx.connectSocket(s({url:e},t));return{set onopen(cb){r.onOpen(cb)},set onmessage(cb){r.onMessage(cb)},set onclose(cb){r.onClose(cb)},set onerror(cb){r.onError(cb)},send:function(e){return r.send({data:e})},close:function(e,t){return r.close({code:e,reason:t})},get readyState(){return r.readyState},CONNECTING:0,OPEN:1,CLOSING:2,CLOSED:3}};t.ZP={genAdapter:function(){return{root:{},reqClass:l,wsClass:h,localStorage:f,primaryStorage:o.StorageType.local,getAppSign:function(){var e=wx.getAccountInfoSync();return"undefined"!=typeof App||"undefined"!=typeof getApp||wx.onAppHide||wx.offAppHide||wx.onAppShow||wx.offAppShow?e&&e.miniProgram?e.miniProgram.appId:"":e&&e.plugin?e.plugin.appId:""}}},isMatch:function(){if("undefined"==typeof wx||"undefined"==typeof Page||!wx.getSystemInfoSync||!wx.getStorageSync||!wx.setStorageSync||!wx.connectSocket||!wx.request)return!1;try{if(!wx.getSystemInfoSync()||"qq"===wx.getSystemInfoSync().AppPlatform)return!1}catch(e){return!1}return!0},runtime:"wx_mp"}},9208:function(e,t,r){"use strict";r.d(t,{X:function(){return c}});var n=r(43957),o=function(){return(o=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},i=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},a=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},s=function(){function e(e){this.localStorage=(null==e?void 0:e.localStorage)||globalThis.localStorage}return e.prototype.getItem=function(e){return i(this,void 0,void 0,function(){return a(this,function(t){return[2,this.localStorage.getItem(e)]})})},e.prototype.removeItem=function(e){return i(this,void 0,void 0,function(){return a(this,function(t){return this.localStorage.removeItem(e),[2]})})},e.prototype.setItem=function(e,t){return i(this,void 0,void 0,function(){return a(this,function(r){return this.localStorage.setItem(e,t),[2]})})},e.prototype.getItemSync=function(e){return this.localStorage.getItem(e)},e.prototype.setItemSync=function(e,t){this.localStorage.setItem(e,t)},e.prototype.removeItemSync=function(e){this.localStorage.removeItem(e)},e}(),u=/^[^:]+:\/\/[^/]+(\/[^?#]+)/,c=function(e){if(!n.Us.isMatch()||e.storage&&e.baseRequest)return e;var t={};try{var r=n.Us.genAdapter(),c=r.localStorage,l=r.reqClass;if(c&&(t.storage=new s({localStorage:c})),l){var f=new l({timeout:1e4,restrictedMethods:["get","post","upload","download","request"]});t.request=function(e,t){var r;return i(this,void 0,void 0,function(){var n;return a(this,function(o){return n=null===(r=null==t?void 0:t.headers)||void 0===r?void 0:r["x-request-id"],[2,new Promise(function(r,o){var i=Object.assign({},t);i.body&&"string"!=typeof i.body&&(i.body=JSON.stringify(i.body));var a=wx.request({url:e,data:i.body,timeout:f._timeout,method:i.method||"GET",header:i.headers,success:function(t){var i,a;(t.code||(null===(i=t.data)||void 0===i?void 0:i.error_code))&&(t={error:t.code||t.data.error,error_description:t.data.error_description||t.message||t.code||t.data.error_code,request_id:t.requestId,error_code:null===(a=t.data)||void 0===a?void 0:a.error_code}),t.request_id||(t.request_id=t.request_id||n),t.error&&(t.error_uri=u.test(e)?RegExp.$1:e,o(t)),r(t.data||{})},fail:function(e){o({error:"unreachable",error_description:e.message})},complete:function(e){if(e&&e.errMsg&&f._timeout&&-1!==f._restrictedMethods.indexOf("request")&&"request:fail timeout"===e.errMsg){console.warn(f._timeoutMsg);try{a.abort()}catch(e){}}}})})]})})}}return e.captchaOptions&&(t.baseRequest=t.request,t.request=void 0),o(o({},e),t)}catch(e){console.error("adapter generate fail:",e)}return e}},43957:function(e,t,r){"use strict";r.d(t,{Dz:function(){return i.catchErrorsDecorator},Sg:function(){return o.ERRORS},Us:function(){return u.ZP},_y:function(){return n.throwError},qU:function(){return s.CloudbaseCache},vM:function(){return o.COMMUNITY_SITE_URL},xY:function(){return c.useDefaultAdapter},xe:function(){return n.printWarn},yE:function(){return a.CloudbaseEventEmitter}});var n=r(20226),o=r(83544),i=r(99794),a=r(13826),s=r(32348),u=r(47857),c=r(10191)},83393:function(e,t,r){"use strict";r.d(t,{Z:function(){return ip}});var n,o,i,a,s,u,c,l,f,h,d,p,y,m,v,b,g,_,w,S,E,T,I,O,R,A,P,C,N,x,q,j,k={};r.r(k),r.d(k,{ArkSimpleModel:function(){return rz},DSSimpleModel:function(){return rX},DefaultSimpleModel:function(){return np},HunYuanBetaSimpleModel:function(){return rU},HunYuanExpSimpleModel:function(){return nr},HunYuanOpenSimpleModel:function(){return na},HunYuanSimpleModel:function(){return rB},MODELS:function(){return nP},MoonshotSimpleModel:function(){return r8},ReactModel:function(){return nS},YiSimpleModel:function(){return r3},ZhiPuSimpleModel:function(){return rN},toolMap:function(){return nI}});var D={};r.r(D),r.d(D,{LineString:function(){return oo},MultiLineString:function(){return ol},MultiPoint:function(){return ou},MultiPolygon:function(){return oh},Point:function(){return ob},Polygon:function(){return oa}});var L="@cloudbase/js-sdk",U="https:",M="https://support.qq.com/products/148793",W={INVALID_PARAMS:"INVALID_PARAMS",INVALID_OPERATION:"INVALID_OPERATION",OPERATION_FAIL:"OPERATION_FAIL"},F=r(43077);function B(e){return"[object Array]"===Object.prototype.toString.call(e)}function G(e){return"string"==typeof e}function V(e){return"[object FormData]"===Object.prototype.toString.call(e)}function H(e,t,r){void 0===r&&(r={});var n=/\?/.test(t),o="";return(Object.keys(r).forEach(function(e){""===o?n||(t+="?"):o+="&",o+="".concat(e,"=").concat(encodeURIComponent(r[e]))}),t+=o,/^http(s)?:\/\//.test(t))?t:"".concat(e).concat(t)}function $(e,t,r){if(void 0===r&&(r=null),e&&"function"==typeof e)return e(t,r);if(t)throw t;return r}function z(e,t){console.warn("[".concat(L,"][").concat(e,"]:").concat(t))}var K=(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),J=function(){return(J=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},Q=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},Y=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},X=function(e){function t(t){var r=e.call(this)||this,n=t.timeout,o=t.timeoutMsg,i=t.restrictedMethods;return r.timeout=n||0,r.timeoutMsg=o||"请求超时",r.restrictedMethods=i||["get","post","upload","download"],r}return K(t,e),t.prototype.get=function(e){return this.request(J(J({},e),{method:"get"}),this.restrictedMethods.includes("get"))},t.prototype.post=function(e){return this.request(J(J({},e),{method:"post"}),this.restrictedMethods.includes("post"))},t.prototype.put=function(e){return this.request(J(J({},e),{method:"put"}))},t.prototype.upload=function(e){var t=e.data,r=e.file,n=e.name,o=e.method,i=e.headers,a={post:"post",put:"put"}[null==o?void 0:o.toLowerCase()]||"put",s=new FormData;return"post"===a?(Object.keys(t).forEach(function(e){s.append(e,t[e])}),s.append("key",n),s.append("file",r),this.request(J(J({},e),{data:s,method:a}),this.restrictedMethods.includes("upload"))):this.request(J(J({},e),{method:"put",headers:void 0===i?{}:i,body:r}),this.restrictedMethods.includes("upload"))},t.prototype.download=function(e){return Q(this,void 0,void 0,function(){var t,r,n,o;return Y(this,function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,this.get(J(J({},e),{headers:{},responseType:"blob"}))];case 1:return t=i.sent().data,r=window.URL.createObjectURL(new Blob([t])),n=decodeURIComponent(new URL(e.url).pathname.split("/").pop()||""),(o=document.createElement("a")).href=r,o.setAttribute("download",n),o.style.display="none",document.body.appendChild(o),o.click(),window.URL.revokeObjectURL(r),document.body.removeChild(o),[3,3];case 2:return i.sent(),[3,3];case 3:return[2,new Promise(function(t){t({statusCode:200,tempFilePath:e.url})})]}})})},t.prototype.fetch=function(e){var t;return Q(this,void 0,void 0,function(){var r,n,o,i,a,s,u,c,l,f,h,d=this;return Y(this,function(p){switch(p.label){case 0:return r=new AbortController,n=e.url,i=void 0!==(o=e.enableAbort)&&o,s=void 0!==(a=e.stream)&&a,u=e.signal,l=null!=(c=e.timeout)?c:this.timeout,u&&(u.aborted&&r.abort(),u.addEventListener("abort",function(){return r.abort()})),f=null,i&&l&&(f=setTimeout(function(){console.warn(d.timeoutMsg),r.abort(Error(d.timeoutMsg))},l)),[4,fetch(n,J(J({},e),{signal:r.signal})).then(function(e){return Q(d,void 0,void 0,function(){var t,r,n;return Y(this,function(o){switch(o.label){case 0:if(clearTimeout(f),!e.ok)return[3,1];return t=e,[3,3];case 1:return n=(r=Promise).reject,[4,e.json()];case 2:t=n.apply(r,[o.sent()]),o.label=3;case 3:return[2,t]}})})}).catch(function(e){return clearTimeout(f),Promise.reject(e)})];case 1:return h=p.sent(),[2,{data:s?h.body:(null===(t=h.headers.get("content-type"))||void 0===t?void 0:t.includes("application/json"))?h.json():h.text(),statusCode:h.status,header:h.headers}]}})})},t.prototype.request=function(e,t){var r=this;void 0===t&&(t=!1);var n=String(e.method).toLowerCase()||"get";return new Promise(function(o){var i,a,s,u,c=e.url,l=e.headers,f=void 0===l?{}:l,h=e.data,d=e.responseType,p=e.withCredentials,y=e.body,m=e.onUploadProgress,v=H(U,c,"get"===n?h:{}),b=new XMLHttpRequest;(b.open(n,v),d&&(b.responseType=d),Object.keys(f).forEach(function(e){b.setRequestHeader(e,f[e])}),m&&b.upload.addEventListener("progress",m),b.onreadystatechange=function(){var e={};if(4===b.readyState){var t=b.getAllResponseHeaders().trim().split(/[\r\n]+/),r={};t.forEach(function(e){var t=e.split(": "),n=t.shift().toLowerCase(),o=t.join(": ");r[n]=o}),e.header=r,e.statusCode=b.status;try{e.data="blob"===d?b.response:JSON.parse(b.responseText)}catch(t){e.data="blob"===d?b.response:b.responseText}clearTimeout(s),o(e)}},t&&r.timeout&&(s=setTimeout(function(){console.warn(r.timeoutMsg),b.abort()},r.timeout)),V(h))?u=h:"application/x-www-form-urlencoded"===f["content-type"]?(void 0===(i=h)&&(i={}),a=[],Object.keys(i).forEach(function(e){a.push("".concat(e,"=").concat(encodeURIComponent(i[e])))}),u=a.join("&")):u=y||(h?JSON.stringify(h):void 0),p&&(b.withCredentials=!0),b.send(u)})},t}(F.AbstractSDKRequest);(o=E||(E={})).WEB="web",o.WX_MP="wx_mp";var Z=r(25566),ee=(i=function(e,t){return(i=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),et=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},er=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},en=function(e){function t(t){var r=e.call(this)||this;return r.root=t,t.tcbCacheObject||(t.tcbCacheObject={}),r}return ee(t,e),t.prototype.setItem=function(e,t){this.root.tcbCacheObject[e]=t},t.prototype.getItem=function(e){return this.root.tcbCacheObject[e]},t.prototype.removeItem=function(e){delete this.root.tcbCacheObject[e]},t.prototype.clear=function(){delete this.root.tcbCacheObject},t}(F.AbstractStorage),eo=function(){function e(e){this.keys={};var t=e.persistence,r=e.platformInfo,n=e.keys;this.platformInfo=void 0===r?{}:r,this.storage||(this.persistenceTag=this.platformInfo.adapter.primaryStorage||t,this.storage=function(e,t){switch(e){case"local":default:if(!t.localStorage)return z(W.INVALID_PARAMS,"localStorage is not supported on current platform"),new en(t.root);return t.localStorage;case"none":return new en(t.root)}}(this.persistenceTag,this.platformInfo.adapter),this.keys=void 0===n?{}:n)}return Object.defineProperty(e.prototype,"mode",{get:function(){return this.storage.mode||"sync"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"persistence",{get:function(){return this.persistenceTag},enumerable:!1,configurable:!0}),e.prototype.setStore=function(e,t,r){if("async"===this.mode){z(W.INVALID_OPERATION,"current platform's storage is asynchronous, please use setStoreAsync insteed");return}if(this.storage)try{this.storage.setItem(e,JSON.stringify({version:r||"localCachev1",content:t}))}catch(e){throw Error(JSON.stringify({code:W.OPERATION_FAIL,msg:"[".concat(L,"][").concat(W.OPERATION_FAIL,"]setStore failed"),info:e}))}},e.prototype.setStoreAsync=function(e,t,r){return et(this,void 0,void 0,function(){var n;return er(this,function(o){switch(o.label){case 0:if(!this.storage)return[2];o.label=1;case 1:return o.trys.push([1,3,,4]),n={version:r||"localCachev1",content:t},[4,this.storage.setItem(e,JSON.stringify(n))];case 2:return o.sent(),[3,4];case 3:return o.sent(),[2];case 4:return[2]}})})},e.prototype.getStore=function(e,t){if("async"===this.mode){z(W.INVALID_OPERATION,"current platform's storage is asynchronous, please use getStoreAsync insteed");return}try{if(void 0!==Z&&(null===(r=Z.env)||void 0===r?void 0:r.tcb_token))return Z.env.tcb_token;if(!this.storage)return""}catch(e){return""}t=t||"localCachev1";var r,n=this.storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""},e.prototype.getStoreAsync=function(e,t){var r;return et(this,void 0,void 0,function(){var n;return er(this,function(o){switch(o.label){case 0:try{if(void 0!==Z&&(null===(r=Z.env)||void 0===r?void 0:r.tcb_token))return[2,Z.env.tcb_token];if(!this.storage)return[2,""]}catch(e){return[2,""]}return t=t||"localCachev1",[4,this.storage.getItem(e)];case 1:if(!(n=o.sent()))return[2,""];if(n.indexOf(t)>=0)return[2,JSON.parse(n).content];return[2,""]}})})},e.prototype.removeStore=function(e){if("async"===this.mode){z(W.INVALID_OPERATION,"current platform's storage is asynchronous, please use removeStoreAsync insteed");return}this.storage.removeItem(e)},e.prototype.removeStoreAsync=function(e){return et(this,void 0,void 0,function(){return er(this,function(t){switch(t.label){case 0:return[4,this.storage.removeItem(e)];case 1:return t.sent(),[2]}})})},e}(),ei=(a=function(e,t){return(a=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}a(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),ea=function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))},es=function(e,t){this.data=t||null,this.name=e},eu=function(e){function t(t,r){var n=e.call(this,"error",{error:t,data:r})||this;return n.error=t,n}return ei(t,e),t}(es),ec=function(){function e(){this.listeners={}}return e.prototype.on=function(e,t){var r;return(r=this.listeners)[e]=r[e]||[],r[e].push(t),this},e.prototype.off=function(e,t){return function(e,t,r){if(null==r?void 0:r[e]){var n=r[e].indexOf(t);-1!==n&&r[e].splice(n,1)}}(e,t,this.listeners),this},e.prototype.fire=function(e,t){if(e instanceof eu)return console.error(e.error),this;var r=G(e)?new es(e,t||{}):e,n=r.name;if(this.listens(n)){r.target=this;for(var o=this.listeners[n]?ea([],this.listeners[n],!0):[],i=0;i<o.length;i++)o[i].call(this,r)}return this},e.prototype.listens=function(e){return this.listeners[e]&&this.listeners[e].length>0},e}();new ec;var el=!1;"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.indexOf("Firefox");function ef(e){return e.mode,e.customInfo,e.title,e.messages,function(e,t,r){}}function eh(e){this.message=e}function ed(e){this.message=e}(function(){var e=this;this.listeners=[],this.signal={aborted:!1,addEventListener:function(t,r){"abort"===t&&e.listeners.push(r)}}}).prototype.abort=function(){this.signal.aborted||(this.signal.aborted=!0,this.listeners.forEach(function(e){return e()}))},eh.prototype=Error(),eh.prototype.name="InvalidCharacterError","undefined"!=typeof window&&window.atob&&window.atob.bind(window),ed.prototype=Error(),ed.prototype.name="InvalidTokenError";var ep=r(47857),ey=function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))},em={},ev={},eb=function(){return(eb=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},eg={},e_={};function ew(e){var t=e.env,r=e.platformInfo,n={userInfoKey:"".concat("user_info","_").concat(t)};eg[t]=eg[t]||new eo(eb(eb({},e),{keys:n,platformInfo:r})),e_[t]=e_[t]||new eo(eb(eb({},e),{keys:n,platformInfo:r,persistence:"local"}))}var eS=function(){return(eS=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},eE=function(e){L=e},eT=function(e){U=e},eI="",eO="@cloudbase/js-sdk",eR=[],eA=["env","endPointKey","region"],eP="https:";function eC(e){return eR.find(function(t){return eA.filter(function(t){return null!=e[t]}).every(function(r){return t[r]===e[r]})})}function eN(e){var t,r,n=eC(e);n?(null!=e.baseUrl&&(n.baseUrl=e.baseUrl),null!=e.protocol&&(n.protocol=e.protocol)):eR.push(eS(eS({},e),{protocol:null!==(t=e.protocol)&&void 0!==t?t:eP})),"CLOUD_API"===e.endPointKey&&eT(null!==(r=e.protocol)&&void 0!==r?r:eP)}function ex(e,t,r){return eC({env:e,endPointKey:t,region:r})}function eq(e){var t=ex(e,"CLOUD_API"),r=t.protocol,n=t.baseUrl;return"".concat(r).concat(n).match(/(http(s)?:)?\/\/([^/?#]*)/)[0]}(s=T||(T={})).NULL="NULL",s.ANONYMOUS="ANONYMOUS",s.WECHAT="WECHAT",s.WECHAT_PUBLIC="WECHAT-PUBLIC",s.WECHAT_OPEN="WECHAT-OPEN",s.CUSTOM="CUSTOM",s.EMAIL="EMAIL",s.USERNAME="USERNAME",s.PHONE="PHONE";var ej=function(){return(ej=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},ek=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},eD=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},eL=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r},eU=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"];function eM(e,t,r){var n=e[t];e[t]=function(t){var o={},i={};r.forEach(function(r){var n=r.call(e,t),a=n.data,s=n.headers;Object.assign(o,a),Object.assign(i,s)});var a=t.data;return a&&function(){if(V(a)){Object.keys(o).forEach(function(e){a.append(e,o[e])});return}t.data=ej(ej({},a),o)}(),t.headers=ej(ej({},t.headers||{}),i),n.call(e,t)}}function eW(){var e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:{"X-SDK-Version":"@cloudbase/js-sdk/".concat(eI),"x-seqid":e}}}var eF=function(){function e(e){this.throwWhenRequestFail=!1,this.config=e;var t={timeout:this.config.timeout,timeoutMsg:"[@cloudbase/js-sdk] 请求在".concat(this.config.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post","put"]};this.reqClass=new ev.adapter.reqClass(t),this.throwWhenRequestFail=e.throw||!1,this.localCache=e_[this.config.env],eM(this.reqClass,"post",[eW]),eM(this.reqClass,"upload",[eW]),eM(this.reqClass,"download",[eW])}return e.prototype.post=function(e){return ek(this,void 0,void 0,function(){return eD(this,function(t){switch(t.label){case 0:return[4,this.reqClass.post(e)];case 1:return[2,t.sent()]}})})},e.prototype.upload=function(e){return ek(this,void 0,void 0,function(){return eD(this,function(t){switch(t.label){case 0:return[4,this.reqClass.upload(e)];case 1:return[2,t.sent()]}})})},e.prototype.download=function(e){return ek(this,void 0,void 0,function(){return eD(this,function(t){switch(t.label){case 0:return[4,this.reqClass.download(e)];case 1:return[2,t.sent()]}})})},e.prototype.getBaseEndPoint=function(){return eq(this.config.env)},e.prototype.getOauthAccessTokenV2=function(e){return ek(this,void 0,void 0,function(){var t,r;return eD(this,function(n){switch(n.label){case 0:return[4,e.getAccessToken()];case 1:return t=n.sent(),[4,e.getCredentials()];case 2:return r=n.sent(),[2,{accessToken:t,accessTokenExpire:new Date(r.expires_at).getTime()}]}})})},e.prototype.request=function(e,t,r){var n,o;return ek(this,void 0,void 0,function(){var i,a,s,u,c,l,f,h,d,p,y,m,v,b,g,_,w,S,E;return eD(this,function(T){switch(T.label){case 0:if(i="x-tcb-trace_".concat(this.config.env),a="application/x-www-form-urlencoded",s=ej({action:e,dataVersion:"2020-01-10",env:this.config.env},t),-1!==eU.indexOf(e))return[3,2];if(!(u=this.config._fromApp).oauthInstance)throw Error("you can't request without auth");return c=u.oauthInstance.oauth2client,l=s,[4,this.getOauthAccessTokenV2(c)];case 1:l.access_token=T.sent().accessToken,T.label=2;case 2:return"storage.uploadFile"===e?(Object.keys(f=new FormData).forEach(function(e){Object.prototype.hasOwnProperty.call(f,e)&&void 0!==f[e]&&f.append(e,s[e])}),a="multipart/form-data"):(a="application/json;charset=UTF-8",f={},Object.keys(s).forEach(function(e){void 0!==s[e]&&(f[e]=s[e])})),h={headers:{"content-type":a}},(null==r?void 0:r.onUploadProgress)&&(h.onUploadProgress=r.onUploadProgress),this.config.region&&(h.headers["X-TCB-Region"]=this.config.region),(d=this.localCache.getStore(i))&&(h.headers["X-TCB-Trace"]=d),p=(null==r?void 0:r.parse)!==void 0?r.parse:t.parse,y=(null==r?void 0:r.inQuery)!==void 0?r.inQuery:t.inQuery,m=(null==r?void 0:r.search)!==void 0?r.search:t.search,v=ej(ej({},(null==r?void 0:r.defaultQuery)||{}),{env:this.config.env}),p&&(v.parse=!0),y&&(v=ej(ej({},y),v)),g=(b=ex(this.config.env,"CLOUD_API")).baseUrl,_=b.protocol,w=r.pathname?H(_,"".concat(null===(n=eq(this.config.env))||void 0===n?void 0:n.replace(/^https?:/,""),"/").concat(r.pathname),v):H(_,g,v),m&&(w+=m),[4,this.post(ej({url:w,data:f},h))];case 3:if((E=null===(o=(S=T.sent()).header)||void 0===o?void 0:o["x-tcb-trace"])&&this.localCache.setStore(i,E),200!==Number(S.status)&&200!==Number(S.statusCode)||!S.data)throw Error("network request error");return[2,S]}})})},e.prototype.fetch=function(e){var t,r,n,o,i,a,s,u;return ek(this,void 0,void 0,function(){var c,l,f,h,d,p,y,m=this;return eD(this,function(v){switch(v.label){case 0:c=e.token,f=void 0===(l=e.headers)?{}:l,h=eL(e,["token","headers"]),d=function(){return ek(m,void 0,void 0,function(){var e,t;return eD(this,function(r){switch(r.label){case 0:if(null!=c)return[2,c];if(!(e=this.config._fromApp).oauthInstance)throw Error("you can't request without auth");return t=e.oauthInstance.oauth2client,[4,this.getOauthAccessTokenV2(t)];case 1:return[2,r.sent().accessToken]}})})},p=function(){return ek(m,void 0,void 0,function(){var e,t,r,n,o;return eD(this,function(i){switch(i.label){case 0:return t=(e=this.reqClass).fetch,n={},o={"X-SDK-Version":"@cloudbase/js-sdk/".concat(eI)},r="Bearer ".concat,[4,d()];case 1:return[2,t.apply(e,[ej.apply(void 0,[(n.headers=ej.apply(void 0,[(o.Authorization=r.apply("Bearer ",[i.sent()]),o),f]),n),h])])]}})})},v.label=1;case 1:return v.trys.push([1,3,,6]),[4,p()];case 2:return[2,v.sent()];case 3:if((null==(y=v.sent())?void 0:y.code)!=="ACCESS_TOKEN_EXPIRED")return[3,5];if("function"!=typeof(null===(o=null===(n=null===(r=null===(t=this.config)||void 0===t?void 0:t._fromApp)||void 0===r?void 0:r.oauthInstance)||void 0===n?void 0:n.authApi)||void 0===o?void 0:o.refreshTokenForce))throw y;return[4,null===(u=null===(s=null===(a=null===(i=this.config)||void 0===i?void 0:i._fromApp)||void 0===a?void 0:a.oauthInstance)||void 0===s?void 0:s.authApi)||void 0===u?void 0:u.refreshTokenForce()];case 4:return v.sent(),[2,p()];case 5:throw y;case 6:return[2]}})})},e.prototype.send=function(e,t,r){return void 0===t&&(t={}),void 0===r&&(r={}),ek(this,void 0,void 0,function(){var n;return eD(this,function(o){switch(o.label){case 0:return[4,this.request(e,t,ej(ej({},r),{onUploadProgress:t.onUploadProgress}))];case 1:if((n=o.sent()).data.code&&this.throwWhenRequestFail)throw Error(JSON.stringify({code:W.OPERATION_FAIL,msg:"[".concat(n.data.code,"] ").concat(n.data.message)}));return[2,n.data]}})})},e}(),eB={},eG=function(){return(eG=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},eV=function(e,t,r,n){var o,i=arguments.length,a=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,r,n);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(i<3?o(a):i>3?o(t,r,a):o(t,r))||a);return i>3&&a&&Object.defineProperty(t,r,a),a},eH=function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},e$=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},ez=function(e){for(var t=B(e)?e:[e],r=0;r<t.length;r++){var n=t[r],o=n.isMatch,i=n.genAdapter,a=n.runtime;if(o())return{adapter:i(),runtime:a}}},eK={timeout:15e3,persistence:"local"},eJ={},eQ=new(function(){function e(e){this.cloudbaseConfig=e||this.cloudbaseConfig,this.authInstance=null,this.oauthInstance=null,this.version=eI}return Object.defineProperty(e.prototype,"config",{get:function(){return this.cloudbaseConfig},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"platform",{get:function(){return ev},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"cache",{get:function(){return eg[this.cloudbaseConfig.env]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"localCache",{get:function(){return e_[this.cloudbaseConfig.env]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"request",{get:function(){return eB[this.cloudbaseConfig.env]},enumerable:!1,configurable:!0}),e.prototype.init=function(t){if(!t.env)throw Error(JSON.stringify({code:W.INVALID_PARAMS,msg:"env must not be specified"}));ev.adapter||this.useDefaultAdapter();var r,n,o,i,a={timeout:t.timeout||5e3,timeoutMsg:"[".concat(eO,"][REQUEST TIMEOUT] request had been abort since didn't finished within").concat(t.timeout/1e3,"s")};this.requestClient=new ev.adapter.reqClass(a),this.cloudbaseConfig=eG(eG({},eK),t),this.cloudbaseConfig.timeout=this.formatTimeout(this.cloudbaseConfig.timeout);var s=this.cloudbaseConfig,u=s.env,c=s.persistence,l=s.debug,f=s.timeout,h=s.oauthClient;ew({env:u,persistence:c,debug:l,platformInfo:this.platform}),n=(r=t.region||"")?"//".concat(u,".").concat(r,".tcb-api.tencentcloudapi.com/web"):"//".concat(u,".ap-shanghai.tcb-api.tencentcloudapi.com/web"),eN({env:u,region:r,baseUrl:n,protocol:void 0,endPointKey:"CLOUD_API"}),eN({endPointKey:"GATEWAY",env:u,baseUrl:"//".concat(u,".api.tcloudbasegateway.com/v1"),protocol:void 0});var d=new e(this.cloudbaseConfig);return eB[(o={env:u,region:t.region||"",timeout:f,oauthClient:h,_fromApp:d}).env]=new eF(ej(ej({},o),{throw:!0})),d.requestClient=this.requestClient,null===(i=this===null||void 0===this?void 0:this.fire)||void 0===i||i.call(this,"cloudbase_init",d),d},e.prototype.updateConfig=function(e){var t=e.persistence,r=e.debug;this.cloudbaseConfig.persistence=t,this.cloudbaseConfig.debug=r,ew({env:this.cloudbaseConfig.env,persistence:t,debug:r,platformInfo:this.platform})},e.prototype.registerExtension=function(e){eJ[e.name]=e},e.prototype.invokeExtension=function(e,t){var r,n,o,i;return r=this,n=void 0,o=void 0,i=function(){var r;return e$(this,function(n){switch(n.label){case 0:if(!(r=eJ[e]))throw Error(JSON.stringify({code:W.INVALID_PARAMS,msg:"extension:".concat(e," must be registered before invoke")}));return[4,r.invoke(t,this)];case 1:return[2,n.sent()]}})},new(o||(o=Promise))(function(e,t){function a(e){try{u(i.next(e))}catch(e){t(e)}}function s(e){try{u(i.throw(e))}catch(e){t(e)}}function u(t){var r;t.done?e(t.value):((r=t.value)instanceof o?r:new o(function(e){e(r)})).then(a,s)}u((i=i.apply(r,n||[])).next())})},e.prototype.useAdapters=function(e){var t=ez(e)||{},r=t.adapter,n=t.runtime;r&&(ev.adapter=r),n&&(ev.runtime=n)},e.prototype.registerHook=function(t){!function(e,t){var r=t.entity,n=t.target;if(Object.prototype.hasOwnProperty.call(e,n))throw Error(JSON.stringify({code:W.INVALID_OPERATION,msg:"target:".concat(n," is not exist")}));var o=e.prototype[n];if("function"!=typeof o)throw Error(JSON.stringify({code:W.INVALID_OPERATION,msg:"target:".concat(n," is not a function which is the only type supports hook")}));e.prototype[n]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return r.call.apply(r,ey([this],e,!1)),o.call.apply(o,ey([this],e,!1))}}(e,t)},e.prototype.registerComponent=function(t){!function(e,t){var r=t.name,n=t.namespace,o=t.entity,i=t.injectEvents,a=t.IIFE;if(em[r]||n&&e[n])throw Error(JSON.stringify({code:W.INVALID_OPERATION,msg:"Duplicate component ".concat(r)}));if(void 0!==a&&a){if(!o||"function"!=typeof o)throw Error(JSON.stringify({code:W.INVALID_PARAMS,msg:"IIFE component's entity must be a function"}));o.call(e)}if(em[r]=t,n?e.prototype[n]=o:function e(t,r){if(!(r instanceof Object))return r;switch(r.constructor){case Date:return new Date(r.getTime());case Object:void 0===t&&(t={});break;case Array:t=[];break;default:return r}for(var n=Object.keys(r),o=0;o<n.length;o++){var i=n[o];Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=e(t[i],r[i]))}return t}(e.prototype,o),i){var s=i.bus,u=i.events;if(!s||!u||0===u.length)return;var c=e.prototype.fire||function(){};e.prototype.events||(e.prototype.events={}),(e.prototype.events||{})[r]?e.prototype.events[r].events=ey(ey([],e.prototype.events[r].events,!0),u,!0):e.prototype.events[r]={bus:s,events:u},e.prototype.fire=function(e,t){c(e,t);for(var r=Object.keys(this.events),n=0;n<r.length;n++){var o=r[n],i=this.events[o],a=i.bus;if(i.events.includes(e)){a.fire(e,t);break}}}}}(e,t)},e.prototype.registerVersion=function(e){eI=e,this.version=e},e.prototype.registerSdkName=function(e){eO=e,eE(e)},e.prototype.registerEndPoint=function(e,t){eN({baseUrl:e,protocol:t,env:this.config.env,endPointKey:"CLOUD_API"})},e.prototype.registerEndPointWithKey=function(e){eN({env:this.config.env,endPointKey:e.key,baseUrl:e.url,protocol:e.protocol})},e.prototype.getEndPointWithKey=function(e){var t=ex(this.config.env,e);return{BASE_URL:t.baseUrl,PROTOCOL:t.protocol}},e.prototype.useDefaultAdapter=function(){var e={adapter:{root:window,reqClass:X,wsClass:WebSocket,localStorage:localStorage},runtime:E.WEB},t=e.adapter,r=e.runtime;ev.adapter=t,ev.runtime=r},e.prototype.formatTimeout=function(e){switch(!0){case e>6e5:return z(W.INVALID_PARAMS,"timeout is greater than maximum value[10min]"),6e5;case e<100:return z(W.INVALID_PARAMS,"timeout is less than maximum value[100ms]"),100;default:return e}},eV([ef({mode:"sync",title:"Cloudbase 初始化失败",messages:["请确认以下各项：","  1 - 调用 cloudbase.init() 的语法或参数是否正确","  2 - 如果是非浏览器环境，是否配置了安全应用来源（https://docs.cloudbase.net/api-reference/webv3/adapter#%E7%AC%AC-2-%E6%AD%A5%E9%85%8D%E7%BD%AE%E5%AE%89%E5%85%A8%E5%BA%94%E7%94%A8%E6%9D%A5%E6%BA%90）","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(M)]}),eH("design:type",Function),eH("design:paramtypes",[Object]),eH("design:returntype",e)],e.prototype,"init",null),eV([ef({title:"调用扩展能力失败",messages:["请确认以下各项：","  1 - 调用 invokeExtension() 的语法或参数是否正确","  2 - 被调用的扩展能力是否已经安装并通过 registerExtension() 注册","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(M)]}),eH("design:type",Function),eH("design:paramtypes",[String,Object]),eH("design:returntype",Promise)],e.prototype,"invokeExtension",null),e}());eQ.useAdapters(ep.ZP);var eY=r(86847),eX=function(){return(eX=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},eZ=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},e0=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},e1="cloudrun";function e2(e){return eZ(this,void 0,void 0,function(){var t,r,n,o,i,a,s,u,c;return e0(this,function(l){switch(l.label){case 0:return t=e.name,r=e.data,o=void 0===(n=e.path)?"":n,i=e.method,s=void 0===(a=e.header)?{}:a,u="https://".concat(this.config.env,".api.tcloudbasegateway.com/v1/cloudrun/").concat(t),c=o.startsWith("/")?o:"/".concat(o),[4,this.request.fetch({method:i||"POST",headers:Object.assign({},{"Content-Type":"application/json; charset=utf-8"},s),body:r,url:"".concat(u).concat(c)})];case 1:return[4,l.sent().data];case 2:return[2,l.sent()]}})})}var e3=new(function(){function e(){}return e.prototype.callContainer=function(e){return eZ(this,void 0,void 0,function(){var t,r,n;return e0(this,function(o){switch(o.label){case 0:if(t=e.name,r=e.data,!t)throw Error(JSON.stringify({code:W.INVALID_PARAMS,msg:"[".concat(e1,".callContainer] invalid name")}));try{n=r?JSON.stringify(r):""}catch(e){throw Error(JSON.stringify({code:W.INVALID_PARAMS,msg:"[".concat(e1,".callContainer] invalid data")}))}return[4,e2.call(this,eX(eX({},e),{data:n}))];case 1:return[2,o.sent()]}})})},e}()),e4={name:e1,entity:{callContainer:e3.callContainer}};try{cloudbase.registerComponent(e4)}catch(e){}var e6=function(){return(e6=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},e5=function(e,t,r,n){var o,i=arguments.length,a=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,r,n);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(i<3?o(a):i>3?o(t,r,a):o(t,r))||a);return i>3&&a&&Object.defineProperty(t,r,a),a},e7=function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},e8=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},e9="functions",te=new(function(){function e(){}return e.prototype.callFunction=function(e,t){var r,n,o,i,a,s;return o=this,i=void 0,a=void 0,s=function(){var o,i,a,s,u,c,l,f,h,d,p,y,m,v,b,g,_,w;return e8(this,function(S){switch(S.label){case 0:var E,T;if(o=e.name,i=e.data,a=e.query,s=e.parse,u=e.search,c=e.type,f=void 0===(l=e.path)?"":l,h=e.method,p=void 0===(d=e.header)?{}:d,!o)throw Error(JSON.stringify({code:W.INVALID_PARAMS,msg:"[".concat(e9,".callFunction] invalid function name")}));try{y=i?JSON.stringify(i):""}catch(e){throw Error(JSON.stringify({code:W.INVALID_PARAMS,msg:"[".concat(e9,".callFunction] invalid data")}))}if("cloudrun"!==c)return[3,2];return E=new Date().getTime(),T=(null==performance?void 0:performance.now)&&1e3*performance.now()||0,m="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random();return E>0?(t=(E+t)%16|0,E=Math.floor(E/16)):(t=(T+t)%16|0,T=Math.floor(T/16)),("x"===e?t:7&t|8).toString(16)}),[4,e2.call(this,{name:o,data:y,path:f,method:h,header:e6(e6({},p),{"X-Request-Id":m})})];case 1:return[2,{result:v=S.sent(),requestId:m}];case 2:b="functions.invokeFunction",g={inQuery:a,parse:s,search:u,function_name:o,request_data:y},_=this.request,S.label=3;case 3:return S.trys.push([3,5,,6]),[4,_.send(b,g,{defaultQuery:(null===(r=null==i?void 0:i.params)||void 0===r?void 0:r.action)?{action:null===(n=null==i?void 0:i.params)||void 0===n?void 0:n.action}:{}})];case 4:if((w=S.sent()).code)return[2,$(t,null,w)];if(v=w.data.response_data,s)return[2,$(t,null,{result:v,requestId:w.requestId})];try{return v=JSON.parse(w.data.response_data),[2,$(t,null,{result:v,requestId:w.requestId})]}catch(e){$(t,Error("[".concat(L,"][").concat(W.INVALID_PARAMS,"][").concat(e9,".callFunction] response data must be json")))}return[3,6];case 5:return $(t,S.sent()),[3,6];case 6:return[2]}})},new(a||(a=Promise))(function(e,t){function r(e){try{u(s.next(e))}catch(e){t(e)}}function n(e){try{u(s.throw(e))}catch(e){t(e)}}function u(t){var o;t.done?e(t.value):((o=t.value)instanceof a?o:new a(function(e){e(o)})).then(r,n)}u((s=s.apply(o,i||[])).next())})},e5([ef({customInfo:{className:"Cloudbase",methodName:"callFunction"},title:"函数调用失败",messages:["请确认以下各项：","  1 - 调用 callFunction() 的语法或参数是否正确","  2 - 当前环境下是否存在此函数","  3 - 函数安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(M)]}),e7("design:type",Function),e7("design:paramtypes",[Object,Function]),e7("design:returntype",Promise)],e.prototype,"callFunction",null),e}()),tt={name:e9,entity:{callFunction:te.callFunction}};try{cloudbase.registerComponent(tt)}catch(e){}var tr=function(){return(tr=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},tn=function(e,t,r,n){var o,i=arguments.length,a=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,r,n);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(i<3?o(a):i>3?o(t,r,a):o(t,r))||a);return i>3&&a&&Object.defineProperty(t,r,a),a},to=function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},ti=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},ta=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}};(u=I||(I={})).put="put",u.post="post";var ts="storage",tu=new(function(){function e(){}return e.prototype.uploadFile=function(e,t){return ti(this,void 0,void 0,function(){var r,n,o,i,a,s,u,c,l,f,h,d,p,y,m,v,b,g,_,w,S,E,T,O,R,A;return ta(this,function(P){switch(P.label){case 0:if(r=e.cloudPath,n=e.filePath,o=e.onUploadProgress,a=void 0===(i=e.method)?"put":i,u=void 0===(s=e.headers)?{}:s,!G(r)||!n)throw Error(JSON.stringify({code:W.INVALID_PARAMS,msg:"[".concat(ts,".uploadFile] invalid params")}));return c=({put:I.put,post:I.post})[a.toLocaleLowerCase()]||I.put,l="storage.getUploadMetadata",f=this.request,h={path:r,method:c},c===I.put&&(h.headers=u),[4,f.send(l,h)];case 1:return y=(p=(d=P.sent()).data).url,m=p.authorization,v=p.token,b=p.fileId,g=p.cosFileId,_=p.download_url,w=d.requestId,E=tr(tr({},S={url:y,file:n,name:r,onUploadProgress:o}),{method:I.put,headers:tr(tr({},u),{authorization:m,"x-cos-meta-fileid":g,"x-cos-security-token":v})}),T=tr(tr({},S),{method:I.post,data:{key:r,signature:m,"x-cos-meta-fileid":g,success_action_status:"201","x-cos-security-token":v}}),(A={})[I.put]={params:E,isSuccess:function(e){return e>=200&&e<300}},A[I.post]={params:T,isSuccess:function(e){return 201===e}},O=A,[4,f.upload(O[c].params)];case 2:if(R=P.sent(),O[c].isSuccess(R.statusCode))return[2,$(t,null,{fileID:b,download_url:_,requestId:w})];return[2,$(t,Error("[".concat(L,"][").concat(W.OPERATION_FAIL,"][").concat(ts,"]:").concat(R.data)))]}})})},e.prototype.getUploadMetadata=function(e,t){return ti(this,void 0,void 0,function(){var r,n,o;return ta(this,function(i){switch(i.label){case 0:if(!G(r=e.cloudPath))throw Error(JSON.stringify({code:W.INVALID_PARAMS,msg:"[".concat(ts,".getUploadMetadata] invalid cloudPath")}));n=this.request,o="storage.getUploadMetadata",i.label=1;case 1:return i.trys.push([1,3,,4]),[4,n.send(o,{path:r})];case 2:return[2,$(t,null,i.sent())];case 3:return[2,$(t,i.sent())];case 4:return[2]}})})},e.prototype.deleteFile=function(e,t){return ti(this,void 0,void 0,function(){var r,n,o,i,a,s;return ta(this,function(u){switch(u.label){case 0:if(!(r=e.fileList)||!B(r)||0===r.length)throw Error(JSON.stringify({code:W.INVALID_PARAMS,msg:"[".concat(ts,".deleteFile] fileList must not be empty")}));for(n=0,o=r;n<o.length;n++)if(!(i=o[n])||!G(i))throw Error(JSON.stringify({code:W.INVALID_PARAMS,msg:"[".concat(ts,".deleteFile] fileID must be string")}));return a="storage.batchDeleteFile",[4,this.request.send(a,{fileid_list:r})];case 1:if((s=u.sent()).code)return[2,$(t,null,s)];return[2,$(t,null,{fileList:s.data.delete_list,requestId:s.requestId})]}})})},e.prototype.getTempFileURL=function(e,t){return ti(this,void 0,void 0,function(){var r,n,o,i,a,s,u;return ta(this,function(c){switch(c.label){case 0:if(!(r=e.fileList)||!B(r)||0===r.length)throw Error(JSON.stringify({code:W.INVALID_PARAMS,msg:"[".concat(ts,".getTempFileURL] fileList must not be empty")}));for(o=0,n=[],i=r;o<i.length;o++){var l;if(l=a=i[o],"[object Object]"===Object.prototype.toString.call(l)){if(!Object.prototype.hasOwnProperty.call(a,"fileID")||!Object.prototype.hasOwnProperty.call(a,"maxAge"))throw Error(JSON.stringify({code:W.INVALID_PARAMS,msg:"[".concat(ts,".getTempFileURL] file info must include fileID and maxAge")}));n.push({fileid:a.fileID,max_age:a.maxAge})}else if(G(a))n.push({fileid:a});else throw Error(JSON.stringify({code:W.INVALID_PARAMS,msg:"[".concat(ts,".getTempFileURL] invalid fileList")}))}return s="storage.batchGetDownloadUrl",[4,this.request.send(s,{file_list:n})];case 1:if((u=c.sent()).code)return[2,$(t,null,u)];return[2,$(t,null,{fileList:u.data.download_list,requestId:u.requestId})]}})})},e.prototype.downloadFile=function(e,t){return ti(this,void 0,void 0,function(){var r,n,o,i;return ta(this,function(a){switch(a.label){case 0:if(!G(r=e.fileID))throw Error(JSON.stringify({code:W.INVALID_PARAMS,msg:"[".concat(ts,".getTempFileURL] fileID must be string")}));return[4,this.getTempFileURL.call(this,{fileList:[{fileID:r,maxAge:600}]})];case 1:if("SUCCESS"!==(n=a.sent().fileList[0]).code)return[2,$(t,n)];return o=this.request,i=encodeURI(n.download_url),[4,o.download({url:i})];case 2:return[2,$(t,null,a.sent())]}})})},tn([ef({customInfo:{className:"Cloudbase",methodName:"uploadFile"},title:"上传文件失败",messages:["请确认以下各项：","  1 - 调用 uploadFile() 的语法或参数是否正确","  2 - 当前域名是否在安全域名列表中：https://console.cloud.tencent.com/tcb/env/safety","  3 - 云存储安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(M)]}),to("design:type",Function),to("design:paramtypes",[Object,Function]),to("design:returntype",Promise)],e.prototype,"uploadFile",null),tn([ef({customInfo:{className:"Cloudbase",methodName:"getUploadMetadata"},title:"获取上传元信息失败",messages:["请确认以下各项：","  1 - 调用 getUploadMetadata() 的语法或参数是否正确","  2 - 当前域名是否在安全域名列表中：https://console.cloud.tencent.com/tcb/env/safety","  3 - 云存储安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(M)]}),to("design:type",Function),to("design:paramtypes",[Object,Function]),to("design:returntype",Promise)],e.prototype,"getUploadMetadata",null),tn([ef({customInfo:{className:"Cloudbase",methodName:"deleteFile"},title:"删除文件失败",messages:["请确认以下各项：","  1 - 调用 deleteFile() 的语法或参数是否正确","  2 - 当前域名是否在安全域名列表中：https://console.cloud.tencent.com/tcb/env/safety","  3 - 云存储安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(M)]}),to("design:type",Function),to("design:paramtypes",[Object,Function]),to("design:returntype",Promise)],e.prototype,"deleteFile",null),tn([ef({customInfo:{className:"Cloudbase",methodName:"getTempFileURL"},title:"获取文件下载链接",messages:["请确认以下各项：","  1 - 调用 getTempFileURL() 的语法或参数是否正确","  2 - 当前域名是否在安全域名列表中：https://console.cloud.tencent.com/tcb/env/safety","  3 - 云存储安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(M)]}),to("design:type",Function),to("design:paramtypes",[Object,Function]),to("design:returntype",Promise)],e.prototype,"getTempFileURL",null),tn([ef({customInfo:{className:"Cloudbase",methodName:"downloadFile"},title:"下载文件失败",messages:["请确认以下各项：","  1 - 调用 downloadFile() 的语法或参数是否正确","  2 - 当前域名是否在安全域名列表中：https://console.cloud.tencent.com/tcb/env/safety","  3 - 云存储安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(M)]}),to("design:type",Function),to("design:paramtypes",[Object,Function]),to("design:returntype",Promise)],e.prototype,"downloadFile",null),e}()),tc={name:ts,entity:{uploadFile:tu.uploadFile,deleteFile:tu.deleteFile,getTempFileURL:tu.getTempFileURL,downloadFile:tu.downloadFile,getUploadMetadata:tu.getUploadMetadata}};try{cloudbase.registerComponent(tc)}catch(e){}var tl=r(69854),tf=r.n(tl),th=r(10283),td=r.n(th),tp=r(59128),ty=r.n(tp);function tm(e){return void 0===e&&(e=""),"".concat(e?"".concat(e,"_"):"").concat(+new Date,"_").concat(Math.random())}var tv=function(e){this.close=e.close,this.onChange=e.onChange,this.onError=e.onError,e.debug&&Object.defineProperty(this,"virtualClient",{get:function(){return e.virtualClient}})},tb=function(e){var t,r,n=e.id,o=e.docChanges,i=e.docs,a=e.msgType,s=e.type;Object.defineProperties(this,{id:{get:function(){return n},enumerable:!0},docChanges:{get:function(){return t||(t=JSON.parse(JSON.stringify(o))),t},enumerable:!0},docs:{get:function(){return r||(r=JSON.parse(JSON.stringify(i))),r},enumerable:!0},msgType:{get:function(){return a},enumerable:!0},type:{get:function(){return s},enumerable:!0}})},tg=(c=function(e,t){return(c=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}c(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),t_=function(e){function t(t){var r=e.call(this,"Watch Error ".concat(JSON.stringify(t.msgData)," (requestid: ").concat(t.requestId,")"))||this;return r.isRealtimeErrorMessageError=!0,r.payload=t,r}return tg(t,e),t}(Error),tw=function(e){return null==e?void 0:e.isRealtimeErrorMessageError},tS=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type="timeout",t.payload=null,t.generic=!0,t}return tg(t,e),t}(Error),tE=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type="cancelled",t.payload=null,t.generic=!0,t}return tg(t,e),t}(Error),tT=function(e){function t(t){var r=e.call(this,t.errMsg)||this;return r.errCode="UNKNOWN_ERROR",Object.defineProperties(r,{message:{get:function(){return"errCode: ".concat(this.errCode," ").concat(tI[this.errCode]||""," | errMsg: ").concat(this.errMsg)},set:function(e){this.errMsg=e}}}),r.errCode=t.errCode||"UNKNOWN_ERROR",r.errMsg=t.errMsg,r}return tg(t,e),Object.defineProperty(t.prototype,"message",{get:function(){return"errCode: ".concat(this.errCode," | errMsg: ").concat(this.errMsg)},set:function(e){this.errMsg=e},enumerable:!1,configurable:!0}),t}(Error),tI={UNKNOWN_ERROR:"UNKNOWN_ERROR",SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL:"SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL",SDK_DATABASE_REALTIME_LISTENER_RECONNECT_WATCH_FAIL:"SDK_DATABASE_REALTIME_LISTENER_RECONNECT_WATCH_FAIL",SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL:"SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL",SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL:"SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL",SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG:"SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG",SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA:"SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA",SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_ERROR:"SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_ERROR",SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_CLOSED:"SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_CLOSED",SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL:"SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL",SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR:"SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR"},tO=function(e){return void 0===e&&(e=0),new Promise(function(t){return setTimeout(t,e)})},tR=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},tA=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},tP=function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};(l=O||(O={})).LOGGINGIN="LOGGINGIN",l.INITING="INITING",l.REBUILDING="REBUILDING",l.ACTIVE="ACTIVE",l.ERRORED="ERRORED",l.CLOSING="CLOSING",l.CLOSED="CLOSED",l.PAUSED="PAUSED",l.RESUMING="RESUMING";var tC=function(){function e(e){var t=this;this.watchStatus=O.INITING,this.wsLogin=function(e,r){return tR(t,void 0,void 0,function(){var t;return tA(this,function(n){switch(n.label){case 0:return this.watchStatus=O.LOGGINGIN,[4,this.login(e,r)];case 1:return t=n.sent(),this.envId||(this.envId=t.envId),[2,t]}})})},this.initWatch=function(e){return tR(t,void 0,void 0,function(){var t,r=this;return tA(this,function(n){switch(n.label){case 0:if(null!==this.initWatchPromise&&void 0!==this.initWatchPromise)return[2,this.initWatchPromise];this.initWatchPromise=new Promise(function(t,n){tR(r,void 0,void 0,function(){var r,o,i,a,s,u,c,l,f,h;return tA(this,function(d){switch(d.label){case 0:if(d.trys.push([0,3,,4]),this.watchStatus===O.PAUSED)return console.log("[realtime] initWatch cancelled on pause"),[2,t()];return[4,this.wsLogin(this.envId,e)];case 1:if(r=d.sent().envId,this.watchStatus===O.PAUSED)return console.log("[realtime] initWatch cancelled on pause"),[2,t()];return this.watchStatus=O.INITING,o={watchId:this.watchId,requestId:tm(),msgType:"INIT_WATCH",msgData:{envId:r,collName:this.collectionName,query:this.query,limit:this.limit,orderBy:this.orderBy}},[4,this.send({msg:o,waitResponse:!0,skipOnMessage:!0,timeout:1e4})];case 2:if(s=(a=(i=d.sent()).msgData).events,u=a.currEvent,this.sessionInfo={queryID:i.msgData.queryID,currentEventId:u-1,currentDocs:[]},s.length>0){for(c=0,l=s;c<l.length;c++)l[c].ID=u;this.handleServerEvents(i)}else this.sessionInfo.currentEventId=u,f=new tb({id:u,docChanges:[],docs:[],type:"init"}),this.listener.onChange(f),this.scheduleSendACK();return this.onWatchStart(this,this.sessionInfo.queryID),this.watchStatus=O.ACTIVE,this.availableRetries.INIT_WATCH=2,t(),[3,4];case 3:return h=d.sent(),this.handleWatchEstablishmentError(h,{operationName:"INIT_WATCH",resolve:t,reject:n}),[3,4];case 4:return[2]}})})}),t=!1,n.label=1;case 1:return n.trys.push([1,,3,4]),[4,this.initWatchPromise];case 2:return n.sent(),t=!0,[3,4];case 3:return this.initWatchPromise=void 0,[7];case 4:return console.log("[realtime] initWatch ".concat(t?"success":"fail")),[2]}})})},this.rebuildWatch=function(e){return tR(t,void 0,void 0,function(){var t,r=this;return tA(this,function(n){switch(n.label){case 0:if(null!==this.rebuildWatchPromise&&void 0!==this.rebuildWatchPromise)return[2,this.rebuildWatchPromise];this.rebuildWatchPromise=new Promise(function(t,n){tR(r,void 0,void 0,function(){var r,o,i,a;return tA(this,function(s){switch(s.label){case 0:if(s.trys.push([0,3,,4]),this.watchStatus===O.PAUSED)return console.log("[realtime] rebuildWatch cancelled on pause"),[2,t()];return[4,this.wsLogin(this.envId,e)];case 1:if(r=s.sent().envId,!this.sessionInfo)throw Error("can not rebuildWatch without a successful initWatch (lack of sessionInfo)");if(this.watchStatus===O.PAUSED)return console.log("[realtime] rebuildWatch cancelled on pause"),[2,t()];return this.watchStatus=O.REBUILDING,o={watchId:this.watchId,requestId:tm(),msgType:"REBUILD_WATCH",msgData:{envId:r,collName:this.collectionName,queryID:this.sessionInfo.queryID,eventID:this.sessionInfo.currentEventId}},[4,this.send({msg:o,waitResponse:!0,skipOnMessage:!1,timeout:1e4})];case 2:return i=s.sent(),this.handleServerEvents(i),this.watchStatus=O.ACTIVE,this.availableRetries.REBUILD_WATCH=2,t(),[3,4];case 3:return a=s.sent(),this.handleWatchEstablishmentError(a,{operationName:"REBUILD_WATCH",resolve:t,reject:n}),[3,4];case 4:return[2]}})})}),t=!1,n.label=1;case 1:return n.trys.push([1,,3,4]),[4,this.rebuildWatchPromise];case 2:return n.sent(),t=!0,[3,4];case 3:return this.rebuildWatchPromise=void 0,[7];case 4:return console.log("[realtime] rebuildWatch ".concat(t?"success":"fail")),[2]}})})},this.handleWatchEstablishmentError=function(e,r){return tR(t,void 0,void 0,function(){var t,n,o,i=this;return tA(this,function(a){return t="INIT_WATCH"===r.operationName,n=function(){i.closeWithError(new tT({errCode:t?tI.SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL:tI.SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL,errMsg:e})),r.reject(e)},o=function(e){i.useRetryTicket(r.operationName)?t?(i.initWatchPromise=void 0,r.resolve(i.initWatch(e))):(i.rebuildWatchPromise=void 0,r.resolve(i.rebuildWatch(e))):n()},this.handleCommonError(e,{onSignError:function(){return o(!0)},onTimeoutError:function(){return o(!1)},onNotRetryableError:n,onCancelledError:r.reject,onUnknownError:function(){tR(i,void 0,void 0,function(){var e,t=this;return tA(this,function(n){switch(n.label){case 0:if(n.trys.push([0,8,,9]),e=function(){return tR(t,void 0,void 0,function(){return tA(this,function(e){switch(e.label){case 0:return this.pause(),[4,this.onceWSConnected()];case 1:return e.sent(),o(!0),[2]}})})},this.isWSConnected())return[3,2];return[4,e()];case 1:case 5:return n.sent(),[3,7];case 2:return[4,tO(100)];case 3:if(n.sent(),this.watchStatus!==O.PAUSED)return[3,4];return r.reject(new tE("".concat(r.operationName," cancelled due to pause after unknownError"))),[3,7];case 4:if(this.isWSConnected())return[3,6];return[4,e()];case 6:o(!1),n.label=7;case 7:return[3,9];case 8:return n.sent(),o(!0),[3,9];case 9:return[2]}})})}}),[2]})})},this.closeWatch=function(){return tR(t,void 0,void 0,function(){var e,t,r;return tA(this,function(n){switch(n.label){case 0:if(e=this.sessionInfo?this.sessionInfo.queryID:"",this.watchStatus!==O.ACTIVE)return this.watchStatus=O.CLOSED,this.onWatchClose(this,e),[2];n.label=1;case 1:return n.trys.push([1,3,4,5]),this.watchStatus=O.CLOSING,t={watchId:this.watchId,requestId:tm(),msgType:"CLOSE_WATCH",msgData:null},[4,this.send({msg:t})];case 2:return n.sent(),this.sessionInfo=void 0,this.watchStatus=O.CLOSED,[3,5];case 3:return r=n.sent(),this.closeWithError(new tT({errCode:tI.SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL,errMsg:r})),[3,5];case 4:return this.onWatchClose(this,e),[7];case 5:return[2]}})})},this.scheduleSendACK=function(){t.clearACKSchedule(),t.ackTimeoutId=setTimeout(function(){t.waitExpectedTimeoutId?t.scheduleSendACK():t.sendACK()},1e4)},this.clearACKSchedule=function(){t.ackTimeoutId&&clearTimeout(t.ackTimeoutId)},this.sendACK=function(){return tR(t,void 0,void 0,function(){var e,t,r;return tA(this,function(n){switch(n.label){case 0:if(n.trys.push([0,2,,3]),this.watchStatus!==O.ACTIVE)return this.scheduleSendACK(),[2];if(!this.sessionInfo)return console.warn("[realtime listener] can not send ack without a successful initWatch (lack of sessionInfo)"),[2];return e={watchId:this.watchId,requestId:tm(),msgType:"CHECK_LAST",msgData:{queryID:this.sessionInfo.queryID,eventID:this.sessionInfo.currentEventId}},[4,this.send({msg:e})];case 1:return n.sent(),this.scheduleSendACK(),[3,3];case 2:if(tw(t=n.sent()))switch((r=t.payload).msgData.code){case"CHECK_LOGIN_FAILED":case"SIGN_EXPIRED_ERROR":case"SIGN_INVALID_ERROR":case"SIGN_PARAM_INVALID":return this.rebuildWatch(),[2];case"QUERYID_INVALID_ERROR":case"SYS_ERR":case"INVALIID_ENV":case"COLLECTION_PERMISSION_DENIED":return this.closeWithError(new tT({errCode:tI.SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL,errMsg:r.msgData.code})),[2]}return this.availableRetries.CHECK_LAST&&this.availableRetries.CHECK_LAST>0?(this.availableRetries.CHECK_LAST-=1,this.scheduleSendACK()):this.closeWithError(new tT({errCode:tI.SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL,errMsg:t})),[3,3];case 3:return[2]}})})},this.handleCommonError=function(e,t){if(tw(e))switch(e.payload.msgData.code){case"CHECK_LOGIN_FAILED":case"SIGN_EXPIRED_ERROR":case"SIGN_INVALID_ERROR":case"SIGN_PARAM_INVALID":t.onSignError(e);return;default:t.onNotRetryableError(e);return}else{if("timeout"===e.type){t.onTimeoutError(e);return}if("cancelled"===e.type){t.onCancelledError(e);return}}t.onUnknownError(e)},this.watchId="watchid_".concat(+new Date,"_").concat(Math.random()),this.envId=e.envId,this.collectionName=e.collectionName,this.query=e.query,this.limit=e.limit,this.orderBy=e.orderBy,this.send=e.send,this.login=e.login,this.isWSConnected=e.isWSConnected,this.onceWSConnected=e.onceWSConnected,this.getWaitExpectedTimeoutLength=e.getWaitExpectedTimeoutLength,this.onWatchStart=e.onWatchStart,this.onWatchClose=e.onWatchClose,this.debug=e.debug,this.availableRetries={INIT_WATCH:2,REBUILD_WATCH:2,CHECK_LAST:2},this.listener=new tv({close:function(){t.closeWatch()},onChange:e.onChange,onError:e.onError,debug:this.debug,virtualClient:this}),this.initWatch()}return e.prototype.onMessage=function(e){var t=this;switch(this.watchStatus){case O.PAUSED:if("ERROR"!==e.msgType)return;break;case O.LOGGINGIN:case O.INITING:case O.REBUILDING:console.warn("[realtime listener] internal non-fatal error: unexpected message received while ".concat(this.watchStatus));return;case O.CLOSED:console.warn("[realtime listener] internal non-fatal error: unexpected message received when the watch has closed");return;case O.ERRORED:console.warn("[realtime listener] internal non-fatal error: unexpected message received when the watch has ended with error");return}if(!this.sessionInfo){console.warn("[realtime listener] internal non-fatal error: sessionInfo not found while message is received.");return}switch(this.scheduleSendACK(),e.msgType){case"NEXT_EVENT":console.warn("nextevent ".concat(e.msgData.currEvent," ignored"),e),this.handleServerEvents(e);break;case"CHECK_EVENT":this.sessionInfo.currentEventId<e.msgData.currEvent&&(this.sessionInfo.expectEventId=e.msgData.currEvent,this.clearWaitExpectedEvent(),this.waitExpectedTimeoutId=setTimeout(function(){t.rebuildWatch()},this.getWaitExpectedTimeoutLength()),console.log("[realtime] waitExpectedTimeoutLength ".concat(this.getWaitExpectedTimeoutLength())));break;case"ERROR":this.closeWithError(new tT({errCode:tI.SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG,errMsg:"".concat(e.msgData.code," - ").concat(e.msgData.message)}));break;default:console.warn("[realtime listener] virtual client receive unexpected msg ".concat(e.msgType,": "),e)}},e.prototype.closeWithError=function(e){var t;this.watchStatus=O.ERRORED,this.clearACKSchedule(),this.listener.onError(e),this.onWatchClose(this,(null===(t=this.sessionInfo)||void 0===t?void 0:t.queryID)||""),console.log("[realtime] client closed (".concat(this.collectionName," ").concat(this.query,") (watchId ").concat(this.watchId,")"))},e.prototype.pause=function(){this.watchStatus=O.PAUSED,console.log("[realtime] client paused (".concat(this.collectionName," ").concat(this.query,") (watchId ").concat(this.watchId,")"))},e.prototype.resume=function(){return tR(this,void 0,void 0,function(){var e;return tA(this,function(t){switch(t.label){case 0:this.watchStatus=O.RESUMING,console.log("[realtime] client resuming with ".concat(this.sessionInfo?"REBUILD_WATCH":"INIT_WATCH"," (").concat(this.collectionName," ").concat(this.query,") (").concat(this.watchId,")")),t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.sessionInfo?this.rebuildWatch():this.initWatch()];case 2:return t.sent(),console.log("[realtime] client successfully resumed (".concat(this.collectionName," ").concat(this.query,") (").concat(this.watchId,")")),[3,4];case 3:return e=t.sent(),console.error("[realtime] client resume failed (".concat(this.collectionName," ").concat(this.query,") (").concat(this.watchId,")"),e),[3,4];case 4:return[2]}})})},e.prototype.useRetryTicket=function(e){return!!this.availableRetries[e]&&this.availableRetries[e]>0&&(this.availableRetries[e]-=1,console.log("[realtime] ".concat(e," use a retry ticket, now only ").concat(this.availableRetries[e]," retry left")),!0)},e.prototype.handleServerEvents=function(e){return tR(this,void 0,void 0,function(){var t;return tA(this,function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),this.scheduleSendACK(),[4,this.handleServerEventsInternel(e)];case 1:return r.sent(),this.postHandleServerEventsValidityCheck(e),[3,3];case 2:throw console.error("[realtime listener] internal non-fatal error: handle server events failed with error: ",t=r.sent()),t;case 3:return[2]}})})},e.prototype.handleServerEventsInternel=function(e){return tR(this,void 0,void 0,function(){var t,r,n,o,i,a,s,u,c,l,f,h;return tA(this,function(d){switch(d.label){case 0:if(t=e.requestId,r=e.msgData.events,n=e.msgType,!r.length||!this.sessionInfo)return[2];o=this.sessionInfo;try{i=r.map(tN)}catch(e){return this.closeWithError(new tT({errCode:tI.SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA,errMsg:e})),[2]}a=tP([],o.currentDocs,!0),s=!1,u=function(r,u){var l,f,h,d,p,y,m,v,b,g,_,w;return tA(this,function(S){switch(S.label){case 0:if(l=i[r],!(o.currentEventId>=l.id))return[3,1];return!i[r-1]||l.id>i[r-1].id?console.warn("[realtime] duplicate event received, cur ".concat(o.currentEventId," but got ").concat(l.id)):console.error("[realtime listener] server non-fatal error: events out of order (the latter event's id is smaller than that of the former) (requestId ".concat(t,")")),[2,"continue"];case 1:if(o.currentEventId!==l.id-1)return[3,2];switch(l.dataType){case"update":if(!l.doc)switch(l.queueType){case"update":case"dequeue":if(f=a.find(function(e){return e._id===l.docId})){if(h=ty()(f),l.updatedFields&&Object.keys(l.updatedFields).forEach(function(e){tf()(h,e,l.updatedFields[e])}),l.removedFields)for(d=0,p=l.removedFields;d<p.length;d++)y=p[d],td()(h,y);l.doc=h}else console.error("[realtime listener] internal non-fatal server error: unexpected update dataType event where no doc is associated.");break;case"enqueue":throw m=new tT({errCode:tI.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,errMsg:'HandleServerEvents: full doc is not provided with dataType="update" and queueType="enqueue" (requestId '.concat(e.requestId,")")}),c.closeWithError(m),m}break;case"replace":if(!l.doc)throw m=new tT({errCode:tI.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,errMsg:'HandleServerEvents: full doc is not provided with dataType="replace" (requestId '.concat(e.requestId,")")}),c.closeWithError(m),m;break;case"remove":(v=a.find(function(e){return e._id===l.docId}))?l.doc=v:console.error("[realtime listener] internal non-fatal server error: unexpected remove event where no doc is associated.");break;case"limit":if(!l.doc)switch(l.queueType){case"dequeue":(v=a.find(function(e){return e._id===l.docId}))?l.doc=v:console.error("[realtime listener] internal non-fatal server error: unexpected limit dataType event where no doc is associated.");break;case"enqueue":throw m=new tT({errCode:tI.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,errMsg:'HandleServerEvents: full doc is not provided with dataType="limit" and queueType="enqueue" (requestId '.concat(e.requestId,")")}),c.closeWithError(m),m}}switch(l.queueType){case"init":s?a.push(l.doc):(s=!0,a=[l.doc]);break;case"enqueue":a.push(l.doc);break;case"dequeue":(b=a.findIndex(function(e){return e._id===l.docId}))>-1?a.splice(b,1):console.error("[realtime listener] internal non-fatal server error: unexpected dequeue event where no doc is associated.");break;case"update":(b=a.findIndex(function(e){return e._id===l.docId}))>-1?a[b]=l.doc:console.error("[realtime listener] internal non-fatal server error: unexpected queueType update event where no doc is associated.")}return(r===u-1||i[r+1]&&i[r+1].id!==l.id)&&(g=tP([],a,!0),_=i.slice(0,r+1).filter(function(e){return e.id===l.id}),c.sessionInfo.currentEventId=l.id,c.sessionInfo.currentDocs=a,w=new tb({id:l.id,docChanges:_,docs:g,msgType:n}),c.listener.onChange(w)),[3,4];case 2:return console.warn("[realtime listener] event received is out of order, cur ".concat(c.sessionInfo.currentEventId," but got ").concat(l.id)),[4,c.rebuildWatch()];case 3:return S.sent(),[2,{value:void 0}];case 4:return[2]}})},c=this,l=0,f=i.length,d.label=1;case 1:if(!(l<f))return[3,4];return[5,u(l,f)];case 2:if("object"==typeof(h=d.sent()))return[2,h.value];d.label=3;case 3:return l++,[3,1];case 4:return[2]}})})},e.prototype.postHandleServerEventsValidityCheck=function(e){if(!this.sessionInfo){console.error("[realtime listener] internal non-fatal error: sessionInfo lost after server event handling, this should never occur");return}if(this.sessionInfo.expectEventId&&this.sessionInfo.currentEventId>=this.sessionInfo.expectEventId&&this.clearWaitExpectedEvent(),this.sessionInfo.currentEventId<e.msgData.currEvent){console.warn("[realtime listener] internal non-fatal error: client eventId does not match with server event id after server event handling");return}},e.prototype.clearWaitExpectedEvent=function(){this.waitExpectedTimeoutId&&(clearTimeout(this.waitExpectedTimeoutId),this.waitExpectedTimeoutId=void 0)},e}();function tN(e){var t={id:e.ID,dataType:e.DataType,queueType:e.QueueType,docId:e.DocID,doc:e.Doc&&"{}"!==e.Doc?JSON.parse(e.Doc):void 0};return"update"===e.DataType&&(e.UpdatedFields&&(t.updatedFields=JSON.parse(e.UpdatedFields)),(e.removedFields||e.RemovedFields)&&(t.removedFields=JSON.parse(e.removedFields))),t}var tx={1e3:{code:1e3,name:"Normal Closure",description:"Normal closure; the connection successfully completed whatever purpose for which it was created."},1001:{code:1001,name:"Going Away",description:"The endpoint is going away, either because of a server failure or because the browser is navigating away from the page that opened the connection."},1002:{code:1002,name:"Protocol Error",description:"The endpoint is terminating the connection due to a protocol error."},1003:{code:1003,name:"Unsupported Data",description:"The connection is being terminated because the endpoint received data of a type it cannot accept (for example, a text-only endpoint received binary data)."},1005:{code:1005,name:"No Status Received",description:"Indicates that no status code was provided even though one was expected."},1006:{code:1006,name:"Abnormal Closure",description:"Used to indicate that a connection was closed abnormally (that is, with no close frame being sent) when a status code is expected."},1007:{code:1007,name:"Invalid frame payload data",description:"The endpoint is terminating the connection because a message was received that contained inconsistent data (e.g., non-UTF-8 data within a text message)."},1008:{code:1008,name:"Policy Violation",description:"The endpoint is terminating the connection because it received a message that violates its policy. This is a generic status code, used when codes 1003 and 1009 are not suitable."},1009:{code:1009,name:"Message too big",description:"The endpoint is terminating the connection because a data frame was received that is too large."},1010:{code:1010,name:"Missing Extension",description:"The client is terminating the connection because it expected the server to negotiate one or more extension, but the server didn't."},1011:{code:1011,name:"Internal Error",description:"The server is terminating the connection because it encountered an unexpected condition that prevented it from fulfilling the request."},1012:{code:1012,name:"Service Restart",description:"The server is terminating the connection because it is restarting."},1013:{code:1013,name:"Try Again Later",description:"The server is terminating the connection due to a temporary condition, e.g. it is overloaded and is casting off some of its clients."},1014:{code:1014,name:"Bad Gateway",description:"The server was acting as a gateway or proxy and received an invalid response from the upstream server. This is similar to 502 HTTP Status Code."},1015:{code:1015,name:"TLS Handshake",description:"Indicates that the connection was closed due to a failure to perform a TLS handshake (e.g., the server certificate can't be verified)."},3e3:{code:3e3,name:"Reconnect WebSocket",description:"The client is terminating the connection because it wants to reconnect"},3001:{code:3001,name:"No Realtime Listeners",description:"The client is terminating the connection because no more realtime listeners exist"},3002:{code:3002,name:"Heartbeat Ping Error",description:"The client is terminating the connection due to its failure in sending heartbeat messages"},3003:{code:3003,name:"Heartbeat Pong Timeout Error",description:"The client is terminating the connection because no heartbeat response is received from the server"},3050:{code:3050,name:"Server Close",description:"The client is terminating the connection because no heartbeat response is received from the server"}};(f=R||(R={}))[f.NormalClosure=1e3]="NormalClosure",f[f.GoingAway=1001]="GoingAway",f[f.ProtocolError=1002]="ProtocolError",f[f.UnsupportedData=1003]="UnsupportedData",f[f.NoStatusReceived=1005]="NoStatusReceived",f[f.AbnormalClosure=1006]="AbnormalClosure",f[f.InvalidFramePayloadData=1007]="InvalidFramePayloadData",f[f.PolicyViolation=1008]="PolicyViolation",f[f.MessageTooBig=1009]="MessageTooBig",f[f.MissingExtension=1010]="MissingExtension",f[f.InternalError=1011]="InternalError",f[f.ServiceRestart=1012]="ServiceRestart",f[f.TryAgainLater=1013]="TryAgainLater",f[f.BadGateway=1014]="BadGateway",f[f.TLSHandshake=1015]="TLSHandshake",f[f.ReconnectWebSocket=3e3]="ReconnectWebSocket",f[f.NoRealtimeListeners=3001]="NoRealtimeListeners",f[f.HeartbeatPingError=3002]="HeartbeatPingError",f[f.HeartbeatPongTimeoutError=3003]="HeartbeatPongTimeoutError",f[f.NoAuthentication=3050]="NoAuthentication";var tq=function(e,t){var r=tx[e],n=r?"".concat(r.name,", code ").concat(e,", reason ").concat(t||r.description):"code ".concat(e);return new tT({errCode:tI.SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_CLOSED,errMsg:n})},tj=null,tk="web",tD=function(){return(tD=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},tL=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},tU=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},tM={OPEN:1},tW=function(){function e(e){var t=this;this.virtualWSClient=new Set,this.queryIdClientMap=new Map,this.watchIdClientMap=new Map,this.pingFailed=0,this.pongMissed=0,this.logins=new Map,this.wsReadySubsribers=[],this.wsResponseWait=new Map,this.rttObserved=[],this.send=function(e){return tL(t,void 0,void 0,function(){var t=this;return tU(this,function(r){return[2,new Promise(function(r,n){tL(t,void 0,void 0,function(){var t,o,i,a,s,u,c,l,f=this;return tU(this,function(h){switch(h.label){case 0:o=!1,i=!1,a=function(e){o=!0,t&&clearTimeout(t),r(e)},s=function(e){i=!0,t&&clearTimeout(t),n(e)},e.timeout&&(t=setTimeout(function(){tL(f,void 0,void 0,function(){return tU(this,function(e){switch(e.label){case 0:if(!(!o||!i))return[3,2];return[4,tO(0)];case 1:e.sent(),o&&i||s(new tS("wsclient.send timedout")),e.label=2;case 2:return[2]}})})},e.timeout)),h.label=1;case 1:if(h.trys.push([1,8,,9]),!(void 0!==this.wsInitPromise||null!==this.wsInitPromise))return[3,3];return[4,this.wsInitPromise];case 2:h.sent(),h.label=3;case 3:if(!this.ws)return s(Error("invalid state: ws connection not exists, can not send message")),[2];if(this.ws.readyState!==tM.OPEN)return s(Error("ws readyState invalid: ".concat(this.ws.readyState,", can not send message"))),[2];e.waitResponse&&(u={resolve:a,reject:s,skipOnMessage:e.skipOnMessage},this.wsResponseWait.set(e.msg.requestId,u)),h.label=4;case 4:return h.trys.push([4,6,,7]),[4,this.ws.send(JSON.stringify(e.msg))];case 5:return h.sent(),e.waitResponse||a(void 0),[3,7];case 6:return(c=h.sent())&&(s(c),e.waitResponse&&this.wsResponseWait.delete(e.msg.requestId)),[3,7];case 7:return[3,9];case 8:return l=h.sent(),s(l),[3,9];case 9:return[2]}})})})]})})},this.closeAllClients=function(e){t.virtualWSClient.forEach(function(t){t.closeWithError(e)})},this.pauseClients=function(e){(e||t.virtualWSClient).forEach(function(e){e.pause()})},this.resumeClients=function(e){(e||t.virtualWSClient).forEach(function(e){e.resume()})},this.initWebSocketConnection=function(e,r){return void 0===r&&(r=t.maxReconnect),tL(t,void 0,void 0,function(){var t=this;return tU(this,function(n){switch(n.label){case 0:if(e&&this.reconnectState)return[2];if(e&&(this.reconnectState=!0),void 0!==this.wsInitPromise&&null!==this.wsInitPromise)return[2,this.wsInitPromise];e&&this.pauseClients(),this.close(R.ReconnectWebSocket),this.wsInitPromise=new Promise(function(n,o){tL(t,void 0,void 0,function(){var t,i,a=this;return tU(this,function(s){switch(s.label){case 0:return s.trys.push([0,6,,11]),[4,this.getWsSign()];case 1:return t=s.sent(),[4,new Promise(function(e){var r=t.wsUrl||"wss://tcb-ws.tencentcloudapi.com",n=tj;a.ws=n?new n(r):new WebSocket(r),e(void 0)})];case 2:if(s.sent(),!this.ws.connect)return[3,4];return[4,this.ws.connect()];case 3:s.sent(),s.label=4;case 4:return[4,this.initWebSocketEvent()];case 5:return s.sent(),n(),e&&(this.resumeClients(),this.reconnectState=!1),[3,11];case 6:if(console.error("[realtime] initWebSocketConnection connect fail",i=s.sent()),!(r>0))return[3,9];return this.wsInitPromise=void 0,[4,tO(this.reconnectInterval)];case 7:s.sent(),e&&(this.reconnectState=!1),s.label=8;case 8:return n(this.initWebSocketConnection(e,r-1)),[3,10];case 9:o(i),e&&this.closeAllClients(new tT({errCode:tI.SDK_DATABASE_REALTIME_LISTENER_RECONNECT_WATCH_FAIL,errMsg:i})),s.label=10;case 10:return[3,11];case 11:return[2]}})})}),n.label=1;case 1:return n.trys.push([1,3,4,5]),[4,this.wsInitPromise];case 2:return n.sent(),this.wsReadySubsribers.forEach(function(e){return(0,e.resolve)()}),[3,5];case 3:return n.sent(),this.wsReadySubsribers.forEach(function(e){return(0,e.reject)()}),[3,5];case 4:return this.wsInitPromise=void 0,this.wsReadySubsribers=[],[7];case 5:return[2]}})})},this.initWebSocketEvent=function(){return new Promise(function(e,r){if(!t.ws)throw Error("can not initWebSocketEvent, ws not exists");var n=!1;t.ws.onopen=function(t){console.warn("[realtime] ws event: open",t),n=!0,e()},t.ws.onerror=function(e){t.logins=new Map,n?(console.error("[realtime] ws event: error",e),t.clearHeartbeat(),t.virtualWSClient.forEach(function(t){return t.closeWithError(new tT({errCode:tI.SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_ERROR,errMsg:e}))})):(console.error("[realtime] ws open failed with ws event: error",e),r(e))},t.ws.onclose=function(e){switch(console.warn("[realtime] ws event: close",e),t.logins=new Map,t.clearHeartbeat(),e.code){case R.ReconnectWebSocket:case R.NoRealtimeListeners:break;case R.HeartbeatPingError:case R.HeartbeatPongTimeoutError:case R.NormalClosure:case R.AbnormalClosure:t.maxReconnect>0?t.initWebSocketConnection(!0,t.maxReconnect):t.closeAllClients(tq(e.code));break;case R.NoAuthentication:t.closeAllClients(tq(e.code,e.reason));break;default:t.maxReconnect>0?t.initWebSocketConnection(!0,t.maxReconnect):t.closeAllClients(tq(e.code))}},t.ws.onmessage=function(e){var r,n=e.data;t.heartbeat();try{r=JSON.parse(n)}catch(e){throw Error("[realtime] onMessage parse res.data error: ".concat(e))}if("ERROR"===r.msgType){var o=null;t.virtualWSClient.forEach(function(e){e.watchId===r.watchId&&(o=e)}),o&&o.listener.onError(r)}var i=t.wsResponseWait.get(r.requestId);if(i){try{"ERROR"===r.msgType?i.reject(new t_(r)):i.resolve(r)}catch(e){console.error("ws onMessage responseWaitSpec.resolve(msg) errored:",e)}finally{t.wsResponseWait.delete(r.requestId)}if(i.skipOnMessage)return}if("PONG"===r.msgType){if(t.lastPingSendTS){var a=Date.now()-t.lastPingSendTS;if(a>1e4){console.warn("[realtime] untrusted rtt observed: ".concat(a));return}t.rttObserved.length>=3&&t.rttObserved.splice(0,t.rttObserved.length-3+1),t.rttObserved.push(a)}return}var s=r.watchId&&t.watchIdClientMap.get(r.watchId);if(s)s.onMessage(r);else switch(console.error("[realtime] no realtime listener found responsible for watchId ".concat(r.watchId,": "),r),r.msgType){case"INIT_EVENT":case"NEXT_EVENT":case"CHECK_EVENT":(s=t.queryIdClientMap.get(r.msgData.queryID))&&s.onMessage(r);break;default:for(var u=0,c=Array.from(t.watchIdClientMap.entries());u<c.length;u++){c[u][1].onMessage(r);break}}},t.heartbeat()})},this.isWSConnected=function(){return!!(t.ws&&t.ws.readyState===tM.OPEN)},this.onceWSConnected=function(){return tL(t,void 0,void 0,function(){var e=this;return tU(this,function(t){return this.isWSConnected()?[2]:null!==this.wsInitPromise&&void 0!==this.wsInitPromise?[2,this.wsInitPromise]:[2,new Promise(function(t,r){e.wsReadySubsribers.push({resolve:t,reject:r})})]})})},this.webLogin=function(e,r){return tL(t,void 0,void 0,function(){var t,n,o,i,a,s,u,c,l=this;return tU(this,function(f){switch(f.label){case 0:if(!r){if(e){if(t=this.logins.get(e)){if(t.loggedIn&&t.loginResult)return[2,t.loginResult];if(null!==t.loggingInPromise&&void 0!==t.loggingInPromise)return[2,t.loggingInPromise]}}else if((null==(n=this.logins.get(""))?void 0:n.loggingInPromise)!==null&&(null==n?void 0:n.loggingInPromise)!==void 0)return[2,n.loggingInPromise]}o=new Promise(function(e,t){tL(l,void 0,void 0,function(){var r,n,o,i;return tU(this,function(a){switch(a.label){case 0:return a.trys.push([0,3,,4]),[4,this.getWsSign()];case 1:return n={envId:(r=a.sent()).envId||"",accessToken:"",referrer:"web",sdkVersion:"",dataVersion:""},o={watchId:void 0,requestId:tm(),msgType:"LOGIN",msgData:n,exMsgData:{runtime:tk,signStr:r.signStr,secretVersion:r.secretVersion}},[4,this.send({msg:o,waitResponse:!0,skipOnMessage:!0,timeout:5e3})];case 2:return(i=a.sent()).msgData.code?t(Error("".concat(i.msgData.code," ").concat(i.msgData.message))):e({envId:r.envId}),[3,4];case 3:return t(a.sent()),[3,4];case 4:return[2]}})})}),i=e&&this.logins.get(e),a=Date.now(),i?(i.loggedIn=!1,i.loggingInPromise=o,i.loginStartTS=a):(i={loggedIn:!1,loggingInPromise:o,loginStartTS:a},this.logins.set(e||"",i)),f.label=1;case 1:return f.trys.push([1,3,,4]),[4,o];case 2:if(s=f.sent(),(u=e&&this.logins.get(e))&&u===i&&u.loginStartTS===a)return i.loggedIn=!0,i.loggingInPromise=void 0,i.loginStartTS=void 0,i.loginResult=s,[2,s];if(u){if(u.loggedIn&&u.loginResult)return[2,u.loginResult];if(null!==u.loggingInPromise&&void 0!==u.loggingInPromise)return[2,u.loggingInPromise];throw Error("ws unexpected login info")}throw Error("ws login info reset");case 3:throw c=f.sent(),i.loggedIn=!1,i.loggingInPromise=void 0,i.loginStartTS=void 0,i.loginResult=void 0,c;case 4:return[2]}})})},this.getWsSign=function(){return tL(t,void 0,void 0,function(){var e,t,r,n;return tU(this,function(o){switch(o.label){case 0:if(this.wsSign&&this.wsSign.expiredTs>Date.now())return[2,this.wsSign];return e=Date.now()+6e4,[4,this.context.appConfig.request.send("auth.wsWebSign",{runtime:tk})];case 1:if((t=o.sent()).code)throw Error("[tcb-js-sdk] 获取实时数据推送登录票据失败: ".concat(t.code));if(t.data)return n=(r=t.data).signStr,[2,{signStr:n,wsUrl:r.wsUrl,secretVersion:r.secretVersion,envId:r.envId,expiredTs:e}];throw Error("[tcb-js-sdk] 获取实时数据推送登录票据失败")}})})},this.getWaitExpectedTimeoutLength=function(){return t.rttObserved.length?t.rttObserved.reduce(function(e,t){return e+t})/t.rttObserved.length*1.5:5e3},this.ping=function(){return tL(t,void 0,void 0,function(){var e;return tU(this,function(t){switch(t.label){case 0:return e={watchId:void 0,requestId:tm(),msgType:"PING",msgData:null},[4,this.send({msg:e})];case 1:return t.sent(),[2]}})})},this.onWatchStart=function(e,r){t.queryIdClientMap.set(r,e)},this.onWatchClose=function(e,r){r&&t.queryIdClientMap.delete(r),t.watchIdClientMap.delete(e.watchId),t.virtualWSClient.delete(e),t.virtualWSClient.size||t.close(R.NoRealtimeListeners)},this.maxReconnect=e.maxReconnect||5,this.reconnectInterval=e.reconnectInterval||1e4,this.context=e.context}return e.prototype.clearHeartbeat=function(){this.pingTimeoutId&&clearTimeout(this.pingTimeoutId),this.pongTimeoutId&&clearTimeout(this.pongTimeoutId)},e.prototype.close=function(e){this.clearHeartbeat(),this.ws&&(this.ws.close(e,tx[e].name),this.ws=void 0)},e.prototype.watch=function(e){this.ws||void 0!==this.wsInitPromise&&null!==this.wsInitPromise||this.initWebSocketConnection(!1);var t=new tC(tD(tD({},e),{send:this.send,login:this.webLogin,isWSConnected:this.isWSConnected,onceWSConnected:this.onceWSConnected,getWaitExpectedTimeoutLength:this.getWaitExpectedTimeoutLength,onWatchStart:this.onWatchStart,onWatchClose:this.onWatchClose,debug:!0}));return this.virtualWSClient.add(t),this.watchIdClientMap.set(t.watchId,t),t.listener},e.prototype.heartbeat=function(e){var t=this;this.clearHeartbeat(),this.pingTimeoutId=setTimeout(function(){tL(t,void 0,void 0,function(){var e=this;return tU(this,function(t){switch(t.label){case 0:if(t.trys.push([0,2,,3]),!this.ws||this.ws.readyState!==tM.OPEN)return[2];return this.lastPingSendTS=Date.now(),[4,this.ping()];case 1:return t.sent(),this.pingFailed=0,this.pongTimeoutId=setTimeout(function(){console.error("pong timed out"),e.pongMissed<2?(e.pongMissed+=1,e.heartbeat(!0)):e.initWebSocketConnection(!0)},this.context.appConfig.realtimePongWaitTimeout),[3,3];case 2:return t.sent(),this.pingFailed<2?(this.pingFailed+=1,this.heartbeat()):this.close(R.HeartbeatPingError),[3,3];case 3:return[2]}})})},e?0:this.context.appConfig.realtimePingInterval)},e}(),tF={target:"database",entity:function(){var e=this.platform,t=e.adapter,r=e.runtime;tj=t.wsClass,tk=r}},tB={name:"realtime",IIFE:!0,entity:function(){this.prototype.wsClientClass=tW}};try{cloudbase.registerComponent(tB),cloudbase.registerHook(tF)}catch(e){}var tG=function(e,t,r,n){var o,i=arguments.length,a=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,r,n);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(i<3?o(a):i>3?o(t,r,a):o(t,r))||a);return i>3&&a&&Object.defineProperty(t,r,a),a},tV=function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},tH=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},t$="analytics",tz=["mall"],tK=new(function(){function e(){}return e.prototype.analytics=function(e){var t,r,n,o;return t=this,r=void 0,n=void 0,o=function(){var t,r,n;return tH(this,function(o){if(!function(e){if("Object"!==Object.prototype.toString.call(e).slice(8,-1))return!1;var t=e.report_data,r=e.report_type;return!!(!1!==tz.includes(r)&&"Object"===Object.prototype.toString.call(t).slice(8,-1)&&(void 0===t.action_time||Number.isInteger(t.action_time)))&&"string"==typeof t.action_type}(e))throw Error(JSON.stringify({code:W.INVALID_PARAMS,msg:"[".concat(t$,".analytics] invalid report data")}));return t="analytics.report",r=void 0===e.report_data.action_time?Math.floor(Date.now()/1e3):e.report_data.action_time,n={requestData:{analytics_scene:e.report_type,analytics_data:Object.assign({},e.report_data,{action_time:r})}},this.request.send(t,n),[2]})},new(n||(n=Promise))(function(e,i){function a(e){try{u(o.next(e))}catch(e){i(e)}}function s(e){try{u(o.throw(e))}catch(e){i(e)}}function u(t){var r;t.done?e(t.value):((r=t.value)instanceof n?r:new n(function(e){e(r)})).then(a,s)}u((o=o.apply(t,r||[])).next())})},tG([ef({customInfo:{className:"Cloudbase",methodName:"analytics"},title:"上报调用失败",messages:["请确认以下各项：","  1 - 调用 analytics() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(M)]}),tV("design:type",Function),tV("design:paramtypes",[Object]),tV("design:returntype",Promise)],e.prototype,"analytics",null),e}()),tJ={name:t$,entity:{analytics:tK.analytics}};try{cloudbase.registerComponent(tJ)}catch(e){}var tQ=r(10991),tY=function(){return(tY=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},tX=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},tZ="models",t0=new WeakMap,t1="cloudbase_init",t2=new ec;t2.on(t1,function(e){var t=e.data;Object.assign(t,{models:new Proxy({},{get:function(e,r){var n=t0.get(t);return n||(n=function(e){var t=this,r=e.callFunction.bind(e),n=e.request.fetch.bind(e.request),o=e.getEndPointWithKey("GATEWAY"),i=o.BASE_URL,a=o.PROTOCOL,s="".concat(a).concat(i,"/model"),u="".concat(a).concat(i,"/sql");return(0,tQ.generateHTTPClient)(r,function(e){var r,o,i;return r=void 0,o=void 0,i=function(){return tX(this,function(t){switch(t.label){case 0:return[4,n(tY(tY({},e),{headers:tY({"Content-Type":"application/json"},e.headers)}))];case 1:return[4,t.sent().data];case 2:return[2,t.sent()]}})},new(o||(o=Promise))(function(e,n){function a(e){try{u(i.next(e))}catch(e){n(e)}}function s(e){try{u(i.throw(e))}catch(e){n(e)}}function u(t){var r;t.done?e(t.value):((r=t.value)instanceof o?r:new o(function(e){e(r)})).then(a,s)}u((i=i.apply(t,r||[])).next())})},s,{sqlBaseUrl:u})}(t),t0.set(t,n)),n[r]}})})});var t3={name:tZ,namespace:tZ,entity:new Proxy({},{get:function(e,t){console.warn("【deprecated】Accessing Cloudbase.prototype.models.".concat(t,"."))}}),injectEvents:{bus:t2,events:[t1]}};try{cloudbase.registerComponent(t3)}catch(e){}var t4=r(41822),t6=function(e,t){return(t6=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function t5(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}t6(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}function t7(e){if(!e)throw TypeError("Assertion failed")}function t8(){}function t9(e){return("object"==typeof e&&null!==e||"function"==typeof e)&&"function"==typeof e.getReader}function re(e){try{return e.getReader({mode:"byob"}).releaseLock(),!0}catch(e){return!1}}function rt(e,t){var r=(void 0===t?{}:t).type;return t7(t9(e)),t7(!1===e.locked),"bytes"===(r=rr(r))?new ra(e):new ro(e)}function rr(e){var t=String(e);if("bytes"===t)return t;if(void 0===e)return e;throw RangeError("Invalid type is specified")}var rn=function(){function e(e){this._underlyingReader=void 0,this._readerMode=void 0,this._readableStreamController=void 0,this._pendingRead=void 0,this._underlyingStream=e,this._attachDefaultReader()}return e.prototype.start=function(e){this._readableStreamController=e},e.prototype.cancel=function(e){return t7(void 0!==this._underlyingReader),this._underlyingReader.cancel(e)},e.prototype._attachDefaultReader=function(){if("default"!==this._readerMode){this._detachReader();var e=this._underlyingStream.getReader();this._readerMode="default",this._attachReader(e)}},e.prototype._attachReader=function(e){var t=this;t7(void 0===this._underlyingReader),this._underlyingReader=e;var r=this._underlyingReader.closed;r&&r.then(function(){return t._finishPendingRead()}).then(function(){e===t._underlyingReader&&t._readableStreamController.close()},function(r){e===t._underlyingReader&&t._readableStreamController.error(r)}).catch(t8)},e.prototype._detachReader=function(){void 0!==this._underlyingReader&&(this._underlyingReader.releaseLock(),this._underlyingReader=void 0,this._readerMode=void 0)},e.prototype._pullWithDefaultReader=function(){var e=this;this._attachDefaultReader();var t=this._underlyingReader.read().then(function(t){var r=e._readableStreamController;t.done?e._tryClose():r.enqueue(t.value)});return this._setPendingRead(t),t},e.prototype._tryClose=function(){try{this._readableStreamController.close()}catch(e){}},e.prototype._setPendingRead=function(e){var t,r=this,n=function(){r._pendingRead===t&&(r._pendingRead=void 0)};this._pendingRead=t=e.then(n,n)},e.prototype._finishPendingRead=function(){var e=this;if(this._pendingRead){var t=function(){return e._finishPendingRead()};return this._pendingRead.then(t,t)}},e}(),ro=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return t5(t,e),t.prototype.pull=function(){return this._pullWithDefaultReader()},t}(rn);function ri(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}var ra=function(e){function t(t){var r=this,n=re(t);return(r=e.call(this,t)||this)._supportsByob=n,r}return t5(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return"bytes"},enumerable:!1,configurable:!0}),t.prototype._attachByobReader=function(){if("byob"!==this._readerMode){t7(this._supportsByob),this._detachReader();var e=this._underlyingStream.getReader({mode:"byob"});this._readerMode="byob",this._attachReader(e)}},t.prototype.pull=function(){if(this._supportsByob){var e=this._readableStreamController.byobRequest;if(e)return this._pullWithByobRequest(e)}return this._pullWithDefaultReader()},t.prototype._pullWithByobRequest=function(e){var t=this;this._attachByobReader();var r=new Uint8Array(e.view.byteLength),n=this._underlyingReader.read(r).then(function(r){t._readableStreamController,r.done?(t._tryClose(),e.respond(0)):(function(e,t){var r=ri(e);ri(t).set(r,0)}(r.value,e.view),e.respond(r.value.byteLength))});return this._setPendingRead(n),n},t}(rn);!function(){function e(e){var t=this;this._writableStreamController=void 0,this._pendingWrite=void 0,this._state="writable",this._storedError=void 0,this._underlyingWriter=e,this._errorPromise=new Promise(function(e,r){t._errorPromiseReject=r}),this._errorPromise.catch(t8)}e.prototype.start=function(e){var t=this;this._writableStreamController=e,this._underlyingWriter.closed.then(function(){t._state="closed"}).catch(function(e){return t._finishErroring(e)})},e.prototype.write=function(e){var t=this,r=this._underlyingWriter;if(null===r.desiredSize)return r.ready;var n=r.write(e);n.catch(function(e){return t._finishErroring(e)}),r.ready.catch(function(e){return t._startErroring(e)});var o=Promise.race([n,this._errorPromise]);return this._setPendingWrite(o),o},e.prototype.close=function(){var e=this;return void 0===this._pendingWrite?this._underlyingWriter.close():this._finishPendingWrite().then(function(){return e.close()})},e.prototype.abort=function(e){if("errored"!==this._state)return this._underlyingWriter.abort(e)},e.prototype._setPendingWrite=function(e){var t,r=this,n=function(){r._pendingWrite===t&&(r._pendingWrite=void 0)};this._pendingWrite=t=e.then(n,n)},e.prototype._finishPendingWrite=function(){var e=this;if(void 0===this._pendingWrite)return Promise.resolve();var t=function(){return e._finishPendingWrite()};return this._pendingWrite.then(t,t)},e.prototype._startErroring=function(e){var t=this;if("writable"===this._state){this._state="erroring",this._storedError=e;var r=function(){return t._finishErroring(e)};void 0===this._pendingWrite?r():this._finishPendingWrite().then(r,r),this._writableStreamController.error(e)}},e.prototype._finishErroring=function(e){"writable"===this._state&&this._startErroring(e),"erroring"===this._state&&(this._state="errored",this._errorPromiseReject(this._storedError))}}(),function(){function e(e,t){var r=this;this._transformStreamController=void 0,this._onRead=function(e){if(!e.done)return r._transformStreamController.enqueue(e.value),r._reader.read().then(r._onRead)},this._onError=function(e){r._flushReject(e),r._transformStreamController.error(e),r._reader.cancel(e).catch(t8),r._writer.abort(e).catch(t8)},this._onTerminate=function(){r._flushResolve(),r._transformStreamController.terminate();var e=TypeError("TransformStream terminated");r._writer.abort(e).catch(t8)},this._reader=e,this._writer=t,this._flushPromise=new Promise(function(e,t){r._flushResolve=e,r._flushReject=t})}e.prototype.start=function(e){this._transformStreamController=e,this._reader.read().then(this._onRead).then(this._onTerminate,this._onError);var t=this._reader.closed;t&&t.then(this._onTerminate,this._onError)},e.prototype.transform=function(e){return this._writer.write(e)},e.prototype.flush=function(){var e=this;return this._writer.close().then(function(){return e._flushPromise})}}();var rs=[239,187,191],ru=r(88687),rc=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},rl=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},rf=function(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e="function"==typeof __values?__values(e):e[Symbol.iterator](),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise(function(n,o){!function(e,t,r,n){Promise.resolve(n).then(function(t){e({value:t,done:r})},t)}(n,o,(t=e[r](t)).done,t.value)})}}},rh=t4.Pz,rd=t4.GJ,rp=function(){var e;return new rd({start:function(t){e=function(e){var t,r,n,o,i,a,s;return u(),{feed:function(u){var c;r=r?r+u:u,t&&(c=r,rs.every(function(e,t){return c.charCodeAt(t)===e}))&&(r=r.slice(rs.length)),t=!1;for(var l=r.length,f=0,h=!1;f<l;){h&&("\n"===r[f]&&(f+=1),h=!1);for(var d=-1,p=o,y=void 0,m=n;d<0&&m<l;m++)":"===(y=r[m])&&p<0?p=m-f:"\r"===y?(h=!0,d=m-f):"\n"===y&&(d=m-f);if(d<0){n=l-f,o=p;break}n=0,o=-1,function(t,r,n,o){if(0===o){s.length>0&&(e({type:"event",id:i,event:a||void 0,data:s.slice(0,-1)}),s="",i=void 0),a=void 0;return}var u=n<0,c=t.slice(r,r+(u?o:n)),l=0;l=u?o:" "===t[r+n+1]?n+2:n+1;var f=r+l,h=o-l,d=t.slice(f,f+h).toString();if("data"===c)s+=d?"".concat(d,"\n"):"\n";else if("event"===c)a=d;else if("id"!==c||d.includes("\0")){if("retry"===c){var p=parseInt(d,10);Number.isNaN(p)||e({type:"reconnect-interval",value:p})}}else i=d}(r,f,p,d),f+=d+1}f===l?r="":f>0&&(r=r.slice(f))},reset:u};function u(){t=!0,r="",n=0,o=-1,i=void 0,a=void 0,s=""}}(function(e){"event"===e.type&&t.enqueue(e)})},transform:function(t){e.feed(t)}})},ry=(t7(!!(function(e){if("function"!=typeof e)return!1;var t=!1;try{new e({start:function(){t=!0}})}catch(e){}return t}(rh)&&t9(new rh))),h=function(e){try{return new e({type:"bytes"}),!0}catch(e){return!1}}(rh),function(e,t){var r=(void 0===t?{}:t).type;if("bytes"!==(r=rr(r))||h||(r=void 0),e.constructor===rh&&("bytes"!==r||re(e)))return e;if("bytes"===r){var n=rt(e,{type:r});return new rh(n)}var n=rt(e);return new rh(n)}),rm=function(){function e(e,t){void 0===e&&(e="utf-8"),void 0===t&&(t={});var r=this;this.transform=new rd({transform:function(e,t){var n=r.handle.decode(new Uint8Array(e),{stream:!0});n&&t.enqueue(n)},flush:function(e){var t=r.handle.decode();t&&e.enqueue(t),e.terminate()}}),this.handle=new ru.TextDecoder(e,t)}return Object.defineProperty(e.prototype,"encoding",{get:function(){return this.handle.encoding},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fatal",{get:function(){return this.handle.fatal},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"ignoreBOM",{get:function(){return this.handle.ignoreBOM},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"readable",{get:function(){return this.transform.readable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"writable",{get:function(){return this.transform.writable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,Symbol.toStringTag,{get:function(){return"TextDecoderStream"},enumerable:!1,configurable:!0}),e}();function rv(e){return e[Symbol.asyncIterator]=function(){var t=e.getReader();return{next:function(){return rc(this,void 0,void 0,function(){var e,r,n;return rl(this,function(o){switch(o.label){case 0:return[4,t.read()];case 1:return r=(e=o.sent()).done,n=e.value,[2,r?{done:!0,value:void 0}:{done:!1,value:n}]}})})}}},e}function rb(e){return rv(e.pipeThrough(new rm).pipeThrough(rp()).pipeThrough(new rd({transform:function(e,t){try{var r=JSON.parse(e.data);t.enqueue(r)}catch(r){"[DONE]"!==e.data?console.warn("Error when transforming event source data to json",r,e):t.terminate()}}})))}function rg(){var e,t;return{promise:new Promise(function(r,n){e=r,t=n}),res:e,rej:t}}function r_(e){var t;return"assistant"===e.role&&"tool_calls"in e&&(null===(t=e.tool_calls)||void 0===t?void 0:t[0])!=null}var rw=function(){return(rw=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},rS=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},rE=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},rT=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r},rI=function(){function e(e,t){this.baseUrl=t;var r=arguments[2];"string"==typeof r?this.req=function(t){var n=t.headers,o=rT(t,["headers"]);return e(rw(rw({},o),{headers:rw(rw({},void 0===n?{}:n),{Authorization:"Bearer ".concat(r)})}))}:this.req=e}return e.prototype.list=function(e,t){return this.req({method:"get",url:this.join("bots"),data:e,timeout:null==t?void 0:t.timeout})},e.prototype.create=function(e,t){var r=e.botInfo;return this.req({method:"post",url:this.join("bots"),data:r,timeout:null==t?void 0:t.timeout})},e.prototype.get=function(e,t){var r=e.botId;return this.req({method:"get",url:this.join("bots/".concat(r)),timeout:null==t?void 0:t.timeout})},e.prototype.update=function(e,t){var r=e.botId,n=e.botInfo;return this.req({method:"PATCH",url:this.join("bots/".concat(r)),data:n,timeout:null==t?void 0:t.timeout})},e.prototype.delete=function(e,t){var r=e.botId;return this.req({method:"delete",url:this.join("bots/".concat(r)),timeout:null==t?void 0:t.timeout})},e.prototype.getChatRecords=function(e,t){return this.req({method:"get",url:this.join("bots/".concat(e.botId,"/records")),data:e,timeout:null==t?void 0:t.timeout})},e.prototype.sendFeedback=function(e,t){var r=e.userFeedback;return this.req({method:"post",url:this.join("bots/".concat(r.botId,"/feedback")),data:r,timeout:null==t?void 0:t.timeout})},e.prototype.getFeedback=function(e,t){return this.req({method:"get",url:this.join("bots/".concat(e.botId,"/feedback")),data:e,timeout:null==t?void 0:t.timeout})},e.prototype.uploadFiles=function(e,t){return rS(this,void 0,void 0,function(){return rE(this,function(r){return[2,this.req({method:"post",url:this.join("bots/".concat(e.botId,"/files")),data:e,timeout:null==t?void 0:t.timeout})]})})},e.prototype.createConversation=function(e,t){return rS(this,void 0,void 0,function(){return rE(this,function(r){return[2,this.req({method:"post",url:this.join("conversation"),data:e,timeout:null==t?void 0:t.timeout})]})})},e.prototype.getConversation=function(e,t){var r=e.pageSize,n=void 0===r?10:r,o=e.pageNumber,i=void 0===o?1:o,a=rT(e,["pageSize","pageNumber"]);return rS(this,void 0,void 0,function(){var e,r;return rE(this,function(o){if(i<1)throw Error("pageNumber must be greater than 0");return e=n*(i-1),r=n,[2,this.req({method:"get",url:this.join("conversation"),data:rw(rw({},a),{offset:e,limit:r}),timeout:null==t?void 0:t.timeout})]})})},e.prototype.deleteConversation=function(e,t){return rS(this,void 0,void 0,function(){return rE(this,function(r){return[2,this.req({method:"delete",url:this.join("conversation/".concat(e.conversationId)),data:e,timeout:null==t?void 0:t.timeout})]})})},e.prototype.speechToText=function(e,t){return rS(this,void 0,void 0,function(){return rE(this,function(r){return[2,this.req({method:"post",url:this.join("bots/".concat(e.botId,"/speech-to-text")),data:e,timeout:null==t?void 0:t.timeout})]})})},e.prototype.textToSpeech=function(e,t){return rS(this,void 0,void 0,function(){return rE(this,function(r){return[2,this.req({method:"post",url:this.join("bots/".concat(e.botId,"/text-to-speech")),data:e,timeout:null==t?void 0:t.timeout})]})})},e.prototype.getTextToSpeechResult=function(e,t){return rS(this,void 0,void 0,function(){return rE(this,function(r){return[2,this.req({method:"get",url:this.join("bots/".concat(e.botId,"/text-to-speech")),data:e,timeout:null==t?void 0:t.timeout})]})})},e.prototype.getRecommendQuestions=function(e,t){return rS(this,void 0,void 0,function(){return rE(this,function(r){switch(r.label){case 0:return[4,this.req({method:"post",url:this.join("bots/".concat(e.botId,"/recommend-questions")),data:e,stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,new rO(r.sent())]}})})},e.prototype.generateBot=function(e,t){return rS(this,void 0,void 0,function(){return rE(this,function(r){switch(r.label){case 0:return[4,this.req({method:"post",url:this.join("generate-bot"),data:e,stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,new rO(r.sent())]}})})},e.prototype.getPreview=function(e,t){return rS(this,void 0,void 0,function(){return rE(this,function(r){switch(r.label){case 0:return[4,this.req({method:"post",url:this.join("preview"),data:e,stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,new rO(r.sent())]}})})},e.prototype.generateImage=function(e,t){return this.req({method:"post",url:this.join("generate-image"),data:e,timeout:null==t?void 0:t.timeout})},e.prototype.sendMessage=function(e,t){return rS(this,void 0,void 0,function(){return rE(this,function(r){switch(r.label){case 0:return[4,this.req({method:"post",url:this.join("bots/".concat(e.botId,"/send-message")),data:e,stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,new rO(r.sent())]}})})},e.prototype.join=function(e){return"".concat(this.baseUrl,"/").concat(e)},e}(),rO=function(){function e(e){var t=ry(e);this._eventSourceStream=t.pipeThrough(new rm).pipeThrough(rp())}return Object.defineProperty(e.prototype,"teeedStream",{get:function(){var e=this._eventSourceStream.tee(),t=e[0],r=e[1];return this._eventSourceStream=r,t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"eventSourceStream",{get:function(){return rv(this.teeedStream)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dataStream",{get:function(){return rv(this.eventSourceStream.pipeThrough(new rd({transform:function(e,t){try{var r=JSON.parse(e.data);t.enqueue(r)}catch(r){"[DONE]"!==e.data?console.warn("Error when transforming event source data to json",r,e):t.terminate()}}})))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"textStream",{get:function(){return rv(this.dataStream.pipeThrough(new rd({transform:function(e,t){var r;t.enqueue(null!==(r=null==e?void 0:e.content)&&void 0!==r?r:"")}})))},enumerable:!1,configurable:!0}),e}(),rR=function(){return(rR=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},rA=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},rP=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}};function rC(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tool_choice,i=e.tools,a=e.top_p;return rR(rR({},e),{messages:t,model:r,temperature:n,tool_choice:(o&&"auto"!==o&&console.warn("`tool_choice` is not 'auto'"),o),tools:i,top_p:a})}var rN=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="zhipu/api/paas/v4/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return rA(this,void 0,void 0,function(){var r,n;return rP(this,function(o){switch(o.label){case 0:return r=rC(e),[4,this.req({url:this.url,data:rR(rR({},r),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return n=o.sent(),[2,rR(rR({},n),{rawResponse:n})]}})})},e.prototype.doStream=function(e,t){return rA(this,void 0,void 0,function(){var r,n;return rP(this,function(o){switch(o.label){case 0:return r=rC(e),n=null,[4,this.req({url:this.url,data:rR(rR({},r),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,rv(rb(ry(o.sent())).pipeThrough(new rd({transform:function(e,t){var r=e.choices.map(function(e){var t=e.delta;return(null==n&&(n=r_(t)),n)?rR(rR({},e),{finish_reason:"tool_calls",delta:t}):e}),o=rR(rR({},e),{choices:r});t.enqueue(rR(rR({},o),{rawResponse:e}))}})))]}})})},e}(),rx=function(){return(rx=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function rq(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tool_choice,i=e.tools,a=e.top_p;return rx(rx({},e),{messages:(t.forEach(function(e){"tool_calls"in e&&e.tool_calls.filter(function(e){return"function"!==e.type}).forEach(function(t){return console.warn("`type` in tool_call is not 'function'",t,e)})}),t),model:r,tools:function(){if(i)return i.forEach(function(e){"function"!==e.type&&console.warn("`type` in tool is not 'function'",e)}),i}(),top_p:a,tool_choice:o,temperature:n})}function rj(e){return"object"!=typeof e||null==e?e:Array.isArray(e)?e.map(function(e){return rj(e)}):Object.entries(e).reduce(function(e,t){var r,n=t[0],o=t[1];return e["_"===(r=n.replace(/[A-Z]/g,function(e){return"_".concat(e.toLowerCase())})).charAt(0)?r.slice(1):r]="object"==typeof o?rj(o):o,e},{})}var rk=function(){return(rk=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},rD=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},rL=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},rU=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="hunyuan-beta/openapi/v1/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return rD(this,void 0,void 0,function(){var r;return rL(this,function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:rk(rk({},rq(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,rk(rk({},r),{rawResponse:r})]}})})},e.prototype.doStream=function(e,t){return rD(this,void 0,void 0,function(){var r;return rL(this,function(n){switch(n.label){case 0:return r=null,[4,this.req({url:this.url,data:rk(rk({},rq(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,rv(rb(ry(n.sent())).pipeThrough(new rd({transform:function(e,t){var n=e.choices.map(function(e){var t=e.delta;return(null==r&&(r=r_(t)),r)?rk(rk({},e),{finish_reason:"tool_calls",delta:t}):e}),o=rk(rk({},e),{choices:n});t.enqueue(rk(rk({},o),{rawResponse:e}))}})))]}})})},e}(),rM=function(){return(rM=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},rW=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},rF=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},rB=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="hunyuan",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return rW(this,void 0,void 0,function(){var r,n;return rF(this,function(o){switch(o.label){case 0:return[4,this.req({url:this.url,headers:{"X-Tc-Action":"ChatCompletions"},data:rM(rM({},rq(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return n=rj((r=o.sent()).Response),[2,rM(rM({},n),{rawResponse:r})]}})})},e.prototype.doStream=function(e,t){return rW(this,void 0,void 0,function(){var r;return rF(this,function(n){switch(n.label){case 0:return r=null,[4,this.req({url:this.url,headers:{"X-Tc-Action":"ChatCompletions"},data:rM(rM({},e),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,rv(rb(ry(n.sent())).pipeThrough(new rd({transform:function(e,t){var n=rj(e),o=n.choices.map(function(e){var t=e.delta;return(null==r&&(r=r_(t)),r)?rM(rM({},e),{finish_reason:"tool_calls",delta:t}):e}),i=rM(rM({},n),{choices:o});t.enqueue(rM(rM({},i),{rawResponse:e}))}})))]}})})},e}(),rG=function(){return(rG=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},rV=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},rH=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}};function r$(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tools,i=e.top_p;return rG(rG({},e),{messages:t,model:r,tools:o,top_p:i,temperature:n})}var rz=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="ark/api/v3/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return rV(this,void 0,void 0,function(){var r;return rH(this,function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:rG(rG({},r$(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,rG(rG({},r),{rawResponse:r})]}})})},e.prototype.doStream=function(e,t){return rV(this,void 0,void 0,function(){var r;return rH(this,function(n){switch(n.label){case 0:return r=null,[4,this.req({url:this.url,data:rG(rG({},r$(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,rv(rb(ry(n.sent())).pipeThrough(new rd({transform:function(e,t){var n=e.choices.map(function(e){var t=e.delta;return(null==r&&(r=r_(t)),r)?rG(rG({},e),{finish_reason:"tool_calls",delta:t}):e}),o=rG(rG({},e),{choices:n});t.enqueue(rG(rG({},o),{rawResponse:e}))}})))]}})})},e}(),rK=function(){return(rK=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},rJ=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},rQ=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}};function rY(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tools,i=e.top_p;return rK(rK({},e),{messages:t,model:r,tools:o,top_p:i,temperature:n})}var rX=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="dashscope/compatible-mode/v1/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return rJ(this,void 0,void 0,function(){var r;return rQ(this,function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:rK(rK({},rY(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,rK(rK({},r),{rawResponse:r})]}})})},e.prototype.doStream=function(e,t){return rJ(this,void 0,void 0,function(){var r;return rQ(this,function(n){switch(n.label){case 0:return r=null,[4,this.req({url:this.url,data:rK(rK({},rY(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,rv(rb(ry(n.sent())).pipeThrough(new rd({transform:function(e,t){var n=e.choices.map(function(e){var t=Object.assign(e.delta,{role:"assistant"});return(null==r&&(r=r_(t)),r)?rK(rK({},e),{finish_reason:"tool_calls",delta:t}):rK(rK({},e),{delta:t})}),o=rK(rK({},e),{choices:n});t.enqueue(rK(rK({},o),{rawResponse:e}))}})))]}})})},e}(),rZ=function(){return(rZ=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},r0=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},r1=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}};function r2(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tools,i=e.top_p;return rZ(rZ({},e),{messages:t,model:r,tools:o,top_p:i,temperature:n})}var r3=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="01-ai/v1/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return r0(this,void 0,void 0,function(){var r;return r1(this,function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:rZ(rZ({},r2(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,rZ(rZ({},r),{rawResponse:r})]}})})},e.prototype.doStream=function(e,t){return r0(this,void 0,void 0,function(){var r;return r1(this,function(n){switch(n.label){case 0:return r=null,[4,this.req({url:this.url,data:rZ(rZ({},r2(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,rv(rb(ry(n.sent())).pipeThrough(new rd({transform:function(e,t){if((null===(i=null===(o=null===(n=null==e?void 0:e.choices)||void 0===n?void 0:n[0])||void 0===o?void 0:o.delta)||void 0===i?void 0:i.content)||(null===(u=null===(s=null===(a=null==e?void 0:e.choices)||void 0===a?void 0:a[0])||void 0===s?void 0:s.delta)||void 0===u?void 0:u.tool_calls)){var n,o,i,a,s,u,c=e.choices.map(function(e){var t=Object.assign(e.delta,{role:"assistant"});return(null==r&&(r=r_(t)),r)?rZ(rZ({},e),{finish_reason:"tool_calls",delta:t}):rZ(rZ({},e),{delta:t})}),l=rZ(rZ({},e),{choices:c});t.enqueue(rZ(rZ({},l),{rawResponse:e}))}}})))]}})})},e}(),r4=function(){return(r4=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},r6=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},r5=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}};function r7(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tools,i=e.top_p;return r4(r4({},e),{messages:t,model:r,tools:o,top_p:i,temperature:n})}var r8=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="moonshot/v1/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return r6(this,void 0,void 0,function(){var r;return r5(this,function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:r4(r4({},r7(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,r4(r4({},r),{rawResponse:r})]}})})},e.prototype.doStream=function(e,t){return r6(this,void 0,void 0,function(){var r;return r5(this,function(n){switch(n.label){case 0:return r=null,[4,this.req({url:this.url,data:r4(r4({},r7(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,rv(rb(ry(n.sent())).pipeThrough(new rd({transform:function(e,t){var n=e.choices.map(function(e){var t=e.delta;return(null==r&&(r=r_(t)),r)?r4(r4({},e),{finish_reason:"tool_calls",delta:t}):e}),o=r4(r4({},e),{choices:n});t.enqueue(r4(r4({},o),{rawResponse:e}))}})))]}})})},e}(),r9=function(){return(r9=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},ne=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},nt=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},nr=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="hunyuan-exp/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e){return ne(this,void 0,void 0,function(){var t;return nt(this,function(r){switch(r.label){case 0:return[4,this.req({url:this.url,data:r9(r9({},rq(e)),{stream:!1}),stream:!1})];case 1:return t=r.sent(),[2,r9(r9({},t),{rawResponse:t})]}})})},e.prototype.doStream=function(e){return ne(this,void 0,void 0,function(){var t;return nt(this,function(r){switch(r.label){case 0:return t=null,[4,this.req({url:this.url,data:r9(r9({},rq(e)),{stream:!0}),stream:!0})];case 1:return[2,rv(rb(ry(r.sent())).pipeThrough(new rd({transform:function(e,r){var n=e.choices.map(function(e){var r=e.delta;return(null==t&&(t=r_(r)),t)?r9(r9({},e),{finish_reason:"tool_calls",delta:r}):e}),o=r9(r9({},e),{choices:n});r.enqueue(r9(r9({},o),{rawResponse:e}))}})))]}})})},e}(),nn=function(){return(nn=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},no=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},ni=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},na=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="hunyuan-open/v1/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return no(this,void 0,void 0,function(){var r;return ni(this,function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:nn(nn({},rq(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,nn(nn({},r),{rawResponse:r})]}})})},e.prototype.doStream=function(e,t){return no(this,void 0,void 0,function(){var r;return ni(this,function(n){switch(n.label){case 0:return r=null,[4,this.req({url:this.url,data:nn(nn({},rq(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,rv(rb(ry(n.sent())).pipeThrough(new rd({transform:function(e,t){var n=e.choices.map(function(e){var t=e.delta;return(null==r&&(r=r_(t)),r)?nn(nn({},e),{finish_reason:"tool_calls",delta:t}):e}),o=nn(nn({},e),{choices:n});t.enqueue(nn(nn({},o),{rawResponse:e}))}})))]}})})},e}(),ns=function(){return(ns=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},nu=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},nc=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},nl=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="deepseek/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return nu(this,void 0,void 0,function(){var r;return nc(this,function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:ns(ns({},e),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,ns(ns({},r),{rawResponse:r})]}})})},e.prototype.doStream=function(e,t){return nu(this,void 0,void 0,function(){var r;return nc(this,function(n){switch(n.label){case 0:return r=null,[4,this.req({url:this.url,data:ns(ns({},e),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,rv(rb(ry(n.sent())).pipeThrough(new rd({transform:function(e,t){var n=e.choices.map(function(e){var t=e.delta;return(null==r&&(r=r_(t)),r)?ns(ns({},e),{finish_reason:"tool_calls",delta:t}):e}),o=ns(ns({},e),{choices:n});t.enqueue(ns(ns({},o),{rawResponse:e}))}})))]}})})},e}(),nf=function(){return(nf=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},nh=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},nd=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},np=function(){function e(e,t,r){void 0===r&&(r=""),this.req=e,this.baseUrl=t,this.subUrl=r}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e){return nh(this,void 0,void 0,function(){var t;return nd(this,function(r){switch(r.label){case 0:return[4,this.req({url:this.url,data:nf(nf({},e),{stream:!1}),stream:!1})];case 1:return t=r.sent(),[2,nf(nf({},t),{rawResponse:t})]}})})},e.prototype.doStream=function(e){return nh(this,void 0,void 0,function(){var t;return nd(this,function(r){switch(r.label){case 0:return t=null,[4,this.req({url:this.url,data:nf(nf({},e),{stream:!0}),stream:!0})];case 1:return[2,rv(rb(ry(r.sent())).pipeThrough(new rd({transform:function(e,r){var n=e.choices.map(function(e){var r=e.delta;return(null==t&&(t=r_(r)),t)?nf(nf({},e),{finish_reason:"tool_calls",delta:r}):e}),o=nf(nf({},e),{choices:n});r.enqueue(nf(nf({},o),{rawResponse:e}))}})))]}})})},e}(),ny=function(){return(ny=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},nm=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},nv=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},nb=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r},ng=function(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e="function"==typeof __values?__values(e):e[Symbol.iterator](),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise(function(n,o){!function(e,t,r,n){Promise.resolve(n).then(function(t){e({value:t,done:r})},t)}(n,o,(t=e[r](t)).done,t.value)})}}},n_=function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};function nw(e){var t,r=e.onStepFinish,n=e.abortSignal,o=e.maxSteps,i=e.topP,a=e.toolChoice,s=nb(e,["onStepFinish","abortSignal","maxSteps","topP","toolChoice"]);if(null!=o&&o<1)throw Error("`maxSteps` muse be greater than 0.");return[{onStepFinish:r,abortSignal:n,maxSteps:o},ny(ny({},s),{tools:null===(t=s.tools)||void 0===t?void 0:t.map(function(e){return"fn"in e?{type:"function",function:{description:e.description,name:e.name,parameters:e.parameters}}:e}),top_p:null!=i?i:s.top_p,tool_choice:null!=a?a:s.tool_choice})]}var nS=function(){function e(e){this.model=e}return e.prototype.generateText=function(e,t){var r,n;return nm(this,void 0,void 0,function(){var o,i,a,s,u,c,l,f,h,d,p,y,m,v,b,g,_,w,S,E,T,I=this;return nv(this,function(O){switch(O.label){case 0:return o=[],i={completion_tokens:0,prompt_tokens:0,total_tokens:0},u=(s=(a=nw(e))[0]).onStepFinish,l=void 0===(c=s.maxSteps)?10:c,f=a[1],[4,(h=function(){return I.model.doGenerate(f,t)})()];case 1:d=O.sent(),p=1,d.rawResponse&&o.push(d.rawResponse),y=null,O.label=2;case 2:if(!(p<l&&null!=(y=nE(d))))return[3,9];m=nR(d.usage),nA(i,m),O.label=3;case 3:return O.trys.push([3,7,,8]),[4,nO(y)];case 4:return v=O.sent(),b=d.choices[0],[4,null==u?void 0:u({finishReason:b.finish_reason,messages:f.messages.slice(),text:b.message.content,toolCall:y,toolResult:v,stepUsage:m,totalUsage:Object.assign({},i)})];case 5:return O.sent(),nT(f.messages,b.message,v),[4,h()];case 6:return(d=O.sent()).rawResponse&&o.push(d.rawResponse),p+=1,[3,8];case 7:return g=O.sent(),[2,{text:"",messages:f.messages,usage:i,error:g,rawResponses:o}];case 8:return[3,2];case 9:return S=null!==(n=null==(w=null==(_=null===(r=null==d?void 0:d.choices)||void 0===r?void 0:r[0])?void 0:_.message)?void 0:w.content)&&void 0!==n?n:"",E=w?n_(n_([],f.messages,!0),[w],!1):f.messages,T=nR(d.usage),nA(i,T),[4,null==u?void 0:u({finishReason:_.finish_reason,messages:E.slice(),text:S,toolCall:nE(d),toolResult:null,stepUsage:T,totalUsage:Object.assign({},i)})];case 10:return O.sent(),[2,{text:S,messages:E,usage:i,rawResponses:o}]}})})},e.prototype.streamText=function(e,t){var r;return nm(this,void 0,void 0,function(){var n,o,i,a,s,u,c,l,f,h,d,p,y,m,v,b,g,_,w,S,E,T,I,O,R,A,P,C,N,x,q,j,k,D,L,U=this;return nv(this,function(M){switch(M.label){case 0:return n={completion_tokens:0,prompt_tokens:0,total_tokens:0},a=(i=(o=nw(e))[0]).onStepFinish,u=void 0===(s=i.maxSteps)?10:s,c=o[1],[4,(l=function(){return U.model.doStream(c,t)})()];case 1:f=M.sent(),h=1,d=null,p=function(){var e=f.tee(),t=e[0],r=e[1];return f=rv(t),function(e){var t,r,n,o,i,a,s,u,c,l;return nm(this,void 0,void 0,function(){var f,h,d,p,y,m,v,b,g,_,w,S;return nv(this,function(E){switch(E.label){case 0:f={completion_tokens:0,prompt_tokens:0,total_tokens:0},h=rv(e),p={role:"assistant",content:"",tool_calls:[d={id:"",function:{name:"",arguments:""},type:""}]},E.label=1;case 1:E.trys.push([1,6,7,12]),y=!0,m=ng(h),E.label=2;case 2:return[4,m.next()];case 3:if(t=(v=E.sent()).done)return[3,5];o=v.value,y=!1;try{if(!(g=null==(b=o)?void 0:b.choices[0])||(_=g.finish_reason,w=g.delta,"tool_calls"!==_))return[2,null];if(!w||(w.content&&(p.content+=w.content),!("tool_calls"in w)))return[3,4];(null==(S=null===(i=null==w?void 0:w.tool_calls)||void 0===i?void 0:i[0])?void 0:S.id)&&(d.id=S.id),(null==S?void 0:S.type)&&(d.type=S.type),(null===(a=null==S?void 0:S.function)||void 0===a?void 0:a.name)&&(d.function.name=S.function.name),(null===(s=null==S?void 0:S.function)||void 0===s?void 0:s.arguments)&&(d.function.arguments+=S.function.arguments),(null===(u=null==b?void 0:b.usage)||void 0===u?void 0:u.completion_tokens)&&(f.completion_tokens=b.usage.completion_tokens),(null===(c=null==b?void 0:b.usage)||void 0===c?void 0:c.prompt_tokens)&&(f.prompt_tokens=b.usage.prompt_tokens),(null===(l=null==b?void 0:b.usage)||void 0===l?void 0:l.total_tokens)&&(f.total_tokens=b.usage.total_tokens)}finally{y=!0}E.label=4;case 4:return[3,2];case 5:return[3,12];case 6:return r={error:E.sent()},[3,12];case 7:if(E.trys.push([7,,10,11]),!(!y&&!t&&(n=m.return)))return[3,9];return[4,n.call(m)];case 8:E.sent(),E.label=9;case 9:return[3,11];case 10:if(r)throw r.error;return[7];case 11:return[7];case 12:return[2,{message:p,usage:f}]}})})}(r)},M.label=2;case 2:if(!(y=h<u))return[3,4];return[4,p()];case 3:y=null!=(d=M.sent()),M.label=4;case 4:if(!y)return[3,11];m=d.message,v=d.usage,nA(n,v),b=null===(r=m.tool_calls)||void 0===r?void 0:r[0],M.label=5;case 5:return M.trys.push([5,9,,10]),[4,nO(b)];case 6:return g=M.sent(),[4,null==a?void 0:a({finishReason:"tool_calls",messages:c.messages.slice(),text:m.content,toolCall:b,toolResult:g,stepUsage:v,totalUsage:Object.assign({},n)})];case 7:return M.sent(),nT(c.messages,m,g),[4,l()];case 8:return f=M.sent(),[3,10];case 9:return _=M.sent(),S=(w=f.tee())[0],E=w[1],[2,{messages:Promise.resolve(c.messages),dataStream:rv(S),textStream:rv(E.pipeThrough(new rd({transform:function(e,t){var r,n,o,i=null===(o=null===(n=null===(r=null==e?void 0:e.choices)||void 0===r?void 0:r[0])||void 0===n?void 0:n.delta)||void 0===o?void 0:o.content;"string"==typeof i&&t.enqueue(i)}}))),usage:Promise.resolve(n),error:_}];case 10:return[3,2];case 11:return[4,p()];case 12:if(d=M.sent())return T=d.message,I=d.usage,nA(n,I),O=n_(n_([],c.messages,!0),[T],!1),a({messages:O.slice(),finishReason:"tool_call",stepUsage:I,text:T.content,toolCall:T.tool_calls[0],totalUsage:Object.assign({},n)}),A=(R=f.tee())[0],P=R[1],[2,{messages:Promise.resolve(n_(n_([],c.messages,!0),[T],!1)),dataStream:rv(A),textStream:rv(P.pipeThrough(new rd({transform:function(e,t){var r,n,o,i=null===(o=null===(n=null===(r=null==e?void 0:e.choices)||void 0===r?void 0:r[0])||void 0===n?void 0:n.delta)||void 0===o?void 0:o.content;"string"==typeof i&&t.enqueue(i)}}))),usage:Promise.resolve(n)}];return C=rg(),N=rg(),x={role:"assistant",content:""},q="",j={completion_tokens:0,prompt_tokens:0,total_tokens:0},D=(k=f.pipeThrough(new rd({transform:function(e,t){var r,n,o,i,a,s,u,c,l=null===(o=null===(n=null===(r=null==e?void 0:e.choices)||void 0===r?void 0:r[0])||void 0===n?void 0:n.delta)||void 0===o?void 0:o.content;"string"==typeof l&&(x.content+=l);var f=null===(a=null===(i=null==e?void 0:e.choices)||void 0===i?void 0:i[0])||void 0===a?void 0:a.finish_reason;f&&(q=f),(null===(s=null==e?void 0:e.usage)||void 0===s?void 0:s.completion_tokens)&&(j.completion_tokens=e.usage.completion_tokens),(null===(u=null==e?void 0:e.usage)||void 0===u?void 0:u.prompt_tokens)&&(j.prompt_tokens=e.usage.prompt_tokens),(null===(c=null==e?void 0:e.usage)||void 0===c?void 0:c.total_tokens)&&(j.total_tokens=e.usage.total_tokens),t.enqueue(e)},flush:function(){C.res(n_(n_([],c.messages,!0),[x],!1)),nA(n,j),N.res(Object.assign({},n)),null==a||a({messages:n_(n_([],c.messages,!0),[x],!1),finishReason:q,text:x.content,stepUsage:j,totalUsage:Object.assign({},n)})}})).tee())[0],L=k[1],[2,{messages:C.promise,dataStream:rv(D),textStream:rv(L.pipeThrough(new rd({transform:function(e,t){var r,n,o,i=null===(o=null===(n=null===(r=null==e?void 0:e.choices)||void 0===r?void 0:r[0])||void 0===n?void 0:n.delta)||void 0===o?void 0:o.content;"string"==typeof i&&t.enqueue(i)}}))),usage:N.promise}]}})})},e}();function nE(e){var t,r=null===(t=null==e?void 0:e.choices)||void 0===t?void 0:t[0];if(!r)return null;var n=r.finish_reason,o=r.message;return"tool_calls"===n&&o&&r_(o)?o.tool_calls[0]:null}function nT(e,t,r){e.push(t,{role:"tool",tool_call_id:t.tool_calls[0].id,content:JSON.stringify(r)})}var nI=new Map;function nO(e){return nI.get(e.function.name)(JSON.parse(e.function.arguments))}function nR(e){var t,r,n;return{completion_tokens:null!==(t=null==e?void 0:e.completion_tokens)&&void 0!==t?t:0,prompt_tokens:null!==(r=null==e?void 0:e.prompt_tokens)&&void 0!==r?r:0,total_tokens:null!==(n=null==e?void 0:e.total_tokens)&&void 0!==n?n:0}}function nA(e,t){e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens}var nP={hunyuan:rB,"hunyuan-beta":rU,ark:rz,dashscope:rX,"01-ai":r3,moonshot:r8,zhipu:rN,"hunyuan-exp":nr,"hunyuan-open":na,deepseek:nl},nC=function(){return(nC=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},nN=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},nx=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},nq=function(){function e(e,t){var r=this;this.req=e,this.baseUrl=t,this.modelRequest=function(e){var t=e.url,n=e.data,o=e.headers,i=e.stream,a=e.timeout;return nN(r,void 0,void 0,function(){var e,r;return nx(this,function(s){switch(s.label){case 0:return e={"Content-Type":"application/json"},i&&Object.assign(e,{Accept:"text/event-stream"}),[4,this.req.fetch({method:"post",headers:nC(nC({},e),o),body:JSON.stringify(n),url:t,stream:i,timeout:a})];case 1:return[2,nk((r=s.sent()).data,r.header)]}})})},this.botRequest=function(e){var t=e.method,n=e.url,o=e.data,i=void 0===o?{}:o,a=e.headers,s=e.stream,u=e.timeout;return nN(r,void 0,void 0,function(){var e,r,o;return nx(this,function(c){switch(c.label){case 0:if("get"!==t)return[3,2];return e=nk,[4,this.req.fetch({url:"".concat(n,"?").concat(Object.entries(i).map(function(e){var t=e[0],r=e[1];return"".concat(t,"=").concat(r)}).join("&")),method:t,headers:a,stream:s,timeout:u})];case 1:return[2,e.apply(void 0,[c.sent().data])];case 2:return r={"Content-Type":"application/json"},s&&Object.assign(r,{Accept:"text/event-stream"}),[4,this.req.fetch({url:n,body:JSON.stringify(i),headers:nC(nC({},r),a),stream:s,method:t,timeout:u})];case 3:return[2,nk((o=c.sent()).data,o.header)]}})})},this.aiBaseUrl="".concat(t,"/ai"),this.aiBotBaseUrl="".concat(t,"/aibot"),this.bot=new rI(this.botRequest,this.aiBotBaseUrl)}return e.prototype.createModel=function(e,t){var r,n=nP[e];if(n)r=new n(this.modelRequest,this.aiBaseUrl);else{var o="string"==typeof(null==t?void 0:t.defaultModelSubUrl)?t.defaultModelSubUrl:"/chat/completions";r=new np(this.modelRequest,this.aiBaseUrl,"".concat(e).concat(o))}return new nS(r)},e.prototype.registerModel=function(e,t){if(null!=nP[e]){console.warn("AI model ".concat(e," already exists!"));return}nP[e]=t},e.prototype.registerFunctionTool=function(e){nI.has(e.name)&&console.warn("AI function tool ".concat(e.name," already exists and will be overwritten!")),nI.set(e.name,e.fn)},e}(),nj="请检查调用方式，或前往云开发 AI+ 首页查看文档：https://tcb.cloud.tencent.com/dev#/ai";function nk(e,t){var r,n;return nN(this,void 0,void 0,function(){var o;return nx(this,function(i){switch(i.label){case 0:if(!("object"==typeof e&&e&&"then"in e))return[3,2];return[4,e];case 1:if("object"==typeof(o=i.sent())&&o&&"code"in o&&"NORMAL"!==o.code)throw Error("AI+ 请求出错，错误码：".concat(o.code,"，错误信息：").concat(o.message,"\n").concat(nj,"\n").concat(JSON.stringify(o,null,2)));return[2,e];case 2:if(!(null===(n=null===(r=null==t?void 0:t.get)||void 0===r?void 0:r.call(t,"content-type"))||void 0===n?void 0:n.includes("application/json")))return[3,4];return[4,function(e){var t,r,n,o;return rc(this,void 0,void 0,function(){var i,a,s,u,c,l;return rl(this,function(f){switch(f.label){case 0:i=rv(ry(e).pipeThrough(new rm)),a="",f.label=1;case 1:f.trys.push([1,6,7,12]),s=!0,u=rf(i),f.label=2;case 2:return[4,u.next()];case 3:if(t=(c=f.sent()).done)return[3,5];o=c.value,s=!1;try{l=o,a+=l}finally{s=!0}f.label=4;case 4:return[3,2];case 5:return[3,12];case 6:return r={error:f.sent()},[3,12];case 7:if(f.trys.push([7,,10,11]),!(!s&&!t&&(n=u.return)))return[3,9];return[4,n.call(u)];case 8:f.sent(),f.label=9;case 9:return[3,11];case 10:if(r)throw r.error;return[7];case 11:return[7];case 12:return[2,JSON.parse(a)]}})})}(e)];case 3:if("object"==typeof(o=i.sent())&&o&&"code"in o&&"NORMAL"!==o.code)throw Error("AI+ 请求出错，错误码：".concat(o.code,"，错误信息：").concat(o.message,"\n").concat(nj,"\n").concat(JSON.stringify(o,null,2)));i.label=4;case 4:return[2,e]}})})}var nD=function(){return(nD=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},nL=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}},nU=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};nU(k,["MODELS"]);var nM=function(e){var t=e.getAccessToken,r=e.req;return{download:r.download,post:r.post,upload:r.upload,fetch:function(e){var n,o,i,a;return n=void 0,o=void 0,i=void 0,a=function(){var n,o,i,a,s;return nL(this,function(u){switch(u.label){case 0:if("function"!=typeof r.fetch)throw Error("req.fetch is not a function");if(n=e.token,i=void 0===(o=e.headers)?{}:o,a=nU(e,["token","headers"]),!(null!=n))return[3,1];return s=n,[3,3];case 1:return[4,t()];case 2:s=u.sent().accessToken,u.label=3;case 3:return[2,r.fetch(nD({headers:nD({Authorization:"Bearer ".concat(s)},i)},a))]}})},new(i||(i=Promise))(function(e,t){function r(e){try{u(a.next(e))}catch(e){t(e)}}function s(e){try{u(a.throw(e))}catch(e){t(e)}}function u(t){var n;t.done?e(t.value):((n=t.value)instanceof i?n:new i(function(e){e(n)})).then(r,s)}u((a=a.apply(n,o||[])).next())})}}},nW={name:"ai",entity:{ai:function(e){var t,r,n,o,i,a,s,u,c,l,f=this.request;if(null==f.fetch)throw Error("cloudbase.request.fetch() unimplemented!");return i=(o={req:f,baseUrl:null!==(l=null==e?void 0:e.baseUrl)&&void 0!==l?l:(r=(t=this.getEndPointWithKey("GATEWAY")).BASE_URL,n=t.PROTOCOL,"".concat(n).concat(r)),handleReqInstance:function(e){return e.req}}).env,a=o.baseUrl,s=o.req,u=o.getAccessToken,c=o.handleReqInstance,new nq(function(){if(null==c){if(null==u)throw Error("`getAccessToken` is required when `handleReqInstance` is not provided!");return nM({req:s,getAccessToken:u})}return c({req:s})}(),function(){if(null!=a)return a;if(null==i)throw Error("`env` is required when `baseUrl` is not provided!");return"https://".concat(i,".api.tcloudbasegateway.com/v1")}())}}};(d=P||(P={})).DocIDError="文档ID不合法",d.CollNameError="集合名称不合法",d.OpStrError="操作符不合法",d.DirectionError="排序字符不合法",d.IntergerError="must be integer",d.QueryParamTypeError="查询参数必须为对象",d.QueryParamValueError="查询参数对象值不能均为undefined";var nF={Number:"Number",Object:"Object",Array:"Array",GeoPoint:"GeoPoint",GeoLineString:"GeoLineString",GeoPolygon:"GeoPolygon",GeoMultiPoint:"GeoMultiPoint",GeoMultiLineString:"GeoMultiLineString",GeoMultiPolygon:"GeoMultiPolygon",Timestamp:"Date",ServerDate:"ServerDate",BsonDate:"BsonDate"},nB=["desc","asc"],nG=["<","<=","==",">=",">"];(p=C||(C={})).lt="<",p.gt=">",p.lte="<=",p.gte=">=",p.eq="==",(A={})[C.eq]="$eq",A[C.lt]="$lt",A[C.lte]="$lte",A[C.gt]="$gt",A[C.gte]="$gte",(y=N||(N={})).WHERE="WHERE",y.DOC="DOC";var nV=(m=function(e,t){return(m=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}m(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),nH=[],n$={},nz=function(e){function t(t,r){if(r!==n$)throw TypeError("InternalSymbol cannot be constructed with new operator");return e.call(this,t)||this}return nV(t,e),t.for=function(e){for(var r=0,n=nH.length;r<n;r++)if(nH[r].target===e)return nH[r].instance;var o=new t(e,n$);return nH.push({target:e,instance:o}),o},t}(function(e){Object.defineProperties(this,{target:{enumerable:!1,writable:!1,configurable:!1,value:e}})}),nK=nz.for("UNSET_FIELD_NAME"),nJ=nz.for("UPDATE_COMMAND"),nQ=nz.for("QUERY_COMMAND"),nY=nz.for("LOGIC_COMMAND"),nX=nz.for("GEO_POINT"),nZ=nz.for("SYMBOL_GEO_LINE_STRING"),n0=nz.for("SYMBOL_GEO_POLYGON"),n1=nz.for("SYMBOL_GEO_MULTI_POINT"),n2=nz.for("SYMBOL_GEO_MULTI_LINE_STRING"),n3=nz.for("SYMBOL_GEO_MULTI_POLYGON"),n4=nz.for("SERVER_DATE"),n6=nz.for("REGEXP"),n5=function(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()},n7=function(e){return"object"===n5(e)},n8=function(e){return"number"===n5(e)},n9=function(e){return Array.isArray(e)},oe=function(e){return"date"===n5(e)},ot=function(e){return"regexp"===n5(e)},or=function(e){return e&&e._internalType instanceof nz},on=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},oo=function(){function e(e){if(!n9(e))throw TypeError('"points" must be of type Point[]. Received type '+typeof e);if(e.length<2)throw Error('"points" must contain 2 points at least');e.forEach(function(e){if(!(e instanceof ob))throw TypeError('"points" must be of type Point[]. Received type '+typeof e+"[]")}),this.points=e}return e.prototype.parse=function(e){var t;return(t={})[e]={type:"LineString",coordinates:this.points.map(function(e){return e.toJSON().coordinates})},t},e.prototype.toJSON=function(){return{type:"LineString",coordinates:this.points.map(function(e){return e.toJSON().coordinates})}},e.validate=function(e){var t,r;if("LineString"!==e.type||!n9(e.coordinates))return!1;try{for(var n=on(e.coordinates),o=n.next();!o.done;o=n.next()){var i=o.value;if(!n8(i[0])||!n8(i[1]))return!1}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}return!0},e.isClosed=function(e){var t=e.points[0],r=e.points[e.points.length-1];if(t.latitude===r.latitude&&t.longitude===r.longitude)return!0},Object.defineProperty(e.prototype,"_internalType",{get:function(){return nZ},enumerable:!0,configurable:!0}),e}(),oi=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},oa=function(){function e(e){if(!n9(e))throw TypeError('"lines" must be of type LineString[]. Received type '+typeof e);if(0===e.length)throw Error("Polygon must contain 1 linestring at least");e.forEach(function(e){if(!(e instanceof oo))throw TypeError('"lines" must be of type LineString[]. Received type '+typeof e+"[]");if(!oo.isClosed(e))throw Error("LineString "+e.points.map(function(e){return e.toReadableString()})+" is not a closed cycle")}),this.lines=e}return e.prototype.parse=function(e){var t;return(t={})[e]={type:"Polygon",coordinates:this.lines.map(function(e){return e.points.map(function(e){return[e.longitude,e.latitude]})})},t},e.prototype.toJSON=function(){return{type:"Polygon",coordinates:this.lines.map(function(e){return e.points.map(function(e){return[e.longitude,e.latitude]})})}},e.validate=function(e){var t,r,n,o;if("Polygon"!==e.type||!n9(e.coordinates))return!1;try{for(var i=oi(e.coordinates),a=i.next();!a.done;a=i.next()){var s=a.value;if(!this.isCloseLineString(s))return!1;try{for(var u=(n=void 0,oi(s)),c=u.next();!c.done;c=u.next()){var l=c.value;if(!n8(l[0])||!n8(l[1]))return!1}}catch(e){n={error:e}}finally{try{c&&!c.done&&(o=u.return)&&o.call(u)}finally{if(n)throw n.error}}}}catch(e){t={error:e}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}return!0},e.isCloseLineString=function(e){var t=e[0],r=e[e.length-1];return t[0]===r[0]&&t[1]===r[1]},Object.defineProperty(e.prototype,"_internalType",{get:function(){return n3},enumerable:!0,configurable:!0}),e}(),os=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},ou=function(){function e(e){if(!n9(e))throw TypeError('"points" must be of type Point[]. Received type '+typeof e);if(0===e.length)throw Error('"points" must contain 1 point at least');e.forEach(function(e){if(!(e instanceof ob))throw TypeError('"points" must be of type Point[]. Received type '+typeof e+"[]")}),this.points=e}return e.prototype.parse=function(e){var t;return(t={})[e]={type:"MultiPoint",coordinates:this.points.map(function(e){return e.toJSON().coordinates})},t},e.prototype.toJSON=function(){return{type:"MultiPoint",coordinates:this.points.map(function(e){return e.toJSON().coordinates})}},e.validate=function(e){var t,r;if("MultiPoint"!==e.type||!n9(e.coordinates))return!1;try{for(var n=os(e.coordinates),o=n.next();!o.done;o=n.next()){var i=o.value;if(!n8(i[0])||!n8(i[1]))return!1}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}return!0},Object.defineProperty(e.prototype,"_internalType",{get:function(){return n1},enumerable:!0,configurable:!0}),e}(),oc=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},ol=function(){function e(e){if(!n9(e))throw TypeError('"lines" must be of type LineString[]. Received type '+typeof e);if(0===e.length)throw Error("Polygon must contain 1 linestring at least");e.forEach(function(e){if(!(e instanceof oo))throw TypeError('"lines" must be of type LineString[]. Received type '+typeof e+"[]")}),this.lines=e}return e.prototype.parse=function(e){var t;return(t={})[e]={type:"MultiLineString",coordinates:this.lines.map(function(e){return e.points.map(function(e){return[e.longitude,e.latitude]})})},t},e.prototype.toJSON=function(){return{type:"MultiLineString",coordinates:this.lines.map(function(e){return e.points.map(function(e){return[e.longitude,e.latitude]})})}},e.validate=function(e){var t,r,n,o;if("MultiLineString"!==e.type||!n9(e.coordinates))return!1;try{for(var i=oc(e.coordinates),a=i.next();!a.done;a=i.next()){var s=a.value;try{for(var u=(n=void 0,oc(s)),c=u.next();!c.done;c=u.next()){var l=c.value;if(!n8(l[0])||!n8(l[1]))return!1}}catch(e){n={error:e}}finally{try{c&&!c.done&&(o=u.return)&&o.call(u)}finally{if(n)throw n.error}}}}catch(e){t={error:e}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}return!0},Object.defineProperty(e.prototype,"_internalType",{get:function(){return n2},enumerable:!0,configurable:!0}),e}(),of=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},oh=function(){function e(e){var t,r;if(!n9(e))throw TypeError('"polygons" must be of type Polygon[]. Received type '+typeof e);if(0===e.length)throw Error("MultiPolygon must contain 1 polygon at least");try{for(var n=of(e),o=n.next();!o.done;o=n.next()){var i=o.value;if(!(i instanceof oa))throw TypeError('"polygon" must be of type Polygon[]. Received type '+typeof i+"[]")}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}this.polygons=e}return e.prototype.parse=function(e){var t;return(t={})[e]={type:"MultiPolygon",coordinates:this.polygons.map(function(e){return e.lines.map(function(e){return e.points.map(function(e){return[e.longitude,e.latitude]})})})},t},e.prototype.toJSON=function(){return{type:"MultiPolygon",coordinates:this.polygons.map(function(e){return e.lines.map(function(e){return e.points.map(function(e){return[e.longitude,e.latitude]})})})}},e.validate=function(e){var t,r,n,o,i,a;if("MultiPolygon"!==e.type||!n9(e.coordinates))return!1;try{for(var s=of(e.coordinates),u=s.next();!u.done;u=s.next()){var c=u.value;try{for(var l=(n=void 0,of(c)),f=l.next();!f.done;f=l.next()){var h=f.value;try{for(var d=(i=void 0,of(h)),p=d.next();!p.done;p=d.next()){var y=p.value;if(!n8(y[0])||!n8(y[1]))return!1}}catch(e){i={error:e}}finally{try{p&&!p.done&&(a=d.return)&&a.call(d)}finally{if(i)throw i.error}}}}catch(e){n={error:e}}finally{try{f&&!f.done&&(o=l.return)&&o.call(l)}finally{if(n)throw n.error}}}}catch(e){t={error:e}}finally{try{u&&!u.done&&(r=s.return)&&r.call(s)}finally{if(t)throw t.error}}return!0},Object.defineProperty(e.prototype,"_internalType",{get:function(){return n0},enumerable:!0,configurable:!0}),e}(),od=function(){function e(e){var t=(void 0===e?{}:e).offset;this.offset=void 0===t?0:t}return Object.defineProperty(e.prototype,"_internalType",{get:function(){return n4},enumerable:!0,configurable:!0}),e.prototype.parse=function(){return{$date:{offset:this.offset}}},e}();function op(e){return new od(e)}var oy=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},om=function(){function e(){}return e.formatResDocumentData=function(t){return t.map(function(t){return e.formatField(t)})},e.formatField=function(t){var r=Object.keys(t),n={};return Array.isArray(t)&&(n=[]),r.forEach(function(r){var o,i=t[r];switch(e.whichType(i)){case nF.GeoPoint:o=new ob(i.coordinates[0],i.coordinates[1]);break;case nF.GeoLineString:o=new oo(i.coordinates.map(function(e){return new ob(e[0],e[1])}));break;case nF.GeoPolygon:o=new oa(i.coordinates.map(function(e){return new oo(e.map(function(e){var t=oy(e,2);return new ob(t[0],t[1])}))}));break;case nF.GeoMultiPoint:o=new ou(i.coordinates.map(function(e){return new ob(e[0],e[1])}));break;case nF.GeoMultiLineString:o=new ol(i.coordinates.map(function(e){return new oo(e.map(function(e){var t=oy(e,2);return new ob(t[0],t[1])}))}));break;case nF.GeoMultiPolygon:o=new oh(i.coordinates.map(function(e){return new oa(e.map(function(e){return new oo(e.map(function(e){var t=oy(e,2);return new ob(t[0],t[1])}))}))}));break;case nF.Timestamp:o=new Date(1e3*i.$timestamp);break;case nF.Object:case nF.Array:o=e.formatField(i);break;case nF.ServerDate:o=new Date(i.$date);break;default:o=i}Array.isArray(n)?n.push(o):n[r]=o}),n},e.whichType=function(e){var t=Object.prototype.toString.call(e).slice(8,-1);if(t===nF.Timestamp)return nF.BsonDate;if(t===nF.Object){if(e instanceof ob)return nF.GeoPoint;if(e instanceof Date)return nF.Timestamp;if(e instanceof od)return nF.ServerDate;e.$timestamp?t=nF.Timestamp:e.$date?t=nF.ServerDate:ob.validate(e)?t=nF.GeoPoint:oo.validate(e)?t=nF.GeoLineString:oa.validate(e)?t=nF.GeoPolygon:ou.validate(e)?t=nF.GeoMultiPoint:ol.validate(e)?t=nF.GeoMultiLineString:oh.validate(e)&&(t=nF.GeoMultiPolygon)}return t},e.generateDocId=function(){for(var e="ABCDEFabcdef0123456789",t="",r=0;r<24;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},e}(),ov=function(){function e(){}return e.isGeopoint=function(e,t){if(om.whichType(t)!==nF.Number)throw Error("Geo Point must be number type");var r=Math.abs(t);if("latitude"===e&&r>90)throw Error("latitude should be a number ranges from -90 to 90");if("longitude"===e&&r>180)throw Error("longitude should be a number ranges from -180 to 180");return!0},e.isInteger=function(e,t){if(!Number.isInteger(t))throw Error(e+P.IntergerError);return!0},e.isFieldOrder=function(e){if(-1===nB.indexOf(e))throw Error(P.DirectionError);return!0},e.isFieldPath=function(e){if(!/^[a-zA-Z0-9-_\.]/.test(e))throw Error();return!0},e.isOperator=function(e){if(-1===nG.indexOf(e))throw Error(P.OpStrError);return!0},e.isCollName=function(e){if(!/^[a-zA-Z0-9]([a-zA-Z0-9-_]){1,32}$/.test(e))throw Error(P.CollNameError);return!0},e.isDocID=function(e){if(!/^([a-fA-F0-9]){24}$/.test(e))throw Error(P.DocIDError);return!0},e}(),ob=function(){function e(e,t){ov.isGeopoint("longitude",e),ov.isGeopoint("latitude",t),this.longitude=e,this.latitude=t}return e.prototype.parse=function(e){var t;return(t={})[e]={type:"Point",coordinates:[this.longitude,this.latitude]},t},e.prototype.toJSON=function(){return{type:"Point",coordinates:[this.longitude,this.latitude]}},e.prototype.toReadableString=function(){return"["+this.longitude+","+this.latitude+"]"},e.validate=function(e){return"Point"===e.type&&n9(e.coordinates)&&ov.isGeopoint("longitude",e.coordinates[0])&&ov.isGeopoint("latitude",e.coordinates[1])},Object.defineProperty(e.prototype,"_internalType",{get:function(){return nX},enumerable:!0,configurable:!0}),e}(),og=function(){if(!Promise){(e=function(){}).promise={};var e,t=function(){throw Error('Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.')};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}var r=new Promise(function(t,r){e=function(e,n){return e?r(e):t(n)}});return e.promise=r,e};(v=x||(x={})).SET="set",v.REMOVE="remove",v.INC="inc",v.MUL="mul",v.PUSH="push",v.PULL="pull",v.PULL_ALL="pullAll",v.POP="pop",v.SHIFT="shift",v.UNSHIFT="unshift",v.ADD_TO_SET="addToSet",v.BIT="bit",v.RENAME="rename",v.MAX="max",v.MIN="min";var o_=function(){function e(e,t,r){this._internalType=nJ,Object.defineProperties(this,{_internalType:{enumerable:!1,configurable:!1}}),this.operator=e,this.operands=t,this.fieldName=r||nK}return e.prototype._setFieldName=function(t){return new e(this.operator,this.operands,t)},e}();function ow(e){return e&&e instanceof o_&&e._internalType===nJ}(b=q||(q={})).AND="and",b.OR="or",b.NOT="not",b.NOR="nor";var oS=function(){function e(e,t,r){if(this._internalType=nY,Object.defineProperties(this,{_internalType:{enumerable:!1,configurable:!1}}),this.operator=e,this.operands=t,this.fieldName=r||nK,this.fieldName!==nK){if(Array.isArray(t)){t=t.slice(),this.operands=t;for(var n=0,o=t.length;n<o;n++){var i=t[n];(oE(i)||oO(i))&&(t[n]=i._setFieldName(this.fieldName))}}else{var i=t;(oE(i)||oO(i))&&(t=i._setFieldName(this.fieldName))}}}return e.prototype._setFieldName=function(t){var r=this.operands.map(function(r){return r instanceof e?r._setFieldName(t):r});return new e(this.operator,r,t)},e.prototype.and=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=Array.isArray(arguments[0])?arguments[0]:Array.from(arguments);return n.unshift(this),new e(q.AND,n,this.fieldName)},e.prototype.or=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=Array.isArray(arguments[0])?arguments[0]:Array.from(arguments);return n.unshift(this),new e(q.OR,n,this.fieldName)},e}();function oE(e){return e&&e instanceof oS&&e._internalType===nY}var oT=(g=function(e,t){return(g=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}g(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});(_=j||(j={})).EQ="eq",_.NEQ="neq",_.GT="gt",_.GTE="gte",_.LT="lt",_.LTE="lte",_.IN="in",_.NIN="nin",_.ALL="all",_.ELEM_MATCH="elemMatch",_.EXISTS="exists",_.SIZE="size",_.MOD="mod",_.GEO_NEAR="geoNear",_.GEO_WITHIN="geoWithin",_.GEO_INTERSECTS="geoIntersects";var oI=function(e){function t(t,r,n){var o=e.call(this,t,r,n)||this;return o.operator=t,o._internalType=nQ,o}return oT(t,e),t.prototype.toJSON=function(){var e,t;switch(this.operator){case j.IN:case j.NIN:return(e={})["$"+this.operator]=this.operands,e;default:return(t={})["$"+this.operator]=this.operands[0],t}},t.prototype._setFieldName=function(e){return new t(this.operator,this.operands,e)},t.prototype.eq=function(e){var r=new t(j.EQ,[e],this.fieldName);return this.and(r)},t.prototype.neq=function(e){var r=new t(j.NEQ,[e],this.fieldName);return this.and(r)},t.prototype.gt=function(e){var r=new t(j.GT,[e],this.fieldName);return this.and(r)},t.prototype.gte=function(e){var r=new t(j.GTE,[e],this.fieldName);return this.and(r)},t.prototype.lt=function(e){var r=new t(j.LT,[e],this.fieldName);return this.and(r)},t.prototype.lte=function(e){var r=new t(j.LTE,[e],this.fieldName);return this.and(r)},t.prototype.in=function(e){var r=new t(j.IN,e,this.fieldName);return this.and(r)},t.prototype.nin=function(e){var r=new t(j.NIN,e,this.fieldName);return this.and(r)},t.prototype.geoNear=function(e){if(!(e.geometry instanceof ob))throw TypeError('"geometry" must be of type Point. Received type '+typeof e.geometry);if(void 0!==e.maxDistance&&!n8(e.maxDistance))throw TypeError('"maxDistance" must be of type Number. Received type '+typeof e.maxDistance);if(void 0!==e.minDistance&&!n8(e.minDistance))throw TypeError('"minDistance" must be of type Number. Received type '+typeof e.minDistance);var r=new t(j.GEO_NEAR,[e],this.fieldName);return this.and(r)},t.prototype.geoWithin=function(e){if(!(e.geometry instanceof oh)&&!(e.geometry instanceof oa))throw TypeError('"geometry" must be of type Polygon or MultiPolygon. Received type '+typeof e.geometry);var r=new t(j.GEO_WITHIN,[e],this.fieldName);return this.and(r)},t.prototype.geoIntersects=function(e){if(!(e.geometry instanceof ob)&&!(e.geometry instanceof oo)&&!(e.geometry instanceof oa)&&!(e.geometry instanceof ou)&&!(e.geometry instanceof ol)&&!(e.geometry instanceof oh))throw TypeError('"geometry" must be of type Point, LineString, Polygon, MultiPoint, MultiLineString or MultiPolygon. Received type '+typeof e.geometry);var r=new t(j.GEO_INTERSECTS,[e],this.fieldName);return this.and(r)},t}(oS);function oO(e){return e&&e instanceof oI&&e._internalType===nQ}var oR={};for(var oA in j)oR[oA]="$"+oA;for(var oA in q)oR[oA]="$"+oA;for(var oA in x)oR[oA]="$"+oA;function oP(e){return oR[e]||"$"+e}oR[j.NEQ]="$ne",oR[x.REMOVE]="$unset",oR[x.SHIFT]="$pop",oR[x.UNSHIFT]="$push";var oC=function(){return(oC=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},oN=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},ox=function(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(oN(arguments[t]));return e};function oq(e){return function e(t,r){if(or(t))switch(t._internalType){case nX:return t.toJSON();case n4:case n6:return t.parse();default:return t.toJSON?t.toJSON():t}else{if(oe(t))return{$date:+t};if(ot(t))return{$regex:t.source,$options:t.flags};if(n9(t))return t.map(function(t){if(r.indexOf(t)>-1)throw Error("Cannot convert circular structure to JSON");return e(t,ox(r,[t]))});if(!n7(t))return t;var n=oC({},t);for(var o in n){if(r.indexOf(n[o])>-1)throw Error("Cannot convert circular structure to JSON");n[o]=e(n[o],ox(r,[n[o]]))}return n}}(e,[e])}var oj=function(){return(oj=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},ok=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},oD=function(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(ok(arguments[t]));return e};function oL(e){return function e(t,r,n,o){var i=oj({},t);for(var a in t)if(!/^\$/.test(a)){var s=t[a];if(s&&n7(s)&&!r(s)){if(o.indexOf(s)>-1)throw Error("Cannot convert circular structure to JSON");var u=oD(n,[a]),c=oD(o,[s]),l=e(s,r,u,c);i[a]=l;var f=!1;for(var h in l)/^\$/.test(h)?f=!0:(i[a+"."+h]=l[h],delete i[a][h]);f||delete i[a]}}return i}(e,oM,[],[e])}function oU(e,t,r){for(var n in t[r]||delete e[r],t)e[n]?n9(e[n])?e[n].push(t[n]):n7(e[n])?n7(t[n])?Object.assign(e[n],t[n]):(console.warn("unmergable condition, query is object but condition is "+n5(t)+", can only overwrite",t,r),e[n]=t[n]):(console.warn("to-merge query is of type "+n5(e)+", can only overwrite",e,t,r),e[n]=t[n]):e[n]=t[n]}function oM(e){return or(e)||oe(e)||ot(e)}function oW(e){return oq(e)}var oF=function(){function e(){}return e.encode=function(t){return new e().encodeUpdate(t)},e.prototype.encodeUpdate=function(e){return ow(e)?this.encodeUpdateCommand(e):"object"===n5(e)?this.encodeUpdateObject(e):e},e.prototype.encodeUpdateCommand=function(e){if(e.fieldName===nK)throw Error("Cannot encode a comparison command with unset field name");switch(e.operator){case x.PUSH:case x.PULL:case x.PULL_ALL:case x.POP:case x.SHIFT:case x.UNSHIFT:case x.ADD_TO_SET:return this.encodeArrayUpdateCommand(e);default:return this.encodeFieldUpdateCommand(e)}},e.prototype.encodeFieldUpdateCommand=function(e){var t,r,n,o,i=oP(e.operator);return e.operator===x.REMOVE?((t={})[i]=((r={})[e.fieldName]="",r),t):((n={})[i]=((o={})[e.fieldName]=e.operands[0],o),n)},e.prototype.encodeArrayUpdateCommand=function(e){var t,r,n,o,i,a,s,u,c,l,f=oP(e.operator);switch(e.operator){case x.PUSH:var h=void 0;return h=n9(e.operands)?{$each:e.operands.map(oW)}:e.operands,(t={})[f]=((r={})[e.fieldName]=h,r),t;case x.UNSHIFT:var h={$each:e.operands.map(oW),$position:0};return(n={})[f]=((o={})[e.fieldName]=h,o),n;case x.POP:return(i={})[f]=((a={})[e.fieldName]=1,a),i;case x.SHIFT:return(s={})[f]=((u={})[e.fieldName]=-1,u),s;default:return(c={})[f]=((l={})[e.fieldName]=oW(e.operands),l),c}},e.prototype.encodeUpdateObject=function(e){var t=oL(e);for(var r in t)if(!/^\$/.test(r)){var n=t[r];if(ow(n)){t[r]=n._setFieldName(r);var o=this.encodeUpdateCommand(t[r]);oU(t,o,r)}else{t[r]=n=oW(n);var i=new o_(x.SET,[n],r),o=this.encodeUpdateCommand(i);oU(t,o,r)}}return t},e}(),oB={};function oG(e){if(!il.wsClientClass)throw Error("to use realtime you must import realtime module first");var t=e.config.env;return oB[t]||(oB[t]=new il.wsClientClass({context:{appConfig:{docSizeLimit:1e3,realtimePingInterval:1e4,realtimePongWaitTimeout:5e3,request:new il.reqClass(e.config)}}})),oB[t]}var oV=function(){return(oV=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},oH=function(){function e(e,t,r,n){var o=this;void 0===n&&(n={}),this.watch=function(e){return oG(o._db).watch(oV(oV({},e),{envId:o._db.config.env,collectionName:o._coll,query:JSON.stringify({_id:o.id})}))},this._db=e,this._coll=t,this.id=r,this.request=new il.reqClass(this._db.config),this.projection=n}return e.prototype.create=function(e,t){t=t||og();var r={collectionName:this._coll,data:oq(e)};return this.id&&(r._id=this.id),this.request.send("database.addDocument",r).then(function(e){e.code?t(0,e):t(0,{id:e.data._id,requestId:e.requestId})}).catch(function(e){t(e)}),t.promise},e.prototype.set=function(e,t){if(t=t||og(),!this.id)return Promise.resolve({code:"INVALID_PARAM",message:"docId不能为空"});if(!e||"object"!=typeof e)return Promise.resolve({code:"INVALID_PARAM",message:"参数必需是非空对象"});if(e.hasOwnProperty("_id"))return Promise.resolve({code:"INVALID_PARAM",message:"不能更新_id的值"});var r=!1,n=function(e){if("object"==typeof e)for(var t in e)e[t]instanceof o_?r=!0:"object"==typeof e[t]&&n(e[t])};if(n(e),r)return Promise.resolve({code:"DATABASE_REQUEST_FAILED",message:"update operator complicit"});var o={collectionName:this._coll,queryType:N.DOC,data:oq(e),multi:!1,merge:!1,upsert:!0};return this.id&&(o.query={_id:this.id}),this.request.send("database.updateDocument",o).then(function(e){e.code?t(0,e):t(0,{updated:e.data.updated,upsertedId:e.data.upserted_id,requestId:e.requestId})}).catch(function(e){t(e)}),t.promise},e.prototype.update=function(e,t){if(t=t||og(),!e||"object"!=typeof e)return Promise.resolve({code:"INVALID_PARAM",message:"参数必需是非空对象"});if(e.hasOwnProperty("_id"))return Promise.resolve({code:"INVALID_PARAM",message:"不能更新_id的值"});var r={_id:this.id},n={collectionName:this._coll,data:oF.encode(e),query:r,queryType:N.DOC,multi:!1,merge:!0,upsert:!1};return this.request.send("database.updateDocument",n).then(function(e){e.code?t(0,e):t(0,{updated:e.data.updated,upsertedId:e.data.upserted_id,requestId:e.requestId})}).catch(function(e){t(e)}),t.promise},e.prototype.remove=function(e){e=e||og();var t={_id:this.id},r={collectionName:this._coll,query:t,queryType:N.DOC,multi:!1};return this.request.send("database.deleteDocument",r).then(function(t){t.code?e(0,t):e(0,{deleted:t.data.deleted,requestId:t.requestId})}).catch(function(t){e(t)}),e.promise},e.prototype.get=function(e){e=e||og();var t={_id:this.id},r={collectionName:this._coll,query:t,queryType:N.DOC,multi:!1,projection:this.projection};return this.request.send("database.queryDocument",r).then(function(t){if(t.code)e(0,t);else{var r=om.formatResDocumentData(t.data.list);e(0,{data:r,requestId:t.requestId})}}).catch(function(t){e(t)}),e.promise},e.prototype.field=function(t){for(var r in t)t[r]?t[r]=1:t[r]=0;return new e(this._db,this._coll,this.id,t)},e}(),o$=function(){function e(){}return e.encode=function(e){return new oz().encodeQuery(e)},e}(),oz=function(){function e(){}return e.prototype.encodeQuery=function(e,t){var r;return oM(e)?oE(e)?this.encodeLogicCommand(e):oO(e)?this.encodeQueryCommand(e):((r={})[t]=this.encodeQueryObject(e),r):n7(e)?this.encodeQueryObject(e):e},e.prototype.encodeRegExp=function(e){return{$regex:e.source,$options:e.flags}},e.prototype.encodeLogicCommand=function(e){var t,r,n,o,i,a,s,u=this;switch(e.operator){case q.NOR:case q.AND:case q.OR:var c=oP(e.operator),l=e.operands.map(function(t){return u.encodeQuery(t,e.fieldName)});return(t={})[c]=l,t;case q.NOT:var c=oP(e.operator),f=e.operands[0];if(ot(f))return(r={})[e.fieldName]=((n={})[c]=this.encodeRegExp(f),n),r;var l=this.encodeQuery(f)[e.fieldName];return(o={})[e.fieldName]=((i={})[c]=l,i),o;default:var c=oP(e.operator);if(1===e.operands.length){var h=this.encodeQuery(e.operands[0]);return(a={})[c]=h,a}var l=e.operands.map(this.encodeQuery.bind(this));return(s={})[c]=l,s}},e.prototype.encodeQueryCommand=function(e){return oO(e),this.encodeComparisonCommand(e)},e.prototype.encodeComparisonCommand=function(e){if(e.fieldName===nK)throw Error("Cannot encode a comparison command with unset field name");var t,r,n,o,i,a,s,u,c,l=oP(e.operator);switch(e.operator){case j.EQ:case j.NEQ:case j.LT:case j.LTE:case j.GT:case j.GTE:case j.ELEM_MATCH:case j.EXISTS:case j.SIZE:case j.MOD:return(t={})[e.fieldName]=((r={})[l]=oW(e.operands[0]),r),t;case j.IN:case j.NIN:case j.ALL:return(n={})[e.fieldName]=((o={})[l]=oW(e.operands),o),n;case j.GEO_NEAR:var f=e.operands[0];return(i={})[e.fieldName]={$nearSphere:{$geometry:f.geometry.toJSON(),$maxDistance:f.maxDistance,$minDistance:f.minDistance}},i;case j.GEO_WITHIN:var f=e.operands[0];return(a={})[e.fieldName]={$geoWithin:{$geometry:f.geometry.toJSON()}},a;case j.GEO_INTERSECTS:var f=e.operands[0];return(s={})[e.fieldName]={$geoIntersects:{$geometry:f.geometry.toJSON()}},s;default:return(u={})[e.fieldName]=((c={})[l]=oW(e.operands[0]),c),u}},e.prototype.encodeQueryObject=function(e){var t=oL(e);for(var r in t){var n=t[r];if(oE(n)){t[r]=n._setFieldName(r);var o=this.encodeLogicCommand(t[r]);this.mergeConditionAfterEncode(t,o,r)}else if(oO(n)){t[r]=n._setFieldName(r);var o=this.encodeComparisonCommand(t[r]);this.mergeConditionAfterEncode(t,o,r)}else oM(n)&&(t[r]=oW(n))}return t},e.prototype.mergeConditionAfterEncode=function(e,t,r){for(var n in t[r]||delete e[r],t)e[n]?n9(e[n])?e[n]=e[n].concat(t[n]):n7(e[n])?n7(t[n])?Object.assign(e,t):(console.warn("unmergable condition, query is object but condition is "+n5(t)+", can only overwrite",t,r),e[n]=t[n]):(console.warn("to-merge query is of type "+n5(e)+", can only overwrite",e,t,r),e[n]=t[n]):e[n]=t[n]},e}(),oK=function(){return(oK=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},oJ=function(){function e(e,t,r,n,o){var i=this;this.watch=function(e){return oG(i._db).watch(oK(oK({},e),{envId:i._db.config.env,collectionName:i._coll,query:JSON.stringify(i._fieldFilters||{}),limit:i._queryOptions.limit,orderBy:i._fieldOrders?i._fieldOrders.reduce(function(e,t){return e[t.field]=t.direction,e},{}):void 0}))},this._db=e,this._coll=t,this._fieldFilters=r,this._fieldOrders=n||[],this._queryOptions=o||{},this._request=new il.reqClass(this._db.config)}return e.prototype.get=function(e){e=e||og();var t=[];this._fieldOrders&&this._fieldOrders.forEach(function(e){t.push(e)});var r={collectionName:this._coll,queryType:N.WHERE};return this._fieldFilters&&(r.query=this._fieldFilters),t.length>0&&(r.order=t),this._queryOptions.offset&&(r.offset=this._queryOptions.offset),this._queryOptions.limit?r.limit=this._queryOptions.limit<1e3?this._queryOptions.limit:1e3:r.limit=100,this._queryOptions.projection&&(r.projection=this._queryOptions.projection),this._request.send("database.queryDocument",r).then(function(t){if(t.code)e(0,t);else{var r={data:om.formatResDocumentData(t.data.list),requestId:t.requestId};t.total&&(r.total=t.total),t.limit&&(r.limit=t.limit),t.offset&&(r.offset=t.offset),e(0,r)}}).catch(function(t){e(t)}),e.promise},e.prototype.count=function(e){e=e||og();var t={collectionName:this._coll,queryType:N.WHERE};return this._fieldFilters&&(t.query=this._fieldFilters),this._request.send("database.countDocument",t).then(function(t){t.code?e(0,t):e(0,{requestId:t.requestId,total:t.data.total})}).catch(function(t){e(t)}),e.promise},e.prototype.where=function(t){if("Object"!==Object.prototype.toString.call(t).slice(8,-1))throw Error(P.QueryParamTypeError);var r=Object.keys(t),n=r.some(function(e){return void 0!==t[e]});if(r.length&&!n)throw Error(P.QueryParamValueError);return new e(this._db,this._coll,o$.encode(t),this._fieldOrders,this._queryOptions)},e.prototype.orderBy=function(t,r){ov.isFieldPath(t),ov.isFieldOrder(r);var n=this._fieldOrders.concat({field:t,direction:r});return new e(this._db,this._coll,this._fieldFilters,n,this._queryOptions)},e.prototype.limit=function(t){ov.isInteger("limit",t);var r=oK({},this._queryOptions);return r.limit=t,new e(this._db,this._coll,this._fieldFilters,this._fieldOrders,r)},e.prototype.skip=function(t){ov.isInteger("offset",t);var r=oK({},this._queryOptions);return r.offset=t,new e(this._db,this._coll,this._fieldFilters,this._fieldOrders,r)},e.prototype.update=function(e,t){if(t=t||og(),!e||"object"!=typeof e)return Promise.resolve({code:"INVALID_PARAM",message:"参数必需是非空对象"});if(e.hasOwnProperty("_id"))return Promise.resolve({code:"INVALID_PARAM",message:"不能更新_id的值"});var r={collectionName:this._coll,query:this._fieldFilters,queryType:N.WHERE,multi:!0,merge:!0,upsert:!1,data:oF.encode(e)};return this._request.send("database.updateDocument",r).then(function(e){e.code?t(0,e):t(0,{requestId:e.requestId,updated:e.data.updated,upsertId:e.data.upsert_id})}).catch(function(e){t(e)}),t.promise},e.prototype.field=function(t){for(var r in t)t[r]?"object"!=typeof t[r]&&(t[r]=1):t[r]=0;var n=oK({},this._queryOptions);return n.projection=t,new e(this._db,this._coll,this._fieldFilters,this._fieldOrders,n)},e.prototype.remove=function(e){e=e||og(),Object.keys(this._queryOptions).length>0&&console.warn("`offset`, `limit` and `projection` are not supported in remove() operation"),this._fieldOrders.length>0&&console.warn("`orderBy` is not supported in remove() operation");var t={collectionName:this._coll,query:o$.encode(this._fieldFilters),queryType:N.WHERE,multi:!0};return this._request.send("database.deleteDocument",t).then(function(t){t.code?e(0,t):e(0,{requestId:t.requestId,deleted:t.data.deleted})}).catch(function(t){e(t)}),e.promise},e}(),oQ=r(75447),oY=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},oX=function(){function e(e,t){this._stages=[],e&&t&&(this._db=e,this._request=new il.reqClass(this._db.config),this._collectionName=t)}return e.prototype.end=function(){var e,t,r,n;return e=this,t=void 0,r=void 0,n=function(){var e;return oY(this,function(t){switch(t.label){case 0:if(!this._collectionName||!this._db)throw Error("Aggregation pipeline cannot send request");return[4,this._request.send("database.aggregate",{collectionName:this._collectionName,stages:this._stages})];case 1:if((e=t.sent())&&e.data&&e.data.list)return[2,{requestId:e.requestId,data:JSON.parse(e.data.list).map(oQ.dF.parse)}];return[2,e]}})},new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},e.prototype.unwrap=function(){return this._stages},e.prototype.done=function(){return this._stages.map(function(e){var t,r=e.stageKey,n=e.stageValue;return(t={})[r]=JSON.parse(n),t})},e.prototype._pipe=function(e,t){return this._stages.push({stageKey:"$"+e,stageValue:JSON.stringify(t)}),this},e.prototype.addFields=function(e){return this._pipe("addFields",e)},e.prototype.bucket=function(e){return this._pipe("bucket",e)},e.prototype.bucketAuto=function(e){return this._pipe("bucketAuto",e)},e.prototype.count=function(e){return this._pipe("count",e)},e.prototype.geoNear=function(e){return e.query&&(e.query=o$.encode(e.query)),e.distanceMultiplier&&"number"==typeof e.distanceMultiplier&&(e.distanceMultiplier=e.distanceMultiplier),e.near&&(e.near=new ob(e.near.longitude,e.near.latitude).toJSON()),this._pipe("geoNear",e)},e.prototype.group=function(e){return this._pipe("group",e)},e.prototype.limit=function(e){return this._pipe("limit",e)},e.prototype.match=function(e){return this._pipe("match",o$.encode(e))},e.prototype.project=function(e){return this._pipe("project",e)},e.prototype.lookup=function(e){return this._pipe("lookup",e)},e.prototype.replaceRoot=function(e){return this._pipe("replaceRoot",e)},e.prototype.sample=function(e){return this._pipe("sample",e)},e.prototype.skip=function(e){return this._pipe("skip",e)},e.prototype.sort=function(e){return this._pipe("sort",e)},e.prototype.sortByCount=function(e){return this._pipe("sortByCount",e)},e.prototype.unwind=function(e){return this._pipe("unwind",e)},e}(),oZ=(w=function(e,t){return(w=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}w(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),o0=function(e){function t(t,r){return e.call(this,t,r)||this}return oZ(t,e),Object.defineProperty(t.prototype,"name",{get:function(){return this._coll},enumerable:!0,configurable:!0}),t.prototype.doc=function(e){if("string"!=typeof e&&"number"!=typeof e)throw Error("docId必须为字符串或数字");return new oH(this._db,this._coll,e)},t.prototype.add=function(e,t){return new oH(this._db,this._coll,void 0).create(e,t)},t.prototype.aggregate=function(){return new oX(this._db,this._coll)},t}(oJ),o1={eq:function(e){return new oI(j.EQ,[e])},neq:function(e){return new oI(j.NEQ,[e])},lt:function(e){return new oI(j.LT,[e])},lte:function(e){return new oI(j.LTE,[e])},gt:function(e){return new oI(j.GT,[e])},gte:function(e){return new oI(j.GTE,[e])},in:function(e){return new oI(j.IN,e)},nin:function(e){return new oI(j.NIN,e)},all:function(e){return new oI(j.ALL,e)},elemMatch:function(e){return new oI(j.ELEM_MATCH,[e])},exists:function(e){return new oI(j.EXISTS,[e])},size:function(e){return new oI(j.SIZE,[e])},mod:function(e){return new oI(j.MOD,[e])},geoNear:function(e){return new oI(j.GEO_NEAR,[e])},geoWithin:function(e){return new oI(j.GEO_WITHIN,[e])},geoIntersects:function(e){return new oI(j.GEO_INTERSECTS,[e])},and:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=n9(arguments[0])?arguments[0]:Array.from(arguments);return new oS(q.AND,r)},nor:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=n9(arguments[0])?arguments[0]:Array.from(arguments);return new oS(q.NOR,r)},or:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=n9(arguments[0])?arguments[0]:Array.from(arguments);return new oS(q.OR,r)},not:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=n9(arguments[0])?arguments[0]:Array.from(arguments);return new oS(q.NOT,r)},set:function(e){return new o_(x.SET,[e])},remove:function(){return new o_(x.REMOVE,[])},inc:function(e){return new o_(x.INC,[e])},mul:function(e){return new o_(x.MUL,[e])},push:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];if(n7(t[0])&&t[0].hasOwnProperty("each")){var n=t[0];e={$each:n.each,$position:n.position,$sort:n.sort,$slice:n.slice}}else e=n9(t[0])?t[0]:Array.from(t);return new o_(x.PUSH,e)},pull:function(e){return new o_(x.PULL,e)},pullAll:function(e){return new o_(x.PULL_ALL,e)},pop:function(){return new o_(x.POP,[])},shift:function(){return new o_(x.SHIFT,[])},unshift:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=n9(arguments[0])?arguments[0]:Array.from(arguments);return new o_(x.UNSHIFT,r)},addToSet:function(e){return new o_(x.ADD_TO_SET,e)},rename:function(e){return new o_(x.RENAME,[e])},bit:function(e){return new o_(x.BIT,[e])},max:function(e){return new o_(x.MAX,[e])},min:function(e){return new o_(x.MIN,[e])},expr:function(e){return{$expr:e}},jsonSchema:function(e){return{$jsonSchema:e}},text:function(e){return"string"===n5(e)?{$search:e.search}:{$search:e.search,$language:e.language,$caseSensitive:e.caseSensitive,$diacriticSensitive:e.diacriticSensitive}},aggregate:{pipeline:function(){return new oX},abs:function(e){return new o2("abs",e)},add:function(e){return new o2("add",e)},ceil:function(e){return new o2("ceil",e)},divide:function(e){return new o2("divide",e)},exp:function(e){return new o2("exp",e)},floor:function(e){return new o2("floor",e)},ln:function(e){return new o2("ln",e)},log:function(e){return new o2("log",e)},log10:function(e){return new o2("log10",e)},mod:function(e){return new o2("mod",e)},multiply:function(e){return new o2("multiply",e)},pow:function(e){return new o2("pow",e)},sqrt:function(e){return new o2("sqrt",e)},subtract:function(e){return new o2("subtract",e)},trunc:function(e){return new o2("trunc",e)},arrayElemAt:function(e){return new o2("arrayElemAt",e)},arrayToObject:function(e){return new o2("arrayToObject",e)},concatArrays:function(e){return new o2("concatArrays",e)},filter:function(e){return new o2("filter",e)},in:function(e){return new o2("in",e)},indexOfArray:function(e){return new o2("indexOfArray",e)},isArray:function(e){return new o2("isArray",e)},map:function(e){return new o2("map",e)},range:function(e){return new o2("range",e)},reduce:function(e){return new o2("reduce",e)},reverseArray:function(e){return new o2("reverseArray",e)},size:function(e){return new o2("size",e)},slice:function(e){return new o2("slice",e)},zip:function(e){return new o2("zip",e)},and:function(e){return new o2("and",e)},not:function(e){return new o2("not",e)},or:function(e){return new o2("or",e)},cmp:function(e){return new o2("cmp",e)},eq:function(e){return new o2("eq",e)},gt:function(e){return new o2("gt",e)},gte:function(e){return new o2("gte",e)},lt:function(e){return new o2("lt",e)},lte:function(e){return new o2("lte",e)},neq:function(e){return new o2("ne",e)},cond:function(e){return new o2("cond",e)},ifNull:function(e){return new o2("ifNull",e)},switch:function(e){return new o2("switch",e)},dateFromParts:function(e){return new o2("dateFromParts",e)},dateFromString:function(e){return new o2("dateFromString",e)},dayOfMonth:function(e){return new o2("dayOfMonth",e)},dayOfWeek:function(e){return new o2("dayOfWeek",e)},dayOfYear:function(e){return new o2("dayOfYear",e)},isoDayOfWeek:function(e){return new o2("isoDayOfWeek",e)},isoWeek:function(e){return new o2("isoWeek",e)},isoWeekYear:function(e){return new o2("isoWeekYear",e)},millisecond:function(e){return new o2("millisecond",e)},minute:function(e){return new o2("minute",e)},month:function(e){return new o2("month",e)},second:function(e){return new o2("second",e)},hour:function(e){return new o2("hour",e)},week:function(e){return new o2("week",e)},year:function(e){return new o2("year",e)},literal:function(e){return new o2("literal",e)},mergeObjects:function(e){return new o2("mergeObjects",e)},objectToArray:function(e){return new o2("objectToArray",e)},allElementsTrue:function(e){return new o2("allElementsTrue",e)},anyElementTrue:function(e){return new o2("anyElementTrue",e)},setDifference:function(e){return new o2("setDifference",e)},setEquals:function(e){return new o2("setEquals",e)},setIntersection:function(e){return new o2("setIntersection",e)},setIsSubset:function(e){return new o2("setIsSubset",e)},setUnion:function(e){return new o2("setUnion",e)},concat:function(e){return new o2("concat",e)},dateToString:function(e){return new o2("dateToString",e)},indexOfBytes:function(e){return new o2("indexOfBytes",e)},indexOfCP:function(e){return new o2("indexOfCP",e)},split:function(e){return new o2("split",e)},strLenBytes:function(e){return new o2("strLenBytes",e)},strLenCP:function(e){return new o2("strLenCP",e)},strcasecmp:function(e){return new o2("strcasecmp",e)},substr:function(e){return new o2("substr",e)},substrBytes:function(e){return new o2("substrBytes",e)},substrCP:function(e){return new o2("substrCP",e)},toLower:function(e){return new o2("toLower",e)},toUpper:function(e){return new o2("toUpper",e)},meta:function(e){return new o2("meta",e)},addToSet:function(e){return new o2("addToSet",e)},avg:function(e){return new o2("avg",e)},first:function(e){return new o2("first",e)},last:function(e){return new o2("last",e)},max:function(e){return new o2("max",e)},min:function(e){return new o2("min",e)},push:function(e){return new o2("push",e)},stdDevPop:function(e){return new o2("stdDevPop",e)},stdDevSamp:function(e){return new o2("stdDevSamp",e)},sum:function(e){return new o2("sum",e)},let:function(e){return new o2("let",e)}},project:{slice:function(e){return new o3("slice",e)},elemMatch:function(e){return new o3("elemMatch",e)}}},o2=function(e,t){this["$"+e]=t},o3=function(e,t){this["$"+e]=t},o4=function(){function e(e){var t=e.regexp,r=e.options;if(!t)throw TypeError("regexp must be a string");this.$regex=t,this.$options=r}return e.prototype.parse=function(){return{$regex:this.$regex,$options:this.$options}},Object.defineProperty(e.prototype,"_internalType",{get:function(){return n6},enumerable:!0,configurable:!0}),e}();function o6(e){return new o4(e)}var o5={INSERT_DOC_FAIL:{code:"INSERT_DOC_FAIL",message:"insert document failed"},DATABASE_TRANSACTION_CONFLICT:{code:"DATABASE_TRANSACTION_CONFLICT",message:"database transaction conflict"}},o7=function(){return(o7=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o8=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},o9=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},ie="database.updateDocInTransaction",it=function(){function e(e,t,r){this._coll=t,this.id=r,this._transaction=e,this._request=this._transaction.getRequestMethod(),this._transactionId=this._transaction.getTransactionId()}return e.prototype.create=function(e){return o8(this,void 0,void 0,function(){var t,r,n,o;return o9(this,function(i){switch(i.label){case 0:return t={collectionName:this._coll,transactionId:this._transactionId,data:oQ.dF.stringify(oq(e),{relaxed:!1})},this.id&&(t._id=this.id),[4,this._request.send("database.insertDocInTransaction",t)];case 1:if((r=i.sent()).code)throw r;if(n=oQ.dF.parse(r.inserted),1==(o=oQ.dF.parse(r.ok))&&1==n)return[2,o7(o7({},r),{ok:o,inserted:n})];throw Error(o5.INSERT_DOC_FAIL.message)}})})},e.prototype.get=function(){return o8(this,void 0,void 0,function(){var e,t;return o9(this,function(r){switch(r.label){case 0:return e={collectionName:this._coll,transactionId:this._transactionId,query:{_id:{$eq:this.id}}},[4,this._request.send("database.getInTransaction",e)];case 1:if((t=r.sent()).code)throw t;return[2,{data:"null"!==t.data?om.formatField(oQ.dF.parse(t.data)):oQ.dF.parse(t.data),requestId:t.requestId}]}})})},e.prototype.set=function(e){return o8(this,void 0,void 0,function(){var t,r;return o9(this,function(n){switch(n.label){case 0:return t={collectionName:this._coll,transactionId:this._transactionId,query:{_id:{$eq:this.id}},data:oQ.dF.stringify(oq(e),{relaxed:!1}),upsert:!0},[4,this._request.send(ie,t)];case 1:if((r=n.sent()).code)throw r;return[2,o7(o7({},r),{updated:oQ.dF.parse(r.updated),upserted:r.upserted?JSON.parse(r.upserted):null})]}})})},e.prototype.update=function(e){return o8(this,void 0,void 0,function(){var t,r;return o9(this,function(n){switch(n.label){case 0:return t={collectionName:this._coll,transactionId:this._transactionId,query:{_id:{$eq:this.id}},data:oQ.dF.stringify(oF.encode(e),{relaxed:!1})},[4,this._request.send(ie,t)];case 1:if((r=n.sent()).code)throw r;return[2,o7(o7({},r),{updated:oQ.dF.parse(r.updated)})]}})})},e.prototype.delete=function(){return o8(this,void 0,void 0,function(){var e,t;return o9(this,function(r){switch(r.label){case 0:return e={collectionName:this._coll,transactionId:this._transactionId,query:{_id:{$eq:this.id}}},[4,this._request.send("database.deleteDocInTransaction",e)];case 1:if((t=r.sent()).code)throw t;return[2,o7(o7({},t),{deleted:oQ.dF.parse(t.deleted)})]}})})},e}(),ir=(S=function(e,t){return(S=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}S(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),io=function(e){function t(t,r){return e.call(this,t,r)||this}return ir(t,e),Object.defineProperty(t.prototype,"name",{get:function(){return this._coll},enumerable:!0,configurable:!0}),t.prototype.doc=function(e){if("string"!=typeof e&&"number"!=typeof e)throw Error("docId必须为字符串或数字");return new it(this._transaction,this._coll,e)},t.prototype.add=function(e){var t;return void 0!==e._id&&(t=e._id),new it(this._transaction,this._coll,t).create(e)},t}(function(e,t){this._coll=t,this._transaction=e}),ii=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},ia=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},is=function(){function e(e){this._db=e,this._request=new il.reqClass(this._db.config),this.aborted=!1,this.commited=!1,this.inited=!1}return e.prototype.init=function(){return ii(this,void 0,void 0,function(){var e;return ia(this,function(t){switch(t.label){case 0:return[4,this._request.send("database.startTransaction")];case 1:if((e=t.sent()).code)throw e;return this.inited=!0,this._id=e.transactionId,[2]}})})},e.prototype.collection=function(e){if(!e)throw Error("Collection name is required");return new io(this,e)},e.prototype.getTransactionId=function(){return this._id},e.prototype.getRequestMethod=function(){return this._request},e.prototype.commit=function(){return ii(this,void 0,void 0,function(){var e,t;return ia(this,function(r){switch(r.label){case 0:return e={transactionId:this._id},[4,this._request.send("database.commitTransaction",e)];case 1:if((t=r.sent()).code)throw t;return this.commited=!0,[2,t]}})})},e.prototype.rollback=function(e){return ii(this,void 0,void 0,function(){var t,r;return ia(this,function(n){switch(n.label){case 0:return t={transactionId:this._id},[4,this._request.send("database.abortTransaction",t)];case 1:if((r=n.sent()).code)throw r;return this.aborted=!0,this.abortReason=e,[2,r]}})})},e}();function iu(){return ii(this,void 0,void 0,function(){var e;return ia(this,function(t){switch(t.label){case 0:return[4,(e=new is(this)).init()];case 1:return t.sent(),[2,e]}})})}function ic(e,t){return void 0===t&&(t=3),ii(this,void 0,void 0,function(){var r,n,o,i,a=this;return ia(this,function(s){switch(s.label){case 0:return s.trys.push([0,4,,10]),[4,(r=new is(this)).init()];case 1:return s.sent(),[4,e(r)];case 2:if(n=s.sent(),!0===r.aborted)throw r.abortReason;return[4,r.commit()];case 3:return s.sent(),[2,n];case 4:if(o=s.sent(),!1===r.inited)throw o;if(i=function(e){return ii(a,void 0,void 0,function(){return ia(this,function(t){switch(t.label){case 0:if(!(!r.aborted&&!r.commited))return[3,5];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,r.rollback()];case 2:case 3:return t.sent(),[3,4];case 4:throw e;case 5:if(!0===r.aborted)throw r.abortReason;throw e}})})},!(t<=0))return[3,6];return[4,i(o)];case 5:s.sent(),s.label=6;case 6:if(!(o&&o.code===o5.DATABASE_TRANSACTION_CONFLICT.code))return[3,8];return[4,ic.bind(this)(e,--t)];case 7:return[2,s.sent()];case 8:return[4,i(o)];case 9:return s.sent(),[3,10];case 10:return[2]}})})}var il=function(){function e(e){this.config=e,this.Geo=D,this.serverDate=op,this.command=o1,this.RegExp=o6,this.startTransaction=iu,this.runTransaction=ic,this.logicCommand=oS,this.updateCommand=o_,this.queryCommand=oI}return e.prototype.collection=function(e){if(!e)throw Error("Collection name is required");return new o0(this,e)},e.prototype.createCollection=function(t){return new e.reqClass(this.config).send("database.addCollection",{collectionName:t})},e}(),ih=function(){return(ih=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},id={name:"database",entity:{database:function(e){var t=this.platform,r=t.adapter,n=t.runtime;return il.reqClass=this.request.constructor,il.getAccessToken=this.authInstance?this.authInstance.getAccessToken.bind(this.authInstance):function(){return""},il.runtime=n,this.wsClientClass&&(il.wsClass=r.wsClass,il.wsClientClass=this.wsClientClass),il.ws||(il.ws=null),new il(ih(ih(ih({},this.config),{_fromApp:this}),e))}}};try{cloudbase.registerComponent(id)}catch(e){}eQ.registerVersion("2.17.6");try{(0,eY.Wr)(eQ),function(e){try{e.registerComponent(tt)}catch(e){console.warn(e)}}(eQ),function(e){try{e.registerComponent(tc)}catch(e){console.warn(e)}}(eQ),function(e){try{e.registerComponent(id)}catch(e){console.warn(e)}}(eQ),function(e){try{e.registerComponent(tB),e.registerHook(tF)}catch(e){console.warn(e)}}(eQ),function(e){try{e.registerComponent(tJ)}catch(e){console.warn(e)}}(eQ),function(e){try{e.registerComponent(t3)}catch(e){console.warn(e)}}(eQ),function(e){try{e.registerComponent(nW)}catch(e){console.warn(e)}}(eQ),function(e){try{e.registerComponent(e4)}catch(e){console.warn(e)}}(eQ)}catch(e){}try{window.cloudbase=eQ}catch(e){}var ip=eQ},6299:function(e,t,r){"use strict";function n(){return"xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}r.d(t,{hW:function(){return q}}),(o=c||(c={})).AUTH_SIGN_UP_URL="/auth/v1/signup",o.AUTH_TOKEN_URL="/auth/v1/token",o.AUTH_REVOKE_URL="/auth/v1/revoke",o.WEDA_USER_URL="/auth/v1/plugin/weda/userinfo",o.AUTH_RESET_PASSWORD="/auth/v1/reset",o.AUTH_GET_DEVICE_CODE="/auth/v1/device/code",o.CHECK_USERNAME="/auth/v1/checkUsername",o.CHECK_IF_USER_EXIST="/auth/v1/checkIfUserExist",o.GET_PROVIDER_TYPE="/auth/v1/mgr/provider/providerSubType",o.AUTH_SIGN_IN_URL="/auth/v1/signin",o.AUTH_SIGN_IN_ANONYMOUSLY_URL="/auth/v1/signin/anonymously",o.AUTH_SIGN_IN_WITH_PROVIDER_URL="/auth/v1/signin/with/provider",o.AUTH_SIGN_IN_WITH_WECHAT_URL="/auth/v1/signin/wechat/noauth",o.AUTH_SIGN_IN_CUSTOM="/auth/v1/signin/custom",o.PROVIDER_TOKEN_URL="/auth/v1/provider/token",o.PROVIDER_URI_URL="/auth/v1/provider/uri",o.USER_ME_URL="/auth/v1/user/me",o.AUTH_SIGNOUT_URL="/auth/v1/user/signout",o.USER_QUERY_URL="/auth/v1/user/query",o.USER_PRIFILE_URL="/auth/v1/user/profile",o.USER_BASIC_EDIT_URL="/auth/v1/user/basic/edit",o.USER_TRANS_BY_PROVIDER_URL="/auth/v1/user/trans/by/provider",o.PROVIDER_LIST="/auth/v1/user/provider",o.PROVIDER_BIND_URL="/auth/v1/user/provider/bind",o.PROVIDER_UNBIND_URL="/auth/v1/user/provider",o.CHECK_PWD_URL="/auth/v1/user/sudo",o.SUDO_URL="/auth/v1/user/sudo",o.BIND_CONTACT_URL="/auth/v1/user/contact",o.AUTH_SET_PASSWORD="/auth/v1/user/password",o.AUTHORIZE_DEVICE_URL="/auth/v1/user/device/authorize",o.AUTHORIZE_URL="/auth/v1/user/authorize",o.AUTHORIZE_INFO_URL="/auth/v1/user/authorize/info",o.AUTHORIZED_DEVICES_DELETE_URL="/auth/v1/user/authorized/devices/",o.AUTH_REVOKE_ALL_URL="/auth/v1/user/revoke/all",o.GET_USER_BEHAVIOR_LOG="/auth/v1/user/security/history",o.VERIFICATION_URL="/auth/v1/verification",o.VERIFY_URL="/auth/v1/verification/verify",o.CAPTCHA_DATA_URL="/auth/v1/captcha/data",o.VERIFY_CAPTCHA_DATA_URL="/auth/v1/captcha/data/verify",o.GET_CAPTCHA_URL="/auth/v1/captcha/init",o.GET_MINIPROGRAM_QRCODE="/auth/v1/qrcode/generate",o.GET_MINIPROGRAM_QRCODE_STATUS="/auth/v1/qrcode/get/status",(i=l||(l={})).AUTH_SIGN_IN_URL="/auth/v2/signin/username",i.AUTH_TOKEN_URL="/auth/v2/token",i.USER_ME_URL="/auth/v2/user/me",i.VERIFY_URL="/auth/v2/signin/verificationcode",i.AUTH_SIGN_IN_WITH_PROVIDER_URL="/auth/v2/signin/with/provider",i.AUTH_PUBLIC_KEY="/auth/v2/signin/publichkey",i.AUTH_RESET_PASSWORD="/auth/v2/signin/password/update",(a=f||(f={})).REGISTER="REGISTER",a.SIGN_IN="SIGN_IN",a.PASSWORD_RESET="PASSWORD_RESET",a.EMAIL_ADDRESS_CHANGE="EMAIL_ADDRESS_CHANGE",a.PHONE_NUMBER_CHANGE="PHONE_NUMBER_CHANGE",(s=h||(h={})).UNREACHABLE="unreachable",s.LOCAL="local",s.CANCELLED="cancelled",s.UNKNOWN="unknown",s.UNAUTHENTICATED="unauthenticated",s.RESOURCE_EXHAUSTED="resource_exhausted",s.FAILED_PRECONDITION="failed_precondition",s.INVALID_ARGUMENT="invalid_argument",s.DEADLINE_EXCEEDED="deadline_exceeded",s.NOT_FOUND="not_found",s.ALREADY_EXISTS="already_exists",s.PERMISSION_DENIED="permission_denied",s.ABORTED="aborted",s.OUT_OF_RANGE="out_of_range",s.UNIMPLEMENTED="unimplemented",s.INTERNAL="internal",s.UNAVAILABLE="unavailable",s.DATA_LOSS="data_loss",s.INVALID_PASSWORD="invalid_password",s.PASSWORD_NOT_SET="password_not_set",s.INVALID_STATUS="invalid_status",s.USER_PENDING="user_pending",s.USER_BLOCKED="user_blocked",s.INVALID_VERIFICATION_CODE="invalid_verification_code",s.TWO_FACTOR_REQUIRED="two_factor_required",s.INVALID_TWO_FACTOR="invalid_two_factor",s.INVALID_TWO_FACTOR_RECOVERY="invalid_two_factor_recovery",s.UNDER_REVIEW="under_review",s.INVALID_REQUEST="invalid_request",s.UNAUTHORIZED_CLIENT="unauthorized_client",s.ACCESS_DENIED="access_denied",s.UNSUPPORTED_RESPONSE_TYPE="unsupported_response_type",s.INVALID_SCOPE="invalid_scope",s.INVALID_GRANT="invalid_grant",s.SERVER_ERROR="server_error",s.TEMPORARILY_UNAVAILABLE="temporarily_unavailable",s.INTERACTION_REQUIRED="interaction_required",s.LOGIN_REQUIRED="login_required",s.ACCOUNT_SELECTION_REQUIRED="account_selection_required",s.CONSENT_REQUIRED="consent_required",s.INVALID_REQUEST_URI="invalid_request_uri",s.INVALID_REQUEST_OBJECT="invalid_request_object",s.REQUEST_NOT_SUPPORTED="request_not_supported",s.REQUEST_URI_NOT_SUPPORTED="request_uri_not_supported",s.REGISTRATION_NOT_SUPPORTED="registration_not_supported",s.CAPTCHA_REQUIRED="captcha_required",s.CAPTCHA_INVALID="captcha_invalid",(u=d||(d={})).CLIENT_ID="client_id",u.CLIENT_SECRET="client_secret",u.RESPONSE_TYPE="response_type",u.SCOPE="scope",u.STATE="state",u.REDIRECT_URI="redirect_uri",u.ERROR="error",u.ERROR_DESCRIPTION="error_description",u.ERROR_URI="error_uri",u.GRANT_TYPE="grant_type",u.CODE="code",u.ACCESS_TOKEN="access_token",u.TOKEN_TYPE="token_type",u.EXPIRES_IN="expires_in",u.USERNAME="username",u.PASSWORD="password",u.REFRESH_TOKEN="refresh_token";var o,i,a,s,u,c,l,f,h,d,p=r(72903);class y{constructor(e){this.clientId=e?.clientId||"",globalThis.jsSdkFnPromiseMap=globalThis.jsSdkFnPromiseMap||new Map}async run(e,t){e=`${this.clientId}_${e}`;let r=globalThis.jsSdkFnPromiseMap.get(e);return r||(r=new Promise((r,n)=>{(async()=>{try{await this.runIdlePromise();let e=t();r(await e)}catch(e){n(e)}finally{globalThis.jsSdkFnPromiseMap.delete(e)}})()}),globalThis.jsSdkFnPromiseMap.set(e,r)),r}runIdlePromise(){return Promise.resolve()}}let m="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",v="x-request-id",b="x-device-id",g="device_id",_=async function(e,t){let r=null,n=null;try{let o=Object.assign({},t);o.method||(o.method="GET"),o.body&&"string"!=typeof o.body&&(o.body=JSON.stringify(o.body));let i=await fetch(e,o),a=await i.json();a?.error?(n=a).error_uri=new URL(e).pathname:r=a}catch(t){n={error:h.UNREACHABLE,error_description:t.message,error_uri:new URL(e).pathname}}if(!n)return r;throw n};class w{constructor(e){this._env=e?.env||""}async getItem(e){return window.localStorage.getItem(`${e}${this._env}`)}async removeItem(e){window.localStorage.removeItem(`${e}${this._env}`)}async setItem(e,t){window.localStorage.setItem(`${e}${this._env}`,t)}getItemSync(e){return window.localStorage.getItem(`${e}${this._env}`)}removeItemSync(e){window.localStorage.removeItem(`${e}${this._env}`)}setItemSync(e,t){window.localStorage.setItem(`${e}${this._env}`,t)}}let S=new w;function E(e){let t=!0;return e?.expires_at&&e?.access_token&&(t=e.expires_at<new Date),t}class T{constructor(e){this.credentials=null,this.singlePromise=null,this.tokenSectionName=e.tokenSectionName,this.storage=e.storage,this.clientId=e.clientId,this.singlePromise=new y({clientId:this.clientId})}getStorageCredentialsSync(){let e=null,t=this.storage.getItemSync(this.tokenSectionName);if(null!=t)try{e=JSON.parse(t),e?.expires_at&&(e.expires_at=new Date(e.expires_at))}catch(t){this.storage.removeItem(this.tokenSectionName),e=null}return e}async setCredentials(e){if(e?.expires_in){if(e?.expires_at||(e.expires_at=new Date(Date.now()+(e.expires_in-30)*1e3)),this.storage){let t=JSON.stringify(e);await this.storage.setItem(this.tokenSectionName,t)}this.credentials=e}else this.storage&&await this.storage.removeItem(this.tokenSectionName),this.credentials=null}async getCredentials(){return this.singlePromise.run("getCredentials",async()=>(E(this.credentials)&&(this.credentials=await this.getStorageCredentials()),this.credentials))}async getStorageCredentials(){return this.singlePromise.run("_getStorageCredentials",async()=>{let e=null,t=await this.storage.getItem(this.tokenSectionName);if(null!=t)try{e=JSON.parse(t),e?.expires_at&&(e.expires_at=new Date(e.expires_at))}catch(t){await this.storage.removeItem(this.tokenSectionName),e=null}return e})}}class I{constructor(e){this.singlePromise=null,e.clientSecret||(e.clientSecret=""),!e.clientId&&e.env&&(e.clientId=e.env),this.apiOrigin=e.apiOrigin,this.clientId=e.clientId,this.singlePromise=new y({clientId:this.clientId}),this.retry=this.formatRetry(e.retry,I.defaultRetry),e.baseRequest?this.baseRequest=e.baseRequest:this.baseRequest=_,this.tokenInURL=e.tokenInURL,this.headers=e.headers,this.storage=e.storage||S,this.localCredentials=new T({tokenSectionName:`credentials_${e.clientId}`,storage:this.storage,clientId:e.clientId}),this.clientSecret=e.clientSecret,e.clientId&&(this.basicAuth=`Basic ${function(e){let t,r,n,o;e=String(e);let i="",a=0,s=e.length%3;for(;a<e.length;){if((r=e.charCodeAt(a++))>255||(n=e.charCodeAt(a++))>255||(o=e.charCodeAt(a++))>255)throw TypeError("Failed to execute 'btoa' on 'Window': The string to be encoded contains characters outside of the Latin1 range.");t=r<<16|n<<8|o,i+=m.charAt(t>>18&63)+m.charAt(t>>12&63)+m.charAt(t>>6&63)+m.charAt(63&t)}return s?i.slice(0,s-3)+"===".substring(s):i}(`${e.clientId}:${this.clientSecret}`)}`),this.wxCloud=e.wxCloud;try{(function(){if("undefined"==typeof wx||"undefined"==typeof Page||!wx.getSystemInfoSync||!wx.getStorageSync||!wx.setStorageSync||!wx.connectSocket||!wx.request)return!1;try{if(!wx.getSystemInfoSync()||"qq"===wx.getSystemInfoSync().AppPlatform)return!1}catch(e){return!1}return!0})()&&void 0===this.wxCloud&&e.env&&(wx.cloud.init({env:e.env}),this.wxCloud=wx.cloud)}catch(e){}this.refreshTokenFunc=e.refreshTokenFunc||this.defaultRefreshTokenFunc,this.anonymousSignInFunc=e.anonymousSignInFunc}setCredentials(e){return this.localCredentials.setCredentials(e)}async getAccessToken(){let e=await this.getCredentials();return e?.access_token?Promise.resolve(e.access_token):Promise.reject({error:h.UNAUTHENTICATED})}async request(e,t){t||(t={});let r=this.formatRetry(t.retry,this.retry);if(t.headers=t.headers||{},this.headers&&(t.headers={...this.headers,...t.headers}),t.headers[v]||(t.headers[v]=n()),!t.headers[b]){let e=await this.getDeviceId();t.headers[b]=e}if(t?.withBasicAuth&&this.basicAuth&&(t.headers.Authorization=this.basicAuth),t?.withCredentials){let r=await this.getCredentials();r&&(this.tokenInURL?(0>e.indexOf("?")&&(e+="?"),e+=`access_token=${r.access_token}`):t.headers.Authorization=`${r.token_type} ${r.access_token}`)}else this.clientId&&0>e.indexOf("client_id")&&(e+=(0>e.indexOf("?")?"?":"&")+`client_id=${this.clientId}`);e.startsWith("/")&&(e=this.apiOrigin+e);let o=null,i=r+1;for(let n=0;n<i;n++){try{o=t.useWxCloud?await this.wxCloudCallFunction(e,t):await this.baseRequest(e,t);break}catch(e){if(t.withCredentials&&e&&e.error===h.UNAUTHENTICATED)return await this.setCredentials(null),Promise.reject(e);if(n===r||!e||"unreachable"!==e.error)return Promise.reject(e)}await this.sleep(I.retryInterval)}return o}async wxCloudCallFunction(e,t){let r=null,n=null;try{let o="";try{o=await wx.getRendererUserAgent()}catch(e){}let{result:i}=await this.wxCloud.callFunction({name:"httpOverCallFunction",data:{url:e,method:t.method,headers:{origin:"https://servicewechat.com","User-Agent":o,...t.headers},body:t.body}});i?.body?.error_code?(n=i?.body).error_uri=(0,p.y)(e):r=i?.body}catch(t){n={error:h.UNREACHABLE,error_description:t.message,error_uri:(0,p.y)(e)}}if(!n)return r;throw n}async getCredentials(){let e=await this.localCredentials.getCredentials();return e?(E(e)&&(e=e&&"anonymous"===e.scope?this.anonymousSignInFunc?await this.anonymousSignInFunc(e)||await this.localCredentials.getCredentials():await this.anonymousSignIn(e):await this.refreshToken(e)),e):this.unAuthenticatedError("credentials not found")}getCredentialsSync(){return this.localCredentials.getStorageCredentialsSync()}getCredentialsAsync(){return this.getCredentials()}async getScope(){let e=await this.localCredentials.getCredentials();return e?e.scope:this.unAuthenticatedError("credentials not found")}async getGroups(){let e=await this.localCredentials.getCredentials();return e?e.groups:this.unAuthenticatedError("credentials not found")}async refreshToken(e){return this.singlePromise.run("_refreshToken",async()=>{if(!e||!e.refresh_token)return this.unAuthenticatedError("no refresh token found in credentials");try{let t=await this.refreshTokenFunc(e.refresh_token,e);return await this.localCredentials.setCredentials(t),t}catch(e){if(e.error===h.INVALID_GRANT)return await this.localCredentials.setCredentials(null),this.unAuthenticatedError(e.error_description);return Promise.reject(e)}})}checkRetry(e){let t=null;if(("number"!=typeof e||e<I.minRetry||e>I.maxRetry)&&(t={error:h.UNREACHABLE,error_description:"wrong options param: retry"}),t)throw t;return e}formatRetry(e,t){return void 0===e?t:this.checkRetry(e)}async sleep(e){return new Promise(t=>{setTimeout(()=>{t()},e)})}async anonymousSignIn(e){return this.singlePromise.run("_anonymous",async()=>{if(!e||"anonymous"!==e.scope)return this.unAuthenticatedError("no anonymous in credentials");try{let e=await this.request(c.AUTH_SIGN_IN_ANONYMOUSLY_URL,{method:"POST",withBasicAuth:!0,body:{}});return await this.localCredentials.setCredentials(e),e}catch(e){if(e.error===h.INVALID_GRANT)return await this.localCredentials.setCredentials(null),this.unAuthenticatedError(e.error_description);return Promise.reject(e)}})}async defaultRefreshTokenFunc(e,t){if(void 0===e||""===e)return this.unAuthenticatedError("refresh token not found");let r=c.AUTH_TOKEN_URL;return t?.version==="v2"&&(r=l.AUTH_TOKEN_URL),{...await this.request(r,{method:"POST",body:{client_id:this.clientId,client_secret:this.clientSecret,grant_type:"refresh_token",refresh_token:e}}),version:t?.version||"v1"}}async getDeviceId(){if(this.deviceID)return this.deviceID;let e=await this.storage.getItem(g);return"string"==typeof e&&e.length>=16&&e.length<=48||(e=n(),await this.storage.setItem(g,e)),this.deviceID=e,e}unAuthenticatedError(e){return Promise.reject({error:h.UNAUTHENTICATED,error_description:e})}}function O(){let{wx:e}=globalThis;if(void 0===e||void 0===globalThis.Page||!e.getSystemInfoSync||!e.getStorageSync||!e.setStorageSync||!e.connectSocket||!e.request)return!1;try{if(!e.getSystemInfoSync()||"qq"===e.getSystemInfoSync().AppPlatform)return!1}catch(e){return!1}return!0}I.defaultRetry=2,I.minRetry=0,I.maxRetry=5,I.retryInterval=1e3;let R=!1;function A(){R=R||"miniprogram"===window.__wxjs_environment}try{O()||(R=R||!!navigator.userAgent.match(/miniprogram/i)||"miniprogram"===window.__wxjs_environment,window&&window.WeixinJSBridge&&window.WeixinJSBridge.invoke?A():"undefined"!=typeof document&&document.addEventListener("WeixinJSBridgeReady",A,!1))}catch(e){}class P{constructor(e){if(this.params={},"string"==typeof e)this.parse(e);else if(e&&"object"==typeof e)for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&this.append(t,e[t])}parse(e){e.split("&").forEach(e=>{let[t,r]=e.split("=").map(decodeURIComponent);this.append(t,r)})}append(e,t){this.params[e]?this.params[e]=this.params[e].concat([t]):this.params[e]=[t]}get(e){return this.params[e]?this.params[e][0]:null}getAll(e){return this.params[e]||[]}delete(e){delete this.params[e]}has(e){return Object.prototype.hasOwnProperty.call(this.params,e)}set(e,t){this.params[e]=[t]}toString(){let e=[];for(let t in this.params)Object.prototype.hasOwnProperty.call(this.params,t)&&this.params[t].forEach(r=>{e.push(`${encodeURIComponent(t)}=${encodeURIComponent(r)}`)});return e.join("&")}}class C{constructor(e){e.openURIWithCallback||(e.openURIWithCallback=this.getDefaultOpenURIWithCallback()),e.storage||(e.storage=S),this.config=e,this.tokenSectionName=`captcha_${e.clientId}`}async request(e,t){let r;t||(t={}),t.method||(t.method="GET");let n=`${t.method}:${e}`,o=e;t.withCaptcha&&(o=await this.appendCaptchaTokenToURL(e,n,!1));try{r=await this.config.request(o,t)}catch(r){if(r.error===h.CAPTCHA_REQUIRED||r.error===h.CAPTCHA_INVALID)return e=await this.appendCaptchaTokenToURL(e,n,r.error===h.CAPTCHA_INVALID),this.config.request(e,t);return Promise.reject(r)}return r}getDefaultOpenURIWithCallback(){if(!O()&&!R&&(window.location.search.indexOf("__captcha")>0&&(document.body.style.display="none"),null===document.getElementById("captcha_panel_wrap"))){let e=document.createElement("div");e.style.cssText="background-color: rgba(0, 0, 0, 0.7);position: fixed;left: 0px;right: 0px;top: 0px;bottom: 0px;padding: 9vw 0 0 0;display: none;z-index:100;",e.setAttribute("id","captcha_panel_wrap"),setTimeout(()=>{document.body.appendChild(e)},0)}return this.defaultOpenURIWithCallback}async defaultOpenURIWithCallback(e,t){let{width:r="355px",height:n="355px"}=t||{};if(e.match(/^(data:.*)$/))return Promise.reject({error:h.UNIMPLEMENTED,error_description:"need to impl captcha data"});let o=document.getElementById("captcha_panel_wrap"),i=document.createElement("iframe");return o.innerHTML="",i.setAttribute("src",e),i.setAttribute("id","review-panel-iframe"),i.style.cssText=`min-width:${r};display:block;height:${n};margin:0 auto;background-color: rgb(255, 255, 255);border: none;`,o.appendChild(i),o.style.display="block",new Promise((e,t)=>{i.onload=function(){try{let r=window.location,n=i.contentWindow.location;if(n.host+n.pathname===r.host+r.pathname){o.style.display="none";let r=new P(n.search),i=r.get("captcha_token");if(i)return e({captcha_token:i,expires_in:Number(r.get("expires_in"))});return t({error:r.get("error"),error_description:r.get("error_description")})}o.style.display="block"}catch(e){o.style.display="block"}}})}async getCaptchaToken(e,t){let r;if(!e){let e=await this.findCaptchaToken();if(e)return e}if(O()||R){let e=await this.config.request(c.CAPTCHA_DATA_URL,{method:"POST",body:{state:t,redirect_uri:""},withCredentials:!1});r={url:`${e.data}?state=${encodeURIComponent(t)}&token=${encodeURIComponent(e.token)}`}}else{let e=`${window.location.origin+window.location.pathname}?__captcha=on`;if((r=await this.config.request(c.GET_CAPTCHA_URL,{method:"POST",body:{client_id:this.config.clientId,redirect_uri:e,state:t},withCredentials:!1})).captcha_token){let e={captcha_token:r.captcha_token,expires_in:r.expires_in};return this.saveCaptchaToken(e),r.captcha_token}}let n=await this.config.openURIWithCallback(r.url);return this.saveCaptchaToken(n),n.captcha_token}async appendCaptchaTokenToURL(e,t,r){let n=await this.getCaptchaToken(r,t);return e.indexOf("?")>0?e+=`&captcha_token=${n}`:e+=`?captcha_token=${n}`,e}async saveCaptchaToken(e){e.expires_at=new Date(Date.now()+(e.expires_in-10)*1e3);let t=JSON.stringify(e);await this.config.storage.setItem(this.tokenSectionName,t)}async findCaptchaToken(){let e=await this.config.storage.getItem(this.tokenSectionName);if(null!=e)try{let t=JSON.parse(e);if(t?.expires_at&&(t.expires_at=new Date(t.expires_at)),t.expires_at<new Date)return null;return t.captcha_token}catch(e){await this.config.storage.removeItem(this.tokenSectionName)}return null}}function N(e){if(!globalThis.IS_MP_BUILD&&e)return r(16855)}class x{static parseParamsToSearch(e){return Object.keys(e).forEach(t=>{e[t]||delete e[t]}),new P(e).toString()}constructor(e){let{request:t}=e,r=e.credentialsClient;if(r||(r=new I({apiOrigin:e.apiOrigin,clientId:e.clientId,storage:e.storage,env:e.env,baseRequest:e.baseRequest,anonymousSignInFunc:e.anonymousSignInFunc,wxCloud:e.wxCloud})),!t){let n=r.request.bind(r),o=new C({clientId:e.clientId,request:n,storage:e.storage,...e.captchaOptions});t=o.request.bind(o)}this.config={env:e.env,apiOrigin:e.apiOrigin,clientId:e.clientId,request:t,credentialsClient:r,storage:e.storage||S}}getParamsByVersion(e,t){let r=(0,p.I)(e),n={v2:l}[r?.version]?.[t]||c[t];return r&&delete r.version,{params:r,url:n}}async signIn(e){let t=e.version||"v1",r=this.getParamsByVersion(e,"AUTH_SIGN_IN_URL");r.params.query&&delete r.params.query;let n=await this.getEncryptParams(r.params),o=await this.config.request(r.url,{method:"POST",body:n});return await this.config.credentialsClient.setCredentials({...o,version:t}),Promise.resolve(o)}async signInAnonymously(e={},t=!1){let r=await this.config.request(c.AUTH_SIGN_IN_ANONYMOUSLY_URL,{method:"POST",body:e,useWxCloud:t});return await this.config.credentialsClient.setCredentials(r),Promise.resolve(r)}async signUp(e){let t=await this.config.request(c.AUTH_SIGN_UP_URL,{method:"POST",body:e});return await this.config.credentialsClient.setCredentials(t),Promise.resolve(t)}async signOut(e){let t={};if(e){try{t=await this.config.request(c.AUTH_SIGNOUT_URL,{method:"POST",withCredentials:!0,body:e})}catch(e){e.error!==h.UNAUTHENTICATED&&console.log("sign_out_error",e)}return await this.config.credentialsClient.setCredentials(),t}let r=await this.config.credentialsClient.getAccessToken(),n=await this.config.request(c.AUTH_REVOKE_URL,{method:"POST",body:{token:r}});return await this.config.credentialsClient.setCredentials(),Promise.resolve(n)}async revokeAllDevices(){await this.config.request(c.AUTH_REVOKE_ALL_URL,{method:"DELETE",withCredentials:!0})}async revokeDevice(e){await this.config.request(c.AUTHORIZED_DEVICES_DELETE_URL+e.device_id,{method:"DELETE",withCredentials:!0})}async getVerification(e,t){let r=!1;return"CUR_USER"===e.target?r=!0:await this.hasLoginState()&&(r=!0),this.config.request(c.VERIFICATION_URL,{method:"POST",body:e,withCaptcha:t?.withCaptcha||!1,withCredentials:r})}async verify(e){let t=this.getParamsByVersion(e,"VERIFY_URL"),r=await this.config.request(t.url,{method:"POST",body:t.params});return e?.version==="v2"&&await this.config.credentialsClient.setCredentials({...r,version:"v2"}),r}async genProviderRedirectUri(e){let{provider_redirect_uri:t,other_params:r={},...n}=e;t&&!n.redirect_uri&&(n.redirect_uri=t);let o=`${c.PROVIDER_URI_URL}?${x.parseParamsToSearch(n)}`;return Object.keys(r).forEach(e=>{let t=r[e];("sign_out_uri"!==e||t?.length>0)&&(o+=`&other_params[${e}]=${encodeURIComponent(t)}`)}),this.config.request(o,{method:"GET"})}async grantProviderToken(e,t=!1){return this.config.request(c.PROVIDER_TOKEN_URL,{method:"POST",body:e,useWxCloud:t})}async patchProviderToken(e){return this.config.request(c.PROVIDER_TOKEN_URL,{method:"PATCH",body:e})}async signInWithProvider(e,t=!1){let r=this.getParamsByVersion(e,"AUTH_SIGN_IN_WITH_PROVIDER_URL"),n=await this.config.request(r.url,{method:"POST",body:r.params,useWxCloud:t});return await this.config.credentialsClient.setCredentials({...n,version:e?.version||"v1"}),Promise.resolve(n)}async signInCustom(e){let t=await this.config.request(c.AUTH_SIGN_IN_CUSTOM,{method:"POST",body:e});return await this.config.credentialsClient.setCredentials(t),Promise.resolve(t)}async signInWithWechat(e={}){let t=await this.config.request(c.AUTH_SIGN_IN_WITH_WECHAT_URL,{method:"POST",body:e});return await this.config.credentialsClient.setCredentials(t),Promise.resolve(t)}async bindWithProvider(e){return this.config.request(c.PROVIDER_BIND_URL,{method:"POST",body:e,withCredentials:!0})}async getUserProfile(e){return this.getUserInfo(e)}async getUserInfo(e={}){let t=this.getParamsByVersion(e,"USER_ME_URL");if(t.params?.query){let e=new P(t.params.query);t.url+=`?${e.toString()}`}let r=await this.config.request(t.url,{method:"GET",withCredentials:!0});return r.sub&&(r.uid=r.sub),r}async getWedaUserInfo(){return await this.config.request(c.WEDA_USER_URL,{method:"GET",withCredentials:!0})}async deleteMe(e){let t=this.getParamsByVersion(e,"USER_ME_URL"),r=`${t.url}?${x.parseParamsToSearch(t.params)}`;return this.config.request(r,{method:"DELETE",withCredentials:!0})}async hasLoginState(){try{return await this.config.credentialsClient.getAccessToken(),!0}catch(e){return!1}}hasLoginStateSync(){return this.config.credentialsClient.getCredentialsSync()}async getLoginState(){return this.config.credentialsClient.getCredentialsAsync()}async transByProvider(e){return this.config.request(c.USER_TRANS_BY_PROVIDER_URL,{method:"PATCH",body:e,withCredentials:!0})}async grantToken(e){let t=this.getParamsByVersion(e,"AUTH_TOKEN_URL");return this.config.request(t.url,{method:"POST",body:t.params})}async getProviders(){return this.config.request(c.PROVIDER_LIST,{method:"GET",withCredentials:!0})}async unbindProvider(e){return this.config.request(`${c.PROVIDER_UNBIND_URL}/${e.provider_id}`,{method:"DELETE",withCredentials:!0})}async checkPassword(e){return this.config.request(`${c.CHECK_PWD_URL}`,{method:"POST",withCredentials:!0,body:e})}async editContact(e){return this.config.request(`${c.BIND_CONTACT_URL}`,{method:"PATCH",withCredentials:!0,body:e})}async setPassword(e){return this.config.request(`${c.AUTH_SET_PASSWORD}`,{method:"PATCH",withCredentials:!0,body:e})}async updatePasswordByOld(e){let t=await this.sudo({password:e.old_password});return this.setPassword({sudo_token:t.sudo_token,new_password:e.new_password})}async sudo(e){return this.config.request(`${c.SUDO_URL}`,{method:"POST",withCredentials:!0,body:e})}async sendVerificationCodeToCurrentUser(e){return e.target="CUR_USER",this.config.request(c.VERIFICATION_URL,{method:"POST",body:e,withCredentials:!0,withCaptcha:!0})}async changeBoundProvider(e){return this.config.request(`${c.PROVIDER_LIST}/${e.provider_id}/trans`,{method:"POST",body:{provider_trans_token:e.trans_token},withCredentials:!0})}async setUserProfile(e){return this.config.request(c.USER_PRIFILE_URL,{method:"PATCH",body:e,withCredentials:!0})}async updateUserBasicInfo(e){return this.config.request(c.USER_BASIC_EDIT_URL,{method:"POST",withCredentials:!0,body:e})}async queryUserProfile(e){let t=new P(e);return this.config.request(`${c.USER_QUERY_URL}?${t.toString()}`,{method:"GET",withCredentials:!0})}setCustomSignFunc(e){this.getCustomSignTicketFn=e}async signInWithCustomTicket(){let e=this.getCustomSignTicketFn;if(!e)return Promise.reject({error:"failed_precondition",error_description:"please use setCustomSignFunc to set custom sign function"});let t=await e();return this.signInCustom({provider_id:"custom",ticket:t})}async resetPassword(e){return this.config.request(c.AUTH_RESET_PASSWORD,{method:"POST",body:e})}async authorize(e){return this.config.request(c.AUTHORIZE_URL,{method:"POST",withCredentials:!0,body:e})}async authorizeDevice(e){return this.config.request(c.AUTHORIZE_DEVICE_URL,{method:"POST",withCredentials:!0,body:e})}async deviceAuthorize(e){return this.config.request(c.AUTH_GET_DEVICE_CODE,{method:"POST",body:e,withCredentials:!0})}async authorizeInfo(e){let t=`${c.AUTHORIZE_INFO_URL}?${x.parseParamsToSearch(e)}`,r=!0,n=!1;return await this.hasLoginState()&&(n=!0,r=!1),this.config.request(t,{method:"GET",withBasicAuth:r,withCredentials:n})}async checkUsername(e){return this.config.request(c.CHECK_USERNAME,{method:"GET",body:e,withCredentials:!0})}async checkIfUserExist(e){let t=new P(e);return this.config.request(`${c.CHECK_IF_USER_EXIST}?${t.toString()}`,{method:"GET"})}async loginScope(){return this.config.credentialsClient.getScope()}async loginGroups(){return this.config.credentialsClient.getGroups()}async refreshTokenForce(e){let t=await this.config.credentialsClient.getCredentials();return await this.config.credentialsClient.refreshToken({...t,version:e?.version||"v1"})}async getCredentials(){return this.config.credentialsClient.getCredentials()}async getPublicKey(){return this.config.request(l.AUTH_PUBLIC_KEY,{method:"POST",body:{}})}async getEncryptParams(e){let{isEncrypt:t}=e;delete e.isEncrypt;let r=(0,p.I)(e),n=N(t);if(!n)return e;let o="",i="";try{let e=await this.getPublicKey();if(o=e.public_key,i=e.public_key_thumbprint,!o||!i)throw e}catch(e){throw e}return{params:n.getEncryptInfo({publicKey:o,payload:r}),public_key_thumbprint:i}}async getProviderSubType(){return this.config.request(c.GET_PROVIDER_TYPE,{method:"POST",body:{provider_id:"weda"}})}async verifyCaptchaData({token:e,key:t}){return this.config.request(c.VERIFY_CAPTCHA_DATA_URL,{method:"POST",body:{token:e,key:t},withCredentials:!1})}async createCaptchaData({state:e,redirect_uri:t}){return this.config.request(c.CAPTCHA_DATA_URL,{method:"POST",body:{state:e,redirect_uri:t},withCredentials:!1})}async getMiniProgramCode(e){return this.config.request(c.GET_MINIPROGRAM_QRCODE,{method:"POST",body:e})}async getMiniProgramQrCodeStatus(e){return this.config.request(c.GET_MINIPROGRAM_QRCODE_STATUS,{method:"POST",body:e})}async getUserBehaviorLog(e){let t=`${c.GET_USER_BEHAVIOR_LOG}?${{LOGIN:"query[action]=USER_LOGIN",MODIFY:"ne_query[action]=USER_LOGIN"}[e.type]}&limit=${e.limit}${e.page_token?`&page_token=${e.page_token}`:""}`;return this.config.request(t,{method:"GET",withCredentials:!0})}async modifyPassword(e){let t="",r="",n=N(!0);if(!n)throw Error("do not support encrypt, a encrypt util required.");try{let e=await this.getPublicKey();if(t=e.public_key,r=e.public_key_thumbprint,!t||!r)throw e}catch(e){throw e}let o=e.password?n.getEncryptInfo({publicKey:t,payload:e.password}):"",i=n.getEncryptInfo({publicKey:t,payload:e.new_password});return this.config.request(c.USER_BASIC_EDIT_URL,{method:"POST",withCredentials:!0,body:{user_id:e.user_id,encrypt_password:o,encrypt_new_password:i,public_key_thumbprint:r}})}async modifyPasswordWithoutLogin(e){let t="",r="",n=N(!0);if(!n)throw Error("do not support encrypt, a encrypt util required.");try{let e=await this.getPublicKey();if(t=e.public_key,r=e.public_key_thumbprint,!t||!r)throw e}catch(e){throw e}let o=e.password?n.getEncryptInfo({publicKey:t,payload:e.password}):"",i=n.getEncryptInfo({publicKey:t,payload:e.new_password});return this.config.request(l.AUTH_RESET_PASSWORD,{method:"POST",body:{username:e.username,password:o,new_password:i,public_key_thumbprint:r}})}}class q{constructor(e){let{apiOrigin:t,clientId:r,env:n,storage:o,request:i,baseRequest:a,anonymousSignInFunc:s,wxCloud:u}=e;this.oauth2client=new I({apiOrigin:t,clientId:r,env:n,storage:o,baseRequest:a||i,anonymousSignInFunc:s,wxCloud:u}),this.authApi=new x({credentialsClient:this.oauth2client,...e,request:i?this.oauth2client.request.bind(this.oauth2client):void 0})}}},16855:function(e,t,r){"use strict";let n;if(r.r(t),r.d(t,{getEncryptInfo:function(){return B}}),!globalThis.IS_MP_BUILD){let e="undefined"!=typeof globalThis?globalThis.navigator:window.globalThis;function o(e){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(e)}function i(e,t){return e&t}function a(e,t){return e|t}function s(e,t){return e^t}function u(e,t){return e&~t}var c,l,f,h,d,p,y,m="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function v(e){var t,r,n="";for(t=0;t+3<=e.length;t+=3)r=parseInt(e.substring(t,t+3),16),n+=m.charAt(r>>6)+m.charAt(63&r);for(t+1==e.length?(r=parseInt(e.substring(t,t+1),16),n+=m.charAt(r<<2)):t+2==e.length&&(r=parseInt(e.substring(t,t+2),16),n+=m.charAt(r>>2)+m.charAt((3&r)<<4));(3&n.length)>0;)n+="=";return n}function b(e){var t,r="",n=0,i=0;for(t=0;t<e.length&&"="!=e.charAt(t);++t){var a=m.indexOf(e.charAt(t));a<0||(0==n?(r+=o(a>>2),i=3&a,n=1):1==n?(r+=o(i<<2|a>>4),i=15&a,n=2):2==n?(r+=o(i)+o(a>>2),i=3&a,n=3):(r+=o(i<<2|a>>4)+o(15&a),n=0))}return 1==n&&(r+=o(i<<2)),r}var g={decode:function(e){if(void 0===c){var t,r="0123456789ABCDEF",n=" \f\n\r	\xa0\u2028\u2029";for(t=0,c={};t<16;++t)c[r.charAt(t)]=t;for(t=10,r=r.toLowerCase();t<16;++t)c[r.charAt(t)]=t;for(t=0;t<n.length;++t)c[n.charAt(t)]=-1}var o=[],i=0,a=0;for(t=0;t<e.length;++t){var s=e.charAt(t);if("="==s)break;if(-1!=(s=c[s])){if(void 0===s)throw Error("Illegal character at offset "+t);i|=s,++a>=2?(o[o.length]=i,i=0,a=0):i<<=4}}if(a)throw Error("Hex encoding incomplete: 4 bits missing");return o}},_={decode:function(e){if(void 0===l){var t,r="= \f\n\r	\xa0\u2028\u2029";for(t=0,l=Object.create(null);t<64;++t)l["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t)]=t;for(t=0;t<r.length;++t)l[r.charAt(t)]=-1}var n=[],o=0,i=0;for(t=0;t<e.length;++t){var a=e.charAt(t);if("="==a)break;if(-1!=(a=l[a])){if(void 0===a)throw Error("Illegal character at offset "+t);o|=a,++i>=4?(n[n.length]=o>>16,n[n.length]=o>>8&255,n[n.length]=255&o,o=0,i=0):o<<=6}}switch(i){case 1:throw Error("Base64 encoding incomplete: at least 2 bits missing");case 2:n[n.length]=o>>10;break;case 3:n[n.length]=o>>16,n[n.length]=o>>8&255}return n},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(e){var t=_.re.exec(e);if(t){if(t[1])e=t[1];else if(t[2])e=t[2];else throw Error("RegExp out of sync")}return _.decode(e)}};class t{constructor(e){this.buf=[+e||0]}mulAdd(e,t){var r,n,o=this.buf,i=o.length;for(r=0;r<i;++r)(n=o[r]*e+t)<1e13?t=0:(t=0|n/1e13,n-=1e13*t),o[r]=n;t>0&&(o[r]=t)}sub(e){var t,r,n=this.buf,o=n.length;for(t=0;t<o;++t)(r=n[t]-e)<0?(r+=1e13,e=1):e=0,n[t]=r;for(;0===n[n.length-1];)n.pop()}toString(e){if(10!=(e||10))throw Error("only base 10 is supported");for(var t=this.buf,r=t[t.length-1].toString(),n=t.length-2;n>=0;--n)r+=(1e13+t[n]).toString().substring(1);return r}valueOf(){for(var e=this.buf,t=0,r=e.length-1;r>=0;--r)t=1e13*t+e[r];return t}simplify(){var e=this.buf;return 1==e.length?e[0]:this}}var w=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,S=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function E(e,t){return e.length>t&&(e=e.substring(0,t)+"…"),e}class r{constructor(e,t){this.hexDigits="0123456789ABCDEF",e instanceof r?(this.enc=e.enc,this.pos=e.pos):(this.enc=e,this.pos=t)}get(e){if(void 0===e&&(e=this.pos++),e>=this.enc.length)throw Error("Requesting byte offset "+e+" on a stream of length "+this.enc.length);return"string"==typeof this.enc?this.enc.charCodeAt(e):this.enc[e]}hexByte(e){return this.hexDigits.charAt(e>>4&15)+this.hexDigits.charAt(15&e)}hexDump(e,t,r){for(var n="",o=e;o<t;++o)if(n+=this.hexByte(this.get(o)),!0!==r)switch(15&o){case 7:n+="  ";break;case 15:n+="\n";break;default:n+=" "}return n}isASCII(e,t){for(var r=e;r<t;++r){var n=this.get(r);if(n<32||n>176)return!1}return!0}parseStringISO(e,t){for(var r="",n=e;n<t;++n)r+=String.fromCharCode(this.get(n));return r}parseStringUTF(e,t){for(var r="",n=e;n<t;){var o=this.get(n++);o<128?r+=String.fromCharCode(o):o>191&&o<224?r+=String.fromCharCode((31&o)<<6|63&this.get(n++)):r+=String.fromCharCode((15&o)<<12|(63&this.get(n++))<<6|63&this.get(n++))}return r}parseStringBMP(e,t){for(var r="",n=e;n<t;)r+=String.fromCharCode(this.get(n++)<<8|this.get(n++));return r}parseTime(e,t,r){var n=this.parseStringISO(e,t),o=(r?w:S).exec(n);return o?(r&&(o[1]=+o[1],o[1]+=70>+o[1]?2e3:1900),n=o[1]+"-"+o[2]+"-"+o[3]+" "+o[4],o[5]&&(n+=":"+o[5],o[6]&&(n+=":"+o[6],o[7]&&(n+="."+o[7]))),o[8]&&(n+=" UTC","Z"!=o[8]&&(n+=o[8],o[9]&&(n+=":"+o[9]))),n):"Unrecognized time: "+n}parseInteger(e,r){for(var n,o=this.get(e),i=o>127,a=i?255:0,s="";o==a&&++e<r;)o=this.get(e);if(0==(n=r-e))return i?-1:0;if(n>4){for(s=o,n<<=3;((+s^a)&128)==0;)s=+s<<1,--n;s="("+n+" bit)\n"}i&&(o-=256);for(var u=new t(o),c=e+1;c<r;++c)u.mulAdd(256,this.get(c));return s+u.toString()}parseBitString(e,t,r){for(var n=this.get(e),o="("+((t-e-1<<3)-n)+" bit)\n",i="",a=e+1;a<t;++a){for(var s=this.get(a),u=a==t-1?n:0,c=7;c>=u;--c)i+=s>>c&1?"1":"0";if(i.length>r)return o+E(i,r)}return o+i}parseOctetString(e,t,r){if(this.isASCII(e,t))return E(this.parseStringISO(e,t),r);var n=t-e,o="("+n+" byte)\n";n>(r/=2)&&(t=e+r);for(var i=e;i<t;++i)o+=this.hexByte(this.get(i));return n>r&&(o+="…"),o}parseOID(e,r,n){for(var o="",i=new t,a=0,s=e;s<r;++s){var u=this.get(s);if(i.mulAdd(128,127&u),a+=7,!(128&u)){if(""===o){if((i=i.simplify())instanceof t)i.sub(80),o="2."+i.toString();else{var c=i<80?i<40?0:1:2;o=c+"."+(i-40*c)}}else o+="."+i.toString();if(o.length>n)return E(o,n);i=new t,a=0}}return a>0&&(o+=".incomplete"),o}}class W{constructor(e,t,r,n,o){if(!(n instanceof F))throw Error("Invalid tag value.");this.stream=e,this.header=t,this.length=r,this.tag=n,this.sub=o}typeName(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}}content(e){if(void 0===this.tag)return null;void 0===e&&(e=1/0);var t=this.posContent(),r=Math.abs(this.length);if(!this.tag.isUniversal())return null!==this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(t,t+r,e);switch(this.tag.tagNumber){case 1:return 0===this.stream.get(t)?"false":"true";case 2:return this.stream.parseInteger(t,t+r);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(t,t+r,e);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(t,t+r,e);case 6:return this.stream.parseOID(t,t+r,e);case 16:case 17:if(null!==this.sub)return"("+this.sub.length+" elem)";return"(no elem)";case 12:return E(this.stream.parseStringUTF(t,t+r),e);case 18:case 19:case 20:case 21:case 22:case 26:return E(this.stream.parseStringISO(t,t+r),e);case 30:return E(this.stream.parseStringBMP(t,t+r),e);case 23:case 24:return this.stream.parseTime(t,t+r,23==this.tag.tagNumber)}return null}toString(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(null===this.sub?"null":this.sub.length)+"]"}toPrettyString(e){void 0===e&&(e="");var t=e+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(t+="+"),t+=this.length,this.tag.tagConstructed?t+=" (constructed)":this.tag.isUniversal()&&(3==this.tag.tagNumber||4==this.tag.tagNumber)&&null!==this.sub&&(t+=" (encapsulates)"),t+="\n",null!==this.sub){e+="  ";for(var r=0,n=this.sub.length;r<n;++r)t+=this.sub[r].toPrettyString(e)}return t}posStart(){return this.stream.pos}posContent(){return this.stream.pos+this.header}posEnd(){return this.stream.pos+this.header+Math.abs(this.length)}toHexString(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)}static decodeLength(e){var t=e.get(),r=127&t;if(r==t)return r;if(r>6)throw Error("Length over 48 bits not supported at position "+(e.pos-1));if(0===r)return null;t=0;for(var n=0;n<r;++n)t=256*t+e.get();return t}getHexStringValue(){var e=this.toHexString(),t=2*this.header,r=2*this.length;return e.substr(t,r)}static decode(e){t=e instanceof r?e:new r(e,0);var t,n=new r(t),o=new F(t),i=W.decodeLength(t),a=t.pos,s=a-n.pos,u=null,c=function(){var e=[];if(null!==i){for(var r=a+i;t.pos<r;)e[e.length]=W.decode(t);if(t.pos!=r)throw Error("Content size is not correct for container starting at offset "+a)}else try{for(;;){var n=W.decode(t);if(n.tag.isEOC())break;e[e.length]=n}i=a-t.pos}catch(e){throw Error("Exception while decoding undefined length content: "+e)}return e};if(o.tagConstructed)u=c();else if(o.isUniversal()&&(3==o.tagNumber||4==o.tagNumber))try{if(3==o.tagNumber&&0!=t.get())throw Error("BIT STRINGs with unused bits cannot encapsulate.");u=c();for(var l=0;l<u.length;++l)if(u[l].tag.isEOC())throw Error("EOC is not supposed to be actual content.")}catch(e){u=null}if(null===u){if(null===i)throw Error("We can't skip over an invalid tag with undefined length at offset "+a);t.pos=a+Math.abs(i)}return new W(n,s,i,o,u)}}class F{constructor(e){var r=e.get();if(this.tagClass=r>>6,this.tagConstructed=(32&r)!=0,this.tagNumber=31&r,31==this.tagNumber){var n=new t;do r=e.get(),n.mulAdd(128,127&r);while(128&r);this.tagNumber=n.simplify()}}isUniversal(){return 0===this.tagClass}isEOC(){return 0===this.tagClass&&0===this.tagNumber}}var T=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],I=67108864/T[T.length-1];class B{constructor(e,t,r){null!=e&&("number"==typeof e?this.fromNumber(e,t,r):null==t&&"string"!=typeof e?this.fromString(e,256):this.fromString(e,t))}toString(e){if(this.s<0)return"-"+this.negate().toString(e);if(16==e)t=4;else if(8==e)t=3;else if(2==e)t=1;else if(32==e)t=5;else{if(4!=e)return this.toRadix(e);t=2}var t,r,n=(1<<t)-1,i=!1,a="",s=this.t,u=this.DB-s*this.DB%t;if(s-- >0)for(u<this.DB&&(r=this[s]>>u)>0&&(i=!0,a=o(r));s>=0;)u<t?r=(this[s]&(1<<u)-1)<<t-u|this[--s]>>(u+=this.DB-t):(r=this[s]>>(u-=t)&n,u<=0&&(u+=this.DB,--s)),r>0&&(i=!0),i&&(a+=o(r));return i?a:"0"}negate(){var e=C();return B.ZERO.subTo(this,e),e}abs(){return this.s<0?this.negate():this}compareTo(e){var t=this.s-e.s;if(0!=t)return t;var r=this.t;if(0!=(t=r-e.t))return this.s<0?-t:t;for(;--r>=0;)if(0!=(t=this[r]-e[r]))return t;return 0}bitLength(){return this.t<=0?0:this.DB*(this.t-1)+k(this[this.t-1]^this.s&this.DM)}mod(e){var t=C();return this.abs().divRemTo(e,null,t),this.s<0&&t.compareTo(B.ZERO)>0&&e.subTo(t,t),t}modPowInt(e,t){var r;return r=e<256||t.isEven()?new R(t):new A(t),this.exp(e,r)}clone(){var e=C();return this.copyTo(e),e}intValue(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return -1}else if(1==this.t)return this[0];else if(0==this.t)return 0;return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}byteValue(){return 0==this.t?this.s:this[0]<<24>>24}shortValue(){return 0==this.t?this.s:this[0]<<16>>16}signum(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1}toByteArray(){var e,t=this.t,r=[];r[0]=this.s;var n=this.DB-t*this.DB%8,o=0;if(t-- >0)for(n<this.DB&&(e=this[t]>>n)!=(this.s&this.DM)>>n&&(r[o++]=e|this.s<<this.DB-n);t>=0;)n<8?e=(this[t]&(1<<n)-1)<<8-n|this[--t]>>(n+=this.DB-8):(e=this[t]>>(n-=8)&255,n<=0&&(n+=this.DB,--t)),(128&e)!=0&&(e|=-256),0==o&&(128&this.s)!=(128&e)&&++o,(o>0||e!=this.s)&&(r[o++]=e);return r}equals(e){return 0==this.compareTo(e)}min(e){return 0>this.compareTo(e)?this:e}max(e){return this.compareTo(e)>0?this:e}and(e){var t=C();return this.bitwiseTo(e,i,t),t}or(e){var t=C();return this.bitwiseTo(e,a,t),t}xor(e){var t=C();return this.bitwiseTo(e,s,t),t}andNot(e){var t=C();return this.bitwiseTo(e,u,t),t}not(){for(var e=C(),t=0;t<this.t;++t)e[t]=this.DM&~this[t];return e.t=this.t,e.s=~this.s,e}shiftLeft(e){var t=C();return e<0?this.rShiftTo(-e,t):this.lShiftTo(e,t),t}shiftRight(e){var t=C();return e<0?this.lShiftTo(-e,t):this.rShiftTo(e,t),t}getLowestSetBit(){for(var e=0;e<this.t;++e)if(0!=this[e])return e*this.DB+function(e){if(0==e)return -1;var t=0;return(65535&e)==0&&(e>>=16,t+=16),(255&e)==0&&(e>>=8,t+=8),(15&e)==0&&(e>>=4,t+=4),(3&e)==0&&(e>>=2,t+=2),(1&e)==0&&++t,t}(this[e]);return this.s<0?this.t*this.DB:-1}bitCount(){for(var e=0,t=this.s&this.DM,r=0;r<this.t;++r)e+=function(e){for(var t=0;0!=e;)e&=e-1,++t;return t}(this[r]^t);return e}testBit(e){var t=Math.floor(e/this.DB);return t>=this.t?0!=this.s:(this[t]&1<<e%this.DB)!=0}setBit(e){return this.changeBit(e,a)}clearBit(e){return this.changeBit(e,u)}flipBit(e){return this.changeBit(e,s)}add(e){var t=C();return this.addTo(e,t),t}subtract(e){var t=C();return this.subTo(e,t),t}multiply(e){var t=C();return this.multiplyTo(e,t),t}divide(e){var t=C();return this.divRemTo(e,t,null),t}remainder(e){var t=C();return this.divRemTo(e,null,t),t}divideAndRemainder(e){var t=C(),r=C();return this.divRemTo(e,t,r),[t,r]}modPow(e,t){var r,n,o,i,a=e.bitLength(),s=j(1);if(a<=0)return s;r=a<18?1:a<48?3:a<144?4:a<768?5:6,n=a<8?new R(t):t.isEven()?new P(t):new A(t);var u=[],c=3,l=r-1,f=(1<<r)-1;if(u[1]=n.convert(this),r>1){var h=C();for(n.sqrTo(u[1],h);c<=f;)u[c]=C(),n.mulTo(h,u[c-2],u[c]),c+=2}var d=e.t-1,p=!0,y=C();for(a=k(e[d])-1;d>=0;){for(a>=l?o=e[d]>>a-l&f:(o=(e[d]&(1<<a+1)-1)<<l-a,d>0&&(o|=e[d-1]>>this.DB+a-l)),c=r;(1&o)==0;)o>>=1,--c;if((a-=c)<0&&(a+=this.DB,--d),p)u[o].copyTo(s),p=!1;else{for(;c>1;)n.sqrTo(s,y),n.sqrTo(y,s),c-=2;c>0?n.sqrTo(s,y):(i=s,s=y,y=i),n.mulTo(y,u[o],s)}for(;d>=0&&(e[d]&1<<a)==0;)n.sqrTo(s,y),i=s,s=y,y=i,--a<0&&(a=this.DB-1,--d)}return n.revert(s)}modInverse(e){var t=e.isEven();if(this.isEven()&&t||0==e.signum())return B.ZERO;for(var r=e.clone(),n=this.clone(),o=j(1),i=j(0),a=j(0),s=j(1);0!=r.signum();){for(;r.isEven();)r.rShiftTo(1,r),t?(o.isEven()&&i.isEven()||(o.addTo(this,o),i.subTo(e,i)),o.rShiftTo(1,o)):i.isEven()||i.subTo(e,i),i.rShiftTo(1,i);for(;n.isEven();)n.rShiftTo(1,n),t?(a.isEven()&&s.isEven()||(a.addTo(this,a),s.subTo(e,s)),a.rShiftTo(1,a)):s.isEven()||s.subTo(e,s),s.rShiftTo(1,s);r.compareTo(n)>=0?(r.subTo(n,r),t&&o.subTo(a,o),i.subTo(s,i)):(n.subTo(r,n),t&&a.subTo(o,a),s.subTo(i,s))}return 0!=n.compareTo(B.ONE)?B.ZERO:s.compareTo(e)>=0?s.subtract(e):0>s.signum()&&(s.addTo(e,s),0>s.signum())?s.add(e):s}pow(e){return this.exp(e,new O)}gcd(e){var t=this.s<0?this.negate():this.clone(),r=e.s<0?e.negate():e.clone();if(0>t.compareTo(r)){var n=t;t=r,r=n}var o=t.getLowestSetBit(),i=r.getLowestSetBit();if(i<0)return t;for(o<i&&(i=o),i>0&&(t.rShiftTo(i,t),r.rShiftTo(i,r));t.signum()>0;)(o=t.getLowestSetBit())>0&&t.rShiftTo(o,t),(o=r.getLowestSetBit())>0&&r.rShiftTo(o,r),t.compareTo(r)>=0?(t.subTo(r,t),t.rShiftTo(1,t)):(r.subTo(t,r),r.rShiftTo(1,r));return i>0&&r.lShiftTo(i,r),r}isProbablePrime(e){var t,r=this.abs();if(1==r.t&&r[0]<=T[T.length-1]){for(t=0;t<T.length;++t)if(r[0]==T[t])return!0;return!1}if(r.isEven())return!1;for(t=1;t<T.length;){for(var n=T[t],o=t+1;o<T.length&&n<I;)n*=T[o++];for(n=r.modInt(n);t<o;)if(n%T[t++]==0)return!1}return r.millerRabin(e)}copyTo(e){for(var t=this.t-1;t>=0;--t)e[t]=this[t];e.t=this.t,e.s=this.s}fromInt(e){this.t=1,this.s=e<0?-1:0,e>0?this[0]=e:e<-1?this[0]=e+this.DV:this.t=0}fromString(e,t){if(16==t)r=4;else if(8==t)r=3;else if(256==t)r=8;else if(2==t)r=1;else if(32==t)r=5;else if(4==t)r=2;else{this.fromRadix(e,t);return}this.t=0,this.s=0;for(var r,n=e.length,o=!1,i=0;--n>=0;){var a=8==r?255&+e[n]:q(e,n);if(a<0){"-"==e.charAt(n)&&(o=!0);continue}o=!1,0==i?this[this.t++]=a:i+r>this.DB?(this[this.t-1]|=(a&(1<<this.DB-i)-1)<<i,this[this.t++]=a>>this.DB-i):this[this.t-1]|=a<<i,(i+=r)>=this.DB&&(i-=this.DB)}8==r&&(128&+e[0])!=0&&(this.s=-1,i>0&&(this[this.t-1]|=(1<<this.DB-i)-1<<i)),this.clamp(),o&&B.ZERO.subTo(this,this)}clamp(){for(var e=this.s&this.DM;this.t>0&&this[this.t-1]==e;)--this.t}dlShiftTo(e,t){var r;for(r=this.t-1;r>=0;--r)t[r+e]=this[r];for(r=e-1;r>=0;--r)t[r]=0;t.t=this.t+e,t.s=this.s}drShiftTo(e,t){for(var r=e;r<this.t;++r)t[r-e]=this[r];t.t=Math.max(this.t-e,0),t.s=this.s}lShiftTo(e,t){for(var r=e%this.DB,n=this.DB-r,o=(1<<n)-1,i=Math.floor(e/this.DB),a=this.s<<r&this.DM,s=this.t-1;s>=0;--s)t[s+i+1]=this[s]>>n|a,a=(this[s]&o)<<r;for(var s=i-1;s>=0;--s)t[s]=0;t[i]=a,t.t=this.t+i+1,t.s=this.s,t.clamp()}rShiftTo(e,t){t.s=this.s;var r=Math.floor(e/this.DB);if(r>=this.t){t.t=0;return}var n=e%this.DB,o=this.DB-n,i=(1<<n)-1;t[0]=this[r]>>n;for(var a=r+1;a<this.t;++a)t[a-r-1]|=(this[a]&i)<<o,t[a-r]=this[a]>>n;n>0&&(t[this.t-r-1]|=(this.s&i)<<o),t.t=this.t-r,t.clamp()}subTo(e,t){for(var r=0,n=0,o=Math.min(e.t,this.t);r<o;)n+=this[r]-e[r],t[r++]=n&this.DM,n>>=this.DB;if(e.t<this.t){for(n-=e.s;r<this.t;)n+=this[r],t[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<e.t;)n-=e[r],t[r++]=n&this.DM,n>>=this.DB;n-=e.s}t.s=n<0?-1:0,n<-1?t[r++]=this.DV+n:n>0&&(t[r++]=n),t.t=r,t.clamp()}multiplyTo(e,t){var r=this.abs(),n=e.abs(),o=r.t;for(t.t=o+n.t;--o>=0;)t[o]=0;for(o=0;o<n.t;++o)t[o+r.t]=r.am(0,n[o],t,o,0,r.t);t.s=0,t.clamp(),this.s!=e.s&&B.ZERO.subTo(t,t)}squareTo(e){for(var t=this.abs(),r=e.t=2*t.t;--r>=0;)e[r]=0;for(r=0;r<t.t-1;++r){var n=t.am(r,t[r],e,2*r,0,1);(e[r+t.t]+=t.am(r+1,2*t[r],e,2*r+1,n,t.t-r-1))>=t.DV&&(e[r+t.t]-=t.DV,e[r+t.t+1]=1)}e.t>0&&(e[e.t-1]+=t.am(r,t[r],e,2*r,0,1)),e.s=0,e.clamp()}divRemTo(e,t,r){var n=e.abs();if(!(n.t<=0)){var o=this.abs();if(o.t<n.t){null!=t&&t.fromInt(0),null!=r&&this.copyTo(r);return}null==r&&(r=C());var i=C(),a=this.s,s=e.s,u=this.DB-k(n[n.t-1]);u>0?(n.lShiftTo(u,i),o.lShiftTo(u,r)):(n.copyTo(i),o.copyTo(r));var c=i.t,l=i[c-1];if(0!=l){var f=l*(1<<this.F1)+(c>1?i[c-2]>>this.F2:0),h=this.FV/f,d=(1<<this.F1)/f,p=1<<this.F2,y=r.t,m=y-c,v=null==t?C():t;for(i.dlShiftTo(m,v),r.compareTo(v)>=0&&(r[r.t++]=1,r.subTo(v,r)),B.ONE.dlShiftTo(c,v),v.subTo(i,i);i.t<c;)i[i.t++]=0;for(;--m>=0;){var b=r[--y]==l?this.DM:Math.floor(r[y]*h+(r[y-1]+p)*d);if((r[y]+=i.am(0,b,r,m,0,c))<b)for(i.dlShiftTo(m,v),r.subTo(v,r);r[y]<--b;)r.subTo(v,r)}null!=t&&(r.drShiftTo(c,t),a!=s&&B.ZERO.subTo(t,t)),r.t=c,r.clamp(),u>0&&r.rShiftTo(u,r),a<0&&B.ZERO.subTo(r,r)}}}invDigit(){if(this.t<1)return 0;var e=this[0];if((1&e)==0)return 0;var t=3&e;return(t=(t=(t=(t=t*(2-(15&e)*t)&15)*(2-(255&e)*t)&255)*(2-((65535&e)*t&65535))&65535)*(2-e*t%this.DV)%this.DV)>0?this.DV-t:-t}isEven(){return(this.t>0?1&this[0]:this.s)==0}exp(e,t){if(e>4294967295||e<1)return B.ONE;var r=C(),n=C(),o=t.convert(this),i=k(e)-1;for(o.copyTo(r);--i>=0;)if(t.sqrTo(r,n),(e&1<<i)>0)t.mulTo(n,o,r);else{var a=r;r=n,n=a}return t.revert(r)}chunkSize(e){return Math.floor(Math.LN2*this.DB/Math.log(e))}toRadix(e){if(null==e&&(e=10),0==this.signum()||e<2||e>36)return"0";var t=this.chunkSize(e),r=Math.pow(e,t),n=j(r),o=C(),i=C(),a="";for(this.divRemTo(n,o,i);o.signum()>0;)a=(r+i.intValue()).toString(e).substr(1)+a,o.divRemTo(n,o,i);return i.intValue().toString(e)+a}fromRadix(e,t){this.fromInt(0),null==t&&(t=10);for(var r=this.chunkSize(t),n=Math.pow(t,r),o=!1,i=0,a=0,s=0;s<e.length;++s){var u=q(e,s);if(u<0){"-"==e.charAt(s)&&0==this.signum()&&(o=!0);continue}a=t*a+u,++i>=r&&(this.dMultiply(n),this.dAddOffset(a,0),i=0,a=0)}i>0&&(this.dMultiply(Math.pow(t,i)),this.dAddOffset(a,0)),o&&B.ZERO.subTo(this,this)}fromNumber(e,t,r){if("number"==typeof t){if(e<2)this.fromInt(1);else for(this.fromNumber(e,r),this.testBit(e-1)||this.bitwiseTo(B.ONE.shiftLeft(e-1),a,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(t);)this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(B.ONE.shiftLeft(e-1),this)}else{var n=[],o=7&e;n.length=(e>>3)+1,t.nextBytes(n),o>0?n[0]&=(1<<o)-1:n[0]=0,this.fromString(n,256)}}bitwiseTo(e,t,r){var n,o,i=Math.min(e.t,this.t);for(n=0;n<i;++n)r[n]=t(this[n],e[n]);if(e.t<this.t){for(o=e.s&this.DM,n=i;n<this.t;++n)r[n]=t(this[n],o);r.t=this.t}else{for(o=this.s&this.DM,n=i;n<e.t;++n)r[n]=t(o,e[n]);r.t=e.t}r.s=t(this.s,e.s),r.clamp()}changeBit(e,t){var r=B.ONE.shiftLeft(e);return this.bitwiseTo(r,t,r),r}addTo(e,t){for(var r=0,n=0,o=Math.min(e.t,this.t);r<o;)n+=this[r]+e[r],t[r++]=n&this.DM,n>>=this.DB;if(e.t<this.t){for(n+=e.s;r<this.t;)n+=this[r],t[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<e.t;)n+=e[r],t[r++]=n&this.DM,n>>=this.DB;n+=e.s}t.s=n<0?-1:0,n>0?t[r++]=n:n<-1&&(t[r++]=this.DV+n),t.t=r,t.clamp()}dMultiply(e){this[this.t]=this.am(0,e-1,this,0,0,this.t),++this.t,this.clamp()}dAddOffset(e,t){if(0!=e){for(;this.t<=t;)this[this.t++]=0;for(this[t]+=e;this[t]>=this.DV;)this[t]-=this.DV,++t>=this.t&&(this[this.t++]=0),++this[t]}}multiplyLowerTo(e,t,r){var n=Math.min(this.t+e.t,t);for(r.s=0,r.t=n;n>0;)r[--n]=0;for(var o=r.t-this.t;n<o;++n)r[n+this.t]=this.am(0,e[n],r,n,0,this.t);for(var o=Math.min(e.t,t);n<o;++n)this.am(0,e[n],r,n,0,t-n);r.clamp()}multiplyUpperTo(e,t,r){--t;var n=r.t=this.t+e.t-t;for(r.s=0;--n>=0;)r[n]=0;for(n=Math.max(t-this.t,0);n<e.t;++n)r[this.t+n-t]=this.am(t-n,e[n],r,0,0,this.t+n-t);r.clamp(),r.drShiftTo(1,r)}modInt(e){if(e<=0)return 0;var t=this.DV%e,r=this.s<0?e-1:0;if(this.t>0){if(0==t)r=this[0]%e;else for(var n=this.t-1;n>=0;--n)r=(t*r+this[n])%e}return r}millerRabin(e){var t=this.subtract(B.ONE),r=t.getLowestSetBit();if(r<=0)return!1;var n=t.shiftRight(r);(e=e+1>>1)>T.length&&(e=T.length);for(var o=C(),i=0;i<e;++i){o.fromInt(T[Math.floor(Math.random()*T.length)]);var a=o.modPow(n,this);if(0!=a.compareTo(B.ONE)&&0!=a.compareTo(t)){for(var s=1;s++<r&&0!=a.compareTo(t);)if(0==(a=a.modPowInt(2,this)).compareTo(B.ONE))return!1;if(0!=a.compareTo(t))return!1}}return!0}square(){var e=C();return this.squareTo(e),e}gcda(e,t){var r=this.s<0?this.negate():this.clone(),n=e.s<0?e.negate():e.clone();if(0>r.compareTo(n)){var o=r;r=n,n=o}var i=r.getLowestSetBit(),a=n.getLowestSetBit();if(a<0){t(r);return}i<a&&(a=i),a>0&&(r.rShiftTo(a,r),n.rShiftTo(a,n));var s=()=>{(i=r.getLowestSetBit())>0&&r.rShiftTo(i,r),(i=n.getLowestSetBit())>0&&n.rShiftTo(i,n),r.compareTo(n)>=0?(r.subTo(n,r),r.rShiftTo(1,r)):(n.subTo(r,n),n.rShiftTo(1,n)),r.signum()>0?setTimeout(s,0):(a>0&&n.lShiftTo(a,n),setTimeout(function(){t(n)},0))};setTimeout(s,10)}fromNumberAsync(e,t,r,n){if("number"==typeof t){if(e<2)this.fromInt(1);else{this.fromNumber(e,r),this.testBit(e-1)||this.bitwiseTo(B.ONE.shiftLeft(e-1),a,this),this.isEven()&&this.dAddOffset(1,0);var o=this,i=function(){o.dAddOffset(2,0),o.bitLength()>e&&o.subTo(B.ONE.shiftLeft(e-1),o),o.isProbablePrime(t)?setTimeout(function(){n()},0):setTimeout(i,0)};setTimeout(i,0)}}else{var s=[],u=7&e;s.length=(e>>3)+1,t.nextBytes(s),u>0?s[0]&=(1<<u)-1:s[0]=0,this.fromString(s,256)}}}var O=function(){function e(){}return e.prototype.convert=function(e){return e},e.prototype.revert=function(e){return e},e.prototype.mulTo=function(e,t,r){e.multiplyTo(t,r)},e.prototype.sqrTo=function(e,t){e.squareTo(t)},e}(),R=function(){function e(e){this.m=e}return e.prototype.convert=function(e){return e.s<0||e.compareTo(this.m)>=0?e.mod(this.m):e},e.prototype.revert=function(e){return e},e.prototype.reduce=function(e){e.divRemTo(this.m,null,e)},e.prototype.mulTo=function(e,t,r){e.multiplyTo(t,r),this.reduce(r)},e.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)},e}(),A=function(){function e(e){this.m=e,this.mp=e.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<e.DB-15)-1,this.mt2=2*e.t}return e.prototype.convert=function(e){var t=C();return e.abs().dlShiftTo(this.m.t,t),t.divRemTo(this.m,null,t),e.s<0&&t.compareTo(B.ZERO)>0&&this.m.subTo(t,t),t},e.prototype.revert=function(e){var t=C();return e.copyTo(t),this.reduce(t),t},e.prototype.reduce=function(e){for(;e.t<=this.mt2;)e[e.t++]=0;for(var t=0;t<this.m.t;++t){var r=32767&e[t],n=r*this.mpl+((r*this.mph+(e[t]>>15)*this.mpl&this.um)<<15)&e.DM;for(r=t+this.m.t,e[r]+=this.m.am(0,n,e,t,0,this.m.t);e[r]>=e.DV;)e[r]-=e.DV,e[++r]++}e.clamp(),e.drShiftTo(this.m.t,e),e.compareTo(this.m)>=0&&e.subTo(this.m,e)},e.prototype.mulTo=function(e,t,r){e.multiplyTo(t,r),this.reduce(r)},e.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)},e}(),P=function(){function e(e){this.m=e,this.r2=C(),this.q3=C(),B.ONE.dlShiftTo(2*e.t,this.r2),this.mu=this.r2.divide(e)}return e.prototype.convert=function(e){if(e.s<0||e.t>2*this.m.t)return e.mod(this.m);if(0>e.compareTo(this.m))return e;var t=C();return e.copyTo(t),this.reduce(t),t},e.prototype.revert=function(e){return e},e.prototype.reduce=function(e){for(e.drShiftTo(this.m.t-1,this.r2),e.t>this.m.t+1&&(e.t=this.m.t+1,e.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);0>e.compareTo(this.r2);)e.dAddOffset(1,this.m.t+1);for(e.subTo(this.r2,e);e.compareTo(this.m)>=0;)e.subTo(this.m,e)},e.prototype.mulTo=function(e,t,r){e.multiplyTo(t,r),this.reduce(r)},e.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)},e}();function C(){return new B(null)}function N(e,t){return new B(e,t)}void 0!==e&&e?.appName=="Microsoft Internet Explorer"?(B.prototype.am=function(e,t,r,n,o,i){for(var a=32767&t,s=t>>15;--i>=0;){var u=32767&this[e],c=this[e++]>>15,l=s*u+c*a;o=((u=a*u+((32767&l)<<15)+r[n]+(1073741823&o))>>>30)+(l>>>15)+s*c+(o>>>30),r[n++]=1073741823&u}return o},f=30):e?.appName!="Netscape"?(B.prototype.am=function(e,t,r,n,o,i){for(;--i>=0;){var a=t*this[e++]+r[n]+o;o=Math.floor(a/67108864),r[n++]=67108863&a}return o},f=26):(B.prototype.am=function(e,t,r,n,o,i){for(var a=16383&t,s=t>>14;--i>=0;){var u=16383&this[e],c=this[e++]>>14,l=s*u+c*a;o=((u=a*u+((16383&l)<<14)+r[n]+o)>>28)+(l>>14)+s*c,r[n++]=268435455&u}return o},f=28),B.prototype.DB=f,B.prototype.DM=(1<<f)-1,B.prototype.DV=1<<f,B.prototype.FV=4503599627370496,B.prototype.F1=52-f,B.prototype.F2=2*f-52;var x=[];for(d=0,h=48;d<=9;++d)x[h++]=d;for(d=10,h=97;d<36;++d)x[h++]=d;for(d=10,h=65;d<36;++d)x[h++]=d;function q(e,t){var r=x[e.charCodeAt(t)];return null==r?-1:r}function j(e){var t=C();return t.fromInt(e),t}function k(e){var t,r=1;return 0!=(t=e>>>16)&&(e=t,r+=16),0!=(t=e>>8)&&(e=t,r+=8),0!=(t=e>>4)&&(e=t,r+=4),0!=(t=e>>2)&&(e=t,r+=2),0!=(t=e>>1)&&(e=t,r+=1),r}B.ZERO=j(0),B.ONE=j(1);class G{constructor(){this.i=0,this.j=0,this.S=[]}init(e){var t,r,n;for(t=0;t<256;++t)this.S[t]=t;for(t=0,r=0;t<256;++t)r=r+this.S[t]+e[t%e.length]&255,n=this.S[t],this.S[t]=this.S[r],this.S[r]=n;this.i=0,this.j=0}next(){var e;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,e=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=e,this.S[e+this.S[this.i]&255]}}var D=null;D=[],y=0;var L=void 0;if(window?.crypto&&window?.crypto.getRandomValues){var U=new Uint32Array(256);for(window?.crypto.getRandomValues(U),L=0;L<U.length;++L)D[y++]=255&U[L]}var M=function(e){if(this.count=this.count||0,this.count>=256||y>=256){window?.removeEventListener?window?.removeEventListener("mousemove",M,!1):window?.detachEvent&&window?.detachEvent("onmousemove",M);return}try{var t=e.x+e.y;D[y++]=255&t,this.count+=1}catch(e){}};window?.addEventListener?window?.addEventListener("mousemove",M,!1):window?.attachEvent&&window?.attachEvent("onmousemove",M);class V{constructor(){}nextBytes(e){for(var t=0;t<e.length;++t)e[t]=function(){if(null==p){for(p=new G;y<256;){var e=Math.floor(65536*Math.random());D[y++]=255&e}for(p.init(D),y=0;y<D.length;++y)D[y]=0;y=0}return p.next()}()}}class H{constructor(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}doPublic(e){return e.modPowInt(this.e,this.n)}doPrivate(e){if(null==this.p||null==this.q)return e.modPow(this.d,this.n);for(var t=e.mod(this.p).modPow(this.dmp1,this.p),r=e.mod(this.q).modPow(this.dmq1,this.q);0>t.compareTo(r);)t=t.add(this.p);return t.subtract(r).multiply(this.coeff).mod(this.p).multiply(this.q).add(r)}setPublic(e,t){null!=e&&null!=t&&e.length>0&&t.length>0?(this.n=N(e,16),this.e=parseInt(t,16)):console.error("Invalid RSA public key")}encrypt(e){var t=function(e,t){if(t<e.length+11)return console.error("Message too long for RSA"),null;for(var r=[],n=e.length-1;n>=0&&t>0;){var o=e.charCodeAt(n--);o<128?r[--t]=o:o>127&&o<2048?(r[--t]=63&o|128,r[--t]=o>>6|192):(r[--t]=63&o|128,r[--t]=o>>6&63|128,r[--t]=o>>12|224)}r[--t]=0;for(var i=new V,a=[];t>2;){for(a[0]=0;0==a[0];)i.nextBytes(a);r[--t]=a[0]}return r[--t]=2,r[--t]=0,new B(r)}(e,this.n.bitLength()+7>>3);if(null==t)return null;var r=this.doPublic(t);if(null==r)return null;var n=r.toString(16);return(1&n.length)==0?n:"0"+n}encryptLong(e){var t=this,r=(this.n.bitLength()+7>>3)-11;try{var n="";if(e.length>r)return e.match(/.{1,117}/g).forEach(function(e){var r=t.encrypt(e);n+=r}),v(n);var o=this.encrypt(e);return v(o)}catch(e){return!1}}decryptLong(e){var t=this,r=this.n.bitLength()+7>>3;e=b(e);try{if(e.length>r){var n="";return e.match(/.{1,256}/g).forEach(function(e){var r=t.decrypt(e);n+=r}),n}return this.decrypt(e)}catch(e){return!1}}setPrivate(e,t,r){null!=e&&null!=t&&e.length>0&&t.length>0?(this.n=N(e,16),this.e=parseInt(t,16),this.d=N(r,16)):console.error("Invalid RSA private key")}setPrivateEx(e,t,r,n,o,i,a,s){null!=e&&null!=t&&e.length>0&&t.length>0?(this.n=N(e,16),this.e=parseInt(t,16),this.d=N(r,16),this.p=N(n,16),this.q=N(o,16),this.dmp1=N(i,16),this.dmq1=N(a,16),this.coeff=N(s,16)):console.error("Invalid RSA private key")}generate(e,t){var r=new V,n=e>>1;this.e=parseInt(t,16);for(var o=new B(t,16);;){for(;this.p=new B(e-n,1,r),!(0==this.p.subtract(B.ONE).gcd(o).compareTo(B.ONE)&&this.p.isProbablePrime(10)););for(;this.q=new B(n,1,r),!(0==this.q.subtract(B.ONE).gcd(o).compareTo(B.ONE)&&this.q.isProbablePrime(10)););if(0>=this.p.compareTo(this.q)){var i=this.p;this.p=this.q,this.q=i}var a=this.p.subtract(B.ONE),s=this.q.subtract(B.ONE),u=a.multiply(s);if(0==u.gcd(o).compareTo(B.ONE)){this.n=this.p.multiply(this.q),this.d=o.modInverse(u),this.dmp1=this.d.mod(a),this.dmq1=this.d.mod(s),this.coeff=this.q.modInverse(this.p);break}}}decrypt(e){var t=N(e,16),r=this.doPrivate(t);return null==r?null:function(e,t){for(var r=e.toByteArray(),n=0;n<r.length&&0==r[n];)++n;if(r.length-n!=t-1||2!=r[n])return null;for(++n;0!=r[n];)if(++n>=r.length)return null;for(var o="";++n<r.length;){var i=255&r[n];i<128?o+=String.fromCharCode(i):i>191&&i<224?(o+=String.fromCharCode((31&i)<<6|63&r[n+1]),++n):(o+=String.fromCharCode((15&i)<<12|(63&r[n+1])<<6|63&r[n+2]),n+=2)}return o}(r,this.n.bitLength()+7>>3)}generateAsync(e,t,r){var n=new V,o=e>>1;this.e=parseInt(t,16);var i=new B(t,16),a=this,s=()=>{var t=()=>{if(0>=a.p.compareTo(a.q)){var e=a.p;a.p=a.q,a.q=e}var t=a.p.subtract(B.ONE),n=a.q.subtract(B.ONE),o=t.multiply(n);0==o.gcd(i).compareTo(B.ONE)?(a.n=a.p.multiply(a.q),a.d=i.modInverse(o),a.dmp1=a.d.mod(t),a.dmq1=a.d.mod(n),a.coeff=a.q.modInverse(a.p),setTimeout(function(){r()},0)):setTimeout(s,0)},u=()=>{a.q=C(),a.q.fromNumberAsync(o,1,n,function(){a.q.subtract(B.ONE).gcda(i,function(e){0==e.compareTo(B.ONE)&&a.q.isProbablePrime(10)?setTimeout(t,0):setTimeout(u,0)})})},c=()=>{a.p=C(),a.p.fromNumberAsync(e-o,1,n,function(){a.p.subtract(B.ONE).gcda(i,function(e){0==e.compareTo(B.ONE)&&a.p.isProbablePrime(10)?setTimeout(u,0):setTimeout(c,0)})})};setTimeout(c,0)};setTimeout(s,0)}}class $ extends H{constructor(e=""){super(),e&&("string"==typeof e?this.parseKey(e):(this.hasPrivateKeyProperty(e)||this.hasPublicKeyProperty(e))&&this.parsePropertiesFrom(e))}parseKey(e){try{var t=0,r=0,n=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(e)?g.decode(e):_.unarmor(e),o=W.decode(n);if(3===o.sub.length&&(o=o.sub[2].sub[0]),9===o.sub.length){t=o.sub[1].getHexStringValue(),this.n=N(t,16),r=o.sub[2].getHexStringValue(),this.e=parseInt(r,16);var i=o.sub[3].getHexStringValue();this.d=N(i,16);var a=o.sub[4].getHexStringValue();this.p=N(a,16);var s=o.sub[5].getHexStringValue();this.q=N(s,16);var u=o.sub[6].getHexStringValue();this.dmp1=N(u,16);var c=o.sub[7].getHexStringValue();this.dmq1=N(c,16);var l=o.sub[8].getHexStringValue();this.coeff=N(l,16)}else{if(2!==o.sub.length)return!1;var f=o.sub[1].sub[0];t=f.sub[0].getHexStringValue(),this.n=N(t,16),r=f.sub[1].getHexStringValue(),this.e=parseInt(r,16)}return!0}catch(e){return!1}}hasPublicKeyProperty(e){return(e=e||{}).hasOwnProperty("n")&&e.hasOwnProperty("e")}hasPrivateKeyProperty(e){return(e=e||{}).hasOwnProperty("n")&&e.hasOwnProperty("e")&&e.hasOwnProperty("d")&&e.hasOwnProperty("p")&&e.hasOwnProperty("q")&&e.hasOwnProperty("dmp1")&&e.hasOwnProperty("dmq1")&&e.hasOwnProperty("coeff")}parsePropertiesFrom(e){this.n=e.n,this.e=e.e,e.hasOwnProperty("d")&&(this.d=e.d,this.p=e.p,this.q=e.q,this.dmp1=e.dmp1,this.dmq1=e.dmq1,this.coeff=e.coeff)}}(n=function(e){e=e||{},this.default_key_size=parseInt(e.default_key_size,10)||1024,this.default_public_exponent=e.default_public_exponent||"010001",this.log=e.log||!1,this.key=null}).prototype.setKey=function(e){this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new $(e)},n.prototype.setPrivateKey=function(e){this.setKey(e)},n.prototype.setPublicKey=function(e){this.setKey(e)},n.prototype.decrypt=function(e){try{return this.getKey().decrypt(b(e))}catch(e){return!1}},n.prototype.encrypt=function(e){try{return v(this.getKey().encrypt(e))}catch(e){return!1}},n.prototype.encryptLong=function(e){try{for(var t=this.getKey().encryptLong(e)||"",r=this.getKey().decryptLong(t)||"",n=0,o=/null$/g;o.test(r)&&(n++,t=this.getKey().encryptLong(e)||"",r=this.getKey().decryptLong(t)||"",!(n>10)););return t}catch(e){return!1}},n.prototype.getKey=function(e){if(!this.key){if(this.key=new $,e&&"[object Function]"===({}).toString.call(e)){this.key.generateAsync(this.default_key_size,this.default_public_exponent,e);return}this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},n.version="3.1.4"}var W=n,F=r(72903);let B=({publicKey:e="",payload:t={}}={})=>{if(!e)return"";try{let r=(0,F.I)(t),n=new W;return n.setPublicKey(e),n.encryptLong("object"==typeof r?JSON.stringify(r):r)}catch(e){console.error("encrypt error:",e)}return""}},72903:function(e,t,r){"use strict";r.d(t,{I:function(){return n},y:function(){return o}});let n=e=>{let t=t=>{for(let r in e)e.hasOwnProperty(r)&&(t[r]=n(e[r]));return t},r=null==e?"NullOrUndefined":Object.prototype.toString.call(e).slice(8,-1);if(["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array","BigInt64Array","BigUint64Array"].includes(r))return e.slice();switch(r){case"Object":return t(Object.create(Object.getPrototypeOf(e)));case"Array":return t([]);case"Date":return new Date(e.valueOf());case"RegExp":return new RegExp(e.source,(e.global?"g":"")+(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.sticky?"y":"")+(e.unicode?"u":""));default:return e}},o=e=>{let t=e.match(/^(?:http(s)?:\/\/[^\/]+)?(\/[^\?#]*)/);return t&&t[2]||""}},10191:function(e,t,r){"use strict";var n,o,i=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&i(t,e,r);return a(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.useDefaultAdapter=t.useAdapters=t.RUNTIME=void 0;var u=s(r(65986)),c=r(20226);(n=o=t.RUNTIME||(t.RUNTIME={})).WEB="web",n.WX_MP="wx_mp",t.useAdapters=function(e){for(var t=(0,c.isArray)(e)?e:[e],r=0;r<t.length;r++){var n=t[r],o=n.isMatch,i=n.genAdapter,a=n.runtime;if(o())return{adapter:i(),runtime:a}}},t.useDefaultAdapter=function(){return{adapter:u.genAdapter(),runtime:o.WEB}}},65986:function(e,t,r){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},a=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},s=this&&this.__generator||function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.WebRequest=t.genAdapter=void 0;var u=r(43077),c=r(20226),l=r(81302),f=function(e){function t(t){var r=e.call(this)||this,n=t.timeout,o=t.timeoutMsg,i=t.restrictedMethods;return r.timeout=n||0,r.timeoutMsg=o||"请求超时",r.restrictedMethods=i||["get","post","upload","download"],r}return o(t,e),t.prototype.get=function(e){return this.request(i(i({},e),{method:"get"}),this.restrictedMethods.includes("get"))},t.prototype.post=function(e){return this.request(i(i({},e),{method:"post"}),this.restrictedMethods.includes("post"))},t.prototype.put=function(e){return this.request(i(i({},e),{method:"put"}))},t.prototype.upload=function(e){var t=e.data,r=e.file,n=e.name,o=e.method,a=e.headers,s={post:"post",put:"put"}[null==o?void 0:o.toLowerCase()]||"put",u=new FormData;return"post"===s?(Object.keys(t).forEach(function(e){u.append(e,t[e])}),u.append("key",n),u.append("file",r),this.request(i(i({},e),{data:u,method:s}),this.restrictedMethods.includes("upload"))):this.request(i(i({},e),{method:"put",headers:void 0===a?{}:a,body:r}),this.restrictedMethods.includes("upload"))},t.prototype.download=function(e){return a(this,void 0,void 0,function(){var t,r,n,o;return s(this,function(a){switch(a.label){case 0:return a.trys.push([0,2,,3]),[4,this.get(i(i({},e),{headers:{},responseType:"blob"}))];case 1:return t=a.sent().data,r=window.URL.createObjectURL(new Blob([t])),n=decodeURIComponent(new URL(e.url).pathname.split("/").pop()||""),(o=document.createElement("a")).href=r,o.setAttribute("download",n),o.style.display="none",document.body.appendChild(o),o.click(),window.URL.revokeObjectURL(r),document.body.removeChild(o),[3,3];case 2:return a.sent(),[3,3];case 3:return[2,new Promise(function(t){t({statusCode:200,tempFilePath:e.url})})]}})})},t.prototype.fetch=function(e){var t;return a(this,void 0,void 0,function(){var r,n,o,u,c,l,f,h,d,p,y,m=this;return s(this,function(v){switch(v.label){case 0:return r=new AbortController,n=e.url,u=void 0!==(o=e.enableAbort)&&o,l=void 0!==(c=e.stream)&&c,f=e.signal,d=null!=(h=e.timeout)?h:this.timeout,f&&(f.aborted&&r.abort(),f.addEventListener("abort",function(){return r.abort()})),p=null,u&&d&&(p=setTimeout(function(){console.warn(m.timeoutMsg),r.abort(Error(m.timeoutMsg))},d)),[4,fetch(n,i(i({},e),{signal:r.signal})).then(function(e){return a(m,void 0,void 0,function(){var t,r,n;return s(this,function(o){switch(o.label){case 0:if(clearTimeout(p),!e.ok)return[3,1];return t=e,[3,3];case 1:return n=(r=Promise).reject,[4,e.json()];case 2:t=n.apply(r,[o.sent()]),o.label=3;case 3:return[2,t]}})})}).catch(function(e){return clearTimeout(p),Promise.reject(e)})];case 1:return y=v.sent(),[2,{data:l?y.body:(null===(t=y.headers.get("content-type"))||void 0===t?void 0:t.includes("application/json"))?y.json():y.text(),statusCode:y.status,header:y.headers}]}})})},t.prototype.request=function(e,t){var r=this;void 0===t&&(t=!1);var n=String(e.method).toLowerCase()||"get";return new Promise(function(o){var i,a,s=e.url,u=e.headers,f=void 0===u?{}:u,h=e.data,d=e.responseType,p=e.withCredentials,y=e.body,m=e.onUploadProgress,v=(0,c.formatUrl)((0,l.getProtocol)(),s,"get"===n?h:{}),b=new XMLHttpRequest;b.open(n,v),d&&(b.responseType=d),Object.keys(f).forEach(function(e){b.setRequestHeader(e,f[e])}),m&&b.upload.addEventListener("progress",m),b.onreadystatechange=function(){var e={};if(4===b.readyState){var t=b.getAllResponseHeaders().trim().split(/[\r\n]+/),r={};t.forEach(function(e){var t=e.split(": "),n=t.shift().toLowerCase(),o=t.join(": ");r[n]=o}),e.header=r,e.statusCode=b.status;try{e.data="blob"===d?b.response:JSON.parse(b.responseText)}catch(t){e.data="blob"===d?b.response:b.responseText}clearTimeout(i),o(e)}},t&&r.timeout&&(i=setTimeout(function(){console.warn(r.timeoutMsg),b.abort()},r.timeout)),a=(0,c.isFormData)(h)?h:"application/x-www-form-urlencoded"===f["content-type"]?(0,c.toQueryString)(h):y||(h?JSON.stringify(h):void 0),p&&(b.withCredentials=!0),b.send(a)})},t}(u.AbstractSDKRequest);t.WebRequest=f,t.genAdapter=function(){return{root:window,reqClass:f,wsClass:WebSocket,localStorage:localStorage}}},81302:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.COMMUNITY_SITE_URL=t.IS_DEBUG_MODE=t.getProtocol=t.setProtocol=t.getSdkName=t.setSdkName=void 0;var r="@cloudbase/js-sdk";t.setSdkName=function(e){r=e},t.getSdkName=function(){return r};var n="https:";t.setProtocol=function(e){n=e},t.getProtocol=function(){return n},t.IS_DEBUG_MODE=!1,t.COMMUNITY_SITE_URL="https://support.qq.com/products/148793"},20925:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ERRORS=void 0,t.ERRORS={INVALID_PARAMS:"INVALID_PARAMS",INVALID_SYNTAX:"INVALID_SYNTAX",INVALID_OPERATION:"INVALID_OPERATION",OPERATION_FAIL:"OPERATION_FAIL",NETWORK_ERROR:"NETWORK_ERROR",UNKOWN_ERROR:"UNKOWN_ERROR"}},83544:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.OATUH_LOGINTYPE=void 0,o(r(81302),t),o(r(20925),t),t.OATUH_LOGINTYPE="constants"},37612:function(e,t,r){"use strict";var n=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},o=this&&this.__generator||function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.catchErrorsDecorator=void 0;var i=r(20226),a=r(83544),s=!1;"undefined"!=typeof navigator&&navigator.userAgent&&(s=-1!==navigator.userAgent.indexOf("Firefox"));var u=s?/(\.js\/)?__decorate(\$\d+)?<@.*\d$/:/(\/\w+\.js\.)?__decorate(\$\d+)?\s*\(.*\)$/,c=/https?:\/\/.+:\d*\/.*\.js:\d+:\d+/;function l(e){var t,r=e.err,n=e.className,o=e.methodName,i=e.sourceLink;if(!i)return null;var a=r.stack.split("\n"),u=s?/^catchErrorsDecorator\/<\/descriptor.value@.*\d$/:new RegExp("".concat(n,"\\.descriptor.value\\s*\\[as\\s").concat(o,"\\]\\s*\\(.*\\)$")),l=s?/^catchErrorsDecorator\/<\/descriptor.value/:new RegExp("".concat(n,"\\.descriptor.value\\s*\\[as\\s").concat(o,"\\]")),f=a.findIndex(function(e){return u.test(e)});if(-1!==f){var h=a.filter(function(e,t){return t>f});h.unshift(a[f].replace(l,"".concat(n,".").concat(o)).replace(c,i)),(t=Error()).stack="".concat(s?"@debugger":"Error","\n").concat(h.join("\n"))}return t}t.catchErrorsDecorator=function(e){var t=e.mode,r=void 0===t?"async":t,s=e.customInfo,f=void 0===s?{}:s,h=e.title,d=e.messages,p=void 0===d?[]:d;return function(e,t,s){if(a.IS_DEBUG_MODE){var d=f.className||e.constructor.name,y=f.methodName||t,m=s.value,v=function(e){var t="",r=e.stack.split("\n"),n=r.findIndex(function(e){return u.test(e)});if(-1!==n){var o=c.exec(r[n+1]||"");t=o?o[0]:""}return t}(Error());"sync"===r?s.value=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=l({err:Error(),className:d,methodName:y,sourceLink:v});try{return m.apply(this,e)}catch(e){var n=e,o=e.message,a=e.error,s=e.error_description,u={title:h||"".concat(d,".").concat(y," failed"),content:[{type:"error",body:e}]};if(o&&/^\{.*\}$/.test(o)){var c=JSON.parse(o);u.subtitle=o,c.code&&(r?(r.code=c.code,r.msg=c.msg):(e.code=c.code,e.message=c.msg),n=r||e,u.content=p.map(function(e){return{type:"info",body:e}}))}throw a&&s&&(u.subtitle=s,r?(r.code=a,r.msg=s):(e.code=a,e.message=s),n=r||e,u.content=p.map(function(e){return{type:"info",body:e}})),(0,i.printGroupLog)(u),n}}:s.value=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n(this,void 0,void 0,function(){var t,r,n,a,s,u,c,f;return o(this,function(o){switch(o.label){case 0:t=l({err:Error(),className:d,methodName:y,sourceLink:v}),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,m.apply(this,e)];case 2:return[2,o.sent()];case 3:throw n=r=o.sent(),a=r.message,s=r.error,u=r.error_description,c={title:h||"".concat(d,".").concat(y," failed"),content:[{type:"error",body:r}]},a&&/^\{.*\}$/.test(a)&&(f=JSON.parse(a),c.subtitle=f,f.code&&(t?(t.code=f.code,t.message=f.msg):(r.code=f.code,r.message=f.msg),n=t||r,c.content=p.map(function(e){return{type:"info",body:e}}))),s&&u&&(c.subtitle=u,t?(t.code=s,t.msg=u):(r.code=s,r.message=u),n=t||r,c.content=p.map(function(e){return{type:"info",body:e}})),(0,i.printGroupLog)(c),n;case 4:return[2]}})})}}}}},99794:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),o(r(37612),t)},32348:function(e,t,r){"use strict";var n,o=r(25566),i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),a=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})},s=this&&this.__generator||function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.CloudbaseCache=void 0;var u=r(43077),c=r(20226),l=r(83544),f=function(e){function t(t){var r=e.call(this)||this;return r.root=t,t.tcbCacheObject||(t.tcbCacheObject={}),r}return i(t,e),t.prototype.setItem=function(e,t){this.root.tcbCacheObject[e]=t},t.prototype.getItem=function(e){return this.root.tcbCacheObject[e]},t.prototype.removeItem=function(e){delete this.root.tcbCacheObject[e]},t.prototype.clear=function(){delete this.root.tcbCacheObject},t}(u.AbstractStorage),h=function(){function e(e){this.keys={};var t=e.persistence,r=e.platformInfo,n=e.keys;this.platformInfo=void 0===r?{}:r,this.storage||(this.persistenceTag=this.platformInfo.adapter.primaryStorage||t,this.storage=function(e,t){switch(e){case"local":default:if(!t.localStorage)return(0,c.printWarn)(l.ERRORS.INVALID_PARAMS,"localStorage is not supported on current platform"),new f(t.root);return t.localStorage;case"none":return new f(t.root)}}(this.persistenceTag,this.platformInfo.adapter),this.keys=void 0===n?{}:n)}return Object.defineProperty(e.prototype,"mode",{get:function(){return this.storage.mode||"sync"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"persistence",{get:function(){return this.persistenceTag},enumerable:!1,configurable:!0}),e.prototype.setStore=function(e,t,r){if("async"===this.mode){(0,c.printWarn)(l.ERRORS.INVALID_OPERATION,"current platform's storage is asynchronous, please use setStoreAsync insteed");return}if(this.storage)try{this.storage.setItem(e,JSON.stringify({version:r||"localCachev1",content:t}))}catch(e){throw Error(JSON.stringify({code:l.ERRORS.OPERATION_FAIL,msg:"[".concat((0,l.getSdkName)(),"][").concat(l.ERRORS.OPERATION_FAIL,"]setStore failed"),info:e}))}},e.prototype.setStoreAsync=function(e,t,r){return a(this,void 0,void 0,function(){var n;return s(this,function(o){switch(o.label){case 0:if(!this.storage)return[2];o.label=1;case 1:return o.trys.push([1,3,,4]),n={version:r||"localCachev1",content:t},[4,this.storage.setItem(e,JSON.stringify(n))];case 2:return o.sent(),[3,4];case 3:return o.sent(),[2];case 4:return[2]}})})},e.prototype.getStore=function(e,t){if("async"===this.mode){(0,c.printWarn)(l.ERRORS.INVALID_OPERATION,"current platform's storage is asynchronous, please use getStoreAsync insteed");return}try{if(void 0!==o&&(null===(r=o.env)||void 0===r?void 0:r.tcb_token))return o.env.tcb_token;if(!this.storage)return""}catch(e){return""}t=t||"localCachev1";var r,n=this.storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""},e.prototype.getStoreAsync=function(e,t){var r;return a(this,void 0,void 0,function(){var n;return s(this,function(i){switch(i.label){case 0:try{if(void 0!==o&&(null===(r=o.env)||void 0===r?void 0:r.tcb_token))return[2,o.env.tcb_token];if(!this.storage)return[2,""]}catch(e){return[2,""]}return t=t||"localCachev1",[4,this.storage.getItem(e)];case 1:if(!(n=i.sent()))return[2,""];if(n.indexOf(t)>=0)return[2,JSON.parse(n).content];return[2,""]}})})},e.prototype.removeStore=function(e){if("async"===this.mode){(0,c.printWarn)(l.ERRORS.INVALID_OPERATION,"current platform's storage is asynchronous, please use removeStoreAsync insteed");return}this.storage.removeItem(e)},e.prototype.removeStoreAsync=function(e){return a(this,void 0,void 0,function(){return s(this,function(t){switch(t.label){case 0:return[4,this.storage.removeItem(e)];case 1:return t.sent(),[2]}})})},e}();t.CloudbaseCache=h},13826:function(e,t,r){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),i=this&&this.__spreadArray||function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.removeEventListener=t.activateEvent=t.addEventListener=t.CloudbaseEventEmitter=t.IErrorEvent=t.CloudbaseEvent=void 0;var a=r(20226),s=function(e,t){this.data=t||null,this.name=e};t.CloudbaseEvent=s;var u=function(e){function t(t,r){var n=e.call(this,"error",{error:t,data:r})||this;return n.error=t,n}return o(t,e),t}(s);t.IErrorEvent=u;var c=function(){function e(){this.listeners={}}return e.prototype.on=function(e,t){var r;return(r=this.listeners)[e]=r[e]||[],r[e].push(t),this},e.prototype.off=function(e,t){return function(e,t,r){if(null==r?void 0:r[e]){var n=r[e].indexOf(t);-1!==n&&r[e].splice(n,1)}}(e,t,this.listeners),this},e.prototype.fire=function(e,t){if((0,a.isInstanceOf)(e,u))return console.error(e.error),this;var r=(0,a.isString)(e)?new s(e,t||{}):e,n=r.name;if(this.listens(n)){r.target=this;for(var o=this.listeners[n]?i([],this.listeners[n],!0):[],c=0;c<o.length;c++)o[c].call(this,r)}return this},e.prototype.listens=function(e){return this.listeners[e]&&this.listeners[e].length>0},e}();t.CloudbaseEventEmitter=c;var l=new c;t.addEventListener=function(e,t){l.on(e,t)},t.activateEvent=function(e,t){void 0===t&&(t={}),l.fire(e,t)},t.removeEventListener=function(e,t){l.off(e,t)}},20226:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.transformPhone=t.sleep=t.printGroupLog=t.throwError=t.printInfo=t.printError=t.printWarn=t.execCallback=t.createPromiseCallback=t.removeParam=t.getHash=t.getQuery=t.toQueryString=t.formatUrl=t.generateRequestId=t.genSeqId=t.isFormData=t.isInstanceOf=t.isNull=t.isPalinObject=t.isUndefined=t.isString=t.isArray=void 0;var n=r(83544);t.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)},t.isString=function(e){return"string"==typeof e},t.isUndefined=function(e){return void 0===e},t.isPalinObject=function(e){return"[object Object]"===Object.prototype.toString.call(e)},t.isNull=function(e){return"[object Null]"===Object.prototype.toString.call(e)},t.isInstanceOf=function(e,t){return e instanceof t},t.isFormData=function(e){return"[object FormData]"===Object.prototype.toString.call(e)},t.genSeqId=function(){return Math.random().toString(16).slice(2)},t.generateRequestId=function(){var e=new Date().getTime(),t=(null==performance?void 0:performance.now)&&1e3*performance.now()||0;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(r){var n=16*Math.random();return e>0?(n=(e+n)%16|0,e=Math.floor(e/16)):(n=(t+n)%16|0,t=Math.floor(t/16)),("x"===r?n:7&n|8).toString(16)})},t.formatUrl=function(e,t,r){void 0===r&&(r={});var n=/\?/.test(t),o="";return(Object.keys(r).forEach(function(e){""===o?n||(t+="?"):o+="&",o+="".concat(e,"=").concat(encodeURIComponent(r[e]))}),t+=o,/^http(s)?:\/\//.test(t))?t:"".concat(e).concat(t)},t.toQueryString=function(e){void 0===e&&(e={});var t=[];return Object.keys(e).forEach(function(r){t.push("".concat(r,"=").concat(encodeURIComponent(e[r])))}),t.join("&")},t.getQuery=function(e,t){if("undefined"==typeof window)return!1;var r=t||window.location.search,n=new RegExp("(^|&)".concat(e,"=([^&]*)(&|$)")),o=r.substr(r.indexOf("?")+1).match(n);return null!=o?o[2]:""},t.getHash=function(e){if("undefined"==typeof window)return"";var t=window.location.hash.match(new RegExp("[#?&/]".concat(e,"=([^&#]*)")));return t?t[1]:""},t.removeParam=function(e,t){var r=t.split("?")[0],n=[],o=-1!==t.indexOf("?")?t.split("?")[1]:"";if(""!==o){n=o.split("&");for(var i=n.length-1;i>=0;i-=1)n[i].split("=")[0]===e&&n.splice(i,1);r="".concat(r,"?").concat(n.join("&"))}return r},t.createPromiseCallback=function(){if(!Promise){(e=function(){}).promise={};var e,t=function(){throw Error('Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.')};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}var r=new Promise(function(t,r){e=function(e,n){return e?r(e):t(n)}});return e.promise=r,e},t.execCallback=function(e,t,r){if(void 0===r&&(r=null),e&&"function"==typeof e)return e(t,r);if(t)throw t;return r},t.printWarn=function(e,t){console.warn("[".concat((0,n.getSdkName)(),"][").concat(e,"]:").concat(t))},t.printError=function(e,t){console.error({code:e,msg:"[".concat((0,n.getSdkName)(),"][").concat(e,"]:").concat(t)})},t.printInfo=function(e,t){console.log("[".concat((0,n.getSdkName)(),"][").concat(e,"]:").concat(t))},t.throwError=function(e,t){throw Error(JSON.stringify({code:e,msg:"[".concat((0,n.getSdkName)(),"][").concat(e,"]:").concat(t)}))},t.printGroupLog=function(e){var t=e.title,r=e.subtitle,n=void 0===r?"":r,o=e.content,i=e.printTrace,a=e.collapsed;void 0!==a&&a?console.groupCollapsed(t,n):console.group(t,n);for(var s=0,u=void 0===o?[]:o;s<u.length;s++){var c=u[s],l=c.type,f=c.body;switch(l){case"info":console.log(f);break;case"warn":console.warn(f);break;case"error":console.error(f)}}void 0!==i&&i&&console.trace("stack trace:"),console.groupEnd()},t.sleep=function(e){return void 0===e&&(e=0),new Promise(function(t){return setTimeout(t,e)})},t.transformPhone=function(e){return"+86".concat(e)}},10991:function(e,t){!function(e){"use strict";var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,r)},r=function(){return(r=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function n(e,t,r,n){return new(r||(r=Promise))(function(t,o){function i(e){try{s(n.next(e))}catch(e){o(e)}}function a(e){try{s(n.throw(e))}catch(e){o(e)}}function s(e){var n;e.done?t(e.value):((n=e.value)instanceof r?n:new r(function(e){e(n)})).then(i,a)}s((n=n.apply(e,[])).next())})}function o(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}var i=function(e){function r(t,r){var n=e.call(this,t)||this;return n.name="WxCloudSDKError",n.code=null==r?void 0:r.code,n.requestId=null==r?void 0:r.requestId,n.originError=null==r?void 0:r.originError,n}return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}(r,e),r}(Error);function a(){return"undefined"!=typeof window&&window||"undefined"!=typeof globalThis&&globalThis}var s=function(e){var t=e.dataSourceName,r=e.methodName,s=e.params,u=e.realMethodName,c=e.callFunction,l=e.envType,f=void 0===l?"prod":l,h=e.mode;return n(void 0,void 0,void 0,function(){var e,n,l,d,p,y;return o(this,function(o){switch(o.label){case 0:e={data:{},requestId:""},o.label=1;case 1:var m,v;return o.trys.push([1,3,,4]),[4,c({name:"lowcode-datasource",data:{dataSourceName:t,methodName:r,params:s,userAgent:(null==(v=a())?void 0:v.navigator)?v.navigator.userAgent:"undefined"!=typeof wx&&wx.getSystemInfo?(wx.getSystemInfo({success:function(e){e&&(m=["brand","model","version","system","platform","SDKVersion","language"].map(function(t){return"".concat(t,": ").concat(e[t])}).join(", "))}}),m):void 0,referrer:function(){try{var e=a();if(!e)return;if("undefined"==typeof wx)return a().location.href;return e.__wxRoute}catch(e){}}(),"x-sdk-version":"1.6.1",envType:f,mode:h}})];case 2:if(l=(null===(p=null==(n=o.sent())?void 0:n.result)||void 0===p?void 0:p.requestId)||(null==n?void 0:n.requestId)||(null==n?void 0:n.requestID),null==n?void 0:n.result.code)throw new i("【错误】".concat(null==n?void 0:n.result.message,"\n【操作】调用 models.").concat(t?"".concat(t,"."):"").concat(u,"\n【错误码】").concat(null==n?void 0:n.result.code,"\n【请求ID】").concat(l||"N/A"),{code:null==n?void 0:n.result.code,requestId:l});return e.data=(null===(y=null==n?void 0:n.result)||void 0===y?void 0:y.data)||{},e.requestId=l,[3,4];case 3:if("WxCloudSDKError"===(d=o.sent()).name)throw d;throw console.log(d),new i("【错误】".concat(d.message,"\n      【操作】调用 models.").concat(t?"".concat(t,"."):"").concat(u,"\n      【请求ID】N/A"),{code:"UnknownError",originError:d});case 4:return[2,e]}})})},u=function(e){var t=e.sql,r=e.params,i=e.config,a=e.callFunction,u=e.unsafe,c=void 0!==u&&u;return n(void 0,void 0,void 0,function(){return o(this,function(e){return[2,s({realMethodName:"$runSQL",methodName:"callWedaApi",params:{action:"RunMysqlCommand",data:{sqlTemplate:t,config:i,parameter:c?"":Object.entries(r||{}).reduce(function(e,t){var r=t[0],n=t[1];if(void 0!==n){var o="OBJECT";switch(typeof n){case"boolean":o="BOOLEAN";break;case"number":o="NUMBER";break;case"string":o="STRING";break;default:o=Array.isArray(n)?"ARRAY":"OBJECT"}e.push({key:r,type:o,value:"STRING"===o?n:JSON.stringify(n)})}return e},[])||[]}},callFunction:a,mode:"sdk"})]})})},c=function(e){return{$runSQL:function(t,i,a){return n(this,void 0,void 0,function(){return o(this,function(n){switch(n.label){case 0:return[4,u({sql:t,params:i,config:r(r({},a),{preparedStatements:!0}),callFunction:e})];case 1:return[2,n.sent()]}})})},$runSQLRaw:function(t,i){return n(this,void 0,void 0,function(){return o(this,function(n){switch(n.label){case 0:return[4,u({sql:t,params:[],config:r(r({},i),{preparedStatements:!1}),callFunction:e})];case 1:return[2,n.sent()]}})})}}},l={filter:{where:{}},select:{$master:!0}};function f(e){return{getUrl:function(t){return"".concat(t,"/").concat(e)},method:"post"}}var h={get:r(r({},f("get")),{defaultParams:r({},l)}),list:r(r({},f("list")),{defaultParams:r({},l)}),create:f("create"),createMany:f("createMany"),update:r(r({},f("update")),{method:"put"}),updateMany:r(r({},f("updateMany")),{method:"put"}),upsert:f("upsert"),delete:f("delete"),deleteMany:f("deleteMany")},d="Unknown error occurred",p="NotSupported",y=function(e,t,r,n){var o=c(e);return new Proxy({},{get:function(e,i){if("string"==typeof i)return Object.prototype.hasOwnProperty.call(o,i)?o[i]:v(r,i,t,n)}})},m=function(e,t,r,n,o){return new i("【错误】".concat(e,"\n【操作】调用 models.").concat(t,".").concat(r,"\n【错误码】").concat(n,"\n【请求ID】").concat(o),{code:n,requestId:o})},v=function(e,t,a,s){return new Proxy({},{get:function(u,c){if("runSQLTemplate"!==c){var l=h[c];if(!l){var f=Error("不支持的操作: ".concat(c));throw new i(f.message||d,{originError:f,code:p,requestId:"N/A"})}return function(r){return void 0===r&&(r={}),n(void 0,void 0,void 0,function(){var n,s,u,f,h,p,y,v;return o(this,function(o){switch(o.label){case 0:n=l.getUrl,s=l.method,h=[e,"pre"===(f=Object.assign({},void 0===(u=l.defaultParams)?{}:u,r)).envType?"pre":"prod",n(t)].join("/"),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,a({url:h,body:JSON.stringify(f),method:s})];case 2:if((p=o.sent()).code)throw m(null==p?void 0:p.message,t,c,null==p?void 0:p.code,null==p?void 0:p.requestId);return"get"===c&&Object.assign(p,{data:null!==(v=p.data.record)&&void 0!==v?v:p.data}),[2,p];case 3:throw new i((null==(y=o.sent())?void 0:y.message)||d,{originError:y});case 4:return[2]}})})}}if(!(null==s?void 0:s.sqlBaseUrl)){var f=Error("不支持的操作: ".concat(c));throw new i(f.message||d,{originError:f,code:p,requestId:"N/A"})}return function(e){return n(void 0,void 0,void 0,function(){var n,u,l,f,h,p,y,v,b,g,_,w,S,E,T;return o(this,function(o){switch(o.label){case 0:n=e.params,u=e.templateName,l="pre"===e.envType?"pre":"prod",f=[s.sqlBaseUrl,l,u,"run"].join("/"),h=Object.entries(n||{}).reduce(function(e,t){var r=t[0],n=t[1];if(void 0!==n){var o="OBJECT";switch(typeof n){case"boolean":o="BOOLEAN";break;case"number":o="NUMBER";break;case"string":o="STRING";break;default:o=Array.isArray(n)?"ARRAY":"OBJECT"}e.push({key:r,type:o,value:"STRING"===o?n:JSON.stringify(n)})}return e},[]),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,a({url:f,body:JSON.stringify({parameter:h}),method:"POST"})];case 2:if(null===(v=null==(p=o.sent())?void 0:p.Response)||void 0===v?void 0:v.Error)throw m(null===(g=null===(b=null==p?void 0:p.Response)||void 0===b?void 0:b.Error)||void 0===g?void 0:g.Message,t,c,null===(w=null===(_=null==p?void 0:p.Response)||void 0===_?void 0:_.Error)||void 0===w?void 0:w.Code,null===(S=null==p?void 0:p.Response)||void 0===S?void 0:S.RequestId);return[2,r(r({},null!==(E=null==p?void 0:p.Response)&&void 0!==E?E:{}),{data:null===(T=null==p?void 0:p.Response)||void 0===T?void 0:T.Data})];case 3:throw new i((null==(y=o.sent())?void 0:y.message)||d,{originError:y});case 4:return[2]}})})}}})},b={create:{methodName:"wedaCreateV2"},createMany:{methodName:"wedaBatchCreateV2"},update:{methodName:"wedaUpdateV2"},upsert:{methodName:"wedaUpsertV2"},updateMany:{methodName:"wedaBatchUpdateV2"},delete:{methodName:"wedaDeleteV2"},deleteMany:{methodName:"wedaBatchDeleteV2"},get:{methodName:"wedaGetItemV2",defaultParams:{filter:{where:{}},select:{$master:!0}}},list:{methodName:"wedaGetRecordsV2",defaultParams:{filter:{where:{}},select:{$master:!0}}}},g=function(e){var t=c(e);return new Proxy({},{get:function(a,u){if("string"==typeof u)return Object.prototype.hasOwnProperty.call(t,u)?t[u]:new Proxy({},{get:function(t,a){var c=b[a];if(!c){var l=Error("不支持的操作: ".concat(a));throw new i(l.message||"Unknown error occurred",{originError:l,code:"NotSupported",requestId:"N/A"})}return function(t){return n(void 0,void 0,void 0,function(){var n,i,l,f,h;return o(this,function(o){switch(o.label){case 0:return n=r(r({},c.defaultParams||{}),t||{}),[4,s({callFunction:e,dataSourceName:u,methodName:c.methodName,realMethodName:a,params:n,envType:null==t?void 0:t.envType})];case 1:return i=o.sent(),l={data:{}},f=c.responseKey,l.data=f?null===(h=null==i?void 0:i.data)||void 0===h?void 0:h[f]:null==i?void 0:i.data,[2,l]}})})}}})}})};function _(e){if(!e)throw Error("cloud is required");if(!e.callFunction)throw Error("cloud.callFunction is required");var t=g(e.callFunction.bind(e));return e.models=t,e}function w(e){var t,r,n,o,i=null===(r=null===(t=null==e?void 0:e.extend)||void 0===t?void 0:t.AI)||void 0===r?void 0:r.env;if("string"==typeof i&&i.trim()||(i=null===(n=null==e?void 0:e.config)||void 0===n?void 0:n.env),"string"==typeof i&&i.trim()||(i=null===(o=null==e?void 0:e.config)||void 0===o?void 0:o.envName),"string"!=typeof i||!i.trim())throw Error("Generating default gateway base url failed: env not found");return i=i.trim(),{model:"https://".concat(i,".api.tcloudbasegateway.com/v1/model"),sql:"https://".concat(i,".api.tcloudbasegateway.com/v1/sql")}}e.default={init:_,generateHTTPClient:y},e.generateHTTPClient=y,e.init=_,e.initHTTPOverCallFunction=function(e,t){var i,a=this;if(!e)throw Error("cloud is required");if(!e.callFunction)throw Error("cloud.callFunction is required");var s=e.callFunction.bind(e);try{var u=(null==t?void 0:t.baseUrl)||w(e).model,c=(null==t?void 0:t.sqlBaseUrl)||w(e).sql;i=y(s,function(e){var t=e.url,i=e.body,u=e.method,c=e.headers;return n(a,void 0,void 0,function(){var e,n,a;return o(this,function(o){switch(o.label){case 0:return[4,s({name:"httpOverCallFunction",data:{url:t,method:null==u?void 0:u.toUpperCase(),headers:r(r({},c||{}),{"Content-Type":"application/json"}),body:i}})];case 1:return e=o.sent(),n=null===(a=null==e?void 0:e.result)||void 0===a?void 0:a.body,[2,n]}})})},u,{sqlBaseUrl:c})}catch(e){console.error("init http orm client failed, try call function orm",e),i=g(s)}return e.models=i,e},Object.defineProperty(e,"__esModule",{value:!0})}(t)},94975:function(e,t,r){var n=r(39866)(r(74288),"DataView");e.exports=n},9855:function(e,t,r){var n=r(43596),o=r(35907),i=r(35355),a=r(39870),s=r(73372);function u(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=s,e.exports=u},99078:function(e,t,r){var n=r(62285),o=r(28706),i=r(63717),a=r(78410),s=r(13368);function u(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=s,e.exports=u},88675:function(e,t,r){var n=r(39866)(r(74288),"Map");e.exports=n},76219:function(e,t,r){var n=r(38764),o=r(78615),i=r(83391),a=r(53483),s=r(74724);function u(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=s,e.exports=u},69308:function(e,t,r){var n=r(39866)(r(74288),"Promise");e.exports=n},41497:function(e,t,r){var n=r(39866)(r(74288),"Set");e.exports=n},85885:function(e,t,r){var n=r(99078),o=r(84092),i=r(31663),a=r(69135),s=r(39552),u=r(63960);function c(e){var t=this.__data__=new n(e);this.size=t.size}c.prototype.clear=o,c.prototype.delete=i,c.prototype.get=a,c.prototype.has=s,c.prototype.set=u,e.exports=c},23910:function(e,t,r){var n=r(74288).Symbol;e.exports=n},80098:function(e,t,r){var n=r(74288).Uint8Array;e.exports=n},10880:function(e,t,r){var n=r(39866)(r(74288),"WeakMap");e.exports=n},2421:function(e){e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n&&!1!==t(e[r],r,e););return e}},42774:function(e){e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=0,i=[];++r<n;){var a=e[r];t(a,r,e)&&(i[o++]=a)}return i}},28579:function(e,t,r){var n=r(89772),o=r(56569),i=r(25614),a=r(98051),s=r(84257),u=r(9792),c=Object.prototype.hasOwnProperty;e.exports=function(e,t){var r=i(e),l=!r&&o(e),f=!r&&!l&&a(e),h=!r&&!l&&!f&&u(e),d=r||l||f||h,p=d?n(e.length,String):[],y=p.length;for(var m in e)(t||c.call(e,m))&&!(d&&("length"==m||f&&("offset"==m||"parent"==m)||h&&("buffer"==m||"byteLength"==m||"byteOffset"==m)||s(m,y)))&&p.push(m);return p}},73819:function(e){e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=Array(n);++r<n;)o[r]=t(e[r],r,e);return o}},73817:function(e){e.exports=function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e}},83295:function(e,t,r){var n=r(83023),o=r(37560),i=Object.prototype.hasOwnProperty;e.exports=function(e,t,r){var a=e[t];i.call(e,t)&&o(a,r)&&(void 0!==r||t in e)||n(e,t,r)}},24457:function(e,t,r){var n=r(37560);e.exports=function(e,t){for(var r=e.length;r--;)if(n(e[r][0],t))return r;return -1}},90665:function(e,t,r){var n=r(33485),o=r(43228);e.exports=function(e,t){return e&&n(t,o(t),e)}},19455:function(e,t,r){var n=r(33485),o=r(18587);e.exports=function(e,t){return e&&n(t,o(t),e)}},83023:function(e,t,r){var n=r(4521);e.exports=function(e,t,r){"__proto__"==t&&n?n(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}},68829:function(e,t,r){var n=r(85885),o=r(2421),i=r(83295),a=r(90665),s=r(19455),u=r(19243),c=r(56075),l=r(96962),f=r(57972),h=r(28529),d=r(75474),p=r(81690),y=r(6914),m=r(32772),v=r(32685),b=r(25614),g=r(98051),_=r(38839),w=r(28302),S=r(87615),E=r(43228),T=r(18587),I="[object Arguments]",O="[object Function]",R="[object Object]",A={};A[I]=A["[object Array]"]=A["[object ArrayBuffer]"]=A["[object DataView]"]=A["[object Boolean]"]=A["[object Date]"]=A["[object Float32Array]"]=A["[object Float64Array]"]=A["[object Int8Array]"]=A["[object Int16Array]"]=A["[object Int32Array]"]=A["[object Map]"]=A["[object Number]"]=A[R]=A["[object RegExp]"]=A["[object Set]"]=A["[object String]"]=A["[object Symbol]"]=A["[object Uint8Array]"]=A["[object Uint8ClampedArray]"]=A["[object Uint16Array]"]=A["[object Uint32Array]"]=!0,A["[object Error]"]=A[O]=A["[object WeakMap]"]=!1,e.exports=function e(t,r,P,C,N,x){var q,j=1&r,k=2&r,D=4&r;if(P&&(q=N?P(t,C,N,x):P(t)),void 0!==q)return q;if(!w(t))return t;var L=b(t);if(L){if(q=y(t),!j)return c(t,q)}else{var U=p(t),M=U==O||"[object GeneratorFunction]"==U;if(g(t))return u(t,j);if(U==R||U==I||M&&!N){if(q=k||M?{}:v(t),!j)return k?f(t,s(q,t)):l(t,a(q,t))}else{if(!A[U])return N?t:{};q=m(t,U,j)}}x||(x=new n);var W=x.get(t);if(W)return W;x.set(t,q),S(t)?t.forEach(function(n){q.add(e(n,r,P,n,t,x))}):_(t)&&t.forEach(function(n,o){q.set(o,e(n,r,P,o,t,x))});var F=D?k?d:h:k?T:E,B=L?void 0:F(t);return o(B||t,function(n,o){B&&(n=t[o=n]),i(q,o,e(n,r,P,o,t,x))}),q}},32318:function(e,t,r){var n=r(28302),o=Object.create,i=function(){function e(){}return function(t){if(!n(t))return{};if(o)return o(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();e.exports=i},92167:function(e,t,r){var n=r(67906),o=r(70235);e.exports=function(e,t){t=n(t,e);for(var r=0,i=t.length;null!=e&&r<i;)e=e[o(t[r++])];return r&&r==i?e:void 0}},36452:function(e,t,r){var n=r(73817),o=r(25614);e.exports=function(e,t,r){var i=t(e);return o(e)?i:n(i,r(e))}},54506:function(e,t,r){var n=r(23910),o=r(4479),i=r(80910),a=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?o(e):i(e)}},90370:function(e,t,r){var n=r(54506),o=r(10303);e.exports=function(e){return o(e)&&"[object Arguments]"==n(e)}},32012:function(e,t,r){var n=r(81690),o=r(10303);e.exports=function(e){return o(e)&&"[object Map]"==n(e)}},57595:function(e,t,r){var n=r(86757),o=r(79551),i=r(28302),a=r(1292),s=/^\[object .+?Constructor\]$/,u=Object.prototype,c=Function.prototype.toString,l=u.hasOwnProperty,f=RegExp("^"+c.call(l).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||o(e))&&(n(e)?f:s).test(a(e))}},34188:function(e,t,r){var n=r(81690),o=r(10303);e.exports=function(e){return o(e)&&"[object Set]"==n(e)}},59332:function(e,t,r){var n=r(54506),o=r(13973),i=r(10303),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&o(e.length)&&!!a[n(e)]}},4578:function(e,t,r){var n=r(35365),o=r(77184),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return o(e);var t=[];for(var r in Object(e))i.call(e,r)&&"constructor"!=r&&t.push(r);return t}},22476:function(e,t,r){var n=r(28302),o=r(35365),i=r(92147),a=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return i(e);var t=o(e),r=[];for(var s in e)"constructor"==s&&(t||!a.call(e,s))||r.push(s);return r}},87325:function(e,t,r){var n=r(83295),o=r(67906),i=r(84257),a=r(28302),s=r(70235);e.exports=function(e,t,r,u){if(!a(e))return e;t=o(t,e);for(var c=-1,l=t.length,f=l-1,h=e;null!=h&&++c<l;){var d=s(t[c]),p=r;if("__proto__"===d||"constructor"===d||"prototype"===d)break;if(c!=f){var y=h[d];void 0===(p=u?u(y,d,h):void 0)&&(p=a(y)?y:i(t[c+1])?[]:{})}n(h,d,p),h=h[d]}return e}},99558:function(e){e.exports=function(e,t,r){var n=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(r=r>o?o:r)<0&&(r+=o),o=t>r?0:r-t>>>0,t>>>=0;for(var i=Array(o);++n<o;)i[n]=e[n+t];return i}},89772:function(e){e.exports=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}},65020:function(e,t,r){var n=r(23910),o=r(73819),i=r(25614),a=r(78371),s=1/0,u=n?n.prototype:void 0,c=u?u.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return o(t,e)+"";if(a(t))return c?c.call(t):"";var r=t+"";return"0"==r&&1/t==-s?"-0":r}},23305:function(e){e.exports=function(e){return function(t){return e(t)}}},34347:function(e,t,r){var n=r(67906),o=r(86185),i=r(19278),a=r(70235);e.exports=function(e,t){return t=n(t,e),null==(e=i(e,t))||delete e[a(o(t))]}},67906:function(e,t,r){var n=r(25614),o=r(67352),i=r(39365),a=r(3641);e.exports=function(e,t){return n(e)?e:o(e,t)?[e]:i(a(e))}},94420:function(e,t,r){var n=r(80098);e.exports=function(e){var t=new e.constructor(e.byteLength);return new n(t).set(new n(e)),t}},19243:function(e,t,r){e=r.nmd(e);var n=r(74288),o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,a=i&&i.exports===o?n.Buffer:void 0,s=a?a.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var r=e.length,n=s?s(r):new e.constructor(r);return e.copy(n),n}},20580:function(e,t,r){var n=r(94420);e.exports=function(e,t){var r=t?n(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.byteLength)}},23153:function(e){var t=/\w*$/;e.exports=function(e){var r=new e.constructor(e.source,t.exec(e));return r.lastIndex=e.lastIndex,r}},69775:function(e,t,r){var n=r(23910),o=n?n.prototype:void 0,i=o?o.valueOf:void 0;e.exports=function(e){return i?Object(i.call(e)):{}}},65919:function(e,t,r){var n=r(94420);e.exports=function(e,t){var r=t?n(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}},56075:function(e){e.exports=function(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t}},33485:function(e,t,r){var n=r(83295),o=r(83023);e.exports=function(e,t,r,i){var a=!r;r||(r={});for(var s=-1,u=t.length;++s<u;){var c=t[s],l=i?i(r[c],e[c],c,r,e):void 0;void 0===l&&(l=e[c]),a?o(r,c,l):n(r,c,l)}return r}},96962:function(e,t,r){var n=r(33485),o=r(80466);e.exports=function(e,t){return n(e,o(e),t)}},57972:function(e,t,r){var n=r(33485),o=r(28868);e.exports=function(e,t){return n(e,o(e),t)}},92077:function(e,t,r){var n=r(74288)["__core-js_shared__"];e.exports=n},4521:function(e,t,r){var n=r(39866),o=function(){try{var e=n(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();e.exports=o},17071:function(e,t,r){var n="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;e.exports=n},28529:function(e,t,r){var n=r(36452),o=r(80466),i=r(43228);e.exports=function(e){return n(e,i,o)}},75474:function(e,t,r){var n=r(36452),o=r(28868),i=r(18587);e.exports=function(e){return n(e,i,o)}},1507:function(e,t,r){var n=r(7545);e.exports=function(e,t){var r=e.__data__;return n(t)?r["string"==typeof t?"string":"hash"]:r.map}},39866:function(e,t,r){var n=r(57595),o=r(3138);e.exports=function(e,t){var r=o(e,t);return n(r)?r:void 0}},62602:function(e,t,r){var n=r(45070)(Object.getPrototypeOf,Object);e.exports=n},4479:function(e,t,r){var n=r(23910),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,s=n?n.toStringTag:void 0;e.exports=function(e){var t=i.call(e,s),r=e[s];try{e[s]=void 0;var n=!0}catch(e){}var o=a.call(e);return n&&(t?e[s]=r:delete e[s]),o}},80466:function(e,t,r){var n=r(42774),o=r(55716),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,s=a?function(e){return null==e?[]:n(a(e=Object(e)),function(t){return i.call(e,t)})}:o;e.exports=s},28868:function(e,t,r){var n=r(73817),o=r(62602),i=r(80466),a=r(55716),s=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)n(t,i(e)),e=o(e);return t}:a;e.exports=s},81690:function(e,t,r){var n=r(94975),o=r(88675),i=r(69308),a=r(41497),s=r(10880),u=r(54506),c=r(1292),l="[object Map]",f="[object Promise]",h="[object Set]",d="[object WeakMap]",p="[object DataView]",y=c(n),m=c(o),v=c(i),b=c(a),g=c(s),_=u;(n&&_(new n(new ArrayBuffer(1)))!=p||o&&_(new o)!=l||i&&_(i.resolve())!=f||a&&_(new a)!=h||s&&_(new s)!=d)&&(_=function(e){var t=u(e),r="[object Object]"==t?e.constructor:void 0,n=r?c(r):"";if(n)switch(n){case y:return p;case m:return l;case v:return f;case b:return h;case g:return d}return t}),e.exports=_},3138:function(e){e.exports=function(e,t){return null==e?void 0:e[t]}},43596:function(e,t,r){var n=r(20453);e.exports=function(){this.__data__=n?n(null):{},this.size=0}},35907:function(e){e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},35355:function(e,t,r){var n=r(20453),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(n){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(t,e)?t[e]:void 0}},39870:function(e,t,r){var n=r(20453),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return n?void 0!==t[e]:o.call(t,e)}},73372:function(e,t,r){var n=r(20453);e.exports=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=n&&void 0===t?"__lodash_hash_undefined__":t,this}},6914:function(e){var t=Object.prototype.hasOwnProperty;e.exports=function(e){var r=e.length,n=new e.constructor(r);return r&&"string"==typeof e[0]&&t.call(e,"index")&&(n.index=e.index,n.input=e.input),n}},32772:function(e,t,r){var n=r(94420),o=r(20580),i=r(23153),a=r(69775),s=r(65919);e.exports=function(e,t,r){var u=e.constructor;switch(t){case"[object ArrayBuffer]":return n(e);case"[object Boolean]":case"[object Date]":return new u(+e);case"[object DataView]":return o(e,r);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return s(e,r);case"[object Map]":case"[object Set]":return new u;case"[object Number]":case"[object String]":return new u(e);case"[object RegExp]":return i(e);case"[object Symbol]":return a(e)}}},32685:function(e,t,r){var n=r(32318),o=r(62602),i=r(35365);e.exports=function(e){return"function"!=typeof e.constructor||i(e)?{}:n(o(e))}},84257:function(e){var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,r){var n=typeof e;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}},67352:function(e,t,r){var n=r(25614),o=r(78371),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;e.exports=function(e,t){if(n(e))return!1;var r=typeof e;return!!("number"==r||"symbol"==r||"boolean"==r||null==e||o(e))||a.test(e)||!i.test(e)||null!=t&&e in Object(t)}},7545:function(e){e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},79551:function(e,t,r){var n,o=r(92077),i=(n=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"";e.exports=function(e){return!!i&&i in e}},35365:function(e){var t=Object.prototype;e.exports=function(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||t)}},62285:function(e){e.exports=function(){this.__data__=[],this.size=0}},28706:function(e,t,r){var n=r(24457),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,r=n(t,e);return!(r<0)&&(r==t.length-1?t.pop():o.call(t,r,1),--this.size,!0)}},63717:function(e,t,r){var n=r(24457);e.exports=function(e){var t=this.__data__,r=n(t,e);return r<0?void 0:t[r][1]}},78410:function(e,t,r){var n=r(24457);e.exports=function(e){return n(this.__data__,e)>-1}},13368:function(e,t,r){var n=r(24457);e.exports=function(e,t){var r=this.__data__,o=n(r,e);return o<0?(++this.size,r.push([e,t])):r[o][1]=t,this}},38764:function(e,t,r){var n=r(9855),o=r(99078),i=r(88675);e.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},78615:function(e,t,r){var n=r(1507);e.exports=function(e){var t=n(this,e).delete(e);return this.size-=t?1:0,t}},83391:function(e,t,r){var n=r(1507);e.exports=function(e){return n(this,e).get(e)}},53483:function(e,t,r){var n=r(1507);e.exports=function(e){return n(this,e).has(e)}},74724:function(e,t,r){var n=r(1507);e.exports=function(e,t){var r=n(this,e),o=r.size;return r.set(e,t),this.size+=r.size==o?0:1,this}},23787:function(e,t,r){var n=r(50967);e.exports=function(e){var t=n(e,function(e){return 500===r.size&&r.clear(),e}),r=t.cache;return t}},20453:function(e,t,r){var n=r(39866)(Object,"create");e.exports=n},77184:function(e,t,r){var n=r(45070)(Object.keys,Object);e.exports=n},92147:function(e){e.exports=function(e){var t=[];if(null!=e)for(var r in Object(e))t.push(r);return t}},39931:function(e,t,r){e=r.nmd(e);var n=r(17071),o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,a=i&&i.exports===o&&n.process,s=function(){try{var e=i&&i.require&&i.require("util").types;if(e)return e;return a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=s},80910:function(e){var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},45070:function(e){e.exports=function(e,t){return function(r){return e(t(r))}}},19278:function(e,t,r){var n=r(92167),o=r(99558);e.exports=function(e,t){return t.length<2?e:n(e,o(t,0,-1))}},74288:function(e,t,r){var n=r(17071),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();e.exports=i},84092:function(e,t,r){var n=r(99078);e.exports=function(){this.__data__=new n,this.size=0}},31663:function(e){e.exports=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}},69135:function(e){e.exports=function(e){return this.__data__.get(e)}},39552:function(e){e.exports=function(e){return this.__data__.has(e)}},63960:function(e,t,r){var n=r(99078),o=r(88675),i=r(76219);e.exports=function(e,t){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([e,t]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(e,t),this.size=r.size,this}},39365:function(e,t,r){var n=r(23787),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=n(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(o,function(e,r,n,o){t.push(n?o.replace(i,"$1"):r||e)}),t});e.exports=a},70235:function(e,t,r){var n=r(78371),o=1/0;e.exports=function(e){if("string"==typeof e||n(e))return e;var t=e+"";return"0"==t&&1/e==-o?"-0":t}},1292:function(e){var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},59128:function(e,t,r){var n=r(68829);e.exports=function(e){return n(e,5)}},37560:function(e){e.exports=function(e,t){return e===t||e!=e&&t!=t}},56569:function(e,t,r){var n=r(90370),o=r(10303),i=Object.prototype,a=i.hasOwnProperty,s=i.propertyIsEnumerable,u=n(function(){return arguments}())?n:function(e){return o(e)&&a.call(e,"callee")&&!s.call(e,"callee")};e.exports=u},25614:function(e){var t=Array.isArray;e.exports=t},5629:function(e,t,r){var n=r(86757),o=r(13973);e.exports=function(e){return null!=e&&o(e.length)&&!n(e)}},98051:function(e,t,r){e=r.nmd(e);var n=r(74288),o=r(7406),i=t&&!t.nodeType&&t,a=i&&e&&!e.nodeType&&e,s=a&&a.exports===i?n.Buffer:void 0,u=s?s.isBuffer:void 0;e.exports=u||o},86757:function(e,t,r){var n=r(54506),o=r(28302);e.exports=function(e){if(!o(e))return!1;var t=n(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},13973:function(e){e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},38839:function(e,t,r){var n=r(32012),o=r(23305),i=r(39931),a=i&&i.isMap,s=a?o(a):n;e.exports=s},28302:function(e){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},10303:function(e){e.exports=function(e){return null!=e&&"object"==typeof e}},87615:function(e,t,r){var n=r(34188),o=r(23305),i=r(39931),a=i&&i.isSet,s=a?o(a):n;e.exports=s},78371:function(e,t,r){var n=r(54506),o=r(10303);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==n(e)}},9792:function(e,t,r){var n=r(59332),o=r(23305),i=r(39931),a=i&&i.isTypedArray,s=a?o(a):n;e.exports=s},43228:function(e,t,r){var n=r(28579),o=r(4578),i=r(5629);e.exports=function(e){return i(e)?n(e):o(e)}},18587:function(e,t,r){var n=r(28579),o=r(22476),i=r(5629);e.exports=function(e){return i(e)?n(e,!0):o(e)}},86185:function(e){e.exports=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}},50967:function(e,t,r){var n=r(76219);function o(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw TypeError("Expected a function");var r=function(){var n=arguments,o=t?t.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=e.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,e.exports=o},69854:function(e,t,r){var n=r(87325);e.exports=function(e,t,r){return null==e?e:n(e,t,r)}},55716:function(e){e.exports=function(){return[]}},7406:function(e){e.exports=function(){return!1}},3641:function(e,t,r){var n=r(65020);e.exports=function(e){return null==e?"":n(e)}},10283:function(e,t,r){var n=r(34347);e.exports=function(e,t){return null==e||n(e,t)}},88687:function(e,t,r){var n,o,i;o=[],void 0!==(i="function"==typeof(n=function(){"use strict";var e=void 0!==r.g?r.g:self;if(void 0!==e.TextEncoder&&void 0!==e.TextDecoder)return{TextEncoder:e.TextEncoder,TextDecoder:e.TextDecoder};var t=["utf8","utf-8","unicode-1-1-utf-8"];return{TextEncoder:function(e){if(0>t.indexOf(e)&&null!=e)throw RangeError("Invalid encoding type. Only utf-8 is supported");this.encoding="utf-8",this.encode=function(e){if("string"!=typeof e)throw TypeError("passed argument must be of type string");var t=unescape(encodeURIComponent(e)),r=new Uint8Array(t.length);return t.split("").forEach(function(e,t){r[t]=e.charCodeAt(0)}),r}},TextDecoder:function(e,r){if(0>t.indexOf(e)&&null!=e)throw RangeError("Invalid encoding type. Only utf-8 is supported");if(this.encoding="utf-8",this.ignoreBOM=!1,this.fatal=void 0!==r&&"fatal"in r&&r.fatal,"boolean"!=typeof this.fatal)throw TypeError("fatal flag must be boolean");this.decode=function(e,t){if(void 0===e)return"";if("boolean"!=typeof(void 0!==t&&"stream"in t&&t.stream))throw TypeError("stream option must be boolean");if(ArrayBuffer.isView(e)){var r=new Uint8Array(e.buffer,e.byteOffset,e.byteLength),n=Array(r.length);return r.forEach(function(e,t){n[t]=String.fromCharCode(e)}),decodeURIComponent(escape(n.join("")))}throw TypeError("passed argument must be an array buffer view")}}}})?n.apply(t,o):n)&&(e.exports=i)},25566:function(e){var t,r,n,o=e.exports={};function i(){throw Error("setTimeout has not been defined")}function a(){throw Error("clearTimeout has not been defined")}function s(e){if(t===setTimeout)return setTimeout(e,0);if((t===i||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:i}catch(e){t=i}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(e){r=a}}();var u=[],c=!1,l=-1;function f(){c&&n&&(c=!1,n.length?u=n.concat(u):l=-1,u.length&&h())}function h(){if(!c){var e=s(f);c=!0;for(var t=u.length;t;){for(n=u,u=[];++l<t;)n&&n[l].run();l=-1,t=u.length}n=null,c=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function d(e,t){this.fun=e,this.array=t}function p(){}o.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];u.push(new d(e,t)),1!==u.length||c||s(h)},d.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=p,o.addListener=p,o.once=p,o.off=p,o.removeListener=p,o.removeAllListeners=p,o.emit=p,o.prependListener=p,o.prependOnceListener=p,o.listeners=function(e){return[]},o.binding=function(e){throw Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw Error("process.chdir is not supported")},o.umask=function(){return 0}},41822:function(e,t,r){"use strict";var n,o,i;function a(){}function s(e){return"object"==typeof e&&null!==e||"function"==typeof e}function u(e,t){try{Object.defineProperty(e,"name",{value:t,configurable:!0})}catch(e){}}r.d(t,{GJ:function(){return ru},Pz:function(){return tZ}});let c=Promise,l=Promise.resolve.bind(c),f=Promise.prototype.then,h=Promise.reject.bind(c);function d(e){return new c(e)}function p(e){return d(t=>t(e))}function y(e,t,r){return f.call(e,t,r)}function m(e,t,r){y(y(e,t,r),void 0,a)}function v(e,t){m(e,void 0,t)}function b(e){y(e,void 0,a)}let g=e=>{if("function"==typeof queueMicrotask)g=queueMicrotask;else{let e=p(void 0);g=t=>y(e,t)}return g(e)};function _(e,t,r){if("function"!=typeof e)throw TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function w(e,t,r){try{return p(_(e,t,r))}catch(e){return h(e)}}class S{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){let t=this._back,r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){let e=this._front,t=e,r=this._cursor,n=r+1,o=e._elements,i=o[r];return 16384===n&&(t=e._next,n=0),--this._size,this._cursor=n,e!==t&&(this._front=t),o[r]=void 0,i}forEach(e){let t=this._cursor,r=this._front,n=r._elements;for(;!(t===n.length&&void 0===r._next||t===n.length&&(n=(r=r._next)._elements,t=0,0===n.length));)e(n[t]),++t}peek(){let e=this._front,t=this._cursor;return e._elements[t]}}let E=Symbol("[[AbortSteps]]"),T=Symbol("[[ErrorSteps]]"),I=Symbol("[[CancelSteps]]"),O=Symbol("[[PullSteps]]"),R=Symbol("[[ReleaseSteps]]");function A(e,t){var r;e._ownerReadableStream=t,t._reader=e,"readable"===t._state?x(e):"closed"===t._state?(x(e),j(e)):(r=t._storedError,x(e),q(e,r))}function P(e,t){return t6(e._ownerReadableStream,t)}function C(e){var t;let r=e._ownerReadableStream;"readable"===r._state?q(e,TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):(t=TypeError("Reader was released and can no longer be used to monitor the stream's closedness"),x(e),q(e,t)),r._readableStreamController[R](),r._reader=void 0,e._ownerReadableStream=void 0}function N(e){return TypeError("Cannot "+e+" a stream using a released reader")}function x(e){e._closedPromise=d((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r})}function q(e,t){void 0!==e._closedPromise_reject&&(b(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function j(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}let k=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},D=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function L(e,t){if(void 0!==e&&"object"!=typeof e&&"function"!=typeof e)throw TypeError(`${t} is not an object.`)}function U(e,t){if("function"!=typeof e)throw TypeError(`${t} is not a function.`)}function M(e,t){if(!("object"==typeof e&&null!==e||"function"==typeof e))throw TypeError(`${t} is not an object.`)}function W(e,t,r){if(void 0===e)throw TypeError(`Parameter ${t} is required in '${r}'.`)}function F(e,t,r){if(void 0===e)throw TypeError(`${t} is required in '${r}'.`)}function B(e){return Number(e)}function G(e,t){var r,n;let o=Number.MAX_SAFE_INTEGER,i=Number(e);if(!k(i=0===(r=i)?0:r))throw TypeError(`${t} is not a finite number`);if((i=0===(n=D(i))?0:n)<0||i>o)throw TypeError(`${t} is outside the accepted range of 0 to ${o}, inclusive`);return k(i)&&0!==i?i:0}function V(e,t){if(!t3(e))throw TypeError(`${t} is not a ReadableStream.`)}function H(e){return new Q(e)}function $(e,t){e._reader._readRequests.push(t)}function z(e,t,r){let n=e._reader._readRequests.shift();r?n._closeSteps():n._chunkSteps(t)}function K(e){return e._reader._readRequests.length}function J(e){let t=e._reader;return void 0!==t&&!!Y(t)}class Q{constructor(e){if(W(e,1,"ReadableStreamDefaultReader"),V(e,"First parameter"),t4(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");A(this,e),this._readRequests=new S}get closed(){return Y(this)?this._closedPromise:h(ee("closed"))}cancel(e){return Y(this)?void 0===this._ownerReadableStream?h(N("cancel")):P(this,e):h(ee("cancel"))}read(){let e,t;if(!Y(this))return h(ee("read"));if(void 0===this._ownerReadableStream)return h(N("read from"));let r=d((r,n)=>{e=r,t=n});return X(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!Y(this))throw ee("releaseLock");void 0!==this._ownerReadableStream&&(C(this),Z(this,TypeError("Reader was released")))}}function Y(e){return!!s(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof Q}function X(e,t){let r=e._ownerReadableStream;r._disturbed=!0,"closed"===r._state?t._closeSteps():"errored"===r._state?t._errorSteps(r._storedError):r._readableStreamController[O](t)}function Z(e,t){let r=e._readRequests;e._readRequests=new S,r.forEach(e=>{e._errorSteps(t)})}function ee(e){return TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}function et(e){return e.slice()}function er(e,t,r,n,o){new Uint8Array(e).set(new Uint8Array(r,n,o),t)}Object.defineProperties(Q.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),u(Q.prototype.cancel,"cancel"),u(Q.prototype.read,"read"),u(Q.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Q.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});let en=e=>(en="function"==typeof e.transfer?e=>e.transfer():"function"==typeof structuredClone?e=>structuredClone(e,{transfer:[e]}):e=>e)(e),eo=e=>(eo="boolean"==typeof e.detached?e=>e.detached:e=>0===e.byteLength)(e);function ei(e,t,r){if(e.slice)return e.slice(t,r);let n=r-t,o=new ArrayBuffer(n);return er(o,0,e,t,n),o}function ea(e,t){let r=e[t];if(null!=r){if("function"!=typeof r)throw TypeError(`${String(t)} is not a function`);return r}}function es(e){try{let t=e.done,r=e.value;return y(l(r),e=>({done:t,value:e}))}catch(e){return h(e)}}let eu=null!==(i=null!==(n=Symbol.asyncIterator)&&void 0!==n?n:null===(o=Symbol.for)||void 0===o?void 0:o.call(Symbol,"Symbol.asyncIterator"))&&void 0!==i?i:"@@asyncIterator";function ec(e){let t=_(e.nextMethod,e.iterator,[]);if(!s(t))throw TypeError("The iterator.next() method must return an object");return t}class el{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){let e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?y(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){let t=()=>this._returnSteps(e);return this._ongoingPromise=this._ongoingPromise?y(this._ongoingPromise,t,t):t(),this._ongoingPromise}_nextSteps(){let e,t;if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let r=this._reader,n=d((r,n)=>{e=r,t=n});return X(r,{_chunkSteps:t=>{this._ongoingPromise=void 0,g(()=>e({value:t,done:!1}))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,C(r),e({value:void 0,done:!0})},_errorSteps:e=>{this._ongoingPromise=void 0,this._isFinished=!0,C(r),t(e)}}),n}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;let t=this._reader;if(!this._preventCancel){let r=P(t,e);return C(t),y(r,()=>({value:e,done:!0}),void 0)}return C(t),p({value:e,done:!0})}}let ef={next(){return eh(this)?this._asyncIteratorImpl.next():h(ed("next"))},return(e){return eh(this)?this._asyncIteratorImpl.return(e):h(ed("return"))},[eu](){return this}};function eh(e){if(!s(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof el}catch(e){return!1}}function ed(e){return TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}Object.defineProperty(ef,eu,{enumerable:!1});let ep=Number.isNaN||function(e){return e!=e};function ey(e){return new Uint8Array(ei(e.buffer,e.byteOffset,e.byteOffset+e.byteLength))}function em(e){let t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function ev(e,t,r){if("number"!=typeof r||ep(r)||r<0||r===1/0)throw RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function eb(e){e._queue=new S,e._queueTotalSize=0}function eg(e){return e===DataView}class e_{constructor(){throw TypeError("Illegal constructor")}get view(){if(!eE(this))throw eJ("view");return this._view}respond(e){if(!eE(this))throw eJ("respond");if(W(e,1,"respond"),e=G(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");if(eo(this._view.buffer))throw TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");e$(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!eE(this))throw eJ("respondWithNewView");if(W(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");if(eo(e.buffer))throw TypeError("The given view's buffer has been detached and so cannot be used as a response");ez(this._associatedReadableByteStreamController,e)}}Object.defineProperties(e_.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),u(e_.prototype.respond,"respond"),u(e_.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(e_.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class ew{constructor(){throw TypeError("Illegal constructor")}get byobRequest(){if(!eS(this))throw eQ("byobRequest");return eV(this)}get desiredSize(){if(!eS(this))throw eQ("desiredSize");return eH(this)}close(){if(!eS(this))throw eQ("close");if(this._closeRequested)throw TypeError("The stream has already been closed; do not close it again!");let e=this._controlledReadableByteStream._state;if("readable"!==e)throw TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);eW(this)}enqueue(e){if(!eS(this))throw eQ("enqueue");if(W(e,1,"enqueue"),!ArrayBuffer.isView(e))throw TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw TypeError("stream is closed or draining");let t=this._controlledReadableByteStream._state;if("readable"!==t)throw TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);eF(this,e)}error(e){if(!eS(this))throw eQ("error");eB(this,e)}[I](e){eI(this),eb(this);let t=this._cancelAlgorithm(e);return eM(this),t}[O](e){let t=this._controlledReadableByteStream;if(this._queueTotalSize>0)return void eG(this,e);let r=this._autoAllocateChunkSize;if(void 0!==r){let t;try{t=new ArrayBuffer(r)}catch(t){return void e._errorSteps(t)}let n={buffer:t,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(n)}$(t,e),eT(this)}[R](){if(this._pendingPullIntos.length>0){let e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new S,this._pendingPullIntos.push(e)}}}function eS(e){return!!s(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof ew}function eE(e){return!!s(e)&&!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof e_}function eT(e){if(function(e){let t=e._controlledReadableByteStream;return"readable"===t._state&&!e._closeRequested&&!!e._started&&!!(J(t)&&K(t)>0||eZ(t)&&eX(t)>0||eH(e)>0)}(e)){if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0,m(e._pullAlgorithm(),()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,eT(e)),null),t=>(eB(e,t),null))}}function eI(e){ek(e),e._pendingPullIntos=new S}function eO(e,t){let r=!1;"closed"===e._state&&(r=!0);let n=eA(t);"default"===t.readerType?z(e,n,r):function(e,t,r){let n=e._reader._readIntoRequests.shift();r?n._closeSteps(t):n._chunkSteps(t)}(e,n,r)}function eR(e,t){for(let r=0;r<t.length;++r)eO(e,t[r])}function eA(e){let t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function eP(e,t,r,n){e._queue.push({buffer:t,byteOffset:r,byteLength:n}),e._queueTotalSize+=n}function eC(e,t,r,n){let o;try{o=ei(t,r,r+n)}catch(t){throw eB(e,t),t}eP(e,o,0,n)}function eN(e,t){t.bytesFilled>0&&eC(e,t.buffer,t.byteOffset,t.bytesFilled),eU(e)}function ex(e,t){let r=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),n=t.bytesFilled+r,o=r,i=!1,a=n-n%t.elementSize;a>=t.minimumFill&&(o=a-t.bytesFilled,i=!0);let s=e._queue;for(;o>0;){let r=s.peek(),n=Math.min(o,r.byteLength),i=t.byteOffset+t.bytesFilled;er(t.buffer,i,r.buffer,r.byteOffset,n),r.byteLength===n?s.shift():(r.byteOffset+=n,r.byteLength-=n),e._queueTotalSize-=n,eq(e,n,t),o-=n}return i}function eq(e,t,r){r.bytesFilled+=t}function ej(e){0===e._queueTotalSize&&e._closeRequested?(eM(e),t5(e._controlledReadableByteStream)):eT(e)}function ek(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function eD(e){let t=[];for(;e._pendingPullIntos.length>0&&0!==e._queueTotalSize;){let r=e._pendingPullIntos.peek();ex(e,r)&&(eU(e),t.push(r))}return t}function eL(e,t){let r=e._pendingPullIntos.peek();ek(e),"closed"===e._controlledReadableByteStream._state?function(e,t){"none"===t.readerType&&eU(e);let r=e._controlledReadableByteStream;if(eZ(r)){let t=[];for(let n=0;n<eX(r);++n)t.push(eU(e));eR(r,t)}}(e,r):function(e,t,r){if(eq(0,t,r),"none"===r.readerType){eN(e,r);let t=eD(e);return void eR(e._controlledReadableByteStream,t)}if(r.bytesFilled<r.minimumFill)return;eU(e);let n=r.bytesFilled%r.elementSize;if(n>0){let t=r.byteOffset+r.bytesFilled;eC(e,r.buffer,t-n,n)}r.bytesFilled-=n;let o=eD(e);eO(e._controlledReadableByteStream,r),eR(e._controlledReadableByteStream,o)}(e,t,r),eT(e)}function eU(e){return e._pendingPullIntos.shift()}function eM(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function eW(e){let t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state){if(e._queueTotalSize>0)e._closeRequested=!0;else{if(e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek();if(t.bytesFilled%t.elementSize!=0){let t=TypeError("Insufficient bytes to fill elements in the given buffer");throw eB(e,t),t}}eM(e),t5(t)}}}function eF(e,t){let r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;let{buffer:n,byteOffset:o,byteLength:i}=t;if(eo(n))throw TypeError("chunk's buffer is detached and so cannot be enqueued");let a=en(n);if(e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek();if(eo(t.buffer))throw TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");ek(e),t.buffer=en(t.buffer),"none"===t.readerType&&eN(e,t)}if(J(r))((function(e){let t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(0===e._queueTotalSize)return;eG(e,t._readRequests.shift())}})(e),0===K(r))?eP(e,a,o,i):(e._pendingPullIntos.length>0&&eU(e),z(r,new Uint8Array(a,o,i),!1));else if(eZ(r)){eP(e,a,o,i);let t=eD(e);eR(e._controlledReadableByteStream,t)}else eP(e,a,o,i);eT(e)}function eB(e,t){let r=e._controlledReadableByteStream;"readable"===r._state&&(eI(e),eb(e),eM(e),t7(r,t))}function eG(e,t){let r=e._queue.shift();e._queueTotalSize-=r.byteLength,ej(e);let n=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(n)}function eV(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),n=Object.create(e_.prototype);n._associatedReadableByteStreamController=e,n._view=r,e._byobRequest=n}return e._byobRequest}function eH(e){let t=e._controlledReadableByteStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function e$(e,t){let r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t)throw TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===t)throw TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw RangeError("bytesWritten out of range")}r.buffer=en(r.buffer),eL(e,t)}function ez(e,t){let r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===t.byteLength)throw TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw RangeError("The region specified by view is larger than byobRequest");let n=t.byteLength;r.buffer=en(t.buffer),eL(e,n)}function eK(e,t,r,n,o,i,a){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,eb(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=i,t._pullAlgorithm=n,t._cancelAlgorithm=o,t._autoAllocateChunkSize=a,t._pendingPullIntos=new S,e._readableStreamController=t,m(p(r()),()=>(t._started=!0,eT(t),null),e=>(eB(t,e),null))}function eJ(e){return TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function eQ(e){return TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function eY(e,t){e._reader._readIntoRequests.push(t)}function eX(e){return e._reader._readIntoRequests.length}function eZ(e){let t=e._reader;return void 0!==t&&!!e1(t)}Object.defineProperties(ew.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),u(ew.prototype.close,"close"),u(ew.prototype.enqueue,"enqueue"),u(ew.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ew.prototype,Symbol.toStringTag,{value:"ReadableByteStreamController",configurable:!0});class e0{constructor(e){if(W(e,1,"ReadableStreamBYOBReader"),V(e,"First parameter"),t4(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");if(!eS(e._readableStreamController))throw TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");A(this,e),this._readIntoRequests=new S}get closed(){return e1(this)?this._closedPromise:h(e4("closed"))}cancel(e){return e1(this)?void 0===this._ownerReadableStream?h(N("cancel")):P(this,e):h(e4("cancel"))}read(e,t={}){let r,n,o;if(!e1(this))return h(e4("read"));if(!ArrayBuffer.isView(e))return h(TypeError("view must be an array buffer view"));if(0===e.byteLength)return h(TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return h(TypeError("view's buffer must have non-zero byteLength"));if(eo(e.buffer))return h(TypeError("view's buffer has been detached"));try{var i,a;i="options",L(t,i),r={min:G(null!==(a=null==t?void 0:t.min)&&void 0!==a?a:1,`${i} has member 'min' that`)}}catch(e){return h(e)}let s=r.min;if(0===s)return h(TypeError("options.min must be greater than 0"));if(eg(e.constructor)){if(s>e.byteLength)return h(RangeError("options.min must be less than or equal to view's byteLength"))}else if(s>e.length)return h(RangeError("options.min must be less than or equal to view's length"));if(void 0===this._ownerReadableStream)return h(N("read from"));let u=d((e,t)=>{n=e,o=t});return e2(this,e,s,{_chunkSteps:e=>n({value:e,done:!1}),_closeSteps:e=>n({value:e,done:!0}),_errorSteps:e=>o(e)}),u}releaseLock(){if(!e1(this))throw e4("releaseLock");void 0!==this._ownerReadableStream&&(C(this),e3(this,TypeError("Reader was released")))}}function e1(e){return!!s(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof e0}function e2(e,t,r,n){let o=e._ownerReadableStream;o._disturbed=!0,"errored"===o._state?n._errorSteps(o._storedError):function(e,t,r,n){let o;let i=e._controlledReadableByteStream,a=t.constructor,s=eg(a)?1:a.BYTES_PER_ELEMENT,{byteOffset:u,byteLength:c}=t;try{o=en(t.buffer)}catch(e){return void n._errorSteps(e)}let l={buffer:o,bufferByteLength:o.byteLength,byteOffset:u,byteLength:c,bytesFilled:0,minimumFill:r*s,elementSize:s,viewConstructor:a,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(l),void eY(i,n);if("closed"!==i._state){if(e._queueTotalSize>0){if(ex(e,l)){let t=eA(l);return ej(e),void n._chunkSteps(t)}if(e._closeRequested){let t=TypeError("Insufficient bytes to fill elements in the given buffer");return eB(e,t),void n._errorSteps(t)}}e._pendingPullIntos.push(l),eY(i,n),eT(e)}else{let e=new a(l.buffer,l.byteOffset,0);n._closeSteps(e)}}(o._readableStreamController,t,r,n)}function e3(e,t){let r=e._readIntoRequests;e._readIntoRequests=new S,r.forEach(e=>{e._errorSteps(t)})}function e4(e){return TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function e6(e,t){let{highWaterMark:r}=e;if(void 0===r)return t;if(ep(r)||r<0)throw RangeError("Invalid highWaterMark");return r}function e5(e){let{size:t}=e;return t||(()=>1)}function e7(e,t){L(e,t);let r=null==e?void 0:e.highWaterMark,n=null==e?void 0:e.size;return{highWaterMark:void 0===r?void 0:B(r),size:void 0===n?void 0:(U(n,`${t} has member 'size' that`),e=>B(n(e)))}}function e8(e,t){if(!tt(e))throw TypeError(`${t} is not a WritableStream.`)}Object.defineProperties(e0.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),u(e0.prototype.cancel,"cancel"),u(e0.prototype.read,"read"),u(e0.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(e0.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});class e9{constructor(e={},t={}){void 0===e?e=null:M(e,"First parameter");let r=e7(t,"Second parameter"),n=function(e,t){L(e,t);let r=null==e?void 0:e.abort,n=null==e?void 0:e.close,o=null==e?void 0:e.start,i=null==e?void 0:e.type,a=null==e?void 0:e.write;return{abort:void 0===r?void 0:(U(r,`${t} has member 'abort' that`),t=>w(r,e,[t])),close:void 0===n?void 0:(U(n,`${t} has member 'close' that`),()=>w(n,e,[])),start:void 0===o?void 0:(U(o,`${t} has member 'start' that`),t=>_(o,e,[t])),write:void 0===a?void 0:(U(a,`${t} has member 'write' that`),(t,r)=>w(a,e,[t,r])),type:i}}(e,"First parameter");if(te(this),void 0!==n.type)throw RangeError("Invalid type is specified");let o=e5(r);!function(e,t,r,n){let o,i;let a=Object.create(tb.prototype);o=void 0!==t.start?()=>t.start(a):()=>{},i=void 0!==t.write?e=>t.write(e,a):()=>p(void 0),t_(e,a,o,i,void 0!==t.close?()=>t.close():()=>p(void 0),void 0!==t.abort?e=>t.abort(e):()=>p(void 0),r,n)}(this,n,e6(r,1),o)}get locked(){if(!tt(this))throw tO("locked");return tr(this)}abort(e){return tt(this)?tr(this)?h(TypeError("Cannot abort a stream that already has a writer")):tn(this,e):h(tO("abort"))}close(){return tt(this)?tr(this)?h(TypeError("Cannot close a stream that already has a writer")):tu(this)?h(TypeError("Cannot close an already-closing stream")):to(this):h(tO("close"))}getWriter(){if(!tt(this))throw tO("getWriter");return new tf(this)}}function te(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new S,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function tt(e){return!!s(e)&&!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof e9}function tr(e){return void 0!==e._writer}function tn(e,t){var r;if("closed"===e._state||"errored"===e._state)return p(void 0);e._writableStreamController._abortReason=t,null===(r=e._writableStreamController._abortController)||void 0===r||r.abort(t);let n=e._state;if("closed"===n||"errored"===n)return p(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let o=!1;"erroring"===n&&(o=!0,t=void 0);let i=d((r,n)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:n,_reason:t,_wasAlreadyErroring:o}});return e._pendingAbortRequest._promise=i,o||ta(e,t),i}function to(e){var t;let r=e._state;if("closed"===r||"errored"===r)return h(TypeError(`The stream (in ${r} state) is not in the writable state and cannot be closed`));let n=d((t,r)=>{e._closeRequest={_resolve:t,_reject:r}}),o=e._writer;return void 0!==o&&e._backpressure&&"writable"===r&&tD(o),ev(t=e._writableStreamController,tv,0),tE(t),n}function ti(e,t){"writable"!==e._state?ts(e):ta(e,t)}function ta(e,t){let r=e._writableStreamController;e._state="erroring",e._storedError=t;let n=e._writer;void 0!==n&&tp(n,t),!(void 0!==e._inFlightWriteRequest||void 0!==e._inFlightCloseRequest)&&r._started&&ts(e)}function ts(e){e._state="errored",e._writableStreamController[T]();let t=e._storedError;if(e._writeRequests.forEach(e=>{e._reject(t)}),e._writeRequests=new S,void 0===e._pendingAbortRequest)return void tc(e);let r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring)return r._reject(t),void tc(e);m(e._writableStreamController[E](r._reason),()=>(r._resolve(),tc(e),null),t=>(r._reject(t),tc(e),null))}function tu(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function tc(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);let t=e._writer;void 0!==t&&tN(t,e._storedError)}function tl(e,t){let r=e._writer;void 0!==r&&t!==e._backpressure&&(t?tq(r):tD(r)),e._backpressure=t}Object.defineProperties(e9.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),u(e9.prototype.abort,"abort"),u(e9.prototype.close,"close"),u(e9.prototype.getWriter,"getWriter"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(e9.prototype,Symbol.toStringTag,{value:"WritableStream",configurable:!0});class tf{constructor(e){if(W(e,1,"WritableStreamDefaultWriter"),e8(e,"First parameter"),tr(e))throw TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;let t=e._state;if("writable"===t)!tu(e)&&e._backpressure?tq(this):(tq(this),tD(this)),tC(this);else if("erroring"===t)tj(this,e._storedError),tC(this);else if("closed"===t)tq(this),tD(this),tC(this),tx(this);else{let t=e._storedError;tj(this,t),tC(this),tN(this,t)}}get closed(){return th(this)?this._closedPromise:h(tA("closed"))}get desiredSize(){if(!th(this))throw tA("desiredSize");if(void 0===this._ownerWritableStream)throw tP("desiredSize");return function(e){let t=e._ownerWritableStream,r=t._state;return"errored"===r||"erroring"===r?null:"closed"===r?0:tS(t._writableStreamController)}(this)}get ready(){return th(this)?this._readyPromise:h(tA("ready"))}abort(e){return th(this)?void 0===this._ownerWritableStream?h(tP("abort")):tn(this._ownerWritableStream,e):h(tA("abort"))}close(){if(!th(this))return h(tA("close"));let e=this._ownerWritableStream;return void 0===e?h(tP("close")):tu(e)?h(TypeError("Cannot close an already-closing stream")):td(this)}releaseLock(){if(!th(this))throw tA("releaseLock");void 0!==this._ownerWritableStream&&ty(this)}write(e){return th(this)?void 0===this._ownerWritableStream?h(tP("write to")):tm(this,e):h(tA("write"))}}function th(e){return!!s(e)&&!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof tf}function td(e){return to(e._ownerWritableStream)}function tp(e,t){"pending"===e._readyPromiseState?tk(e,t):tj(e,t)}function ty(e){let t=e._ownerWritableStream,r=TypeError("Writer was released and can no longer be used to monitor the stream's closedness");tp(e,r),"pending"===e._closedPromiseState||tC(e),tN(e,r),t._writer=void 0,e._ownerWritableStream=void 0}function tm(e,t){let r=e._ownerWritableStream,n=r._writableStreamController,o=function(e,t){if(void 0===e._strategySizeAlgorithm)return 1;try{return e._strategySizeAlgorithm(t)}catch(t){return tT(e,t),1}}(n,t);if(r!==e._ownerWritableStream)return h(tP("write to"));let i=r._state;if("errored"===i)return h(r._storedError);if(tu(r)||"closed"===i)return h(TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===i)return h(r._storedError);let a=d((e,t)=>{r._writeRequests.push({_resolve:e,_reject:t})});return function(e,t,r){try{ev(e,t,r)}catch(t){return void tT(e,t)}let n=e._controlledWritableStream;tu(n)||"writable"!==n._state||tl(n,0>=tS(e)),tE(e)}(n,t,o),a}Object.defineProperties(tf.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),u(tf.prototype.abort,"abort"),u(tf.prototype.close,"close"),u(tf.prototype.releaseLock,"releaseLock"),u(tf.prototype.write,"write"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(tf.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});let tv={};class tb{constructor(){throw TypeError("Illegal constructor")}get abortReason(){if(!tg(this))throw tR("abortReason");return this._abortReason}get signal(){if(!tg(this))throw tR("signal");if(void 0===this._abortController)throw TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(e){if(!tg(this))throw tR("error");"writable"===this._controlledWritableStream._state&&tI(this,e)}[E](e){let t=this._abortAlgorithm(e);return tw(this),t}[T](){eb(this)}}function tg(e){return!!s(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof tb}function t_(e,t,r,n,o,i,a,s){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,eb(t),t._abortReason=void 0,t._abortController=function(){if("function"==typeof AbortController)return new AbortController}(),t._started=!1,t._strategySizeAlgorithm=s,t._strategyHWM=a,t._writeAlgorithm=n,t._closeAlgorithm=o,t._abortAlgorithm=i,tl(e,0>=tS(t)),m(p(r()),()=>(t._started=!0,tE(t),null),r=>(t._started=!0,ti(e,r),null))}function tw(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function tS(e){return e._strategyHWM-e._queueTotalSize}function tE(e){let t=e._controlledWritableStream;if(!e._started||void 0!==t._inFlightWriteRequest)return;if("erroring"===t._state)return void ts(t);if(0===e._queue.length)return;let r=e._queue.peek().value;r===tv?function(e){let t=e._controlledWritableStream;t._inFlightCloseRequest=t._closeRequest,t._closeRequest=void 0,em(e);let r=e._closeAlgorithm();tw(e),m(r,()=>((function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";let t=e._writer;void 0!==t&&tx(t)})(t),null),e=>(t._inFlightCloseRequest._reject(e),t._inFlightCloseRequest=void 0,void 0!==t._pendingAbortRequest&&(t._pendingAbortRequest._reject(e),t._pendingAbortRequest=void 0),ti(t,e),null))}(e):function(e,t){let r=e._controlledWritableStream;r._inFlightWriteRequest=r._writeRequests.shift(),m(e._writeAlgorithm(t),()=>{r._inFlightWriteRequest._resolve(void 0),r._inFlightWriteRequest=void 0;let t=r._state;return em(e),tu(r)||"writable"!==t||tl(r,0>=tS(e)),tE(e),null},t=>("writable"===r._state&&tw(e),r._inFlightWriteRequest._reject(t),r._inFlightWriteRequest=void 0,ti(r,t),null))}(e,r)}function tT(e,t){"writable"===e._controlledWritableStream._state&&tI(e,t)}function tI(e,t){let r=e._controlledWritableStream;tw(e),ta(r,t)}function tO(e){return TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function tR(e){return TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function tA(e){return TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function tP(e){return TypeError("Cannot "+e+" a stream using a released writer")}function tC(e){e._closedPromise=d((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"})}function tN(e,t){void 0!==e._closedPromise_reject&&(b(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function tx(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function tq(e){e._readyPromise=d((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r}),e._readyPromiseState="pending"}function tj(e,t){tq(e),tk(e,t)}function tk(e,t){void 0!==e._readyPromise_reject&&(b(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function tD(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(tb.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(tb.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});let tL="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0,tU=function(){let e=null==tL?void 0:tL.DOMException;return!function(e){if("function"!=typeof e&&"object"!=typeof e||"DOMException"!==e.name)return!1;try{return new e,!0}catch(e){return!1}}(e)?void 0:e}()||function(){let e=function(e,t){this.message=e||"",this.name=t||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return u(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}();function tM(e,t,r,n,o,i){let s=H(e),u=new tf(t);e._disturbed=!0;let c=!1,l=p(void 0);return d((f,g)=>{var _,w;let S;if(void 0!==i){if(S=()=>{let r=void 0!==i.reason?i.reason:new tU("Aborted","AbortError"),a=[];n||a.push(()=>"writable"===t._state?tn(t,r):p(void 0)),o||a.push(()=>"readable"===e._state?t6(e,r):p(void 0)),I(()=>Promise.all(a.map(e=>e())),!0,r)},i.aborted)return void S();i.addEventListener("abort",S)}if(T(e,s._closedPromise,e=>(n?O(!0,e):I(()=>tn(t,e),!0,e),null)),T(t,u._closedPromise,t=>(o?O(!0,t):I(()=>t6(e,t),!0,t),null)),_=s._closedPromise,w=()=>(r?O():I(()=>(function(e){let t=e._ownerWritableStream,r=t._state;return tu(t)||"closed"===r?p(void 0):"errored"===r?h(t._storedError):td(e)})(u)),null),"closed"===e._state?w():m(_,w),tu(t)||"closed"===t._state){let t=TypeError("the destination writable stream closed before all data could be piped to it");o?O(!0,t):I(()=>t6(e,t),!0,t)}function E(){let e=l;return y(l,()=>e!==l?E():void 0)}function T(e,t,r){"errored"===e._state?r(e._storedError):v(t,r)}function I(e,r,n){function o(){return m(e(),()=>R(r,n),e=>R(!0,e)),null}c||(c=!0,"writable"!==t._state||tu(t)?o():m(E(),o))}function O(e,r){c||(c=!0,"writable"!==t._state||tu(t)?R(e,r):m(E(),()=>R(e,r)))}function R(e,t){return ty(u),C(s),void 0!==i&&i.removeEventListener("abort",S),e?g(t):f(void 0),null}b(d((e,t)=>{!function r(n){n?e():y(c?p(!0):y(u._readyPromise,()=>d((e,t)=>{X(s,{_chunkSteps:t=>{l=y(tm(u,t),void 0,a),e(!1)},_closeSteps:()=>e(!0),_errorSteps:t})})),r,t)}(!1)}))})}class tW{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!tF(this))throw tY("desiredSize");return tK(this)}close(){if(!tF(this))throw tY("close");if(!tJ(this))throw TypeError("The stream is not in a state that permits close");tH(this)}enqueue(e){if(!tF(this))throw tY("enqueue");if(!tJ(this))throw TypeError("The stream is not in a state that permits enqueue");return t$(this,e)}error(e){if(!tF(this))throw tY("error");tz(this,e)}[I](e){eb(this);let t=this._cancelAlgorithm(e);return tV(this),t}[O](e){let t=this._controlledReadableStream;if(this._queue.length>0){let r=em(this);this._closeRequested&&0===this._queue.length?(tV(this),t5(t)):tB(this),e._chunkSteps(r)}else $(t,e),tB(this)}[R](){}}function tF(e){return!!s(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof tW}function tB(e){if(tG(e)){if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0,m(e._pullAlgorithm(),()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,tB(e)),null),t=>(tz(e,t),null))}}function tG(e){let t=e._controlledReadableStream;return!!tJ(e)&&!!e._started&&(!!(t4(t)&&K(t)>0)||tK(e)>0)}function tV(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function tH(e){if(!tJ(e))return;let t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(tV(e),t5(t))}function t$(e,t){if(!tJ(e))return;let r=e._controlledReadableStream;if(t4(r)&&K(r)>0)z(r,t,!1);else{let r;try{r=e._strategySizeAlgorithm(t)}catch(t){throw tz(e,t),t}try{ev(e,t,r)}catch(t){throw tz(e,t),t}}tB(e)}function tz(e,t){let r=e._controlledReadableStream;"readable"===r._state&&(eb(e),tV(e),t7(r,t))}function tK(e){let t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function tJ(e){let t=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===t}function tQ(e,t,r,n,o,i,a){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,eb(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=a,t._strategyHWM=i,t._pullAlgorithm=n,t._cancelAlgorithm=o,e._readableStreamController=t,m(p(r()),()=>(t._started=!0,tB(t),null),e=>(tz(t,e),null))}function tY(e){return TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function tX(e,t){L(e,t);let r=null==e?void 0:e.preventAbort,n=null==e?void 0:e.preventCancel,o=null==e?void 0:e.preventClose,i=null==e?void 0:e.signal;return void 0!==i&&function(e,t){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw TypeError(`${t} is not an AbortSignal.`)}(i,`${t} has member 'signal' that`),{preventAbort:!!r,preventCancel:!!n,preventClose:!!o,signal:i}}Object.defineProperties(tW.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),u(tW.prototype.close,"close"),u(tW.prototype.enqueue,"enqueue"),u(tW.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(tW.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});class tZ{constructor(e={},t={}){void 0===e?e=null:M(e,"First parameter");let r=e7(t,"Second parameter"),n=function(e,t){L(e,t);let r=null==e?void 0:e.autoAllocateChunkSize,n=null==e?void 0:e.cancel,o=null==e?void 0:e.pull,i=null==e?void 0:e.start,a=null==e?void 0:e.type;return{autoAllocateChunkSize:void 0===r?void 0:G(r,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===n?void 0:(U(n,`${t} has member 'cancel' that`),t=>w(n,e,[t])),pull:void 0===o?void 0:(U(o,`${t} has member 'pull' that`),t=>w(o,e,[t])),start:void 0===i?void 0:(U(i,`${t} has member 'start' that`),t=>_(i,e,[t])),type:void 0===a?void 0:function(e,t){if("bytes"!=(e=`${e}`))throw TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}(a,`${t} has member 'type' that`)}}(e,"First parameter");if(t2(this),"bytes"===n.type){if(void 0!==r.size)throw RangeError("The strategy for a byte stream cannot have a size function");!function(e,t,r){let n,o,i;let a=Object.create(ew.prototype);n=void 0!==t.start?()=>t.start(a):()=>{},o=void 0!==t.pull?()=>t.pull(a):()=>p(void 0),i=void 0!==t.cancel?e=>t.cancel(e):()=>p(void 0);let s=t.autoAllocateChunkSize;if(0===s)throw TypeError("autoAllocateChunkSize must be greater than 0");eK(e,a,n,o,i,r,s)}(this,n,e6(r,0))}else{let e=e5(r);!function(e,t,r,n){let o,i;let a=Object.create(tW.prototype);o=void 0!==t.start?()=>t.start(a):()=>{},i=void 0!==t.pull?()=>t.pull(a):()=>p(void 0),tQ(e,a,o,i,void 0!==t.cancel?e=>t.cancel(e):()=>p(void 0),r,n)}(this,n,e6(r,1),e)}}get locked(){if(!t3(this))throw t8("locked");return t4(this)}cancel(e){return t3(this)?t4(this)?h(TypeError("Cannot cancel a stream that already has a reader")):t6(this,e):h(t8("cancel"))}getReader(e){if(!t3(this))throw t8("getReader");return void 0===function(e,t){L(e,t);let r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:function(e,t){if("byob"!=(e=`${e}`))throw TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}(r,`${t} has member 'mode' that`)}}(e,"First parameter").mode?H(this):new e0(this)}pipeThrough(e,t={}){if(!t3(this))throw t8("pipeThrough");W(e,1,"pipeThrough");let r=function(e,t){L(e,t);let r=null==e?void 0:e.readable;F(r,"readable","ReadableWritablePair"),V(r,`${t} has member 'readable' that`);let n=null==e?void 0:e.writable;return F(n,"writable","ReadableWritablePair"),e8(n,`${t} has member 'writable' that`),{readable:r,writable:n}}(e,"First parameter"),n=tX(t,"Second parameter");if(t4(this))throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(tr(r.writable))throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return b(tM(this,r.writable,n.preventClose,n.preventAbort,n.preventCancel,n.signal)),r.readable}pipeTo(e,t={}){let r;if(!t3(this))return h(t8("pipeTo"));if(void 0===e)return h("Parameter 1 is required in 'pipeTo'.");if(!tt(e))return h(TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));try{r=tX(t,"Second parameter")}catch(e){return h(e)}return t4(this)?h(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):tr(e)?h(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):tM(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!t3(this))throw t8("tee");return et(eS(this._readableStreamController)?function(e){let t,r,n,o,i,a=H(e),s=!1,u=!1,c=!1,l=!1,f=!1,h=d(e=>{i=e});function y(e){v(e._closedPromise,t=>(e!==a||(eB(n._readableStreamController,t),eB(o._readableStreamController,t),l&&f||i(void 0)),null))}function m(){e1(a)&&(C(a),y(a=H(e))),X(a,{_chunkSteps:t=>{g(()=>{u=!1,c=!1;let r=t;if(!l&&!f)try{r=ey(t)}catch(t){return eB(n._readableStreamController,t),eB(o._readableStreamController,t),void i(t6(e,t))}l||eF(n._readableStreamController,t),f||eF(o._readableStreamController,r),s=!1,u?_():c&&w()})},_closeSteps:()=>{s=!1,l||eW(n._readableStreamController),f||eW(o._readableStreamController),n._readableStreamController._pendingPullIntos.length>0&&e$(n._readableStreamController,0),o._readableStreamController._pendingPullIntos.length>0&&e$(o._readableStreamController,0),l&&f||i(void 0)},_errorSteps:()=>{s=!1}})}function b(t,r){Y(a)&&(C(a),y(a=new e0(e)));let h=r?o:n,d=r?n:o;e2(a,t,1,{_chunkSteps:t=>{g(()=>{u=!1,c=!1;let n=r?f:l;if(r?l:f)n||ez(h._readableStreamController,t);else{let r;try{r=ey(t)}catch(t){return eB(h._readableStreamController,t),eB(d._readableStreamController,t),void i(t6(e,t))}n||ez(h._readableStreamController,t),eF(d._readableStreamController,r)}s=!1,u?_():c&&w()})},_closeSteps:e=>{s=!1;let t=r?f:l,n=r?l:f;t||eW(h._readableStreamController),n||eW(d._readableStreamController),void 0!==e&&(t||ez(h._readableStreamController,e),!n&&d._readableStreamController._pendingPullIntos.length>0&&e$(d._readableStreamController,0)),t&&n||i(void 0)},_errorSteps:()=>{s=!1}})}function _(){if(s)return u=!0,p(void 0);s=!0;let e=eV(n._readableStreamController);return null===e?m():b(e._view,!1),p(void 0)}function w(){if(s)return c=!0,p(void 0);s=!0;let e=eV(o._readableStreamController);return null===e?m():b(e._view,!0),p(void 0)}function S(){}return n=t1(S,_,function(n){if(l=!0,t=n,f){let n=t6(e,et([t,r]));i(n)}return h}),o=t1(S,w,function(n){if(f=!0,r=n,l){let n=t6(e,et([t,r]));i(n)}return h}),y(a),[n,o]}(this):function(e,t){let r=H(e),n,o,i,a,s,u=!1,c=!1,l=!1,f=!1,h=d(e=>{s=e});function y(){return u?c=!0:(u=!0,X(r,{_chunkSteps:e=>{g(()=>{c=!1,l||t$(i._readableStreamController,e),f||t$(a._readableStreamController,e),u=!1,c&&y()})},_closeSteps:()=>{u=!1,l||tH(i._readableStreamController),f||tH(a._readableStreamController),l&&f||s(void 0)},_errorSteps:()=>{u=!1}})),p(void 0)}function m(){}return i=t0(m,y,function(t){if(l=!0,n=t,f){let t=t6(e,et([n,o]));s(t)}return h}),a=t0(m,y,function(t){if(f=!0,o=t,l){let t=t6(e,et([n,o]));s(t)}return h}),v(r._closedPromise,e=>(tz(i._readableStreamController,e),tz(a._readableStreamController,e),l&&f||s(void 0),null)),[i,a]}(this))}values(e){if(!t3(this))throw t8("values");return function(e,t){let r=new el(H(e),t),n=Object.create(ef);return n._asyncIteratorImpl=r,n}(this,(L(e,"First parameter"),{preventCancel:!!(null==e?void 0:e.preventCancel)}).preventCancel)}[eu](e){return this.values(e)}static from(e){var t;let r;return s(e)&&void 0!==e.getReader?(t=e.getReader(),r=t0(a,function(){let e;try{e=t.read()}catch(e){return h(e)}return y(e,e=>{if(!s(e))throw TypeError("The promise returned by the reader.read() method must fulfill with an object");if(e.done)tH(r._readableStreamController);else{let t=e.value;t$(r._readableStreamController,t)}},void 0)},function(e){try{return p(t.cancel(e))}catch(e){return h(e)}},0)):function(e){let t;let r=function e(t,r="sync",n){if(void 0===n){if("async"===r){if(void 0===(n=ea(t,eu)))return function(e){let t={next(){let t;try{t=ec(e)}catch(e){return h(e)}return es(t)},return(t){let r;try{let n=ea(e.iterator,"return");if(void 0===n)return p({done:!0,value:t});r=_(n,e.iterator,[t])}catch(e){return h(e)}return s(r)?es(r):h(TypeError("The iterator.return() method must return an object"))}};return{iterator:t,nextMethod:t.next,done:!1}}(e(t,"sync",ea(t,Symbol.iterator)))}else n=ea(t,Symbol.iterator)}if(void 0===n)throw TypeError("The object is not iterable");let o=_(n,t,[]);if(!s(o))throw TypeError("The iterator method must return an object");return{iterator:o,nextMethod:o.next,done:!1}}(e,"async");return t=t0(a,function(){let e;try{e=ec(r)}catch(e){return h(e)}return y(p(e),e=>{if(!s(e))throw TypeError("The promise returned by the iterator.next() method must fulfill with an object");if(e.done)tH(t._readableStreamController);else{let r=e.value;t$(t._readableStreamController,r)}},void 0)},function(e){let t;let n=r.iterator;try{t=ea(n,"return")}catch(e){return h(e)}return void 0===t?p(void 0):y(w(t,n,[e]),e=>{if(!s(e))throw TypeError("The promise returned by the iterator.return() method must fulfill with an object")},void 0)},0)}(e)}}function t0(e,t,r,n=1,o=()=>1){let i=Object.create(tZ.prototype);return t2(i),tQ(i,Object.create(tW.prototype),e,t,r,n,o),i}function t1(e,t,r){let n=Object.create(tZ.prototype);return t2(n),eK(n,Object.create(ew.prototype),e,t,r,0,void 0),n}function t2(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function t3(e){return!!s(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof tZ}function t4(e){return void 0!==e._reader}function t6(e,t){if(e._disturbed=!0,"closed"===e._state)return p(void 0);if("errored"===e._state)return h(e._storedError);t5(e);let r=e._reader;if(void 0!==r&&e1(r)){let e=r._readIntoRequests;r._readIntoRequests=new S,e.forEach(e=>{e._closeSteps(void 0)})}return y(e._readableStreamController[I](t),a,void 0)}function t5(e){e._state="closed";let t=e._reader;if(void 0!==t&&(j(t),Y(t))){let e=t._readRequests;t._readRequests=new S,e.forEach(e=>{e._closeSteps()})}}function t7(e,t){e._state="errored",e._storedError=t;let r=e._reader;void 0!==r&&(q(r,t),Y(r)?Z(r,t):e3(r,t))}function t8(e){return TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function t9(e,t){L(e,t);let r=null==e?void 0:e.highWaterMark;return F(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:B(r)}}Object.defineProperties(tZ,{from:{enumerable:!0}}),Object.defineProperties(tZ.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),u(tZ.from,"from"),u(tZ.prototype.cancel,"cancel"),u(tZ.prototype.getReader,"getReader"),u(tZ.prototype.pipeThrough,"pipeThrough"),u(tZ.prototype.pipeTo,"pipeTo"),u(tZ.prototype.tee,"tee"),u(tZ.prototype.values,"values"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(tZ.prototype,Symbol.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(tZ.prototype,eu,{value:tZ.prototype.values,writable:!0,configurable:!0});let re=e=>e.byteLength;u(re,"size");class rt{constructor(e){W(e,1,"ByteLengthQueuingStrategy"),e=t9(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!rn(this))throw rr("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!rn(this))throw rr("size");return re}}function rr(e){return TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function rn(e){return!!s(e)&&!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof rt}Object.defineProperties(rt.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(rt.prototype,Symbol.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});let ro=()=>1;u(ro,"size");class ri{constructor(e){W(e,1,"CountQueuingStrategy"),e=t9(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!rs(this))throw ra("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!rs(this))throw ra("size");return ro}}function ra(e){return TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function rs(e){return!!s(e)&&!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof ri}Object.defineProperties(ri.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ri.prototype,Symbol.toStringTag,{value:"CountQueuingStrategy",configurable:!0});class ru{constructor(e={},t={},r={}){let n;void 0===e&&(e=null);let o=e7(t,"Second parameter"),i=e7(r,"Third parameter"),a=function(e,t){L(e,t);let r=null==e?void 0:e.cancel,n=null==e?void 0:e.flush,o=null==e?void 0:e.readableType,i=null==e?void 0:e.start,a=null==e?void 0:e.transform,s=null==e?void 0:e.writableType;return{cancel:void 0===r?void 0:(U(r,`${t} has member 'cancel' that`),t=>w(r,e,[t])),flush:void 0===n?void 0:(U(n,`${t} has member 'flush' that`),t=>w(n,e,[t])),readableType:o,start:void 0===i?void 0:(U(i,`${t} has member 'start' that`),t=>_(i,e,[t])),transform:void 0===a?void 0:(U(a,`${t} has member 'transform' that`),(t,r)=>w(a,e,[t,r])),writableType:s}}(e,"First parameter");if(void 0!==a.readableType)throw RangeError("Invalid readableType specified");if(void 0!==a.writableType)throw RangeError("Invalid writableType specified");let s=e6(i,0),u=e5(i),c=e6(o,1),l=e5(o);!function(e,t,r,n,o,i){function a(){return t}e._writable=function(e,t,r,n,o=1,i=()=>1){let a=Object.create(e9.prototype);return te(a),t_(a,Object.create(tb.prototype),e,t,r,n,o,i),a}(a,function(t){return function(e,t){let r=e._transformStreamController;return e._backpressure?y(e._backpressureChangePromise,()=>{let n=e._writable;if("erroring"===n._state)throw n._storedError;return rb(r,t)},void 0):rb(r,t)}(e,t)},function(){return function(e){let t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;let r=e._readable;t._finishPromise=d((e,r)=>{t._finishPromise_resolve=e,t._finishPromise_reject=r});let n=t._flushAlgorithm();return rm(t),m(n,()=>("errored"===r._state?rw(t,r._storedError):(tH(r._readableStreamController),r_(t)),null),e=>(tz(r._readableStreamController,e),rw(t,e),null)),t._finishPromise}(e)},function(t){return function(e,t){let r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;let n=e._readable;r._finishPromise=d((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t});let o=r._cancelAlgorithm(t);return rm(r),m(o,()=>("errored"===n._state?rw(r,n._storedError):(tz(n._readableStreamController,t),r_(r)),null),e=>(tz(n._readableStreamController,e),rw(r,e),null)),r._finishPromise}(e,t)},r,n),e._readable=t0(a,function(){return rd(e,!1),e._backpressureChangePromise},function(t){return function(e,t){let r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;let n=e._writable;r._finishPromise=d((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t});let o=r._cancelAlgorithm(t);return rm(r),m(o,()=>("errored"===n._state?rw(r,n._storedError):(tT(n._writableStreamController,t),rh(e),r_(r)),null),t=>(tT(n._writableStreamController,t),rh(e),rw(r,t),null)),r._finishPromise}(e,t)},o,i),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,rd(e,!0),e._transformStreamController=void 0}(this,d(e=>{n=e}),c,l,s,u),function(e,t){let r,n,o;let i=Object.create(rp.prototype);r=void 0!==t.transform?e=>t.transform(e,i):e=>{try{return rv(i,e),p(void 0)}catch(e){return h(e)}},n=void 0!==t.flush?()=>t.flush(i):()=>p(void 0),o=void 0!==t.cancel?e=>t.cancel(e):()=>p(void 0),i._controlledTransformStream=e,e._transformStreamController=i,i._transformAlgorithm=r,i._flushAlgorithm=n,i._cancelAlgorithm=o,i._finishPromise=void 0,i._finishPromise_resolve=void 0,i._finishPromise_reject=void 0}(this,a),void 0!==a.start?n(a.start(this._transformStreamController)):n(void 0)}get readable(){if(!rc(this))throw rS("readable");return this._readable}get writable(){if(!rc(this))throw rS("writable");return this._writable}}function rc(e){return!!s(e)&&!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof ru}function rl(e,t){tz(e._readable._readableStreamController,t),rf(e,t)}function rf(e,t){rm(e._transformStreamController),tT(e._writable._writableStreamController,t),rh(e)}function rh(e){e._backpressure&&rd(e,!1)}function rd(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=d(t=>{e._backpressureChangePromise_resolve=t}),e._backpressure=t}Object.defineProperties(ru.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ru.prototype,Symbol.toStringTag,{value:"TransformStream",configurable:!0});class rp{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!ry(this))throw rg("desiredSize");return tK(this._controlledTransformStream._readable._readableStreamController)}enqueue(e){if(!ry(this))throw rg("enqueue");rv(this,e)}error(e){if(!ry(this))throw rg("error");rl(this._controlledTransformStream,e)}terminate(){if(!ry(this))throw rg("terminate");!function(e){let t=e._controlledTransformStream;tH(t._readable._readableStreamController),rf(t,TypeError("TransformStream terminated"))}(this)}}function ry(e){return!!s(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof rp}function rm(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}function rv(e,t){let r=e._controlledTransformStream,n=r._readable._readableStreamController;if(!tJ(n))throw TypeError("Readable side is not in a state that permits enqueue");try{t$(n,t)}catch(e){throw rf(r,e),r._readable._storedError}!tG(n)!==r._backpressure&&rd(r,!0)}function rb(e,t){return y(e._transformAlgorithm(t),void 0,t=>{throw rl(e._controlledTransformStream,t),t})}function rg(e){return TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function r_(e){void 0!==e._finishPromise_resolve&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function rw(e,t){void 0!==e._finishPromise_reject&&(b(e._finishPromise),e._finishPromise_reject(t),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function rS(e){return TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}Object.defineProperties(rp.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),u(rp.prototype.enqueue,"enqueue"),u(rp.prototype.error,"error"),u(rp.prototype.terminate,"terminate"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(rp.prototype,Symbol.toStringTag,{value:"TransformStreamDefaultController",configurable:!0})}}]);