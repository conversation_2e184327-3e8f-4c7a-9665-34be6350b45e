(()=>{var e={};e.id=670,e.ids=[670],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},54990:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>g,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>i}),t(86891),t(16953),t(35866);var r=t(23191),o=t(88716),a=t(37922),l=t.n(a),n=t(95231),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let i=["",{children:["debug",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,86891)),"D:\\web-cloudbase-project\\src\\app\\debug\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],c=["D:\\web-cloudbase-project\\src\\app\\debug\\page.tsx"],u="/debug/page",g={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/debug/page",pathname:"/debug",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},86925:(e,s,t)=>{Promise.resolve().then(t.bind(t,20148))},20148:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(10326),o=t(17577),a=t(41828);let l=()=>{let[e,s]=(0,o.useState)(null),[t,l]=(0,o.useState)(!1),[n,d]=(0,o.useState)(null),i=async()=>{l(!0),d(null),s(null);try{console.log("开始调用 getPosts...");let e=await a.petAPI.getPosts({page:1,limit:10});console.log("getPosts 响应:",e),s(e)}catch(e){console.error("getPosts 错误:",e),d(e.message||"调用失败")}finally{l(!1)}},c=async()=>{try{d(""),s(null);let e="yichongyuzhou-3g9112qwf5f3487b";if(console.log("环境ID:",e),!e)throw Error("环境ID未配置");throw Error("非浏览器环境")}catch(e){console.error("CloudBase 初始化检查失败:",e),d(e.message)}};return r.jsx("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"调试页面"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"CloudBase 初始化测试"}),r.jsx("button",{onClick:c,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded",children:"检查 CloudBase 初始化"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"宠物列表 API 测试"}),r.jsx("button",{onClick:i,disabled:t,className:"bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-4 py-2 rounded",children:t?"调用中...":"测试 getPosts"})]}),n&&(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[r.jsx("h3",{className:"text-red-800 font-semibold mb-2",children:"错误信息:"}),r.jsx("pre",{className:"text-red-700 text-sm",children:n})]}),e&&(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[r.jsx("h3",{className:"text-green-800 font-semibold mb-2",children:"调用结果:"}),r.jsx("pre",{className:"text-green-700 text-sm overflow-auto max-h-96",children:JSON.stringify(e,null,2)})]})]})]})})}},86891:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\debug\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[276,201,240],()=>t(54990));module.exports=r})();