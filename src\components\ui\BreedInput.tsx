'use client';

import React, { useState, useEffect, useRef } from 'react';

interface BreedInputProps {
  value: string;
  onChange: (value: string) => void;
  category: string; // 宠物分类，用于提供相关建议
  error?: string;
  placeholder?: string;
}

// 常见品种数据库（按分类）
const BREED_SUGGESTIONS: Record<string, string[]> = {
  '狗狗': [
    '拉布拉多犬', '金毛寻回犬', '泰迪犬', '比熊犬', '博美犬', '柯基犬', 
    '哈士奇', '萨摩耶', '边境牧羊犬', '德国牧羊犬', '阿拉斯加犬', '松狮犬',
    '吉娃娃', '雪纳瑞', '法国斗牛犬', '英国斗牛犬', '巴哥犬', '西施犬',
    '马尔济斯犬', '约克夏梗', '贵宾犬', '可卡犬', '杜宾犬', '罗威纳犬',
    '大丹犬', '圣伯纳犬', '纽芬兰犬', '伯恩山犬', '秋田犬', '柴犬'
  ],
  '猫咪': [
    '英国短毛猫', '美国短毛猫', '苏格兰折耳猫', '波斯猫', '暹罗猫', '布偶猫',
    '缅因猫', '挪威森林猫', '俄罗斯蓝猫', '阿比西尼亚猫', '孟加拉猫', '土耳其安哥拉猫',
    '加菲猫', '金吉拉猫', '银渐层', '蓝猫', '橘猫', '三花猫',
    '狸花猫', '奶牛猫', '玳瑁猫', '重点色短毛猫', '东方短毛猫', '德文卷毛猫'
  ],
  '鸟类': [
    '虎皮鹦鹉', '玄凤鹦鹉', '牡丹鹦鹉', '太平洋鹦鹉', '金丝雀', '文鸟',
    '八哥', '画眉', '百灵', '相思鸟', '十姐妹', '珍珠鸟'
  ],
  '小宠': [
    '仓鼠', '龙猫', '兔子', '荷兰猪', '刺猬', '蜜袋鼯',
    '垂耳兔', '侏儒兔', '安哥拉兔', '熊猫兔', '金丝熊', '布丁仓鼠'
  ],
  '水族': [
    '金鱼', '锦鲤', '热带鱼', '孔雀鱼', '斗鱼', '神仙鱼',
    '龙鱼', '鹦鹉鱼', '地图鱼', '银龙鱼', '罗汉鱼', '七彩神仙'
  ],
  '爬宠': [
    '巴西龟', '草龟', '鳄龟', '陆龟', '蜥蜴', '变色龙',
    '守宫', '蛇类', '鬃狮蜥', '豹纹守宫', '绿鬣蜥', '蓝舌蜥'
  ]
};

const BreedInput: React.FC<BreedInputProps> = ({
  value,
  onChange,
  category,
  error,
  placeholder = '请输入宠物品种，如：拉布拉多犬、英短蓝猫等'
}) => {
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // 获取建议列表
  const getSuggestions = (input: string, petCategory: string): string[] => {
    if (!input.trim()) {
      // 如果没有输入，显示该分类的热门品种
      return BREED_SUGGESTIONS[petCategory]?.slice(0, 8) || [];
    }

    const allBreeds = BREED_SUGGESTIONS[petCategory] || [];
    const filtered = allBreeds.filter(breed => 
      breed.toLowerCase().includes(input.toLowerCase()) ||
      breed.includes(input)
    );

    // 如果当前分类没有匹配的，搜索所有分类
    if (filtered.length === 0) {
      const allBreedsFromAllCategories = Object.values(BREED_SUGGESTIONS).flat();
      return allBreedsFromAllCategories.filter(breed => 
        breed.toLowerCase().includes(input.toLowerCase()) ||
        breed.includes(input)
      ).slice(0, 8);
    }

    return filtered.slice(0, 8);
  };

  // 更新建议列表
  useEffect(() => {
    const newSuggestions = getSuggestions(value, category);
    setSuggestions(newSuggestions);
    setHighlightedIndex(-1);
  }, [value, category]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    setShowSuggestions(true);
  };

  // 处理建议选择
  const handleSuggestionClick = (suggestion: string) => {
    onChange(suggestion);
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0) {
          handleSuggestionClick(suggestions[highlightedIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setHighlightedIndex(-1);
        break;
    }
  };

  // 处理焦点事件
  const handleFocus = () => {
    setShowSuggestions(true);
  };

  const handleBlur = (e: React.FocusEvent) => {
    // 延迟隐藏建议，允许点击建议项
    setTimeout(() => {
      if (!suggestionsRef.current?.contains(e.relatedTarget as Node)) {
        setShowSuggestions(false);
      }
    }, 150);
  };

  return (
    <div className="relative">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        宠物品种 <span className="text-gray-400">(可选)</span>
      </label>
      
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
            error ? 'border-red-500' : 'border-gray-300'
          }`}
        />
        
        {/* 搜索图标 */}
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>

      {/* 建议列表 */}
      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto"
        >
          {suggestions.map((suggestion, index) => (
            <div
              key={suggestion}
              onClick={() => handleSuggestionClick(suggestion)}
              className={`px-4 py-2 cursor-pointer transition-colors ${
                index === highlightedIndex
                  ? 'bg-blue-50 text-blue-700'
                  : 'hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-between">
                <span>{suggestion}</span>
                {BREED_SUGGESTIONS[category]?.includes(suggestion) && (
                  <span className="text-xs text-blue-500 bg-blue-100 px-2 py-1 rounded">
                    热门
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}

      {/* 提示文本 */}
      <p className="mt-1 text-xs text-gray-500">
        💡 输入具体品种有助于其他用户更快找到您的宝贝
      </p>
    </div>
  );
};

export default BreedInput;
