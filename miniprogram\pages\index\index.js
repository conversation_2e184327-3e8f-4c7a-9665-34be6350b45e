// 首页逻辑
Page({
  data: {
    userInfo: {},
    hasUserInfo: false,
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    posts: []
  },

  onLoad: function () {
    // 获取用户信息
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        hasUserInfo: true
      })
    } else if (this.data.canIUse) {
      // 由于 getUserInfo 是网络请求，可能会在 Page.onLoad 之后才返回
      // 所以此处加入 callback 以防止这种情况
      app.userInfoReadyCallback = res => {
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    } else {
      // 在没有 open-type=getUserInfo 版本的兼容处理
      wx.getUserInfo({
        success: res => {
          app.globalData.userInfo = res.userInfo
          this.setData({
            userInfo: res.userInfo,
            hasUserInfo: true
          })
        }
      })
    }

    // 加载宠物帖子
    this.loadPosts()
  },

  getUserInfo: function(e) {
    console.log(e)
    app.globalData.userInfo = e.detail.userInfo
    this.setData({
      userInfo: e.detail.userInfo,
      hasUserInfo: true
    })
  },

  // 加载宠物帖子
  loadPosts: function() {
    wx.showLoading({
      title: '加载中...',
    })

    // 调用云函数获取帖子
    wx.cloud.callFunction({
      name: 'pet-api',
      data: {
        action: 'getPosts',
        limit: 10
      },
      success: res => {
        console.log('获取帖子成功', res)
        this.setData({
          posts: res.result.data || []
        })
      },
      fail: err => {
        console.error('获取帖子失败', err)
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        })
      },
      complete: () => {
        wx.hideLoading()
      }
    })
  },

  // 查看帖子详情
  viewPost: function(e) {
    const postId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/post-detail/post-detail?id=${postId}`
    })
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadPosts()
    wx.stopPullDownRefresh()
  }
})
