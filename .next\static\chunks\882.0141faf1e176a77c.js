"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[882],{33882:function(t,e,s){s.d(e,{U_:function(){return r},dataCache:function(){return c}});class i{set(t,e,s){let i=Date.now(),a=s||this.config.ttl;this.cache.size>=this.config.maxSize&&this.evict(),this.cache.set(t,{data:e,timestamp:i,ttl:a,accessCount:0,lastAccess:i})}get(t){let e=this.cache.get(t);if(!e)return null;let s=Date.now();return s-e.timestamp>e.ttl?(this.cache.delete(t),null):(e.accessCount++,e.lastAccess=s,e.data)}delete(t){return this.cache.delete(t)}clear(){this.cache.clear()}evict(){let t;if(0!==this.cache.size){switch(this.config.strategy){case"LRU":t=this.findLRUKey();break;case"FIFO":t=this.findFIFOKey();break;case"TTL":t=this.findExpiredKey();break;default:t=this.cache.keys().next().value}t&&this.cache.delete(t)}}findLRUKey(){let t;let e=Date.now();return this.cache.forEach((s,i)=>{s.lastAccess<e&&(e=s.lastAccess,t=i)}),t}findFIFOKey(){let t;let e=Date.now();return this.cache.forEach((s,i)=>{s.timestamp<e&&(e=s.timestamp,t=i)}),t}findExpiredKey(){let t;let e=Date.now();return(this.cache.forEach((s,i)=>{if(e-s.timestamp>s.ttl){t=i;return}}),t)?t:this.findLRUKey()}getStats(){let t=Date.now(),e=0,s=0;return this.cache.forEach(i=>{t-i.timestamp>i.ttl&&e++,s+=JSON.stringify(i.data).length}),{size:this.cache.size,maxSize:this.config.maxSize,expired:e,totalSize:s,hitRate:this.calculateHitRate()}}calculateHitRate(){let t=0;return this.cache.forEach(e=>{t+=e.accessCount}),t>0?this.cache.size/t*100:0}constructor(t={}){this.cache=new Map,this.config={ttl:3e5,maxSize:100,strategy:"LRU",...t}}}class a{set(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:864e5;if(!this.storage)return;let i={data:e,timestamp:Date.now(),ttl:s};try{this.storage.setItem(this.prefix+t,JSON.stringify(i))}catch(e){console.warn("浏览器存储空间不足，清理过期缓存"),this.cleanup();try{this.storage.setItem(this.prefix+t,JSON.stringify(i))}catch(t){console.error("缓存设置失败:",t)}}}get(t){if(!this.storage)return null;try{let e=this.storage.getItem(this.prefix+t);if(!e)return null;let s=JSON.parse(e);if(Date.now()-s.timestamp>s.ttl)return this.storage.removeItem(this.prefix+t),null;return s.data}catch(t){return console.error("缓存读取失败:",t),null}}delete(t){this.storage&&this.storage.removeItem(this.prefix+t)}cleanup(){if(!this.storage)return;let t=Date.now(),e=[];for(let s=0;s<this.storage.length;s++){let i=this.storage.key(s);if(i&&i.startsWith(this.prefix))try{let s=this.storage.getItem(i);if(s){let a=JSON.parse(s);t-a.timestamp>a.ttl&&e.push(i)}}catch(t){e.push(i)}}e.forEach(t=>this.storage.removeItem(t))}clear(){if(!this.storage)return;let t=[];for(let e=0;e<this.storage.length;e++){let s=this.storage.key(e);s&&s.startsWith(this.prefix)&&t.push(s)}t.forEach(t=>this.storage.removeItem(t))}constructor(t="pet_cache_",e=!1){this.prefix=t,this.storage=e?sessionStorage:localStorage}}let r=new i({ttl:18e5,maxSize:50,strategy:"LRU"}),c=new i({ttl:3e5,maxSize:100,strategy:"LRU"});new a("pet_app_")}}]);