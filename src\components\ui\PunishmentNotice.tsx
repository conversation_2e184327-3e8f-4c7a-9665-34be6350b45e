import React from 'react';
import { AlertTriangle, Shield, MessageSquare } from 'lucide-react';
import { UserPermissions } from '@/types';
import AppealButton from './AppealButton';

interface PunishmentNoticeProps {
  permissions: UserPermissions;
  className?: string;
}

const PunishmentNotice: React.FC<PunishmentNoticeProps> = ({
  permissions,
  className = ''
}) => {
  const isPunished = !permissions.canPublishPost || !permissions.canContact;

  if (!isPunished) {
    return null;
  }

  const restrictedActions = [];
  if (!permissions.canPublishPost) {
    restrictedActions.push('发布宝贝');
  }
  if (!permissions.canContact) {
    restrictedActions.push('联系功能');
  }

  return (
    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start space-x-3">
        <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-red-800 mb-1">
            账户受限
          </h3>
          <div className="text-sm text-red-700 space-y-2">
            <p>
              您的以下功能已被限制：
              <span className="font-medium"> {restrictedActions.join('、')}</span>
            </p>
            {permissions.banReason && (
              <p>
                <span className="font-medium">限制原因：</span>
                {permissions.banReason}
              </p>
            )}
            <p className="text-xs text-red-600">
              如果您认为此处罚有误，可以提交申诉。
            </p>
          </div>
        </div>
        <div className="flex-shrink-0">
          <AppealButton />
        </div>
      </div>
    </div>
  );
};

export default PunishmentNotice;
