'use client';

import { useState, useEffect } from 'react';
import { MapPin, Search, X, Navigation, ChevronRight } from 'lucide-react';
import { City, Region } from '@/data/cities';
import { 
  chinaRegions, 
  getAllProvinces, 
  getCitiesByProvince, 
  getDistrictsByCity, 
  searchRegions,
  Province,
  City as ChinaCity,
  District
} from '@/data/china-regions';
import AmapLocation, { LocationInfo } from './AmapLocation';

interface EnhancedCitySelectorProps {
  selectedCity: City | Region | null;
  onCityChange: (city: City | Region) => void;
  className?: string;
}

const EnhancedCitySelector: React.FC<EnhancedCitySelectorProps> = ({
  selectedCity,
  onCityChange,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState<'hot' | 'regions' | 'search'>('hot');
  
  // 三级联动状态
  const [selectedProvince, setSelectedProvince] = useState<Province | null>(null);
  const [selectedCityInProvince, setSelectedCityInProvince] = useState<ChinaCity | null>(null);
  const [selectedDistrict, setSelectedDistrict] = useState<District | null>(null);

  // 热门城市（保持原有的快速选择）
  const hotCities: City[] = [
    { name: '北京市', code: '110000', lat: 39.9042, lng: 116.4074, isHot: true },
    { name: '上海市', code: '310000', lat: 31.2304, lng: 121.4737, isHot: true },
    { name: '广州市', code: '440100', lat: 23.1291, lng: 113.2644, isHot: true },
    { name: '深圳市', code: '440300', lat: 22.5431, lng: 114.0579, isHot: true },
    { name: '杭州市', code: '330100', lat: 30.2741, lng: 120.1551, isHot: true },
    { name: '南京市', code: '320100', lat: 32.0603, lng: 118.7969, isHot: true },
    { name: '苏州市', code: '320500', lat: 31.2989, lng: 120.5853, isHot: true },
    { name: '成都市', code: '510100', lat: 30.5728, lng: 104.0668, isHot: true },
    { name: '武汉市', code: '420100', lat: 30.5928, lng: 114.3055, isHot: true },
    { name: '西安市', code: '610100', lat: 34.3416, lng: 108.9398, isHot: true },
    { name: '天津市', code: '120000', lat: 39.3434, lng: 117.3616, isHot: true },
    { name: '重庆市', code: '500000', lat: 29.5647, lng: 106.5507, isHot: true },
  ];

  // 搜索功能
  useEffect(() => {
    if (searchKeyword.trim()) {
      const results = searchRegions(searchKeyword);
      setSearchResults(results);
      setActiveTab('search');
    } else {
      setSearchResults([]);
      if (activeTab === 'search') {
        setActiveTab('hot');
      }
    }
  }, [searchKeyword]);

  // 选择城市/地区
  const handleRegionSelect = (region: City | Region) => {
    onCityChange(region);
    setIsOpen(false);
    setSearchKeyword('');
    resetSelection();
  };

  // 重置选择状态
  const resetSelection = () => {
    setSelectedProvince(null);
    setSelectedCityInProvince(null);
    setSelectedDistrict(null);
  };

  // 选择省份
  const handleProvinceSelect = (province: Province) => {
    setSelectedProvince(province);
    setSelectedCityInProvince(null);
    setSelectedDistrict(null);
  };

  // 选择城市
  const handleCitySelect = (city: ChinaCity) => {
    setSelectedCityInProvince(city);
    setSelectedDistrict(null);
  };

  // 选择城市（最终选择）
  const handleFinalCitySelect = (city: ChinaCity) => {
    // 构建完整的地区信息
    const fullRegion: Region = {
      name: city.name,
      code: city.code,
      lat: city.lat,
      lng: city.lng,
      fullName: `${selectedProvince?.name} ${city.name}`,
      level: 'city'
    };

    handleRegionSelect(fullRegion);
  };

  // 定位成功回调
  const handleLocationSuccess = (location: LocationInfo) => {
    const region: Region = {
      name: location.city.name,
      code: location.city.code,
      lat: location.lat,
      lng: location.lng,
      fullName: location.city.name,
      level: 'city'
    };
    handleRegionSelect(region);
  };

  // 定位失败回调
  const handleLocationError = (error: string) => {
    console.error('定位失败:', error);
  };

  return (
    <div className={`relative ${className}`}>
      {/* 城市选择按钮 */}
      <button
        onClick={() => setIsOpen(true)}
        className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors"
      >
        <MapPin className="h-4 w-4" />
        <span>
          {selectedCity && 'fullName' in selectedCity ? selectedCity.fullName : selectedCity?.name || '选择城市'}
        </span>
        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* 城市选择弹窗 */}
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            {/* 背景遮罩 */}
            <div 
              className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
              onClick={() => setIsOpen(false)}
            />

            {/* 弹窗内容 */}
            <div className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl">
              {/* 头部 */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">选择城市</h3>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              {/* 定位按钮 */}
              <div className="p-4 border-b border-gray-200">
                <AmapLocation
                  onLocationSuccess={handleLocationSuccess}
                  onLocationError={handleLocationError}
                >
                  {({ getCurrentLocation, loading, error }) => (
                    <button
                      onClick={getCurrentLocation}
                      disabled={loading}
                      className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors disabled:opacity-50"
                    >
                      <Navigation className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                      <span>{loading ? '定位中...' : '自动定位'}</span>
                    </button>
                  )}
                </AmapLocation>
              </div>

              {/* 搜索框 */}
              <div className="p-4 border-b border-gray-200">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="搜索省市"
                    value={searchKeyword}
                    onChange={(e) => setSearchKeyword(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* 标签页 */}
              <div className="flex border-b border-gray-200">
                <button
                  onClick={() => setActiveTab('hot')}
                  className={`flex-1 px-4 py-2 text-sm font-medium ${
                    activeTab === 'hot' 
                      ? 'text-blue-600 border-b-2 border-blue-600' 
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  热门城市
                </button>
                <button
                  onClick={() => setActiveTab('regions')}
                  className={`flex-1 px-4 py-2 text-sm font-medium ${
                    activeTab === 'regions'
                      ? 'text-blue-600 border-b-2 border-blue-600'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  省市选择
                </button>
              </div>

              {/* 内容区域 */}
              <div className="max-h-96 overflow-y-auto">
                {/* 搜索结果 */}
                {activeTab === 'search' && (
                  <div className="p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-3">搜索结果</h4>
                    {searchResults.length > 0 ? (
                      <div className="space-y-2">
                        {searchResults.map((result, index) => (
                          <button
                            key={index}
                            onClick={() => {
                              const region: Region = {
                                name: result.city?.name || result.province.name,
                                code: result.city?.code || result.province.code,
                                lat: result.city?.lat || result.province.lat,
                                lng: result.city?.lng || result.province.lng,
                                fullName: result.fullName,
                                level: result.city ? 'city' : 'province'
                              };
                              handleRegionSelect(region);
                            }}
                            className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                          >
                            {result.fullName}
                          </button>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500 text-center py-4">未找到相关地区</p>
                    )}
                  </div>
                )}

                {/* 热门城市 */}
                {activeTab === 'hot' && (
                  <div className="p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-3">热门城市</h4>
                    <div className="grid grid-cols-3 gap-2">
                      {hotCities.map((city) => (
                        <button
                          key={city.code}
                          onClick={() => handleRegionSelect(city)}
                          className="px-3 py-2 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors text-center"
                        >
                          {city.name}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* 省市两级联动 */}
                {activeTab === 'regions' && (
                  <div className="p-4">
                    <div className="grid grid-cols-2 gap-4 h-80">
                      {/* 省份列表 */}
                      <div className="border-r border-gray-200 pr-4">
                        <h4 className="text-sm font-medium text-gray-900 mb-3">省份</h4>
                        <div className="space-y-1 max-h-64 overflow-y-auto">
                          {getAllProvinces().map((province) => (
                            <button
                              key={province.code}
                              onClick={() => handleProvinceSelect(province)}
                              className={`w-full text-left px-2 py-1 text-sm rounded transition-colors ${
                                selectedProvince?.code === province.code
                                  ? 'bg-blue-100 text-blue-700'
                                  : 'text-gray-700 hover:bg-gray-100'
                              }`}
                            >
                              <div className="flex items-center justify-between">
                                <span>{province.name}</span>
                                <ChevronRight className="h-3 w-3" />
                              </div>
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* 城市列表 */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-3">城市</h4>
                        {selectedProvince ? (
                          <div className="space-y-1 max-h-64 overflow-y-auto">
                            {getCitiesByProvince(selectedProvince.code).map((city) => (
                              <button
                                key={city.code}
                                onClick={() => handleFinalCitySelect(city)}
                                className="w-full text-left px-2 py-1 text-sm text-gray-700 hover:bg-blue-100 hover:text-blue-700 rounded transition-colors"
                              >
                                {city.name}
                              </button>
                            ))}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-500">请先选择省份</p>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedCitySelector;
