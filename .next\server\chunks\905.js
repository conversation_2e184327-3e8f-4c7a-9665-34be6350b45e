"use strict";exports.id=905,exports.ids=[905],exports.modules={86333:(e,t,s)=>{s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},48998:(e,t,s)=>{s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},91216:(e,t,s)=>{s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},12714:(e,t,s)=>{s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9015:(e,t,s)=>{s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},39730:(e,t,s)=>{s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},33734:(e,t,s)=>{s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},8040:(e,t,s)=>{s.d(t,{Z:()=>h});var r=s(10326),a=s(17577),l=s(79635),i=s(9015),n=s(77636),o=s(39730),c=s(94019),d=s(91216),m=s(12714),x=s(99837),u=s(20603);function h({isOpen:e,onClose:t,currentUser:s,onUpdate:h,forceContactTab:y=!1}){let[g,p]=(0,a.useState)("nickname"),[f,b]=(0,a.useState)(""),[j,v]=(0,a.useState)(""),[w,N]=(0,a.useState)(""),[k,C]=(0,a.useState)(""),[Z,S]=(0,a.useState)(!1),[I,H]=(0,a.useState)(!1),[D,M]=(0,a.useState)(!1),[z,$]=(0,a.useState)({type:"wechat",value:""}),[K,_]=(0,a.useState)(""),[A,O]=(0,a.useState)(""),[J,R]=(0,a.useState)(!1),[P,T]=(0,a.useState)(""),[B,V]=(0,a.useState)(!1),[E]=(0,a.useState)(new Date(Date.now()-216e7)),L=(e,t)=>{if(!t.trim())return"联系方式不能为空";if("wechat"===e){if(!/^[a-zA-Z][a-zA-Z0-9_-]{5,19}$/.test(t))return"微信号格式：6-20位，字母开头，可包含字母、数字、下划线、减号"}else if("phone"===e&&!/^1[3-9]\d{9}$/.test(t))return"请输入正确的11位手机号";return""},q=()=>!E||Math.floor((Date.now()-E.getTime())/864e5)>=30,F=()=>E?Math.max(0,30-Math.floor((Date.now()-E.getTime())/864e5)):0,X=async e=>Math.floor(3*Math.random()),G=async()=>{if(!f.trim()){u.C.error("昵称不能为空");return}if(!q()){u.C.error(`昵称30天只能修改一次，还需等待${F()}天`);return}V(!0);try{let e=await X(f);0===e?T(f):T(`${f}#${e+1}`),R(!0)}catch(e){u.C.error("检查昵称失败")}finally{V(!1)}},Q=async()=>{if(!f.trim()){u.C.error("昵称不能为空");return}if(!q()){u.C.error(`昵称30天只能修改一次，还需等待${F()}天`);return}V(!0);try{await h({nickname:f}),u.C.success("昵称更新成功")}catch(e){u.C.error("昵称更新失败")}finally{V(!1)}},U=async()=>{if(!j){u.C.error("请输入旧密码");return}if(!w){u.C.error("请输入新密码");return}if(w!==k){u.C.error("两次输入的密码不一致");return}if(w.length<6){u.C.error("密码长度至少6位");return}V(!0);try{await h({oldPassword:j,password:w}),u.C.success("密码修改成功"),v(""),N(""),C("")}catch(e){u.C.error("密码修改失败")}finally{V(!1)}},W=async()=>{V(!0);try{localStorage.setItem(`address_${s._id}`,K),await h({address:K}),u.C.success("地址更新成功")}catch(e){u.C.error("地址更新失败")}finally{V(!1)}},Y=async()=>{let e=L(z.type,z.value);if(e){O(e),u.C.error(e);return}V(!0);try{localStorage.setItem(`contact_${s._id}`,JSON.stringify(z)),await h({contactInfo:z}),O(""),u.C.success("联系方式更新成功"),y&&t()}catch(e){u.C.error("联系方式更新失败")}finally{V(!1)}};if(!e)return null;let ee=[{id:"nickname",label:"昵称设置",icon:l.Z},{id:"password",label:"密码修改",icon:i.Z},{id:"address",label:"地址信息",icon:n.Z},{id:"contact",label:"联系方式",icon:o.Z}];return(0,r.jsxs)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",onClick:e=>{e.target===e.currentTarget&&t()},children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg w-[480px] h-[500px] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b flex-shrink-0",children:[r.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"个人设置"}),r.jsx("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:r.jsx(c.Z,{className:"w-6 h-6"})})]}),r.jsx("div",{className:"border-b border-gray-200 flex-shrink-0",children:r.jsx("div",{className:"flex",children:ee.map(e=>{let t=e.icon;return(0,r.jsxs)("button",{onClick:()=>p(e.id),className:`flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors ${g===e.id?"text-blue-600 border-b-2 border-blue-600 bg-blue-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,children:[r.jsx(t,{className:"w-4 h-4"}),r.jsx("span",{className:"hidden sm:inline",children:e.label})]},e.id)})})}),r.jsx("div",{className:"flex-1 overflow-y-auto p-6",children:function(){switch(g){case"nickname":return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"昵称"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("input",{type:"text",value:f,onChange:e=>b(e.target.value),placeholder:s?.loginType==="wechat"?"微信用户":"邮箱用户",className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:!q()}),r.jsx(x.Z,{onClick:G,loading:B,size:"sm",disabled:!q(),children:"检查"})]}),!q()&&(0,r.jsxs)("p",{className:"text-sm text-orange-600 mt-1",children:["昵称30天只能修改一次，还需等待",F(),"天"]})]}),r.jsx("div",{className:"flex justify-end",children:r.jsx(x.Z,{onClick:Q,loading:B,disabled:!q()||!f.trim(),children:"保存昵称"})})]});case"password":return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"旧密码"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("input",{type:Z?"text":"password",value:j,onChange:e=>v(e.target.value),placeholder:"请输入当前密码",className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),r.jsx("button",{type:"button",onClick:()=>S(!Z),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:Z?r.jsx(d.Z,{className:"w-4 h-4"}):r.jsx(m.Z,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"新密码"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("input",{type:I?"text":"password",value:w,onChange:e=>N(e.target.value),placeholder:"至少6位字符",className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),r.jsx("button",{type:"button",onClick:()=>H(!I),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:I?r.jsx(d.Z,{className:"w-4 h-4"}):r.jsx(m.Z,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"确认密码"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("input",{type:D?"text":"password",value:k,onChange:e=>C(e.target.value),placeholder:"再次输入新密码",className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),r.jsx("button",{type:"button",onClick:()=>M(!D),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:D?r.jsx(d.Z,{className:"w-4 h-4"}):r.jsx(m.Z,{className:"w-4 h-4"})})]})]}),r.jsx("div",{className:"flex justify-end",children:r.jsx(x.Z,{onClick:U,loading:B,disabled:!j||!w||!k,children:"修改密码"})})]});case"address":return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"常用地址"}),r.jsx("input",{type:"text",value:K,onChange:e=>_(e.target.value),placeholder:"请输入您的常用地址",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["• 建议填写到县/区一级，如：北京市朝阳区、上海市浦东新区",r.jsx("br",{}),"• 此地址将作为发布宠物时的默认位置，提升交易效率"]})]}),r.jsx("div",{className:"flex justify-end",children:r.jsx(x.Z,{onClick:W,loading:B,children:"保存地址"})})]});case"contact":return(0,r.jsxs)("div",{className:"space-y-4",children:[y&&r.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4",children:r.jsx("p",{className:"text-sm text-blue-800",children:"\uD83D\uDCA1 检测到您还没有设置联系方式，请先完善联系信息才能使用联系功能"})}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"联系方式类型"}),r.jsx("div",{className:"flex space-x-3 mb-3",children:(0,r.jsxs)("select",{value:z.type,onChange:e=>{let t=e.target.value;$({...z,type:t}),O("")},className:"min-w-[120px] px-3 py-2 pr-8 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none bg-white",style:{backgroundImage:"url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e\")",backgroundPosition:"right 0.5rem center",backgroundRepeat:"no-repeat",backgroundSize:"1.5em 1.5em"},children:[r.jsx("option",{value:"wechat",children:"微信号"}),r.jsx("option",{value:"phone",children:"手机号"})]})})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"wechat"===z.type?"微信号":"手机号"}),r.jsx("input",{type:"text",value:z.value,onChange:e=>{$({...z,value:e.target.value}),O("")},placeholder:"wechat"===z.type?"请输入微信号":"请输入手机号",className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${A?"border-red-300":"border-gray-300"}`}),A&&r.jsx("p",{className:"text-sm text-red-600 mt-1",children:A}),r.jsx("div",{className:"text-xs text-gray-500 mt-2",children:"wechat"===z.type?(0,r.jsxs)("div",{children:[r.jsx("p",{children:"• 微信号格式：6-20位字符"}),r.jsx("p",{children:"• 必须以字母开头"}),r.jsx("p",{children:"• 可包含字母、数字、下划线、减号"}),r.jsx("p",{children:"• 示例：wechat123、user_name、my-id"})]}):(0,r.jsxs)("div",{children:[r.jsx("p",{children:"• 请输入11位中国大陆手机号"}),r.jsx("p",{children:"• 示例：13812345678"})]})})]}),r.jsx("div",{className:"flex justify-end",children:r.jsx(x.Z,{onClick:Y,loading:B,disabled:!z.value.trim(),children:"保存联系方式"})})]});default:return null}}()})]}),J&&r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-sm w-full mx-4",children:[r.jsx("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"确认昵称"}),(0,r.jsxs)("p",{className:"text-gray-600 mb-6",children:["您的昵称将显示为：",r.jsx("span",{className:"font-medium text-gray-900",children:P})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[r.jsx(x.Z,{variant:"outline",onClick:()=>R(!1),className:"flex-1",children:"再想想"}),r.jsx(x.Z,{onClick:()=>{b(P),R(!1)},className:"flex-1",children:"确认"})]})]})})]})}},13570:(e,t,s)=>{s.d(t,{Fd:()=>l,KX:()=>i});class r{constructor(e,t=10){this.storageKey=e,this.maxItems=t}getHistory(){try{let e=localStorage.getItem(this.storageKey);if(e)return JSON.parse(e).sort((e,t)=>t.timestamp-e.timestamp)}catch(e){console.error("获取历史记录失败:",e)}return[]}addItem(e,t){if(!e.trim())return;let s={value:e.trim(),label:t||e.trim(),timestamp:Date.now()},r=this.getHistory();(r=r.filter(e=>e.value!==s.value)).unshift(s),r.length>this.maxItems&&(r=r.slice(0,this.maxItems)),this.saveHistory(r)}removeItem(e){let t=this.getHistory().filter(t=>t.value!==e);this.saveHistory(t)}clearHistory(){localStorage.removeItem(this.storageKey)}getHistoryValues(){return this.getHistory().map(e=>e.value)}hasItem(e){return this.getHistoryValues().includes(e)}saveHistory(e){try{localStorage.setItem(this.storageKey,JSON.stringify(e))}catch(e){console.error("保存历史记录失败:",e)}}}class a{getHistory(){try{let e=localStorage.getItem(this.storageKey);if(e){let t=JSON.parse(e),s=Date.now()-864e5*this.maxDays,r=t.filter(e=>e.timestamp>s);return r.length!==t.length&&this.saveHistory(r),r.sort((e,t)=>t.timestamp-e.timestamp)}}catch(e){console.error("获取浏览历史记录失败:",e)}return[]}addBrowseRecord(e,t,s,r,a){if(!e||!t)return;let l={postId:e,title:t.trim(),author:s.trim(),authorId:r,image:a,timestamp:Date.now()},i=this.getHistory();(i=i.filter(e=>e.postId!==l.postId)).unshift(l),i.length>this.maxItems&&(i=i.slice(0,this.maxItems)),this.saveHistory(i)}removeRecord(e){let t=this.getHistory().filter(t=>t.postId!==e);this.saveHistory(t)}clearHistory(){localStorage.removeItem(this.storageKey)}getRecentPostIds(){return this.getHistory().map(e=>e.postId)}hasBrowsed(e){return this.getRecentPostIds().includes(e)}saveHistory(e){try{localStorage.setItem(this.storageKey,JSON.stringify(e))}catch(e){console.error("保存浏览历史记录失败:",e)}}constructor(){this.storageKey="browse_history",this.maxDays=3,this.maxItems=50}}new r("location_history",10);let l=new r("category_history",8),i=new a}};