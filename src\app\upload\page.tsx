'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { useAuthContext, RequireAuth } from '@/components/auth/AuthProvider';
import { Input, Textarea } from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import ImageUpload from '@/components/upload/ImageUpload';
import LocationTagInput from '@/components/upload/LocationTagInput';
import ContactInput from '@/components/upload/ContactInput';
import PetTypeSelect from '@/components/upload/PetTypeSelect';
import { showToast } from '@/components/ui/Toast';
import { petAPI, uploadFile } from '@/lib/cloudbase';
import { generateCloudPath } from '@/utils';
import { PostFormData } from '@/types';
import { saveDraft, getDraft, updateDraft } from '@/utils/drafts';
import { useSearchParams } from 'next/navigation';
import SimpleBreedInput from '@/components/upload/SimpleBreedInput';
import { BreedClassification } from '@/utils/smartBreedClassifier';
import { optimizeImage } from '@/utils/imageOptimizer';
import { performanceMonitor } from '@/utils/performanceMonitor';
import AuditWaitingModal from '@/components/upload/AuditWaitingModal';

const UploadPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const draftId = searchParams.get('draftId');
  const { user } = useAuthContext();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<PostFormData>({
    title: '', // 不显示输入框，后端使用品种作为标题
    description: '',
    images: [],
    category: '', // 通过智能分类自动设置
    breed: '',
    type: '',
    gender: '',
    location: '',
    contact_info: {
      phone: '',
      wechat: '',
    },
  });

  // 新增：存储草稿的原始 base64 图片数据，用于预览
  const [draftImageData, setDraftImageData] = useState<string[]>([]);

  // 智能分类状态
  const [breedClassification, setBreedClassification] = useState<BreedClassification | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showContactInfo, setShowContactInfo] = useState(false);
  const [showTerms, setShowTerms] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [showAuditWaiting, setShowAuditWaiting] = useState(false);

  // 处理智能品种分类
  const handleBreedChange = (breed: string, classification: BreedClassification | null) => {
    // 优先使用二级分类，如果没有则使用一级分类
    const categoryToUse = classification?.secondaryCategory || classification?.primaryCategory || '';

    setFormData(prev => ({
      ...prev,
      breed,
      category: categoryToUse // 自动设置分类（优先二级分类）
    }));
    setBreedClassification(classification);

    // 清除相关错误
    if (errors.breed) {
      setErrors(prev => ({ ...prev, breed: '' }));
    }
    if (errors.category) {
      setErrors(prev => ({ ...prev, category: '' }));
    }
  };

  // 将base64字符串转换为File对象
  const base64ToFile = (base64String: string, filename: string = 'image.jpg'): File => {
    try {
      // 验证base64字符串格式
      if (!base64String || !base64String.includes(',')) {
        throw new Error('无效的base64字符串格式');
      }

      const arr = base64String.split(',');
      if (arr.length !== 2) {
        throw new Error('base64字符串格式不正确');
      }

      const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg';
      const bstr = atob(arr[1]);

      if (bstr.length === 0) {
        throw new Error('base64解码后数据为空');
      }

      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }

      const file = new File([u8arr], filename, { type: mime });
      console.log(`成功转换base64为File: ${filename}, 大小: ${file.size} bytes`);
      return file;
    } catch (error) {
      console.error('base64转File失败:', error, '原始数据长度:', base64String?.length);
      // 抛出错误而不是返回空File对象
      throw error;
    }
  };

  // 加载草稿数据
  useEffect(() => {
    if (draftId && typeof window !== 'undefined') {
      // 延迟执行，确保客户端完全加载
      const timer = setTimeout(async () => {
        try {
          console.log('正在加载草稿，ID:', draftId);
          const draft = getDraft(draftId);
          console.log('获取到的草稿数据:', draft);

          if (draft) {
            // 处理图片数据 - 保存原始数据用于预览，同时创建File对象用于表单
            const processedImages: File[] = [];
            const originalImageData: string[] = [];

            if (draft.images && Array.isArray(draft.images)) {
              for (let i = 0; i < draft.images.length; i++) {
                const img = draft.images[i] as any;
                if (typeof img === 'string') {
                  if (img.startsWith('data:')) {
                    // base64格式 - 保存原始数据用于预览
                    originalImageData.push(img);
                    // 尝试转换为File对象
                    try {
                      const file = base64ToFile(img, `draft_image_${i}.jpg`);
                      processedImages.push(file);
                    } catch (error) {
                      console.error('base64转File失败:', error);
                      // 创建一个空的标记File对象，实际预览使用base64
                      const placeholderFile = new File([], `draft_image_${i}.jpg`, { type: 'image/jpeg' });
                      processedImages.push(placeholderFile);
                    }
                  } else if (img.startsWith('http')) {
                    // URL格式 - 直接保存URL用于预览
                    originalImageData.push(img);
                    // 创建空的标记File对象
                    const placeholderFile = new File([], `url_image_${i}.jpg`, { type: 'image/jpeg' });
                    processedImages.push(placeholderFile);
                  } else {
                    // 其他格式，使用默认图片
                    originalImageData.push('https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center');
                    const placeholderFile = new File([], `default_image_${i}.jpg`, { type: 'image/jpeg' });
                    processedImages.push(placeholderFile);
                  }
                }
              }
            }

            setFormData({
              title: draft.title || '', // 兼容旧草稿
              description: draft.description || '',
              images: processedImages,
              category: draft.category || '',
              breed: draft.breed || '',
              type: draft.type || 'selling',
              gender: draft.gender || '',
              location: draft.location || '',
              contact_info: draft.contact_info || {},
            });

            // 设置草稿图片数据用于预览 - 使用 setTimeout 确保在下一个事件循环中执行
            console.log('设置草稿图片数据:', originalImageData);
            setTimeout(() => {
              console.log('延迟设置草稿图片数据:', originalImageData);
              setDraftImageData(originalImageData);
            }, 0);

            // 如果有品种信息，设置分类信息但不触发 handleBreedChange 避免覆盖图片数据
            if (draft.breed) {
              // 直接设置分类信息，不调用 handleBreedChange 避免状态竞争
              setBreedClassification(null);
            }

            showToast.success('草稿已加载');
          } else {
            console.warn('未找到草稿，ID:', draftId);
            showToast.error('未找到指定的草稿');
          }
        } catch (error) {
          console.error('加载草稿失败:', error);
          showToast.error('加载草稿失败，请重试');
        }
      }, 100); // 延迟100ms

      return () => clearTimeout(timer);
    }
  }, [draftId]);

  // 从本地存储加载用户的联系方式
  useEffect(() => {
    if (user?._id && !draftId) { // 只有在不是加载草稿时才从本地存储加载联系方式
      const savedContact = localStorage.getItem(`contact_${user._id}`);
      if (savedContact) {
        try {
          const contactInfo = JSON.parse(savedContact);
          // 转换个人设置中的联系方式格式到发布页面格式
          const convertedContact = {
            phone: contactInfo.type === 'phone' ? contactInfo.value : '',
            wechat: contactInfo.type === 'wechat' ? contactInfo.value : '',
          };
          setFormData(prev => ({
            ...prev,
            contact_info: convertedContact,
          }));
        } catch (error) {
          console.error('解析联系方式失败:', error);
        }
      }
    }
  }, [user?._id, draftId]);

  // 更新表单数据
  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // 更新联系方式
  const updateContactInfo = (contactInfo: { phone?: string; wechat?: string }) => {
    setFormData(prev => ({
      ...prev,
      contact_info: contactInfo,
    }));
    // 清除联系方式相关错误
    if (errors.contact) {
      setErrors(prev => ({ ...prev, contact: '' }));
    }
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // 图片验证（第一步）
    if (formData.images.length === 0) {
      newErrors.images = '请至少上传一张图片';
    }

    // 品种验证（第二步）
    if (!formData.breed || !formData.breed.trim()) {
      newErrors.breed = '请输入宠物品种';
    }

    // 注意：分类字段不再是必填项，因为新版本使用智能自动分类系统
    // 用户无法手动选择分类，系统会自动根据品种进行分类
    // 即使分类识别失败，也不应该阻止用户发布

    // 描述验证（第三步）
    if (!formData.description.trim()) {
      newErrors.description = '请输入宝贝描述';
    } else if (formData.description.length > 500) {
      newErrors.description = '描述不能超过500个字符';
    }

    // 配种性别验证
    if (formData.type === 'breeding' && !formData.gender) {
      newErrors.gender = '配种信息需要选择宝贝性别';
    }

    // 位置标签验证
    if (!formData.location || !formData.location.trim()) {
      newErrors.location = '请选择或输入发布地区';
    } else if (!formData.location.startsWith('#')) {
      newErrors.location = '位置格式错误，请重新选择';
    }

    // 联系方式验证（出售、配种、寻回、求购需要联系方式）
    const needsContact = ['breeding', 'selling', 'lost', 'wanted'].includes(formData.type || '');
    const { phone, wechat } = formData.contact_info || {};
    const hasContact = phone || wechat;

    if (needsContact && !hasContact) {
      newErrors.contact = '该类型发布需要填写联系方式';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 优化的图片上传
  const uploadImages = async (images: File[]): Promise<string[]> => {
    const startTime = Date.now();

    const uploadPromises = images.map(async (file, index) => {
      try {
        console.log(`开始优化图片 ${index + 1}/${images.length}: ${file.name}`);

        // 使用新的图片优化工具
        const optimized = await optimizeImage(file, {
          maxWidth: 1200,
          maxHeight: 1200,
          quality: 0.85,
          outputFormat: 'auto'
        });

        console.log(`图片 ${file.name} 优化完成，压缩率: ${optimized.compressionRatio}%`);
        console.log(`格式: ${optimized.outputFormat}, 原始大小: ${file.size}, 压缩后: ${optimized.compressed.size}`);

        // 上传优化后的图片
        const result = await uploadFile(optimized.compressed);
        return result;

      } catch (error) {
        console.error(`图片 ${file.name} 优化失败，使用原图:`, error);
        // 如果优化失败，使用原图
        const result = await uploadFile(file);
        return result;
      }
    });

    const results = await Promise.all(uploadPromises);

    // 记录性能
    const duration = Date.now() - startTime;
    performanceMonitor.recordApiResponse('imageUpload', duration);

    console.log(`所有图片上传完成，总耗时: ${duration}ms`);
    return results;
  };

  // 提交表单
  const handleSubmit = async () => {
    console.log('🚀 开始发布，当前表单数据:', formData);
    console.log('🔍 开始表单验证...');

    if (!validateForm()) {
      console.log('❌ 表单验证失败');
      showToast.error('请检查表单信息');
      return;
    }

    console.log('✅ 表单验证通过，开始发布...');

    try {
      setLoading(true);
      
      // 上传图片
      showToast.loading('正在上传图片...');
      const imageUrls = await uploadImages(formData.images);
      
      // 创建帖子
      showToast.dismiss();
      showToast.loading('正在发布...');
      
      const postData = {
        title: formData.breed?.trim() || '宠物发布', // 简单使用品种作为标题
        description: formData.description.trim(),
        images: imageUrls,
        category: formData.category,
        breed: formData.breed?.trim() || undefined,
        type: formData.type || undefined,
        gender: formData.type === 'breeding' ? formData.gender : undefined,
        location: formData.location?.trim() || undefined,
        contact_info: {
          phone: formData.contact_info?.phone?.trim() || undefined,
          wechat: formData.contact_info?.wechat?.trim() || undefined,
        },
      };

      const result = await petAPI.createPost(postData);

      if (result.success) {
        showToast.dismiss();

        // 显示审核等待页面
        setShowAuditWaiting(true);

        console.log('✅ 帖子创建成功，开始审核流程');
      } else {
        throw new Error(result.message || '发布失败');
      }
    } catch (error: any) {
      console.error('发布失败:', error);
      showToast.dismiss();
      showToast.error(error.message || '发布失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 审核完成处理
  const handleAuditComplete = async () => {
    setShowAuditWaiting(false);

    try {
      // 清理相关缓存，确保新帖子能立即显示
      const { dataCache } = await import('@/utils/cacheManager');
      dataCache.clear();

      // 清理帖子查询缓存
      const cacheKeys = [
        'posts_all',
        `posts_${formData.category}`,
        `posts_${formData.type}`,
        'posts_latest'
      ];
      cacheKeys.forEach(key => dataCache.delete(key));

      console.log('✅ 缓存已清理，新帖子将立即显示');
    } catch (error) {
      console.warn('清理缓存失败:', error);
    }

    showToast.success('发布成功！您的宠物已通过审核并发布到平台');

    // 发布成功后自动跳转到主页查看新帖子
    setTimeout(() => {
      router.push('/');
    }, 1500);
  };

  // 取消发布
  const handleCancel = () => {
    router.back();
  };

  // 保存到待发布
  const handleSaveDraft = async () => {
    if (!formData.breed?.trim()) {
      showToast.error('请至少填写宠物品种');
      return;
    }

    try {
      setLoading(true);
      if (draftId) {
        // 更新现有草稿
        await updateDraft(draftId, formData);
        showToast.success('草稿已更新');
      } else {
        // 创建新草稿
        await saveDraft(formData);
        showToast.success('已保存到待发布');
      }
      router.back(); // 自动返回上一页
    } catch (error) {
      console.error('保存草稿失败:', error);
      showToast.error('保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <RequireAuth>
      <div className="min-h-screen bg-gray-50">
        {/* 头部 */}
        <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center h-16">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.back()}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <ArrowLeft className="h-5 w-5" />
                </button>
                <h1 className="text-lg font-semibold text-gray-900">
                  发布宝贝
                </h1>
              </div>
            </div>
          </div>
        </div>

        {/* 表单内容 */}
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-6">
            {/* 图片上传 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                宝贝图片 <span className="text-red-500">*</span>
              </label>
              <ImageUpload
                images={formData.images}
                onImagesChange={(images) => updateFormData('images', images)}
                draftImages={draftImageData}
              />
              {errors.images && (
                <p className="mt-1 text-sm text-red-600">{errors.images}</p>
              )}
            </div>

            {/* 第二步：智能品种输入 */}
            <SimpleBreedInput
              value={formData.breed || ''}
              onChange={handleBreedChange}
              error={errors.breed}
              placeholder="请输入宠物品种，如：拉布拉多、英短蓝猫、虎皮鹦鹉等"
            />

            {/* 第三步：宝贝描述 */}
            <Textarea
              label="宝贝描述"
              placeholder="请简洁扼要的描述您宝贝的不凡"
              value={formData.description}
              onChange={(e) => updateFormData('description', e.target.value)}
              error={errors.description}
              rows={4}
              maxLength={500}
              required
            />

            {/* 位置标签 */}
            <LocationTagInput
              value={formData.location || ''}
              onChange={(location) => updateFormData('location', location)}
              error={errors.location}
              userId={user?._id}
            />

            {/* 发布类型选择 */}
            <PetTypeSelect
              value={formData.type || ''}
              onChange={(type) => updateFormData('type', type)}
              gender={formData.gender || ''}
              onGenderChange={(gender) => updateFormData('gender', gender)}
              error={errors.gender}
            />

            {/* 联系方式 */}
            <ContactInput
              value={formData.contact_info || {}}
              onChange={updateContactInfo}
              petType={formData.type}
              error={errors.contact}
            />

            {/* 法律提醒 */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <h4 className="font-medium text-red-900 mb-2 flex items-center">
                <span className="text-red-500 mr-2">⚠️</span>
                法律提醒
              </h4>
              <p className="text-sm text-red-800 font-medium">
                请遵守国家法律，禁止出售受保护的野生动物
              </p>
            </div>

            {/* 发布须知 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">发布须知</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• 请确保发布信息真实有效，如实描述物品状况</li>
                <li>• 禁止发布血腥、暴力、违法违规内容</li>
                <li>• 交易时请注意安全，建议当面验货交易</li>
                <li>• 平台仅提供信息展示服务，不参与具体交易过程</li>
                <li>• 用户需自行承担交易风险，请谨慎甄别</li>
              </ul>
            </div>

            {/* 操作按钮 */}
            <div className="pt-4 space-y-3">
              {/* 三个按钮并排 */}
              <div className="grid grid-cols-3 gap-3">
                <Button
                  onClick={handleCancel}
                  variant="outline"
                  disabled={loading}
                  className="w-full"
                >
                  取消发布
                </Button>
                <Button
                  onClick={handleSaveDraft}
                  variant="outline"
                  disabled={loading}
                  className="w-full"
                >
                  保存到待发布
                </Button>
                <Button
                  onClick={handleSubmit}
                  loading={loading}
                  disabled={loading}
                  className="w-full"
                >
                  {loading ? '发布中...' : '发布宝贝'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* 审核等待模态框 */}
        <AuditWaitingModal
          isOpen={showAuditWaiting}
          onComplete={handleAuditComplete}
          postTitle={formData.breed || '您的宠物'}
        />
      </div>
    </RequireAuth>
  );
};

export default UploadPage;
