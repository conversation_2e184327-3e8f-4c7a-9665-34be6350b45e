(()=>{var e={};e.id=667,e.ids=[667],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},428:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>o}),s(34312),s(9457),s(16953),s(35866);var r=s(23191),a=s(88716),l=s(37922),i=s.n(l),n=s(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let o=["",{children:["admin",{children:["posts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,34312)),"D:\\web-cloudbase-project\\src\\app\\admin\\posts\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,9457)),"D:\\web-cloudbase-project\\src\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\web-cloudbase-project\\src\\app\\admin\\posts\\page.tsx"],x="/admin/posts/page",p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/posts/page",pathname:"/admin/posts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},64271:(e,t,s)=>{Promise.resolve().then(s.bind(s,5264))},88057:(e,t,s)=>{Promise.resolve().then(s.bind(s,74879))},5264:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(10326);s(17577);var a=s(20603);function l({children:e}){return(0,r.jsxs)("div",{className:"admin-layout",children:[r.jsx(a.ToastProvider,{}),e]})}s(23824)},74879:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(10326),a=s(17577),l=s(41828);let i=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"}))});var n=s(71684);let c=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"}))});var o=s(39258);function d(){let[e,t]=(0,a.useState)([]),[s,d]=(0,a.useState)(!0),[x,p]=(0,a.useState)([]),[u,m]=(0,a.useState)("all"),[h,g]=(0,a.useState)(!1),[y,j]=(0,a.useState)(""),v=async()=>{d(!0);try{let e=await l.petAPI.getPostsByPriority({priority_level:u,limit:50,include_scores:h});e.success&&t(e.data||[])}catch(e){console.error("加载帖子失败:",e)}finally{d(!1)}},b=async(e,t,s)=>{try{(await l.petAPI.updatePostPriority({post_id:e,priority_level:t,reason:s})).success&&await v()}catch(e){console.error("更新帖子优先级失败:",e),alert("更新失败："+(e?.message||"未知错误"))}},w=async()=>{if(0===x.length||!y)return;let e=prompt("请输入操作原因（可选）：");try{let t=await l.petAPI.batchUpdatePostPriority({post_ids:x,priority_level:y,reason:e||void 0});t.success&&(alert(`批量操作完成：成功 ${t.data.successful} 个，失败 ${t.data.failed} 个`),p([]),j(""),await v())}catch(e){console.error("批量操作失败:",e),alert("批量操作失败："+(e?.message||"未知错误"))}},f=async()=>{if(confirm("确定要批量更新所有帖子的质量评分吗？这可能需要一些时间。"))try{d(!0);let e=await l.petAPI.batchUpdateAllPostsQuality();e.success&&(alert(e.data.message),await v())}catch(e){console.error("批量更新质量评分失败:",e),alert("批量更新失败："+(e?.message||"未知错误"))}finally{d(!1)}},N=e=>{let t={high:{label:"高优先级",color:"bg-red-100 text-red-800",icon:i},normal:{label:"普通",color:"bg-green-100 text-green-800",icon:n.Z},low:{label:"低优先级",color:"bg-yellow-100 text-yellow-800",icon:c},hidden:{label:"已隐藏",color:"bg-gray-100 text-gray-800",icon:o.Z}},s=t[e]||t.normal,a=s.icon;return(0,r.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${s.color}`,children:[r.jsx(a,{className:"h-3 w-3 mr-1"}),s.label]})},k=e=>e>=80?"text-green-600":e>=60?"text-yellow-600":e>=40?"text-orange-600":"text-red-600";return s?r.jsx("div",{className:"flex items-center justify-center h-64",children:r.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"帖子优先级管理"}),r.jsx("p",{className:"text-gray-600",children:"管理帖子展示优先级和内容质量"})]}),r.jsx("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)("label",{className:"flex items-center",children:[r.jsx("input",{type:"checkbox",checked:h,onChange:e=>g(e.target.checked),className:"mr-2"}),"显示详细评分"]})})]}),r.jsx("div",{className:"bg-white p-4 rounded-lg shadow flex justify-between items-center",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("select",{value:u,onChange:e=>m(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2",children:[r.jsx("option",{value:"all",children:"全部优先级"}),r.jsx("option",{value:"high",children:"高优先级"}),r.jsx("option",{value:"normal",children:"普通"}),r.jsx("option",{value:"low",children:"低优先级"}),r.jsx("option",{value:"hidden",children:"已隐藏"})]}),x.length>0&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:["已选择 ",x.length," 个帖子"]}),(0,r.jsxs)("select",{value:y,onChange:e=>j(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2",children:[r.jsx("option",{value:"",children:"选择批量操作"}),r.jsx("option",{value:"high",children:"设为高优先级"}),r.jsx("option",{value:"normal",children:"设为普通"}),r.jsx("option",{value:"low",children:"设为低优先级"}),r.jsx("option",{value:"hidden",children:"隐藏帖子"})]}),r.jsx("button",{onClick:w,disabled:!y,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50",children:"执行"})]}),(0,r.jsxs)("button",{onClick:f,disabled:s,className:"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 disabled:opacity-50 flex items-center space-x-2",children:[r.jsx(n.Z,{className:"w-4 h-4"}),r.jsx("span",{children:s?"更新中...":"批量更新质量评分"})]})]})}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[r.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:r.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"帖子列表"})}),r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:r.jsx("input",{type:"checkbox",checked:x.length===e.length&&e.length>0,onChange:t=>{t.target.checked?p(e.map(e=>e._id)):p([])}})}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"帖子信息"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"优先级"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"质量评分"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"数据统计"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态标识"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:0===e.length?r.jsx("tr",{children:r.jsx("td",{colSpan:7,className:"px-6 py-12 text-center text-gray-500",children:"暂无帖子数据"})}):e.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("input",{type:"checkbox",checked:x.includes(e._id),onChange:t=>{t.target.checked?p([...x,e._id]):p(x.filter(t=>t!==e._id))}})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[e.images&&e.images.length>0&&r.jsx("img",{src:e.images[0],alt:e.title,className:"h-10 w-10 rounded object-cover mr-3"}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm font-medium text-gray-900 max-w-xs truncate",children:e.title||e.description?.substring(0,50)+"..."}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["@",e.username]})]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[N(e.priority_level||"normal"),e.priority_reason&&r.jsx("div",{className:"text-xs text-gray-500 mt-1",children:e.priority_reason})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[r.jsx("div",{className:`text-lg font-bold ${k(e.current_quality_score||e.quality_score||50)}`,children:e.current_quality_score||e.quality_score||50}),h&&e.score_breakdown&&(0,r.jsxs)("div",{className:"text-xs text-gray-500 space-y-1",children:[(0,r.jsxs)("div",{children:["内容: ",e.score_breakdown.content]}),(0,r.jsxs)("div",{children:["用户: ",e.score_breakdown.user]}),(0,r.jsxs)("div",{children:["互动: ",e.score_breakdown.engagement]}),(0,r.jsxs)("div",{children:["时效: ",e.score_breakdown.time]}),(0,r.jsxs)("div",{children:["扣分: ",e.score_breakdown.penalty]})]})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,r.jsxs)("div",{children:["\uD83D\uDC4D ",e.likes||0]}),(0,r.jsxs)("div",{children:["\uD83D\uDCAC ",e.comments||0]}),(0,r.jsxs)("div",{children:["\uD83D\uDC41️ ",e.views||0]})]}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[e.hasAds&&r.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800",children:"\uD83D\uDCE2 含广告"}),e.reportCount&&e.reportCount>0&&(0,r.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800",children:["\uD83D\uDEA8 ",e.reportCount," 举报"]}),e.violationLevel&&(0,r.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800",children:["⚠️ ",e.violationLevel]})]})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:r.jsx("div",{className:"flex space-x-2",children:(0,r.jsxs)("select",{value:e.priority_level||"normal",onChange:t=>{let s=prompt("请输入调整原因（可选）：");b(e._id,t.target.value,s||void 0)},className:"text-xs border border-gray-300 rounded px-2 py-1",children:[r.jsx("option",{value:"high",children:"高优先级"}),r.jsx("option",{value:"normal",children:"普通"}),r.jsx("option",{value:"low",children:"低优先级"}),r.jsx("option",{value:"hidden",children:"隐藏"})]})})})]},e._id))})]})})]})]})}},9457:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\admin\layout.tsx#default`)},34312:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\admin\posts\page.tsx#default`)},23824:()=>{},39258:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});var r=s(17577);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))})},71684:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});var r=s(17577);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[276,201,240],()=>s(428));module.exports=r})();