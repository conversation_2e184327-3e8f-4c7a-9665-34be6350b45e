const cloudbase = require('@cloudbase/node-sdk');

const app = cloudbase.init({
  env: cloudbase.SYMBOL_CURRENT_ENV
});

const db = app.database();

// 生成用户可读性ID
function generateUserCode(userIndex, registrationDate) {
  const dateStr = registrationDate.toISOString().slice(0, 10).replace(/-/g, '');
  const indexStr = userIndex.toString().padStart(6, '0');
  return `USER_${indexStr}_${dateStr}`;
}

// 生成帖子可读性ID
function generatePostCode(postDate, dailyIndex) {
  const dateStr = postDate.toISOString().slice(0, 10).replace(/-/g, '');
  const indexStr = dailyIndex.toString().padStart(3, '0');
  return `POST_${dateStr}_${indexStr}`;
}

// 为现有用户添加user_code
async function addUserCodes() {
  try {
    console.log('开始为现有用户添加user_code...');
    
    // 获取所有用户，按注册时间排序
    const users = await db.collection('users')
      .orderBy('created_at', 'asc')
      .get();

    console.log(`找到 ${users.data.length} 个用户`);

    let userIndex = 1;
    const batch = db.batch();
    let batchCount = 0;

    for (const user of users.data) {
      if (!user.user_code) {
        const registrationDate = user.created_at ? new Date(user.created_at) : new Date();
        const userCode = generateUserCode(userIndex, registrationDate);
        
        batch.update(db.collection('users').doc(user._id), {
          user_code: userCode
        });
        
        batchCount++;
        userIndex++;

        // 每500个用户提交一次批量操作
        if (batchCount >= 500) {
          await batch.commit();
          console.log(`已处理 ${userIndex - 1} 个用户`);
          batchCount = 0;
        }
      }
    }

    // 提交剩余的批量操作
    if (batchCount > 0) {
      await batch.commit();
    }

    console.log(`用户user_code添加完成，共处理 ${userIndex - 1} 个用户`);
    return { success: true, processedUsers: userIndex - 1 };

  } catch (error) {
    console.error('添加用户user_code失败:', error);
    throw error;
  }
}

// 为现有帖子添加post_code
async function addPostCodes() {
  try {
    console.log('开始为现有帖子添加post_code...');
    
    // 获取所有帖子，按创建时间排序
    const posts = await db.collection('posts')
      .orderBy('created_at', 'asc')
      .get();

    console.log(`找到 ${posts.data.length} 个帖子`);

    const dailyCounters = new Map(); // 记录每日帖子计数
    const batch = db.batch();
    let batchCount = 0;

    for (const post of posts.data) {
      if (!post.post_code) {
        const postDate = post.created_at ? new Date(post.created_at) : new Date();
        const dateKey = postDate.toISOString().slice(0, 10);
        
        // 获取当日计数
        const dailyIndex = (dailyCounters.get(dateKey) || 0) + 1;
        dailyCounters.set(dateKey, dailyIndex);
        
        const postCode = generatePostCode(postDate, dailyIndex);
        
        batch.update(db.collection('posts').doc(post._id), {
          post_code: postCode
        });
        
        batchCount++;

        // 每500个帖子提交一次批量操作
        if (batchCount >= 500) {
          await batch.commit();
          console.log(`已处理 ${batchCount} 个帖子`);
          batchCount = 0;
        }
      }
    }

    // 提交剩余的批量操作
    if (batchCount > 0) {
      await batch.commit();
    }

    console.log(`帖子post_code添加完成，共处理 ${posts.data.length} 个帖子`);
    return { success: true, processedPosts: posts.data.length };

  } catch (error) {
    console.error('添加帖子post_code失败:', error);
    throw error;
  }
}

// 创建可读性ID的唯一索引
async function createReadableIdIndexes() {
  try {
    console.log('创建可读性ID索引...');
    
    // 为user_code创建唯一索引
    await db.collection('users').createIndex({
      name: 'user_code_unique',
      keys: [{ name: 'user_code', direction: '1' }],
      unique: true
    });

    // 为post_code创建唯一索引
    await db.collection('posts').createIndex({
      name: 'post_code_unique',
      keys: [{ name: 'post_code', direction: '1' }],
      unique: true
    });

    console.log('可读性ID索引创建完成');
    return { success: true };

  } catch (error) {
    console.error('创建索引失败:', error);
    // 索引可能已存在，不抛出错误
    return { success: true, warning: error.message };
  }
}

// 验证可读性ID的唯一性
async function validateReadableIds() {
  try {
    console.log('验证可读性ID唯一性...');
    
    // 检查user_code重复
    const userCodes = await db.collection('users')
      .field({ user_code: true })
      .get();
    
    const userCodeSet = new Set();
    let duplicateUserCodes = 0;
    
    for (const user of userCodes.data) {
      if (user.user_code) {
        if (userCodeSet.has(user.user_code)) {
          duplicateUserCodes++;
        } else {
          userCodeSet.add(user.user_code);
        }
      }
    }

    // 检查post_code重复
    const postCodes = await db.collection('posts')
      .field({ post_code: true })
      .get();
    
    const postCodeSet = new Set();
    let duplicatePostCodes = 0;
    
    for (const post of postCodes.data) {
      if (post.post_code) {
        if (postCodeSet.has(post.post_code)) {
          duplicatePostCodes++;
        } else {
          postCodeSet.add(post.post_code);
        }
      }
    }

    console.log(`验证完成: 用户重复${duplicateUserCodes}个, 帖子重复${duplicatePostCodes}个`);
    
    return {
      success: true,
      validation: {
        totalUsers: userCodes.data.length,
        duplicateUserCodes,
        totalPosts: postCodes.data.length,
        duplicatePostCodes
      }
    };

  } catch (error) {
    console.error('验证失败:', error);
    throw error;
  }
}

// 云函数入口
exports.main = async (event, context) => {
  const { action = 'addUserCodes' } = event;

  try {
    switch (action) {
      case 'addUserCodes':
        return await addUserCodes();
      
      case 'addPostCodes':
        return await addPostCodes();
      
      case 'createIndexes':
        return await createReadableIdIndexes();
      
      case 'validate':
        return await validateReadableIds();
      
      case 'all':
        // 执行完整的迁移流程
        const userResult = await addUserCodes();
        const postResult = await addPostCodes();
        const indexResult = await createReadableIdIndexes();
        const validationResult = await validateReadableIds();
        
        return {
          success: true,
          results: {
            users: userResult,
            posts: postResult,
            indexes: indexResult,
            validation: validationResult
          }
        };
      
      default:
        return {
          success: false,
          message: '不支持的操作'
        };
    }
  } catch (error) {
    console.error('操作失败:', error);
    return {
      success: false,
      message: error.message,
      error: error
    };
  }
};
