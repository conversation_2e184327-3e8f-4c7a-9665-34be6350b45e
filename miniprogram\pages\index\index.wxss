/* 首页样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.userinfo-avatar {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
}

.userinfo-nickname {
  font-size: 32rpx;
  color: #333;
}

.userinfo-btn {
  background-color: #07c160;
  color: white;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  padding: 16rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.search-btn {
  background-color: #07c160;
  color: white;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.posts-container {
  background-color: white;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.posts-scroll {
  height: 800rpx;
}

.post-item {
  display: flex;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 20rpx;
}

.post-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.post-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.post-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.post-price {
  font-size: 36rpx;
  color: #ff6b6b;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.post-location {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.post-time {
  font-size: 24rpx;
  color: #999;
}

.quick-actions {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  background-color: #07c160;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx;
  font-size: 32rpx;
}
