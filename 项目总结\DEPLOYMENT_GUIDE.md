# 宠物交易平台 - 部署运维指南

## 🚀 快速部署

### 环境要求
- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **腾讯云开发账号**: 已开通CloudBase服务
- **操作系统**: Windows/macOS/Linux

### 一键部署脚本
```bash
#!/bin/bash
# 快速部署脚本

echo "🚀 开始部署宠物交易平台..."

# 1. 安装依赖
echo "📦 安装项目依赖..."
npm install

# 2. 构建项目
echo "🔨 构建前端项目..."
npm run build

# 3. 登录CloudBase
echo "🔐 登录CloudBase..."
npx @cloudbase/cli login

# 4. 部署云函数
echo "☁️ 部署云函数..."
npx @cloudbase/cli functions:deploy pet-api

# 5. 部署静态资源
echo "📁 部署静态资源..."
npx @cloudbase/cli hosting:deploy out -e yichongyuzhou-3g9112qwf5f3487b

echo "✅ 部署完成！"
echo "🌐 访问地址: https://yichongyuzhou-3g9112qwf5f3487b-**********.tcloudbaseapp.com/pet-platform-final/"
```

---

## 🔧 详细部署步骤

### 1. 环境准备

#### 安装Node.js和npm
```bash
# 检查版本
node --version  # 应该 >= 18.0.0
npm --version   # 应该 >= 8.0.0

# 如果版本过低，请更新
# Windows: 下载最新版本安装包
# macOS: brew install node
# Linux: 使用包管理器安装
```

#### 安装CloudBase CLI
```bash
npm install -g @cloudbase/cli

# 验证安装
tcb --version
```

### 2. 项目配置

#### 环境变量配置
创建 `.env.local` 文件：
```env
# CloudBase配置
NEXT_PUBLIC_CLOUDBASE_ENV_ID=yichongyuzhou-3g9112qwf5f3487b
CLOUDBASE_SECRET_ID=your_secret_id
CLOUDBASE_SECRET_KEY=your_secret_key

# 应用配置
NEXT_PUBLIC_APP_NAME=宠物交易平台
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_API_BASE_URL=https://yichongyuzhou-3g9112qwf5f3487b-**********.tcloudbaseapp.com

# 功能开关
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=true
NEXT_PUBLIC_ENABLE_DEBUG=false
```

#### CloudBase配置文件
创建 `cloudbaserc.json`：
```json
{
  "envId": "yichongyuzhou-3g9112qwf5f3487b",
  "region": "ap-shanghai",
  "functionRoot": "./cloudfunctions",
  "functions": [
    {
      "name": "pet-api",
      "timeout": 60,
      "envVariables": {},
      "runtime": "Nodejs18.15",
      "memorySize": 256,
      "handler": "index.main"
    }
  ],
  "hosting": {
    "root": "./out",
    "index": "index.html",
    "error": "404.html"
  }
}
```

### 3. 本地开发

#### 启动开发服务器
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问 http://localhost:3000
```

#### 本地测试云函数
```bash
# 进入云函数目录
cd cloudfunctions/pet-api

# 安装云函数依赖
npm install

# 本地测试
node test.js
```

### 4. 构建和部署

#### 构建前端项目
```bash
# 构建生产版本
npm run build

# 检查构建产物
ls -la out/
```

#### 部署云函数
```bash
# 登录CloudBase
tcb login

# 部署单个云函数
tcb functions:deploy pet-api -e yichongyuzhou-3g9112qwf5f3487b

# 查看云函数状态
tcb functions:list -e yichongyuzhou-3g9112qwf5f3487b
```

#### 部署静态资源
```bash
# 部署到静态托管
tcb hosting:deploy out -e yichongyuzhou-3g9112qwf5f3487b

# 查看部署状态
tcb hosting:detail -e yichongyuzhou-3g9112qwf5f3487b
```

---

## 🗄️ 数据库初始化

### 创建数据库集合
```bash
# 使用CloudBase CLI创建集合
tcb db:createCollection users -e yichongyuzhou-3g9112qwf5f3487b
tcb db:createCollection pets -e yichongyuzhou-3g9112qwf5f3487b
tcb db:createCollection activities -e yichongyuzhou-3g9112qwf5f3487b
tcb db:createCollection notifications -e yichongyuzhou-3g9112qwf5f3487b
tcb db:createCollection bookmarks -e yichongyuzhou-3g9112qwf5f3487b
tcb db:createCollection admins -e yichongyuzhou-3g9112qwf5f3487b
tcb db:createCollection system_config -e yichongyuzhou-3g9112qwf5f3487b
```

### 初始化数据脚本
```javascript
// scripts/init-database.js
const cloudbase = require('@cloudbase/node-sdk');

const app = cloudbase.init({
  env: 'yichongyuzhou-3g9112qwf5f3487b'
});

const db = app.database();

async function initDatabase() {
  try {
    // 创建超级管理员
    await db.collection('admins').add({
      _id: 'superadminTT',
      username: 'superadminTT',
      password: '$2b$10$encrypted_password_hash',
      role: 'super_admin',
      permissions: ['all'],
      created_at: new Date(),
      status: 'active'
    });

    // 初始化系统配置
    await db.collection('system_config').add([
      {
        key: 'activity_system',
        value: {
          enabled: true,
          allow_comments: true,
          comment_cooldown: 10,
          max_comment_length: 100
        },
        description: '活动系统配置'
      },
      {
        key: 'ad_system',
        value: {
          enabled: true,
          banner_ads: true,
          feed_ads: true
        },
        description: '广告系统配置'
      }
    ]);

    console.log('✅ 数据库初始化完成');
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
  }
}

initDatabase();
```

运行初始化脚本：
```bash
node scripts/init-database.js
```

---

## 📊 监控和日志

### 性能监控设置

#### 前端监控
```javascript
// 在 _app.tsx 中添加
import { useEffect } from 'react';

function MyApp({ Component, pageProps }) {
  useEffect(() => {
    // 页面性能监控
    if (typeof window !== 'undefined') {
      window.addEventListener('load', () => {
        const navigation = performance.getEntriesByType('navigation')[0];
        console.log('页面加载时间:', navigation.loadEventEnd - navigation.navigationStart);
        
        // 发送性能数据到监控服务
        fetch('/api/metrics', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'performance',
            loadTime: navigation.loadEventEnd - navigation.navigationStart,
            url: window.location.href,
            timestamp: Date.now()
          })
        });
      });
    }
  }, []);

  return <Component {...pageProps} />;
}
```

#### 云函数监控
```javascript
// 在云函数中添加监控代码
exports.main = async (event, context) => {
  const startTime = Date.now();
  
  try {
    // 业务逻辑
    const result = await handleRequest(event);
    
    // 记录成功指标
    await recordMetric({
      function: context.FUNCTION_NAME,
      action: event.action,
      duration: Date.now() - startTime,
      status: 'success',
      timestamp: new Date()
    });
    
    return result;
  } catch (error) {
    // 记录错误指标
    await recordMetric({
      function: context.FUNCTION_NAME,
      action: event.action,
      duration: Date.now() - startTime,
      status: 'error',
      error: error.message,
      timestamp: new Date()
    });
    
    throw error;
  }
};
```

### 日志配置

#### 结构化日志
```javascript
// utils/logger.js
class Logger {
  constructor(service) {
    this.service = service;
  }
  
  log(level, message, meta = {}) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      service: this.service,
      level,
      message,
      meta,
      traceId: this.generateTraceId()
    };
    
    console.log(JSON.stringify(logEntry));
    
    // 发送到日志服务
    this.sendToLogService(logEntry);
  }
  
  info(message, meta) { this.log('info', message, meta); }
  warn(message, meta) { this.log('warn', message, meta); }
  error(message, meta) { this.log('error', message, meta); }
  
  generateTraceId() {
    return Math.random().toString(36).substring(2, 15);
  }
  
  async sendToLogService(logEntry) {
    // 这里可以集成第三方日志服务
    // 如腾讯云CLS、阿里云SLS等
  }
}

module.exports = Logger;
```

---

## 🔄 CI/CD配置

### GitHub Actions工作流
```yaml
# .github/workflows/deploy.yml
name: Deploy to CloudBase

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  CLOUDBASE_ENV_ID: yichongyuzhou-3g9112qwf5f3487b

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linting
      run: npm run lint
    
    - name: Run tests
      run: npm run test
    
    - name: Build project
      run: npm run build
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-files
        path: out/

  deploy-staging:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: build-files
        path: out/
    
    - name: Deploy to staging
      env:
        CLOUDBASE_SECRET_ID: ${{ secrets.CLOUDBASE_SECRET_ID }}
        CLOUDBASE_SECRET_KEY: ${{ secrets.CLOUDBASE_SECRET_KEY }}
      run: |
        npm install -g @cloudbase/cli
        tcb login --apiKeyId $CLOUDBASE_SECRET_ID --apiKey $CLOUDBASE_SECRET_KEY
        tcb hosting:deploy out -e $CLOUDBASE_ENV_ID

  deploy-production:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: build-files
        path: out/
    
    - name: Deploy functions
      env:
        CLOUDBASE_SECRET_ID: ${{ secrets.CLOUDBASE_SECRET_ID }}
        CLOUDBASE_SECRET_KEY: ${{ secrets.CLOUDBASE_SECRET_KEY }}
      run: |
        npm install -g @cloudbase/cli
        tcb login --apiKeyId $CLOUDBASE_SECRET_ID --apiKey $CLOUDBASE_SECRET_KEY
        tcb functions:deploy pet-api -e $CLOUDBASE_ENV_ID
    
    - name: Deploy hosting
      env:
        CLOUDBASE_SECRET_ID: ${{ secrets.CLOUDBASE_SECRET_ID }}
        CLOUDBASE_SECRET_KEY: ${{ secrets.CLOUDBASE_SECRET_KEY }}
      run: |
        tcb hosting:deploy out -e $CLOUDBASE_ENV_ID
    
    - name: Notify deployment
      run: |
        echo "🎉 部署成功！"
        echo "🌐 访问地址: https://yichongyuzhou-3g9112qwf5f3487b-**********.tcloudbaseapp.com/pet-platform-final/"
```

### 部署脚本
```bash
#!/bin/bash
# deploy.sh - 部署脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查部署环境..."
    
    # 检查Node.js版本
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    if [[ $(echo "$NODE_VERSION 18.0.0" | tr " " "\n" | sort -V | head -n1) != "18.0.0" ]]; then
        log_error "Node.js 版本过低，需要 >= 18.0.0"
        exit 1
    fi
    
    # 检查CloudBase CLI
    if ! command -v tcb &> /dev/null; then
        log_warn "CloudBase CLI 未安装，正在安装..."
        npm install -g @cloudbase/cli
    fi
    
    log_info "环境检查通过"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    # 安装依赖
    npm ci
    
    # 运行测试
    npm run test
    
    # 构建项目
    npm run build
    
    log_info "项目构建完成"
}

# 部署云函数
deploy_functions() {
    log_info "部署云函数..."
    
    tcb functions:deploy pet-api -e $CLOUDBASE_ENV_ID
    
    log_info "云函数部署完成"
}

# 部署静态资源
deploy_hosting() {
    log_info "部署静态资源..."
    
    tcb hosting:deploy out -e $CLOUDBASE_ENV_ID
    
    log_info "静态资源部署完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查网站是否可访问
    SITE_URL="https://yichongyuzhou-3g9112qwf5f3487b-**********.tcloudbaseapp.com/pet-platform-final/"
    
    if curl -f -s "$SITE_URL" > /dev/null; then
        log_info "网站健康检查通过"
    else
        log_error "网站健康检查失败"
        exit 1
    fi
}

# 主函数
main() {
    log_info "开始部署宠物交易平台..."
    
    # 设置环境变量
    export CLOUDBASE_ENV_ID="yichongyuzhou-3g9112qwf5f3487b"
    
    # 执行部署步骤
    check_environment
    build_project
    deploy_functions
    deploy_hosting
    health_check
    
    log_info "🎉 部署完成！"
    log_info "🌐 访问地址: $SITE_URL"
}

# 错误处理
trap 'log_error "部署失败，请检查错误信息"; exit 1' ERR

# 执行主函数
main "$@"
```

---

## 🔧 运维工具

### 数据备份脚本
```bash
#!/bin/bash
# backup.sh - 数据备份脚本

BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"
ENV_ID="yichongyuzhou-3g9112qwf5f3487b"

mkdir -p "$BACKUP_DIR"

# 备份数据库集合
COLLECTIONS=("users" "pets" "activities" "notifications" "bookmarks" "admins" "system_config")

for collection in "${COLLECTIONS[@]}"; do
    echo "备份集合: $collection"
    tcb db:export "$collection" -e "$ENV_ID" -o "$BACKUP_DIR/${collection}.json"
done

# 压缩备份文件
tar -czf "$BACKUP_DIR.tar.gz" -C "$BACKUP_DIR" .
rm -rf "$BACKUP_DIR"

echo "✅ 备份完成: $BACKUP_DIR.tar.gz"
```

### 监控脚本
```bash
#!/bin/bash
# monitor.sh - 系统监控脚本

ENV_ID="yichongyuzhou-3g9112qwf5f3487b"
SITE_URL="https://yichongyuzhou-3g9112qwf5f3487b-**********.tcloudbaseapp.com/pet-platform-final/"

# 检查网站状态
check_website() {
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$SITE_URL")
    if [ "$response" = "200" ]; then
        echo "✅ 网站正常 (HTTP $response)"
    else
        echo "❌ 网站异常 (HTTP $response)"
        # 发送告警通知
        send_alert "网站访问异常" "HTTP状态码: $response"
    fi
}

# 检查云函数状态
check_functions() {
    local functions=$(tcb functions:list -e "$ENV_ID" --json)
    echo "$functions" | jq -r '.[] | select(.Status != "Active") | "❌ 云函数异常: \(.FunctionName) - \(.Status)"'
}

# 发送告警
send_alert() {
    local title="$1"
    local message="$2"
    
    # 这里可以集成钉钉、企业微信等告警通知
    echo "🚨 告警: $title - $message"
}

# 主监控循环
main() {
    echo "🔍 开始系统监控..."
    
    while true; do
        echo "$(date): 执行健康检查"
        check_website
        check_functions
        
        # 每5分钟检查一次
        sleep 300
    done
}

main "$@"
```

---

## 📋 故障排查

### 常见问题及解决方案

#### 1. 部署失败
```bash
# 检查CloudBase CLI登录状态
tcb auth:list

# 重新登录
tcb login

# 检查环境权限
tcb env:list
```

#### 2. 云函数调用失败
```bash
# 查看云函数日志
tcb functions:log pet-api -e yichongyuzhou-3g9112qwf5f3487b

# 检查云函数状态
tcb functions:detail pet-api -e yichongyuzhou-3g9112qwf5f3487b
```

#### 3. 静态资源访问异常
```bash
# 检查静态托管状态
tcb hosting:detail -e yichongyuzhou-3g9112qwf5f3487b

# 清除CDN缓存
tcb hosting:cache:refresh -e yichongyuzhou-3g9112qwf5f3487b
```

#### 4. 数据库连接问题
```bash
# 检查数据库状态
tcb db:collection:list -e yichongyuzhou-3g9112qwf5f3487b

# 测试数据库连接
tcb db:collection:count users -e yichongyuzhou-3g9112qwf5f3487b
```

### 性能优化建议

#### 前端优化
- 启用图片懒加载
- 使用CDN加速静态资源
- 实现代码分割和按需加载
- 优化图片格式和大小

#### 后端优化
- 优化数据库查询
- 实现接口缓存
- 使用连接池
- 监控云函数性能

---

*部署指南最后更新时间: 2025年1月16日*
*适用版本: v1.0.0*
