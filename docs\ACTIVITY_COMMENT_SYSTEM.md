# 🗨️ 活动评论系统技术方案

## 📋 概述

设计一个高性能、可控制的活动评论系统，支持投票后评论、管理员开关控制，并优化大量评论的服务器性能。

## 🎯 **核心功能设计**

### **1. 评论权限控制**

#### **A. 投票后评论机制**
```javascript
// 评论权限验证
async function checkCommentPermission(userId, activityId) {
  // 1. 检查活动是否开启评论
  const activity = await db.collection('activities').doc(activityId).get();
  if (!activity.data.comments_enabled) {
    return { canComment: false, reason: 'comments_disabled' };
  }
  
  // 2. 检查是否需要投票后评论
  if (activity.data.comments_after_vote) {
    const hasVoted = await db.collection('activity_votes')
      .where({ activity_id: activityId, voter_id: userId })
      .limit(1)
      .get();
    
    if (hasVoted.data.length === 0) {
      return { canComment: false, reason: 'vote_required' };
    }
  }
  
  return { canComment: true };
}
```

#### **B. 管理员控制面板**
```javascript
// 活动评论设置
const activityCommentSettings = {
  comments_enabled: true,           // 是否开启评论
  comments_after_vote: true,        // 是否需要投票后评论
  comment_moderation: 'auto',       // 评论审核：auto/manual/none
  max_comment_length: 500,          // 评论最大长度
  rate_limit: {                     // 评论频率限制
    max_per_minute: 3,
    max_per_hour: 20
  }
};
```

### **2. 高性能数据库设计**

#### **A. 评论表结构优化**
```sql
-- 活动评论表 (activity_comments)
CREATE TABLE activity_comments (
  id VARCHAR(50) PRIMARY KEY,
  activity_id VARCHAR(50) NOT NULL,
  user_id VARCHAR(50) NOT NULL,
  parent_id VARCHAR(50),              -- 回复评论的父ID
  content TEXT NOT NULL,
  status ENUM('pending', 'approved', 'rejected') DEFAULT 'approved',
  like_count INT DEFAULT 0,
  reply_count INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- 性能优化索引
  INDEX idx_activity_time (activity_id, created_at DESC),
  INDEX idx_user_activity (user_id, activity_id),
  INDEX idx_parent_time (parent_id, created_at ASC),
  INDEX idx_status_time (status, created_at DESC)
);

-- 评论统计表 (activity_comment_stats)
CREATE TABLE activity_comment_stats (
  activity_id VARCHAR(50) PRIMARY KEY,
  total_comments INT DEFAULT 0,
  approved_comments INT DEFAULT 0,
  last_comment_time TIMESTAMP,
  hot_comments JSON,                  -- 缓存热门评论ID
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### **B. 分页和缓存策略**
```javascript
// 评论分页加载
async function getActivityComments(activityId, page = 1, limit = 20) {
  const offset = (page - 1) * limit;
  
  // 1. 先从缓存获取
  const cacheKey = `activity_comments:${activityId}:${page}`;
  let comments = await redis.get(cacheKey);
  
  if (!comments) {
    // 2. 数据库查询
    comments = await db.collection('activity_comments')
      .where({ 
        activity_id: activityId, 
        status: 'approved',
        parent_id: null  // 只获取顶级评论
      })
      .orderBy('created_at', 'desc')
      .skip(offset)
      .limit(limit)
      .get();
    
    // 3. 缓存结果（5分钟）
    await redis.setex(cacheKey, 300, JSON.stringify(comments.data));
  }
  
  return JSON.parse(comments);
}
```

### **3. 性能优化策略**

#### **A. 分层加载**
```javascript
// 评论分层加载策略
const commentLoadingStrategy = {
  // 第一层：顶级评论
  topLevel: {
    pageSize: 20,
    sortBy: 'created_at',
    cacheTime: 300  // 5分钟缓存
  },
  
  // 第二层：回复评论
  replies: {
    pageSize: 10,
    loadOnDemand: true,  // 点击展开时加载
    maxDepth: 2          // 最大回复层级
  },
  
  // 热门评论
  hotComments: {
    count: 5,
    criteria: 'like_count',
    cacheTime: 1800      // 30分钟缓存
  }
};
```

#### **B. 实时更新优化**
```javascript
// WebSocket 连接管理
class ActivityCommentManager {
  constructor() {
    this.connections = new Map(); // 活动ID -> WebSocket连接数组
    this.rateLimiter = new Map(); // 用户ID -> 最后评论时间
  }
  
  // 加入活动评论房间
  joinActivity(ws, activityId, userId) {
    if (!this.connections.has(activityId)) {
      this.connections.set(activityId, []);
    }
    this.connections.get(activityId).push({ ws, userId });
  }
  
  // 广播新评论
  broadcastComment(activityId, comment, excludeUserId) {
    const connections = this.connections.get(activityId) || [];
    connections.forEach(({ ws, userId }) => {
      if (userId !== excludeUserId && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          type: 'new_comment',
          data: comment
        }));
      }
    });
  }
  
  // 评论频率限制
  checkRateLimit(userId) {
    const lastCommentTime = this.rateLimiter.get(userId) || 0;
    const now = Date.now();
    const minInterval = 20000; // 20秒间隔
    
    if (now - lastCommentTime < minInterval) {
      return false;
    }
    
    this.rateLimiter.set(userId, now);
    return true;
  }
}
```

### **4. 服务器性能优化**

#### **A. 数据库连接池优化**
```javascript
// 数据库连接池配置
const dbConfig = {
  // 连接池设置
  pool: {
    min: 5,           // 最小连接数
    max: 20,          // 最大连接数
    acquire: 30000,   // 获取连接超时时间
    idle: 10000       // 连接空闲时间
  },
  
  // 查询优化
  query: {
    timeout: 5000,    // 查询超时时间
    retry: 2          // 重试次数
  }
};
```

#### **B. Redis 缓存策略**
```javascript
// 缓存层级设计
const cacheStrategy = {
  // L1: 热门评论缓存（长期）
  hotComments: {
    key: 'hot_comments:{activityId}',
    ttl: 1800,        // 30分钟
    updateTrigger: 'like_count_change'
  },
  
  // L2: 分页评论缓存（中期）
  pageComments: {
    key: 'page_comments:{activityId}:{page}',
    ttl: 300,         // 5分钟
    updateTrigger: 'new_comment'
  },
  
  // L3: 用户权限缓存（短期）
  userPermissions: {
    key: 'comment_permission:{userId}:{activityId}',
    ttl: 60,          // 1分钟
    updateTrigger: 'vote_action'
  }
};
```

#### **C. 异步处理队列**
```javascript
// 评论处理队列
const commentQueue = {
  // 评论审核队列
  moderation: {
    name: 'comment_moderation',
    concurrency: 5,
    processor: async (job) => {
      const { commentId } = job.data;
      await moderateComment(commentId);
    }
  },
  
  // 统计更新队列
  statistics: {
    name: 'comment_stats',
    concurrency: 3,
    processor: async (job) => {
      const { activityId } = job.data;
      await updateCommentStats(activityId);
    }
  },
  
  // 通知推送队列
  notifications: {
    name: 'comment_notifications',
    concurrency: 10,
    processor: async (job) => {
      const { userId, commentId } = job.data;
      await sendCommentNotification(userId, commentId);
    }
  }
};
```

### **5. 前端优化策略**

#### **A. 虚拟滚动**
```javascript
// 大量评论的虚拟滚动实现
class VirtualCommentList {
  constructor(container, itemHeight = 120) {
    this.container = container;
    this.itemHeight = itemHeight;
    this.visibleCount = Math.ceil(container.clientHeight / itemHeight) + 2;
    this.startIndex = 0;
    this.comments = [];
  }
  
  // 渲染可见评论
  renderVisibleComments() {
    const endIndex = Math.min(
      this.startIndex + this.visibleCount,
      this.comments.length
    );
    
    const visibleComments = this.comments.slice(this.startIndex, endIndex);
    this.updateDOM(visibleComments);
  }
  
  // 滚动事件处理
  onScroll(scrollTop) {
    const newStartIndex = Math.floor(scrollTop / this.itemHeight);
    if (newStartIndex !== this.startIndex) {
      this.startIndex = newStartIndex;
      this.renderVisibleComments();
    }
  }
}
```

#### **B. 评论组件优化**
```jsx
// React 评论组件优化
const CommentItem = React.memo(({ comment, onReply, onLike }) => {
  const [showReplies, setShowReplies] = useState(false);
  const [replies, setReplies] = useState([]);
  
  // 懒加载回复
  const loadReplies = useCallback(async () => {
    if (!showReplies && comment.reply_count > 0) {
      const replyData = await fetchCommentReplies(comment.id);
      setReplies(replyData);
    }
    setShowReplies(!showReplies);
  }, [comment.id, showReplies]);
  
  return (
    <div className="comment-item">
      <CommentContent comment={comment} />
      <CommentActions 
        onReply={onReply} 
        onLike={onLike}
        onToggleReplies={loadReplies}
      />
      {showReplies && (
        <CommentReplies replies={replies} />
      )}
    </div>
  );
});
```

### **6. 监控和告警**

#### **A. 性能监控指标**
```javascript
// 关键性能指标
const performanceMetrics = {
  // 数据库性能
  database: {
    queryTime: 'avg_query_time_ms',
    connectionPool: 'active_connections',
    slowQueries: 'slow_queries_count'
  },
  
  // 缓存性能
  cache: {
    hitRate: 'cache_hit_rate',
    memoryUsage: 'redis_memory_usage',
    keyCount: 'cached_keys_count'
  },
  
  // 应用性能
  application: {
    responseTime: 'api_response_time',
    errorRate: 'error_rate',
    throughput: 'requests_per_second'
  }
};
```

#### **B. 自动扩容策略**
```javascript
// 自动扩容配置
const autoScalingConfig = {
  // 数据库扩容
  database: {
    trigger: 'connection_pool_usage > 80%',
    action: 'increase_pool_size',
    maxConnections: 50
  },
  
  // 缓存扩容
  cache: {
    trigger: 'memory_usage > 85%',
    action: 'add_redis_instance',
    maxInstances: 3
  },
  
  // 服务器扩容
  server: {
    trigger: 'cpu_usage > 70% for 5min',
    action: 'add_server_instance',
    maxInstances: 5
  }
};
```

## 🎯 **实施建议**

### **第一阶段：基础功能**
1. 实现基本的评论CRUD功能
2. 添加投票后评论的权限控制
3. 实现管理员评论开关

### **第二阶段：性能优化**
1. 添加Redis缓存层
2. 实现评论分页和虚拟滚动
3. 优化数据库查询和索引

### **第三阶段：高级功能**
1. 实现实时评论更新
2. 添加评论审核和过滤
3. 完善监控和告警系统

## 📊 **预期性能表现**

### **处理能力**
- **并发用户**：支持1000+用户同时在线评论
- **评论数量**：单个活动支持10万+评论
- **响应时间**：评论加载 < 200ms，发送 < 500ms

### **资源消耗**
- **数据库**：优化后查询时间 < 50ms
- **内存**：Redis缓存占用 < 1GB
- **带宽**：实时更新带宽 < 10MB/s

这个方案既保证了功能的完整性，又通过多层优化确保了服务器的稳定性能！🚀
