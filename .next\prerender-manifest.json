{"version": 4, "routes": {"/admin/activities": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin/activities", "dataRoute": "/admin/activities.rsc"}, "/admin/dashboard": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin/dashboard", "dataRoute": "/admin/dashboard.rsc"}, "/admin": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin", "dataRoute": "/admin.rsc"}, "/activities": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/activities", "dataRoute": "/activities.rsc"}, "/admin/ads": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin/ads", "dataRoute": "/admin/ads.rsc"}, "/admin/posts": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin/posts", "dataRoute": "/admin/posts.rsc"}, "/admin/performance": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin/performance", "dataRoute": "/admin/performance.rsc"}, "/debug": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/debug", "dataRoute": "/debug.rsc"}, "/favorites": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/favorites", "dataRoute": "/favorites.rsc"}, "/notifications": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/notifications", "dataRoute": "/notifications.rsc"}, "/messages": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/messages", "dataRoute": "/messages.rsc"}, "/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}, "/admin/settings": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin/settings", "dataRoute": "/admin/settings.rsc"}, "/profile": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/profile", "dataRoute": "/profile.rsc"}, "/post/detail": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/post/detail", "dataRoute": "/post/detail.rsc"}, "/upload": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/upload", "dataRoute": "/upload.rsc"}, "/test-email": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/test-email", "dataRoute": "/test-email.rsc"}, "/profile/2ed3518f6875a7f905582dfd0fc94e98": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/profile/[id]", "dataRoute": "/profile/2ed3518f6875a7f905582dfd0fc94e98.rsc"}, "/profile/c611b94668776114057326f5172f0bc5": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/profile/[id]", "dataRoute": "/profile/c611b94668776114057326f5172f0bc5.rsc"}, "/profile/d77d384f6877626a0574989b7124319b": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/profile/[id]", "dataRoute": "/profile/d77d384f6877626a0574989b7124319b.rsc"}, "/profile/eea1754d6873ce81053b899e0f3aa469": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/profile/[id]", "dataRoute": "/profile/eea1754d6873ce81053b899e0f3aa469.rsc"}}, "dynamicRoutes": {"/profile/[id]": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "routeRegex": "^/profile/([^/]+?)(?:/)?$", "dataRoute": "/profile/[id].rsc", "fallback": null, "dataRouteRegex": "^/profile/([^/]+?)\\.rsc$"}}, "notFoundRoutes": [], "preview": {"previewModeId": "f3e532d9173707c2572439b95697106b", "previewModeSigningKey": "7145b926fb75e5b2d0f60f266fc8aada0230490ecd4249a0bd54ea0f714bbfd9", "previewModeEncryptionKey": "c88ff7a84b75067375c3b5c590a62282afa82e8e8d3925568cb090aa8bde85a9"}}