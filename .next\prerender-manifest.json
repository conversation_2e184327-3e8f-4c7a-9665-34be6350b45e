{"version": 4, "routes": {"/admin/activities": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin/activities", "dataRoute": "/admin/activities.rsc"}, "/admin/ads": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin/ads", "dataRoute": "/admin/ads.rsc"}, "/admin/dashboard": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin/dashboard", "dataRoute": "/admin/dashboard.rsc"}, "/admin": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin", "dataRoute": "/admin.rsc"}, "/admin/performance": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin/performance", "dataRoute": "/admin/performance.rsc"}, "/admin/posts": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin/posts", "dataRoute": "/admin/posts.rsc"}, "/admin/settings": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin/settings", "dataRoute": "/admin/settings.rsc"}, "/debug": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/debug", "dataRoute": "/debug.rsc"}, "/favorites": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/favorites", "dataRoute": "/favorites.rsc"}, "/messages": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/messages", "dataRoute": "/messages.rsc"}, "/notifications": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/notifications", "dataRoute": "/notifications.rsc"}, "/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}, "/post/detail": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/post/detail", "dataRoute": "/post/detail.rsc"}, "/profile": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/profile", "dataRoute": "/profile.rsc"}, "/test-email": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/test-email", "dataRoute": "/test-email.rsc"}, "/upload": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/upload", "dataRoute": "/upload.rsc"}, "/profile/2ed3518f6875a7f905582dfd0fc94e98": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/profile/[id]", "dataRoute": "/profile/2ed3518f6875a7f905582dfd0fc94e98.rsc"}, "/profile/c611b94668776114057326f5172f0bc5": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/profile/[id]", "dataRoute": "/profile/c611b94668776114057326f5172f0bc5.rsc"}, "/profile/d77d384f6877626a0574989b7124319b": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/profile/[id]", "dataRoute": "/profile/d77d384f6877626a0574989b7124319b.rsc"}, "/profile/eea1754d6873ce81053b899e0f3aa469": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/profile/[id]", "dataRoute": "/profile/eea1754d6873ce81053b899e0f3aa469.rsc"}, "/activities": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/activities", "dataRoute": "/activities.rsc"}}, "dynamicRoutes": {"/profile/[id]": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "routeRegex": "^/profile/([^/]+?)(?:/)?$", "dataRoute": "/profile/[id].rsc", "fallback": null, "dataRouteRegex": "^/profile/([^/]+?)\\.rsc$"}}, "notFoundRoutes": [], "preview": {"previewModeId": "a715ade7af9f878df66dc9f406c7e073", "previewModeSigningKey": "e54bb31a6fcfdb1dcec0e739c3115d29a6f92bd92ca7a0deff45bfe75f5fbea7", "previewModeEncryptionKey": "b07cd75598013a63a4ba4b2ac544c2120e35c5b956c449a348644dfb2ed230d6"}}