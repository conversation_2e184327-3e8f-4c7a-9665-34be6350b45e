'use client';

import React, { useState } from 'react';
import { Flag } from 'lucide-react';
import { petAPI } from '@/lib/cloudbase';
import { showToast } from '@/components/ui/Toast';
import Button from '@/components/ui/Button';
import { Modal, ModalBody } from '@/components/ui/Modal';

interface UserReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  targetUserId: string;
  targetUserName: string;
  onSuccess?: () => void;
}

const USER_REPORT_REASONS = [
  { id: 1, label: '疑似骗子', description: '该用户存在欺诈行为或疑似诈骗' }
];

const UserReportModal: React.FC<UserReportModalProps> = ({
  isOpen,
  onClose,
  targetUserId,
  targetUserName,
  onSuccess
}) => {
  const [selectedReason, setSelectedReason] = useState(1); // 默认选中唯一的举报原因
  const [loading, setLoading] = useState(false);

  const handleClose = () => {
    setSelectedReason(1);
    onClose();
  };

  const handleSubmit = async () => {

    try {
      setLoading(true);
      const result = await petAPI.reportUser({
        targetUserId,
        reason: '疑似骗子'
      });

      if (result.success) {
        showToast.success('举报提交成功，我们会尽快处理');
        onSuccess?.();
        handleClose();
      } else {
        showToast.error(result.message || '举报失败');
      }
    } catch (error: any) {
      console.error('举报用户失败:', error);
      showToast.error(error.message || '举报失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <ModalBody>
        <div className="space-y-6">
          {/* 标题 */}
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-red-100 rounded-lg">
              <Flag className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">举报用户</h3>
              <p className="text-sm text-gray-500">举报 {targetUserName}</p>
            </div>
          </div>

          {/* 举报原因 */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">请选择举报原因：</h4>
            <div className="space-y-2">
              {USER_REPORT_REASONS.map((reason) => (
                <label
                  key={reason.id}
                  className={`flex items-start space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedReason === reason.id
                      ? 'border-red-500 bg-red-50'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <input
                    type="radio"
                    name="reason"
                    value={reason.id}
                    checked={selectedReason === reason.id}
                    onChange={() => setSelectedReason(reason.id)}
                    className="mt-1 text-red-600 focus:ring-red-500"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{reason.label}</div>
                    <div className="text-sm text-gray-500">{reason.description}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>



          {/* 按钮 */}
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleClose}
              className="flex-1"
              disabled={loading}
            >
              取消
            </Button>
            <Button
              variant="danger"
              onClick={handleSubmit}
              loading={loading}
              className="flex-1"
              disabled={false}
            >
              提交举报
            </Button>
          </div>
        </div>
      </ModalBody>
    </Modal>
  );
};

export default UserReportModal;
