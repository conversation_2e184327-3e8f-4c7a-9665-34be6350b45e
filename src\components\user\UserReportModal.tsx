'use client';

import React, { useState } from 'react';
import { X, Flag } from 'lucide-react';
import { petAPI } from '@/lib/cloudbase';
import { showToast } from '@/components/ui/Toast';
import Button from '@/components/ui/Button';
import { Modal, ModalBody } from '@/components/ui/Modal';

interface UserReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  targetUserId: string;
  targetUserName: string;
  onSuccess?: () => void;
}

const USER_REPORT_REASONS = [
  { id: 1, label: '发布非法野生动物信息', description: '发布受保护野生动物或违法动物信息' },
  { id: 2, label: '频繁发布虚假配种信息', description: '多次发布虚假的宠物配种信息' },
  { id: 3, label: '频繁发布虚假找回信息', description: '多次发布虚假的宠物找回信息' },
  { id: 4, label: '频繁发布虚假求购信息', description: '多次发布虚假的宠物求购信息' },
  { id: 5, label: '疑似商业机构伪装个人用户', description: '商业机构冒充个人用户发布信息' },
  { id: 6, label: '恶意骚扰其他用户', description: '恶意骚扰、辱骂或攻击其他用户' },
  { id: 7, label: '频繁发布低质量或重复内容', description: '大量发布重复或低质量内容' },
  { id: 8, label: '其他违规行为', description: '其他违反社区规定的行为' }
];

const UserReportModal: React.FC<UserReportModalProps> = ({
  isOpen,
  onClose,
  targetUserId,
  targetUserName,
  onSuccess
}) => {
  const [selectedReason, setSelectedReason] = useState(0);
  const [loading, setLoading] = useState(false);

  const handleClose = () => {
    setSelectedReason(0);
    onClose();
  };

  const handleSubmit = async () => {
    if (selectedReason === 0) {
      showToast.warning('请选择举报原因');
      return;
    }

    try {
      setLoading(true);
      const result = await petAPI.reportUser({
        targetUserId,
        reason: selectedReason
      });

      if (result.success) {
        showToast.success('举报提交成功，我们会尽快处理');
        onSuccess?.();
        handleClose();
      } else {
        showToast.error(result.message || '举报失败');
      }
    } catch (error: any) {
      console.error('举报用户失败:', error);
      showToast.error(error.message || '举报失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <ModalBody>
        <div className="space-y-6">
          {/* 标题 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-red-100 rounded-lg">
                <Flag className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">举报用户</h3>
                <p className="text-sm text-gray-500">举报 {targetUserName}</p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="h-5 w-5 text-gray-500" />
            </button>
          </div>

          {/* 举报原因 */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">请选择举报原因：</h4>
            <div className="space-y-2">
              {USER_REPORT_REASONS.map((reason) => (
                <label
                  key={reason.id}
                  className={`flex items-start space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedReason === reason.id
                      ? 'border-red-500 bg-red-50'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <input
                    type="radio"
                    name="reason"
                    value={reason.id}
                    checked={selectedReason === reason.id}
                    onChange={() => setSelectedReason(reason.id)}
                    className="mt-1 text-red-600 focus:ring-red-500"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{reason.label}</div>
                    <div className="text-sm text-gray-500">{reason.description}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* 特别提醒 */}
          {selectedReason === 1 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-sm text-red-800 font-medium">
                ⚠️ 非法野生动物交易是违法行为，我们将严肃处理此类举报。
              </p>
            </div>
          )}

          {/* 按钮 */}
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleClose}
              className="flex-1"
              disabled={loading}
            >
              取消
            </Button>
            <Button
              variant="danger"
              onClick={handleSubmit}
              loading={loading}
              className="flex-1"
              disabled={selectedReason === 0}
            >
              提交举报
            </Button>
          </div>
        </div>
      </ModalBody>
    </Modal>
  );
};

export default UserReportModal;
