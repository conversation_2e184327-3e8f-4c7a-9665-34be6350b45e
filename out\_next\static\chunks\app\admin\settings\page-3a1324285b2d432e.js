(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[140],{12324:function(e,s,a){Promise.resolve().then(a.bind(a,51842))},32660:function(e,s,a){"use strict";a.d(s,{Z:function(){return r}});let r=(0,a(39763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},53113:function(e,s,a){"use strict";a.d(s,{Z:function(){return r}});let r=(0,a(39763).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},82431:function(e,s,a){"use strict";a.d(s,{Z:function(){return r}});let r=(0,a(39763).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},83229:function(e,s,a){"use strict";a.d(s,{Z:function(){return r}});let r=(0,a(39763).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},98728:function(e,s,a){"use strict";a.d(s,{Z:function(){return r}});let r=(0,a(39763).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},99376:function(e,s,a){"use strict";var r=a(35475);a.o(r,"useParams")&&a.d(s,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(s,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(s,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(s,{useSearchParams:function(){return r.useSearchParams}})},51842:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return u}});var r=a(57437),t=a(2265),n=a(99376),i=a(9356),l=a(56334),c=a(98728),o=a(32660),m=a(53113),d=a(82431),x=a(83229);function u(){let e=(0,n.useRouter)(),[s,a]=(0,t.useState)(!0),[u,g]=(0,t.useState)(!1),[p,h]=(0,t.useState)({maxImageSize:5,maxImagesPerPost:9,allowedImageTypes:["image/jpeg","image/png","image/webp"]});(0,t.useEffect)(()=>{let s=localStorage.getItem("adminToken"),a=localStorage.getItem("adminUser");if(!s||!a){i.C.error("请先登录管理员账号"),e.push("/admin");return}y()},[e]);let y=async()=>{try{a(!0);let e=localStorage.getItem("systemSettings");e&&h(JSON.parse(e))}catch(e){console.error("加载设置失败:",e),i.C.error("加载设置失败")}finally{a(!1)}},f=async()=>{try{if(g(!0),p.maxImageSize<=0||p.maxImageSize>100){i.C.error("图片大小限制必须在1-100MB之间");return}if(p.maxImagesPerPost<=0||p.maxImagesPerPost>20){i.C.error("每帖图片数量必须在1-20张之间");return}localStorage.setItem("systemSettings",JSON.stringify(p)),i.C.success("设置保存成功")}catch(e){console.error("保存设置失败:",e),i.C.error("保存设置失败")}finally{g(!1)}};return s?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(c.Z,{className:"w-16 h-16 text-blue-600 mx-auto mb-4 animate-pulse"}),(0,r.jsx)("p",{className:"text-gray-600",children:"加载设置中..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"flex items-center justify-between h-16",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("button",{onClick:()=>e.push("/admin/dashboard"),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900",children:[(0,r.jsx)(o.Z,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"返回控制台"})]}),(0,r.jsx)("div",{className:"h-6 w-px bg-gray-300"}),(0,r.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"系统设置"})]})})})}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,r.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(m.Z,{className:"w-6 h-6 text-blue-600"}),(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"图片上传设置"})]}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"配置用户上传图片的限制和规则"})]}),(0,r.jsxs)("div",{className:"px-6 py-6 space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"单张图片大小限制 (MB)"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("input",{type:"number",min:"1",max:"100",step:"1",value:p.maxImageSize,onChange:e=>h(s=>({...s,maxImageSize:parseInt(e.target.value)||1})),className:"block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:["当前限制：",p.maxImageSize,"MB"]})]}),(0,r.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"建议设置在5-30MB之间，过大会影响上传速度"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"每帖最大图片数量"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("input",{type:"number",min:"1",max:"20",step:"1",value:p.maxImagesPerPost,onChange:e=>h(s=>({...s,maxImagesPerPost:parseInt(e.target.value)||1})),className:"block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:["当前限制：",p.maxImagesPerPost,"张"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"支持的图片格式"}),(0,r.jsx)("div",{className:"space-y-2",children:["image/jpeg","image/png","image/webp","image/gif"].map(e=>(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:p.allowedImageTypes.includes(e),onChange:s=>{s.target.checked?h(s=>({...s,allowedImageTypes:[...s.allowedImageTypes,e]})):h(s=>({...s,allowedImageTypes:s.allowedImageTypes.filter(s=>s!==e)}))},className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:e.replace("image/","").toUpperCase()})]},e))})]})]}),(0,r.jsxs)("div",{className:"px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between",children:[(0,r.jsxs)(l.Z,{variant:"outline",onClick:()=>{confirm("确定要重置为默认设置吗？")&&h({maxImageSize:5,maxImagesPerPost:9,allowedImageTypes:["image/jpeg","image/png","image/webp"]})},className:"flex items-center space-x-2",children:[(0,r.jsx)(d.Z,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"重置默认"})]}),(0,r.jsxs)(l.Z,{onClick:f,loading:u,disabled:u,className:"flex items-center space-x-2",children:[(0,r.jsx)(x.Z,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:u?"保存中...":"保存设置"})]})]})]})})]})}},56334:function(e,s,a){"use strict";var r=a(57437),t=a(2265),n=a(68661);let i=t.forwardRef((e,s)=>{let{className:a,variant:t="primary",size:i="md",loading:l=!1,icon:c,children:o,disabled:m,...d}=e;return(0,r.jsxs)("button",{className:(0,n.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[t],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[i],a),ref:s,disabled:m||l,...d,children:[l&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!l&&c&&(0,r.jsx)("span",{className:"mr-2",children:c}),o]})});i.displayName="Button",s.Z=i},9356:function(e,s,a){"use strict";a.d(s,{C:function(){return u},ToastProvider:function(){return g}});var r=a(57437);a(2265);var t=a(69064),n=a(65302),i=a(45131),l=a(22252),c=a(33245),o=a(32489),m=a(68661);let d={duration:4e3,position:"top-center",style:{background:"#fff",color:"#374151",border:"1px solid #e5e7eb",borderRadius:"8px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",padding:"12px 16px",fontSize:"14px",maxWidth:"400px"}},x=e=>{let{message:s,type:a,onDismiss:t}=e,d={success:(0,r.jsx)(n.Z,{className:"h-5 w-5 text-green-500"}),error:(0,r.jsx)(i.Z,{className:"h-5 w-5 text-red-500"}),warning:(0,r.jsx)(l.Z,{className:"h-5 w-5 text-yellow-500"}),info:(0,r.jsx)(c.Z,{className:"h-5 w-5 text-blue-500"})};return(0,r.jsxs)("div",{className:(0,m.cn)("flex items-center space-x-3 p-3 rounded-lg border shadow-lg",{success:"bg-green-50 border-green-200",error:"bg-red-50 border-red-200",warning:"bg-yellow-50 border-yellow-200",info:"bg-blue-50 border-blue-200"}[a]),children:[d[a],(0,r.jsx)("span",{className:"flex-1 text-sm font-medium",children:s}),(0,r.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(o.Z,{className:"h-4 w-4"})})]})},u={success:e=>{t.Am.custom(s=>(0,r.jsx)(x,{message:e,type:"success",onDismiss:()=>t.Am.dismiss(s.id)}),d)},error:e=>{t.Am.custom(s=>(0,r.jsx)(x,{message:e,type:"error",onDismiss:()=>t.Am.dismiss(s.id)}),{...d,duration:6e3})},warning:e=>{t.Am.custom(s=>(0,r.jsx)(x,{message:e,type:"warning",onDismiss:()=>t.Am.dismiss(s.id)}),d)},info:e=>{t.Am.custom(s=>(0,r.jsx)(x,{message:e,type:"info",onDismiss:()=>t.Am.dismiss(s.id)}),d)},loading:e=>t.Am.loading(e,{style:d.style,position:d.position}),dismiss:e=>{t.Am.dismiss(e)},promise:(e,s)=>t.Am.promise(e,s,{style:d.style,position:d.position})},g=()=>(0,r.jsx)(t.x7,{position:"top-center",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"transparent",boxShadow:"none",padding:0}}})},68661:function(e,s,a){"use strict";a.d(s,{cn:function(){return n},uf:function(){return i},vV:function(){return l}});var r=a(61994),t=a(53335);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,t.m6)((0,r.W)(s))}function i(e){return e<1e3?e.toString():e<1e4?"".concat((e/1e3).toFixed(1),"k"):e<1e5?"".concat((e/1e4).toFixed(1),"w"):"".concat(Math.floor(e/1e4),"w")}function l(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}}},function(e){e.O(0,[554,721,971,117,744],function(){return e(e.s=12324)}),_N_E=e.O()}]);