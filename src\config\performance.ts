// 性能优化配置
export const PERFORMANCE_CONFIG = {
  // 图片优化配置（成本优化版）
  image: {
    // 压缩配置
    compression: {
      quality: 0.75,  // 降低质量以减少文件大小
      maxWidth: 1000, // 减小最大宽度
      maxHeight: 1000,
      outputFormat: 'webp' as const,
      fallbackFormat: 'jpeg' as const,
      aggressiveCompression: true // 启用激进压缩
    },
    
    // 缩略图配置
    thumbnails: {
      enabled: true,
      sizes: [150, 300, 600, 800],
      quality: 0.8
    },
    
    // 懒加载配置
    lazyLoading: {
      rootMargin: '50px',
      threshold: 0.1,
      preloadCount: 3 // 预加载前3张图片
    },
    
    // 预加载配置
    preload: {
      enabled: true,
      maxConcurrent: 3,
      priority: ['cover', 'avatar', 'thumbnail']
    }
  },

  // 缓存配置
  cache: {
    // 内存缓存
    memory: {
      maxSize: 100,
      strategy: 'LRU' as const,
      ttl: {
        posts: 2 * 60 * 1000,      // 2分钟
        users: 10 * 60 * 1000,     // 10分钟
        categories: 30 * 60 * 1000, // 30分钟
        images: 30 * 60 * 1000     // 30分钟
      }
    },
    
    // 浏览器存储缓存
    storage: {
      prefix: 'pet_app_',
      useSessionStorage: false,
      ttl: {
        userPrefs: 7 * 24 * 60 * 60 * 1000,  // 7天
        appConfig: 24 * 60 * 60 * 1000,      // 1天
        tempData: 60 * 60 * 1000             // 1小时
      }
    },
    
    // CDN缓存配置（优化版）
    cdn: {
      maxAge: {
        images: 30 * 24 * 60 * 60,   // 30天（延长图片缓存）
        static: 90 * 24 * 60 * 60,   // 90天（延长静态资源缓存）
        api: 10 * 60,                // 10分钟（延长API缓存）
        thumbnails: 60 * 24 * 60 * 60 // 60天（缩略图长期缓存）
      },
      // 缓存控制策略
      cacheControl: {
        images: 'public, max-age=2592000, immutable',      // 30天不可变
        static: 'public, max-age=7776000, immutable',      // 90天不可变
        api: 'public, max-age=600, stale-while-revalidate=300' // 10分钟+5分钟过期重验证
      }
    }
  },

  // 数据库查询优化
  database: {
    // 分页配置
    pagination: {
      defaultLimit: 10,
      maxLimit: 50,
      preloadNextPage: true
    },
    
    // 批量查询配置
    batch: {
      enabled: true,
      maxBatchSize: 20,
      batchDelay: 50 // 50ms内的查询合并为批量查询
    },
    
    // 查询缓存
    queryCache: {
      enabled: true,
      ttl: 5 * 60 * 1000, // 5分钟
      maxQueries: 50
    }
  },

  // 网络请求优化
  network: {
    // 重试配置
    retry: {
      maxAttempts: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffFactor: 2
    },
    
    // 超时配置
    timeout: {
      api: 10000,      // 10秒
      upload: 30000,   // 30秒
      download: 20000  // 20秒
    },
    
    // 并发控制
    concurrency: {
      maxConcurrentRequests: 6,
      maxConcurrentUploads: 2
    }
  },

  // 性能监控配置
  monitoring: {
    enabled: true,
    
    // Core Web Vitals阈值
    thresholds: {
      lcp: {
        good: 2500,
        needsImprovement: 4000
      },
      fid: {
        good: 100,
        needsImprovement: 300
      },
      cls: {
        good: 0.1,
        needsImprovement: 0.25
      }
    },
    
    // 采样率
    sampleRate: 0.1, // 10%的用户
    
    // 报告配置
    reporting: {
      enabled: false, // 生产环境可开启
      endpoint: '/api/performance',
      batchSize: 10,
      flushInterval: 30000 // 30秒
    }
  },

  // 预加载策略
  preload: {
    // 关键资源预加载
    critical: {
      enabled: true,
      resources: [
        'fonts',
        'critical-css',
        'hero-images'
      ]
    },
    
    // 路由预加载
    routes: {
      enabled: true,
      strategy: 'hover', // 'hover' | 'visible' | 'immediate'
      delay: 100 // hover延迟
    },
    
    // 数据预加载
    data: {
      enabled: true,
      triggers: ['scroll', 'hover', 'idle'],
      prefetchDistance: 2 // 预加载前2页数据
    }
  },

  // 代码分割配置
  codeSplitting: {
    enabled: true,
    
    // 路由级分割
    routes: true,
    
    // 组件级分割
    components: {
      enabled: true,
      threshold: 50000 // 50KB以上的组件进行分割
    },
    
    // 第三方库分割
    vendors: {
      enabled: true,
      chunks: ['react', 'ui', 'utils']
    }
  },

  // 资源优化
  assets: {
    // 压缩配置
    compression: {
      enabled: true,
      gzip: true,
      brotli: true
    },
    
    // 文件大小限制
    limits: {
      image: 5 * 1024 * 1024,    // 5MB
      video: 50 * 1024 * 1024,   // 50MB
      document: 10 * 1024 * 1024 // 10MB
    },
    
    // 格式转换
    conversion: {
      images: {
        webp: true,
        avif: false, // 暂不支持
        progressive: true
      }
    }
  }
};

// 环境相关配置
export const getEnvironmentConfig = () => {
  const isDev = process.env.NODE_ENV === 'development';
  const isProd = process.env.NODE_ENV === 'production';

  return {
    // 开发环境配置
    development: {
      cache: {
        ...PERFORMANCE_CONFIG.cache,
        memory: {
          ...PERFORMANCE_CONFIG.cache.memory,
          ttl: {
            posts: 30 * 1000,      // 30秒（开发时更短）
            users: 60 * 1000,      // 1分钟
            categories: 5 * 60 * 1000, // 5分钟
            images: 5 * 60 * 1000   // 5分钟
          }
        }
      },
      monitoring: {
        ...PERFORMANCE_CONFIG.monitoring,
        sampleRate: 1.0 // 开发环境100%采样
      }
    },

    // 生产环境配置
    production: {
      cache: PERFORMANCE_CONFIG.cache,
      monitoring: {
        ...PERFORMANCE_CONFIG.monitoring,
        reporting: {
          ...PERFORMANCE_CONFIG.monitoring.reporting,
          enabled: true
        }
      }
    }
  };
};

// 获取当前环境配置
export const getCurrentConfig = () => {
  const envConfig = getEnvironmentConfig();
  const isDev = process.env.NODE_ENV === 'development';
  
  return {
    ...PERFORMANCE_CONFIG,
    ...(isDev ? envConfig.development : envConfig.production)
  };
};

// 性能预算配置
export const PERFORMANCE_BUDGET = {
  // 页面大小预算
  pageSize: {
    html: 50 * 1024,      // 50KB
    css: 100 * 1024,      // 100KB
    js: 500 * 1024,       // 500KB
    images: 1024 * 1024,  // 1MB
    total: 2 * 1024 * 1024 // 2MB
  },
  
  // 性能指标预算
  metrics: {
    fcp: 1500,    // 1.5秒
    lcp: 2500,    // 2.5秒
    fid: 100,     // 100ms
    cls: 0.1,     // 0.1
    ttfb: 600     // 600ms
  },
  
  // 资源数量预算
  resources: {
    requests: 50,
    images: 20,
    scripts: 10,
    stylesheets: 5
  }
};

export default PERFORMANCE_CONFIG;
