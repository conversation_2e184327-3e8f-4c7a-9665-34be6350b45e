exports.id=145,exports.ids=[145],exports.modules={53515:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AbstractSDKRequest:()=>o,AbstractStorage:()=>i,StorageType:()=>n,formatUrl:()=>s}),function(e){e.local="local",e.none="none",e.session="session"}(n||(n={}));var n,o=function(){},i=function(){};function s(e,t,r){void 0===r&&(r={});var n=/\?/.test(t),o="";for(var i in r)""===o?n||(t+="?"):o+="&",o+=i+"="+encodeURIComponent(r[i]);return(t+=o,/^http(s)?\:\/\//.test(t))?t:""+e+t}},71492:(e,t,r)=>{"use strict";let n;if(r.r(t),r.d(t,{getEncryptInfo:()=>F}),!globalThis.IS_MP_BUILD){let e="undefined"!=typeof globalThis?globalThis.navigator:window.globalThis;function o(e){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(e)}function i(e,t){return e&t}function s(e,t){return e|t}function a(e,t){return e^t}function u(e,t){return e&~t}var c,l,f,h,d,p,y,v="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function m(e){var t,r,n="";for(t=0;t+3<=e.length;t+=3)r=parseInt(e.substring(t,t+3),16),n+=v.charAt(r>>6)+v.charAt(63&r);for(t+1==e.length?(r=parseInt(e.substring(t,t+1),16),n+=v.charAt(r<<2)):t+2==e.length&&(r=parseInt(e.substring(t,t+2),16),n+=v.charAt(r>>2)+v.charAt((3&r)<<4));(3&n.length)>0;)n+="=";return n}function g(e){var t,r="",n=0,i=0;for(t=0;t<e.length&&"="!=e.charAt(t);++t){var s=v.indexOf(e.charAt(t));s<0||(0==n?(r+=o(s>>2),i=3&s,n=1):1==n?(r+=o(i<<2|s>>4),i=15&s,n=2):2==n?(r+=o(i)+o(s>>2),i=3&s,n=3):(r+=o(i<<2|s>>4)+o(15&s),n=0))}return 1==n&&(r+=o(i<<2)),r}var b={decode:function(e){if(void 0===c){var t,r="0123456789ABCDEF",n=" \f\n\r	\xa0\u2028\u2029";for(t=0,c={};t<16;++t)c[r.charAt(t)]=t;for(t=10,r=r.toLowerCase();t<16;++t)c[r.charAt(t)]=t;for(t=0;t<n.length;++t)c[n.charAt(t)]=-1}var o=[],i=0,s=0;for(t=0;t<e.length;++t){var a=e.charAt(t);if("="==a)break;if(-1!=(a=c[a])){if(void 0===a)throw Error("Illegal character at offset "+t);i|=a,++s>=2?(o[o.length]=i,i=0,s=0):i<<=4}}if(s)throw Error("Hex encoding incomplete: 4 bits missing");return o}},_={decode:function(e){if(void 0===l){var t,r="= \f\n\r	\xa0\u2028\u2029";for(t=0,l=Object.create(null);t<64;++t)l["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t)]=t;for(t=0;t<r.length;++t)l[r.charAt(t)]=-1}var n=[],o=0,i=0;for(t=0;t<e.length;++t){var s=e.charAt(t);if("="==s)break;if(-1!=(s=l[s])){if(void 0===s)throw Error("Illegal character at offset "+t);o|=s,++i>=4?(n[n.length]=o>>16,n[n.length]=o>>8&255,n[n.length]=255&o,o=0,i=0):o<<=6}}switch(i){case 1:throw Error("Base64 encoding incomplete: at least 2 bits missing");case 2:n[n.length]=o>>10;break;case 3:n[n.length]=o>>16,n[n.length]=o>>8&255}return n},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(e){var t=_.re.exec(e);if(t){if(t[1])e=t[1];else if(t[2])e=t[2];else throw Error("RegExp out of sync")}return _.decode(e)}};class t{constructor(e){this.buf=[+e||0]}mulAdd(e,t){var r,n,o=this.buf,i=o.length;for(r=0;r<i;++r)(n=o[r]*e+t)<1e13?t=0:(t=0|n/1e13,n-=1e13*t),o[r]=n;t>0&&(o[r]=t)}sub(e){var t,r,n=this.buf,o=n.length;for(t=0;t<o;++t)(r=n[t]-e)<0?(r+=1e13,e=1):e=0,n[t]=r;for(;0===n[n.length-1];)n.pop()}toString(e){if(10!=(e||10))throw Error("only base 10 is supported");for(var t=this.buf,r=t[t.length-1].toString(),n=t.length-2;n>=0;--n)r+=(1e13+t[n]).toString().substring(1);return r}valueOf(){for(var e=this.buf,t=0,r=e.length-1;r>=0;--r)t=1e13*t+e[r];return t}simplify(){var e=this.buf;return 1==e.length?e[0]:this}}var w=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,S=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function E(e,t){return e.length>t&&(e=e.substring(0,t)+"…"),e}class r{constructor(e,t){this.hexDigits="0123456789ABCDEF",e instanceof r?(this.enc=e.enc,this.pos=e.pos):(this.enc=e,this.pos=t)}get(e){if(void 0===e&&(e=this.pos++),e>=this.enc.length)throw Error("Requesting byte offset "+e+" on a stream of length "+this.enc.length);return"string"==typeof this.enc?this.enc.charCodeAt(e):this.enc[e]}hexByte(e){return this.hexDigits.charAt(e>>4&15)+this.hexDigits.charAt(15&e)}hexDump(e,t,r){for(var n="",o=e;o<t;++o)if(n+=this.hexByte(this.get(o)),!0!==r)switch(15&o){case 7:n+="  ";break;case 15:n+="\n";break;default:n+=" "}return n}isASCII(e,t){for(var r=e;r<t;++r){var n=this.get(r);if(n<32||n>176)return!1}return!0}parseStringISO(e,t){for(var r="",n=e;n<t;++n)r+=String.fromCharCode(this.get(n));return r}parseStringUTF(e,t){for(var r="",n=e;n<t;){var o=this.get(n++);o<128?r+=String.fromCharCode(o):o>191&&o<224?r+=String.fromCharCode((31&o)<<6|63&this.get(n++)):r+=String.fromCharCode((15&o)<<12|(63&this.get(n++))<<6|63&this.get(n++))}return r}parseStringBMP(e,t){for(var r="",n=e;n<t;)r+=String.fromCharCode(this.get(n++)<<8|this.get(n++));return r}parseTime(e,t,r){var n=this.parseStringISO(e,t),o=(r?w:S).exec(n);return o?(r&&(o[1]=+o[1],o[1]+=70>+o[1]?2e3:1900),n=o[1]+"-"+o[2]+"-"+o[3]+" "+o[4],o[5]&&(n+=":"+o[5],o[6]&&(n+=":"+o[6],o[7]&&(n+="."+o[7]))),o[8]&&(n+=" UTC","Z"!=o[8]&&(n+=o[8],o[9]&&(n+=":"+o[9]))),n):"Unrecognized time: "+n}parseInteger(e,r){for(var n,o=this.get(e),i=o>127,s=i?255:0,a="";o==s&&++e<r;)o=this.get(e);if(0==(n=r-e))return i?-1:0;if(n>4){for(a=o,n<<=3;((+a^s)&128)==0;)a=+a<<1,--n;a="("+n+" bit)\n"}i&&(o-=256);for(var u=new t(o),c=e+1;c<r;++c)u.mulAdd(256,this.get(c));return a+u.toString()}parseBitString(e,t,r){for(var n=this.get(e),o="("+((t-e-1<<3)-n)+" bit)\n",i="",s=e+1;s<t;++s){for(var a=this.get(s),u=s==t-1?n:0,c=7;c>=u;--c)i+=a>>c&1?"1":"0";if(i.length>r)return o+E(i,r)}return o+i}parseOctetString(e,t,r){if(this.isASCII(e,t))return E(this.parseStringISO(e,t),r);var n=t-e,o="("+n+" byte)\n";n>(r/=2)&&(t=e+r);for(var i=e;i<t;++i)o+=this.hexByte(this.get(i));return n>r&&(o+="…"),o}parseOID(e,r,n){for(var o="",i=new t,s=0,a=e;a<r;++a){var u=this.get(a);if(i.mulAdd(128,127&u),s+=7,!(128&u)){if(""===o){if((i=i.simplify())instanceof t)i.sub(80),o="2."+i.toString();else{var c=i<80?i<40?0:1:2;o=c+"."+(i-40*c)}}else o+="."+i.toString();if(o.length>n)return E(o,n);i=new t,s=0}}return s>0&&(o+=".incomplete"),o}}class B{constructor(e,t,r,n,o){if(!(n instanceof W))throw Error("Invalid tag value.");this.stream=e,this.header=t,this.length=r,this.tag=n,this.sub=o}typeName(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}}content(e){if(void 0===this.tag)return null;void 0===e&&(e=1/0);var t=this.posContent(),r=Math.abs(this.length);if(!this.tag.isUniversal())return null!==this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(t,t+r,e);switch(this.tag.tagNumber){case 1:return 0===this.stream.get(t)?"false":"true";case 2:return this.stream.parseInteger(t,t+r);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(t,t+r,e);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(t,t+r,e);case 6:return this.stream.parseOID(t,t+r,e);case 16:case 17:if(null!==this.sub)return"("+this.sub.length+" elem)";return"(no elem)";case 12:return E(this.stream.parseStringUTF(t,t+r),e);case 18:case 19:case 20:case 21:case 22:case 26:return E(this.stream.parseStringISO(t,t+r),e);case 30:return E(this.stream.parseStringBMP(t,t+r),e);case 23:case 24:return this.stream.parseTime(t,t+r,23==this.tag.tagNumber)}return null}toString(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(null===this.sub?"null":this.sub.length)+"]"}toPrettyString(e){void 0===e&&(e="");var t=e+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(t+="+"),t+=this.length,this.tag.tagConstructed?t+=" (constructed)":this.tag.isUniversal()&&(3==this.tag.tagNumber||4==this.tag.tagNumber)&&null!==this.sub&&(t+=" (encapsulates)"),t+="\n",null!==this.sub){e+="  ";for(var r=0,n=this.sub.length;r<n;++r)t+=this.sub[r].toPrettyString(e)}return t}posStart(){return this.stream.pos}posContent(){return this.stream.pos+this.header}posEnd(){return this.stream.pos+this.header+Math.abs(this.length)}toHexString(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)}static decodeLength(e){var t=e.get(),r=127&t;if(r==t)return r;if(r>6)throw Error("Length over 48 bits not supported at position "+(e.pos-1));if(0===r)return null;t=0;for(var n=0;n<r;++n)t=256*t+e.get();return t}getHexStringValue(){var e=this.toHexString(),t=2*this.header,r=2*this.length;return e.substr(t,r)}static decode(e){t=e instanceof r?e:new r(e,0);var t,n=new r(t),o=new W(t),i=B.decodeLength(t),s=t.pos,a=s-n.pos,u=null,c=function(){var e=[];if(null!==i){for(var r=s+i;t.pos<r;)e[e.length]=B.decode(t);if(t.pos!=r)throw Error("Content size is not correct for container starting at offset "+s)}else try{for(;;){var n=B.decode(t);if(n.tag.isEOC())break;e[e.length]=n}i=s-t.pos}catch(e){throw Error("Exception while decoding undefined length content: "+e)}return e};if(o.tagConstructed)u=c();else if(o.isUniversal()&&(3==o.tagNumber||4==o.tagNumber))try{if(3==o.tagNumber&&0!=t.get())throw Error("BIT STRINGs with unused bits cannot encapsulate.");u=c();for(var l=0;l<u.length;++l)if(u[l].tag.isEOC())throw Error("EOC is not supposed to be actual content.")}catch(e){u=null}if(null===u){if(null===i)throw Error("We can't skip over an invalid tag with undefined length at offset "+s);t.pos=s+Math.abs(i)}return new B(n,a,i,o,u)}}class W{constructor(e){var r=e.get();if(this.tagClass=r>>6,this.tagConstructed=(32&r)!=0,this.tagNumber=31&r,31==this.tagNumber){var n=new t;do r=e.get(),n.mulAdd(128,127&r);while(128&r);this.tagNumber=n.simplify()}}isUniversal(){return 0===this.tagClass}isEOC(){return 0===this.tagClass&&0===this.tagNumber}}var I=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],O=67108864/I[I.length-1];class F{constructor(e,t,r){null!=e&&("number"==typeof e?this.fromNumber(e,t,r):null==t&&"string"!=typeof e?this.fromString(e,256):this.fromString(e,t))}toString(e){if(this.s<0)return"-"+this.negate().toString(e);if(16==e)t=4;else if(8==e)t=3;else if(2==e)t=1;else if(32==e)t=5;else{if(4!=e)return this.toRadix(e);t=2}var t,r,n=(1<<t)-1,i=!1,s="",a=this.t,u=this.DB-a*this.DB%t;if(a-- >0)for(u<this.DB&&(r=this[a]>>u)>0&&(i=!0,s=o(r));a>=0;)u<t?r=(this[a]&(1<<u)-1)<<t-u|this[--a]>>(u+=this.DB-t):(r=this[a]>>(u-=t)&n,u<=0&&(u+=this.DB,--a)),r>0&&(i=!0),i&&(s+=o(r));return i?s:"0"}negate(){var e=x();return F.ZERO.subTo(this,e),e}abs(){return this.s<0?this.negate():this}compareTo(e){var t=this.s-e.s;if(0!=t)return t;var r=this.t;if(0!=(t=r-e.t))return this.s<0?-t:t;for(;--r>=0;)if(0!=(t=this[r]-e[r]))return t;return 0}bitLength(){return this.t<=0?0:this.DB*(this.t-1)+U(this[this.t-1]^this.s&this.DM)}mod(e){var t=x();return this.abs().divRemTo(e,null,t),this.s<0&&t.compareTo(F.ZERO)>0&&e.subTo(t,t),t}modPowInt(e,t){var r;return r=e<256||t.isEven()?new R(t):new A(t),this.exp(e,r)}clone(){var e=x();return this.copyTo(e),e}intValue(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return -1}else if(1==this.t)return this[0];else if(0==this.t)return 0;return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}byteValue(){return 0==this.t?this.s:this[0]<<24>>24}shortValue(){return 0==this.t?this.s:this[0]<<16>>16}signum(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1}toByteArray(){var e,t=this.t,r=[];r[0]=this.s;var n=this.DB-t*this.DB%8,o=0;if(t-- >0)for(n<this.DB&&(e=this[t]>>n)!=(this.s&this.DM)>>n&&(r[o++]=e|this.s<<this.DB-n);t>=0;)n<8?e=(this[t]&(1<<n)-1)<<8-n|this[--t]>>(n+=this.DB-8):(e=this[t]>>(n-=8)&255,n<=0&&(n+=this.DB,--t)),(128&e)!=0&&(e|=-256),0==o&&(128&this.s)!=(128&e)&&++o,(o>0||e!=this.s)&&(r[o++]=e);return r}equals(e){return 0==this.compareTo(e)}min(e){return 0>this.compareTo(e)?this:e}max(e){return this.compareTo(e)>0?this:e}and(e){var t=x();return this.bitwiseTo(e,i,t),t}or(e){var t=x();return this.bitwiseTo(e,s,t),t}xor(e){var t=x();return this.bitwiseTo(e,a,t),t}andNot(e){var t=x();return this.bitwiseTo(e,u,t),t}not(){for(var e=x(),t=0;t<this.t;++t)e[t]=this.DM&~this[t];return e.t=this.t,e.s=~this.s,e}shiftLeft(e){var t=x();return e<0?this.rShiftTo(-e,t):this.lShiftTo(e,t),t}shiftRight(e){var t=x();return e<0?this.lShiftTo(-e,t):this.rShiftTo(e,t),t}getLowestSetBit(){for(var e=0;e<this.t;++e)if(0!=this[e])return e*this.DB+function(e){if(0==e)return -1;var t=0;return(65535&e)==0&&(e>>=16,t+=16),(255&e)==0&&(e>>=8,t+=8),(15&e)==0&&(e>>=4,t+=4),(3&e)==0&&(e>>=2,t+=2),(1&e)==0&&++t,t}(this[e]);return this.s<0?this.t*this.DB:-1}bitCount(){for(var e=0,t=this.s&this.DM,r=0;r<this.t;++r)e+=function(e){for(var t=0;0!=e;)e&=e-1,++t;return t}(this[r]^t);return e}testBit(e){var t=Math.floor(e/this.DB);return t>=this.t?0!=this.s:(this[t]&1<<e%this.DB)!=0}setBit(e){return this.changeBit(e,s)}clearBit(e){return this.changeBit(e,u)}flipBit(e){return this.changeBit(e,a)}add(e){var t=x();return this.addTo(e,t),t}subtract(e){var t=x();return this.subTo(e,t),t}multiply(e){var t=x();return this.multiplyTo(e,t),t}divide(e){var t=x();return this.divRemTo(e,t,null),t}remainder(e){var t=x();return this.divRemTo(e,null,t),t}divideAndRemainder(e){var t=x(),r=x();return this.divRemTo(e,t,r),[t,r]}modPow(e,t){var r,n,o,i,s=e.bitLength(),a=j(1);if(s<=0)return a;r=s<18?1:s<48?3:s<144?4:s<768?5:6,n=s<8?new R(t):t.isEven()?new P(t):new A(t);var u=[],c=3,l=r-1,f=(1<<r)-1;if(u[1]=n.convert(this),r>1){var h=x();for(n.sqrTo(u[1],h);c<=f;)u[c]=x(),n.mulTo(h,u[c-2],u[c]),c+=2}var d=e.t-1,p=!0,y=x();for(s=U(e[d])-1;d>=0;){for(s>=l?o=e[d]>>s-l&f:(o=(e[d]&(1<<s+1)-1)<<l-s,d>0&&(o|=e[d-1]>>this.DB+s-l)),c=r;(1&o)==0;)o>>=1,--c;if((s-=c)<0&&(s+=this.DB,--d),p)u[o].copyTo(a),p=!1;else{for(;c>1;)n.sqrTo(a,y),n.sqrTo(y,a),c-=2;c>0?n.sqrTo(a,y):(i=a,a=y,y=i),n.mulTo(y,u[o],a)}for(;d>=0&&(e[d]&1<<s)==0;)n.sqrTo(a,y),i=a,a=y,y=i,--s<0&&(s=this.DB-1,--d)}return n.revert(a)}modInverse(e){var t=e.isEven();if(this.isEven()&&t||0==e.signum())return F.ZERO;for(var r=e.clone(),n=this.clone(),o=j(1),i=j(0),s=j(0),a=j(1);0!=r.signum();){for(;r.isEven();)r.rShiftTo(1,r),t?(o.isEven()&&i.isEven()||(o.addTo(this,o),i.subTo(e,i)),o.rShiftTo(1,o)):i.isEven()||i.subTo(e,i),i.rShiftTo(1,i);for(;n.isEven();)n.rShiftTo(1,n),t?(s.isEven()&&a.isEven()||(s.addTo(this,s),a.subTo(e,a)),s.rShiftTo(1,s)):a.isEven()||a.subTo(e,a),a.rShiftTo(1,a);r.compareTo(n)>=0?(r.subTo(n,r),t&&o.subTo(s,o),i.subTo(a,i)):(n.subTo(r,n),t&&s.subTo(o,s),a.subTo(i,a))}return 0!=n.compareTo(F.ONE)?F.ZERO:a.compareTo(e)>=0?a.subtract(e):0>a.signum()&&(a.addTo(e,a),0>a.signum())?a.add(e):a}pow(e){return this.exp(e,new T)}gcd(e){var t=this.s<0?this.negate():this.clone(),r=e.s<0?e.negate():e.clone();if(0>t.compareTo(r)){var n=t;t=r,r=n}var o=t.getLowestSetBit(),i=r.getLowestSetBit();if(i<0)return t;for(o<i&&(i=o),i>0&&(t.rShiftTo(i,t),r.rShiftTo(i,r));t.signum()>0;)(o=t.getLowestSetBit())>0&&t.rShiftTo(o,t),(o=r.getLowestSetBit())>0&&r.rShiftTo(o,r),t.compareTo(r)>=0?(t.subTo(r,t),t.rShiftTo(1,t)):(r.subTo(t,r),r.rShiftTo(1,r));return i>0&&r.lShiftTo(i,r),r}isProbablePrime(e){var t,r=this.abs();if(1==r.t&&r[0]<=I[I.length-1]){for(t=0;t<I.length;++t)if(r[0]==I[t])return!0;return!1}if(r.isEven())return!1;for(t=1;t<I.length;){for(var n=I[t],o=t+1;o<I.length&&n<O;)n*=I[o++];for(n=r.modInt(n);t<o;)if(n%I[t++]==0)return!1}return r.millerRabin(e)}copyTo(e){for(var t=this.t-1;t>=0;--t)e[t]=this[t];e.t=this.t,e.s=this.s}fromInt(e){this.t=1,this.s=e<0?-1:0,e>0?this[0]=e:e<-1?this[0]=e+this.DV:this.t=0}fromString(e,t){if(16==t)r=4;else if(8==t)r=3;else if(256==t)r=8;else if(2==t)r=1;else if(32==t)r=5;else if(4==t)r=2;else{this.fromRadix(e,t);return}this.t=0,this.s=0;for(var r,n=e.length,o=!1,i=0;--n>=0;){var s=8==r?255&+e[n]:L(e,n);if(s<0){"-"==e.charAt(n)&&(o=!0);continue}o=!1,0==i?this[this.t++]=s:i+r>this.DB?(this[this.t-1]|=(s&(1<<this.DB-i)-1)<<i,this[this.t++]=s>>this.DB-i):this[this.t-1]|=s<<i,(i+=r)>=this.DB&&(i-=this.DB)}8==r&&(128&+e[0])!=0&&(this.s=-1,i>0&&(this[this.t-1]|=(1<<this.DB-i)-1<<i)),this.clamp(),o&&F.ZERO.subTo(this,this)}clamp(){for(var e=this.s&this.DM;this.t>0&&this[this.t-1]==e;)--this.t}dlShiftTo(e,t){var r;for(r=this.t-1;r>=0;--r)t[r+e]=this[r];for(r=e-1;r>=0;--r)t[r]=0;t.t=this.t+e,t.s=this.s}drShiftTo(e,t){for(var r=e;r<this.t;++r)t[r-e]=this[r];t.t=Math.max(this.t-e,0),t.s=this.s}lShiftTo(e,t){for(var r=e%this.DB,n=this.DB-r,o=(1<<n)-1,i=Math.floor(e/this.DB),s=this.s<<r&this.DM,a=this.t-1;a>=0;--a)t[a+i+1]=this[a]>>n|s,s=(this[a]&o)<<r;for(var a=i-1;a>=0;--a)t[a]=0;t[i]=s,t.t=this.t+i+1,t.s=this.s,t.clamp()}rShiftTo(e,t){t.s=this.s;var r=Math.floor(e/this.DB);if(r>=this.t){t.t=0;return}var n=e%this.DB,o=this.DB-n,i=(1<<n)-1;t[0]=this[r]>>n;for(var s=r+1;s<this.t;++s)t[s-r-1]|=(this[s]&i)<<o,t[s-r]=this[s]>>n;n>0&&(t[this.t-r-1]|=(this.s&i)<<o),t.t=this.t-r,t.clamp()}subTo(e,t){for(var r=0,n=0,o=Math.min(e.t,this.t);r<o;)n+=this[r]-e[r],t[r++]=n&this.DM,n>>=this.DB;if(e.t<this.t){for(n-=e.s;r<this.t;)n+=this[r],t[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<e.t;)n-=e[r],t[r++]=n&this.DM,n>>=this.DB;n-=e.s}t.s=n<0?-1:0,n<-1?t[r++]=this.DV+n:n>0&&(t[r++]=n),t.t=r,t.clamp()}multiplyTo(e,t){var r=this.abs(),n=e.abs(),o=r.t;for(t.t=o+n.t;--o>=0;)t[o]=0;for(o=0;o<n.t;++o)t[o+r.t]=r.am(0,n[o],t,o,0,r.t);t.s=0,t.clamp(),this.s!=e.s&&F.ZERO.subTo(t,t)}squareTo(e){for(var t=this.abs(),r=e.t=2*t.t;--r>=0;)e[r]=0;for(r=0;r<t.t-1;++r){var n=t.am(r,t[r],e,2*r,0,1);(e[r+t.t]+=t.am(r+1,2*t[r],e,2*r+1,n,t.t-r-1))>=t.DV&&(e[r+t.t]-=t.DV,e[r+t.t+1]=1)}e.t>0&&(e[e.t-1]+=t.am(r,t[r],e,2*r,0,1)),e.s=0,e.clamp()}divRemTo(e,t,r){var n=e.abs();if(!(n.t<=0)){var o=this.abs();if(o.t<n.t){null!=t&&t.fromInt(0),null!=r&&this.copyTo(r);return}null==r&&(r=x());var i=x(),s=this.s,a=e.s,u=this.DB-U(n[n.t-1]);u>0?(n.lShiftTo(u,i),o.lShiftTo(u,r)):(n.copyTo(i),o.copyTo(r));var c=i.t,l=i[c-1];if(0!=l){var f=l*(1<<this.F1)+(c>1?i[c-2]>>this.F2:0),h=this.FV/f,d=(1<<this.F1)/f,p=1<<this.F2,y=r.t,v=y-c,m=null==t?x():t;for(i.dlShiftTo(v,m),r.compareTo(m)>=0&&(r[r.t++]=1,r.subTo(m,r)),F.ONE.dlShiftTo(c,m),m.subTo(i,i);i.t<c;)i[i.t++]=0;for(;--v>=0;){var g=r[--y]==l?this.DM:Math.floor(r[y]*h+(r[y-1]+p)*d);if((r[y]+=i.am(0,g,r,v,0,c))<g)for(i.dlShiftTo(v,m),r.subTo(m,r);r[y]<--g;)r.subTo(m,r)}null!=t&&(r.drShiftTo(c,t),s!=a&&F.ZERO.subTo(t,t)),r.t=c,r.clamp(),u>0&&r.rShiftTo(u,r),s<0&&F.ZERO.subTo(r,r)}}}invDigit(){if(this.t<1)return 0;var e=this[0];if((1&e)==0)return 0;var t=3&e;return(t=(t=(t=(t=t*(2-(15&e)*t)&15)*(2-(255&e)*t)&255)*(2-((65535&e)*t&65535))&65535)*(2-e*t%this.DV)%this.DV)>0?this.DV-t:-t}isEven(){return(this.t>0?1&this[0]:this.s)==0}exp(e,t){if(e>4294967295||e<1)return F.ONE;var r=x(),n=x(),o=t.convert(this),i=U(e)-1;for(o.copyTo(r);--i>=0;)if(t.sqrTo(r,n),(e&1<<i)>0)t.mulTo(n,o,r);else{var s=r;r=n,n=s}return t.revert(r)}chunkSize(e){return Math.floor(Math.LN2*this.DB/Math.log(e))}toRadix(e){if(null==e&&(e=10),0==this.signum()||e<2||e>36)return"0";var t=this.chunkSize(e),r=Math.pow(e,t),n=j(r),o=x(),i=x(),s="";for(this.divRemTo(n,o,i);o.signum()>0;)s=(r+i.intValue()).toString(e).substr(1)+s,o.divRemTo(n,o,i);return i.intValue().toString(e)+s}fromRadix(e,t){this.fromInt(0),null==t&&(t=10);for(var r=this.chunkSize(t),n=Math.pow(t,r),o=!1,i=0,s=0,a=0;a<e.length;++a){var u=L(e,a);if(u<0){"-"==e.charAt(a)&&0==this.signum()&&(o=!0);continue}s=t*s+u,++i>=r&&(this.dMultiply(n),this.dAddOffset(s,0),i=0,s=0)}i>0&&(this.dMultiply(Math.pow(t,i)),this.dAddOffset(s,0)),o&&F.ZERO.subTo(this,this)}fromNumber(e,t,r){if("number"==typeof t){if(e<2)this.fromInt(1);else for(this.fromNumber(e,r),this.testBit(e-1)||this.bitwiseTo(F.ONE.shiftLeft(e-1),s,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(t);)this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(F.ONE.shiftLeft(e-1),this)}else{var n=[],o=7&e;n.length=(e>>3)+1,t.nextBytes(n),o>0?n[0]&=(1<<o)-1:n[0]=0,this.fromString(n,256)}}bitwiseTo(e,t,r){var n,o,i=Math.min(e.t,this.t);for(n=0;n<i;++n)r[n]=t(this[n],e[n]);if(e.t<this.t){for(o=e.s&this.DM,n=i;n<this.t;++n)r[n]=t(this[n],o);r.t=this.t}else{for(o=this.s&this.DM,n=i;n<e.t;++n)r[n]=t(o,e[n]);r.t=e.t}r.s=t(this.s,e.s),r.clamp()}changeBit(e,t){var r=F.ONE.shiftLeft(e);return this.bitwiseTo(r,t,r),r}addTo(e,t){for(var r=0,n=0,o=Math.min(e.t,this.t);r<o;)n+=this[r]+e[r],t[r++]=n&this.DM,n>>=this.DB;if(e.t<this.t){for(n+=e.s;r<this.t;)n+=this[r],t[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<e.t;)n+=e[r],t[r++]=n&this.DM,n>>=this.DB;n+=e.s}t.s=n<0?-1:0,n>0?t[r++]=n:n<-1&&(t[r++]=this.DV+n),t.t=r,t.clamp()}dMultiply(e){this[this.t]=this.am(0,e-1,this,0,0,this.t),++this.t,this.clamp()}dAddOffset(e,t){if(0!=e){for(;this.t<=t;)this[this.t++]=0;for(this[t]+=e;this[t]>=this.DV;)this[t]-=this.DV,++t>=this.t&&(this[this.t++]=0),++this[t]}}multiplyLowerTo(e,t,r){var n=Math.min(this.t+e.t,t);for(r.s=0,r.t=n;n>0;)r[--n]=0;for(var o=r.t-this.t;n<o;++n)r[n+this.t]=this.am(0,e[n],r,n,0,this.t);for(var o=Math.min(e.t,t);n<o;++n)this.am(0,e[n],r,n,0,t-n);r.clamp()}multiplyUpperTo(e,t,r){--t;var n=r.t=this.t+e.t-t;for(r.s=0;--n>=0;)r[n]=0;for(n=Math.max(t-this.t,0);n<e.t;++n)r[this.t+n-t]=this.am(t-n,e[n],r,0,0,this.t+n-t);r.clamp(),r.drShiftTo(1,r)}modInt(e){if(e<=0)return 0;var t=this.DV%e,r=this.s<0?e-1:0;if(this.t>0){if(0==t)r=this[0]%e;else for(var n=this.t-1;n>=0;--n)r=(t*r+this[n])%e}return r}millerRabin(e){var t=this.subtract(F.ONE),r=t.getLowestSetBit();if(r<=0)return!1;var n=t.shiftRight(r);(e=e+1>>1)>I.length&&(e=I.length);for(var o=x(),i=0;i<e;++i){o.fromInt(I[Math.floor(Math.random()*I.length)]);var s=o.modPow(n,this);if(0!=s.compareTo(F.ONE)&&0!=s.compareTo(t)){for(var a=1;a++<r&&0!=s.compareTo(t);)if(0==(s=s.modPowInt(2,this)).compareTo(F.ONE))return!1;if(0!=s.compareTo(t))return!1}}return!0}square(){var e=x();return this.squareTo(e),e}gcda(e,t){var r=this.s<0?this.negate():this.clone(),n=e.s<0?e.negate():e.clone();if(0>r.compareTo(n)){var o=r;r=n,n=o}var i=r.getLowestSetBit(),s=n.getLowestSetBit();if(s<0){t(r);return}i<s&&(s=i),s>0&&(r.rShiftTo(s,r),n.rShiftTo(s,n));var a=()=>{(i=r.getLowestSetBit())>0&&r.rShiftTo(i,r),(i=n.getLowestSetBit())>0&&n.rShiftTo(i,n),r.compareTo(n)>=0?(r.subTo(n,r),r.rShiftTo(1,r)):(n.subTo(r,n),n.rShiftTo(1,n)),r.signum()>0?setTimeout(a,0):(s>0&&n.lShiftTo(s,n),setTimeout(function(){t(n)},0))};setTimeout(a,10)}fromNumberAsync(e,t,r,n){if("number"==typeof t){if(e<2)this.fromInt(1);else{this.fromNumber(e,r),this.testBit(e-1)||this.bitwiseTo(F.ONE.shiftLeft(e-1),s,this),this.isEven()&&this.dAddOffset(1,0);var o=this,i=function(){o.dAddOffset(2,0),o.bitLength()>e&&o.subTo(F.ONE.shiftLeft(e-1),o),o.isProbablePrime(t)?setTimeout(function(){n()},0):setTimeout(i,0)};setTimeout(i,0)}}else{var a=[],u=7&e;a.length=(e>>3)+1,t.nextBytes(a),u>0?a[0]&=(1<<u)-1:a[0]=0,this.fromString(a,256)}}}var T=function(){function e(){}return e.prototype.convert=function(e){return e},e.prototype.revert=function(e){return e},e.prototype.mulTo=function(e,t,r){e.multiplyTo(t,r)},e.prototype.sqrTo=function(e,t){e.squareTo(t)},e}(),R=function(){function e(e){this.m=e}return e.prototype.convert=function(e){return e.s<0||e.compareTo(this.m)>=0?e.mod(this.m):e},e.prototype.revert=function(e){return e},e.prototype.reduce=function(e){e.divRemTo(this.m,null,e)},e.prototype.mulTo=function(e,t,r){e.multiplyTo(t,r),this.reduce(r)},e.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)},e}(),A=function(){function e(e){this.m=e,this.mp=e.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<e.DB-15)-1,this.mt2=2*e.t}return e.prototype.convert=function(e){var t=x();return e.abs().dlShiftTo(this.m.t,t),t.divRemTo(this.m,null,t),e.s<0&&t.compareTo(F.ZERO)>0&&this.m.subTo(t,t),t},e.prototype.revert=function(e){var t=x();return e.copyTo(t),this.reduce(t),t},e.prototype.reduce=function(e){for(;e.t<=this.mt2;)e[e.t++]=0;for(var t=0;t<this.m.t;++t){var r=32767&e[t],n=r*this.mpl+((r*this.mph+(e[t]>>15)*this.mpl&this.um)<<15)&e.DM;for(r=t+this.m.t,e[r]+=this.m.am(0,n,e,t,0,this.m.t);e[r]>=e.DV;)e[r]-=e.DV,e[++r]++}e.clamp(),e.drShiftTo(this.m.t,e),e.compareTo(this.m)>=0&&e.subTo(this.m,e)},e.prototype.mulTo=function(e,t,r){e.multiplyTo(t,r),this.reduce(r)},e.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)},e}(),P=function(){function e(e){this.m=e,this.r2=x(),this.q3=x(),F.ONE.dlShiftTo(2*e.t,this.r2),this.mu=this.r2.divide(e)}return e.prototype.convert=function(e){if(e.s<0||e.t>2*this.m.t)return e.mod(this.m);if(0>e.compareTo(this.m))return e;var t=x();return e.copyTo(t),this.reduce(t),t},e.prototype.revert=function(e){return e},e.prototype.reduce=function(e){for(e.drShiftTo(this.m.t-1,this.r2),e.t>this.m.t+1&&(e.t=this.m.t+1,e.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);0>e.compareTo(this.r2);)e.dAddOffset(1,this.m.t+1);for(e.subTo(this.r2,e);e.compareTo(this.m)>=0;)e.subTo(this.m,e)},e.prototype.mulTo=function(e,t,r){e.multiplyTo(t,r),this.reduce(r)},e.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)},e}();function x(){return new F(null)}function N(e,t){return new F(e,t)}void 0!==e&&e?.appName=="Microsoft Internet Explorer"?(F.prototype.am=function(e,t,r,n,o,i){for(var s=32767&t,a=t>>15;--i>=0;){var u=32767&this[e],c=this[e++]>>15,l=a*u+c*s;o=((u=s*u+((32767&l)<<15)+r[n]+(1073741823&o))>>>30)+(l>>>15)+a*c+(o>>>30),r[n++]=1073741823&u}return o},f=30):e?.appName!="Netscape"?(F.prototype.am=function(e,t,r,n,o,i){for(;--i>=0;){var s=t*this[e++]+r[n]+o;o=Math.floor(s/67108864),r[n++]=67108863&s}return o},f=26):(F.prototype.am=function(e,t,r,n,o,i){for(var s=16383&t,a=t>>14;--i>=0;){var u=16383&this[e],c=this[e++]>>14,l=a*u+c*s;o=((u=s*u+((16383&l)<<14)+r[n]+o)>>28)+(l>>14)+a*c,r[n++]=268435455&u}return o},f=28),F.prototype.DB=f,F.prototype.DM=(1<<f)-1,F.prototype.DV=1<<f,F.prototype.FV=4503599627370496,F.prototype.F1=52-f,F.prototype.F2=2*f-52;var C=[];for(d=0,h=48;d<=9;++d)C[h++]=d;for(d=10,h=97;d<36;++d)C[h++]=d;for(d=10,h=65;d<36;++d)C[h++]=d;function L(e,t){var r=C[e.charCodeAt(t)];return null==r?-1:r}function j(e){var t=x();return t.fromInt(e),t}function U(e){var t,r=1;return 0!=(t=e>>>16)&&(e=t,r+=16),0!=(t=e>>8)&&(e=t,r+=8),0!=(t=e>>4)&&(e=t,r+=4),0!=(t=e>>2)&&(e=t,r+=2),0!=(t=e>>1)&&(e=t,r+=1),r}F.ZERO=j(0),F.ONE=j(1);class ${constructor(){this.i=0,this.j=0,this.S=[]}init(e){var t,r,n;for(t=0;t<256;++t)this.S[t]=t;for(t=0,r=0;t<256;++t)r=r+this.S[t]+e[t%e.length]&255,n=this.S[t],this.S[t]=this.S[r],this.S[r]=n;this.i=0,this.j=0}next(){var e;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,e=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=e,this.S[e+this.S[this.i]&255]}}var D=null;D=[],y=0;var k=void 0;if(window?.crypto&&window?.crypto.getRandomValues){var q=new Uint32Array(256);for(window?.crypto.getRandomValues(q),k=0;k<q.length;++k)D[y++]=255&q[k]}var M=function(e){if(this.count=this.count||0,this.count>=256||y>=256){window?.removeEventListener?window?.removeEventListener("mousemove",M,!1):window?.detachEvent&&window?.detachEvent("onmousemove",M);return}try{var t=e.x+e.y;D[y++]=255&t,this.count+=1}catch(e){}};window?.addEventListener?window?.addEventListener("mousemove",M,!1):window?.attachEvent&&window?.attachEvent("onmousemove",M);class V{constructor(){}nextBytes(e){for(var t=0;t<e.length;++t)e[t]=function(){if(null==p){for(p=new $;y<256;){var e=Math.floor(65536*Math.random());D[y++]=255&e}for(p.init(D),y=0;y<D.length;++y)D[y]=0;y=0}return p.next()}()}}class G{constructor(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}doPublic(e){return e.modPowInt(this.e,this.n)}doPrivate(e){if(null==this.p||null==this.q)return e.modPow(this.d,this.n);for(var t=e.mod(this.p).modPow(this.dmp1,this.p),r=e.mod(this.q).modPow(this.dmq1,this.q);0>t.compareTo(r);)t=t.add(this.p);return t.subtract(r).multiply(this.coeff).mod(this.p).multiply(this.q).add(r)}setPublic(e,t){null!=e&&null!=t&&e.length>0&&t.length>0?(this.n=N(e,16),this.e=parseInt(t,16)):console.error("Invalid RSA public key")}encrypt(e){var t=function(e,t){if(t<e.length+11)return console.error("Message too long for RSA"),null;for(var r=[],n=e.length-1;n>=0&&t>0;){var o=e.charCodeAt(n--);o<128?r[--t]=o:o>127&&o<2048?(r[--t]=63&o|128,r[--t]=o>>6|192):(r[--t]=63&o|128,r[--t]=o>>6&63|128,r[--t]=o>>12|224)}r[--t]=0;for(var i=new V,s=[];t>2;){for(s[0]=0;0==s[0];)i.nextBytes(s);r[--t]=s[0]}return r[--t]=2,r[--t]=0,new F(r)}(e,this.n.bitLength()+7>>3);if(null==t)return null;var r=this.doPublic(t);if(null==r)return null;var n=r.toString(16);return(1&n.length)==0?n:"0"+n}encryptLong(e){var t=this,r=(this.n.bitLength()+7>>3)-11;try{var n="";if(e.length>r)return e.match(/.{1,117}/g).forEach(function(e){var r=t.encrypt(e);n+=r}),m(n);var o=this.encrypt(e);return m(o)}catch(e){return!1}}decryptLong(e){var t=this,r=this.n.bitLength()+7>>3;e=g(e);try{if(e.length>r){var n="";return e.match(/.{1,256}/g).forEach(function(e){var r=t.decrypt(e);n+=r}),n}return this.decrypt(e)}catch(e){return!1}}setPrivate(e,t,r){null!=e&&null!=t&&e.length>0&&t.length>0?(this.n=N(e,16),this.e=parseInt(t,16),this.d=N(r,16)):console.error("Invalid RSA private key")}setPrivateEx(e,t,r,n,o,i,s,a){null!=e&&null!=t&&e.length>0&&t.length>0?(this.n=N(e,16),this.e=parseInt(t,16),this.d=N(r,16),this.p=N(n,16),this.q=N(o,16),this.dmp1=N(i,16),this.dmq1=N(s,16),this.coeff=N(a,16)):console.error("Invalid RSA private key")}generate(e,t){var r=new V,n=e>>1;this.e=parseInt(t,16);for(var o=new F(t,16);;){for(;this.p=new F(e-n,1,r),!(0==this.p.subtract(F.ONE).gcd(o).compareTo(F.ONE)&&this.p.isProbablePrime(10)););for(;this.q=new F(n,1,r),!(0==this.q.subtract(F.ONE).gcd(o).compareTo(F.ONE)&&this.q.isProbablePrime(10)););if(0>=this.p.compareTo(this.q)){var i=this.p;this.p=this.q,this.q=i}var s=this.p.subtract(F.ONE),a=this.q.subtract(F.ONE),u=s.multiply(a);if(0==u.gcd(o).compareTo(F.ONE)){this.n=this.p.multiply(this.q),this.d=o.modInverse(u),this.dmp1=this.d.mod(s),this.dmq1=this.d.mod(a),this.coeff=this.q.modInverse(this.p);break}}}decrypt(e){var t=N(e,16),r=this.doPrivate(t);return null==r?null:function(e,t){for(var r=e.toByteArray(),n=0;n<r.length&&0==r[n];)++n;if(r.length-n!=t-1||2!=r[n])return null;for(++n;0!=r[n];)if(++n>=r.length)return null;for(var o="";++n<r.length;){var i=255&r[n];i<128?o+=String.fromCharCode(i):i>191&&i<224?(o+=String.fromCharCode((31&i)<<6|63&r[n+1]),++n):(o+=String.fromCharCode((15&i)<<12|(63&r[n+1])<<6|63&r[n+2]),n+=2)}return o}(r,this.n.bitLength()+7>>3)}generateAsync(e,t,r){var n=new V,o=e>>1;this.e=parseInt(t,16);var i=new F(t,16),s=this,a=()=>{var t=()=>{if(0>=s.p.compareTo(s.q)){var e=s.p;s.p=s.q,s.q=e}var t=s.p.subtract(F.ONE),n=s.q.subtract(F.ONE),o=t.multiply(n);0==o.gcd(i).compareTo(F.ONE)?(s.n=s.p.multiply(s.q),s.d=i.modInverse(o),s.dmp1=s.d.mod(t),s.dmq1=s.d.mod(n),s.coeff=s.q.modInverse(s.p),setTimeout(function(){r()},0)):setTimeout(a,0)},u=()=>{s.q=x(),s.q.fromNumberAsync(o,1,n,function(){s.q.subtract(F.ONE).gcda(i,function(e){0==e.compareTo(F.ONE)&&s.q.isProbablePrime(10)?setTimeout(t,0):setTimeout(u,0)})})},c=()=>{s.p=x(),s.p.fromNumberAsync(e-o,1,n,function(){s.p.subtract(F.ONE).gcda(i,function(e){0==e.compareTo(F.ONE)&&s.p.isProbablePrime(10)?setTimeout(u,0):setTimeout(c,0)})})};setTimeout(c,0)};setTimeout(a,0)}}class H extends G{constructor(e=""){super(),e&&("string"==typeof e?this.parseKey(e):(this.hasPrivateKeyProperty(e)||this.hasPublicKeyProperty(e))&&this.parsePropertiesFrom(e))}parseKey(e){try{var t=0,r=0,n=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(e)?b.decode(e):_.unarmor(e),o=B.decode(n);if(3===o.sub.length&&(o=o.sub[2].sub[0]),9===o.sub.length){t=o.sub[1].getHexStringValue(),this.n=N(t,16),r=o.sub[2].getHexStringValue(),this.e=parseInt(r,16);var i=o.sub[3].getHexStringValue();this.d=N(i,16);var s=o.sub[4].getHexStringValue();this.p=N(s,16);var a=o.sub[5].getHexStringValue();this.q=N(a,16);var u=o.sub[6].getHexStringValue();this.dmp1=N(u,16);var c=o.sub[7].getHexStringValue();this.dmq1=N(c,16);var l=o.sub[8].getHexStringValue();this.coeff=N(l,16)}else{if(2!==o.sub.length)return!1;var f=o.sub[1].sub[0];t=f.sub[0].getHexStringValue(),this.n=N(t,16),r=f.sub[1].getHexStringValue(),this.e=parseInt(r,16)}return!0}catch(e){return!1}}hasPublicKeyProperty(e){return(e=e||{}).hasOwnProperty("n")&&e.hasOwnProperty("e")}hasPrivateKeyProperty(e){return(e=e||{}).hasOwnProperty("n")&&e.hasOwnProperty("e")&&e.hasOwnProperty("d")&&e.hasOwnProperty("p")&&e.hasOwnProperty("q")&&e.hasOwnProperty("dmp1")&&e.hasOwnProperty("dmq1")&&e.hasOwnProperty("coeff")}parsePropertiesFrom(e){this.n=e.n,this.e=e.e,e.hasOwnProperty("d")&&(this.d=e.d,this.p=e.p,this.q=e.q,this.dmp1=e.dmp1,this.dmq1=e.dmq1,this.coeff=e.coeff)}}(n=function(e){e=e||{},this.default_key_size=parseInt(e.default_key_size,10)||1024,this.default_public_exponent=e.default_public_exponent||"010001",this.log=e.log||!1,this.key=null}).prototype.setKey=function(e){this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new H(e)},n.prototype.setPrivateKey=function(e){this.setKey(e)},n.prototype.setPublicKey=function(e){this.setKey(e)},n.prototype.decrypt=function(e){try{return this.getKey().decrypt(g(e))}catch(e){return!1}},n.prototype.encrypt=function(e){try{return m(this.getKey().encrypt(e))}catch(e){return!1}},n.prototype.encryptLong=function(e){try{for(var t=this.getKey().encryptLong(e)||"",r=this.getKey().decryptLong(t)||"",n=0,o=/null$/g;o.test(r)&&(n++,t=this.getKey().encryptLong(e)||"",r=this.getKey().decryptLong(t)||"",!(n>10)););return t}catch(e){return!1}},n.prototype.getKey=function(e){if(!this.key){if(this.key=new H,e&&"[object Function]"===({}).toString.call(e)){this.key.generateAsync(this.default_key_size,this.default_public_exponent,e);return}this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},n.version="3.1.4"}let B=n;var W=r(85746);let F=({publicKey:e="",payload:t={}}={})=>{if(!e)return"";try{let r=(0,W.I)(t),n=new B;return n.setPublicKey(e),n.encryptLong("object"==typeof r?JSON.stringify(r):r)}catch(e){console.error("encrypt error:",e)}return""}},85746:(e,t,r)=>{"use strict";r.d(t,{I:()=>n,y:()=>o});let n=e=>{let t=t=>{for(let r in e)e.hasOwnProperty(r)&&(t[r]=n(e[r]));return t},r=null==e?"NullOrUndefined":Object.prototype.toString.call(e).slice(8,-1);if(["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array","BigInt64Array","BigUint64Array"].includes(r))return e.slice();switch(r){case"Object":return t(Object.create(Object.getPrototypeOf(e)));case"Array":return t([]);case"Date":return new Date(e.valueOf());case"RegExp":return new RegExp(e.source,(e.global?"g":"")+(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.sticky?"y":"")+(e.unicode?"u":""));default:return e}},o=e=>{let t=e.match(/^(?:http(s)?:\/\/[^\/]+)?(\/[^\?#]*)/);return t&&t[2]||""}},61645:function(e,t,r){"use strict";var n,o,i=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&i(t,e,r);return s(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.useDefaultAdapter=t.useAdapters=t.RUNTIME=void 0;var u=a(r(71638)),c=r(77019);(n=o=t.RUNTIME||(t.RUNTIME={})).WEB="web",n.WX_MP="wx_mp",t.useAdapters=function(e){for(var t=(0,c.isArray)(e)?e:[e],r=0;r<t.length;r++){var n=t[r],o=n.isMatch,i=n.genAdapter,s=n.runtime;if(o())return{adapter:i(),runtime:s}}},t.useDefaultAdapter=function(){return{adapter:u.genAdapter(),runtime:o.WEB}}},71638:function(e,t,r){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},s=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},a=this&&this.__generator||function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.WebRequest=t.genAdapter=void 0;var u=r(53515),c=r(77019),l=r(79197),f=function(e){function t(t){var r=e.call(this)||this,n=t.timeout,o=t.timeoutMsg,i=t.restrictedMethods;return r.timeout=n||0,r.timeoutMsg=o||"请求超时",r.restrictedMethods=i||["get","post","upload","download"],r}return o(t,e),t.prototype.get=function(e){return this.request(i(i({},e),{method:"get"}),this.restrictedMethods.includes("get"))},t.prototype.post=function(e){return this.request(i(i({},e),{method:"post"}),this.restrictedMethods.includes("post"))},t.prototype.put=function(e){return this.request(i(i({},e),{method:"put"}))},t.prototype.upload=function(e){var t=e.data,r=e.file,n=e.name,o=e.method,s=e.headers,a={post:"post",put:"put"}[null==o?void 0:o.toLowerCase()]||"put",u=new FormData;return"post"===a?(Object.keys(t).forEach(function(e){u.append(e,t[e])}),u.append("key",n),u.append("file",r),this.request(i(i({},e),{data:u,method:a}),this.restrictedMethods.includes("upload"))):this.request(i(i({},e),{method:"put",headers:void 0===s?{}:s,body:r}),this.restrictedMethods.includes("upload"))},t.prototype.download=function(e){return s(this,void 0,void 0,function(){var t,r,n,o;return a(this,function(s){switch(s.label){case 0:return s.trys.push([0,2,,3]),[4,this.get(i(i({},e),{headers:{},responseType:"blob"}))];case 1:return t=s.sent().data,r=window.URL.createObjectURL(new Blob([t])),n=decodeURIComponent(new URL(e.url).pathname.split("/").pop()||""),(o=document.createElement("a")).href=r,o.setAttribute("download",n),o.style.display="none",document.body.appendChild(o),o.click(),window.URL.revokeObjectURL(r),document.body.removeChild(o),[3,3];case 2:return s.sent(),[3,3];case 3:return[2,new Promise(function(t){t({statusCode:200,tempFilePath:e.url})})]}})})},t.prototype.fetch=function(e){var t;return s(this,void 0,void 0,function(){var r,n,o,u,c,l,f,h,d,p,y,v=this;return a(this,function(m){switch(m.label){case 0:return r=new AbortController,n=e.url,u=void 0!==(o=e.enableAbort)&&o,l=void 0!==(c=e.stream)&&c,f=e.signal,d=null!=(h=e.timeout)?h:this.timeout,f&&(f.aborted&&r.abort(),f.addEventListener("abort",function(){return r.abort()})),p=null,u&&d&&(p=setTimeout(function(){console.warn(v.timeoutMsg),r.abort(Error(v.timeoutMsg))},d)),[4,fetch(n,i(i({},e),{signal:r.signal})).then(function(e){return s(v,void 0,void 0,function(){var t,r,n;return a(this,function(o){switch(o.label){case 0:if(clearTimeout(p),!e.ok)return[3,1];return t=e,[3,3];case 1:return n=(r=Promise).reject,[4,e.json()];case 2:t=n.apply(r,[o.sent()]),o.label=3;case 3:return[2,t]}})})}).catch(function(e){return clearTimeout(p),Promise.reject(e)})];case 1:return y=m.sent(),[2,{data:l?y.body:(null===(t=y.headers.get("content-type"))||void 0===t?void 0:t.includes("application/json"))?y.json():y.text(),statusCode:y.status,header:y.headers}]}})})},t.prototype.request=function(e,t){var r=this;void 0===t&&(t=!1);var n=String(e.method).toLowerCase()||"get";return new Promise(function(o){var i,s,a=e.url,u=e.headers,f=void 0===u?{}:u,h=e.data,d=e.responseType,p=e.withCredentials,y=e.body,v=e.onUploadProgress,m=(0,c.formatUrl)((0,l.getProtocol)(),a,"get"===n?h:{}),g=new XMLHttpRequest;g.open(n,m),d&&(g.responseType=d),Object.keys(f).forEach(function(e){g.setRequestHeader(e,f[e])}),v&&g.upload.addEventListener("progress",v),g.onreadystatechange=function(){var e={};if(4===g.readyState){var t=g.getAllResponseHeaders().trim().split(/[\r\n]+/),r={};t.forEach(function(e){var t=e.split(": "),n=t.shift().toLowerCase(),o=t.join(": ");r[n]=o}),e.header=r,e.statusCode=g.status;try{e.data="blob"===d?g.response:JSON.parse(g.responseText)}catch(t){e.data="blob"===d?g.response:g.responseText}clearTimeout(i),o(e)}},t&&r.timeout&&(i=setTimeout(function(){console.warn(r.timeoutMsg),g.abort()},r.timeout)),s=(0,c.isFormData)(h)?h:"application/x-www-form-urlencoded"===f["content-type"]?(0,c.toQueryString)(h):y||(h?JSON.stringify(h):void 0),p&&(g.withCredentials=!0),g.send(s)})},t}(u.AbstractSDKRequest);t.WebRequest=f,t.genAdapter=function(){return{root:window,reqClass:f,wsClass:WebSocket,localStorage:localStorage}}},79197:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.COMMUNITY_SITE_URL=t.IS_DEBUG_MODE=t.getProtocol=t.setProtocol=t.getSdkName=t.setSdkName=void 0;var r="@cloudbase/js-sdk";t.setSdkName=function(e){r=e},t.getSdkName=function(){return r};var n="https:";t.setProtocol=function(e){n=e},t.getProtocol=function(){return n},t.IS_DEBUG_MODE=!1,t.COMMUNITY_SITE_URL="https://support.qq.com/products/148793"},30064:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ERRORS=void 0,t.ERRORS={INVALID_PARAMS:"INVALID_PARAMS",INVALID_SYNTAX:"INVALID_SYNTAX",INVALID_OPERATION:"INVALID_OPERATION",OPERATION_FAIL:"OPERATION_FAIL",NETWORK_ERROR:"NETWORK_ERROR",UNKOWN_ERROR:"UNKOWN_ERROR"}},23389:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.OATUH_LOGINTYPE=void 0,o(r(79197),t),o(r(30064),t),t.OATUH_LOGINTYPE="constants"},48608:function(e,t,r){"use strict";var n=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},o=this&&this.__generator||function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.catchErrorsDecorator=void 0;var i=r(77019),s=r(23389),a=!1;"undefined"!=typeof navigator&&navigator.userAgent&&(a=-1!==navigator.userAgent.indexOf("Firefox"));var u=a?/(\.js\/)?__decorate(\$\d+)?<@.*\d$/:/(\/\w+\.js\.)?__decorate(\$\d+)?\s*\(.*\)$/,c=/https?:\/\/.+:\d*\/.*\.js:\d+:\d+/;function l(e){var t,r=e.err,n=e.className,o=e.methodName,i=e.sourceLink;if(!i)return null;var s=r.stack.split("\n"),u=a?/^catchErrorsDecorator\/<\/descriptor.value@.*\d$/:new RegExp("".concat(n,"\\.descriptor.value\\s*\\[as\\s").concat(o,"\\]\\s*\\(.*\\)$")),l=a?/^catchErrorsDecorator\/<\/descriptor.value/:new RegExp("".concat(n,"\\.descriptor.value\\s*\\[as\\s").concat(o,"\\]")),f=s.findIndex(function(e){return u.test(e)});if(-1!==f){var h=s.filter(function(e,t){return t>f});h.unshift(s[f].replace(l,"".concat(n,".").concat(o)).replace(c,i)),(t=Error()).stack="".concat(a?"@debugger":"Error","\n").concat(h.join("\n"))}return t}t.catchErrorsDecorator=function(e){var t=e.mode,r=void 0===t?"async":t,a=e.customInfo,f=void 0===a?{}:a,h=e.title,d=e.messages,p=void 0===d?[]:d;return function(e,t,a){if(s.IS_DEBUG_MODE){var d=f.className||e.constructor.name,y=f.methodName||t,v=a.value,m=function(e){var t="",r=e.stack.split("\n"),n=r.findIndex(function(e){return u.test(e)});if(-1!==n){var o=c.exec(r[n+1]||"");t=o?o[0]:""}return t}(Error());"sync"===r?a.value=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=l({err:Error(),className:d,methodName:y,sourceLink:m});try{return v.apply(this,e)}catch(e){var n=e,o=e.message,s=e.error,a=e.error_description,u={title:h||"".concat(d,".").concat(y," failed"),content:[{type:"error",body:e}]};if(o&&/^\{.*\}$/.test(o)){var c=JSON.parse(o);u.subtitle=o,c.code&&(r?(r.code=c.code,r.msg=c.msg):(e.code=c.code,e.message=c.msg),n=r||e,u.content=p.map(function(e){return{type:"info",body:e}}))}throw s&&a&&(u.subtitle=a,r?(r.code=s,r.msg=a):(e.code=s,e.message=a),n=r||e,u.content=p.map(function(e){return{type:"info",body:e}})),(0,i.printGroupLog)(u),n}}:a.value=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n(this,void 0,void 0,function(){var t,r,n,s,a,u,c,f;return o(this,function(o){switch(o.label){case 0:t=l({err:Error(),className:d,methodName:y,sourceLink:m}),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,v.apply(this,e)];case 2:return[2,o.sent()];case 3:throw n=r=o.sent(),s=r.message,a=r.error,u=r.error_description,c={title:h||"".concat(d,".").concat(y," failed"),content:[{type:"error",body:r}]},s&&/^\{.*\}$/.test(s)&&(f=JSON.parse(s),c.subtitle=f,f.code&&(t?(t.code=f.code,t.message=f.msg):(r.code=f.code,r.message=f.msg),n=t||r,c.content=p.map(function(e){return{type:"info",body:e}}))),a&&u&&(c.subtitle=u,t?(t.code=a,t.msg=u):(r.code=a,r.message=u),n=t||r,c.content=p.map(function(e){return{type:"info",body:e}})),(0,i.printGroupLog)(c),n;case 4:return[2]}})})}}}}},5576:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),o(r(48608),t)},71223:function(e,t,r){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),i=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},s=this&&this.__generator||function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.CloudbaseCache=void 0;var a=r(53515),u=r(77019),c=r(23389),l=function(e){function t(t){var r=e.call(this)||this;return r.root=t,t.tcbCacheObject||(t.tcbCacheObject={}),r}return o(t,e),t.prototype.setItem=function(e,t){this.root.tcbCacheObject[e]=t},t.prototype.getItem=function(e){return this.root.tcbCacheObject[e]},t.prototype.removeItem=function(e){delete this.root.tcbCacheObject[e]},t.prototype.clear=function(){delete this.root.tcbCacheObject},t}(a.AbstractStorage),f=function(){function e(e){this.keys={};var t=e.persistence,r=e.platformInfo,n=e.keys;this.platformInfo=void 0===r?{}:r,this.storage||(this.persistenceTag=this.platformInfo.adapter.primaryStorage||t,this.storage=function(e,t){switch(e){case"local":default:if(!t.localStorage)return(0,u.printWarn)(c.ERRORS.INVALID_PARAMS,"localStorage is not supported on current platform"),new l(t.root);return t.localStorage;case"none":return new l(t.root)}}(this.persistenceTag,this.platformInfo.adapter),this.keys=void 0===n?{}:n)}return Object.defineProperty(e.prototype,"mode",{get:function(){return this.storage.mode||"sync"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"persistence",{get:function(){return this.persistenceTag},enumerable:!1,configurable:!0}),e.prototype.setStore=function(e,t,r){if("async"===this.mode){(0,u.printWarn)(c.ERRORS.INVALID_OPERATION,"current platform's storage is asynchronous, please use setStoreAsync insteed");return}if(this.storage)try{this.storage.setItem(e,JSON.stringify({version:r||"localCachev1",content:t}))}catch(e){throw Error(JSON.stringify({code:c.ERRORS.OPERATION_FAIL,msg:"[".concat((0,c.getSdkName)(),"][").concat(c.ERRORS.OPERATION_FAIL,"]setStore failed"),info:e}))}},e.prototype.setStoreAsync=function(e,t,r){return i(this,void 0,void 0,function(){var n;return s(this,function(o){switch(o.label){case 0:if(!this.storage)return[2];o.label=1;case 1:return o.trys.push([1,3,,4]),n={version:r||"localCachev1",content:t},[4,this.storage.setItem(e,JSON.stringify(n))];case 2:return o.sent(),[3,4];case 3:return o.sent(),[2];case 4:return[2]}})})},e.prototype.getStore=function(e,t){if("async"===this.mode){(0,u.printWarn)(c.ERRORS.INVALID_OPERATION,"current platform's storage is asynchronous, please use getStoreAsync insteed");return}try{if("undefined"!=typeof process&&(null===(r=process.env)||void 0===r?void 0:r.tcb_token))return process.env.tcb_token;if(!this.storage)return""}catch(e){return""}t=t||"localCachev1";var r,n=this.storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""},e.prototype.getStoreAsync=function(e,t){var r;return i(this,void 0,void 0,function(){var n;return s(this,function(o){switch(o.label){case 0:try{if("undefined"!=typeof process&&(null===(r=process.env)||void 0===r?void 0:r.tcb_token))return[2,process.env.tcb_token];if(!this.storage)return[2,""]}catch(e){return[2,""]}return t=t||"localCachev1",[4,this.storage.getItem(e)];case 1:if(!(n=o.sent()))return[2,""];if(n.indexOf(t)>=0)return[2,JSON.parse(n).content];return[2,""]}})})},e.prototype.removeStore=function(e){if("async"===this.mode){(0,u.printWarn)(c.ERRORS.INVALID_OPERATION,"current platform's storage is asynchronous, please use removeStoreAsync insteed");return}this.storage.removeItem(e)},e.prototype.removeStoreAsync=function(e){return i(this,void 0,void 0,function(){return s(this,function(t){switch(t.label){case 0:return[4,this.storage.removeItem(e)];case 1:return t.sent(),[2]}})})},e}();t.CloudbaseCache=f},20777:function(e,t,r){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),i=this&&this.__spreadArray||function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.removeEventListener=t.activateEvent=t.addEventListener=t.CloudbaseEventEmitter=t.IErrorEvent=t.CloudbaseEvent=void 0;var s=r(77019),a=function(e,t){this.data=t||null,this.name=e};t.CloudbaseEvent=a;var u=function(e){function t(t,r){var n=e.call(this,"error",{error:t,data:r})||this;return n.error=t,n}return o(t,e),t}(a);t.IErrorEvent=u;var c=function(){function e(){this.listeners={}}return e.prototype.on=function(e,t){var r;return(r=this.listeners)[e]=r[e]||[],r[e].push(t),this},e.prototype.off=function(e,t){return function(e,t,r){if(null==r?void 0:r[e]){var n=r[e].indexOf(t);-1!==n&&r[e].splice(n,1)}}(e,t,this.listeners),this},e.prototype.fire=function(e,t){if((0,s.isInstanceOf)(e,u))return console.error(e.error),this;var r=(0,s.isString)(e)?new a(e,t||{}):e,n=r.name;if(this.listens(n)){r.target=this;for(var o=this.listeners[n]?i([],this.listeners[n],!0):[],c=0;c<o.length;c++)o[c].call(this,r)}return this},e.prototype.listens=function(e){return this.listeners[e]&&this.listeners[e].length>0},e}();t.CloudbaseEventEmitter=c;var l=new c;t.addEventListener=function(e,t){l.on(e,t)},t.activateEvent=function(e,t){void 0===t&&(t={}),l.fire(e,t)},t.removeEventListener=function(e,t){l.off(e,t)}},77019:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.transformPhone=t.sleep=t.printGroupLog=t.throwError=t.printInfo=t.printError=t.printWarn=t.execCallback=t.createPromiseCallback=t.removeParam=t.getHash=t.getQuery=t.toQueryString=t.formatUrl=t.generateRequestId=t.genSeqId=t.isFormData=t.isInstanceOf=t.isNull=t.isPalinObject=t.isUndefined=t.isString=t.isArray=void 0;var n=r(23389);t.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)},t.isString=function(e){return"string"==typeof e},t.isUndefined=function(e){return void 0===e},t.isPalinObject=function(e){return"[object Object]"===Object.prototype.toString.call(e)},t.isNull=function(e){return"[object Null]"===Object.prototype.toString.call(e)},t.isInstanceOf=function(e,t){return e instanceof t},t.isFormData=function(e){return"[object FormData]"===Object.prototype.toString.call(e)},t.genSeqId=function(){return Math.random().toString(16).slice(2)},t.generateRequestId=function(){var e=new Date().getTime(),t=(null==performance?void 0:performance.now)&&1e3*performance.now()||0;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(r){var n=16*Math.random();return e>0?(n=(e+n)%16|0,e=Math.floor(e/16)):(n=(t+n)%16|0,t=Math.floor(t/16)),("x"===r?n:7&n|8).toString(16)})},t.formatUrl=function(e,t,r){void 0===r&&(r={});var n=/\?/.test(t),o="";return(Object.keys(r).forEach(function(e){""===o?n||(t+="?"):o+="&",o+="".concat(e,"=").concat(encodeURIComponent(r[e]))}),t+=o,/^http(s)?:\/\//.test(t))?t:"".concat(e).concat(t)},t.toQueryString=function(e){void 0===e&&(e={});var t=[];return Object.keys(e).forEach(function(r){t.push("".concat(r,"=").concat(encodeURIComponent(e[r])))}),t.join("&")},t.getQuery=function(e,t){if("undefined"==typeof window)return!1;var r=t||window.location.search,n=new RegExp("(^|&)".concat(e,"=([^&]*)(&|$)")),o=r.substr(r.indexOf("?")+1).match(n);return null!=o?o[2]:""},t.getHash=function(e){if("undefined"==typeof window)return"";var t=window.location.hash.match(new RegExp("[#?&/]".concat(e,"=([^&#]*)")));return t?t[1]:""},t.removeParam=function(e,t){var r=t.split("?")[0],n=[],o=-1!==t.indexOf("?")?t.split("?")[1]:"";if(""!==o){n=o.split("&");for(var i=n.length-1;i>=0;i-=1)n[i].split("=")[0]===e&&n.splice(i,1);r="".concat(r,"?").concat(n.join("&"))}return r},t.createPromiseCallback=function(){if(!Promise){(e=function(){}).promise={};var e,t=function(){throw Error('Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.')};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}var r=new Promise(function(t,r){e=function(e,n){return e?r(e):t(n)}});return e.promise=r,e},t.execCallback=function(e,t,r){if(void 0===r&&(r=null),e&&"function"==typeof e)return e(t,r);if(t)throw t;return r},t.printWarn=function(e,t){console.warn("[".concat((0,n.getSdkName)(),"][").concat(e,"]:").concat(t))},t.printError=function(e,t){console.error({code:e,msg:"[".concat((0,n.getSdkName)(),"][").concat(e,"]:").concat(t)})},t.printInfo=function(e,t){console.log("[".concat((0,n.getSdkName)(),"][").concat(e,"]:").concat(t))},t.throwError=function(e,t){throw Error(JSON.stringify({code:e,msg:"[".concat((0,n.getSdkName)(),"][").concat(e,"]:").concat(t)}))},t.printGroupLog=function(e){var t=e.title,r=e.subtitle,n=void 0===r?"":r,o=e.content,i=e.printTrace,s=e.collapsed;void 0!==s&&s?console.groupCollapsed(t,n):console.group(t,n);for(var a=0,u=void 0===o?[]:o;a<u.length;a++){var c=u[a],l=c.type,f=c.body;switch(l){case"info":console.log(f);break;case"warn":console.warn(f);break;case"error":console.error(f)}}void 0!==i&&i&&console.trace("stack trace:"),console.groupEnd()},t.sleep=function(e){return void 0===e&&(e=0),new Promise(function(t){return setTimeout(t,e)})},t.transformPhone=function(e){return"+86".concat(e)}},1283:(e,t,r)=>{var n=r(62264)(r(65307),"DataView");e.exports=n},50524:(e,t,r)=>{var n=r(86663),o=r(16053),i=r(75927),s=r(26533),a=r(51989);function u(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=s,u.prototype.set=a,e.exports=u},5252:(e,t,r)=>{var n=r(2922),o=r(85336),i=r(13455),s=r(19851),a=r(18261);function u(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=s,u.prototype.set=a,e.exports=u},62926:(e,t,r)=>{var n=r(62264)(r(65307),"Map");e.exports=n},82626:(e,t,r)=>{var n=r(91427),o=r(11036),i=r(32808),s=r(70530),a=r(87941);function u(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=s,u.prototype.set=a,e.exports=u},574:(e,t,r)=>{var n=r(62264)(r(65307),"Promise");e.exports=n},29194:(e,t,r)=>{var n=r(62264)(r(65307),"Set");e.exports=n},8560:(e,t,r)=>{var n=r(5252),o=r(49282),i=r(43627),s=r(54473),a=r(81043),u=r(98856);function c(e){var t=this.__data__=new n(e);this.size=t.size}c.prototype.clear=o,c.prototype.delete=i,c.prototype.get=s,c.prototype.has=a,c.prototype.set=u,e.exports=c},10257:(e,t,r)=>{var n=r(65307).Symbol;e.exports=n},52938:(e,t,r)=>{var n=r(65307).Uint8Array;e.exports=n},84596:(e,t,r)=>{var n=r(62264)(r(65307),"WeakMap");e.exports=n},67966:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n&&!1!==t(e[r],r,e););return e}},65198:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=0,i=[];++r<n;){var s=e[r];t(s,r,e)&&(i[o++]=s)}return i}},88019:(e,t,r)=>{var n=r(32787),o=r(22675),i=r(88543),s=r(76472),a=r(47248),u=r(24859),c=Object.prototype.hasOwnProperty;e.exports=function(e,t){var r=i(e),l=!r&&o(e),f=!r&&!l&&s(e),h=!r&&!l&&!f&&u(e),d=r||l||f||h,p=d?n(e.length,String):[],y=p.length;for(var v in e)(t||c.call(e,v))&&!(d&&("length"==v||f&&("offset"==v||"parent"==v)||h&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||a(v,y)))&&p.push(v);return p}},48092:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=Array(n);++r<n;)o[r]=t(e[r],r,e);return o}},26898:e=>{e.exports=function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e}},19199:(e,t,r)=>{var n=r(84429),o=r(43536),i=Object.prototype.hasOwnProperty;e.exports=function(e,t,r){var s=e[t];i.call(e,t)&&o(s,r)&&(void 0!==r||t in e)||n(e,t,r)}},22613:(e,t,r)=>{var n=r(43536);e.exports=function(e,t){for(var r=e.length;r--;)if(n(e[r][0],t))return r;return -1}},13171:(e,t,r)=>{var n=r(6049),o=r(46737);e.exports=function(e,t){return e&&n(t,o(t),e)}},54925:(e,t,r)=>{var n=r(6049),o=r(77926);e.exports=function(e,t){return e&&n(t,o(t),e)}},84429:(e,t,r)=>{var n=r(89082);e.exports=function(e,t,r){"__proto__"==t&&n?n(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}},35065:(e,t,r)=>{var n=r(8560),o=r(67966),i=r(19199),s=r(13171),a=r(54925),u=r(728),c=r(53837),l=r(57776),f=r(23820),h=r(70764),d=r(4755),p=r(95554),y=r(83450),v=r(3800),m=r(97996),g=r(88543),b=r(76472),_=r(76972),w=r(12930),S=r(85562),E=r(46737),I=r(77926),O="[object Arguments]",T="[object Function]",R="[object Object]",A={};A[O]=A["[object Array]"]=A["[object ArrayBuffer]"]=A["[object DataView]"]=A["[object Boolean]"]=A["[object Date]"]=A["[object Float32Array]"]=A["[object Float64Array]"]=A["[object Int8Array]"]=A["[object Int16Array]"]=A["[object Int32Array]"]=A["[object Map]"]=A["[object Number]"]=A[R]=A["[object RegExp]"]=A["[object Set]"]=A["[object String]"]=A["[object Symbol]"]=A["[object Uint8Array]"]=A["[object Uint8ClampedArray]"]=A["[object Uint16Array]"]=A["[object Uint32Array]"]=!0,A["[object Error]"]=A[T]=A["[object WeakMap]"]=!1,e.exports=function e(t,r,P,x,N,C){var L,j=1&r,U=2&r,D=4&r;if(P&&(L=N?P(t,x,N,C):P(t)),void 0!==L)return L;if(!w(t))return t;var k=g(t);if(k){if(L=y(t),!j)return c(t,L)}else{var q=p(t),M=q==T||"[object GeneratorFunction]"==q;if(b(t))return u(t,j);if(q==R||q==O||M&&!N){if(L=U||M?{}:m(t),!j)return U?f(t,a(L,t)):l(t,s(L,t))}else{if(!A[q])return N?t:{};L=v(t,q,j)}}C||(C=new n);var B=C.get(t);if(B)return B;C.set(t,L),S(t)?t.forEach(function(n){L.add(e(n,r,P,n,t,C))}):_(t)&&t.forEach(function(n,o){L.set(o,e(n,r,P,o,t,C))});var W=D?U?d:h:U?I:E,F=k?void 0:W(t);return o(F||t,function(n,o){F&&(n=t[o=n]),i(L,o,e(n,r,P,o,t,C))}),L}},75199:(e,t,r)=>{var n=r(12930),o=Object.create,i=function(){function e(){}return function(t){if(!n(t))return{};if(o)return o(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();e.exports=i},89929:(e,t,r)=>{var n=r(36874),o=r(62140);e.exports=function(e,t){t=n(t,e);for(var r=0,i=t.length;null!=e&&r<i;)e=e[o(t[r++])];return r&&r==i?e:void 0}},41422:(e,t,r)=>{var n=r(26898),o=r(88543);e.exports=function(e,t,r){var i=t(e);return o(e)?i:n(i,r(e))}},1414:(e,t,r)=>{var n=r(10257),o=r(65911),i=r(99959),s=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":s&&s in Object(e)?o(e):i(e)}},5199:(e,t,r)=>{var n=r(1414),o=r(15977);e.exports=function(e){return o(e)&&"[object Arguments]"==n(e)}},58013:(e,t,r)=>{var n=r(95554),o=r(15977);e.exports=function(e){return o(e)&&"[object Map]"==n(e)}},91646:(e,t,r)=>{var n=r(78364),o=r(75342),i=r(12930),s=r(54867),a=/^\[object .+?Constructor\]$/,u=Object.prototype,c=Function.prototype.toString,l=u.hasOwnProperty,f=RegExp("^"+c.call(l).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||o(e))&&(n(e)?f:a).test(s(e))}},31825:(e,t,r)=>{var n=r(95554),o=r(15977);e.exports=function(e){return o(e)&&"[object Set]"==n(e)}},70995:(e,t,r)=>{var n=r(1414),o=r(86426),i=r(15977),s={};s["[object Float32Array]"]=s["[object Float64Array]"]=s["[object Int8Array]"]=s["[object Int16Array]"]=s["[object Int32Array]"]=s["[object Uint8Array]"]=s["[object Uint8ClampedArray]"]=s["[object Uint16Array]"]=s["[object Uint32Array]"]=!0,s["[object Arguments]"]=s["[object Array]"]=s["[object ArrayBuffer]"]=s["[object Boolean]"]=s["[object DataView]"]=s["[object Date]"]=s["[object Error]"]=s["[object Function]"]=s["[object Map]"]=s["[object Number]"]=s["[object Object]"]=s["[object RegExp]"]=s["[object Set]"]=s["[object String]"]=s["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&o(e.length)&&!!s[n(e)]}},9379:(e,t,r)=>{var n=r(93049),o=r(88363),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return o(e);var t=[];for(var r in Object(e))i.call(e,r)&&"constructor"!=r&&t.push(r);return t}},36501:(e,t,r)=>{var n=r(12930),o=r(93049),i=r(16178),s=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return i(e);var t=o(e),r=[];for(var a in e)"constructor"==a&&(t||!s.call(e,a))||r.push(a);return r}},28992:(e,t,r)=>{var n=r(19199),o=r(36874),i=r(47248),s=r(12930),a=r(62140);e.exports=function(e,t,r,u){if(!s(e))return e;t=o(t,e);for(var c=-1,l=t.length,f=l-1,h=e;null!=h&&++c<l;){var d=a(t[c]),p=r;if("__proto__"===d||"constructor"===d||"prototype"===d)break;if(c!=f){var y=h[d];void 0===(p=u?u(y,d,h):void 0)&&(p=s(y)?y:i(t[c+1])?[]:{})}n(h,d,p),h=h[d]}return e}},4342:e=>{e.exports=function(e,t,r){var n=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(r=r>o?o:r)<0&&(r+=o),o=t>r?0:r-t>>>0,t>>>=0;for(var i=Array(o);++n<o;)i[n]=e[n+t];return i}},32787:e=>{e.exports=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}},7170:(e,t,r)=>{var n=r(10257),o=r(48092),i=r(88543),s=r(94192),a=1/0,u=n?n.prototype:void 0,c=u?u.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return o(t,e)+"";if(s(t))return c?c.call(t):"";var r=t+"";return"0"==r&&1/t==-a?"-0":r}},39186:e=>{e.exports=function(e){return function(t){return e(t)}}},6181:(e,t,r)=>{var n=r(36874),o=r(69932),i=r(87834),s=r(62140);e.exports=function(e,t){return t=n(t,e),null==(e=i(e,t))||delete e[s(o(t))]}},36874:(e,t,r)=>{var n=r(88543),o=r(13367),i=r(61764),s=r(60504);e.exports=function(e,t){return n(e)?e:o(e,t)?[e]:i(s(e))}},14179:(e,t,r)=>{var n=r(52938);e.exports=function(e){var t=new e.constructor(e.byteLength);return new n(t).set(new n(e)),t}},728:(e,t,r)=>{e=r.nmd(e);var n=r(65307),o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,s=i&&i.exports===o?n.Buffer:void 0,a=s?s.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var r=e.length,n=a?a(r):new e.constructor(r);return e.copy(n),n}},52405:(e,t,r)=>{var n=r(14179);e.exports=function(e,t){var r=t?n(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.byteLength)}},68537:e=>{var t=/\w*$/;e.exports=function(e){var r=new e.constructor(e.source,t.exec(e));return r.lastIndex=e.lastIndex,r}},91065:(e,t,r)=>{var n=r(10257),o=n?n.prototype:void 0,i=o?o.valueOf:void 0;e.exports=function(e){return i?Object(i.call(e)):{}}},83783:(e,t,r)=>{var n=r(14179);e.exports=function(e,t){var r=t?n(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}},53837:e=>{e.exports=function(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t}},6049:(e,t,r)=>{var n=r(19199),o=r(84429);e.exports=function(e,t,r,i){var s=!r;r||(r={});for(var a=-1,u=t.length;++a<u;){var c=t[a],l=i?i(r[c],e[c],c,r,e):void 0;void 0===l&&(l=e[c]),s?o(r,c,l):n(r,c,l)}return r}},57776:(e,t,r)=>{var n=r(6049),o=r(21052);e.exports=function(e,t){return n(e,o(e),t)}},23820:(e,t,r)=>{var n=r(6049),o=r(19884);e.exports=function(e,t){return n(e,o(e),t)}},20014:(e,t,r)=>{var n=r(65307)["__core-js_shared__"];e.exports=n},89082:(e,t,r)=>{var n=r(62264),o=function(){try{var e=n(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();e.exports=o},72858:e=>{var t="object"==typeof global&&global&&global.Object===Object&&global;e.exports=t},70764:(e,t,r)=>{var n=r(41422),o=r(21052),i=r(46737);e.exports=function(e){return n(e,i,o)}},4755:(e,t,r)=>{var n=r(41422),o=r(19884),i=r(77926);e.exports=function(e){return n(e,i,o)}},58279:(e,t,r)=>{var n=r(34693);e.exports=function(e,t){var r=e.__data__;return n(t)?r["string"==typeof t?"string":"hash"]:r.map}},62264:(e,t,r)=>{var n=r(91646),o=r(30531);e.exports=function(e,t){var r=o(e,t);return n(r)?r:void 0}},40827:(e,t,r)=>{var n=r(11542)(Object.getPrototypeOf,Object);e.exports=n},65911:(e,t,r)=>{var n=r(10257),o=Object.prototype,i=o.hasOwnProperty,s=o.toString,a=n?n.toStringTag:void 0;e.exports=function(e){var t=i.call(e,a),r=e[a];try{e[a]=void 0;var n=!0}catch(e){}var o=s.call(e);return n&&(t?e[a]=r:delete e[a]),o}},21052:(e,t,r)=>{var n=r(65198),o=r(24051),i=Object.prototype.propertyIsEnumerable,s=Object.getOwnPropertySymbols,a=s?function(e){return null==e?[]:n(s(e=Object(e)),function(t){return i.call(e,t)})}:o;e.exports=a},19884:(e,t,r)=>{var n=r(26898),o=r(40827),i=r(21052),s=r(24051),a=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)n(t,i(e)),e=o(e);return t}:s;e.exports=a},95554:(e,t,r)=>{var n=r(1283),o=r(62926),i=r(574),s=r(29194),a=r(84596),u=r(1414),c=r(54867),l="[object Map]",f="[object Promise]",h="[object Set]",d="[object WeakMap]",p="[object DataView]",y=c(n),v=c(o),m=c(i),g=c(s),b=c(a),_=u;(n&&_(new n(new ArrayBuffer(1)))!=p||o&&_(new o)!=l||i&&_(i.resolve())!=f||s&&_(new s)!=h||a&&_(new a)!=d)&&(_=function(e){var t=u(e),r="[object Object]"==t?e.constructor:void 0,n=r?c(r):"";if(n)switch(n){case y:return p;case v:return l;case m:return f;case g:return h;case b:return d}return t}),e.exports=_},30531:e=>{e.exports=function(e,t){return null==e?void 0:e[t]}},86663:(e,t,r)=>{var n=r(48833);e.exports=function(){this.__data__=n?n(null):{},this.size=0}},16053:e=>{e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},75927:(e,t,r)=>{var n=r(48833),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(n){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(t,e)?t[e]:void 0}},26533:(e,t,r)=>{var n=r(48833),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return n?void 0!==t[e]:o.call(t,e)}},51989:(e,t,r)=>{var n=r(48833);e.exports=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=n&&void 0===t?"__lodash_hash_undefined__":t,this}},83450:e=>{var t=Object.prototype.hasOwnProperty;e.exports=function(e){var r=e.length,n=new e.constructor(r);return r&&"string"==typeof e[0]&&t.call(e,"index")&&(n.index=e.index,n.input=e.input),n}},3800:(e,t,r)=>{var n=r(14179),o=r(52405),i=r(68537),s=r(91065),a=r(83783);e.exports=function(e,t,r){var u=e.constructor;switch(t){case"[object ArrayBuffer]":return n(e);case"[object Boolean]":case"[object Date]":return new u(+e);case"[object DataView]":return o(e,r);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return a(e,r);case"[object Map]":case"[object Set]":return new u;case"[object Number]":case"[object String]":return new u(e);case"[object RegExp]":return i(e);case"[object Symbol]":return s(e)}}},97996:(e,t,r)=>{var n=r(75199),o=r(40827),i=r(93049);e.exports=function(e){return"function"!=typeof e.constructor||i(e)?{}:n(o(e))}},47248:e=>{var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,r){var n=typeof e;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}},13367:(e,t,r)=>{var n=r(88543),o=r(94192),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,s=/^\w*$/;e.exports=function(e,t){if(n(e))return!1;var r=typeof e;return!!("number"==r||"symbol"==r||"boolean"==r||null==e||o(e))||s.test(e)||!i.test(e)||null!=t&&e in Object(t)}},34693:e=>{e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},75342:(e,t,r)=>{var n=r(20014),o=function(){var e=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();e.exports=function(e){return!!o&&o in e}},93049:e=>{var t=Object.prototype;e.exports=function(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||t)}},2922:e=>{e.exports=function(){this.__data__=[],this.size=0}},85336:(e,t,r)=>{var n=r(22613),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,r=n(t,e);return!(r<0)&&(r==t.length-1?t.pop():o.call(t,r,1),--this.size,!0)}},13455:(e,t,r)=>{var n=r(22613);e.exports=function(e){var t=this.__data__,r=n(t,e);return r<0?void 0:t[r][1]}},19851:(e,t,r)=>{var n=r(22613);e.exports=function(e){return n(this.__data__,e)>-1}},18261:(e,t,r)=>{var n=r(22613);e.exports=function(e,t){var r=this.__data__,o=n(r,e);return o<0?(++this.size,r.push([e,t])):r[o][1]=t,this}},91427:(e,t,r)=>{var n=r(50524),o=r(5252),i=r(62926);e.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},11036:(e,t,r)=>{var n=r(58279);e.exports=function(e){var t=n(this,e).delete(e);return this.size-=t?1:0,t}},32808:(e,t,r)=>{var n=r(58279);e.exports=function(e){return n(this,e).get(e)}},70530:(e,t,r)=>{var n=r(58279);e.exports=function(e){return n(this,e).has(e)}},87941:(e,t,r)=>{var n=r(58279);e.exports=function(e,t){var r=n(this,e),o=r.size;return r.set(e,t),this.size+=r.size==o?0:1,this}},42981:(e,t,r)=>{var n=r(51599);e.exports=function(e){var t=n(e,function(e){return 500===r.size&&r.clear(),e}),r=t.cache;return t}},48833:(e,t,r)=>{var n=r(62264)(Object,"create");e.exports=n},88363:(e,t,r)=>{var n=r(11542)(Object.keys,Object);e.exports=n},16178:e=>{e.exports=function(e){var t=[];if(null!=e)for(var r in Object(e))t.push(r);return t}},13538:(e,t,r)=>{e=r.nmd(e);var n=r(72858),o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,s=i&&i.exports===o&&n.process,a=function(){try{var e=i&&i.require&&i.require("util").types;if(e)return e;return s&&s.binding&&s.binding("util")}catch(e){}}();e.exports=a},99959:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},11542:e=>{e.exports=function(e,t){return function(r){return e(t(r))}}},87834:(e,t,r)=>{var n=r(89929),o=r(4342);e.exports=function(e,t){return t.length<2?e:n(e,o(t,0,-1))}},65307:(e,t,r)=>{var n=r(72858),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();e.exports=i},49282:(e,t,r)=>{var n=r(5252);e.exports=function(){this.__data__=new n,this.size=0}},43627:e=>{e.exports=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}},54473:e=>{e.exports=function(e){return this.__data__.get(e)}},81043:e=>{e.exports=function(e){return this.__data__.has(e)}},98856:(e,t,r)=>{var n=r(5252),o=r(62926),i=r(82626);e.exports=function(e,t){var r=this.__data__;if(r instanceof n){var s=r.__data__;if(!o||s.length<199)return s.push([e,t]),this.size=++r.size,this;r=this.__data__=new i(s)}return r.set(e,t),this.size=r.size,this}},61764:(e,t,r)=>{var n=r(42981),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,s=n(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(o,function(e,r,n,o){t.push(n?o.replace(i,"$1"):r||e)}),t});e.exports=s},62140:(e,t,r)=>{var n=r(94192),o=1/0;e.exports=function(e){if("string"==typeof e||n(e))return e;var t=e+"";return"0"==t&&1/e==-o?"-0":t}},54867:e=>{var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},78147:(e,t,r)=>{var n=r(35065);e.exports=function(e){return n(e,5)}},43536:e=>{e.exports=function(e,t){return e===t||e!=e&&t!=t}},22675:(e,t,r)=>{var n=r(5199),o=r(15977),i=Object.prototype,s=i.hasOwnProperty,a=i.propertyIsEnumerable,u=n(function(){return arguments}())?n:function(e){return o(e)&&s.call(e,"callee")&&!a.call(e,"callee")};e.exports=u},88543:e=>{var t=Array.isArray;e.exports=t},33758:(e,t,r)=>{var n=r(78364),o=r(86426);e.exports=function(e){return null!=e&&o(e.length)&&!n(e)}},76472:(e,t,r)=>{e=r.nmd(e);var n=r(65307),o=r(44172),i=t&&!t.nodeType&&t,s=i&&e&&!e.nodeType&&e,a=s&&s.exports===i?n.Buffer:void 0,u=a?a.isBuffer:void 0;e.exports=u||o},78364:(e,t,r)=>{var n=r(1414),o=r(12930);e.exports=function(e){if(!o(e))return!1;var t=n(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},86426:e=>{e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},76972:(e,t,r)=>{var n=r(58013),o=r(39186),i=r(13538),s=i&&i.isMap,a=s?o(s):n;e.exports=a},12930:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},15977:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},85562:(e,t,r)=>{var n=r(31825),o=r(39186),i=r(13538),s=i&&i.isSet,a=s?o(s):n;e.exports=a},94192:(e,t,r)=>{var n=r(1414),o=r(15977);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==n(e)}},24859:(e,t,r)=>{var n=r(70995),o=r(39186),i=r(13538),s=i&&i.isTypedArray,a=s?o(s):n;e.exports=a},46737:(e,t,r)=>{var n=r(88019),o=r(9379),i=r(33758);e.exports=function(e){return i(e)?n(e):o(e)}},77926:(e,t,r)=>{var n=r(88019),o=r(36501),i=r(33758);e.exports=function(e){return i(e)?n(e,!0):o(e)}},69932:e=>{e.exports=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}},51599:(e,t,r)=>{var n=r(82626);function o(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw TypeError("Expected a function");var r=function(){var n=arguments,o=t?t.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var s=e.apply(this,n);return r.cache=i.set(o,s)||i,s};return r.cache=new(o.Cache||n),r}o.Cache=n,e.exports=o},41588:(e,t,r)=>{var n=r(28992);e.exports=function(e,t,r){return null==e?e:n(e,t,r)}},24051:e=>{e.exports=function(){return[]}},44172:e=>{e.exports=function(){return!1}},60504:(e,t,r)=>{var n=r(7170);e.exports=function(e){return null==e?"":n(e)}},43130:(e,t,r)=>{var n=r(6181);e.exports=function(e,t){return null==e||n(e,t)}},35815:function(e,t){var r,n,o;n=[],void 0!==(o="function"==typeof(r=function(){"use strict";var e="undefined"!=typeof global?global:self;if(void 0!==e.TextEncoder&&void 0!==e.TextDecoder)return{TextEncoder:e.TextEncoder,TextDecoder:e.TextDecoder};var t=["utf8","utf-8","unicode-1-1-utf-8"];return{TextEncoder:function(e){if(0>t.indexOf(e)&&null!=e)throw RangeError("Invalid encoding type. Only utf-8 is supported");this.encoding="utf-8",this.encode=function(e){if("string"!=typeof e)throw TypeError("passed argument must be of type string");var t=unescape(encodeURIComponent(e)),r=new Uint8Array(t.length);return t.split("").forEach(function(e,t){r[t]=e.charCodeAt(0)}),r}},TextDecoder:function(e,r){if(0>t.indexOf(e)&&null!=e)throw RangeError("Invalid encoding type. Only utf-8 is supported");if(this.encoding="utf-8",this.ignoreBOM=!1,this.fatal=void 0!==r&&"fatal"in r&&r.fatal,"boolean"!=typeof this.fatal)throw TypeError("fatal flag must be boolean");this.decode=function(e,t){if(void 0===e)return"";if("boolean"!=typeof(void 0!==t&&"stream"in t&&t.stream))throw TypeError("stream option must be boolean");if(ArrayBuffer.isView(e)){var r=new Uint8Array(e.buffer,e.byteOffset,e.byteLength),n=Array(r.length);return r.forEach(function(e,t){n[t]=String.fromCharCode(e)}),decodeURIComponent(escape(n.join("")))}throw TypeError("passed argument must be an array buffer view")}}}})?r.apply(t,n):r)&&(e.exports=o)},84145:(e,t,r)=>{"use strict";r.d(t,{initCloudBase:()=>lT});var n,o,i,s,a,u,c,l,f,h,d,p,y,v,m,g,b,_,w,S,E,I,O={};r.r(O),r.d(O,{ArkSimpleModel:()=>s$,DSSimpleModel:()=>sJ,DefaultSimpleModel:()=>al,HunYuanBetaSimpleModel:()=>sj,HunYuanExpSimpleModel:()=>s7,HunYuanOpenSimpleModel:()=>ar,HunYuanSimpleModel:()=>sq,MODELS:()=>aO,MoonshotSimpleModel:()=>s4,ReactModel:()=>ag,YiSimpleModel:()=>sX,ZhiPuSimpleModel:()=>sR,toolMap:()=>aw});var T={};r.r(T),r.d(T,{LineString:()=>ue,MultiLineString:()=>us,MultiPoint:()=>uo,MultiPolygon:()=>uu,Point:()=>up,Polygon:()=>ur});var R="@cloudbase/js-sdk",A="https:",P="https://support.qq.com/products/148793",x={INVALID_PARAMS:"INVALID_PARAMS",INVALID_OPERATION:"INVALID_OPERATION",OPERATION_FAIL:"OPERATION_FAIL"},N=r(53515);function C(e){return"[object Array]"===Object.prototype.toString.call(e)}function L(e){return"string"==typeof e}function j(e){return"[object FormData]"===Object.prototype.toString.call(e)}function U(e,t,r){void 0===r&&(r={});var n=/\?/.test(t),o="";return(Object.keys(r).forEach(function(e){""===o?n||(t+="?"):o+="&",o+="".concat(e,"=").concat(encodeURIComponent(r[e]))}),t+=o,/^http(s)?:\/\//.test(t))?t:"".concat(e).concat(t)}function D(e,t,r){if(void 0===r&&(r=null),e&&"function"==typeof e)return e(t,r);if(t)throw t;return r}function k(e,t){console.warn("[".concat(R,"][").concat(e,"]:").concat(t))}var q=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),M=function(){return(M=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},B=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},W=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},F=function(e){function t(t){var r=e.call(this)||this,n=t.timeout,o=t.timeoutMsg,i=t.restrictedMethods;return r.timeout=n||0,r.timeoutMsg=o||"请求超时",r.restrictedMethods=i||["get","post","upload","download"],r}return q(t,e),t.prototype.get=function(e){return this.request(M(M({},e),{method:"get"}),this.restrictedMethods.includes("get"))},t.prototype.post=function(e){return this.request(M(M({},e),{method:"post"}),this.restrictedMethods.includes("post"))},t.prototype.put=function(e){return this.request(M(M({},e),{method:"put"}))},t.prototype.upload=function(e){var t=e.data,r=e.file,n=e.name,o=e.method,i=e.headers,s={post:"post",put:"put"}[null==o?void 0:o.toLowerCase()]||"put",a=new FormData;return"post"===s?(Object.keys(t).forEach(function(e){a.append(e,t[e])}),a.append("key",n),a.append("file",r),this.request(M(M({},e),{data:a,method:s}),this.restrictedMethods.includes("upload"))):this.request(M(M({},e),{method:"put",headers:void 0===i?{}:i,body:r}),this.restrictedMethods.includes("upload"))},t.prototype.download=function(e){return B(this,void 0,void 0,function(){var t,r,n,o;return W(this,function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,this.get(M(M({},e),{headers:{},responseType:"blob"}))];case 1:return t=i.sent().data,r=window.URL.createObjectURL(new Blob([t])),n=decodeURIComponent(new URL(e.url).pathname.split("/").pop()||""),(o=document.createElement("a")).href=r,o.setAttribute("download",n),o.style.display="none",document.body.appendChild(o),o.click(),window.URL.revokeObjectURL(r),document.body.removeChild(o),[3,3];case 2:return i.sent(),[3,3];case 3:return[2,new Promise(function(t){t({statusCode:200,tempFilePath:e.url})})]}})})},t.prototype.fetch=function(e){var t;return B(this,void 0,void 0,function(){var r,n,o,i,s,a,u,c,l,f,h,d=this;return W(this,function(p){switch(p.label){case 0:return r=new AbortController,n=e.url,i=void 0!==(o=e.enableAbort)&&o,a=void 0!==(s=e.stream)&&s,u=e.signal,l=null!=(c=e.timeout)?c:this.timeout,u&&(u.aborted&&r.abort(),u.addEventListener("abort",function(){return r.abort()})),f=null,i&&l&&(f=setTimeout(function(){console.warn(d.timeoutMsg),r.abort(Error(d.timeoutMsg))},l)),[4,fetch(n,M(M({},e),{signal:r.signal})).then(function(e){return B(d,void 0,void 0,function(){var t,r,n;return W(this,function(o){switch(o.label){case 0:if(clearTimeout(f),!e.ok)return[3,1];return t=e,[3,3];case 1:return n=(r=Promise).reject,[4,e.json()];case 2:t=n.apply(r,[o.sent()]),o.label=3;case 3:return[2,t]}})})}).catch(function(e){return clearTimeout(f),Promise.reject(e)})];case 1:return h=p.sent(),[2,{data:a?h.body:(null===(t=h.headers.get("content-type"))||void 0===t?void 0:t.includes("application/json"))?h.json():h.text(),statusCode:h.status,header:h.headers}]}})})},t.prototype.request=function(e,t){var r=this;void 0===t&&(t=!1);var n=String(e.method).toLowerCase()||"get";return new Promise(function(o){var i,s,a,u,c=e.url,l=e.headers,f=void 0===l?{}:l,h=e.data,d=e.responseType,p=e.withCredentials,y=e.body,v=e.onUploadProgress,m=U(A,c,"get"===n?h:{}),g=new XMLHttpRequest;(g.open(n,m),d&&(g.responseType=d),Object.keys(f).forEach(function(e){g.setRequestHeader(e,f[e])}),v&&g.upload.addEventListener("progress",v),g.onreadystatechange=function(){var e={};if(4===g.readyState){var t=g.getAllResponseHeaders().trim().split(/[\r\n]+/),r={};t.forEach(function(e){var t=e.split(": "),n=t.shift().toLowerCase(),o=t.join(": ");r[n]=o}),e.header=r,e.statusCode=g.status;try{e.data="blob"===d?g.response:JSON.parse(g.responseText)}catch(t){e.data="blob"===d?g.response:g.responseText}clearTimeout(a),o(e)}},t&&r.timeout&&(a=setTimeout(function(){console.warn(r.timeoutMsg),g.abort()},r.timeout)),j(h))?u=h:"application/x-www-form-urlencoded"===f["content-type"]?(void 0===(i=h)&&(i={}),s=[],Object.keys(i).forEach(function(e){s.push("".concat(e,"=").concat(encodeURIComponent(i[e])))}),u=s.join("&")):u=y||(h?JSON.stringify(h):void 0),p&&(g.withCredentials=!0),g.send(u)})},t}(N.AbstractSDKRequest);!function(e){e.WEB="web",e.WX_MP="wx_mp"}(n||(n={}));var $=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),V=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},G=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},H=function(e){function t(t){var r=e.call(this)||this;return r.root=t,t.tcbCacheObject||(t.tcbCacheObject={}),r}return $(t,e),t.prototype.setItem=function(e,t){this.root.tcbCacheObject[e]=t},t.prototype.getItem=function(e){return this.root.tcbCacheObject[e]},t.prototype.removeItem=function(e){delete this.root.tcbCacheObject[e]},t.prototype.clear=function(){delete this.root.tcbCacheObject},t}(N.AbstractStorage),K=function(){function e(e){this.keys={};var t=e.persistence,r=e.platformInfo,n=e.keys;this.platformInfo=void 0===r?{}:r,this.storage||(this.persistenceTag=this.platformInfo.adapter.primaryStorage||t,this.storage=function(e,t){switch(e){case"local":default:if(!t.localStorage)return k(x.INVALID_PARAMS,"localStorage is not supported on current platform"),new H(t.root);return t.localStorage;case"none":return new H(t.root)}}(this.persistenceTag,this.platformInfo.adapter),this.keys=void 0===n?{}:n)}return Object.defineProperty(e.prototype,"mode",{get:function(){return this.storage.mode||"sync"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"persistence",{get:function(){return this.persistenceTag},enumerable:!1,configurable:!0}),e.prototype.setStore=function(e,t,r){if("async"===this.mode){k(x.INVALID_OPERATION,"current platform's storage is asynchronous, please use setStoreAsync insteed");return}if(this.storage)try{this.storage.setItem(e,JSON.stringify({version:r||"localCachev1",content:t}))}catch(e){throw Error(JSON.stringify({code:x.OPERATION_FAIL,msg:"[".concat(R,"][").concat(x.OPERATION_FAIL,"]setStore failed"),info:e}))}},e.prototype.setStoreAsync=function(e,t,r){return V(this,void 0,void 0,function(){var n;return G(this,function(o){switch(o.label){case 0:if(!this.storage)return[2];o.label=1;case 1:return o.trys.push([1,3,,4]),n={version:r||"localCachev1",content:t},[4,this.storage.setItem(e,JSON.stringify(n))];case 2:return o.sent(),[3,4];case 3:return o.sent(),[2];case 4:return[2]}})})},e.prototype.getStore=function(e,t){if("async"===this.mode){k(x.INVALID_OPERATION,"current platform's storage is asynchronous, please use getStoreAsync insteed");return}try{if("undefined"!=typeof process&&(null===(r=process.env)||void 0===r?void 0:r.tcb_token))return process.env.tcb_token;if(!this.storage)return""}catch(e){return""}t=t||"localCachev1";var r,n=this.storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""},e.prototype.getStoreAsync=function(e,t){var r;return V(this,void 0,void 0,function(){var n;return G(this,function(o){switch(o.label){case 0:try{if("undefined"!=typeof process&&(null===(r=process.env)||void 0===r?void 0:r.tcb_token))return[2,process.env.tcb_token];if(!this.storage)return[2,""]}catch(e){return[2,""]}return t=t||"localCachev1",[4,this.storage.getItem(e)];case 1:if(!(n=o.sent()))return[2,""];if(n.indexOf(t)>=0)return[2,JSON.parse(n).content];return[2,""]}})})},e.prototype.removeStore=function(e){if("async"===this.mode){k(x.INVALID_OPERATION,"current platform's storage is asynchronous, please use removeStoreAsync insteed");return}this.storage.removeItem(e)},e.prototype.removeStoreAsync=function(e){return V(this,void 0,void 0,function(){return G(this,function(t){switch(t.label){case 0:return[4,this.storage.removeItem(e)];case 1:return t.sent(),[2]}})})},e}(),J=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),z=function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))},Y=function(e,t){this.data=t||null,this.name=e},Q=function(e){function t(t,r){var n=e.call(this,"error",{error:t,data:r})||this;return n.error=t,n}return J(t,e),t}(Y),Z=function(){function e(){this.listeners={}}return e.prototype.on=function(e,t){var r;return(r=this.listeners)[e]=r[e]||[],r[e].push(t),this},e.prototype.off=function(e,t){return function(e,t,r){if(null==r?void 0:r[e]){var n=r[e].indexOf(t);-1!==n&&r[e].splice(n,1)}}(e,t,this.listeners),this},e.prototype.fire=function(e,t){if(e instanceof Q)return console.error(e.error),this;var r=L(e)?new Y(e,t||{}):e,n=r.name;if(this.listens(n)){r.target=this;for(var o=this.listeners[n]?z([],this.listeners[n],!0):[],i=0;i<o.length;i++)o[i].call(this,r)}return this},e.prototype.listens=function(e){return this.listeners[e]&&this.listeners[e].length>0},e}(),X=(new Z,!1);"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.indexOf("Firefox");function ee(e){return e.mode,e.customInfo,e.title,e.messages,function(e,t,r){}}function et(e){this.message=e}function er(e){this.message=e}function en(){}function eo(e){return"object"==typeof e&&null!==e||"function"==typeof e}function ei(e,t){try{Object.defineProperty(e,"name",{value:t,configurable:!0})}catch(e){}}(function(){var e=this;this.listeners=[],this.signal={aborted:!1,addEventListener:function(t,r){"abort"===t&&e.listeners.push(r)}}}).prototype.abort=function(){this.signal.aborted||(this.signal.aborted=!0,this.listeners.forEach(function(e){return e()}))},et.prototype=Error(),et.prototype.name="InvalidCharacterError","undefined"!=typeof window&&window.atob&&window.atob.bind(window),er.prototype=Error(),er.prototype.name="InvalidTokenError";let es=Promise,ea=Promise.resolve.bind(es),eu=Promise.prototype.then,ec=Promise.reject.bind(es);function el(e){return new es(e)}function ef(e){return el(t=>t(e))}function eh(e,t,r){return eu.call(e,t,r)}function ed(e,t,r){eh(eh(e,t,r),void 0,en)}function ep(e,t){ed(e,void 0,t)}function ey(e){eh(e,void 0,en)}let ev=e=>{if("function"==typeof queueMicrotask)ev=queueMicrotask;else{let e=ef(void 0);ev=t=>eh(e,t)}return ev(e)};function em(e,t,r){if("function"!=typeof e)throw TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function eg(e,t,r){try{return ef(em(e,t,r))}catch(e){return ec(e)}}class eb{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){let t=this._back,r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){let e=this._front,t=e,r=this._cursor,n=r+1,o=e._elements,i=o[r];return 16384===n&&(t=e._next,n=0),--this._size,this._cursor=n,e!==t&&(this._front=t),o[r]=void 0,i}forEach(e){let t=this._cursor,r=this._front,n=r._elements;for(;!(t===n.length&&void 0===r._next||t===n.length&&(n=(r=r._next)._elements,t=0,0===n.length));)e(n[t]),++t}peek(){let e=this._front,t=this._cursor;return e._elements[t]}}let e_=Symbol("[[AbortSteps]]"),ew=Symbol("[[ErrorSteps]]"),eS=Symbol("[[CancelSteps]]"),eE=Symbol("[[PullSteps]]"),eI=Symbol("[[ReleaseSteps]]");function eO(e,t){var r;e._ownerReadableStream=t,t._reader=e,"readable"===t._state?eP(e):"closed"===t._state?(eP(e),eN(e)):(r=t._storedError,eP(e),ex(e,r))}function eT(e,t){return rH(e._ownerReadableStream,t)}function eR(e){var t;let r=e._ownerReadableStream;"readable"===r._state?ex(e,TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):(t=TypeError("Reader was released and can no longer be used to monitor the stream's closedness"),eP(e),ex(e,t)),r._readableStreamController[eI](),r._reader=void 0,e._ownerReadableStream=void 0}function eA(e){return TypeError("Cannot "+e+" a stream using a released reader")}function eP(e){e._closedPromise=el((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r})}function ex(e,t){void 0!==e._closedPromise_reject&&(ey(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function eN(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}let eC=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},eL=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function ej(e,t){if(void 0!==e&&"object"!=typeof e&&"function"!=typeof e)throw TypeError(`${t} is not an object.`)}function eU(e,t){if("function"!=typeof e)throw TypeError(`${t} is not a function.`)}function eD(e,t){if(!("object"==typeof e&&null!==e||"function"==typeof e))throw TypeError(`${t} is not an object.`)}function ek(e,t,r){if(void 0===e)throw TypeError(`Parameter ${t} is required in '${r}'.`)}function eq(e,t,r){if(void 0===e)throw TypeError(`${t} is required in '${r}'.`)}function eM(e){return Number(e)}function eB(e,t){var r,n;let o=Number.MAX_SAFE_INTEGER,i=Number(e);if(!eC(i=0===(r=i)?0:r))throw TypeError(`${t} is not a finite number`);if((i=0===(n=eL(i))?0:n)<0||i>o)throw TypeError(`${t} is outside the accepted range of 0 to ${o}, inclusive`);return eC(i)&&0!==i?i:0}function eW(e,t){if(!rV(e))throw TypeError(`${t} is not a ReadableStream.`)}function eF(e){return new eK(e)}function e$(e,t){e._reader._readRequests.push(t)}function eV(e,t,r){let n=e._reader._readRequests.shift();r?n._closeSteps():n._chunkSteps(t)}function eG(e){return e._reader._readRequests.length}function eH(e){let t=e._reader;return void 0!==t&&!!eJ(t)}class eK{constructor(e){if(ek(e,1,"ReadableStreamDefaultReader"),eW(e,"First parameter"),rG(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");eO(this,e),this._readRequests=new eb}get closed(){return eJ(this)?this._closedPromise:ec(eQ("closed"))}cancel(e){return eJ(this)?void 0===this._ownerReadableStream?ec(eA("cancel")):eT(this,e):ec(eQ("cancel"))}read(){let e,t;if(!eJ(this))return ec(eQ("read"));if(void 0===this._ownerReadableStream)return ec(eA("read from"));let r=el((r,n)=>{e=r,t=n});return ez(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!eJ(this))throw eQ("releaseLock");void 0!==this._ownerReadableStream&&(eR(this),eY(this,TypeError("Reader was released")))}}function eJ(e){return!!eo(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof eK}function ez(e,t){let r=e._ownerReadableStream;r._disturbed=!0,"closed"===r._state?t._closeSteps():"errored"===r._state?t._errorSteps(r._storedError):r._readableStreamController[eE](t)}function eY(e,t){let r=e._readRequests;e._readRequests=new eb,r.forEach(e=>{e._errorSteps(t)})}function eQ(e){return TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}function eZ(e){return e.slice()}function eX(e,t,r,n,o){new Uint8Array(e).set(new Uint8Array(r,n,o),t)}Object.defineProperties(eK.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),ei(eK.prototype.cancel,"cancel"),ei(eK.prototype.read,"read"),ei(eK.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(eK.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});let e0=e=>(e0="function"==typeof e.transfer?e=>e.transfer():"function"==typeof structuredClone?e=>structuredClone(e,{transfer:[e]}):e=>e)(e),e1=e=>(e1="boolean"==typeof e.detached?e=>e.detached:e=>0===e.byteLength)(e);function e2(e,t,r){if(e.slice)return e.slice(t,r);let n=r-t,o=new ArrayBuffer(n);return eX(o,0,e,t,n),o}function e3(e,t){let r=e[t];if(null!=r){if("function"!=typeof r)throw TypeError(`${String(t)} is not a function`);return r}}function e4(e){try{let t=e.done,r=e.value;return eh(ea(r),e=>({done:t,value:e}))}catch(e){return ec(e)}}let e5=null!==(s=null!==(o=Symbol.asyncIterator)&&void 0!==o?o:null===(i=Symbol.for)||void 0===i?void 0:i.call(Symbol,"Symbol.asyncIterator"))&&void 0!==s?s:"@@asyncIterator";function e6(e){let t=em(e.nextMethod,e.iterator,[]);if(!eo(t))throw TypeError("The iterator.next() method must return an object");return t}class e8{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){let e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?eh(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){let t=()=>this._returnSteps(e);return this._ongoingPromise=this._ongoingPromise?eh(this._ongoingPromise,t,t):t(),this._ongoingPromise}_nextSteps(){let e,t;if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let r=this._reader,n=el((r,n)=>{e=r,t=n});return ez(r,{_chunkSteps:t=>{this._ongoingPromise=void 0,ev(()=>e({value:t,done:!1}))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,eR(r),e({value:void 0,done:!0})},_errorSteps:e=>{this._ongoingPromise=void 0,this._isFinished=!0,eR(r),t(e)}}),n}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;let t=this._reader;if(!this._preventCancel){let r=eT(t,e);return eR(t),eh(r,()=>({value:e,done:!0}),void 0)}return eR(t),ef({value:e,done:!0})}}let e7={next(){return e9(this)?this._asyncIteratorImpl.next():ec(te("next"))},return(e){return e9(this)?this._asyncIteratorImpl.return(e):ec(te("return"))},[e5](){return this}};function e9(e){if(!eo(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof e8}catch(e){return!1}}function te(e){return TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}Object.defineProperty(e7,e5,{enumerable:!1});let tt=Number.isNaN||function(e){return e!=e};function tr(e){return new Uint8Array(e2(e.buffer,e.byteOffset,e.byteOffset+e.byteLength))}function tn(e){let t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function to(e,t,r){if("number"!=typeof r||tt(r)||r<0||r===1/0)throw RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function ti(e){e._queue=new eb,e._queueTotalSize=0}function ts(e){return e===DataView}class ta{constructor(){throw TypeError("Illegal constructor")}get view(){if(!tl(this))throw tD("view");return this._view}respond(e){if(!tl(this))throw tD("respond");if(ek(e,1,"respond"),e=eB(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");if(e1(this._view.buffer))throw TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");tL(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!tl(this))throw tD("respondWithNewView");if(ek(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");if(e1(e.buffer))throw TypeError("The given view's buffer has been detached and so cannot be used as a response");tj(this._associatedReadableByteStreamController,e)}}Object.defineProperties(ta.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),ei(ta.prototype.respond,"respond"),ei(ta.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ta.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class tu{constructor(){throw TypeError("Illegal constructor")}get byobRequest(){if(!tc(this))throw tk("byobRequest");return tN(this)}get desiredSize(){if(!tc(this))throw tk("desiredSize");return tC(this)}close(){if(!tc(this))throw tk("close");if(this._closeRequested)throw TypeError("The stream has already been closed; do not close it again!");let e=this._controlledReadableByteStream._state;if("readable"!==e)throw TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);tR(this)}enqueue(e){if(!tc(this))throw tk("enqueue");if(ek(e,1,"enqueue"),!ArrayBuffer.isView(e))throw TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw TypeError("stream is closed or draining");let t=this._controlledReadableByteStream._state;if("readable"!==t)throw TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);tA(this,e)}error(e){if(!tc(this))throw tk("error");tP(this,e)}[eS](e){th(this),ti(this);let t=this._cancelAlgorithm(e);return tT(this),t}[eE](e){let t=this._controlledReadableByteStream;if(this._queueTotalSize>0)return void tx(this,e);let r=this._autoAllocateChunkSize;if(void 0!==r){let t;try{t=new ArrayBuffer(r)}catch(t){return void e._errorSteps(t)}let n={buffer:t,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(n)}e$(t,e),tf(this)}[eI](){if(this._pendingPullIntos.length>0){let e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new eb,this._pendingPullIntos.push(e)}}}function tc(e){return!!eo(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof tu}function tl(e){return!!eo(e)&&!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof ta}function tf(e){if(function(e){let t=e._controlledReadableByteStream;return"readable"===t._state&&!e._closeRequested&&!!e._started&&!!(eH(t)&&eG(t)>0||tB(t)&&tM(t)>0||tC(e)>0)}(e)){if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0,ed(e._pullAlgorithm(),()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,tf(e)),null),t=>(tP(e,t),null))}}function th(e){tS(e),e._pendingPullIntos=new eb}function td(e,t){let r=!1;"closed"===e._state&&(r=!0);let n=ty(t);"default"===t.readerType?eV(e,n,r):function(e,t,r){let n=e._reader._readIntoRequests.shift();r?n._closeSteps(t):n._chunkSteps(t)}(e,n,r)}function tp(e,t){for(let r=0;r<t.length;++r)td(e,t[r])}function ty(e){let t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function tv(e,t,r,n){e._queue.push({buffer:t,byteOffset:r,byteLength:n}),e._queueTotalSize+=n}function tm(e,t,r,n){let o;try{o=e2(t,r,r+n)}catch(t){throw tP(e,t),t}tv(e,o,0,n)}function tg(e,t){t.bytesFilled>0&&tm(e,t.buffer,t.byteOffset,t.bytesFilled),tO(e)}function tb(e,t){let r=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),n=t.bytesFilled+r,o=r,i=!1,s=n-n%t.elementSize;s>=t.minimumFill&&(o=s-t.bytesFilled,i=!0);let a=e._queue;for(;o>0;){let r=a.peek(),n=Math.min(o,r.byteLength),i=t.byteOffset+t.bytesFilled;eX(t.buffer,i,r.buffer,r.byteOffset,n),r.byteLength===n?a.shift():(r.byteOffset+=n,r.byteLength-=n),e._queueTotalSize-=n,t_(e,n,t),o-=n}return i}function t_(e,t,r){r.bytesFilled+=t}function tw(e){0===e._queueTotalSize&&e._closeRequested?(tT(e),rK(e._controlledReadableByteStream)):tf(e)}function tS(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function tE(e){let t=[];for(;e._pendingPullIntos.length>0&&0!==e._queueTotalSize;){let r=e._pendingPullIntos.peek();tb(e,r)&&(tO(e),t.push(r))}return t}function tI(e,t){let r=e._pendingPullIntos.peek();tS(e),"closed"===e._controlledReadableByteStream._state?function(e,t){"none"===t.readerType&&tO(e);let r=e._controlledReadableByteStream;if(tB(r)){let t=[];for(let n=0;n<tM(r);++n)t.push(tO(e));tp(r,t)}}(e,r):function(e,t,r){if(t_(0,t,r),"none"===r.readerType){tg(e,r);let t=tE(e);return void tp(e._controlledReadableByteStream,t)}if(r.bytesFilled<r.minimumFill)return;tO(e);let n=r.bytesFilled%r.elementSize;if(n>0){let t=r.byteOffset+r.bytesFilled;tm(e,r.buffer,t-n,n)}r.bytesFilled-=n;let o=tE(e);td(e._controlledReadableByteStream,r),tp(e._controlledReadableByteStream,o)}(e,t,r),tf(e)}function tO(e){return e._pendingPullIntos.shift()}function tT(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function tR(e){let t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state){if(e._queueTotalSize>0)e._closeRequested=!0;else{if(e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek();if(t.bytesFilled%t.elementSize!=0){let t=TypeError("Insufficient bytes to fill elements in the given buffer");throw tP(e,t),t}}tT(e),rK(t)}}}function tA(e,t){let r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;let{buffer:n,byteOffset:o,byteLength:i}=t;if(e1(n))throw TypeError("chunk's buffer is detached and so cannot be enqueued");let s=e0(n);if(e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek();if(e1(t.buffer))throw TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");tS(e),t.buffer=e0(t.buffer),"none"===t.readerType&&tg(e,t)}if(eH(r))((function(e){let t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(0===e._queueTotalSize)return;tx(e,t._readRequests.shift())}})(e),0===eG(r))?tv(e,s,o,i):(e._pendingPullIntos.length>0&&tO(e),eV(r,new Uint8Array(s,o,i),!1));else if(tB(r)){tv(e,s,o,i);let t=tE(e);tp(e._controlledReadableByteStream,t)}else tv(e,s,o,i);tf(e)}function tP(e,t){let r=e._controlledReadableByteStream;"readable"===r._state&&(th(e),ti(e),tT(e),rJ(r,t))}function tx(e,t){let r=e._queue.shift();e._queueTotalSize-=r.byteLength,tw(e);let n=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(n)}function tN(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),n=Object.create(ta.prototype);n._associatedReadableByteStreamController=e,n._view=r,e._byobRequest=n}return e._byobRequest}function tC(e){let t=e._controlledReadableByteStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function tL(e,t){let r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t)throw TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===t)throw TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw RangeError("bytesWritten out of range")}r.buffer=e0(r.buffer),tI(e,t)}function tj(e,t){let r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===t.byteLength)throw TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw RangeError("The region specified by view is larger than byobRequest");let n=t.byteLength;r.buffer=e0(t.buffer),tI(e,n)}function tU(e,t,r,n,o,i,s){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,ti(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=i,t._pullAlgorithm=n,t._cancelAlgorithm=o,t._autoAllocateChunkSize=s,t._pendingPullIntos=new eb,e._readableStreamController=t,ed(ef(r()),()=>(t._started=!0,tf(t),null),e=>(tP(t,e),null))}function tD(e){return TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function tk(e){return TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function tq(e,t){e._reader._readIntoRequests.push(t)}function tM(e){return e._reader._readIntoRequests.length}function tB(e){let t=e._reader;return void 0!==t&&!!tF(t)}Object.defineProperties(tu.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),ei(tu.prototype.close,"close"),ei(tu.prototype.enqueue,"enqueue"),ei(tu.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(tu.prototype,Symbol.toStringTag,{value:"ReadableByteStreamController",configurable:!0});class tW{constructor(e){if(ek(e,1,"ReadableStreamBYOBReader"),eW(e,"First parameter"),rG(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");if(!tc(e._readableStreamController))throw TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");eO(this,e),this._readIntoRequests=new eb}get closed(){return tF(this)?this._closedPromise:ec(tG("closed"))}cancel(e){return tF(this)?void 0===this._ownerReadableStream?ec(eA("cancel")):eT(this,e):ec(tG("cancel"))}read(e,t={}){let r,n,o;if(!tF(this))return ec(tG("read"));if(!ArrayBuffer.isView(e))return ec(TypeError("view must be an array buffer view"));if(0===e.byteLength)return ec(TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return ec(TypeError("view's buffer must have non-zero byteLength"));if(e1(e.buffer))return ec(TypeError("view's buffer has been detached"));try{var i,s;i="options",ej(t,i),r={min:eB(null!==(s=null==t?void 0:t.min)&&void 0!==s?s:1,`${i} has member 'min' that`)}}catch(e){return ec(e)}let a=r.min;if(0===a)return ec(TypeError("options.min must be greater than 0"));if(ts(e.constructor)){if(a>e.byteLength)return ec(RangeError("options.min must be less than or equal to view's byteLength"))}else if(a>e.length)return ec(RangeError("options.min must be less than or equal to view's length"));if(void 0===this._ownerReadableStream)return ec(eA("read from"));let u=el((e,t)=>{n=e,o=t});return t$(this,e,a,{_chunkSteps:e=>n({value:e,done:!1}),_closeSteps:e=>n({value:e,done:!0}),_errorSteps:e=>o(e)}),u}releaseLock(){if(!tF(this))throw tG("releaseLock");void 0!==this._ownerReadableStream&&(eR(this),tV(this,TypeError("Reader was released")))}}function tF(e){return!!eo(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof tW}function t$(e,t,r,n){let o=e._ownerReadableStream;o._disturbed=!0,"errored"===o._state?n._errorSteps(o._storedError):function(e,t,r,n){let o;let i=e._controlledReadableByteStream,s=t.constructor,a=ts(s)?1:s.BYTES_PER_ELEMENT,{byteOffset:u,byteLength:c}=t;try{o=e0(t.buffer)}catch(e){return void n._errorSteps(e)}let l={buffer:o,bufferByteLength:o.byteLength,byteOffset:u,byteLength:c,bytesFilled:0,minimumFill:r*a,elementSize:a,viewConstructor:s,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(l),void tq(i,n);if("closed"!==i._state){if(e._queueTotalSize>0){if(tb(e,l)){let t=ty(l);return tw(e),void n._chunkSteps(t)}if(e._closeRequested){let t=TypeError("Insufficient bytes to fill elements in the given buffer");return tP(e,t),void n._errorSteps(t)}}e._pendingPullIntos.push(l),tq(i,n),tf(e)}else{let e=new s(l.buffer,l.byteOffset,0);n._closeSteps(e)}}(o._readableStreamController,t,r,n)}function tV(e,t){let r=e._readIntoRequests;e._readIntoRequests=new eb,r.forEach(e=>{e._errorSteps(t)})}function tG(e){return TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function tH(e,t){let{highWaterMark:r}=e;if(void 0===r)return t;if(tt(r)||r<0)throw RangeError("Invalid highWaterMark");return r}function tK(e){let{size:t}=e;return t||(()=>1)}function tJ(e,t){ej(e,t);let r=null==e?void 0:e.highWaterMark,n=null==e?void 0:e.size;return{highWaterMark:void 0===r?void 0:eM(r),size:void 0===n?void 0:(eU(n,`${t} has member 'size' that`),e=>eM(n(e)))}}function tz(e,t){if(!tZ(e))throw TypeError(`${t} is not a WritableStream.`)}Object.defineProperties(tW.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),ei(tW.prototype.cancel,"cancel"),ei(tW.prototype.read,"read"),ei(tW.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(tW.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});class tY{constructor(e={},t={}){void 0===e?e=null:eD(e,"First parameter");let r=tJ(t,"Second parameter"),n=function(e,t){ej(e,t);let r=null==e?void 0:e.abort,n=null==e?void 0:e.close,o=null==e?void 0:e.start,i=null==e?void 0:e.type,s=null==e?void 0:e.write;return{abort:void 0===r?void 0:(eU(r,`${t} has member 'abort' that`),t=>eg(r,e,[t])),close:void 0===n?void 0:(eU(n,`${t} has member 'close' that`),()=>eg(n,e,[])),start:void 0===o?void 0:(eU(o,`${t} has member 'start' that`),t=>em(o,e,[t])),write:void 0===s?void 0:(eU(s,`${t} has member 'write' that`),(t,r)=>eg(s,e,[t,r])),type:i}}(e,"First parameter");if(tQ(this),void 0!==n.type)throw RangeError("Invalid type is specified");let o=tK(r);!function(e,t,r,n){let o,i;let s=Object.create(ri.prototype);o=void 0!==t.start?()=>t.start(s):()=>{},i=void 0!==t.write?e=>t.write(e,s):()=>ef(void 0),ra(e,s,o,i,void 0!==t.close?()=>t.close():()=>ef(void 0),void 0!==t.abort?e=>t.abort(e):()=>ef(void 0),r,n)}(this,n,tH(r,1),o)}get locked(){if(!tZ(this))throw rd("locked");return tX(this)}abort(e){return tZ(this)?tX(this)?ec(TypeError("Cannot abort a stream that already has a writer")):t0(this,e):ec(rd("abort"))}close(){return tZ(this)?tX(this)?ec(TypeError("Cannot close a stream that already has a writer")):t5(this)?ec(TypeError("Cannot close an already-closing stream")):t1(this):ec(rd("close"))}getWriter(){if(!tZ(this))throw rd("getWriter");return new t7(this)}}function tQ(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new eb,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function tZ(e){return!!eo(e)&&!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof tY}function tX(e){return void 0!==e._writer}function t0(e,t){var r;if("closed"===e._state||"errored"===e._state)return ef(void 0);e._writableStreamController._abortReason=t,null===(r=e._writableStreamController._abortController)||void 0===r||r.abort(t);let n=e._state;if("closed"===n||"errored"===n)return ef(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let o=!1;"erroring"===n&&(o=!0,t=void 0);let i=el((r,n)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:n,_reason:t,_wasAlreadyErroring:o}});return e._pendingAbortRequest._promise=i,o||t3(e,t),i}function t1(e){var t;let r=e._state;if("closed"===r||"errored"===r)return ec(TypeError(`The stream (in ${r} state) is not in the writable state and cannot be closed`));let n=el((t,r)=>{e._closeRequest={_resolve:t,_reject:r}}),o=e._writer;return void 0!==o&&e._backpressure&&"writable"===r&&rE(o),to(t=e._writableStreamController,ro,0),rl(t),n}function t2(e,t){"writable"!==e._state?t4(e):t3(e,t)}function t3(e,t){let r=e._writableStreamController;e._state="erroring",e._storedError=t;let n=e._writer;void 0!==n&&rt(n,t),!(void 0!==e._inFlightWriteRequest||void 0!==e._inFlightCloseRequest)&&r._started&&t4(e)}function t4(e){e._state="errored",e._writableStreamController[ew]();let t=e._storedError;if(e._writeRequests.forEach(e=>{e._reject(t)}),e._writeRequests=new eb,void 0===e._pendingAbortRequest)return void t6(e);let r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring)return r._reject(t),void t6(e);ed(e._writableStreamController[e_](r._reason),()=>(r._resolve(),t6(e),null),t=>(r._reject(t),t6(e),null))}function t5(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function t6(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);let t=e._writer;void 0!==t&&rg(t,e._storedError)}function t8(e,t){let r=e._writer;void 0!==r&&t!==e._backpressure&&(t?r_(r):rE(r)),e._backpressure=t}Object.defineProperties(tY.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),ei(tY.prototype.abort,"abort"),ei(tY.prototype.close,"close"),ei(tY.prototype.getWriter,"getWriter"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(tY.prototype,Symbol.toStringTag,{value:"WritableStream",configurable:!0});class t7{constructor(e){if(ek(e,1,"WritableStreamDefaultWriter"),tz(e,"First parameter"),tX(e))throw TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;let t=e._state;if("writable"===t)!t5(e)&&e._backpressure?r_(this):function(e){r_(e),rE(e)}(this),rm(this);else if("erroring"===t)rw(this,e._storedError),rm(this);else if("closed"===t)(function(e){r_(e),rE(e)})(this),rm(this),rb(this);else{let t=e._storedError;rw(this,t),function(e,t){rm(e),rg(e,t)}(this,t)}}get closed(){return t9(this)?this._closedPromise:ec(ry("closed"))}get desiredSize(){if(!t9(this))throw ry("desiredSize");if(void 0===this._ownerWritableStream)throw rv("desiredSize");return function(e){let t=e._ownerWritableStream,r=t._state;return"errored"===r||"erroring"===r?null:"closed"===r?0:rc(t._writableStreamController)}(this)}get ready(){return t9(this)?this._readyPromise:ec(ry("ready"))}abort(e){return t9(this)?void 0===this._ownerWritableStream?ec(rv("abort")):t0(this._ownerWritableStream,e):ec(ry("abort"))}close(){if(!t9(this))return ec(ry("close"));let e=this._ownerWritableStream;return void 0===e?ec(rv("close")):t5(e)?ec(TypeError("Cannot close an already-closing stream")):re(this)}releaseLock(){if(!t9(this))throw ry("releaseLock");void 0!==this._ownerWritableStream&&rr(this)}write(e){return t9(this)?void 0===this._ownerWritableStream?ec(rv("write to")):rn(this,e):ec(ry("write"))}}function t9(e){return!!eo(e)&&!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof t7}function re(e){return t1(e._ownerWritableStream)}function rt(e,t){"pending"===e._readyPromiseState?rS(e,t):rw(e,t)}function rr(e){let t=e._ownerWritableStream,r=TypeError("Writer was released and can no longer be used to monitor the stream's closedness");rt(e,r),"pending"===e._closedPromiseState||rm(e),rg(e,r),t._writer=void 0,e._ownerWritableStream=void 0}function rn(e,t){let r=e._ownerWritableStream,n=r._writableStreamController,o=function(e,t){if(void 0===e._strategySizeAlgorithm)return 1;try{return e._strategySizeAlgorithm(t)}catch(t){return rf(e,t),1}}(n,t);if(r!==e._ownerWritableStream)return ec(rv("write to"));let i=r._state;if("errored"===i)return ec(r._storedError);if(t5(r)||"closed"===i)return ec(TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===i)return ec(r._storedError);let s=el((e,t)=>{r._writeRequests.push({_resolve:e,_reject:t})});return function(e,t,r){try{to(e,t,r)}catch(t){return void rf(e,t)}let n=e._controlledWritableStream;t5(n)||"writable"!==n._state||t8(n,0>=rc(e)),rl(e)}(n,t,o),s}Object.defineProperties(t7.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),ei(t7.prototype.abort,"abort"),ei(t7.prototype.close,"close"),ei(t7.prototype.releaseLock,"releaseLock"),ei(t7.prototype.write,"write"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(t7.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});let ro={};class ri{constructor(){throw TypeError("Illegal constructor")}get abortReason(){if(!rs(this))throw rp("abortReason");return this._abortReason}get signal(){if(!rs(this))throw rp("signal");if(void 0===this._abortController)throw TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(e){if(!rs(this))throw rp("error");"writable"===this._controlledWritableStream._state&&rh(this,e)}[e_](e){let t=this._abortAlgorithm(e);return ru(this),t}[ew](){ti(this)}}function rs(e){return!!eo(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof ri}function ra(e,t,r,n,o,i,s,a){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,ti(t),t._abortReason=void 0,t._abortController=function(){if("function"==typeof AbortController)return new AbortController}(),t._started=!1,t._strategySizeAlgorithm=a,t._strategyHWM=s,t._writeAlgorithm=n,t._closeAlgorithm=o,t._abortAlgorithm=i,t8(e,0>=rc(t)),ed(ef(r()),()=>(t._started=!0,rl(t),null),r=>(t._started=!0,t2(e,r),null))}function ru(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function rc(e){return e._strategyHWM-e._queueTotalSize}function rl(e){let t=e._controlledWritableStream;if(!e._started||void 0!==t._inFlightWriteRequest)return;if("erroring"===t._state)return void t4(t);if(0===e._queue.length)return;let r=e._queue.peek().value;r===ro?function(e){let t=e._controlledWritableStream;t._inFlightCloseRequest=t._closeRequest,t._closeRequest=void 0,tn(e);let r=e._closeAlgorithm();ru(e),ed(r,()=>((function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";let t=e._writer;void 0!==t&&rb(t)})(t),null),e=>(t._inFlightCloseRequest._reject(e),t._inFlightCloseRequest=void 0,void 0!==t._pendingAbortRequest&&(t._pendingAbortRequest._reject(e),t._pendingAbortRequest=void 0),t2(t,e),null))}(e):function(e,t){let r=e._controlledWritableStream;r._inFlightWriteRequest=r._writeRequests.shift(),ed(e._writeAlgorithm(t),()=>{r._inFlightWriteRequest._resolve(void 0),r._inFlightWriteRequest=void 0;let t=r._state;return tn(e),t5(r)||"writable"!==t||t8(r,0>=rc(e)),rl(e),null},t=>("writable"===r._state&&ru(e),r._inFlightWriteRequest._reject(t),r._inFlightWriteRequest=void 0,t2(r,t),null))}(e,r)}function rf(e,t){"writable"===e._controlledWritableStream._state&&rh(e,t)}function rh(e,t){let r=e._controlledWritableStream;ru(e),t3(r,t)}function rd(e){return TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function rp(e){return TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function ry(e){return TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function rv(e){return TypeError("Cannot "+e+" a stream using a released writer")}function rm(e){e._closedPromise=el((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"})}function rg(e,t){void 0!==e._closedPromise_reject&&(ey(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function rb(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function r_(e){e._readyPromise=el((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r}),e._readyPromiseState="pending"}function rw(e,t){r_(e),rS(e,t)}function rS(e,t){void 0!==e._readyPromise_reject&&(ey(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function rE(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(ri.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ri.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});let rI="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0,rO=function(){let e=null==rI?void 0:rI.DOMException;return!function(e){if("function"!=typeof e&&"object"!=typeof e||"DOMException"!==e.name)return!1;try{return new e,!0}catch(e){return!1}}(e)?void 0:e}()||function(){let e=function(e,t){this.message=e||"",this.name=t||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return ei(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}();function rT(e,t,r,n,o,i){let s=eF(e),a=new t7(t);e._disturbed=!0;let u=!1,c=ef(void 0);return el((l,f)=>{var h,d;let p;if(void 0!==i){if(p=()=>{let r=void 0!==i.reason?i.reason:new rO("Aborted","AbortError"),s=[];n||s.push(()=>"writable"===t._state?t0(t,r):ef(void 0)),o||s.push(()=>"readable"===e._state?rH(e,r):ef(void 0)),m(()=>Promise.all(s.map(e=>e())),!0,r)},i.aborted)return void p();i.addEventListener("abort",p)}if(v(e,s._closedPromise,e=>(n?g(!0,e):m(()=>t0(t,e),!0,e),null)),v(t,a._closedPromise,t=>(o?g(!0,t):m(()=>rH(e,t),!0,t),null)),h=s._closedPromise,d=()=>(r?g():m(()=>(function(e){let t=e._ownerWritableStream,r=t._state;return t5(t)||"closed"===r?ef(void 0):"errored"===r?ec(t._storedError):re(e)})(a)),null),"closed"===e._state?d():ed(h,d),t5(t)||"closed"===t._state){let t=TypeError("the destination writable stream closed before all data could be piped to it");o?g(!0,t):m(()=>rH(e,t),!0,t)}function y(){let e=c;return eh(c,()=>e!==c?y():void 0)}function v(e,t,r){"errored"===e._state?r(e._storedError):ep(t,r)}function m(e,r,n){function o(){return ed(e(),()=>b(r,n),e=>b(!0,e)),null}u||(u=!0,"writable"!==t._state||t5(t)?o():ed(y(),o))}function g(e,r){u||(u=!0,"writable"!==t._state||t5(t)?b(e,r):ed(y(),()=>b(e,r)))}function b(e,t){return rr(a),eR(s),void 0!==i&&i.removeEventListener("abort",p),e?f(t):l(void 0),null}ey(el((e,t)=>{!function r(n){n?e():eh(u?ef(!0):eh(a._readyPromise,()=>el((e,t)=>{ez(s,{_chunkSteps:t=>{c=eh(rn(a,t),void 0,en),e(!1)},_closeSteps:()=>e(!0),_errorSteps:t})})),r,t)}(!1)}))})}class rR{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!rA(this))throw rq("desiredSize");return rU(this)}close(){if(!rA(this))throw rq("close");if(!rD(this))throw TypeError("The stream is not in a state that permits close");rC(this)}enqueue(e){if(!rA(this))throw rq("enqueue");if(!rD(this))throw TypeError("The stream is not in a state that permits enqueue");return rL(this,e)}error(e){if(!rA(this))throw rq("error");rj(this,e)}[eS](e){ti(this);let t=this._cancelAlgorithm(e);return rN(this),t}[eE](e){let t=this._controlledReadableStream;if(this._queue.length>0){let r=tn(this);this._closeRequested&&0===this._queue.length?(rN(this),rK(t)):rP(this),e._chunkSteps(r)}else e$(t,e),rP(this)}[eI](){}}function rA(e){return!!eo(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof rR}function rP(e){if(rx(e)){if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0,ed(e._pullAlgorithm(),()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,rP(e)),null),t=>(rj(e,t),null))}}function rx(e){let t=e._controlledReadableStream;return!!rD(e)&&!!e._started&&(!!(rG(t)&&eG(t)>0)||rU(e)>0)}function rN(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function rC(e){if(!rD(e))return;let t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(rN(e),rK(t))}function rL(e,t){if(!rD(e))return;let r=e._controlledReadableStream;if(rG(r)&&eG(r)>0)eV(r,t,!1);else{let r;try{r=e._strategySizeAlgorithm(t)}catch(t){throw rj(e,t),t}try{to(e,t,r)}catch(t){throw rj(e,t),t}}rP(e)}function rj(e,t){let r=e._controlledReadableStream;"readable"===r._state&&(ti(e),rN(e),rJ(r,t))}function rU(e){let t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function rD(e){let t=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===t}function rk(e,t,r,n,o,i,s){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,ti(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=s,t._strategyHWM=i,t._pullAlgorithm=n,t._cancelAlgorithm=o,e._readableStreamController=t,ed(ef(r()),()=>(t._started=!0,rP(t),null),e=>(rj(t,e),null))}function rq(e){return TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function rM(e,t){ej(e,t);let r=null==e?void 0:e.preventAbort,n=null==e?void 0:e.preventCancel,o=null==e?void 0:e.preventClose,i=null==e?void 0:e.signal;return void 0!==i&&function(e,t){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw TypeError(`${t} is not an AbortSignal.`)}(i,`${t} has member 'signal' that`),{preventAbort:!!r,preventCancel:!!n,preventClose:!!o,signal:i}}Object.defineProperties(rR.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),ei(rR.prototype.close,"close"),ei(rR.prototype.enqueue,"enqueue"),ei(rR.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(rR.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});class rB{constructor(e={},t={}){void 0===e?e=null:eD(e,"First parameter");let r=tJ(t,"Second parameter"),n=function(e,t){ej(e,t);let r=null==e?void 0:e.autoAllocateChunkSize,n=null==e?void 0:e.cancel,o=null==e?void 0:e.pull,i=null==e?void 0:e.start,s=null==e?void 0:e.type;return{autoAllocateChunkSize:void 0===r?void 0:eB(r,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===n?void 0:(eU(n,`${t} has member 'cancel' that`),t=>eg(n,e,[t])),pull:void 0===o?void 0:(eU(o,`${t} has member 'pull' that`),t=>eg(o,e,[t])),start:void 0===i?void 0:(eU(i,`${t} has member 'start' that`),t=>em(i,e,[t])),type:void 0===s?void 0:function(e,t){if("bytes"!=(e=`${e}`))throw TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}(s,`${t} has member 'type' that`)}}(e,"First parameter");if(r$(this),"bytes"===n.type){if(void 0!==r.size)throw RangeError("The strategy for a byte stream cannot have a size function");!function(e,t,r){let n,o,i;let s=Object.create(tu.prototype);n=void 0!==t.start?()=>t.start(s):()=>{},o=void 0!==t.pull?()=>t.pull(s):()=>ef(void 0),i=void 0!==t.cancel?e=>t.cancel(e):()=>ef(void 0);let a=t.autoAllocateChunkSize;if(0===a)throw TypeError("autoAllocateChunkSize must be greater than 0");tU(e,s,n,o,i,r,a)}(this,n,tH(r,0))}else{let e=tK(r);!function(e,t,r,n){let o,i;let s=Object.create(rR.prototype);o=void 0!==t.start?()=>t.start(s):()=>{},i=void 0!==t.pull?()=>t.pull(s):()=>ef(void 0),rk(e,s,o,i,void 0!==t.cancel?e=>t.cancel(e):()=>ef(void 0),r,n)}(this,n,tH(r,1),e)}}get locked(){if(!rV(this))throw rz("locked");return rG(this)}cancel(e){return rV(this)?rG(this)?ec(TypeError("Cannot cancel a stream that already has a reader")):rH(this,e):ec(rz("cancel"))}getReader(e){if(!rV(this))throw rz("getReader");return void 0===function(e,t){ej(e,t);let r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:function(e,t){if("byob"!=(e=`${e}`))throw TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}(r,`${t} has member 'mode' that`)}}(e,"First parameter").mode?eF(this):new tW(this)}pipeThrough(e,t={}){if(!rV(this))throw rz("pipeThrough");ek(e,1,"pipeThrough");let r=function(e,t){ej(e,t);let r=null==e?void 0:e.readable;eq(r,"readable","ReadableWritablePair"),eW(r,`${t} has member 'readable' that`);let n=null==e?void 0:e.writable;return eq(n,"writable","ReadableWritablePair"),tz(n,`${t} has member 'writable' that`),{readable:r,writable:n}}(e,"First parameter"),n=rM(t,"Second parameter");if(rG(this))throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(tX(r.writable))throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return ey(rT(this,r.writable,n.preventClose,n.preventAbort,n.preventCancel,n.signal)),r.readable}pipeTo(e,t={}){let r;if(!rV(this))return ec(rz("pipeTo"));if(void 0===e)return ec("Parameter 1 is required in 'pipeTo'.");if(!tZ(e))return ec(TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));try{r=rM(t,"Second parameter")}catch(e){return ec(e)}return rG(this)?ec(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):tX(e)?ec(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):rT(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!rV(this))throw rz("tee");return eZ(tc(this._readableStreamController)?function(e){let t,r,n,o,i,s=eF(e),a=!1,u=!1,c=!1,l=!1,f=!1,h=el(e=>{i=e});function d(e){ep(e._closedPromise,t=>(e!==s||(tP(n._readableStreamController,t),tP(o._readableStreamController,t),l&&f||i(void 0)),null))}function p(){tF(s)&&(eR(s),d(s=eF(e))),ez(s,{_chunkSteps:t=>{ev(()=>{u=!1,c=!1;let r=t;if(!l&&!f)try{r=tr(t)}catch(t){return tP(n._readableStreamController,t),tP(o._readableStreamController,t),void i(rH(e,t))}l||tA(n._readableStreamController,t),f||tA(o._readableStreamController,r),a=!1,u?v():c&&m()})},_closeSteps:()=>{a=!1,l||tR(n._readableStreamController),f||tR(o._readableStreamController),n._readableStreamController._pendingPullIntos.length>0&&tL(n._readableStreamController,0),o._readableStreamController._pendingPullIntos.length>0&&tL(o._readableStreamController,0),l&&f||i(void 0)},_errorSteps:()=>{a=!1}})}function y(t,r){eJ(s)&&(eR(s),d(s=new tW(e)));let h=r?o:n,p=r?n:o;t$(s,t,1,{_chunkSteps:t=>{ev(()=>{u=!1,c=!1;let n=r?f:l;if(r?l:f)n||tj(h._readableStreamController,t);else{let r;try{r=tr(t)}catch(t){return tP(h._readableStreamController,t),tP(p._readableStreamController,t),void i(rH(e,t))}n||tj(h._readableStreamController,t),tA(p._readableStreamController,r)}a=!1,u?v():c&&m()})},_closeSteps:e=>{a=!1;let t=r?f:l,n=r?l:f;t||tR(h._readableStreamController),n||tR(p._readableStreamController),void 0!==e&&(t||tj(h._readableStreamController,e),!n&&p._readableStreamController._pendingPullIntos.length>0&&tL(p._readableStreamController,0)),t&&n||i(void 0)},_errorSteps:()=>{a=!1}})}function v(){if(a)return u=!0,ef(void 0);a=!0;let e=tN(n._readableStreamController);return null===e?p():y(e._view,!1),ef(void 0)}function m(){if(a)return c=!0,ef(void 0);a=!0;let e=tN(o._readableStreamController);return null===e?p():y(e._view,!0),ef(void 0)}function g(){}return n=rF(g,v,function(n){if(l=!0,t=n,f){let n=rH(e,eZ([t,r]));i(n)}return h}),o=rF(g,m,function(n){if(f=!0,r=n,l){let n=rH(e,eZ([t,r]));i(n)}return h}),d(s),[n,o]}(this):function(e,t){let r=eF(e),n,o,i,s,a,u=!1,c=!1,l=!1,f=!1,h=el(e=>{a=e});function d(){return u?c=!0:(u=!0,ez(r,{_chunkSteps:e=>{ev(()=>{c=!1,l||rL(i._readableStreamController,e),f||rL(s._readableStreamController,e),u=!1,c&&d()})},_closeSteps:()=>{u=!1,l||rC(i._readableStreamController),f||rC(s._readableStreamController),l&&f||a(void 0)},_errorSteps:()=>{u=!1}})),ef(void 0)}function p(){}return i=rW(p,d,function(t){if(l=!0,n=t,f){let t=rH(e,eZ([n,o]));a(t)}return h}),s=rW(p,d,function(t){if(f=!0,o=t,l){let t=rH(e,eZ([n,o]));a(t)}return h}),ep(r._closedPromise,e=>(rj(i._readableStreamController,e),rj(s._readableStreamController,e),l&&f||a(void 0),null)),[i,s]}(this))}values(e){if(!rV(this))throw rz("values");return function(e,t){let r=new e8(eF(e),t),n=Object.create(e7);return n._asyncIteratorImpl=r,n}(this,(ej(e,"First parameter"),{preventCancel:!!(null==e?void 0:e.preventCancel)}).preventCancel)}[e5](e){return this.values(e)}static from(e){var t;let r;return eo(e)&&void 0!==e.getReader?(t=e.getReader(),r=rW(en,function(){let e;try{e=t.read()}catch(e){return ec(e)}return eh(e,e=>{if(!eo(e))throw TypeError("The promise returned by the reader.read() method must fulfill with an object");if(e.done)rC(r._readableStreamController);else{let t=e.value;rL(r._readableStreamController,t)}},void 0)},function(e){try{return ef(t.cancel(e))}catch(e){return ec(e)}},0)):function(e){let t;let r=function e(t,r="sync",n){if(void 0===n){if("async"===r){if(void 0===(n=e3(t,e5)))return function(e){let t={next(){let t;try{t=e6(e)}catch(e){return ec(e)}return e4(t)},return(t){let r;try{let n=e3(e.iterator,"return");if(void 0===n)return ef({done:!0,value:t});r=em(n,e.iterator,[t])}catch(e){return ec(e)}return eo(r)?e4(r):ec(TypeError("The iterator.return() method must return an object"))}};return{iterator:t,nextMethod:t.next,done:!1}}(e(t,"sync",e3(t,Symbol.iterator)))}else n=e3(t,Symbol.iterator)}if(void 0===n)throw TypeError("The object is not iterable");let o=em(n,t,[]);if(!eo(o))throw TypeError("The iterator method must return an object");return{iterator:o,nextMethod:o.next,done:!1}}(e,"async");return t=rW(en,function(){let e;try{e=e6(r)}catch(e){return ec(e)}return eh(ef(e),e=>{if(!eo(e))throw TypeError("The promise returned by the iterator.next() method must fulfill with an object");if(e.done)rC(t._readableStreamController);else{let r=e.value;rL(t._readableStreamController,r)}},void 0)},function(e){let t;let n=r.iterator;try{t=e3(n,"return")}catch(e){return ec(e)}return void 0===t?ef(void 0):eh(eg(t,n,[e]),e=>{if(!eo(e))throw TypeError("The promise returned by the iterator.return() method must fulfill with an object")},void 0)},0)}(e)}}function rW(e,t,r,n=1,o=()=>1){let i=Object.create(rB.prototype);return r$(i),rk(i,Object.create(rR.prototype),e,t,r,n,o),i}function rF(e,t,r){let n=Object.create(rB.prototype);return r$(n),tU(n,Object.create(tu.prototype),e,t,r,0,void 0),n}function r$(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function rV(e){return!!eo(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof rB}function rG(e){return void 0!==e._reader}function rH(e,t){if(e._disturbed=!0,"closed"===e._state)return ef(void 0);if("errored"===e._state)return ec(e._storedError);rK(e);let r=e._reader;if(void 0!==r&&tF(r)){let e=r._readIntoRequests;r._readIntoRequests=new eb,e.forEach(e=>{e._closeSteps(void 0)})}return eh(e._readableStreamController[eS](t),en,void 0)}function rK(e){e._state="closed";let t=e._reader;if(void 0!==t&&(eN(t),eJ(t))){let e=t._readRequests;t._readRequests=new eb,e.forEach(e=>{e._closeSteps()})}}function rJ(e,t){e._state="errored",e._storedError=t;let r=e._reader;void 0!==r&&(ex(r,t),eJ(r)?eY(r,t):tV(r,t))}function rz(e){return TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function rY(e,t){ej(e,t);let r=null==e?void 0:e.highWaterMark;return eq(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:eM(r)}}Object.defineProperties(rB,{from:{enumerable:!0}}),Object.defineProperties(rB.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),ei(rB.from,"from"),ei(rB.prototype.cancel,"cancel"),ei(rB.prototype.getReader,"getReader"),ei(rB.prototype.pipeThrough,"pipeThrough"),ei(rB.prototype.pipeTo,"pipeTo"),ei(rB.prototype.tee,"tee"),ei(rB.prototype.values,"values"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(rB.prototype,Symbol.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(rB.prototype,e5,{value:rB.prototype.values,writable:!0,configurable:!0});let rQ=e=>e.byteLength;ei(rQ,"size");class rZ{constructor(e){ek(e,1,"ByteLengthQueuingStrategy"),e=rY(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!r0(this))throw rX("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!r0(this))throw rX("size");return rQ}}function rX(e){return TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function r0(e){return!!eo(e)&&!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof rZ}Object.defineProperties(rZ.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(rZ.prototype,Symbol.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});let r1=()=>1;ei(r1,"size");class r2{constructor(e){ek(e,1,"CountQueuingStrategy"),e=rY(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!r4(this))throw r3("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!r4(this))throw r3("size");return r1}}function r3(e){return TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function r4(e){return!!eo(e)&&!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof r2}Object.defineProperties(r2.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(r2.prototype,Symbol.toStringTag,{value:"CountQueuingStrategy",configurable:!0});class r5{constructor(e={},t={},r={}){let n;void 0===e&&(e=null);let o=tJ(t,"Second parameter"),i=tJ(r,"Third parameter"),s=function(e,t){ej(e,t);let r=null==e?void 0:e.cancel,n=null==e?void 0:e.flush,o=null==e?void 0:e.readableType,i=null==e?void 0:e.start,s=null==e?void 0:e.transform,a=null==e?void 0:e.writableType;return{cancel:void 0===r?void 0:(eU(r,`${t} has member 'cancel' that`),t=>eg(r,e,[t])),flush:void 0===n?void 0:(eU(n,`${t} has member 'flush' that`),t=>eg(n,e,[t])),readableType:o,start:void 0===i?void 0:(eU(i,`${t} has member 'start' that`),t=>em(i,e,[t])),transform:void 0===s?void 0:(eU(s,`${t} has member 'transform' that`),(t,r)=>eg(s,e,[t,r])),writableType:a}}(e,"First parameter");if(void 0!==s.readableType)throw RangeError("Invalid readableType specified");if(void 0!==s.writableType)throw RangeError("Invalid writableType specified");let a=tH(i,0),u=tK(i),c=tH(o,1),l=tK(o);(function(e,t,r,n,o,i){function s(){return t}e._writable=function(e,t,r,n,o=1,i=()=>1){let s=Object.create(tY.prototype);return tQ(s),ra(s,Object.create(ri.prototype),e,t,r,n,o,i),s}(s,function(t){return function(e,t){let r=e._transformStreamController;return e._backpressure?eh(e._backpressureChangePromise,()=>{let n=e._writable;if("erroring"===n._state)throw n._storedError;return ni(r,t)},void 0):ni(r,t)}(e,t)},function(){return function(e){let t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;let r=e._readable;t._finishPromise=el((e,r)=>{t._finishPromise_resolve=e,t._finishPromise_reject=r});let n=t._flushAlgorithm();return nn(t),ed(n,()=>("errored"===r._state?nu(t,r._storedError):(rC(r._readableStreamController),na(t)),null),e=>(rj(r._readableStreamController,e),nu(t,e),null)),t._finishPromise}(e)},function(t){return function(e,t){let r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;let n=e._readable;r._finishPromise=el((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t});let o=r._cancelAlgorithm(t);return nn(r),ed(o,()=>("errored"===n._state?nu(r,n._storedError):(rj(n._readableStreamController,t),na(r)),null),e=>(rj(n._readableStreamController,e),nu(r,e),null)),r._finishPromise}(e,t)},r,n),e._readable=rW(s,function(){return ne(e,!1),e._backpressureChangePromise},function(t){return function(e,t){let r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;let n=e._writable;r._finishPromise=el((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t});let o=r._cancelAlgorithm(t);return nn(r),ed(o,()=>("errored"===n._state?nu(r,n._storedError):(rf(n._writableStreamController,t),r9(e),na(r)),null),t=>(rf(n._writableStreamController,t),r9(e),nu(r,t),null)),r._finishPromise}(e,t)},o,i),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,ne(e,!0),e._transformStreamController=void 0})(this,el(e=>{n=e}),c,l,a,u),function(e,t){let r,n,o;let i=Object.create(nt.prototype);r=void 0!==t.transform?e=>t.transform(e,i):e=>{try{return no(i,e),ef(void 0)}catch(e){return ec(e)}},n=void 0!==t.flush?()=>t.flush(i):()=>ef(void 0),o=void 0!==t.cancel?e=>t.cancel(e):()=>ef(void 0),i._controlledTransformStream=e,e._transformStreamController=i,i._transformAlgorithm=r,i._flushAlgorithm=n,i._cancelAlgorithm=o,i._finishPromise=void 0,i._finishPromise_resolve=void 0,i._finishPromise_reject=void 0}(this,s),void 0!==s.start?n(s.start(this._transformStreamController)):n(void 0)}get readable(){if(!r6(this))throw nc("readable");return this._readable}get writable(){if(!r6(this))throw nc("writable");return this._writable}}function r6(e){return!!eo(e)&&!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof r5}function r8(e,t){rj(e._readable._readableStreamController,t),r7(e,t)}function r7(e,t){nn(e._transformStreamController),rf(e._writable._writableStreamController,t),r9(e)}function r9(e){e._backpressure&&ne(e,!1)}function ne(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=el(t=>{e._backpressureChangePromise_resolve=t}),e._backpressure=t}Object.defineProperties(r5.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(r5.prototype,Symbol.toStringTag,{value:"TransformStream",configurable:!0});class nt{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!nr(this))throw ns("desiredSize");return rU(this._controlledTransformStream._readable._readableStreamController)}enqueue(e){if(!nr(this))throw ns("enqueue");no(this,e)}error(e){if(!nr(this))throw ns("error");r8(this._controlledTransformStream,e)}terminate(){if(!nr(this))throw ns("terminate");!function(e){let t=e._controlledTransformStream;rC(t._readable._readableStreamController),r7(t,TypeError("TransformStream terminated"))}(this)}}function nr(e){return!!eo(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof nt}function nn(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}function no(e,t){let r=e._controlledTransformStream,n=r._readable._readableStreamController;if(!rD(n))throw TypeError("Readable side is not in a state that permits enqueue");try{rL(n,t)}catch(e){throw r7(r,e),r._readable._storedError}!rx(n)!==r._backpressure&&ne(r,!0)}function ni(e,t){return eh(e._transformAlgorithm(t),void 0,t=>{throw r8(e._controlledTransformStream,t),t})}function ns(e){return TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function na(e){void 0!==e._finishPromise_resolve&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function nu(e,t){void 0!==e._finishPromise_reject&&(ey(e._finishPromise),e._finishPromise_reject(t),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function nc(e){return TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}Object.defineProperties(nt.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),ei(nt.prototype.enqueue,"enqueue"),ei(nt.prototype.error,"error"),ei(nt.prototype.terminate,"terminate"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(nt.prototype,Symbol.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});var nl=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),nf=function(){return(nf=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},nh=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(r)throw TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,n=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}},nd="Error when aborting requestTask",np=function(e){function t(t){void 0===t&&(t={});var r=e.call(this)||this,n=t.timeout,o=t.timeoutMsg,i=t.restrictedMethods;return r._timeout=n||0,r._timeoutMsg=o||"请求超时",r._restrictedMethods=i||["get","post","upload","download"],r}return nl(t,e),t.prototype.post=function(e){var t=this;return new Promise(function(r,n){var o=e.url,i=e.data,s=e.headers,a=wx.request({url:(0,N.formatUrl)("https:",o),data:i,timeout:t._timeout,method:"POST",header:s,success:function(e){r(e)},fail:function(e){n(e)},complete:function(e){if(e&&e.errMsg&&t._timeout&&-1!==t._restrictedMethods.indexOf("post")&&"request:fail timeout"===e.errMsg){console.warn(t._timeoutMsg);try{a.abort()}catch(e){}}}})})},t.prototype.upload=function(e){var t=this,r=this;return new Promise(function(n){var o,i,s;return o=void 0,i=void 0,s=function(){var t,o,i,s,a,u;return nh(this,function(c){return t=e.url,o=e.file,i=e.data,s=e.headers,a=e.onUploadProgress,u=wx.uploadFile({url:t,filePath:o,name:"file",formData:nf({},i),header:s,timeout:this._timeout,success:function(e){var t={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&i.success_action_status&&(t.statusCode=parseInt(i.success_action_status,10)),n(t)},fail:function(e){n(e)},complete:function(e){if(e&&e.errMsg&&r._timeout&&-1!==r._restrictedMethods.indexOf("upload")&&"request:fail timeout"===e.errMsg){console.warn(r._timeoutMsg);try{u.abort()}catch(e){}}}}),a&&u.onProgressUpdate(function(e){a(e)}),[2]})},new(i||(i=Promise))(function(e,r){function n(e){try{u(s.next(e))}catch(e){r(e)}}function a(e){try{u(s.throw(e))}catch(e){r(e)}}function u(t){var r;t.done?e(t.value):((r=t.value)instanceof i?r:new i(function(e){e(r)})).then(n,a)}u((s=s.apply(t,o||[])).next())})})},t.prototype.download=function(e){var t=this,r=this;return new Promise(function(n,o){var i=e.url,s=e.headers,a=wx.downloadFile({url:(0,N.formatUrl)("https:",i),header:s,timeout:t._timeout,success:function(e){200===e.statusCode&&e.tempFilePath?n({statusCode:200,tempFilePath:e.tempFilePath}):n(e)},fail:function(e){o(e)},complete:function(e){if(e&&e.errMsg&&r._timeout&&-1!==r._restrictedMethods.indexOf("download")&&"request:fail timeout"===e.errMsg){console.warn(r._timeoutMsg);try{a.abort()}catch(e){}}}})})},t.prototype.fetch=function(e){var t=e.url,r=e.body,n=e.enableAbort,o=e.headers,i=e.method,s=e.stream,a=void 0!==s&&s,u=e.signal,c=e.timeout,l=this,f=null!=c?c:this._timeout,h=null,d=new rB({start:function(e){h=e},cancel:function(){h=null}});return new Promise(function(e,s){a&&e({data:d});var c=wx.request({url:(0,N.formatUrl)("https:",t),data:r,timeout:f,method:i.toUpperCase(),header:o,success:function(t){var r;null===(r=h)||void 0===r||r.close(),a||e(t)},fail:function(e){var t;if(null===(t=h)||void 0===t||t.close(),s(e),a)throw e},complete:function(e){if(e&&e.errMsg&&f&&-1!==l._restrictedMethods.indexOf("post")&&n&&"request:fail timeout"===e.errMsg){console.warn(l._timeoutMsg);try{c.abort()}catch(e){console.warn(nd,e)}}},enableChunked:a});if(c.onChunkReceived(function(e){var t;null===(t=h)||void 0===t||t.enqueue(new Uint8Array(e.data))}),u){var p=function(){try{c.abort()}catch(e){console.warn(nd,e)}};u.aborted?p():u.addEventListener("abort",function(){return p()})}})},t}(N.AbstractSDKRequest),ny={setItem:function(e,t){wx.setStorageSync(e,t)},getItem:function(e){return wx.getStorageSync(e)},removeItem:function(e){wx.removeStorageSync(e)},clear:function(){wx.clearStorageSync()}},nv=function(e,t){void 0===t&&(t={});var r=wx.connectSocket(nf({url:e},t));return{set onopen(cb){r.onOpen(cb)},set onmessage(cb){r.onMessage(cb)},set onclose(cb){r.onClose(cb)},set onerror(cb){r.onError(cb)},send:function(e){return r.send({data:e})},close:function(e,t){return r.close({code:e,reason:t})},get readyState(){return r.readyState},CONNECTING:0,OPEN:1,CLOSING:2,CLOSED:3}};let nm={genAdapter:function(){return{root:{},reqClass:np,wsClass:nv,localStorage:ny,primaryStorage:N.StorageType.local,getAppSign:function(){var e=wx.getAccountInfoSync();return"undefined"!=typeof App||"undefined"!=typeof getApp||wx.onAppHide||wx.offAppHide||wx.onAppShow||wx.offAppShow?e&&e.miniProgram?e.miniProgram.appId:"":e&&e.plugin?e.plugin.appId:""}}},isMatch:function(){if("undefined"==typeof wx||"undefined"==typeof Page||!wx.getSystemInfoSync||!wx.getStorageSync||!wx.setStorageSync||!wx.connectSocket||!wx.request)return!1;try{if(!wx.getSystemInfoSync()||"qq"===wx.getSystemInfoSync().AppPlatform)return!1}catch(e){return!1}return!0},runtime:"wx_mp"};var ng=function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))},nb={},n_={},nw=function(){return(nw=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},nS={},nE={};function nI(e){var t=e.env,r=e.platformInfo,n={userInfoKey:"".concat("user_info","_").concat(t)};nS[t]=nS[t]||new K(nw(nw({},e),{keys:n,platformInfo:r})),nE[t]=nE[t]||new K(nw(nw({},e),{keys:n,platformInfo:r,persistence:"local"}))}var nO=function(){return(nO=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},nT=function(e){R=e},nR=function(e){A=e},nA="",nP="@cloudbase/js-sdk",nx=[],nN=["env","endPointKey","region"],nC="https:";function nL(e){return nx.find(function(t){return nN.filter(function(t){return null!=e[t]}).every(function(r){return t[r]===e[r]})})}function nj(e){var t,r,n=nL(e);n?(null!=e.baseUrl&&(n.baseUrl=e.baseUrl),null!=e.protocol&&(n.protocol=e.protocol)):nx.push(nO(nO({},e),{protocol:null!==(t=e.protocol)&&void 0!==t?t:nC})),"CLOUD_API"===e.endPointKey&&nR(null!==(r=e.protocol)&&void 0!==r?r:nC)}function nU(e,t,r){return nL({env:e,endPointKey:t,region:r})}function nD(e){var t=nU(e,"CLOUD_API"),r=t.protocol,n=t.baseUrl;return"".concat(r).concat(n).match(/(http(s)?:)?\/\/([^/?#]*)/)[0]}!function(e){e.NULL="NULL",e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.PHONE="PHONE"}(a||(a={}));var nk=function(){return(nk=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},nq=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},nM=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},nB=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r},nW=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"];function nF(e,t,r){var n=e[t];e[t]=function(t){var o={},i={};r.forEach(function(r){var n=r.call(e,t),s=n.data,a=n.headers;Object.assign(o,s),Object.assign(i,a)});var s=t.data;return s&&function(){if(j(s)){Object.keys(o).forEach(function(e){s.append(e,o[e])});return}t.data=nk(nk({},s),o)}(),t.headers=nk(nk({},t.headers||{}),i),n.call(e,t)}}function n$(){var e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:{"X-SDK-Version":"@cloudbase/js-sdk/".concat(nA),"x-seqid":e}}}var nV=function(){function e(e){this.throwWhenRequestFail=!1,this.config=e;var t={timeout:this.config.timeout,timeoutMsg:"[@cloudbase/js-sdk] 请求在".concat(this.config.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post","put"]};this.reqClass=new n_.adapter.reqClass(t),this.throwWhenRequestFail=e.throw||!1,this.localCache=nE[this.config.env],nF(this.reqClass,"post",[n$]),nF(this.reqClass,"upload",[n$]),nF(this.reqClass,"download",[n$])}return e.prototype.post=function(e){return nq(this,void 0,void 0,function(){return nM(this,function(t){switch(t.label){case 0:return[4,this.reqClass.post(e)];case 1:return[2,t.sent()]}})})},e.prototype.upload=function(e){return nq(this,void 0,void 0,function(){return nM(this,function(t){switch(t.label){case 0:return[4,this.reqClass.upload(e)];case 1:return[2,t.sent()]}})})},e.prototype.download=function(e){return nq(this,void 0,void 0,function(){return nM(this,function(t){switch(t.label){case 0:return[4,this.reqClass.download(e)];case 1:return[2,t.sent()]}})})},e.prototype.getBaseEndPoint=function(){return nD(this.config.env)},e.prototype.getOauthAccessTokenV2=function(e){return nq(this,void 0,void 0,function(){var t,r;return nM(this,function(n){switch(n.label){case 0:return[4,e.getAccessToken()];case 1:return t=n.sent(),[4,e.getCredentials()];case 2:return r=n.sent(),[2,{accessToken:t,accessTokenExpire:new Date(r.expires_at).getTime()}]}})})},e.prototype.request=function(e,t,r){var n,o;return nq(this,void 0,void 0,function(){var i,s,a,u,c,l,f,h,d,p,y,v,m,g,b,_,w,S,E;return nM(this,function(I){switch(I.label){case 0:if(i="x-tcb-trace_".concat(this.config.env),s="application/x-www-form-urlencoded",a=nk({action:e,dataVersion:"2020-01-10",env:this.config.env},t),-1!==nW.indexOf(e))return[3,2];if(!(u=this.config._fromApp).oauthInstance)throw Error("you can't request without auth");return c=u.oauthInstance.oauth2client,l=a,[4,this.getOauthAccessTokenV2(c)];case 1:l.access_token=I.sent().accessToken,I.label=2;case 2:return"storage.uploadFile"===e?(Object.keys(f=new FormData).forEach(function(e){Object.prototype.hasOwnProperty.call(f,e)&&void 0!==f[e]&&f.append(e,a[e])}),s="multipart/form-data"):(s="application/json;charset=UTF-8",f={},Object.keys(a).forEach(function(e){void 0!==a[e]&&(f[e]=a[e])})),h={headers:{"content-type":s}},(null==r?void 0:r.onUploadProgress)&&(h.onUploadProgress=r.onUploadProgress),this.config.region&&(h.headers["X-TCB-Region"]=this.config.region),(d=this.localCache.getStore(i))&&(h.headers["X-TCB-Trace"]=d),p=(null==r?void 0:r.parse)!==void 0?r.parse:t.parse,y=(null==r?void 0:r.inQuery)!==void 0?r.inQuery:t.inQuery,v=(null==r?void 0:r.search)!==void 0?r.search:t.search,m=nk(nk({},(null==r?void 0:r.defaultQuery)||{}),{env:this.config.env}),p&&(m.parse=!0),y&&(m=nk(nk({},y),m)),b=(g=nU(this.config.env,"CLOUD_API")).baseUrl,_=g.protocol,w=r.pathname?U(_,"".concat(null===(n=nD(this.config.env))||void 0===n?void 0:n.replace(/^https?:/,""),"/").concat(r.pathname),m):U(_,b,m),v&&(w+=v),[4,this.post(nk({url:w,data:f},h))];case 3:if((E=null===(o=(S=I.sent()).header)||void 0===o?void 0:o["x-tcb-trace"])&&this.localCache.setStore(i,E),200!==Number(S.status)&&200!==Number(S.statusCode)||!S.data)throw Error("network request error");return[2,S]}})})},e.prototype.fetch=function(e){var t,r,n,o,i,s,a,u;return nq(this,void 0,void 0,function(){var c,l,f,h,d,p,y,v=this;return nM(this,function(m){switch(m.label){case 0:c=e.token,f=void 0===(l=e.headers)?{}:l,h=nB(e,["token","headers"]),d=function(){return nq(v,void 0,void 0,function(){var e,t;return nM(this,function(r){switch(r.label){case 0:if(null!=c)return[2,c];if(!(e=this.config._fromApp).oauthInstance)throw Error("you can't request without auth");return t=e.oauthInstance.oauth2client,[4,this.getOauthAccessTokenV2(t)];case 1:return[2,r.sent().accessToken]}})})},p=function(){return nq(v,void 0,void 0,function(){var e,t,r,n,o;return nM(this,function(i){switch(i.label){case 0:return t=(e=this.reqClass).fetch,n={},o={"X-SDK-Version":"@cloudbase/js-sdk/".concat(nA)},r="Bearer ".concat,[4,d()];case 1:return[2,t.apply(e,[nk.apply(void 0,[(n.headers=nk.apply(void 0,[(o.Authorization=r.apply("Bearer ",[i.sent()]),o),f]),n),h])])]}})})},m.label=1;case 1:return m.trys.push([1,3,,6]),[4,p()];case 2:return[2,m.sent()];case 3:if((null==(y=m.sent())?void 0:y.code)!=="ACCESS_TOKEN_EXPIRED")return[3,5];if("function"!=typeof(null===(o=null===(n=null===(r=null===(t=this.config)||void 0===t?void 0:t._fromApp)||void 0===r?void 0:r.oauthInstance)||void 0===n?void 0:n.authApi)||void 0===o?void 0:o.refreshTokenForce))throw y;return[4,null===(u=null===(a=null===(s=null===(i=this.config)||void 0===i?void 0:i._fromApp)||void 0===s?void 0:s.oauthInstance)||void 0===a?void 0:a.authApi)||void 0===u?void 0:u.refreshTokenForce()];case 4:return m.sent(),[2,p()];case 5:throw y;case 6:return[2]}})})},e.prototype.send=function(e,t,r){return void 0===t&&(t={}),void 0===r&&(r={}),nq(this,void 0,void 0,function(){var n;return nM(this,function(o){switch(o.label){case 0:return[4,this.request(e,t,nk(nk({},r),{onUploadProgress:t.onUploadProgress}))];case 1:if((n=o.sent()).data.code&&this.throwWhenRequestFail)throw Error(JSON.stringify({code:x.OPERATION_FAIL,msg:"[".concat(n.data.code,"] ").concat(n.data.message)}));return[2,n.data]}})})},e}(),nG={},nH=function(){return(nH=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},nK=function(e,t,r,n){var o,i=arguments.length,s=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,r,n);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(i<3?o(s):i>3?o(t,r,s):o(t,r))||s);return i>3&&s&&Object.defineProperty(t,r,s),s},nJ=function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},nz=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},nY=function(e){for(var t=C(e)?e:[e],r=0;r<t.length;r++){var n=t[r],o=n.isMatch,i=n.genAdapter,s=n.runtime;if(o())return{adapter:i(),runtime:s}}},nQ={timeout:15e3,persistence:"local"},nZ={},nX=new(function(){function e(e){this.cloudbaseConfig=e||this.cloudbaseConfig,this.authInstance=null,this.oauthInstance=null,this.version=nA}return Object.defineProperty(e.prototype,"config",{get:function(){return this.cloudbaseConfig},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"platform",{get:function(){return n_},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"cache",{get:function(){return nS[this.cloudbaseConfig.env]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"localCache",{get:function(){return nE[this.cloudbaseConfig.env]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"request",{get:function(){return nG[this.cloudbaseConfig.env]},enumerable:!1,configurable:!0}),e.prototype.init=function(t){if(!t.env)throw Error(JSON.stringify({code:x.INVALID_PARAMS,msg:"env must not be specified"}));n_.adapter||this.useDefaultAdapter();var r,n,o,i,s={timeout:t.timeout||5e3,timeoutMsg:"[".concat(nP,"][REQUEST TIMEOUT] request had been abort since didn't finished within").concat(t.timeout/1e3,"s")};this.requestClient=new n_.adapter.reqClass(s),this.cloudbaseConfig=nH(nH({},nQ),t),this.cloudbaseConfig.timeout=this.formatTimeout(this.cloudbaseConfig.timeout);var a=this.cloudbaseConfig,u=a.env,c=a.persistence,l=a.debug,f=a.timeout,h=a.oauthClient;nI({env:u,persistence:c,debug:l,platformInfo:this.platform}),n=(r=t.region||"")?"//".concat(u,".").concat(r,".tcb-api.tencentcloudapi.com/web"):"//".concat(u,".ap-shanghai.tcb-api.tencentcloudapi.com/web"),nj({env:u,region:r,baseUrl:n,protocol:void 0,endPointKey:"CLOUD_API"}),nj({endPointKey:"GATEWAY",env:u,baseUrl:"//".concat(u,".api.tcloudbasegateway.com/v1"),protocol:void 0});var d=new e(this.cloudbaseConfig);return nG[(o={env:u,region:t.region||"",timeout:f,oauthClient:h,_fromApp:d}).env]=new nV(nk(nk({},o),{throw:!0})),d.requestClient=this.requestClient,null===(i=this===null||void 0===this?void 0:this.fire)||void 0===i||i.call(this,"cloudbase_init",d),d},e.prototype.updateConfig=function(e){var t=e.persistence,r=e.debug;this.cloudbaseConfig.persistence=t,this.cloudbaseConfig.debug=r,nI({env:this.cloudbaseConfig.env,persistence:t,debug:r,platformInfo:this.platform})},e.prototype.registerExtension=function(e){nZ[e.name]=e},e.prototype.invokeExtension=function(e,t){var r,n,o,i;return r=this,n=void 0,o=void 0,i=function(){var r;return nz(this,function(n){switch(n.label){case 0:if(!(r=nZ[e]))throw Error(JSON.stringify({code:x.INVALID_PARAMS,msg:"extension:".concat(e," must be registered before invoke")}));return[4,r.invoke(t,this)];case 1:return[2,n.sent()]}})},new(o||(o=Promise))(function(e,t){function s(e){try{u(i.next(e))}catch(e){t(e)}}function a(e){try{u(i.throw(e))}catch(e){t(e)}}function u(t){var r;t.done?e(t.value):((r=t.value)instanceof o?r:new o(function(e){e(r)})).then(s,a)}u((i=i.apply(r,n||[])).next())})},e.prototype.useAdapters=function(e){var t=nY(e)||{},r=t.adapter,n=t.runtime;r&&(n_.adapter=r),n&&(n_.runtime=n)},e.prototype.registerHook=function(t){!function(e,t){var r=t.entity,n=t.target;if(Object.prototype.hasOwnProperty.call(e,n))throw Error(JSON.stringify({code:x.INVALID_OPERATION,msg:"target:".concat(n," is not exist")}));var o=e.prototype[n];if("function"!=typeof o)throw Error(JSON.stringify({code:x.INVALID_OPERATION,msg:"target:".concat(n," is not a function which is the only type supports hook")}));e.prototype[n]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return r.call.apply(r,ng([this],e,!1)),o.call.apply(o,ng([this],e,!1))}}(e,t)},e.prototype.registerComponent=function(t){!function(e,t){var r=t.name,n=t.namespace,o=t.entity,i=t.injectEvents,s=t.IIFE;if(nb[r]||n&&e[n])throw Error(JSON.stringify({code:x.INVALID_OPERATION,msg:"Duplicate component ".concat(r)}));if(void 0!==s&&s){if(!o||"function"!=typeof o)throw Error(JSON.stringify({code:x.INVALID_PARAMS,msg:"IIFE component's entity must be a function"}));o.call(e)}if(nb[r]=t,n?e.prototype[n]=o:function e(t,r){if(!(r instanceof Object))return r;switch(r.constructor){case Date:return new Date(r.getTime());case Object:void 0===t&&(t={});break;case Array:t=[];break;default:return r}for(var n=Object.keys(r),o=0;o<n.length;o++){var i=n[o];Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=e(t[i],r[i]))}return t}(e.prototype,o),i){var a=i.bus,u=i.events;if(!a||!u||0===u.length)return;var c=e.prototype.fire||function(){};e.prototype.events||(e.prototype.events={}),(e.prototype.events||{})[r]?e.prototype.events[r].events=ng(ng([],e.prototype.events[r].events,!0),u,!0):e.prototype.events[r]={bus:a,events:u},e.prototype.fire=function(e,t){c(e,t);for(var r=Object.keys(this.events),n=0;n<r.length;n++){var o=r[n],i=this.events[o],s=i.bus;if(i.events.includes(e)){s.fire(e,t);break}}}}}(e,t)},e.prototype.registerVersion=function(e){nA=e,this.version=e},e.prototype.registerSdkName=function(e){nP=e,nT(e)},e.prototype.registerEndPoint=function(e,t){nj({baseUrl:e,protocol:t,env:this.config.env,endPointKey:"CLOUD_API"})},e.prototype.registerEndPointWithKey=function(e){nj({env:this.config.env,endPointKey:e.key,baseUrl:e.url,protocol:e.protocol})},e.prototype.getEndPointWithKey=function(e){var t=nU(this.config.env,e);return{BASE_URL:t.baseUrl,PROTOCOL:t.protocol}},e.prototype.useDefaultAdapter=function(){var e={adapter:{root:window,reqClass:F,wsClass:WebSocket,localStorage:localStorage},runtime:n.WEB},t=e.adapter,r=e.runtime;n_.adapter=t,n_.runtime=r},e.prototype.formatTimeout=function(e){switch(!0){case e>6e5:return k(x.INVALID_PARAMS,"timeout is greater than maximum value[10min]"),6e5;case e<100:return k(x.INVALID_PARAMS,"timeout is less than maximum value[100ms]"),100;default:return e}},nK([ee({mode:"sync",title:"Cloudbase 初始化失败",messages:["请确认以下各项：","  1 - 调用 cloudbase.init() 的语法或参数是否正确","  2 - 如果是非浏览器环境，是否配置了安全应用来源（https://docs.cloudbase.net/api-reference/webv3/adapter#%E7%AC%AC-2-%E6%AD%A5%E9%85%8D%E7%BD%AE%E5%AE%89%E5%85%A8%E5%BA%94%E7%94%A8%E6%9D%A5%E6%BA%90）","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(P)]}),nJ("design:type",Function),nJ("design:paramtypes",[Object]),nJ("design:returntype",e)],e.prototype,"init",null),nK([ee({title:"调用扩展能力失败",messages:["请确认以下各项：","  1 - 调用 invokeExtension() 的语法或参数是否正确","  2 - 被调用的扩展能力是否已经安装并通过 registerExtension() 注册","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(P)]}),nJ("design:type",Function),nJ("design:paramtypes",[String,Object]),nJ("design:returntype",Promise)],e.prototype,"invokeExtension",null),e}());function n0(){return"xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}nX.useAdapters(nm),function(e){e.AUTH_SIGN_UP_URL="/auth/v1/signup",e.AUTH_TOKEN_URL="/auth/v1/token",e.AUTH_REVOKE_URL="/auth/v1/revoke",e.WEDA_USER_URL="/auth/v1/plugin/weda/userinfo",e.AUTH_RESET_PASSWORD="/auth/v1/reset",e.AUTH_GET_DEVICE_CODE="/auth/v1/device/code",e.CHECK_USERNAME="/auth/v1/checkUsername",e.CHECK_IF_USER_EXIST="/auth/v1/checkIfUserExist",e.GET_PROVIDER_TYPE="/auth/v1/mgr/provider/providerSubType",e.AUTH_SIGN_IN_URL="/auth/v1/signin",e.AUTH_SIGN_IN_ANONYMOUSLY_URL="/auth/v1/signin/anonymously",e.AUTH_SIGN_IN_WITH_PROVIDER_URL="/auth/v1/signin/with/provider",e.AUTH_SIGN_IN_WITH_WECHAT_URL="/auth/v1/signin/wechat/noauth",e.AUTH_SIGN_IN_CUSTOM="/auth/v1/signin/custom",e.PROVIDER_TOKEN_URL="/auth/v1/provider/token",e.PROVIDER_URI_URL="/auth/v1/provider/uri",e.USER_ME_URL="/auth/v1/user/me",e.AUTH_SIGNOUT_URL="/auth/v1/user/signout",e.USER_QUERY_URL="/auth/v1/user/query",e.USER_PRIFILE_URL="/auth/v1/user/profile",e.USER_BASIC_EDIT_URL="/auth/v1/user/basic/edit",e.USER_TRANS_BY_PROVIDER_URL="/auth/v1/user/trans/by/provider",e.PROVIDER_LIST="/auth/v1/user/provider",e.PROVIDER_BIND_URL="/auth/v1/user/provider/bind",e.PROVIDER_UNBIND_URL="/auth/v1/user/provider",e.CHECK_PWD_URL="/auth/v1/user/sudo",e.SUDO_URL="/auth/v1/user/sudo",e.BIND_CONTACT_URL="/auth/v1/user/contact",e.AUTH_SET_PASSWORD="/auth/v1/user/password",e.AUTHORIZE_DEVICE_URL="/auth/v1/user/device/authorize",e.AUTHORIZE_URL="/auth/v1/user/authorize",e.AUTHORIZE_INFO_URL="/auth/v1/user/authorize/info",e.AUTHORIZED_DEVICES_DELETE_URL="/auth/v1/user/authorized/devices/",e.AUTH_REVOKE_ALL_URL="/auth/v1/user/revoke/all",e.GET_USER_BEHAVIOR_LOG="/auth/v1/user/security/history",e.VERIFICATION_URL="/auth/v1/verification",e.VERIFY_URL="/auth/v1/verification/verify",e.CAPTCHA_DATA_URL="/auth/v1/captcha/data",e.VERIFY_CAPTCHA_DATA_URL="/auth/v1/captcha/data/verify",e.GET_CAPTCHA_URL="/auth/v1/captcha/init",e.GET_MINIPROGRAM_QRCODE="/auth/v1/qrcode/generate",e.GET_MINIPROGRAM_QRCODE_STATUS="/auth/v1/qrcode/get/status"}(u||(u={})),function(e){e.AUTH_SIGN_IN_URL="/auth/v2/signin/username",e.AUTH_TOKEN_URL="/auth/v2/token",e.USER_ME_URL="/auth/v2/user/me",e.VERIFY_URL="/auth/v2/signin/verificationcode",e.AUTH_SIGN_IN_WITH_PROVIDER_URL="/auth/v2/signin/with/provider",e.AUTH_PUBLIC_KEY="/auth/v2/signin/publichkey",e.AUTH_RESET_PASSWORD="/auth/v2/signin/password/update"}(c||(c={})),function(e){e.REGISTER="REGISTER",e.SIGN_IN="SIGN_IN",e.PASSWORD_RESET="PASSWORD_RESET",e.EMAIL_ADDRESS_CHANGE="EMAIL_ADDRESS_CHANGE",e.PHONE_NUMBER_CHANGE="PHONE_NUMBER_CHANGE"}(l||(l={})),function(e){e.UNREACHABLE="unreachable",e.LOCAL="local",e.CANCELLED="cancelled",e.UNKNOWN="unknown",e.UNAUTHENTICATED="unauthenticated",e.RESOURCE_EXHAUSTED="resource_exhausted",e.FAILED_PRECONDITION="failed_precondition",e.INVALID_ARGUMENT="invalid_argument",e.DEADLINE_EXCEEDED="deadline_exceeded",e.NOT_FOUND="not_found",e.ALREADY_EXISTS="already_exists",e.PERMISSION_DENIED="permission_denied",e.ABORTED="aborted",e.OUT_OF_RANGE="out_of_range",e.UNIMPLEMENTED="unimplemented",e.INTERNAL="internal",e.UNAVAILABLE="unavailable",e.DATA_LOSS="data_loss",e.INVALID_PASSWORD="invalid_password",e.PASSWORD_NOT_SET="password_not_set",e.INVALID_STATUS="invalid_status",e.USER_PENDING="user_pending",e.USER_BLOCKED="user_blocked",e.INVALID_VERIFICATION_CODE="invalid_verification_code",e.TWO_FACTOR_REQUIRED="two_factor_required",e.INVALID_TWO_FACTOR="invalid_two_factor",e.INVALID_TWO_FACTOR_RECOVERY="invalid_two_factor_recovery",e.UNDER_REVIEW="under_review",e.INVALID_REQUEST="invalid_request",e.UNAUTHORIZED_CLIENT="unauthorized_client",e.ACCESS_DENIED="access_denied",e.UNSUPPORTED_RESPONSE_TYPE="unsupported_response_type",e.INVALID_SCOPE="invalid_scope",e.INVALID_GRANT="invalid_grant",e.SERVER_ERROR="server_error",e.TEMPORARILY_UNAVAILABLE="temporarily_unavailable",e.INTERACTION_REQUIRED="interaction_required",e.LOGIN_REQUIRED="login_required",e.ACCOUNT_SELECTION_REQUIRED="account_selection_required",e.CONSENT_REQUIRED="consent_required",e.INVALID_REQUEST_URI="invalid_request_uri",e.INVALID_REQUEST_OBJECT="invalid_request_object",e.REQUEST_NOT_SUPPORTED="request_not_supported",e.REQUEST_URI_NOT_SUPPORTED="request_uri_not_supported",e.REGISTRATION_NOT_SUPPORTED="registration_not_supported",e.CAPTCHA_REQUIRED="captcha_required",e.CAPTCHA_INVALID="captcha_invalid"}(f||(f={})),function(e){e.CLIENT_ID="client_id",e.CLIENT_SECRET="client_secret",e.RESPONSE_TYPE="response_type",e.SCOPE="scope",e.STATE="state",e.REDIRECT_URI="redirect_uri",e.ERROR="error",e.ERROR_DESCRIPTION="error_description",e.ERROR_URI="error_uri",e.GRANT_TYPE="grant_type",e.CODE="code",e.ACCESS_TOKEN="access_token",e.TOKEN_TYPE="token_type",e.EXPIRES_IN="expires_in",e.USERNAME="username",e.PASSWORD="password",e.REFRESH_TOKEN="refresh_token"}(h||(h={}));var n1=r(85746);class n2{constructor(e){this.clientId=e?.clientId||"",globalThis.jsSdkFnPromiseMap=globalThis.jsSdkFnPromiseMap||new Map}async run(e,t){e=`${this.clientId}_${e}`;let r=globalThis.jsSdkFnPromiseMap.get(e);return r||(r=new Promise((r,n)=>{(async()=>{try{await this.runIdlePromise();let e=t();r(await e)}catch(e){n(e)}finally{globalThis.jsSdkFnPromiseMap.delete(e)}})()}),globalThis.jsSdkFnPromiseMap.set(e,r)),r}runIdlePromise(){return Promise.resolve()}}let n3="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",n4="x-request-id",n5="x-device-id",n6="device_id",n8=async function(e,t){let r=null,n=null;try{let o=Object.assign({},t);o.method||(o.method="GET"),o.body&&"string"!=typeof o.body&&(o.body=JSON.stringify(o.body));let i=await fetch(e,o),s=await i.json();s?.error?(n=s).error_uri=new URL(e).pathname:r=s}catch(t){n={error:f.UNREACHABLE,error_description:t.message,error_uri:new URL(e).pathname}}if(!n)return r;throw n};class n7{constructor(e){this._env=e?.env||""}async getItem(e){return window.localStorage.getItem(`${e}${this._env}`)}async removeItem(e){window.localStorage.removeItem(`${e}${this._env}`)}async setItem(e,t){window.localStorage.setItem(`${e}${this._env}`,t)}getItemSync(e){return window.localStorage.getItem(`${e}${this._env}`)}removeItemSync(e){window.localStorage.removeItem(`${e}${this._env}`)}setItemSync(e,t){window.localStorage.setItem(`${e}${this._env}`,t)}}let n9=new n7;function oe(e){let t=!0;return e?.expires_at&&e?.access_token&&(t=e.expires_at<new Date),t}class ot{constructor(e){this.credentials=null,this.singlePromise=null,this.tokenSectionName=e.tokenSectionName,this.storage=e.storage,this.clientId=e.clientId,this.singlePromise=new n2({clientId:this.clientId})}getStorageCredentialsSync(){let e=null,t=this.storage.getItemSync(this.tokenSectionName);if(null!=t)try{e=JSON.parse(t),e?.expires_at&&(e.expires_at=new Date(e.expires_at))}catch(t){this.storage.removeItem(this.tokenSectionName),e=null}return e}async setCredentials(e){if(e?.expires_in){if(e?.expires_at||(e.expires_at=new Date(Date.now()+(e.expires_in-30)*1e3)),this.storage){let t=JSON.stringify(e);await this.storage.setItem(this.tokenSectionName,t)}this.credentials=e}else this.storage&&await this.storage.removeItem(this.tokenSectionName),this.credentials=null}async getCredentials(){return this.singlePromise.run("getCredentials",async()=>(oe(this.credentials)&&(this.credentials=await this.getStorageCredentials()),this.credentials))}async getStorageCredentials(){return this.singlePromise.run("_getStorageCredentials",async()=>{let e=null,t=await this.storage.getItem(this.tokenSectionName);if(null!=t)try{e=JSON.parse(t),e?.expires_at&&(e.expires_at=new Date(e.expires_at))}catch(t){await this.storage.removeItem(this.tokenSectionName),e=null}return e})}}class or{constructor(e){this.singlePromise=null,e.clientSecret||(e.clientSecret=""),!e.clientId&&e.env&&(e.clientId=e.env),this.apiOrigin=e.apiOrigin,this.clientId=e.clientId,this.singlePromise=new n2({clientId:this.clientId}),this.retry=this.formatRetry(e.retry,or.defaultRetry),e.baseRequest?this.baseRequest=e.baseRequest:this.baseRequest=n8,this.tokenInURL=e.tokenInURL,this.headers=e.headers,this.storage=e.storage||n9,this.localCredentials=new ot({tokenSectionName:`credentials_${e.clientId}`,storage:this.storage,clientId:e.clientId}),this.clientSecret=e.clientSecret,e.clientId&&(this.basicAuth=`Basic ${function(e){let t,r,n,o;e=String(e);let i="",s=0,a=e.length%3;for(;s<e.length;){if((r=e.charCodeAt(s++))>255||(n=e.charCodeAt(s++))>255||(o=e.charCodeAt(s++))>255)throw TypeError("Failed to execute 'btoa' on 'Window': The string to be encoded contains characters outside of the Latin1 range.");t=r<<16|n<<8|o,i+=n3.charAt(t>>18&63)+n3.charAt(t>>12&63)+n3.charAt(t>>6&63)+n3.charAt(63&t)}return a?i.slice(0,a-3)+"===".substring(a):i}(`${e.clientId}:${this.clientSecret}`)}`),this.wxCloud=e.wxCloud;try{(function(){if("undefined"==typeof wx||"undefined"==typeof Page||!wx.getSystemInfoSync||!wx.getStorageSync||!wx.setStorageSync||!wx.connectSocket||!wx.request)return!1;try{if(!wx.getSystemInfoSync()||"qq"===wx.getSystemInfoSync().AppPlatform)return!1}catch(e){return!1}return!0})()&&void 0===this.wxCloud&&e.env&&(wx.cloud.init({env:e.env}),this.wxCloud=wx.cloud)}catch(e){}this.refreshTokenFunc=e.refreshTokenFunc||this.defaultRefreshTokenFunc,this.anonymousSignInFunc=e.anonymousSignInFunc}setCredentials(e){return this.localCredentials.setCredentials(e)}async getAccessToken(){let e=await this.getCredentials();return e?.access_token?Promise.resolve(e.access_token):Promise.reject({error:f.UNAUTHENTICATED})}async request(e,t){t||(t={});let r=this.formatRetry(t.retry,this.retry);if(t.headers=t.headers||{},this.headers&&(t.headers={...this.headers,...t.headers}),t.headers[n4]||(t.headers[n4]=n0()),!t.headers[n5]){let e=await this.getDeviceId();t.headers[n5]=e}if(t?.withBasicAuth&&this.basicAuth&&(t.headers.Authorization=this.basicAuth),t?.withCredentials){let r=await this.getCredentials();r&&(this.tokenInURL?(0>e.indexOf("?")&&(e+="?"),e+=`access_token=${r.access_token}`):t.headers.Authorization=`${r.token_type} ${r.access_token}`)}else this.clientId&&0>e.indexOf("client_id")&&(e+=(0>e.indexOf("?")?"?":"&")+`client_id=${this.clientId}`);e.startsWith("/")&&(e=this.apiOrigin+e);let n=null,o=r+1;for(let i=0;i<o;i++){try{n=t.useWxCloud?await this.wxCloudCallFunction(e,t):await this.baseRequest(e,t);break}catch(e){if(t.withCredentials&&e&&e.error===f.UNAUTHENTICATED)return await this.setCredentials(null),Promise.reject(e);if(i===r||!e||"unreachable"!==e.error)return Promise.reject(e)}await this.sleep(or.retryInterval)}return n}async wxCloudCallFunction(e,t){let r=null,n=null;try{let o="";try{o=await wx.getRendererUserAgent()}catch(e){}let{result:i}=await this.wxCloud.callFunction({name:"httpOverCallFunction",data:{url:e,method:t.method,headers:{origin:"https://servicewechat.com","User-Agent":o,...t.headers},body:t.body}});i?.body?.error_code?(n=i?.body).error_uri=(0,n1.y)(e):r=i?.body}catch(t){n={error:f.UNREACHABLE,error_description:t.message,error_uri:(0,n1.y)(e)}}if(!n)return r;throw n}async getCredentials(){let e=await this.localCredentials.getCredentials();return e?(oe(e)&&(e=e&&"anonymous"===e.scope?this.anonymousSignInFunc?await this.anonymousSignInFunc(e)||await this.localCredentials.getCredentials():await this.anonymousSignIn(e):await this.refreshToken(e)),e):this.unAuthenticatedError("credentials not found")}getCredentialsSync(){return this.localCredentials.getStorageCredentialsSync()}getCredentialsAsync(){return this.getCredentials()}async getScope(){let e=await this.localCredentials.getCredentials();return e?e.scope:this.unAuthenticatedError("credentials not found")}async getGroups(){let e=await this.localCredentials.getCredentials();return e?e.groups:this.unAuthenticatedError("credentials not found")}async refreshToken(e){return this.singlePromise.run("_refreshToken",async()=>{if(!e||!e.refresh_token)return this.unAuthenticatedError("no refresh token found in credentials");try{let t=await this.refreshTokenFunc(e.refresh_token,e);return await this.localCredentials.setCredentials(t),t}catch(e){if(e.error===f.INVALID_GRANT)return await this.localCredentials.setCredentials(null),this.unAuthenticatedError(e.error_description);return Promise.reject(e)}})}checkRetry(e){let t=null;if(("number"!=typeof e||e<or.minRetry||e>or.maxRetry)&&(t={error:f.UNREACHABLE,error_description:"wrong options param: retry"}),t)throw t;return e}formatRetry(e,t){return void 0===e?t:this.checkRetry(e)}async sleep(e){return new Promise(t=>{setTimeout(()=>{t()},e)})}async anonymousSignIn(e){return this.singlePromise.run("_anonymous",async()=>{if(!e||"anonymous"!==e.scope)return this.unAuthenticatedError("no anonymous in credentials");try{let e=await this.request(u.AUTH_SIGN_IN_ANONYMOUSLY_URL,{method:"POST",withBasicAuth:!0,body:{}});return await this.localCredentials.setCredentials(e),e}catch(e){if(e.error===f.INVALID_GRANT)return await this.localCredentials.setCredentials(null),this.unAuthenticatedError(e.error_description);return Promise.reject(e)}})}async defaultRefreshTokenFunc(e,t){if(void 0===e||""===e)return this.unAuthenticatedError("refresh token not found");let r=u.AUTH_TOKEN_URL;return t?.version==="v2"&&(r=c.AUTH_TOKEN_URL),{...await this.request(r,{method:"POST",body:{client_id:this.clientId,client_secret:this.clientSecret,grant_type:"refresh_token",refresh_token:e}}),version:t?.version||"v1"}}async getDeviceId(){if(this.deviceID)return this.deviceID;let e=await this.storage.getItem(n6);return"string"==typeof e&&e.length>=16&&e.length<=48||(e=n0(),await this.storage.setItem(n6,e)),this.deviceID=e,e}unAuthenticatedError(e){return Promise.reject({error:f.UNAUTHENTICATED,error_description:e})}}function on(){let{wx:e}=globalThis;if(void 0===e||void 0===globalThis.Page||!e.getSystemInfoSync||!e.getStorageSync||!e.setStorageSync||!e.connectSocket||!e.request)return!1;try{if(!e.getSystemInfoSync()||"qq"===e.getSystemInfoSync().AppPlatform)return!1}catch(e){return!1}return!0}or.defaultRetry=2,or.minRetry=0,or.maxRetry=5,or.retryInterval=1e3;let oo=!1;function oi(){oo=oo||"miniprogram"===window.__wxjs_environment}try{on()||(oo=oo||!!navigator.userAgent.match(/miniprogram/i)||"miniprogram"===window.__wxjs_environment,window&&window.WeixinJSBridge&&window.WeixinJSBridge.invoke?oi():"undefined"!=typeof document&&document.addEventListener("WeixinJSBridgeReady",oi,!1))}catch(e){}class os{constructor(e){if(this.params={},"string"==typeof e)this.parse(e);else if(e&&"object"==typeof e)for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&this.append(t,e[t])}parse(e){e.split("&").forEach(e=>{let[t,r]=e.split("=").map(decodeURIComponent);this.append(t,r)})}append(e,t){this.params[e]?this.params[e]=this.params[e].concat([t]):this.params[e]=[t]}get(e){return this.params[e]?this.params[e][0]:null}getAll(e){return this.params[e]||[]}delete(e){delete this.params[e]}has(e){return Object.prototype.hasOwnProperty.call(this.params,e)}set(e,t){this.params[e]=[t]}toString(){let e=[];for(let t in this.params)Object.prototype.hasOwnProperty.call(this.params,t)&&this.params[t].forEach(r=>{e.push(`${encodeURIComponent(t)}=${encodeURIComponent(r)}`)});return e.join("&")}}class oa{constructor(e){e.openURIWithCallback||(e.openURIWithCallback=this.getDefaultOpenURIWithCallback()),e.storage||(e.storage=n9),this.config=e,this.tokenSectionName=`captcha_${e.clientId}`}async request(e,t){let r;t||(t={}),t.method||(t.method="GET");let n=`${t.method}:${e}`,o=e;t.withCaptcha&&(o=await this.appendCaptchaTokenToURL(e,n,!1));try{r=await this.config.request(o,t)}catch(r){if(r.error===f.CAPTCHA_REQUIRED||r.error===f.CAPTCHA_INVALID)return e=await this.appendCaptchaTokenToURL(e,n,r.error===f.CAPTCHA_INVALID),this.config.request(e,t);return Promise.reject(r)}return r}getDefaultOpenURIWithCallback(){if(!on()&&!oo&&(window.location.search.indexOf("__captcha")>0&&(document.body.style.display="none"),null===document.getElementById("captcha_panel_wrap"))){let e=document.createElement("div");e.style.cssText="background-color: rgba(0, 0, 0, 0.7);position: fixed;left: 0px;right: 0px;top: 0px;bottom: 0px;padding: 9vw 0 0 0;display: none;z-index:100;",e.setAttribute("id","captcha_panel_wrap"),setTimeout(()=>{document.body.appendChild(e)},0)}return this.defaultOpenURIWithCallback}async defaultOpenURIWithCallback(e,t){let{width:r="355px",height:n="355px"}=t||{};if(e.match(/^(data:.*)$/))return Promise.reject({error:f.UNIMPLEMENTED,error_description:"need to impl captcha data"});let o=document.getElementById("captcha_panel_wrap"),i=document.createElement("iframe");return o.innerHTML="",i.setAttribute("src",e),i.setAttribute("id","review-panel-iframe"),i.style.cssText=`min-width:${r};display:block;height:${n};margin:0 auto;background-color: rgb(255, 255, 255);border: none;`,o.appendChild(i),o.style.display="block",new Promise((e,t)=>{i.onload=function(){try{let r=window.location,n=i.contentWindow.location;if(n.host+n.pathname===r.host+r.pathname){o.style.display="none";let r=new os(n.search),i=r.get("captcha_token");if(i)return e({captcha_token:i,expires_in:Number(r.get("expires_in"))});return t({error:r.get("error"),error_description:r.get("error_description")})}o.style.display="block"}catch(e){o.style.display="block"}}})}async getCaptchaToken(e,t){let r;if(!e){let e=await this.findCaptchaToken();if(e)return e}if(on()||oo){let e=await this.config.request(u.CAPTCHA_DATA_URL,{method:"POST",body:{state:t,redirect_uri:""},withCredentials:!1});r={url:`${e.data}?state=${encodeURIComponent(t)}&token=${encodeURIComponent(e.token)}`}}else{let e=`${window.location.origin+window.location.pathname}?__captcha=on`;if((r=await this.config.request(u.GET_CAPTCHA_URL,{method:"POST",body:{client_id:this.config.clientId,redirect_uri:e,state:t},withCredentials:!1})).captcha_token){let e={captcha_token:r.captcha_token,expires_in:r.expires_in};return this.saveCaptchaToken(e),r.captcha_token}}let n=await this.config.openURIWithCallback(r.url);return this.saveCaptchaToken(n),n.captcha_token}async appendCaptchaTokenToURL(e,t,r){let n=await this.getCaptchaToken(r,t);return e.indexOf("?")>0?e+=`&captcha_token=${n}`:e+=`?captcha_token=${n}`,e}async saveCaptchaToken(e){e.expires_at=new Date(Date.now()+(e.expires_in-10)*1e3);let t=JSON.stringify(e);await this.config.storage.setItem(this.tokenSectionName,t)}async findCaptchaToken(){let e=await this.config.storage.getItem(this.tokenSectionName);if(null!=e)try{let t=JSON.parse(e);if(t?.expires_at&&(t.expires_at=new Date(t.expires_at)),t.expires_at<new Date)return null;return t.captcha_token}catch(e){await this.config.storage.removeItem(this.tokenSectionName)}return null}}function ou(e){if(!globalThis.IS_MP_BUILD&&e)return r(71492)}class oc{static parseParamsToSearch(e){return Object.keys(e).forEach(t=>{e[t]||delete e[t]}),new os(e).toString()}constructor(e){let{request:t}=e,r=e.credentialsClient;if(r||(r=new or({apiOrigin:e.apiOrigin,clientId:e.clientId,storage:e.storage,env:e.env,baseRequest:e.baseRequest,anonymousSignInFunc:e.anonymousSignInFunc,wxCloud:e.wxCloud})),!t){let n=r.request.bind(r),o=new oa({clientId:e.clientId,request:n,storage:e.storage,...e.captchaOptions});t=o.request.bind(o)}this.config={env:e.env,apiOrigin:e.apiOrigin,clientId:e.clientId,request:t,credentialsClient:r,storage:e.storage||n9}}getParamsByVersion(e,t){let r=(0,n1.I)(e),n={v2:c}[r?.version]?.[t]||u[t];return r&&delete r.version,{params:r,url:n}}async signIn(e){let t=e.version||"v1",r=this.getParamsByVersion(e,"AUTH_SIGN_IN_URL");r.params.query&&delete r.params.query;let n=await this.getEncryptParams(r.params),o=await this.config.request(r.url,{method:"POST",body:n});return await this.config.credentialsClient.setCredentials({...o,version:t}),Promise.resolve(o)}async signInAnonymously(e={},t=!1){let r=await this.config.request(u.AUTH_SIGN_IN_ANONYMOUSLY_URL,{method:"POST",body:e,useWxCloud:t});return await this.config.credentialsClient.setCredentials(r),Promise.resolve(r)}async signUp(e){let t=await this.config.request(u.AUTH_SIGN_UP_URL,{method:"POST",body:e});return await this.config.credentialsClient.setCredentials(t),Promise.resolve(t)}async signOut(e){let t={};if(e){try{t=await this.config.request(u.AUTH_SIGNOUT_URL,{method:"POST",withCredentials:!0,body:e})}catch(e){e.error!==f.UNAUTHENTICATED&&console.log("sign_out_error",e)}return await this.config.credentialsClient.setCredentials(),t}let r=await this.config.credentialsClient.getAccessToken(),n=await this.config.request(u.AUTH_REVOKE_URL,{method:"POST",body:{token:r}});return await this.config.credentialsClient.setCredentials(),Promise.resolve(n)}async revokeAllDevices(){await this.config.request(u.AUTH_REVOKE_ALL_URL,{method:"DELETE",withCredentials:!0})}async revokeDevice(e){await this.config.request(u.AUTHORIZED_DEVICES_DELETE_URL+e.device_id,{method:"DELETE",withCredentials:!0})}async getVerification(e,t){let r=!1;return"CUR_USER"===e.target?r=!0:await this.hasLoginState()&&(r=!0),this.config.request(u.VERIFICATION_URL,{method:"POST",body:e,withCaptcha:t?.withCaptcha||!1,withCredentials:r})}async verify(e){let t=this.getParamsByVersion(e,"VERIFY_URL"),r=await this.config.request(t.url,{method:"POST",body:t.params});return e?.version==="v2"&&await this.config.credentialsClient.setCredentials({...r,version:"v2"}),r}async genProviderRedirectUri(e){let{provider_redirect_uri:t,other_params:r={},...n}=e;t&&!n.redirect_uri&&(n.redirect_uri=t);let o=`${u.PROVIDER_URI_URL}?${oc.parseParamsToSearch(n)}`;return Object.keys(r).forEach(e=>{let t=r[e];("sign_out_uri"!==e||t?.length>0)&&(o+=`&other_params[${e}]=${encodeURIComponent(t)}`)}),this.config.request(o,{method:"GET"})}async grantProviderToken(e,t=!1){return this.config.request(u.PROVIDER_TOKEN_URL,{method:"POST",body:e,useWxCloud:t})}async patchProviderToken(e){return this.config.request(u.PROVIDER_TOKEN_URL,{method:"PATCH",body:e})}async signInWithProvider(e,t=!1){let r=this.getParamsByVersion(e,"AUTH_SIGN_IN_WITH_PROVIDER_URL"),n=await this.config.request(r.url,{method:"POST",body:r.params,useWxCloud:t});return await this.config.credentialsClient.setCredentials({...n,version:e?.version||"v1"}),Promise.resolve(n)}async signInCustom(e){let t=await this.config.request(u.AUTH_SIGN_IN_CUSTOM,{method:"POST",body:e});return await this.config.credentialsClient.setCredentials(t),Promise.resolve(t)}async signInWithWechat(e={}){let t=await this.config.request(u.AUTH_SIGN_IN_WITH_WECHAT_URL,{method:"POST",body:e});return await this.config.credentialsClient.setCredentials(t),Promise.resolve(t)}async bindWithProvider(e){return this.config.request(u.PROVIDER_BIND_URL,{method:"POST",body:e,withCredentials:!0})}async getUserProfile(e){return this.getUserInfo(e)}async getUserInfo(e={}){let t=this.getParamsByVersion(e,"USER_ME_URL");if(t.params?.query){let e=new os(t.params.query);t.url+=`?${e.toString()}`}let r=await this.config.request(t.url,{method:"GET",withCredentials:!0});return r.sub&&(r.uid=r.sub),r}async getWedaUserInfo(){return await this.config.request(u.WEDA_USER_URL,{method:"GET",withCredentials:!0})}async deleteMe(e){let t=this.getParamsByVersion(e,"USER_ME_URL"),r=`${t.url}?${oc.parseParamsToSearch(t.params)}`;return this.config.request(r,{method:"DELETE",withCredentials:!0})}async hasLoginState(){try{return await this.config.credentialsClient.getAccessToken(),!0}catch(e){return!1}}hasLoginStateSync(){return this.config.credentialsClient.getCredentialsSync()}async getLoginState(){return this.config.credentialsClient.getCredentialsAsync()}async transByProvider(e){return this.config.request(u.USER_TRANS_BY_PROVIDER_URL,{method:"PATCH",body:e,withCredentials:!0})}async grantToken(e){let t=this.getParamsByVersion(e,"AUTH_TOKEN_URL");return this.config.request(t.url,{method:"POST",body:t.params})}async getProviders(){return this.config.request(u.PROVIDER_LIST,{method:"GET",withCredentials:!0})}async unbindProvider(e){return this.config.request(`${u.PROVIDER_UNBIND_URL}/${e.provider_id}`,{method:"DELETE",withCredentials:!0})}async checkPassword(e){return this.config.request(`${u.CHECK_PWD_URL}`,{method:"POST",withCredentials:!0,body:e})}async editContact(e){return this.config.request(`${u.BIND_CONTACT_URL}`,{method:"PATCH",withCredentials:!0,body:e})}async setPassword(e){return this.config.request(`${u.AUTH_SET_PASSWORD}`,{method:"PATCH",withCredentials:!0,body:e})}async updatePasswordByOld(e){let t=await this.sudo({password:e.old_password});return this.setPassword({sudo_token:t.sudo_token,new_password:e.new_password})}async sudo(e){return this.config.request(`${u.SUDO_URL}`,{method:"POST",withCredentials:!0,body:e})}async sendVerificationCodeToCurrentUser(e){return e.target="CUR_USER",this.config.request(u.VERIFICATION_URL,{method:"POST",body:e,withCredentials:!0,withCaptcha:!0})}async changeBoundProvider(e){return this.config.request(`${u.PROVIDER_LIST}/${e.provider_id}/trans`,{method:"POST",body:{provider_trans_token:e.trans_token},withCredentials:!0})}async setUserProfile(e){return this.config.request(u.USER_PRIFILE_URL,{method:"PATCH",body:e,withCredentials:!0})}async updateUserBasicInfo(e){return this.config.request(u.USER_BASIC_EDIT_URL,{method:"POST",withCredentials:!0,body:e})}async queryUserProfile(e){let t=new os(e);return this.config.request(`${u.USER_QUERY_URL}?${t.toString()}`,{method:"GET",withCredentials:!0})}setCustomSignFunc(e){this.getCustomSignTicketFn=e}async signInWithCustomTicket(){let e=this.getCustomSignTicketFn;if(!e)return Promise.reject({error:"failed_precondition",error_description:"please use setCustomSignFunc to set custom sign function"});let t=await e();return this.signInCustom({provider_id:"custom",ticket:t})}async resetPassword(e){return this.config.request(u.AUTH_RESET_PASSWORD,{method:"POST",body:e})}async authorize(e){return this.config.request(u.AUTHORIZE_URL,{method:"POST",withCredentials:!0,body:e})}async authorizeDevice(e){return this.config.request(u.AUTHORIZE_DEVICE_URL,{method:"POST",withCredentials:!0,body:e})}async deviceAuthorize(e){return this.config.request(u.AUTH_GET_DEVICE_CODE,{method:"POST",body:e,withCredentials:!0})}async authorizeInfo(e){let t=`${u.AUTHORIZE_INFO_URL}?${oc.parseParamsToSearch(e)}`,r=!0,n=!1;return await this.hasLoginState()&&(n=!0,r=!1),this.config.request(t,{method:"GET",withBasicAuth:r,withCredentials:n})}async checkUsername(e){return this.config.request(u.CHECK_USERNAME,{method:"GET",body:e,withCredentials:!0})}async checkIfUserExist(e){let t=new os(e);return this.config.request(`${u.CHECK_IF_USER_EXIST}?${t.toString()}`,{method:"GET"})}async loginScope(){return this.config.credentialsClient.getScope()}async loginGroups(){return this.config.credentialsClient.getGroups()}async refreshTokenForce(e){let t=await this.config.credentialsClient.getCredentials();return await this.config.credentialsClient.refreshToken({...t,version:e?.version||"v1"})}async getCredentials(){return this.config.credentialsClient.getCredentials()}async getPublicKey(){return this.config.request(c.AUTH_PUBLIC_KEY,{method:"POST",body:{}})}async getEncryptParams(e){let{isEncrypt:t}=e;delete e.isEncrypt;let r=(0,n1.I)(e),n=ou(t);if(!n)return e;let o="",i="";try{let e=await this.getPublicKey();if(o=e.public_key,i=e.public_key_thumbprint,!o||!i)throw e}catch(e){throw e}return{params:n.getEncryptInfo({publicKey:o,payload:r}),public_key_thumbprint:i}}async getProviderSubType(){return this.config.request(u.GET_PROVIDER_TYPE,{method:"POST",body:{provider_id:"weda"}})}async verifyCaptchaData({token:e,key:t}){return this.config.request(u.VERIFY_CAPTCHA_DATA_URL,{method:"POST",body:{token:e,key:t},withCredentials:!1})}async createCaptchaData({state:e,redirect_uri:t}){return this.config.request(u.CAPTCHA_DATA_URL,{method:"POST",body:{state:e,redirect_uri:t},withCredentials:!1})}async getMiniProgramCode(e){return this.config.request(u.GET_MINIPROGRAM_QRCODE,{method:"POST",body:e})}async getMiniProgramQrCodeStatus(e){return this.config.request(u.GET_MINIPROGRAM_QRCODE_STATUS,{method:"POST",body:e})}async getUserBehaviorLog(e){let t=`${u.GET_USER_BEHAVIOR_LOG}?${{LOGIN:"query[action]=USER_LOGIN",MODIFY:"ne_query[action]=USER_LOGIN"}[e.type]}&limit=${e.limit}${e.page_token?`&page_token=${e.page_token}`:""}`;return this.config.request(t,{method:"GET",withCredentials:!0})}async modifyPassword(e){let t="",r="",n=ou(!0);if(!n)throw Error("do not support encrypt, a encrypt util required.");try{let e=await this.getPublicKey();if(t=e.public_key,r=e.public_key_thumbprint,!t||!r)throw e}catch(e){throw e}let o=e.password?n.getEncryptInfo({publicKey:t,payload:e.password}):"",i=n.getEncryptInfo({publicKey:t,payload:e.new_password});return this.config.request(u.USER_BASIC_EDIT_URL,{method:"POST",withCredentials:!0,body:{user_id:e.user_id,encrypt_password:o,encrypt_new_password:i,public_key_thumbprint:r}})}async modifyPasswordWithoutLogin(e){let t="",r="",n=ou(!0);if(!n)throw Error("do not support encrypt, a encrypt util required.");try{let e=await this.getPublicKey();if(t=e.public_key,r=e.public_key_thumbprint,!t||!r)throw e}catch(e){throw e}let o=e.password?n.getEncryptInfo({publicKey:t,payload:e.password}):"",i=n.getEncryptInfo({publicKey:t,payload:e.new_password});return this.config.request(c.AUTH_RESET_PASSWORD,{method:"POST",body:{username:e.username,password:o,new_password:i,public_key_thumbprint:r}})}}class ol{constructor(e){let{apiOrigin:t,clientId:r,env:n,storage:o,request:i,baseRequest:s,anonymousSignInFunc:a,wxCloud:u}=e;this.oauth2client=new or({apiOrigin:t,clientId:r,env:n,storage:o,baseRequest:s||i,anonymousSignInFunc:a,wxCloud:u}),this.authApi=new oc({credentialsClient:this.oauth2client,...e,request:i?this.oauth2client.request.bind(this.oauth2client):void 0})}}var of=r(77019),oh=r(23389),od=r(5576),op=r(20777),oy=r(71223),ov=r(61645),om=function(){return(om=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},og=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},ob=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},o_=function(){function e(e){this.localStorage=(null==e?void 0:e.localStorage)||globalThis.localStorage}return e.prototype.getItem=function(e){return og(this,void 0,void 0,function(){return ob(this,function(t){return[2,this.localStorage.getItem(e)]})})},e.prototype.removeItem=function(e){return og(this,void 0,void 0,function(){return ob(this,function(t){return this.localStorage.removeItem(e),[2]})})},e.prototype.setItem=function(e,t){return og(this,void 0,void 0,function(){return ob(this,function(r){return this.localStorage.setItem(e,t),[2]})})},e.prototype.getItemSync=function(e){return this.localStorage.getItem(e)},e.prototype.setItemSync=function(e,t){this.localStorage.setItem(e,t)},e.prototype.removeItemSync=function(e){this.localStorage.removeItem(e)},e}(),ow=/^[^:]+:\/\/[^/]+(\/[^?#]+)/,oS=function(e){if(!nm.isMatch()||e.storage&&e.baseRequest)return e;var t={};try{var r=nm.genAdapter(),n=r.localStorage,o=r.reqClass;if(n&&(t.storage=new o_({localStorage:n})),o){var i=new o({timeout:1e4,restrictedMethods:["get","post","upload","download","request"]});t.request=function(e,t){var r;return og(this,void 0,void 0,function(){var n;return ob(this,function(o){return n=null===(r=null==t?void 0:t.headers)||void 0===r?void 0:r["x-request-id"],[2,new Promise(function(r,o){var s=Object.assign({},t);s.body&&"string"!=typeof s.body&&(s.body=JSON.stringify(s.body));var a=wx.request({url:e,data:s.body,timeout:i._timeout,method:s.method||"GET",header:s.headers,success:function(t){var i,s;(t.code||(null===(i=t.data)||void 0===i?void 0:i.error_code))&&(t={error:t.code||t.data.error,error_description:t.data.error_description||t.message||t.code||t.data.error_code,request_id:t.requestId,error_code:null===(s=t.data)||void 0===s?void 0:s.error_code}),t.request_id||(t.request_id=t.request_id||n),t.error&&(t.error_uri=ow.test(e)?RegExp.$1:e,o(t)),r(t.data||{})},fail:function(e){o({error:"unreachable",error_description:e.message})},complete:function(e){if(e&&e.errMsg&&i._timeout&&-1!==i._restrictedMethods.indexOf("request")&&"request:fail timeout"===e.errMsg){console.warn(i._timeoutMsg);try{a.abort()}catch(e){}}}})})]})})}}return e.captchaOptions&&(t.baseRequest=t.request,t.request=void 0),om(om({},e),t)}catch(e){console.error("adapter generate fail:",e)}return e},oE=function(){return(oE=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},oI=function(e,t,r,n){var o,i=arguments.length,s=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,r,n);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(i<3?o(s):i>3?o(t,r,s):o(t,r))||s);return i>3&&s&&Object.defineProperty(t,r,s),s},oO=function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},oT=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},oR=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},oA={LOGIN_STATE_CHANGED:"loginStateChanged"},oP=new op.CloudbaseEventEmitter,ox=function(){function e(e){var t=e.cache,r=e.oauthInstance;this.cache=t,this.oauthInstance=r,this.setUserInfo()}return e.prototype.checkLocalInfo=function(){return oT(this,void 0,void 0,function(){return oR(this,function(e){return this.uid=this.getLocalUserInfo("uid"),this.gender=this.getLocalUserInfo("gender"),this.picture=this.getLocalUserInfo("picture"),this.email=this.getLocalUserInfo("email"),this.emailVerified=this.getLocalUserInfo("email_verified"),this.phoneNumber=this.getLocalUserInfo("phone_number"),this.username=this.getLocalUserInfo("username"),this.name=this.getLocalUserInfo("name"),this.birthdate=this.getLocalUserInfo("birthdate"),this.zoneinfo=this.getLocalUserInfo("zoneinfo"),this.locale=this.getLocalUserInfo("locale"),this.sub=this.getLocalUserInfo("sub"),this.createdFrom=this.getLocalUserInfo("created_from"),this.providers=this.getLocalUserInfo("providers"),[2]})})},e.prototype.checkLocalInfoAsync=function(){return oT(this,void 0,void 0,function(){var e,t,r,n;return oR(this,function(o){switch(o.label){case 0:return e=this,[4,this.getLocalUserInfoAsync("uid")];case 1:return e.uid=o.sent(),t=this,[4,this.getLocalUserInfoAsync("gender")];case 2:return t.gender=o.sent(),this.picture=this.getLocalUserInfo("picture"),r=this,[4,this.getLocalUserInfoAsync("email")];case 3:return r.email=o.sent(),this.emailVerified=this.getLocalUserInfo("email_verified"),this.phoneNumber=this.getLocalUserInfo("phone_number"),n=this,[4,this.getLocalUserInfoAsync("username")];case 4:return n.username=o.sent(),this.name=this.getLocalUserInfo("name"),this.birthdate=this.getLocalUserInfo("birthdate"),this.zoneinfo=this.getLocalUserInfo("zoneinfo"),this.locale=this.getLocalUserInfo("locale"),this.sub=this.getLocalUserInfo("sub"),this.createdFrom=this.getLocalUserInfo("created_from"),this.providers=this.getLocalUserInfo("providers"),[2]}})})},e.prototype.update=function(e){return oT(this,void 0,void 0,function(){var t;return oR(this,function(r){switch(r.label){case 0:return[4,this.oauthInstance.authApi.setUserProfile(oE({},e))];case 1:return t=r.sent(),this.setLocalUserInfo(t),[2]}})})},e.prototype.updateUserBasicInfo=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){switch(t.label){case 0:return[4,this.oauthInstance.authApi.updateUserBasicInfo(oE({},e))];case 1:return t.sent(),this.setLocalUserInfo({username:e.username}),[2]}})})},e.prototype.updatePassword=function(e,t){return this.oauthInstance.authApi.updatePasswordByOld({old_password:t,new_password:e})},e.prototype.updateUsername=function(e){return"string"!=typeof e&&(0,of.throwError)(oh.ERRORS.INVALID_PARAMS,"username must be a string"),this.update({username:e})},e.prototype.refresh=function(e){return oT(this,void 0,void 0,function(){var t;return oR(this,function(r){switch(r.label){case 0:return[4,this.oauthInstance.authApi.getUserInfo(e)];case 1:return t=r.sent(),this.setLocalUserInfo(t),[2,t]}})})},e.prototype.getLocalUserInfo=function(e){var t=this.cache.keys.userInfoKey;return this.cache.getStore(t)[e]},e.prototype.getLocalUserInfoAsync=function(e){return oT(this,void 0,void 0,function(){var t;return oR(this,function(r){switch(r.label){case 0:return t=this.cache.keys.userInfoKey,[4,this.cache.getStoreAsync(t)];case 1:return[2,r.sent()[e]]}})})},e.prototype.setUserInfo=function(){var e=this,t=this.cache.keys.userInfoKey,r=this.cache.getStore(t);["uid","email","name","gender","picture","email_verified","phone_number","birthdate","zoneinfo","locale","sub","created_from","providers","username"].forEach(function(t){e[t]=r[t]})},e.prototype.setLocalUserInfo=function(e){var t=this.cache.keys.userInfoKey;this.cache.setStore(t,e),this.setUserInfo()},oI([(0,od.catchErrorsDecorator)({title:"更新用户信息失败",messages:["请确认以下各项：","  1 - 调用 User.update() 的语法或参数是否正确","  2 - 用户信息中是否包含非法值","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[Object]),oO("design:returntype",Promise)],e.prototype,"update",null),oI([(0,od.catchErrorsDecorator)({title:"更新密码失败",messages:["请确认以下各项：","  1 - 调用 User.updatePassword() 的语法或参数是否正确","  3 - 新密码中是否包含非法字符","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[String,String]),oO("design:returntype",void 0)],e.prototype,"updatePassword",null),oI([(0,od.catchErrorsDecorator)({title:"更新用户名失败",messages:["请确认以下各项：","  1 - 调用 User.updateUsername() 的语法或参数是否正确","  2 - 当前环境是否开通了用户名密码登录","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[String]),oO("design:returntype",void 0)],e.prototype,"updateUsername",null),oI([(0,od.catchErrorsDecorator)({title:"刷新本地用户信息失败",messages:["请确认以下各项：","  1 - 调用 User.refresh() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[Object]),oO("design:returntype",Promise)],e.prototype,"refresh",null),e}(),oN=function(){function e(e){var t=e.envId,r=e.cache,n=e.oauthInstance;t||(0,of.throwError)(oh.ERRORS.INVALID_PARAMS,"envId is not defined"),this.cache=r,this.oauthInstance=n,this.user=new ox({cache:this.cache,oauthInstance:n})}return e.prototype.checkLocalState=function(){var e;this.oauthLoginState=null===(e=this.oauthInstance)||void 0===e?void 0:e.authApi.hasLoginStateSync(),this.user.checkLocalInfo()},e.prototype.checkLocalStateAsync=function(){var e;return oT(this,void 0,void 0,function(){return oR(this,function(t){switch(t.label){case 0:return[4,null===(e=this.oauthInstance)||void 0===e?void 0:e.authApi.getLoginState()];case 1:return t.sent(),[4,this.user.checkLocalInfoAsync()];case 2:return t.sent(),[2]}})})},e}(),oC=function(){function e(e){this.config=e,this.cache=e.cache,this.oauthInstance=e.oauthInstance}return e.prototype.bindPhoneNumber=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.editContact(e)]})})},e.prototype.unbindProvider=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.unbindProvider(e)]})})},e.prototype.bindEmail=function(e){return this.oauthInstance.authApi.editContact(e)},e.prototype.verify=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.verify(e)]})})},e.prototype.getVerification=function(e,t){return oT(this,void 0,void 0,function(){return oR(this,function(r){return[2,this.oauthInstance.authApi.getVerification(e,t)]})})},Object.defineProperty(e.prototype,"currentUser",{get:function(){if("async"===this.cache.mode){(0,of.printWarn)(oh.ERRORS.INVALID_OPERATION,"current platform's storage is asynchronous, please use getCurrentUser insteed");return}var e=this.hasLoginState();return e&&e.user||null},enumerable:!1,configurable:!0}),e.prototype.getCurrentUser=function(){return oT(this,void 0,void 0,function(){var e;return oR(this,function(t){switch(t.label){case 0:return[4,this.getLoginState()];case 1:if(!(e=t.sent()))return[3,3];return[4,e.user.checkLocalInfoAsync()];case 2:return t.sent(),[2,e.user||null];case 3:return[2,null]}})})},e.prototype.signInAnonymously=function(e){return void 0===e&&(e={}),oT(this,void 0,void 0,function(){return oR(this,function(t){switch(t.label){case 0:return[4,this.oauthInstance.authApi.signInAnonymously(e)];case 1:return t.sent(),[2,this.createLoginState()]}})})},e.prototype.signInAnonymouslyInWx=function(e){var t=(void 0===e?{}:e).useWxCloud;return oT(this,void 0,void 0,function(){var e,r,n=this;return oR(this,function(o){switch(o.label){case 0:if(!nm.isMatch())throw Error("wx api undefined");return e=wx.getAccountInfoSync().miniProgram,r=function(r){return oT(n,void 0,void 0,function(){var n,o;return oR(this,function(i){switch(i.label){case 0:n=void 0,o=void 0,i.label=1;case 1:return i.trys.push([1,4,,5]),[4,this.oauthInstance.authApi.grantProviderToken({provider_id:null==e?void 0:e.appId,provider_code:r,provider_params:{provider_code_type:"open_id",appid:null==e?void 0:e.appId}},t)];case 2:if((null==(n=i.sent())?void 0:n.error_code)||!n.provider_token)throw n;return[4,this.oauthInstance.authApi.signInAnonymously({provider_token:n.provider_token},t)];case 3:if(null==(o=i.sent())?void 0:o.error_code)throw o;return[3,5];case 4:throw i.sent();case 5:return[2]}})})},[4,new Promise(function(e,t){wx.login({success:function(o){return oT(n,void 0,void 0,function(){return oR(this,function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,r(o.code)];case 1:return n.sent(),e(!0),[3,3];case 2:return t(n.sent()),[3,3];case 3:return[2]}})})},fail:function(e){var r=Error(null==e?void 0:e.errMsg);r.code=null==e?void 0:e.errno,t(r)}})})];case 1:return o.sent(),[2,this.createLoginState(void 0,{asyncRefreshUser:!0})]}})})},e.prototype.bindOpenId=function(){return oT(this,void 0,void 0,function(){var e,t,r=this;return oR(this,function(n){switch(n.label){case 0:if(!nm.isMatch())throw Error("wx api undefined");return e=wx.getAccountInfoSync().miniProgram,t=function(t){return oT(r,void 0,void 0,function(){var r;return oR(this,function(n){switch(n.label){case 0:r=void 0,n.label=1;case 1:return n.trys.push([1,4,,5]),[4,this.oauthInstance.authApi.grantProviderToken({provider_id:null==e?void 0:e.appId,provider_code:t,provider_params:{provider_code_type:"open_id",appid:null==e?void 0:e.appId}})];case 2:if((null==(r=n.sent())?void 0:r.error_code)||!r.provider_token)throw r;return[4,this.oauthInstance.authApi.bindWithProvider({provider_token:r.provider_token})];case 3:return n.sent(),[3,5];case 4:throw n.sent();case 5:return[2]}})})},[4,new Promise(function(e,n){wx.login({success:function(o){return oT(r,void 0,void 0,function(){return oR(this,function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,t(o.code)];case 1:return r.sent(),e(!0),[3,3];case 2:return n(r.sent()),[3,3];case 3:return[2]}})})},fail:function(e){var t=Error(null==e?void 0:e.errMsg);t.code=null==e?void 0:e.errno,n(t)}})})];case 1:return n.sent(),[2]}})})},e.prototype.signInWithOpenId=function(e){var t=(void 0===e?{}:e).useWxCloud,r=void 0===t||t;return oT(this,void 0,void 0,function(){var e,t,n=this;return oR(this,function(o){switch(o.label){case 0:if(!nm.isMatch())throw Error("wx api undefined");return e=wx.getAccountInfoSync().miniProgram,t=function(t){return oT(n,void 0,void 0,function(){var n,o;return oR(this,function(i){switch(i.label){case 0:n=void 0,o=void 0,i.label=1;case 1:return i.trys.push([1,4,,5]),[4,this.oauthInstance.authApi.grantProviderToken({provider_id:null==e?void 0:e.appId,provider_code:t,provider_params:{provider_code_type:"open_id",appid:null==e?void 0:e.appId}},r)];case 2:if((null==(n=i.sent())?void 0:n.error_code)||!n.provider_token)throw n;return[4,this.oauthInstance.authApi.signInWithProvider({provider_token:n.provider_token},r)];case 3:if(null==(o=i.sent())?void 0:o.error_code)throw o;return[3,5];case 4:throw i.sent();case 5:return[4,this.oauthInstance.oauth2client.setCredentials(o)];case 6:return i.sent(),[2]}})})},[4,new Promise(function(e,r){wx.login({success:function(o){return oT(n,void 0,void 0,function(){return oR(this,function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,t(o.code)];case 1:return n.sent(),e(!0),[3,3];case 2:return r(n.sent()),[3,3];case 3:return[2]}})})},fail:function(e){var t=Error(null==e?void 0:e.errMsg);t.code=null==e?void 0:e.errno,r(t)}})})];case 1:return o.sent(),[2,this.createLoginState()]}})})},e.prototype.signInWithUnionId=function(){return oT(this,void 0,void 0,function(){var e=this;return oR(this,function(t){switch(t.label){case 0:if(!nm.isMatch())throw Error("wx api undefined");t.label=1;case 1:return t.trys.push([1,3,,4]),[4,new Promise(function(t,r){var n=wx.getAccountInfoSync().miniProgram;wx.login({success:function(o){return oT(e,void 0,void 0,function(){var e,i,s,a;return oR(this,function(u){switch(u.label){case 0:e=null==n?void 0:n.appId,u.label=1;case 1:return u.trys.push([1,4,,5]),[4,this.oauthInstance.authApi.grantProviderToken({provider_code:o.code,provider_id:e,provider_params:{provider_code_type:"union_id",appid:null==n?void 0:n.appId}})];case 2:if(!(s=(i=u.sent()).provider_token))return r(i),[2];return[4,this.oauthInstance.authApi.signInWithProvider({provider_id:e,provider_token:s})];case 3:if(null==(a=u.sent())?void 0:a.error_code)return r(a),[2];return t(!0),[3,5];case 4:return r(u.sent()),[3,5];case 5:return[2]}})})},fail:function(e){var t=Error(null==e?void 0:e.errMsg);t.code=null==e?void 0:e.errno,r(t)}})})];case 2:return t.sent(),[3,4];case 3:throw t.sent();case 4:return[2,this.createLoginState()]}})})},e.prototype.signInWithPhoneAuth=function(e){var t=e.phoneCode,r=void 0===t?"":t;return oT(this,void 0,void 0,function(){var e,t,n,o,i;return oR(this,function(s){switch(s.label){case 0:if(!nm.isMatch())throw Error("wx api undefined");return t={provider_params:{provider_code_type:"phone"},provider_id:(e=wx.getAccountInfoSync().miniProgram).appId},[4,wx.login()];case 1:n=s.sent().code,t.provider_code=n,s.label=2;case 2:return s.trys.push([2,6,,7]),[4,this.oauthInstance.authApi.grantProviderToken(t)];case 3:if((o=s.sent()).error_code)throw o;return[4,this.oauthInstance.authApi.patchProviderToken({provider_token:o.provider_token,provider_id:e.appId,provider_params:{code:r,provider_code_type:"phone"}})];case 4:if((o=s.sent()).error_code)throw o;return[4,this.oauthInstance.authApi.signInWithProvider({provider_token:o.provider_token})];case 5:if(null==(i=s.sent())?void 0:i.error_code)throw i;return[3,7];case 6:throw s.sent();case 7:return[2,this.createLoginState()]}})})},e.prototype.signInWithSms=function(e){var t=e.verificationInfo,r=void 0===t?{verification_id:"",is_user:!1}:t,n=e.verificationCode,o=void 0===n?"":n,i=e.phoneNum,s=void 0===i?"":i,a=e.bindInfo,u=void 0===a?void 0:a;return oT(this,void 0,void 0,function(){return oR(this,function(e){try{return[2,this.signInWithUsername({verificationInfo:r,verificationCode:o,bindInfo:u,username:s,loginType:"sms"})]}catch(e){throw e}return[2]})})},e.prototype.signInWithEmail=function(e){var t=e.verificationInfo,r=void 0===t?{verification_id:"",is_user:!1}:t,n=e.verificationCode,o=void 0===n?"":n,i=e.bindInfo,s=void 0===i?void 0:i,a=e.email,u=void 0===a?"":a;return oT(this,void 0,void 0,function(){return oR(this,function(e){try{return[2,this.signInWithUsername({verificationInfo:r,verificationCode:o,bindInfo:s,username:u,loginType:"email"})]}catch(e){throw e}return[2]})})},e.prototype.setCustomSignFunc=function(e){this.oauthInstance.authApi.setCustomSignFunc(e)},e.prototype.signInWithCustomTicket=function(){return oT(this,void 0,void 0,function(){return oR(this,function(e){switch(e.label){case 0:return[4,this.oauthInstance.authApi.signInWithCustomTicket()];case 1:return e.sent(),[2,this.createLoginState()]}})})},e.prototype.signIn=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){switch(t.label){case 0:return[4,this.oauthInstance.authApi.signIn(e)];case 1:return t.sent(),[2,this.createLoginState(e)]}})})},e.prototype.signUp=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){switch(t.label){case 0:return[4,this.oauthInstance.authApi.signUp(e)];case 1:return t.sent(),[2,this.createLoginState()]}})})},e.prototype.setPassword=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.setPassword(e)]})})},e.prototype.isUsernameRegistered=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){switch(t.label){case 0:return"string"!=typeof e&&(0,of.throwError)(oh.ERRORS.INVALID_PARAMS,"username must be a string"),[4,this.oauthInstance.authApi.checkIfUserExist({username:e})];case 1:return[2,t.sent().exist]}})})},e.prototype.signOut=function(e){return oT(this,void 0,void 0,function(){var t,r;return oR(this,function(n){switch(n.label){case 0:return t=this.cache.keys.userInfoKey,[4,this.oauthInstance.authApi.signOut(e)];case 1:return r=n.sent(),[4,this.cache.removeStoreAsync(t)];case 2:return n.sent(),oP.fire(oA.LOGIN_STATE_CHANGED),[2,r]}})})},e.prototype.hasLoginState=function(){var e;if("async"===this.cache.mode){(0,of.printWarn)(oh.ERRORS.INVALID_OPERATION,"current platform's storage is asynchronous, please use getLoginState insteed");return}return(null===(e=this.oauthInstance)||void 0===e?void 0:e.authApi.hasLoginStateSync())?new oN({envId:this.config.env,cache:this.cache,oauthInstance:this.oauthInstance}):null},e.prototype.getLoginState=function(){return oT(this,void 0,void 0,function(){var e;return oR(this,function(t){switch(t.label){case 0:e=null,t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.oauthInstance.authApi.getLoginState()];case 2:return e=t.sent(),[3,4];case 3:return t.sent(),[2,null];case 4:if(e)return[2,new oN({envId:this.config.env,cache:this.cache,oauthInstance:this.oauthInstance})];return[2,null]}})})},e.prototype.getUserInfo=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.getUserInfo(e)]})})},e.prototype.getWedaUserInfo=function(){return oT(this,void 0,void 0,function(){return oR(this,function(e){return[2,this.oauthInstance.authApi.getWedaUserInfo()]})})},e.prototype.updateUserBasicInfo=function(e){return oT(this,void 0,void 0,function(){var t;return oR(this,function(r){switch(r.label){case 0:return[4,this.getLoginState()];case 1:if(!(t=r.sent()))return[3,3];return[4,t.user.updateUserBasicInfo(e)];case 2:r.sent(),r.label=3;case 3:return[2]}})})},e.prototype.getAuthHeader=function(){return console.error("Auth.getAuthHeader API 已废弃"),{}},e.prototype.bindWithProvider=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.bindWithProvider(e)]})})},e.prototype.queryUser=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.queryUserProfile(e)]})})},e.prototype.getAccessToken=function(){return oT(this,void 0,void 0,function(){return oR(this,function(e){switch(e.label){case 0:return[4,this.oauthInstance.oauth2client.getAccessToken()];case 1:return[2,{accessToken:e.sent(),env:this.config.env}]}})})},e.prototype.grantProviderToken=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.grantProviderToken(e)]})})},e.prototype.patchProviderToken=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.patchProviderToken(e)]})})},e.prototype.signInWithProvider=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){switch(t.label){case 0:return[4,this.oauthInstance.authApi.signInWithProvider(e)];case 1:return t.sent(),[2,this.createLoginState(e)]}})})},e.prototype.signInWithWechat=function(e){return void 0===e&&(e={}),oT(this,void 0,void 0,function(){return oR(this,function(t){switch(t.label){case 0:return[4,this.oauthInstance.authApi.signInWithWechat(e)];case 1:return t.sent(),[2,this.createLoginState(e)]}})})},e.prototype.grantToken=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){switch(t.label){case 0:return[4,this.oauthInstance.authApi.grantToken(e)];case 1:return t.sent(),[2,this.createLoginState()]}})})},e.prototype.genProviderRedirectUri=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.genProviderRedirectUri(e)]})})},e.prototype.resetPassword=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.resetPassword(e)]})})},e.prototype.deviceAuthorize=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.deviceAuthorize(e)]})})},e.prototype.sudo=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.sudo(e)]})})},e.prototype.deleteMe=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.deleteMe(e)]})})},e.prototype.getProviders=function(){return oT(this,void 0,void 0,function(){return oR(this,function(e){return[2,this.oauthInstance.authApi.getProviders()]})})},e.prototype.loginScope=function(){return oT(this,void 0,void 0,function(){return oR(this,function(e){return[2,this.oauthInstance.authApi.loginScope()]})})},e.prototype.loginGroups=function(){return oT(this,void 0,void 0,function(){return oR(this,function(e){return[2,this.oauthInstance.authApi.loginGroups()]})})},e.prototype.onLoginStateChanged=function(e){return oT(this,void 0,void 0,function(){var t,r=this;return oR(this,function(n){switch(n.label){case 0:return oP.on(oA.LOGIN_STATE_CHANGED,function(){return oT(r,void 0,void 0,function(){var t;return oR(this,function(r){switch(r.label){case 0:return[4,this.getLoginState()];case 1:return t=r.sent(),e.call(this,t),[2]}})})}),[4,this.getLoginState()];case 1:return t=n.sent(),e.call(this,t),[2]}})})},e.prototype.refreshTokenForce=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.refreshTokenForce(e)]})})},e.prototype.getCredentials=function(){return oT(this,void 0,void 0,function(){return oR(this,function(e){return[2,this.oauthInstance.authApi.getCredentials()]})})},e.prototype.setCredentials=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){switch(t.label){case 0:return[4,this.oauthInstance.oauth2client.setCredentials(e)];case 1:return t.sent(),[2]}})})},e.prototype.getProviderSubType=function(){return oT(this,void 0,void 0,function(){return oR(this,function(e){return[2,this.oauthInstance.authApi.getProviderSubType()]})})},e.prototype.createCaptchaData=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.createCaptchaData(e)]})})},e.prototype.verifyCaptchaData=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.verifyCaptchaData(e)]})})},e.prototype.getMiniProgramQrCode=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.getMiniProgramCode(e)]})})},e.prototype.getMiniProgramQrCodeStatus=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.getMiniProgramQrCodeStatus(e)]})})},e.prototype.modifyPassword=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.modifyPassword(e)]})})},e.prototype.modifyPasswordWithoutLogin=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.modifyPasswordWithoutLogin(e)]})})},e.prototype.getUserBehaviorLog=function(e){return oT(this,void 0,void 0,function(){return oR(this,function(t){return[2,this.oauthInstance.authApi.getUserBehaviorLog(e)]})})},e.prototype.toDefaultLoginPage=function(e){return void 0===e&&(e={}),oT(this,void 0,void 0,function(){var t,r,n,o;return oR(this,function(i){return t=e.config_version||"env",nm.isMatch()?wx.navigateTo({url:"/packages/$wd_system/pages/login/index"}):(n=new URL(r=e.redirect_uri||window.location.href),o="".concat(n.origin,"/__auth/?app_id=").concat(e.app_id||"","&env_id=").concat(this.config.env,"&client_id=").concat(this.config.clientId,"&config_version=").concat(t,"&redirect_uri=").concat(encodeURIComponent(r)),window.location.href=o),[2]})})},e.prototype.createLoginState=function(e,t){return oT(this,void 0,void 0,function(){var r;return oR(this,function(n){switch(n.label){case 0:return[4,(r=new oN({envId:this.config.env,cache:this.cache,oauthInstance:this.oauthInstance})).checkLocalStateAsync()];case 1:if(n.sent(),!(null==t?void 0:t.asyncRefreshUser))return[3,2];return r.user.refresh(e),[3,4];case 2:return[4,r.user.refresh(e)];case 3:n.sent(),n.label=4;case 4:return oP.fire(oA.LOGIN_STATE_CHANGED),[2,r]}})})},e.prototype.signInWithUsername=function(e){var t=e.verificationInfo,r=void 0===t?{verification_id:"",is_user:!1}:t,n=e.verificationCode,o=void 0===n?"":n,i=e.username,s=void 0===i?"":i,a=e.bindInfo,u=void 0===a?void 0:a,c=e.loginType,l=void 0===c?"":c;return oT(this,void 0,void 0,function(){var e,t,n,i,a,c,f;return oR(this,function(h){switch(h.label){case 0:return h.trys.push([0,8,,9]),[4,this.oauthInstance.authApi.verify({verification_id:r.verification_id,verification_code:o})];case 1:if(null==(e=h.sent())?void 0:e.error_code)throw e;if(t=e.verification_token,i={phone_number:n="+86 ".concat(s)},"email"===l&&(i={email:n=s}),!r.is_user)return[3,5];return[4,this.oauthInstance.authApi.signIn({username:n,verification_token:t})];case 2:if(null==(a=h.sent())?void 0:a.error_code)throw a;if(!u)return[3,4];return[4,this.oauthInstance.authApi.bindWithProvider({provider_token:null==u?void 0:u.providerToken})];case 3:if(null==(c=h.sent())?void 0:c.error_code)throw c;h.label=4;case 4:return[3,7];case 5:return[4,this.oauthInstance.authApi.signUp(oE(oE({},i),{verification_token:t,provider_token:null==u?void 0:u.providerId}))];case 6:if(null==(f=h.sent())?void 0:f.error_code)throw f;h.label=7;case 7:return[2,this.createLoginState()];case 8:throw h.sent();case 9:return[2]}})})},oI([(0,od.catchErrorsDecorator)({title:"绑定手机号失败",messages:["请确认以下各项：","  1 - 调用 auth().bindPhoneNumber() 的语法或参数是否正确","  2 - 当前环境是否开通了短信验证码登录","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[Object]),oO("design:returntype",Promise)],e.prototype,"bindPhoneNumber",null),oI([(0,od.catchErrorsDecorator)({title:"解除三方绑定失败",messages:["请确认以下各项：","  1 - 调用 auth().unbindProvider() 的语法或参数是否正确","  2 - 当前账户是否已经与此登录方式解绑","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[Object]),oO("design:returntype",Promise)],e.prototype,"unbindProvider",null),oI([(0,od.catchErrorsDecorator)({title:"绑定邮箱地址失败",messages:["请确认以下各项：","  1 - 调用 auth().bindEmail() 的语法或参数是否正确","  2 - 当前环境是否开通了邮箱密码登录","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[Object]),oO("design:returntype",void 0)],e.prototype,"bindEmail",null),oI([(0,od.catchErrorsDecorator)({title:"验证码验证失败",messages:["请确认以下各项：","  1 - 调用 auth().verify() 的语法或参数是否正确","  2 - 当前环境是否开通了手机验证码/邮箱登录","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[Object]),oO("design:returntype",Promise)],e.prototype,"verify",null),oI([(0,od.catchErrorsDecorator)({title:"获取验证码失败",messages:["请确认以下各项：","  1 - 调用 auth().getVerification() 的语法或参数是否正确","  2 - 当前环境是否开通了手机验证码/邮箱登录","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[Object,Object]),oO("design:returntype",Promise)],e.prototype,"getVerification",null),oI([(0,od.catchErrorsDecorator)({title:"获取用户信息失败",messages:["请确认以下各项：","  1 - 调用 auth().getCurrentUser() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[]),oO("design:returntype",Promise)],e.prototype,"getCurrentUser",null),oI([(0,od.catchErrorsDecorator)({title:"匿名登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了匿名登录","  2 - 调用 auth().signInAnonymously() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[Object]),oO("design:returntype",Promise)],e.prototype,"signInAnonymously",null),oI([(0,od.catchErrorsDecorator)({title:"小程序匿名登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了匿名登录","  2 - 调用 auth().signInAnonymouslyInWx() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[Object]),oO("design:returntype",Promise)],e.prototype,"signInAnonymouslyInWx",null),oI([(0,od.catchErrorsDecorator)({title:"小程序绑定OpenID失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了小程序openId静默登录","  2 - 调用 auth().bindOpenId() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[]),oO("design:returntype",Promise)],e.prototype,"bindOpenId",null),oI([(0,od.catchErrorsDecorator)({title:"小程序openId静默登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了小程序openId静默登录","  2 - 调用 auth().signInWithOpenId() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[Object]),oO("design:returntype",Promise)],e.prototype,"signInWithOpenId",null),oI([(0,od.catchErrorsDecorator)({title:"小程序unionId静默登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了小程序unionId静默登录","  2 - 调用 auth().signInWithUnionId() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[]),oO("design:returntype",Promise)],e.prototype,"signInWithUnionId",null),oI([(0,od.catchErrorsDecorator)({title:"小程序手机号授权登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了小程序手机号授权登录","  2 - 调用 auth().signInWithPhoneAuth() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[Object]),oO("design:returntype",Promise)],e.prototype,"signInWithPhoneAuth",null),oI([(0,od.catchErrorsDecorator)({title:"短信验证码登陆",messages:["请确认以下各项：","  1 - 当前环境是否开启了小程序短信验证码登陆","  2 - 调用 auth().signInWithSms() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[Object]),oO("design:returntype",Promise)],e.prototype,"signInWithSms",null),oI([(0,od.catchErrorsDecorator)({title:"邮箱验证码登陆",messages:["请确认以下各项：","  1 - 当前环境是否开启了邮箱登陆","  2 - 调用 auth().signInWithEmail() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[Object]),oO("design:returntype",Promise)],e.prototype,"signInWithEmail",null),oI([(0,od.catchErrorsDecorator)({title:"自定义登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了自定义登录","  2 - 调用 auth().signInWithCustomTicket() 的语法或参数是否正确","  3 - ticket 是否归属于当前环境","  4 - 创建 ticket 的自定义登录私钥是否过期","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[]),oO("design:returntype",Promise)],e.prototype,"signInWithCustomTicket",null),oI([(0,od.catchErrorsDecorator)({title:"注册失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了指定登录方式","  2 - 调用 auth().signUp() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[Object]),oO("design:returntype",Promise)],e.prototype,"signUp",null),oI([(0,od.catchErrorsDecorator)({title:"获取用户是否被占用失败",messages:["请确认以下各项：","  1 - 调用 auth().isUsernameRegistered() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[String]),oO("design:returntype",Promise)],e.prototype,"isUsernameRegistered",null),oI([(0,od.catchErrorsDecorator)({title:"用户登出失败",messages:["请确认以下各项：","  1 - 调用 auth().signOut() 的语法或参数是否正确","  2 - 当前用户是否为匿名登录（匿名登录不支持signOut）","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[Object]),oO("design:returntype",Promise)],e.prototype,"signOut",null),oI([(0,od.catchErrorsDecorator)({title:"获取本地登录态失败",messages:["请确认以下各项：","  1 - 调用 auth().getLoginState() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[]),oO("design:returntype",Promise)],e.prototype,"getLoginState",null),oI([(0,od.catchErrorsDecorator)({title:"获取用户信息失败",messages:["请确认以下各项：","  1 - 是否已登录","  2 - 调用 auth().getUserInfo() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[Object]),oO("design:returntype",Promise)],e.prototype,"getUserInfo",null),oI([(0,od.catchErrorsDecorator)({title:"获取微搭插件用户信息失败",messages:["请确认以下各项：","  1 - 是否已登录","  2 - 调用 auth().getWedaUserInfo() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[]),oO("design:returntype",Promise)],e.prototype,"getWedaUserInfo",null),oI([(0,od.catchErrorsDecorator)({title:"绑定第三方登录方式失败",messages:["请确认以下各项：","  1 - 调用 auth().bindWithProvider() 的语法或参数是否正确","  2 - 此账户是否已经绑定此第三方","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[Object]),oO("design:returntype",Promise)],e.prototype,"bindWithProvider",null),oI([(0,od.catchErrorsDecorator)({title:"获取身份源类型",messages:["请确认以下各项：","  1 - 调用 auth().getProviderSubType() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(oh.COMMUNITY_SITE_URL)]}),oO("design:type",Function),oO("design:paramtypes",[]),oO("design:returntype",Promise)],e.prototype,"getProviderSubType",null),e}(),oL={name:"auth",namespace:"auth",entity:function(e){if(void 0===e&&(e={region:"",persistence:"local"}),this.authInstance)return(0,of.printWarn)(oh.ERRORS.INVALID_OPERATION,"every cloudbase instance should has only one auth object"),this.authInstance;var t,r,n,o,i,s,a,u,c,l,f,h,d,p,y=this.platform.adapter,v=e.persistence||y.primaryStorage;v&&v!==this.config.persistence&&this.updateConfig({persistence:v});var m=(t=oE(oE({wxCloud:this.config.wxCloud,storage:this.config.storage},e),{persistence:this.config.persistence}),r={env:this.config.env,clientId:this.config.clientId,apiOrigin:this.request.getBaseEndPoint(),platform:this.platform,cache:this.cache,app:this,debug:this.config.debug},o=void 0===(n=t.region)?"ap-shanghai":n,s=(i=(null==r?void 0:r.platform)||(0,ov.useDefaultAdapter)()).runtime,u=(a=r||{}).env,c=a.clientId,l=a.debug,f=a.cache,h=a.app,(d=(r||{}).apiOrigin)||(d="https://".concat(u,".").concat(o,".tcb-api.tencentcloudapi.com")),p=new ol(oS({env:u,clientId:c,apiOrigin:d,storage:null==t?void 0:t.storage,baseRequest:null==t?void 0:t.baseRequest,request:null==t?void 0:t.request,anonymousSignInFunc:null==t?void 0:t.anonymousSignInFunc,captchaOptions:null==t?void 0:t.captchaOptions,wxCloud:null==t?void 0:t.wxCloud})),{authInstance:new oC({env:u,clientId:c,region:o,persistence:t.persistence,debug:l,cache:f||new oy.CloudbaseCache({persistence:t.persistence,keys:{userInfoKey:"user_info_".concat(u)},platformInfo:i}),runtime:s||"web",_fromApp:h,oauthInstance:p}),oauthInstance:p}),g=m.authInstance,b=m.oauthInstance;return this.oauthInstance=b,this.authInstance=g,this.authInstance}};try{cloudbase.registerComponent(oL)}catch(e){}var oj=function(){return(oj=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},oU=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},oD=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},ok="cloudrun";function oq(e){return oU(this,void 0,void 0,function(){var t,r,n,o,i,s,a,u,c;return oD(this,function(l){switch(l.label){case 0:return t=e.name,r=e.data,o=void 0===(n=e.path)?"":n,i=e.method,a=void 0===(s=e.header)?{}:s,u="https://".concat(this.config.env,".api.tcloudbasegateway.com/v1/cloudrun/").concat(t),c=o.startsWith("/")?o:"/".concat(o),[4,this.request.fetch({method:i||"POST",headers:Object.assign({},{"Content-Type":"application/json; charset=utf-8"},a),body:r,url:"".concat(u).concat(c)})];case 1:return[4,l.sent().data];case 2:return[2,l.sent()]}})})}var oM=new(function(){function e(){}return e.prototype.callContainer=function(e){return oU(this,void 0,void 0,function(){var t,r,n;return oD(this,function(o){switch(o.label){case 0:if(t=e.name,r=e.data,!t)throw Error(JSON.stringify({code:x.INVALID_PARAMS,msg:"[".concat(ok,".callContainer] invalid name")}));try{n=r?JSON.stringify(r):""}catch(e){throw Error(JSON.stringify({code:x.INVALID_PARAMS,msg:"[".concat(ok,".callContainer] invalid data")}))}return[4,oq.call(this,oj(oj({},e),{data:n}))];case 1:return[2,o.sent()]}})})},e}()),oB={name:ok,entity:{callContainer:oM.callContainer}};try{cloudbase.registerComponent(oB)}catch(e){}var oW=function(){return(oW=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},oF=function(e,t,r,n){var o,i=arguments.length,s=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,r,n);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(i<3?o(s):i>3?o(t,r,s):o(t,r))||s);return i>3&&s&&Object.defineProperty(t,r,s),s},o$=function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},oV=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},oG="functions",oH=new(function(){function e(){}return e.prototype.callFunction=function(e,t){var r,n,o,i,s,a;return o=this,i=void 0,s=void 0,a=function(){var o,i,s,a,u,c,l,f,h,d,p,y,v,m,g,b,_,w;return oV(this,function(S){switch(S.label){case 0:var E,I;if(o=e.name,i=e.data,s=e.query,a=e.parse,u=e.search,c=e.type,f=void 0===(l=e.path)?"":l,h=e.method,p=void 0===(d=e.header)?{}:d,!o)throw Error(JSON.stringify({code:x.INVALID_PARAMS,msg:"[".concat(oG,".callFunction] invalid function name")}));try{y=i?JSON.stringify(i):""}catch(e){throw Error(JSON.stringify({code:x.INVALID_PARAMS,msg:"[".concat(oG,".callFunction] invalid data")}))}if("cloudrun"!==c)return[3,2];return E=new Date().getTime(),I=(null==performance?void 0:performance.now)&&1e3*performance.now()||0,v="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random();return E>0?(t=(E+t)%16|0,E=Math.floor(E/16)):(t=(I+t)%16|0,I=Math.floor(I/16)),("x"===e?t:7&t|8).toString(16)}),[4,oq.call(this,{name:o,data:y,path:f,method:h,header:oW(oW({},p),{"X-Request-Id":v})})];case 1:return[2,{result:m=S.sent(),requestId:v}];case 2:g="functions.invokeFunction",b={inQuery:s,parse:a,search:u,function_name:o,request_data:y},_=this.request,S.label=3;case 3:return S.trys.push([3,5,,6]),[4,_.send(g,b,{defaultQuery:(null===(r=null==i?void 0:i.params)||void 0===r?void 0:r.action)?{action:null===(n=null==i?void 0:i.params)||void 0===n?void 0:n.action}:{}})];case 4:if((w=S.sent()).code)return[2,D(t,null,w)];if(m=w.data.response_data,a)return[2,D(t,null,{result:m,requestId:w.requestId})];try{return m=JSON.parse(w.data.response_data),[2,D(t,null,{result:m,requestId:w.requestId})]}catch(e){D(t,Error("[".concat(R,"][").concat(x.INVALID_PARAMS,"][").concat(oG,".callFunction] response data must be json")))}return[3,6];case 5:return D(t,S.sent()),[3,6];case 6:return[2]}})},new(s||(s=Promise))(function(e,t){function r(e){try{u(a.next(e))}catch(e){t(e)}}function n(e){try{u(a.throw(e))}catch(e){t(e)}}function u(t){var o;t.done?e(t.value):((o=t.value)instanceof s?o:new s(function(e){e(o)})).then(r,n)}u((a=a.apply(o,i||[])).next())})},oF([ee({customInfo:{className:"Cloudbase",methodName:"callFunction"},title:"函数调用失败",messages:["请确认以下各项：","  1 - 调用 callFunction() 的语法或参数是否正确","  2 - 当前环境下是否存在此函数","  3 - 函数安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(P)]}),o$("design:type",Function),o$("design:paramtypes",[Object,Function]),o$("design:returntype",Promise)],e.prototype,"callFunction",null),e}()),oK={name:oG,entity:{callFunction:oH.callFunction}};try{cloudbase.registerComponent(oK)}catch(e){}var oJ=function(){return(oJ=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},oz=function(e,t,r,n){var o,i=arguments.length,s=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,r,n);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(i<3?o(s):i>3?o(t,r,s):o(t,r))||s);return i>3&&s&&Object.defineProperty(t,r,s),s},oY=function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},oQ=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},oZ=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}};!function(e){e.put="put",e.post="post"}(d||(d={}));var oX="storage",o0=new(function(){function e(){}return e.prototype.uploadFile=function(e,t){return oQ(this,void 0,void 0,function(){var r,n,o,i,s,a,u,c,l,f,h,p,y,v,m,g,b,_,w,S,E,I,O,T,A,P;return oZ(this,function(N){switch(N.label){case 0:if(r=e.cloudPath,n=e.filePath,o=e.onUploadProgress,s=void 0===(i=e.method)?"put":i,u=void 0===(a=e.headers)?{}:a,!L(r)||!n)throw Error(JSON.stringify({code:x.INVALID_PARAMS,msg:"[".concat(oX,".uploadFile] invalid params")}));return c=({put:d.put,post:d.post})[s.toLocaleLowerCase()]||d.put,l="storage.getUploadMetadata",f=this.request,h={path:r,method:c},c===d.put&&(h.headers=u),[4,f.send(l,h)];case 1:return v=(y=(p=N.sent()).data).url,m=y.authorization,g=y.token,b=y.fileId,_=y.cosFileId,w=y.download_url,S=p.requestId,I=oJ(oJ({},E={url:v,file:n,name:r,onUploadProgress:o}),{method:d.put,headers:oJ(oJ({},u),{authorization:m,"x-cos-meta-fileid":_,"x-cos-security-token":g})}),O=oJ(oJ({},E),{method:d.post,data:{key:r,signature:m,"x-cos-meta-fileid":_,success_action_status:"201","x-cos-security-token":g}}),(P={})[d.put]={params:I,isSuccess:function(e){return e>=200&&e<300}},P[d.post]={params:O,isSuccess:function(e){return 201===e}},T=P,[4,f.upload(T[c].params)];case 2:if(A=N.sent(),T[c].isSuccess(A.statusCode))return[2,D(t,null,{fileID:b,download_url:w,requestId:S})];return[2,D(t,Error("[".concat(R,"][").concat(x.OPERATION_FAIL,"][").concat(oX,"]:").concat(A.data)))]}})})},e.prototype.getUploadMetadata=function(e,t){return oQ(this,void 0,void 0,function(){var r,n,o;return oZ(this,function(i){switch(i.label){case 0:if(!L(r=e.cloudPath))throw Error(JSON.stringify({code:x.INVALID_PARAMS,msg:"[".concat(oX,".getUploadMetadata] invalid cloudPath")}));n=this.request,o="storage.getUploadMetadata",i.label=1;case 1:return i.trys.push([1,3,,4]),[4,n.send(o,{path:r})];case 2:return[2,D(t,null,i.sent())];case 3:return[2,D(t,i.sent())];case 4:return[2]}})})},e.prototype.deleteFile=function(e,t){return oQ(this,void 0,void 0,function(){var r,n,o,i,s,a;return oZ(this,function(u){switch(u.label){case 0:if(!(r=e.fileList)||!C(r)||0===r.length)throw Error(JSON.stringify({code:x.INVALID_PARAMS,msg:"[".concat(oX,".deleteFile] fileList must not be empty")}));for(n=0,o=r;n<o.length;n++)if(!(i=o[n])||!L(i))throw Error(JSON.stringify({code:x.INVALID_PARAMS,msg:"[".concat(oX,".deleteFile] fileID must be string")}));return s="storage.batchDeleteFile",[4,this.request.send(s,{fileid_list:r})];case 1:if((a=u.sent()).code)return[2,D(t,null,a)];return[2,D(t,null,{fileList:a.data.delete_list,requestId:a.requestId})]}})})},e.prototype.getTempFileURL=function(e,t){return oQ(this,void 0,void 0,function(){var r,n,o,i,s,a,u;return oZ(this,function(c){switch(c.label){case 0:if(!(r=e.fileList)||!C(r)||0===r.length)throw Error(JSON.stringify({code:x.INVALID_PARAMS,msg:"[".concat(oX,".getTempFileURL] fileList must not be empty")}));for(o=0,n=[],i=r;o<i.length;o++){var l;if(l=s=i[o],"[object Object]"===Object.prototype.toString.call(l)){if(!Object.prototype.hasOwnProperty.call(s,"fileID")||!Object.prototype.hasOwnProperty.call(s,"maxAge"))throw Error(JSON.stringify({code:x.INVALID_PARAMS,msg:"[".concat(oX,".getTempFileURL] file info must include fileID and maxAge")}));n.push({fileid:s.fileID,max_age:s.maxAge})}else if(L(s))n.push({fileid:s});else throw Error(JSON.stringify({code:x.INVALID_PARAMS,msg:"[".concat(oX,".getTempFileURL] invalid fileList")}))}return a="storage.batchGetDownloadUrl",[4,this.request.send(a,{file_list:n})];case 1:if((u=c.sent()).code)return[2,D(t,null,u)];return[2,D(t,null,{fileList:u.data.download_list,requestId:u.requestId})]}})})},e.prototype.downloadFile=function(e,t){return oQ(this,void 0,void 0,function(){var r,n,o,i;return oZ(this,function(s){switch(s.label){case 0:if(!L(r=e.fileID))throw Error(JSON.stringify({code:x.INVALID_PARAMS,msg:"[".concat(oX,".getTempFileURL] fileID must be string")}));return[4,this.getTempFileURL.call(this,{fileList:[{fileID:r,maxAge:600}]})];case 1:if("SUCCESS"!==(n=s.sent().fileList[0]).code)return[2,D(t,n)];return o=this.request,i=encodeURI(n.download_url),[4,o.download({url:i})];case 2:return[2,D(t,null,s.sent())]}})})},oz([ee({customInfo:{className:"Cloudbase",methodName:"uploadFile"},title:"上传文件失败",messages:["请确认以下各项：","  1 - 调用 uploadFile() 的语法或参数是否正确","  2 - 当前域名是否在安全域名列表中：https://console.cloud.tencent.com/tcb/env/safety","  3 - 云存储安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(P)]}),oY("design:type",Function),oY("design:paramtypes",[Object,Function]),oY("design:returntype",Promise)],e.prototype,"uploadFile",null),oz([ee({customInfo:{className:"Cloudbase",methodName:"getUploadMetadata"},title:"获取上传元信息失败",messages:["请确认以下各项：","  1 - 调用 getUploadMetadata() 的语法或参数是否正确","  2 - 当前域名是否在安全域名列表中：https://console.cloud.tencent.com/tcb/env/safety","  3 - 云存储安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(P)]}),oY("design:type",Function),oY("design:paramtypes",[Object,Function]),oY("design:returntype",Promise)],e.prototype,"getUploadMetadata",null),oz([ee({customInfo:{className:"Cloudbase",methodName:"deleteFile"},title:"删除文件失败",messages:["请确认以下各项：","  1 - 调用 deleteFile() 的语法或参数是否正确","  2 - 当前域名是否在安全域名列表中：https://console.cloud.tencent.com/tcb/env/safety","  3 - 云存储安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(P)]}),oY("design:type",Function),oY("design:paramtypes",[Object,Function]),oY("design:returntype",Promise)],e.prototype,"deleteFile",null),oz([ee({customInfo:{className:"Cloudbase",methodName:"getTempFileURL"},title:"获取文件下载链接",messages:["请确认以下各项：","  1 - 调用 getTempFileURL() 的语法或参数是否正确","  2 - 当前域名是否在安全域名列表中：https://console.cloud.tencent.com/tcb/env/safety","  3 - 云存储安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(P)]}),oY("design:type",Function),oY("design:paramtypes",[Object,Function]),oY("design:returntype",Promise)],e.prototype,"getTempFileURL",null),oz([ee({customInfo:{className:"Cloudbase",methodName:"downloadFile"},title:"下载文件失败",messages:["请确认以下各项：","  1 - 调用 downloadFile() 的语法或参数是否正确","  2 - 当前域名是否在安全域名列表中：https://console.cloud.tencent.com/tcb/env/safety","  3 - 云存储安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(P)]}),oY("design:type",Function),oY("design:paramtypes",[Object,Function]),oY("design:returntype",Promise)],e.prototype,"downloadFile",null),e}()),o1={name:oX,entity:{uploadFile:o0.uploadFile,deleteFile:o0.deleteFile,getTempFileURL:o0.getTempFileURL,downloadFile:o0.downloadFile,getUploadMetadata:o0.getUploadMetadata}};try{cloudbase.registerComponent(o1)}catch(e){}var o2=r(41588),o3=r.n(o2),o4=r(43130),o5=r.n(o4),o6=r(78147),o8=r.n(o6);function o7(e){return void 0===e&&(e=""),"".concat(e?"".concat(e,"_"):"").concat(+new Date,"_").concat(Math.random())}var o9=function(e){this.close=e.close,this.onChange=e.onChange,this.onError=e.onError,e.debug&&Object.defineProperty(this,"virtualClient",{get:function(){return e.virtualClient}})},ie=function(e){var t,r,n=e.id,o=e.docChanges,i=e.docs,s=e.msgType,a=e.type;Object.defineProperties(this,{id:{get:function(){return n},enumerable:!0},docChanges:{get:function(){return t||(t=JSON.parse(JSON.stringify(o))),t},enumerable:!0},docs:{get:function(){return r||(r=JSON.parse(JSON.stringify(i))),r},enumerable:!0},msgType:{get:function(){return s},enumerable:!0},type:{get:function(){return a},enumerable:!0}})},it=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),ir=function(e){function t(t){var r=e.call(this,"Watch Error ".concat(JSON.stringify(t.msgData)," (requestid: ").concat(t.requestId,")"))||this;return r.isRealtimeErrorMessageError=!0,r.payload=t,r}return it(t,e),t}(Error),io=function(e){return null==e?void 0:e.isRealtimeErrorMessageError},ii=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type="timeout",t.payload=null,t.generic=!0,t}return it(t,e),t}(Error),is=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type="cancelled",t.payload=null,t.generic=!0,t}return it(t,e),t}(Error),ia=function(e){function t(t){var r=e.call(this,t.errMsg)||this;return r.errCode="UNKNOWN_ERROR",Object.defineProperties(r,{message:{get:function(){return"errCode: ".concat(this.errCode," ").concat(iu[this.errCode]||""," | errMsg: ").concat(this.errMsg)},set:function(e){this.errMsg=e}}}),r.errCode=t.errCode||"UNKNOWN_ERROR",r.errMsg=t.errMsg,r}return it(t,e),Object.defineProperty(t.prototype,"message",{get:function(){return"errCode: ".concat(this.errCode," | errMsg: ").concat(this.errMsg)},set:function(e){this.errMsg=e},enumerable:!1,configurable:!0}),t}(Error),iu={UNKNOWN_ERROR:"UNKNOWN_ERROR",SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL:"SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL",SDK_DATABASE_REALTIME_LISTENER_RECONNECT_WATCH_FAIL:"SDK_DATABASE_REALTIME_LISTENER_RECONNECT_WATCH_FAIL",SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL:"SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL",SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL:"SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL",SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG:"SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG",SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA:"SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA",SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_ERROR:"SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_ERROR",SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_CLOSED:"SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_CLOSED",SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL:"SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL",SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR:"SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR"},ic=function(e){return void 0===e&&(e=0),new Promise(function(t){return setTimeout(t,e)})},il=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},ih=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},id=function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};!function(e){e.LOGGINGIN="LOGGINGIN",e.INITING="INITING",e.REBUILDING="REBUILDING",e.ACTIVE="ACTIVE",e.ERRORED="ERRORED",e.CLOSING="CLOSING",e.CLOSED="CLOSED",e.PAUSED="PAUSED",e.RESUMING="RESUMING"}(p||(p={}));var ip=function(){function e(e){var t=this;this.watchStatus=p.INITING,this.wsLogin=function(e,r){return il(t,void 0,void 0,function(){var t;return ih(this,function(n){switch(n.label){case 0:return this.watchStatus=p.LOGGINGIN,[4,this.login(e,r)];case 1:return t=n.sent(),this.envId||(this.envId=t.envId),[2,t]}})})},this.initWatch=function(e){return il(t,void 0,void 0,function(){var t,r=this;return ih(this,function(n){switch(n.label){case 0:if(null!==this.initWatchPromise&&void 0!==this.initWatchPromise)return[2,this.initWatchPromise];this.initWatchPromise=new Promise(function(t,n){il(r,void 0,void 0,function(){var r,o,i,s,a,u,c,l,f,h;return ih(this,function(d){switch(d.label){case 0:if(d.trys.push([0,3,,4]),this.watchStatus===p.PAUSED)return console.log("[realtime] initWatch cancelled on pause"),[2,t()];return[4,this.wsLogin(this.envId,e)];case 1:if(r=d.sent().envId,this.watchStatus===p.PAUSED)return console.log("[realtime] initWatch cancelled on pause"),[2,t()];return this.watchStatus=p.INITING,o={watchId:this.watchId,requestId:o7(),msgType:"INIT_WATCH",msgData:{envId:r,collName:this.collectionName,query:this.query,limit:this.limit,orderBy:this.orderBy}},[4,this.send({msg:o,waitResponse:!0,skipOnMessage:!0,timeout:1e4})];case 2:if(a=(s=(i=d.sent()).msgData).events,u=s.currEvent,this.sessionInfo={queryID:i.msgData.queryID,currentEventId:u-1,currentDocs:[]},a.length>0){for(c=0,l=a;c<l.length;c++)l[c].ID=u;this.handleServerEvents(i)}else this.sessionInfo.currentEventId=u,f=new ie({id:u,docChanges:[],docs:[],type:"init"}),this.listener.onChange(f),this.scheduleSendACK();return this.onWatchStart(this,this.sessionInfo.queryID),this.watchStatus=p.ACTIVE,this.availableRetries.INIT_WATCH=2,t(),[3,4];case 3:return h=d.sent(),this.handleWatchEstablishmentError(h,{operationName:"INIT_WATCH",resolve:t,reject:n}),[3,4];case 4:return[2]}})})}),t=!1,n.label=1;case 1:return n.trys.push([1,,3,4]),[4,this.initWatchPromise];case 2:return n.sent(),t=!0,[3,4];case 3:return this.initWatchPromise=void 0,[7];case 4:return console.log("[realtime] initWatch ".concat(t?"success":"fail")),[2]}})})},this.rebuildWatch=function(e){return il(t,void 0,void 0,function(){var t,r=this;return ih(this,function(n){switch(n.label){case 0:if(null!==this.rebuildWatchPromise&&void 0!==this.rebuildWatchPromise)return[2,this.rebuildWatchPromise];this.rebuildWatchPromise=new Promise(function(t,n){il(r,void 0,void 0,function(){var r,o,i,s;return ih(this,function(a){switch(a.label){case 0:if(a.trys.push([0,3,,4]),this.watchStatus===p.PAUSED)return console.log("[realtime] rebuildWatch cancelled on pause"),[2,t()];return[4,this.wsLogin(this.envId,e)];case 1:if(r=a.sent().envId,!this.sessionInfo)throw Error("can not rebuildWatch without a successful initWatch (lack of sessionInfo)");if(this.watchStatus===p.PAUSED)return console.log("[realtime] rebuildWatch cancelled on pause"),[2,t()];return this.watchStatus=p.REBUILDING,o={watchId:this.watchId,requestId:o7(),msgType:"REBUILD_WATCH",msgData:{envId:r,collName:this.collectionName,queryID:this.sessionInfo.queryID,eventID:this.sessionInfo.currentEventId}},[4,this.send({msg:o,waitResponse:!0,skipOnMessage:!1,timeout:1e4})];case 2:return i=a.sent(),this.handleServerEvents(i),this.watchStatus=p.ACTIVE,this.availableRetries.REBUILD_WATCH=2,t(),[3,4];case 3:return s=a.sent(),this.handleWatchEstablishmentError(s,{operationName:"REBUILD_WATCH",resolve:t,reject:n}),[3,4];case 4:return[2]}})})}),t=!1,n.label=1;case 1:return n.trys.push([1,,3,4]),[4,this.rebuildWatchPromise];case 2:return n.sent(),t=!0,[3,4];case 3:return this.rebuildWatchPromise=void 0,[7];case 4:return console.log("[realtime] rebuildWatch ".concat(t?"success":"fail")),[2]}})})},this.handleWatchEstablishmentError=function(e,r){return il(t,void 0,void 0,function(){var t,n,o,i=this;return ih(this,function(s){return t="INIT_WATCH"===r.operationName,n=function(){i.closeWithError(new ia({errCode:t?iu.SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL:iu.SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL,errMsg:e})),r.reject(e)},o=function(e){i.useRetryTicket(r.operationName)?t?(i.initWatchPromise=void 0,r.resolve(i.initWatch(e))):(i.rebuildWatchPromise=void 0,r.resolve(i.rebuildWatch(e))):n()},this.handleCommonError(e,{onSignError:function(){return o(!0)},onTimeoutError:function(){return o(!1)},onNotRetryableError:n,onCancelledError:r.reject,onUnknownError:function(){il(i,void 0,void 0,function(){var e,t=this;return ih(this,function(n){switch(n.label){case 0:if(n.trys.push([0,8,,9]),e=function(){return il(t,void 0,void 0,function(){return ih(this,function(e){switch(e.label){case 0:return this.pause(),[4,this.onceWSConnected()];case 1:return e.sent(),o(!0),[2]}})})},this.isWSConnected())return[3,2];return[4,e()];case 1:case 5:return n.sent(),[3,7];case 2:return[4,ic(100)];case 3:if(n.sent(),this.watchStatus!==p.PAUSED)return[3,4];return r.reject(new is("".concat(r.operationName," cancelled due to pause after unknownError"))),[3,7];case 4:if(this.isWSConnected())return[3,6];return[4,e()];case 6:o(!1),n.label=7;case 7:return[3,9];case 8:return n.sent(),o(!0),[3,9];case 9:return[2]}})})}}),[2]})})},this.closeWatch=function(){return il(t,void 0,void 0,function(){var e,t,r;return ih(this,function(n){switch(n.label){case 0:if(e=this.sessionInfo?this.sessionInfo.queryID:"",this.watchStatus!==p.ACTIVE)return this.watchStatus=p.CLOSED,this.onWatchClose(this,e),[2];n.label=1;case 1:return n.trys.push([1,3,4,5]),this.watchStatus=p.CLOSING,t={watchId:this.watchId,requestId:o7(),msgType:"CLOSE_WATCH",msgData:null},[4,this.send({msg:t})];case 2:return n.sent(),this.sessionInfo=void 0,this.watchStatus=p.CLOSED,[3,5];case 3:return r=n.sent(),this.closeWithError(new ia({errCode:iu.SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL,errMsg:r})),[3,5];case 4:return this.onWatchClose(this,e),[7];case 5:return[2]}})})},this.scheduleSendACK=function(){t.clearACKSchedule(),t.ackTimeoutId=setTimeout(function(){t.waitExpectedTimeoutId?t.scheduleSendACK():t.sendACK()},1e4)},this.clearACKSchedule=function(){t.ackTimeoutId&&clearTimeout(t.ackTimeoutId)},this.sendACK=function(){return il(t,void 0,void 0,function(){var e,t,r;return ih(this,function(n){switch(n.label){case 0:if(n.trys.push([0,2,,3]),this.watchStatus!==p.ACTIVE)return this.scheduleSendACK(),[2];if(!this.sessionInfo)return console.warn("[realtime listener] can not send ack without a successful initWatch (lack of sessionInfo)"),[2];return e={watchId:this.watchId,requestId:o7(),msgType:"CHECK_LAST",msgData:{queryID:this.sessionInfo.queryID,eventID:this.sessionInfo.currentEventId}},[4,this.send({msg:e})];case 1:return n.sent(),this.scheduleSendACK(),[3,3];case 2:if(io(t=n.sent()))switch((r=t.payload).msgData.code){case"CHECK_LOGIN_FAILED":case"SIGN_EXPIRED_ERROR":case"SIGN_INVALID_ERROR":case"SIGN_PARAM_INVALID":return this.rebuildWatch(),[2];case"QUERYID_INVALID_ERROR":case"SYS_ERR":case"INVALIID_ENV":case"COLLECTION_PERMISSION_DENIED":return this.closeWithError(new ia({errCode:iu.SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL,errMsg:r.msgData.code})),[2]}return this.availableRetries.CHECK_LAST&&this.availableRetries.CHECK_LAST>0?(this.availableRetries.CHECK_LAST-=1,this.scheduleSendACK()):this.closeWithError(new ia({errCode:iu.SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL,errMsg:t})),[3,3];case 3:return[2]}})})},this.handleCommonError=function(e,t){if(io(e))switch(e.payload.msgData.code){case"CHECK_LOGIN_FAILED":case"SIGN_EXPIRED_ERROR":case"SIGN_INVALID_ERROR":case"SIGN_PARAM_INVALID":t.onSignError(e);return;default:t.onNotRetryableError(e);return}else{if("timeout"===e.type){t.onTimeoutError(e);return}if("cancelled"===e.type){t.onCancelledError(e);return}}t.onUnknownError(e)},this.watchId="watchid_".concat(+new Date,"_").concat(Math.random()),this.envId=e.envId,this.collectionName=e.collectionName,this.query=e.query,this.limit=e.limit,this.orderBy=e.orderBy,this.send=e.send,this.login=e.login,this.isWSConnected=e.isWSConnected,this.onceWSConnected=e.onceWSConnected,this.getWaitExpectedTimeoutLength=e.getWaitExpectedTimeoutLength,this.onWatchStart=e.onWatchStart,this.onWatchClose=e.onWatchClose,this.debug=e.debug,this.availableRetries={INIT_WATCH:2,REBUILD_WATCH:2,CHECK_LAST:2},this.listener=new o9({close:function(){t.closeWatch()},onChange:e.onChange,onError:e.onError,debug:this.debug,virtualClient:this}),this.initWatch()}return e.prototype.onMessage=function(e){var t=this;switch(this.watchStatus){case p.PAUSED:if("ERROR"!==e.msgType)return;break;case p.LOGGINGIN:case p.INITING:case p.REBUILDING:console.warn("[realtime listener] internal non-fatal error: unexpected message received while ".concat(this.watchStatus));return;case p.CLOSED:console.warn("[realtime listener] internal non-fatal error: unexpected message received when the watch has closed");return;case p.ERRORED:console.warn("[realtime listener] internal non-fatal error: unexpected message received when the watch has ended with error");return}if(!this.sessionInfo){console.warn("[realtime listener] internal non-fatal error: sessionInfo not found while message is received.");return}switch(this.scheduleSendACK(),e.msgType){case"NEXT_EVENT":console.warn("nextevent ".concat(e.msgData.currEvent," ignored"),e),this.handleServerEvents(e);break;case"CHECK_EVENT":this.sessionInfo.currentEventId<e.msgData.currEvent&&(this.sessionInfo.expectEventId=e.msgData.currEvent,this.clearWaitExpectedEvent(),this.waitExpectedTimeoutId=setTimeout(function(){t.rebuildWatch()},this.getWaitExpectedTimeoutLength()),console.log("[realtime] waitExpectedTimeoutLength ".concat(this.getWaitExpectedTimeoutLength())));break;case"ERROR":this.closeWithError(new ia({errCode:iu.SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG,errMsg:"".concat(e.msgData.code," - ").concat(e.msgData.message)}));break;default:console.warn("[realtime listener] virtual client receive unexpected msg ".concat(e.msgType,": "),e)}},e.prototype.closeWithError=function(e){var t;this.watchStatus=p.ERRORED,this.clearACKSchedule(),this.listener.onError(e),this.onWatchClose(this,(null===(t=this.sessionInfo)||void 0===t?void 0:t.queryID)||""),console.log("[realtime] client closed (".concat(this.collectionName," ").concat(this.query,") (watchId ").concat(this.watchId,")"))},e.prototype.pause=function(){this.watchStatus=p.PAUSED,console.log("[realtime] client paused (".concat(this.collectionName," ").concat(this.query,") (watchId ").concat(this.watchId,")"))},e.prototype.resume=function(){return il(this,void 0,void 0,function(){var e;return ih(this,function(t){switch(t.label){case 0:this.watchStatus=p.RESUMING,console.log("[realtime] client resuming with ".concat(this.sessionInfo?"REBUILD_WATCH":"INIT_WATCH"," (").concat(this.collectionName," ").concat(this.query,") (").concat(this.watchId,")")),t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.sessionInfo?this.rebuildWatch():this.initWatch()];case 2:return t.sent(),console.log("[realtime] client successfully resumed (".concat(this.collectionName," ").concat(this.query,") (").concat(this.watchId,")")),[3,4];case 3:return e=t.sent(),console.error("[realtime] client resume failed (".concat(this.collectionName," ").concat(this.query,") (").concat(this.watchId,")"),e),[3,4];case 4:return[2]}})})},e.prototype.useRetryTicket=function(e){return!!this.availableRetries[e]&&this.availableRetries[e]>0&&(this.availableRetries[e]-=1,console.log("[realtime] ".concat(e," use a retry ticket, now only ").concat(this.availableRetries[e]," retry left")),!0)},e.prototype.handleServerEvents=function(e){return il(this,void 0,void 0,function(){var t;return ih(this,function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),this.scheduleSendACK(),[4,this.handleServerEventsInternel(e)];case 1:return r.sent(),this.postHandleServerEventsValidityCheck(e),[3,3];case 2:throw console.error("[realtime listener] internal non-fatal error: handle server events failed with error: ",t=r.sent()),t;case 3:return[2]}})})},e.prototype.handleServerEventsInternel=function(e){return il(this,void 0,void 0,function(){var t,r,n,o,i,s,a,u,c,l,f,h;return ih(this,function(d){switch(d.label){case 0:if(t=e.requestId,r=e.msgData.events,n=e.msgType,!r.length||!this.sessionInfo)return[2];o=this.sessionInfo;try{i=r.map(iy)}catch(e){return this.closeWithError(new ia({errCode:iu.SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA,errMsg:e})),[2]}s=id([],o.currentDocs,!0),a=!1,u=function(r,u){var l,f,h,d,p,y,v,m,g,b,_,w;return ih(this,function(S){switch(S.label){case 0:if(l=i[r],!(o.currentEventId>=l.id))return[3,1];return!i[r-1]||l.id>i[r-1].id?console.warn("[realtime] duplicate event received, cur ".concat(o.currentEventId," but got ").concat(l.id)):console.error("[realtime listener] server non-fatal error: events out of order (the latter event's id is smaller than that of the former) (requestId ".concat(t,")")),[2,"continue"];case 1:if(o.currentEventId!==l.id-1)return[3,2];switch(l.dataType){case"update":if(!l.doc)switch(l.queueType){case"update":case"dequeue":if(f=s.find(function(e){return e._id===l.docId})){if(h=o8()(f),l.updatedFields&&Object.keys(l.updatedFields).forEach(function(e){o3()(h,e,l.updatedFields[e])}),l.removedFields)for(d=0,p=l.removedFields;d<p.length;d++)y=p[d],o5()(h,y);l.doc=h}else console.error("[realtime listener] internal non-fatal server error: unexpected update dataType event where no doc is associated.");break;case"enqueue":throw v=new ia({errCode:iu.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,errMsg:'HandleServerEvents: full doc is not provided with dataType="update" and queueType="enqueue" (requestId '.concat(e.requestId,")")}),c.closeWithError(v),v}break;case"replace":if(!l.doc)throw v=new ia({errCode:iu.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,errMsg:'HandleServerEvents: full doc is not provided with dataType="replace" (requestId '.concat(e.requestId,")")}),c.closeWithError(v),v;break;case"remove":(m=s.find(function(e){return e._id===l.docId}))?l.doc=m:console.error("[realtime listener] internal non-fatal server error: unexpected remove event where no doc is associated.");break;case"limit":if(!l.doc)switch(l.queueType){case"dequeue":(m=s.find(function(e){return e._id===l.docId}))?l.doc=m:console.error("[realtime listener] internal non-fatal server error: unexpected limit dataType event where no doc is associated.");break;case"enqueue":throw v=new ia({errCode:iu.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,errMsg:'HandleServerEvents: full doc is not provided with dataType="limit" and queueType="enqueue" (requestId '.concat(e.requestId,")")}),c.closeWithError(v),v}}switch(l.queueType){case"init":a?s.push(l.doc):(a=!0,s=[l.doc]);break;case"enqueue":s.push(l.doc);break;case"dequeue":(g=s.findIndex(function(e){return e._id===l.docId}))>-1?s.splice(g,1):console.error("[realtime listener] internal non-fatal server error: unexpected dequeue event where no doc is associated.");break;case"update":(g=s.findIndex(function(e){return e._id===l.docId}))>-1?s[g]=l.doc:console.error("[realtime listener] internal non-fatal server error: unexpected queueType update event where no doc is associated.")}return(r===u-1||i[r+1]&&i[r+1].id!==l.id)&&(b=id([],s,!0),_=i.slice(0,r+1).filter(function(e){return e.id===l.id}),c.sessionInfo.currentEventId=l.id,c.sessionInfo.currentDocs=s,w=new ie({id:l.id,docChanges:_,docs:b,msgType:n}),c.listener.onChange(w)),[3,4];case 2:return console.warn("[realtime listener] event received is out of order, cur ".concat(c.sessionInfo.currentEventId," but got ").concat(l.id)),[4,c.rebuildWatch()];case 3:return S.sent(),[2,{value:void 0}];case 4:return[2]}})},c=this,l=0,f=i.length,d.label=1;case 1:if(!(l<f))return[3,4];return[5,u(l,f)];case 2:if("object"==typeof(h=d.sent()))return[2,h.value];d.label=3;case 3:return l++,[3,1];case 4:return[2]}})})},e.prototype.postHandleServerEventsValidityCheck=function(e){if(!this.sessionInfo){console.error("[realtime listener] internal non-fatal error: sessionInfo lost after server event handling, this should never occur");return}if(this.sessionInfo.expectEventId&&this.sessionInfo.currentEventId>=this.sessionInfo.expectEventId&&this.clearWaitExpectedEvent(),this.sessionInfo.currentEventId<e.msgData.currEvent){console.warn("[realtime listener] internal non-fatal error: client eventId does not match with server event id after server event handling");return}},e.prototype.clearWaitExpectedEvent=function(){this.waitExpectedTimeoutId&&(clearTimeout(this.waitExpectedTimeoutId),this.waitExpectedTimeoutId=void 0)},e}();function iy(e){var t={id:e.ID,dataType:e.DataType,queueType:e.QueueType,docId:e.DocID,doc:e.Doc&&"{}"!==e.Doc?JSON.parse(e.Doc):void 0};return"update"===e.DataType&&(e.UpdatedFields&&(t.updatedFields=JSON.parse(e.UpdatedFields)),(e.removedFields||e.RemovedFields)&&(t.removedFields=JSON.parse(e.removedFields))),t}var iv={1e3:{code:1e3,name:"Normal Closure",description:"Normal closure; the connection successfully completed whatever purpose for which it was created."},1001:{code:1001,name:"Going Away",description:"The endpoint is going away, either because of a server failure or because the browser is navigating away from the page that opened the connection."},1002:{code:1002,name:"Protocol Error",description:"The endpoint is terminating the connection due to a protocol error."},1003:{code:1003,name:"Unsupported Data",description:"The connection is being terminated because the endpoint received data of a type it cannot accept (for example, a text-only endpoint received binary data)."},1005:{code:1005,name:"No Status Received",description:"Indicates that no status code was provided even though one was expected."},1006:{code:1006,name:"Abnormal Closure",description:"Used to indicate that a connection was closed abnormally (that is, with no close frame being sent) when a status code is expected."},1007:{code:1007,name:"Invalid frame payload data",description:"The endpoint is terminating the connection because a message was received that contained inconsistent data (e.g., non-UTF-8 data within a text message)."},1008:{code:1008,name:"Policy Violation",description:"The endpoint is terminating the connection because it received a message that violates its policy. This is a generic status code, used when codes 1003 and 1009 are not suitable."},1009:{code:1009,name:"Message too big",description:"The endpoint is terminating the connection because a data frame was received that is too large."},1010:{code:1010,name:"Missing Extension",description:"The client is terminating the connection because it expected the server to negotiate one or more extension, but the server didn't."},1011:{code:1011,name:"Internal Error",description:"The server is terminating the connection because it encountered an unexpected condition that prevented it from fulfilling the request."},1012:{code:1012,name:"Service Restart",description:"The server is terminating the connection because it is restarting."},1013:{code:1013,name:"Try Again Later",description:"The server is terminating the connection due to a temporary condition, e.g. it is overloaded and is casting off some of its clients."},1014:{code:1014,name:"Bad Gateway",description:"The server was acting as a gateway or proxy and received an invalid response from the upstream server. This is similar to 502 HTTP Status Code."},1015:{code:1015,name:"TLS Handshake",description:"Indicates that the connection was closed due to a failure to perform a TLS handshake (e.g., the server certificate can't be verified)."},3e3:{code:3e3,name:"Reconnect WebSocket",description:"The client is terminating the connection because it wants to reconnect"},3001:{code:3001,name:"No Realtime Listeners",description:"The client is terminating the connection because no more realtime listeners exist"},3002:{code:3002,name:"Heartbeat Ping Error",description:"The client is terminating the connection due to its failure in sending heartbeat messages"},3003:{code:3003,name:"Heartbeat Pong Timeout Error",description:"The client is terminating the connection because no heartbeat response is received from the server"},3050:{code:3050,name:"Server Close",description:"The client is terminating the connection because no heartbeat response is received from the server"}};!function(e){e[e.NormalClosure=1e3]="NormalClosure",e[e.GoingAway=1001]="GoingAway",e[e.ProtocolError=1002]="ProtocolError",e[e.UnsupportedData=1003]="UnsupportedData",e[e.NoStatusReceived=1005]="NoStatusReceived",e[e.AbnormalClosure=1006]="AbnormalClosure",e[e.InvalidFramePayloadData=1007]="InvalidFramePayloadData",e[e.PolicyViolation=1008]="PolicyViolation",e[e.MessageTooBig=1009]="MessageTooBig",e[e.MissingExtension=1010]="MissingExtension",e[e.InternalError=1011]="InternalError",e[e.ServiceRestart=1012]="ServiceRestart",e[e.TryAgainLater=1013]="TryAgainLater",e[e.BadGateway=1014]="BadGateway",e[e.TLSHandshake=1015]="TLSHandshake",e[e.ReconnectWebSocket=3e3]="ReconnectWebSocket",e[e.NoRealtimeListeners=3001]="NoRealtimeListeners",e[e.HeartbeatPingError=3002]="HeartbeatPingError",e[e.HeartbeatPongTimeoutError=3003]="HeartbeatPongTimeoutError",e[e.NoAuthentication=3050]="NoAuthentication"}(y||(y={}));var im=function(e,t){var r=iv[e],n=r?"".concat(r.name,", code ").concat(e,", reason ").concat(t||r.description):"code ".concat(e);return new ia({errCode:iu.SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_CLOSED,errMsg:n})},ig=null,ib="web",i_=function(){return(i_=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},iw=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},iS=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},iE={OPEN:1},iI=function(){function e(e){var t=this;this.virtualWSClient=new Set,this.queryIdClientMap=new Map,this.watchIdClientMap=new Map,this.pingFailed=0,this.pongMissed=0,this.logins=new Map,this.wsReadySubsribers=[],this.wsResponseWait=new Map,this.rttObserved=[],this.send=function(e){return iw(t,void 0,void 0,function(){var t=this;return iS(this,function(r){return[2,new Promise(function(r,n){iw(t,void 0,void 0,function(){var t,o,i,s,a,u,c,l,f=this;return iS(this,function(h){switch(h.label){case 0:o=!1,i=!1,s=function(e){o=!0,t&&clearTimeout(t),r(e)},a=function(e){i=!0,t&&clearTimeout(t),n(e)},e.timeout&&(t=setTimeout(function(){iw(f,void 0,void 0,function(){return iS(this,function(e){switch(e.label){case 0:if(!(!o||!i))return[3,2];return[4,ic(0)];case 1:e.sent(),o&&i||a(new ii("wsclient.send timedout")),e.label=2;case 2:return[2]}})})},e.timeout)),h.label=1;case 1:if(h.trys.push([1,8,,9]),!(void 0!==this.wsInitPromise||null!==this.wsInitPromise))return[3,3];return[4,this.wsInitPromise];case 2:h.sent(),h.label=3;case 3:if(!this.ws)return a(Error("invalid state: ws connection not exists, can not send message")),[2];if(this.ws.readyState!==iE.OPEN)return a(Error("ws readyState invalid: ".concat(this.ws.readyState,", can not send message"))),[2];e.waitResponse&&(u={resolve:s,reject:a,skipOnMessage:e.skipOnMessage},this.wsResponseWait.set(e.msg.requestId,u)),h.label=4;case 4:return h.trys.push([4,6,,7]),[4,this.ws.send(JSON.stringify(e.msg))];case 5:return h.sent(),e.waitResponse||s(void 0),[3,7];case 6:return(c=h.sent())&&(a(c),e.waitResponse&&this.wsResponseWait.delete(e.msg.requestId)),[3,7];case 7:return[3,9];case 8:return l=h.sent(),a(l),[3,9];case 9:return[2]}})})})]})})},this.closeAllClients=function(e){t.virtualWSClient.forEach(function(t){t.closeWithError(e)})},this.pauseClients=function(e){(e||t.virtualWSClient).forEach(function(e){e.pause()})},this.resumeClients=function(e){(e||t.virtualWSClient).forEach(function(e){e.resume()})},this.initWebSocketConnection=function(e,r){return void 0===r&&(r=t.maxReconnect),iw(t,void 0,void 0,function(){var t=this;return iS(this,function(n){switch(n.label){case 0:if(e&&this.reconnectState)return[2];if(e&&(this.reconnectState=!0),void 0!==this.wsInitPromise&&null!==this.wsInitPromise)return[2,this.wsInitPromise];e&&this.pauseClients(),this.close(y.ReconnectWebSocket),this.wsInitPromise=new Promise(function(n,o){iw(t,void 0,void 0,function(){var t,i,s=this;return iS(this,function(a){switch(a.label){case 0:return a.trys.push([0,6,,11]),[4,this.getWsSign()];case 1:return t=a.sent(),[4,new Promise(function(e){var r=t.wsUrl||"wss://tcb-ws.tencentcloudapi.com",n=ig;s.ws=n?new n(r):new WebSocket(r),e(void 0)})];case 2:if(a.sent(),!this.ws.connect)return[3,4];return[4,this.ws.connect()];case 3:a.sent(),a.label=4;case 4:return[4,this.initWebSocketEvent()];case 5:return a.sent(),n(),e&&(this.resumeClients(),this.reconnectState=!1),[3,11];case 6:if(console.error("[realtime] initWebSocketConnection connect fail",i=a.sent()),!(r>0))return[3,9];return this.wsInitPromise=void 0,[4,ic(this.reconnectInterval)];case 7:a.sent(),e&&(this.reconnectState=!1),a.label=8;case 8:return n(this.initWebSocketConnection(e,r-1)),[3,10];case 9:o(i),e&&this.closeAllClients(new ia({errCode:iu.SDK_DATABASE_REALTIME_LISTENER_RECONNECT_WATCH_FAIL,errMsg:i})),a.label=10;case 10:return[3,11];case 11:return[2]}})})}),n.label=1;case 1:return n.trys.push([1,3,4,5]),[4,this.wsInitPromise];case 2:return n.sent(),this.wsReadySubsribers.forEach(function(e){return(0,e.resolve)()}),[3,5];case 3:return n.sent(),this.wsReadySubsribers.forEach(function(e){return(0,e.reject)()}),[3,5];case 4:return this.wsInitPromise=void 0,this.wsReadySubsribers=[],[7];case 5:return[2]}})})},this.initWebSocketEvent=function(){return new Promise(function(e,r){if(!t.ws)throw Error("can not initWebSocketEvent, ws not exists");var n=!1;t.ws.onopen=function(t){console.warn("[realtime] ws event: open",t),n=!0,e()},t.ws.onerror=function(e){t.logins=new Map,n?(console.error("[realtime] ws event: error",e),t.clearHeartbeat(),t.virtualWSClient.forEach(function(t){return t.closeWithError(new ia({errCode:iu.SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_ERROR,errMsg:e}))})):(console.error("[realtime] ws open failed with ws event: error",e),r(e))},t.ws.onclose=function(e){switch(console.warn("[realtime] ws event: close",e),t.logins=new Map,t.clearHeartbeat(),e.code){case y.ReconnectWebSocket:case y.NoRealtimeListeners:break;case y.HeartbeatPingError:case y.HeartbeatPongTimeoutError:case y.NormalClosure:case y.AbnormalClosure:t.maxReconnect>0?t.initWebSocketConnection(!0,t.maxReconnect):t.closeAllClients(im(e.code));break;case y.NoAuthentication:t.closeAllClients(im(e.code,e.reason));break;default:t.maxReconnect>0?t.initWebSocketConnection(!0,t.maxReconnect):t.closeAllClients(im(e.code))}},t.ws.onmessage=function(e){var r,n=e.data;t.heartbeat();try{r=JSON.parse(n)}catch(e){throw Error("[realtime] onMessage parse res.data error: ".concat(e))}if("ERROR"===r.msgType){var o=null;t.virtualWSClient.forEach(function(e){e.watchId===r.watchId&&(o=e)}),o&&o.listener.onError(r)}var i=t.wsResponseWait.get(r.requestId);if(i){try{"ERROR"===r.msgType?i.reject(new ir(r)):i.resolve(r)}catch(e){console.error("ws onMessage responseWaitSpec.resolve(msg) errored:",e)}finally{t.wsResponseWait.delete(r.requestId)}if(i.skipOnMessage)return}if("PONG"===r.msgType){if(t.lastPingSendTS){var s=Date.now()-t.lastPingSendTS;if(s>1e4){console.warn("[realtime] untrusted rtt observed: ".concat(s));return}t.rttObserved.length>=3&&t.rttObserved.splice(0,t.rttObserved.length-3+1),t.rttObserved.push(s)}return}var a=r.watchId&&t.watchIdClientMap.get(r.watchId);if(a)a.onMessage(r);else switch(console.error("[realtime] no realtime listener found responsible for watchId ".concat(r.watchId,": "),r),r.msgType){case"INIT_EVENT":case"NEXT_EVENT":case"CHECK_EVENT":(a=t.queryIdClientMap.get(r.msgData.queryID))&&a.onMessage(r);break;default:for(var u=0,c=Array.from(t.watchIdClientMap.entries());u<c.length;u++){c[u][1].onMessage(r);break}}},t.heartbeat()})},this.isWSConnected=function(){return!!(t.ws&&t.ws.readyState===iE.OPEN)},this.onceWSConnected=function(){return iw(t,void 0,void 0,function(){var e=this;return iS(this,function(t){return this.isWSConnected()?[2]:null!==this.wsInitPromise&&void 0!==this.wsInitPromise?[2,this.wsInitPromise]:[2,new Promise(function(t,r){e.wsReadySubsribers.push({resolve:t,reject:r})})]})})},this.webLogin=function(e,r){return iw(t,void 0,void 0,function(){var t,n,o,i,s,a,u,c,l=this;return iS(this,function(f){switch(f.label){case 0:if(!r){if(e){if(t=this.logins.get(e)){if(t.loggedIn&&t.loginResult)return[2,t.loginResult];if(null!==t.loggingInPromise&&void 0!==t.loggingInPromise)return[2,t.loggingInPromise]}}else if((null==(n=this.logins.get(""))?void 0:n.loggingInPromise)!==null&&(null==n?void 0:n.loggingInPromise)!==void 0)return[2,n.loggingInPromise]}o=new Promise(function(e,t){iw(l,void 0,void 0,function(){var r,n,o,i;return iS(this,function(s){switch(s.label){case 0:return s.trys.push([0,3,,4]),[4,this.getWsSign()];case 1:return n={envId:(r=s.sent()).envId||"",accessToken:"",referrer:"web",sdkVersion:"",dataVersion:""},o={watchId:void 0,requestId:o7(),msgType:"LOGIN",msgData:n,exMsgData:{runtime:ib,signStr:r.signStr,secretVersion:r.secretVersion}},[4,this.send({msg:o,waitResponse:!0,skipOnMessage:!0,timeout:5e3})];case 2:return(i=s.sent()).msgData.code?t(Error("".concat(i.msgData.code," ").concat(i.msgData.message))):e({envId:r.envId}),[3,4];case 3:return t(s.sent()),[3,4];case 4:return[2]}})})}),i=e&&this.logins.get(e),s=Date.now(),i?(i.loggedIn=!1,i.loggingInPromise=o,i.loginStartTS=s):(i={loggedIn:!1,loggingInPromise:o,loginStartTS:s},this.logins.set(e||"",i)),f.label=1;case 1:return f.trys.push([1,3,,4]),[4,o];case 2:if(a=f.sent(),(u=e&&this.logins.get(e))&&u===i&&u.loginStartTS===s)return i.loggedIn=!0,i.loggingInPromise=void 0,i.loginStartTS=void 0,i.loginResult=a,[2,a];if(u){if(u.loggedIn&&u.loginResult)return[2,u.loginResult];if(null!==u.loggingInPromise&&void 0!==u.loggingInPromise)return[2,u.loggingInPromise];throw Error("ws unexpected login info")}throw Error("ws login info reset");case 3:throw c=f.sent(),i.loggedIn=!1,i.loggingInPromise=void 0,i.loginStartTS=void 0,i.loginResult=void 0,c;case 4:return[2]}})})},this.getWsSign=function(){return iw(t,void 0,void 0,function(){var e,t,r,n;return iS(this,function(o){switch(o.label){case 0:if(this.wsSign&&this.wsSign.expiredTs>Date.now())return[2,this.wsSign];return e=Date.now()+6e4,[4,this.context.appConfig.request.send("auth.wsWebSign",{runtime:ib})];case 1:if((t=o.sent()).code)throw Error("[tcb-js-sdk] 获取实时数据推送登录票据失败: ".concat(t.code));if(t.data)return n=(r=t.data).signStr,[2,{signStr:n,wsUrl:r.wsUrl,secretVersion:r.secretVersion,envId:r.envId,expiredTs:e}];throw Error("[tcb-js-sdk] 获取实时数据推送登录票据失败")}})})},this.getWaitExpectedTimeoutLength=function(){return t.rttObserved.length?t.rttObserved.reduce(function(e,t){return e+t})/t.rttObserved.length*1.5:5e3},this.ping=function(){return iw(t,void 0,void 0,function(){var e;return iS(this,function(t){switch(t.label){case 0:return e={watchId:void 0,requestId:o7(),msgType:"PING",msgData:null},[4,this.send({msg:e})];case 1:return t.sent(),[2]}})})},this.onWatchStart=function(e,r){t.queryIdClientMap.set(r,e)},this.onWatchClose=function(e,r){r&&t.queryIdClientMap.delete(r),t.watchIdClientMap.delete(e.watchId),t.virtualWSClient.delete(e),t.virtualWSClient.size||t.close(y.NoRealtimeListeners)},this.maxReconnect=e.maxReconnect||5,this.reconnectInterval=e.reconnectInterval||1e4,this.context=e.context}return e.prototype.clearHeartbeat=function(){this.pingTimeoutId&&clearTimeout(this.pingTimeoutId),this.pongTimeoutId&&clearTimeout(this.pongTimeoutId)},e.prototype.close=function(e){this.clearHeartbeat(),this.ws&&(this.ws.close(e,iv[e].name),this.ws=void 0)},e.prototype.watch=function(e){this.ws||void 0!==this.wsInitPromise&&null!==this.wsInitPromise||this.initWebSocketConnection(!1);var t=new ip(i_(i_({},e),{send:this.send,login:this.webLogin,isWSConnected:this.isWSConnected,onceWSConnected:this.onceWSConnected,getWaitExpectedTimeoutLength:this.getWaitExpectedTimeoutLength,onWatchStart:this.onWatchStart,onWatchClose:this.onWatchClose,debug:!0}));return this.virtualWSClient.add(t),this.watchIdClientMap.set(t.watchId,t),t.listener},e.prototype.heartbeat=function(e){var t=this;this.clearHeartbeat(),this.pingTimeoutId=setTimeout(function(){iw(t,void 0,void 0,function(){var e=this;return iS(this,function(t){switch(t.label){case 0:if(t.trys.push([0,2,,3]),!this.ws||this.ws.readyState!==iE.OPEN)return[2];return this.lastPingSendTS=Date.now(),[4,this.ping()];case 1:return t.sent(),this.pingFailed=0,this.pongTimeoutId=setTimeout(function(){console.error("pong timed out"),e.pongMissed<2?(e.pongMissed+=1,e.heartbeat(!0)):e.initWebSocketConnection(!0)},this.context.appConfig.realtimePongWaitTimeout),[3,3];case 2:return t.sent(),this.pingFailed<2?(this.pingFailed+=1,this.heartbeat()):this.close(y.HeartbeatPingError),[3,3];case 3:return[2]}})})},e?0:this.context.appConfig.realtimePingInterval)},e}(),iO={target:"database",entity:function(){var e=this.platform,t=e.adapter,r=e.runtime;ig=t.wsClass,ib=r}},iT={name:"realtime",IIFE:!0,entity:function(){this.prototype.wsClientClass=iI}};try{cloudbase.registerComponent(iT),cloudbase.registerHook(iO)}catch(e){}var iR=function(e,t,r,n){var o,i=arguments.length,s=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,r,n);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(i<3?o(s):i>3?o(t,r,s):o(t,r))||s);return i>3&&s&&Object.defineProperty(t,r,s),s},iA=function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},iP=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},ix="analytics",iN=["mall"],iC=new(function(){function e(){}return e.prototype.analytics=function(e){var t,r,n,o;return t=this,r=void 0,n=void 0,o=function(){var t,r,n;return iP(this,function(o){if(!function(e){if("Object"!==Object.prototype.toString.call(e).slice(8,-1))return!1;var t=e.report_data,r=e.report_type;return!!(!1!==iN.includes(r)&&"Object"===Object.prototype.toString.call(t).slice(8,-1)&&(void 0===t.action_time||Number.isInteger(t.action_time)))&&"string"==typeof t.action_type}(e))throw Error(JSON.stringify({code:x.INVALID_PARAMS,msg:"[".concat(ix,".analytics] invalid report data")}));return t="analytics.report",r=void 0===e.report_data.action_time?Math.floor(Date.now()/1e3):e.report_data.action_time,n={requestData:{analytics_scene:e.report_type,analytics_data:Object.assign({},e.report_data,{action_time:r})}},this.request.send(t,n),[2]})},new(n||(n=Promise))(function(e,i){function s(e){try{u(o.next(e))}catch(e){i(e)}}function a(e){try{u(o.throw(e))}catch(e){i(e)}}function u(t){var r;t.done?e(t.value):((r=t.value)instanceof n?r:new n(function(e){e(r)})).then(s,a)}u((o=o.apply(t,r||[])).next())})},iR([ee({customInfo:{className:"Cloudbase",methodName:"analytics"},title:"上报调用失败",messages:["请确认以下各项：","  1 - 调用 analytics() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(P)]}),iA("design:type",Function),iA("design:paramtypes",[Object]),iA("design:returntype",Promise)],e.prototype,"analytics",null),e}()),iL={name:ix,entity:{analytics:iC.analytics}};try{cloudbase.registerComponent(iL)}catch(e){}var ij=function(e,t){return(ij=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},iU=function(){return(iU=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function iD(e,t,r,n){return new(r||(r=Promise))(function(t,o){function i(e){try{a(n.next(e))}catch(e){o(e)}}function s(e){try{a(n.throw(e))}catch(e){o(e)}}function a(e){var n;e.done?t(e.value):((n=e.value)instanceof r?n:new r(function(e){e(n)})).then(i,s)}a((n=n.apply(e,[])).next())})}function ik(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(r)throw TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,n=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}}var iq=function(e){function t(t,r){var n=e.call(this,t)||this;return n.name="WxCloudSDKError",n.code=null==r?void 0:r.code,n.requestId=null==r?void 0:r.requestId,n.originError=null==r?void 0:r.originError,n}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}ij(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}(t,e),t}(Error);function iM(){return"undefined"!=typeof window&&window||"undefined"!=typeof globalThis&&globalThis}var iB=function(e){var t=e.dataSourceName,r=e.methodName,n=e.params,o=e.realMethodName,i=e.callFunction,s=e.envType,a=void 0===s?"prod":s,u=e.mode;return iD(void 0,void 0,void 0,function(){var e,s,c,l,f,h;return ik(this,function(d){switch(d.label){case 0:e={data:{},requestId:""},d.label=1;case 1:var p,y;return d.trys.push([1,3,,4]),[4,i({name:"lowcode-datasource",data:{dataSourceName:t,methodName:r,params:n,userAgent:(null==(y=iM())?void 0:y.navigator)?y.navigator.userAgent:"undefined"!=typeof wx&&wx.getSystemInfo?(wx.getSystemInfo({success:function(e){e&&(p=["brand","model","version","system","platform","SDKVersion","language"].map(function(t){return"".concat(t,": ").concat(e[t])}).join(", "))}}),p):void 0,referrer:function(){try{var e=iM();if(!e)return;if("undefined"==typeof wx)return iM().location.href;return e.__wxRoute}catch(e){}}(),"x-sdk-version":"1.6.1",envType:a,mode:u}})];case 2:if(c=(null===(f=null==(s=d.sent())?void 0:s.result)||void 0===f?void 0:f.requestId)||(null==s?void 0:s.requestId)||(null==s?void 0:s.requestID),null==s?void 0:s.result.code)throw new iq("【错误】".concat(null==s?void 0:s.result.message,"\n【操作】调用 models.").concat(t?"".concat(t,"."):"").concat(o,"\n【错误码】").concat(null==s?void 0:s.result.code,"\n【请求ID】").concat(c||"N/A"),{code:null==s?void 0:s.result.code,requestId:c});return e.data=(null===(h=null==s?void 0:s.result)||void 0===h?void 0:h.data)||{},e.requestId=c,[3,4];case 3:if("WxCloudSDKError"===(l=d.sent()).name)throw l;throw console.log(l),new iq("【错误】".concat(l.message,"\n      【操作】调用 models.").concat(t?"".concat(t,"."):"").concat(o,"\n      【请求ID】N/A"),{code:"UnknownError",originError:l});case 4:return[2,e]}})})},iW=function(e){var t=e.sql,r=e.params,n=e.config,o=e.callFunction,i=e.unsafe,s=void 0!==i&&i;return iD(void 0,void 0,void 0,function(){return ik(this,function(e){return[2,iB({realMethodName:"$runSQL",methodName:"callWedaApi",params:{action:"RunMysqlCommand",data:{sqlTemplate:t,config:n,parameter:s?"":Object.entries(r||{}).reduce(function(e,t){var r=t[0],n=t[1];if(void 0!==n){var o="OBJECT";switch(typeof n){case"boolean":o="BOOLEAN";break;case"number":o="NUMBER";break;case"string":o="STRING";break;default:o=Array.isArray(n)?"ARRAY":"OBJECT"}e.push({key:r,type:o,value:"STRING"===o?n:JSON.stringify(n)})}return e},[])||[]}},callFunction:o,mode:"sdk"})]})})},iF={filter:{where:{}},select:{$master:!0}};function i$(e){return{getUrl:function(t){return"".concat(t,"/").concat(e)},method:"post"}}var iV={get:iU(iU({},i$("get")),{defaultParams:iU({},iF)}),list:iU(iU({},i$("list")),{defaultParams:iU({},iF)}),create:i$("create"),createMany:i$("createMany"),update:iU(iU({},i$("update")),{method:"put"}),updateMany:iU(iU({},i$("updateMany")),{method:"put"}),upsert:i$("upsert"),delete:i$("delete"),deleteMany:i$("deleteMany")},iG="Unknown error occurred",iH="NotSupported",iK=function(e,t,r,n){var o,i=(o=e,{$runSQL:function(e,t,r){return iD(this,void 0,void 0,function(){return ik(this,function(n){switch(n.label){case 0:return[4,iW({sql:e,params:t,config:iU(iU({},r),{preparedStatements:!0}),callFunction:o})];case 1:return[2,n.sent()]}})})},$runSQLRaw:function(e,t){return iD(this,void 0,void 0,function(){return ik(this,function(r){switch(r.label){case 0:return[4,iW({sql:e,params:[],config:iU(iU({},t),{preparedStatements:!1}),callFunction:o})];case 1:return[2,r.sent()]}})})}});return new Proxy({},{get:function(e,o){if("string"==typeof o)return Object.prototype.hasOwnProperty.call(i,o)?i[o]:iz(r,o,t,n)}})},iJ=function(e,t,r,n,o){return new iq("【错误】".concat(e,"\n【操作】调用 models.").concat(t,".").concat(r,"\n【错误码】").concat(n,"\n【请求ID】").concat(o),{code:n,requestId:o})},iz=function(e,t,r,n){return new Proxy({},{get:function(o,i){if("runSQLTemplate"!==i){var s=iV[i];if(!s){var a=Error("不支持的操作: ".concat(i));throw new iq(a.message||iG,{originError:a,code:iH,requestId:"N/A"})}return function(n){return void 0===n&&(n={}),iD(void 0,void 0,void 0,function(){var o,a,u,c,l,f,h,d;return ik(this,function(p){switch(p.label){case 0:o=s.getUrl,a=s.method,l=[e,"pre"===(c=Object.assign({},void 0===(u=s.defaultParams)?{}:u,n)).envType?"pre":"prod",o(t)].join("/"),p.label=1;case 1:return p.trys.push([1,3,,4]),[4,r({url:l,body:JSON.stringify(c),method:a})];case 2:if((f=p.sent()).code)throw iJ(null==f?void 0:f.message,t,i,null==f?void 0:f.code,null==f?void 0:f.requestId);return"get"===i&&Object.assign(f,{data:null!==(d=f.data.record)&&void 0!==d?d:f.data}),[2,f];case 3:throw new iq((null==(h=p.sent())?void 0:h.message)||iG,{originError:h});case 4:return[2]}})})}}if(!(null==n?void 0:n.sqlBaseUrl)){var a=Error("不支持的操作: ".concat(i));throw new iq(a.message||iG,{originError:a,code:iH,requestId:"N/A"})}return function(e){return iD(void 0,void 0,void 0,function(){var o,s,a,u,c,l,f,h,d,p,y,v,m,g,b;return ik(this,function(_){switch(_.label){case 0:o=e.params,s=e.templateName,a="pre"===e.envType?"pre":"prod",u=[n.sqlBaseUrl,a,s,"run"].join("/"),c=Object.entries(o||{}).reduce(function(e,t){var r=t[0],n=t[1];if(void 0!==n){var o="OBJECT";switch(typeof n){case"boolean":o="BOOLEAN";break;case"number":o="NUMBER";break;case"string":o="STRING";break;default:o=Array.isArray(n)?"ARRAY":"OBJECT"}e.push({key:r,type:o,value:"STRING"===o?n:JSON.stringify(n)})}return e},[]),_.label=1;case 1:return _.trys.push([1,3,,4]),[4,r({url:u,body:JSON.stringify({parameter:c}),method:"POST"})];case 2:if(null===(h=null==(l=_.sent())?void 0:l.Response)||void 0===h?void 0:h.Error)throw iJ(null===(p=null===(d=null==l?void 0:l.Response)||void 0===d?void 0:d.Error)||void 0===p?void 0:p.Message,t,i,null===(v=null===(y=null==l?void 0:l.Response)||void 0===y?void 0:y.Error)||void 0===v?void 0:v.Code,null===(m=null==l?void 0:l.Response)||void 0===m?void 0:m.RequestId);return[2,iU(iU({},null!==(g=null==l?void 0:l.Response)&&void 0!==g?g:{}),{data:null===(b=null==l?void 0:l.Response)||void 0===b?void 0:b.Data})];case 3:throw new iq((null==(f=_.sent())?void 0:f.message)||iG,{originError:f});case 4:return[2]}})})}}})},iY=function(){return(iY=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},iQ=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},iZ="models",iX=new WeakMap,i0="cloudbase_init",i1=new Z;i1.on(i0,function(e){var t=e.data;Object.assign(t,{models:new Proxy({},{get:function(e,r){var n=iX.get(t);return n||(n=function(e){var t=this,r=e.callFunction.bind(e),n=e.request.fetch.bind(e.request),o=e.getEndPointWithKey("GATEWAY"),i=o.BASE_URL,s=o.PROTOCOL;return iK(r,function(e){var r,o,i;return r=void 0,o=void 0,i=function(){return iQ(this,function(t){switch(t.label){case 0:return[4,n(iY(iY({},e),{headers:iY({"Content-Type":"application/json"},e.headers)}))];case 1:return[4,t.sent().data];case 2:return[2,t.sent()]}})},new(o||(o=Promise))(function(e,n){function s(e){try{u(i.next(e))}catch(e){n(e)}}function a(e){try{u(i.throw(e))}catch(e){n(e)}}function u(t){var r;t.done?e(t.value):((r=t.value)instanceof o?r:new o(function(e){e(r)})).then(s,a)}u((i=i.apply(t,r||[])).next())})},"".concat(s).concat(i,"/model"),{sqlBaseUrl:"".concat(s).concat(i,"/sql")})}(t),iX.set(t,n)),n[r]}})})});var i2={name:iZ,namespace:iZ,entity:new Proxy({},{get:function(e,t){console.warn("【deprecated】Accessing Cloudbase.prototype.models.".concat(t,"."))}}),injectEvents:{bus:i1,events:[i0]}};try{cloudbase.registerComponent(i2)}catch(e){}var i3=function(e,t){return(i3=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function i4(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}i3(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}function i5(e){if(!e)throw TypeError("Assertion failed")}function i6(){}function i8(e){var t;return!!("object"==typeof(t=e)&&null!==t||"function"==typeof t)&&"function"==typeof e.getReader}function i7(e){try{return e.getReader({mode:"byob"}).releaseLock(),!0}catch(e){return!1}}function i9(e,t){var r=(void 0===t?{}:t).type;return i5(i8(e)),i5(!1===e.locked),"bytes"===(r=se(r))?new so(e):new sr(e)}function se(e){var t=String(e);if("bytes"===t)return t;if(void 0===e)return e;throw RangeError("Invalid type is specified")}var st=function(){function e(e){this._underlyingReader=void 0,this._readerMode=void 0,this._readableStreamController=void 0,this._pendingRead=void 0,this._underlyingStream=e,this._attachDefaultReader()}return e.prototype.start=function(e){this._readableStreamController=e},e.prototype.cancel=function(e){return i5(void 0!==this._underlyingReader),this._underlyingReader.cancel(e)},e.prototype._attachDefaultReader=function(){if("default"!==this._readerMode){this._detachReader();var e=this._underlyingStream.getReader();this._readerMode="default",this._attachReader(e)}},e.prototype._attachReader=function(e){var t=this;i5(void 0===this._underlyingReader),this._underlyingReader=e;var r=this._underlyingReader.closed;r&&r.then(function(){return t._finishPendingRead()}).then(function(){e===t._underlyingReader&&t._readableStreamController.close()},function(r){e===t._underlyingReader&&t._readableStreamController.error(r)}).catch(i6)},e.prototype._detachReader=function(){void 0!==this._underlyingReader&&(this._underlyingReader.releaseLock(),this._underlyingReader=void 0,this._readerMode=void 0)},e.prototype._pullWithDefaultReader=function(){var e=this;this._attachDefaultReader();var t=this._underlyingReader.read().then(function(t){var r=e._readableStreamController;t.done?e._tryClose():r.enqueue(t.value)});return this._setPendingRead(t),t},e.prototype._tryClose=function(){try{this._readableStreamController.close()}catch(e){}},e.prototype._setPendingRead=function(e){var t,r=this,n=function(){r._pendingRead===t&&(r._pendingRead=void 0)};this._pendingRead=t=e.then(n,n)},e.prototype._finishPendingRead=function(){var e=this;if(this._pendingRead){var t=function(){return e._finishPendingRead()};return this._pendingRead.then(t,t)}},e}(),sr=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i4(t,e),t.prototype.pull=function(){return this._pullWithDefaultReader()},t}(st);function sn(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}var so=function(e){function t(t){var r=this,n=i7(t);return(r=e.call(this,t)||this)._supportsByob=n,r}return i4(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return"bytes"},enumerable:!1,configurable:!0}),t.prototype._attachByobReader=function(){if("byob"!==this._readerMode){i5(this._supportsByob),this._detachReader();var e=this._underlyingStream.getReader({mode:"byob"});this._readerMode="byob",this._attachReader(e)}},t.prototype.pull=function(){if(this._supportsByob){var e=this._readableStreamController.byobRequest;if(e)return this._pullWithByobRequest(e)}return this._pullWithDefaultReader()},t.prototype._pullWithByobRequest=function(e){var t=this;this._attachByobReader();var r=new Uint8Array(e.view.byteLength),n=this._underlyingReader.read(r).then(function(r){t._readableStreamController,r.done?(t._tryClose(),e.respond(0)):(function(e,t){var r=sn(e);sn(t).set(r,0)}(r.value,e.view),e.respond(r.value.byteLength))});return this._setPendingRead(n),n},t}(st),si=(function(){function e(e){var t=this;this._writableStreamController=void 0,this._pendingWrite=void 0,this._state="writable",this._storedError=void 0,this._underlyingWriter=e,this._errorPromise=new Promise(function(e,r){t._errorPromiseReject=r}),this._errorPromise.catch(i6)}e.prototype.start=function(e){var t=this;this._writableStreamController=e,this._underlyingWriter.closed.then(function(){t._state="closed"}).catch(function(e){return t._finishErroring(e)})},e.prototype.write=function(e){var t=this,r=this._underlyingWriter;if(null===r.desiredSize)return r.ready;var n=r.write(e);n.catch(function(e){return t._finishErroring(e)}),r.ready.catch(function(e){return t._startErroring(e)});var o=Promise.race([n,this._errorPromise]);return this._setPendingWrite(o),o},e.prototype.close=function(){var e=this;return void 0===this._pendingWrite?this._underlyingWriter.close():this._finishPendingWrite().then(function(){return e.close()})},e.prototype.abort=function(e){if("errored"!==this._state)return this._underlyingWriter.abort(e)},e.prototype._setPendingWrite=function(e){var t,r=this,n=function(){r._pendingWrite===t&&(r._pendingWrite=void 0)};this._pendingWrite=t=e.then(n,n)},e.prototype._finishPendingWrite=function(){var e=this;if(void 0===this._pendingWrite)return Promise.resolve();var t=function(){return e._finishPendingWrite()};return this._pendingWrite.then(t,t)},e.prototype._startErroring=function(e){var t=this;if("writable"===this._state){this._state="erroring",this._storedError=e;var r=function(){return t._finishErroring(e)};void 0===this._pendingWrite?r():this._finishPendingWrite().then(r,r),this._writableStreamController.error(e)}},e.prototype._finishErroring=function(e){"writable"===this._state&&this._startErroring(e),"erroring"===this._state&&(this._state="errored",this._errorPromiseReject(this._storedError))}}(),function(){function e(e,t){var r=this;this._transformStreamController=void 0,this._onRead=function(e){if(!e.done)return r._transformStreamController.enqueue(e.value),r._reader.read().then(r._onRead)},this._onError=function(e){r._flushReject(e),r._transformStreamController.error(e),r._reader.cancel(e).catch(i6),r._writer.abort(e).catch(i6)},this._onTerminate=function(){r._flushResolve(),r._transformStreamController.terminate();var e=TypeError("TransformStream terminated");r._writer.abort(e).catch(i6)},this._reader=e,this._writer=t,this._flushPromise=new Promise(function(e,t){r._flushResolve=e,r._flushReject=t})}e.prototype.start=function(e){this._transformStreamController=e,this._reader.read().then(this._onRead).then(this._onTerminate,this._onError);var t=this._reader.closed;t&&t.then(this._onTerminate,this._onError)},e.prototype.transform=function(e){return this._writer.write(e)},e.prototype.flush=function(){var e=this;return this._writer.close().then(function(){return e._flushPromise})}}(),[239,187,191]),ss=r(35815),sa=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},su=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},sc=function(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e="function"==typeof __values?__values(e):e[Symbol.iterator](),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise(function(n,o){(function(e,t,r,n){Promise.resolve(n).then(function(t){e({value:t,done:r})},t)})(n,o,(t=e[r](t)).done,t.value)})}}},sl=function(){var e;return new r5({start:function(t){e=function(e){var t,r,n,o,i,s,a;return u(),{feed:function(u){var c;r=r?r+u:u,t&&(c=r,si.every(function(e,t){return c.charCodeAt(t)===e}))&&(r=r.slice(si.length)),t=!1;for(var l=r.length,f=0,h=!1;f<l;){h&&("\n"===r[f]&&(f+=1),h=!1);for(var d=-1,p=o,y=void 0,v=n;d<0&&v<l;v++)":"===(y=r[v])&&p<0?p=v-f:"\r"===y?(h=!0,d=v-f):"\n"===y&&(d=v-f);if(d<0){n=l-f,o=p;break}n=0,o=-1,function(t,r,n,o){if(0===o){a.length>0&&(e({type:"event",id:i,event:s||void 0,data:a.slice(0,-1)}),a="",i=void 0),s=void 0;return}var u=n<0,c=t.slice(r,r+(u?o:n)),l=0;l=u?o:" "===t[r+n+1]?n+2:n+1;var f=r+l,h=o-l,d=t.slice(f,f+h).toString();if("data"===c)a+=d?"".concat(d,"\n"):"\n";else if("event"===c)s=d;else if("id"!==c||d.includes("\0")){if("retry"===c){var p=parseInt(d,10);Number.isNaN(p)||e({type:"reconnect-interval",value:p})}}else i=d}(r,f,p,d),f+=d+1}f===l?r="":f>0&&(r=r.slice(f))},reset:u};function u(){t=!0,r="",n=0,o=-1,i=void 0,s=void 0,a=""}}(function(e){"event"===e.type&&t.enqueue(e)})},transform:function(t){e.feed(t)}})},sf=function(e){i5(!!(function(e){if("function"!=typeof e)return!1;var t=!1;try{new e({start:function(){t=!0}})}catch(e){}return t}(e)&&i8(new e)));var t=function(e){try{return new e({type:"bytes"}),!0}catch(e){return!1}}(e);return function(r,n){var o=(void 0===n?{}:n).type;if("bytes"!==(o=se(o))||t||(o=void 0),r.constructor===e&&("bytes"!==o||i7(r)))return r;if("bytes"===o){var i=i9(r,{type:o});return new e(i)}var i=i9(r);return new e(i)}}(rB),sh=function(){function e(e,t){void 0===e&&(e="utf-8"),void 0===t&&(t={});var r=this;this.transform=new r5({transform:function(e,t){var n=r.handle.decode(new Uint8Array(e),{stream:!0});n&&t.enqueue(n)},flush:function(e){var t=r.handle.decode();t&&e.enqueue(t),e.terminate()}}),this.handle=new ss.TextDecoder(e,t)}return Object.defineProperty(e.prototype,"encoding",{get:function(){return this.handle.encoding},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fatal",{get:function(){return this.handle.fatal},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"ignoreBOM",{get:function(){return this.handle.ignoreBOM},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"readable",{get:function(){return this.transform.readable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"writable",{get:function(){return this.transform.writable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,Symbol.toStringTag,{get:function(){return"TextDecoderStream"},enumerable:!1,configurable:!0}),e}();function sd(e){return e[Symbol.asyncIterator]=function(){var t=e.getReader();return{next:function(){return sa(this,void 0,void 0,function(){var e,r,n;return su(this,function(o){switch(o.label){case 0:return[4,t.read()];case 1:return r=(e=o.sent()).done,n=e.value,[2,r?{done:!0,value:void 0}:{done:!1,value:n}]}})})}}},e}function sp(e){return sd(e.pipeThrough(new sh).pipeThrough(sl()).pipeThrough(new r5({transform:function(e,t){try{var r=JSON.parse(e.data);t.enqueue(r)}catch(r){"[DONE]"!==e.data?console.warn("Error when transforming event source data to json",r,e):t.terminate()}}})))}function sy(){var e,t;return{promise:new Promise(function(r,n){e=r,t=n}),res:e,rej:t}}function sv(e){var t;return"assistant"===e.role&&"tool_calls"in e&&(null===(t=e.tool_calls)||void 0===t?void 0:t[0])!=null}var sm=function(){return(sm=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},sg=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},sb=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},s_=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r},sw=function(){function e(e,t){this.baseUrl=t;var r=arguments[2];"string"==typeof r?this.req=function(t){var n=t.headers,o=s_(t,["headers"]);return e(sm(sm({},o),{headers:sm(sm({},void 0===n?{}:n),{Authorization:"Bearer ".concat(r)})}))}:this.req=e}return e.prototype.list=function(e,t){return this.req({method:"get",url:this.join("bots"),data:e,timeout:null==t?void 0:t.timeout})},e.prototype.create=function(e,t){var r=e.botInfo;return this.req({method:"post",url:this.join("bots"),data:r,timeout:null==t?void 0:t.timeout})},e.prototype.get=function(e,t){var r=e.botId;return this.req({method:"get",url:this.join("bots/".concat(r)),timeout:null==t?void 0:t.timeout})},e.prototype.update=function(e,t){var r=e.botId,n=e.botInfo;return this.req({method:"PATCH",url:this.join("bots/".concat(r)),data:n,timeout:null==t?void 0:t.timeout})},e.prototype.delete=function(e,t){var r=e.botId;return this.req({method:"delete",url:this.join("bots/".concat(r)),timeout:null==t?void 0:t.timeout})},e.prototype.getChatRecords=function(e,t){return this.req({method:"get",url:this.join("bots/".concat(e.botId,"/records")),data:e,timeout:null==t?void 0:t.timeout})},e.prototype.sendFeedback=function(e,t){var r=e.userFeedback;return this.req({method:"post",url:this.join("bots/".concat(r.botId,"/feedback")),data:r,timeout:null==t?void 0:t.timeout})},e.prototype.getFeedback=function(e,t){return this.req({method:"get",url:this.join("bots/".concat(e.botId,"/feedback")),data:e,timeout:null==t?void 0:t.timeout})},e.prototype.uploadFiles=function(e,t){return sg(this,void 0,void 0,function(){return sb(this,function(r){return[2,this.req({method:"post",url:this.join("bots/".concat(e.botId,"/files")),data:e,timeout:null==t?void 0:t.timeout})]})})},e.prototype.createConversation=function(e,t){return sg(this,void 0,void 0,function(){return sb(this,function(r){return[2,this.req({method:"post",url:this.join("conversation"),data:e,timeout:null==t?void 0:t.timeout})]})})},e.prototype.getConversation=function(e,t){var r=e.pageSize,n=void 0===r?10:r,o=e.pageNumber,i=void 0===o?1:o,s=s_(e,["pageSize","pageNumber"]);return sg(this,void 0,void 0,function(){var e,r;return sb(this,function(o){if(i<1)throw Error("pageNumber must be greater than 0");return e=n*(i-1),r=n,[2,this.req({method:"get",url:this.join("conversation"),data:sm(sm({},s),{offset:e,limit:r}),timeout:null==t?void 0:t.timeout})]})})},e.prototype.deleteConversation=function(e,t){return sg(this,void 0,void 0,function(){return sb(this,function(r){return[2,this.req({method:"delete",url:this.join("conversation/".concat(e.conversationId)),data:e,timeout:null==t?void 0:t.timeout})]})})},e.prototype.speechToText=function(e,t){return sg(this,void 0,void 0,function(){return sb(this,function(r){return[2,this.req({method:"post",url:this.join("bots/".concat(e.botId,"/speech-to-text")),data:e,timeout:null==t?void 0:t.timeout})]})})},e.prototype.textToSpeech=function(e,t){return sg(this,void 0,void 0,function(){return sb(this,function(r){return[2,this.req({method:"post",url:this.join("bots/".concat(e.botId,"/text-to-speech")),data:e,timeout:null==t?void 0:t.timeout})]})})},e.prototype.getTextToSpeechResult=function(e,t){return sg(this,void 0,void 0,function(){return sb(this,function(r){return[2,this.req({method:"get",url:this.join("bots/".concat(e.botId,"/text-to-speech")),data:e,timeout:null==t?void 0:t.timeout})]})})},e.prototype.getRecommendQuestions=function(e,t){return sg(this,void 0,void 0,function(){return sb(this,function(r){switch(r.label){case 0:return[4,this.req({method:"post",url:this.join("bots/".concat(e.botId,"/recommend-questions")),data:e,stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,new sS(r.sent())]}})})},e.prototype.generateBot=function(e,t){return sg(this,void 0,void 0,function(){return sb(this,function(r){switch(r.label){case 0:return[4,this.req({method:"post",url:this.join("generate-bot"),data:e,stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,new sS(r.sent())]}})})},e.prototype.getPreview=function(e,t){return sg(this,void 0,void 0,function(){return sb(this,function(r){switch(r.label){case 0:return[4,this.req({method:"post",url:this.join("preview"),data:e,stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,new sS(r.sent())]}})})},e.prototype.generateImage=function(e,t){return this.req({method:"post",url:this.join("generate-image"),data:e,timeout:null==t?void 0:t.timeout})},e.prototype.sendMessage=function(e,t){return sg(this,void 0,void 0,function(){return sb(this,function(r){switch(r.label){case 0:return[4,this.req({method:"post",url:this.join("bots/".concat(e.botId,"/send-message")),data:e,stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,new sS(r.sent())]}})})},e.prototype.join=function(e){return"".concat(this.baseUrl,"/").concat(e)},e}(),sS=function(){function e(e){var t=sf(e);this._eventSourceStream=t.pipeThrough(new sh).pipeThrough(sl())}return Object.defineProperty(e.prototype,"teeedStream",{get:function(){var e=this._eventSourceStream.tee(),t=e[0],r=e[1];return this._eventSourceStream=r,t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"eventSourceStream",{get:function(){return sd(this.teeedStream)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dataStream",{get:function(){return sd(this.eventSourceStream.pipeThrough(new r5({transform:function(e,t){try{var r=JSON.parse(e.data);t.enqueue(r)}catch(r){"[DONE]"!==e.data?console.warn("Error when transforming event source data to json",r,e):t.terminate()}}})))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"textStream",{get:function(){return sd(this.dataStream.pipeThrough(new r5({transform:function(e,t){var r;t.enqueue(null!==(r=null==e?void 0:e.content)&&void 0!==r?r:"")}})))},enumerable:!1,configurable:!0}),e}(),sE=function(){return(sE=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},sI=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},sO=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}};function sT(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tool_choice,i=e.tools,s=e.top_p;return sE(sE({},e),{messages:t,model:r,temperature:n,tool_choice:(o&&"auto"!==o&&console.warn("`tool_choice` is not 'auto'"),o),tools:i,top_p:s})}var sR=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="zhipu/api/paas/v4/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return sI(this,void 0,void 0,function(){var r,n;return sO(this,function(o){switch(o.label){case 0:return r=sT(e),[4,this.req({url:this.url,data:sE(sE({},r),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return n=o.sent(),[2,sE(sE({},n),{rawResponse:n})]}})})},e.prototype.doStream=function(e,t){return sI(this,void 0,void 0,function(){var r,n;return sO(this,function(o){switch(o.label){case 0:return r=sT(e),n=null,[4,this.req({url:this.url,data:sE(sE({},r),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,sd(sp(sf(o.sent())).pipeThrough(new r5({transform:function(e,t){var r=e.choices.map(function(e){var t=e.delta;return(null==n&&(n=sv(t)),n)?sE(sE({},e),{finish_reason:"tool_calls",delta:t}):e}),o=sE(sE({},e),{choices:r});t.enqueue(sE(sE({},o),{rawResponse:e}))}})))]}})})},e}(),sA=function(){return(sA=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function sP(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tool_choice,i=e.tools,s=e.top_p;return sA(sA({},e),{messages:(t.forEach(function(e){"tool_calls"in e&&e.tool_calls.filter(function(e){return"function"!==e.type}).forEach(function(t){return console.warn("`type` in tool_call is not 'function'",t,e)})}),t),model:r,tools:function(){if(i)return i.forEach(function(e){"function"!==e.type&&console.warn("`type` in tool is not 'function'",e)}),i}(),top_p:s,tool_choice:o,temperature:n})}function sx(e){return"object"!=typeof e||null==e?e:Array.isArray(e)?e.map(function(e){return sx(e)}):Object.entries(e).reduce(function(e,t){var r,n=t[0],o=t[1];return e["_"===(r=n.replace(/[A-Z]/g,function(e){return"_".concat(e.toLowerCase())})).charAt(0)?r.slice(1):r]="object"==typeof o?sx(o):o,e},{})}var sN=function(){return(sN=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},sC=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},sL=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},sj=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="hunyuan-beta/openapi/v1/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return sC(this,void 0,void 0,function(){var r;return sL(this,function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:sN(sN({},sP(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,sN(sN({},r),{rawResponse:r})]}})})},e.prototype.doStream=function(e,t){return sC(this,void 0,void 0,function(){var r;return sL(this,function(n){switch(n.label){case 0:return r=null,[4,this.req({url:this.url,data:sN(sN({},sP(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,sd(sp(sf(n.sent())).pipeThrough(new r5({transform:function(e,t){var n=e.choices.map(function(e){var t=e.delta;return(null==r&&(r=sv(t)),r)?sN(sN({},e),{finish_reason:"tool_calls",delta:t}):e}),o=sN(sN({},e),{choices:n});t.enqueue(sN(sN({},o),{rawResponse:e}))}})))]}})})},e}(),sU=function(){return(sU=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},sD=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},sk=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},sq=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="hunyuan",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return sD(this,void 0,void 0,function(){var r,n;return sk(this,function(o){switch(o.label){case 0:return[4,this.req({url:this.url,headers:{"X-Tc-Action":"ChatCompletions"},data:sU(sU({},sP(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return n=sx((r=o.sent()).Response),[2,sU(sU({},n),{rawResponse:r})]}})})},e.prototype.doStream=function(e,t){return sD(this,void 0,void 0,function(){var r;return sk(this,function(n){switch(n.label){case 0:return r=null,[4,this.req({url:this.url,headers:{"X-Tc-Action":"ChatCompletions"},data:sU(sU({},e),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,sd(sp(sf(n.sent())).pipeThrough(new r5({transform:function(e,t){var n=sx(e),o=n.choices.map(function(e){var t=e.delta;return(null==r&&(r=sv(t)),r)?sU(sU({},e),{finish_reason:"tool_calls",delta:t}):e}),i=sU(sU({},n),{choices:o});t.enqueue(sU(sU({},i),{rawResponse:e}))}})))]}})})},e}(),sM=function(){return(sM=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},sB=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},sW=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}};function sF(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tools,i=e.top_p;return sM(sM({},e),{messages:t,model:r,tools:o,top_p:i,temperature:n})}var s$=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="ark/api/v3/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return sB(this,void 0,void 0,function(){var r;return sW(this,function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:sM(sM({},sF(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,sM(sM({},r),{rawResponse:r})]}})})},e.prototype.doStream=function(e,t){return sB(this,void 0,void 0,function(){var r;return sW(this,function(n){switch(n.label){case 0:return r=null,[4,this.req({url:this.url,data:sM(sM({},sF(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,sd(sp(sf(n.sent())).pipeThrough(new r5({transform:function(e,t){var n=e.choices.map(function(e){var t=e.delta;return(null==r&&(r=sv(t)),r)?sM(sM({},e),{finish_reason:"tool_calls",delta:t}):e}),o=sM(sM({},e),{choices:n});t.enqueue(sM(sM({},o),{rawResponse:e}))}})))]}})})},e}(),sV=function(){return(sV=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},sG=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},sH=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}};function sK(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tools,i=e.top_p;return sV(sV({},e),{messages:t,model:r,tools:o,top_p:i,temperature:n})}var sJ=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="dashscope/compatible-mode/v1/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return sG(this,void 0,void 0,function(){var r;return sH(this,function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:sV(sV({},sK(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,sV(sV({},r),{rawResponse:r})]}})})},e.prototype.doStream=function(e,t){return sG(this,void 0,void 0,function(){var r;return sH(this,function(n){switch(n.label){case 0:return r=null,[4,this.req({url:this.url,data:sV(sV({},sK(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,sd(sp(sf(n.sent())).pipeThrough(new r5({transform:function(e,t){var n=e.choices.map(function(e){var t=Object.assign(e.delta,{role:"assistant"});return(null==r&&(r=sv(t)),r)?sV(sV({},e),{finish_reason:"tool_calls",delta:t}):sV(sV({},e),{delta:t})}),o=sV(sV({},e),{choices:n});t.enqueue(sV(sV({},o),{rawResponse:e}))}})))]}})})},e}(),sz=function(){return(sz=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},sY=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},sQ=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}};function sZ(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tools,i=e.top_p;return sz(sz({},e),{messages:t,model:r,tools:o,top_p:i,temperature:n})}var sX=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="01-ai/v1/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return sY(this,void 0,void 0,function(){var r;return sQ(this,function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:sz(sz({},sZ(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,sz(sz({},r),{rawResponse:r})]}})})},e.prototype.doStream=function(e,t){return sY(this,void 0,void 0,function(){var r;return sQ(this,function(n){switch(n.label){case 0:return r=null,[4,this.req({url:this.url,data:sz(sz({},sZ(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,sd(sp(sf(n.sent())).pipeThrough(new r5({transform:function(e,t){if((null===(i=null===(o=null===(n=null==e?void 0:e.choices)||void 0===n?void 0:n[0])||void 0===o?void 0:o.delta)||void 0===i?void 0:i.content)||(null===(u=null===(a=null===(s=null==e?void 0:e.choices)||void 0===s?void 0:s[0])||void 0===a?void 0:a.delta)||void 0===u?void 0:u.tool_calls)){var n,o,i,s,a,u,c=e.choices.map(function(e){var t=Object.assign(e.delta,{role:"assistant"});return(null==r&&(r=sv(t)),r)?sz(sz({},e),{finish_reason:"tool_calls",delta:t}):sz(sz({},e),{delta:t})}),l=sz(sz({},e),{choices:c});t.enqueue(sz(sz({},l),{rawResponse:e}))}}})))]}})})},e}(),s0=function(){return(s0=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},s1=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},s2=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}};function s3(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tools,i=e.top_p;return s0(s0({},e),{messages:t,model:r,tools:o,top_p:i,temperature:n})}var s4=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="moonshot/v1/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return s1(this,void 0,void 0,function(){var r;return s2(this,function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:s0(s0({},s3(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,s0(s0({},r),{rawResponse:r})]}})})},e.prototype.doStream=function(e,t){return s1(this,void 0,void 0,function(){var r;return s2(this,function(n){switch(n.label){case 0:return r=null,[4,this.req({url:this.url,data:s0(s0({},s3(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,sd(sp(sf(n.sent())).pipeThrough(new r5({transform:function(e,t){var n=e.choices.map(function(e){var t=e.delta;return(null==r&&(r=sv(t)),r)?s0(s0({},e),{finish_reason:"tool_calls",delta:t}):e}),o=s0(s0({},e),{choices:n});t.enqueue(s0(s0({},o),{rawResponse:e}))}})))]}})})},e}(),s5=function(){return(s5=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},s6=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},s8=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},s7=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="hunyuan-exp/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e){return s6(this,void 0,void 0,function(){var t;return s8(this,function(r){switch(r.label){case 0:return[4,this.req({url:this.url,data:s5(s5({},sP(e)),{stream:!1}),stream:!1})];case 1:return t=r.sent(),[2,s5(s5({},t),{rawResponse:t})]}})})},e.prototype.doStream=function(e){return s6(this,void 0,void 0,function(){var t;return s8(this,function(r){switch(r.label){case 0:return t=null,[4,this.req({url:this.url,data:s5(s5({},sP(e)),{stream:!0}),stream:!0})];case 1:return[2,sd(sp(sf(r.sent())).pipeThrough(new r5({transform:function(e,r){var n=e.choices.map(function(e){var r=e.delta;return(null==t&&(t=sv(r)),t)?s5(s5({},e),{finish_reason:"tool_calls",delta:r}):e}),o=s5(s5({},e),{choices:n});r.enqueue(s5(s5({},o),{rawResponse:e}))}})))]}})})},e}(),s9=function(){return(s9=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},ae=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},at=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},ar=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="hunyuan-open/v1/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return ae(this,void 0,void 0,function(){var r;return at(this,function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:s9(s9({},sP(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,s9(s9({},r),{rawResponse:r})]}})})},e.prototype.doStream=function(e,t){return ae(this,void 0,void 0,function(){var r;return at(this,function(n){switch(n.label){case 0:return r=null,[4,this.req({url:this.url,data:s9(s9({},sP(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,sd(sp(sf(n.sent())).pipeThrough(new r5({transform:function(e,t){var n=e.choices.map(function(e){var t=e.delta;return(null==r&&(r=sv(t)),r)?s9(s9({},e),{finish_reason:"tool_calls",delta:t}):e}),o=s9(s9({},e),{choices:n});t.enqueue(s9(s9({},o),{rawResponse:e}))}})))]}})})},e}(),an=function(){return(an=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},ao=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},ai=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},as=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="deepseek/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return ao(this,void 0,void 0,function(){var r;return ai(this,function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:an(an({},e),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,an(an({},r),{rawResponse:r})]}})})},e.prototype.doStream=function(e,t){return ao(this,void 0,void 0,function(){var r;return ai(this,function(n){switch(n.label){case 0:return r=null,[4,this.req({url:this.url,data:an(an({},e),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return[2,sd(sp(sf(n.sent())).pipeThrough(new r5({transform:function(e,t){var n=e.choices.map(function(e){var t=e.delta;return(null==r&&(r=sv(t)),r)?an(an({},e),{finish_reason:"tool_calls",delta:t}):e}),o=an(an({},e),{choices:n});t.enqueue(an(an({},o),{rawResponse:e}))}})))]}})})},e}(),aa=function(){return(aa=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},au=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},ac=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},al=function(){function e(e,t,r){void 0===r&&(r=""),this.req=e,this.baseUrl=t,this.subUrl=r}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e){return au(this,void 0,void 0,function(){var t;return ac(this,function(r){switch(r.label){case 0:return[4,this.req({url:this.url,data:aa(aa({},e),{stream:!1}),stream:!1})];case 1:return t=r.sent(),[2,aa(aa({},t),{rawResponse:t})]}})})},e.prototype.doStream=function(e){return au(this,void 0,void 0,function(){var t;return ac(this,function(r){switch(r.label){case 0:return t=null,[4,this.req({url:this.url,data:aa(aa({},e),{stream:!0}),stream:!0})];case 1:return[2,sd(sp(sf(r.sent())).pipeThrough(new r5({transform:function(e,r){var n=e.choices.map(function(e){var r=e.delta;return(null==t&&(t=sv(r)),t)?aa(aa({},e),{finish_reason:"tool_calls",delta:r}):e}),o=aa(aa({},e),{choices:n});r.enqueue(aa(aa({},o),{rawResponse:e}))}})))]}})})},e}(),af=function(){return(af=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},ah=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},ad=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},ap=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r},ay=function(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e="function"==typeof __values?__values(e):e[Symbol.iterator](),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise(function(n,o){(function(e,t,r,n){Promise.resolve(n).then(function(t){e({value:t,done:r})},t)})(n,o,(t=e[r](t)).done,t.value)})}}},av=function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};function am(e){var t,r=e.onStepFinish,n=e.abortSignal,o=e.maxSteps,i=e.topP,s=e.toolChoice,a=ap(e,["onStepFinish","abortSignal","maxSteps","topP","toolChoice"]);if(null!=o&&o<1)throw Error("`maxSteps` muse be greater than 0.");return[{onStepFinish:r,abortSignal:n,maxSteps:o},af(af({},a),{tools:null===(t=a.tools)||void 0===t?void 0:t.map(function(e){return"fn"in e?{type:"function",function:{description:e.description,name:e.name,parameters:e.parameters}}:e}),top_p:null!=i?i:a.top_p,tool_choice:null!=s?s:a.tool_choice})]}var ag=function(){function e(e){this.model=e}return e.prototype.generateText=function(e,t){var r,n;return ah(this,void 0,void 0,function(){var o,i,s,a,u,c,l,f,h,d,p,y,v,m,g,b,_,w,S,E,I,O=this;return ad(this,function(T){switch(T.label){case 0:return o=[],i={completion_tokens:0,prompt_tokens:0,total_tokens:0},u=(a=(s=am(e))[0]).onStepFinish,l=void 0===(c=a.maxSteps)?10:c,f=s[1],[4,(h=function(){return O.model.doGenerate(f,t)})()];case 1:d=T.sent(),p=1,d.rawResponse&&o.push(d.rawResponse),y=null,T.label=2;case 2:if(!(p<l&&null!=(y=ab(d))))return[3,9];v=aE(d.usage),aI(i,v),T.label=3;case 3:return T.trys.push([3,7,,8]),[4,aS(y)];case 4:return m=T.sent(),g=d.choices[0],[4,null==u?void 0:u({finishReason:g.finish_reason,messages:f.messages.slice(),text:g.message.content,toolCall:y,toolResult:m,stepUsage:v,totalUsage:Object.assign({},i)})];case 5:return T.sent(),a_(f.messages,g.message,m),[4,h()];case 6:return(d=T.sent()).rawResponse&&o.push(d.rawResponse),p+=1,[3,8];case 7:return b=T.sent(),[2,{text:"",messages:f.messages,usage:i,error:b,rawResponses:o}];case 8:return[3,2];case 9:return S=null!==(n=null==(w=null==(_=null===(r=null==d?void 0:d.choices)||void 0===r?void 0:r[0])?void 0:_.message)?void 0:w.content)&&void 0!==n?n:"",E=w?av(av([],f.messages,!0),[w],!1):f.messages,I=aE(d.usage),aI(i,I),[4,null==u?void 0:u({finishReason:_.finish_reason,messages:E.slice(),text:S,toolCall:ab(d),toolResult:null,stepUsage:I,totalUsage:Object.assign({},i)})];case 10:return T.sent(),[2,{text:S,messages:E,usage:i,rawResponses:o}]}})})},e.prototype.streamText=function(e,t){var r;return ah(this,void 0,void 0,function(){var n,o,i,s,a,u,c,l,f,h,d,p,y,v,m,g,b,_,w,S,E,I,O,T,R,A,P,x,N,C,L,j,U,D,k,q=this;return ad(this,function(M){switch(M.label){case 0:return n={completion_tokens:0,prompt_tokens:0,total_tokens:0},s=(i=(o=am(e))[0]).onStepFinish,u=void 0===(a=i.maxSteps)?10:a,c=o[1],[4,(l=function(){return q.model.doStream(c,t)})()];case 1:f=M.sent(),h=1,d=null,p=function(){var e=f.tee(),t=e[0],r=e[1];return f=sd(t),function(e){var t,r,n,o,i,s,a,u,c,l;return ah(this,void 0,void 0,function(){var f,h,d,p,y,v,m,g,b,_,w,S;return ad(this,function(E){switch(E.label){case 0:f={completion_tokens:0,prompt_tokens:0,total_tokens:0},h=sd(e),p={role:"assistant",content:"",tool_calls:[d={id:"",function:{name:"",arguments:""},type:""}]},E.label=1;case 1:E.trys.push([1,6,7,12]),y=!0,v=ay(h),E.label=2;case 2:return[4,v.next()];case 3:if(t=(m=E.sent()).done)return[3,5];o=m.value,y=!1;try{if(!(b=null==(g=o)?void 0:g.choices[0])||(_=b.finish_reason,w=b.delta,"tool_calls"!==_))return[2,null];if(!w||(w.content&&(p.content+=w.content),!("tool_calls"in w)))return[3,4];(null==(S=null===(i=null==w?void 0:w.tool_calls)||void 0===i?void 0:i[0])?void 0:S.id)&&(d.id=S.id),(null==S?void 0:S.type)&&(d.type=S.type),(null===(s=null==S?void 0:S.function)||void 0===s?void 0:s.name)&&(d.function.name=S.function.name),(null===(a=null==S?void 0:S.function)||void 0===a?void 0:a.arguments)&&(d.function.arguments+=S.function.arguments),(null===(u=null==g?void 0:g.usage)||void 0===u?void 0:u.completion_tokens)&&(f.completion_tokens=g.usage.completion_tokens),(null===(c=null==g?void 0:g.usage)||void 0===c?void 0:c.prompt_tokens)&&(f.prompt_tokens=g.usage.prompt_tokens),(null===(l=null==g?void 0:g.usage)||void 0===l?void 0:l.total_tokens)&&(f.total_tokens=g.usage.total_tokens)}finally{y=!0}E.label=4;case 4:return[3,2];case 5:return[3,12];case 6:return r={error:E.sent()},[3,12];case 7:if(E.trys.push([7,,10,11]),!(!y&&!t&&(n=v.return)))return[3,9];return[4,n.call(v)];case 8:E.sent(),E.label=9;case 9:return[3,11];case 10:if(r)throw r.error;return[7];case 11:return[7];case 12:return[2,{message:p,usage:f}]}})})}(r)},M.label=2;case 2:if(!(y=h<u))return[3,4];return[4,p()];case 3:y=null!=(d=M.sent()),M.label=4;case 4:if(!y)return[3,11];v=d.message,m=d.usage,aI(n,m),g=null===(r=v.tool_calls)||void 0===r?void 0:r[0],M.label=5;case 5:return M.trys.push([5,9,,10]),[4,aS(g)];case 6:return b=M.sent(),[4,null==s?void 0:s({finishReason:"tool_calls",messages:c.messages.slice(),text:v.content,toolCall:g,toolResult:b,stepUsage:m,totalUsage:Object.assign({},n)})];case 7:return M.sent(),a_(c.messages,v,b),[4,l()];case 8:return f=M.sent(),[3,10];case 9:return _=M.sent(),S=(w=f.tee())[0],E=w[1],[2,{messages:Promise.resolve(c.messages),dataStream:sd(S),textStream:sd(E.pipeThrough(new r5({transform:function(e,t){var r,n,o,i=null===(o=null===(n=null===(r=null==e?void 0:e.choices)||void 0===r?void 0:r[0])||void 0===n?void 0:n.delta)||void 0===o?void 0:o.content;"string"==typeof i&&t.enqueue(i)}}))),usage:Promise.resolve(n),error:_}];case 10:return[3,2];case 11:return[4,p()];case 12:if(d=M.sent())return I=d.message,O=d.usage,aI(n,O),T=av(av([],c.messages,!0),[I],!1),s({messages:T.slice(),finishReason:"tool_call",stepUsage:O,text:I.content,toolCall:I.tool_calls[0],totalUsage:Object.assign({},n)}),A=(R=f.tee())[0],P=R[1],[2,{messages:Promise.resolve(av(av([],c.messages,!0),[I],!1)),dataStream:sd(A),textStream:sd(P.pipeThrough(new r5({transform:function(e,t){var r,n,o,i=null===(o=null===(n=null===(r=null==e?void 0:e.choices)||void 0===r?void 0:r[0])||void 0===n?void 0:n.delta)||void 0===o?void 0:o.content;"string"==typeof i&&t.enqueue(i)}}))),usage:Promise.resolve(n)}];return x=sy(),N=sy(),C={role:"assistant",content:""},L="",j={completion_tokens:0,prompt_tokens:0,total_tokens:0},D=(U=f.pipeThrough(new r5({transform:function(e,t){var r,n,o,i,s,a,u,c,l=null===(o=null===(n=null===(r=null==e?void 0:e.choices)||void 0===r?void 0:r[0])||void 0===n?void 0:n.delta)||void 0===o?void 0:o.content;"string"==typeof l&&(C.content+=l);var f=null===(s=null===(i=null==e?void 0:e.choices)||void 0===i?void 0:i[0])||void 0===s?void 0:s.finish_reason;f&&(L=f),(null===(a=null==e?void 0:e.usage)||void 0===a?void 0:a.completion_tokens)&&(j.completion_tokens=e.usage.completion_tokens),(null===(u=null==e?void 0:e.usage)||void 0===u?void 0:u.prompt_tokens)&&(j.prompt_tokens=e.usage.prompt_tokens),(null===(c=null==e?void 0:e.usage)||void 0===c?void 0:c.total_tokens)&&(j.total_tokens=e.usage.total_tokens),t.enqueue(e)},flush:function(){x.res(av(av([],c.messages,!0),[C],!1)),aI(n,j),N.res(Object.assign({},n)),null==s||s({messages:av(av([],c.messages,!0),[C],!1),finishReason:L,text:C.content,stepUsage:j,totalUsage:Object.assign({},n)})}})).tee())[0],k=U[1],[2,{messages:x.promise,dataStream:sd(D),textStream:sd(k.pipeThrough(new r5({transform:function(e,t){var r,n,o,i=null===(o=null===(n=null===(r=null==e?void 0:e.choices)||void 0===r?void 0:r[0])||void 0===n?void 0:n.delta)||void 0===o?void 0:o.content;"string"==typeof i&&t.enqueue(i)}}))),usage:N.promise}]}})})},e}();function ab(e){var t,r=null===(t=null==e?void 0:e.choices)||void 0===t?void 0:t[0];if(!r)return null;var n=r.finish_reason,o=r.message;return"tool_calls"===n&&o&&sv(o)?o.tool_calls[0]:null}function a_(e,t,r){e.push(t,{role:"tool",tool_call_id:t.tool_calls[0].id,content:JSON.stringify(r)})}var aw=new Map;function aS(e){return aw.get(e.function.name)(JSON.parse(e.function.arguments))}function aE(e){var t,r,n;return{completion_tokens:null!==(t=null==e?void 0:e.completion_tokens)&&void 0!==t?t:0,prompt_tokens:null!==(r=null==e?void 0:e.prompt_tokens)&&void 0!==r?r:0,total_tokens:null!==(n=null==e?void 0:e.total_tokens)&&void 0!==n?n:0}}function aI(e,t){e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens}var aO={hunyuan:sq,"hunyuan-beta":sj,ark:s$,dashscope:sJ,"01-ai":sX,moonshot:s4,zhipu:sR,"hunyuan-exp":s7,"hunyuan-open":ar,deepseek:as},aT=function(){return(aT=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},aR=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},aA=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},aP=function(){function e(e,t){var r=this;this.req=e,this.baseUrl=t,this.modelRequest=function(e){var t=e.url,n=e.data,o=e.headers,i=e.stream,s=e.timeout;return aR(r,void 0,void 0,function(){var e,r;return aA(this,function(a){switch(a.label){case 0:return e={"Content-Type":"application/json"},i&&Object.assign(e,{Accept:"text/event-stream"}),[4,this.req.fetch({method:"post",headers:aT(aT({},e),o),body:JSON.stringify(n),url:t,stream:i,timeout:s})];case 1:return[2,aN((r=a.sent()).data,r.header)]}})})},this.botRequest=function(e){var t=e.method,n=e.url,o=e.data,i=void 0===o?{}:o,s=e.headers,a=e.stream,u=e.timeout;return aR(r,void 0,void 0,function(){var e,r,o;return aA(this,function(c){switch(c.label){case 0:if("get"!==t)return[3,2];return e=aN,[4,this.req.fetch({url:"".concat(n,"?").concat(Object.entries(i).map(function(e){var t=e[0],r=e[1];return"".concat(t,"=").concat(r)}).join("&")),method:t,headers:s,stream:a,timeout:u})];case 1:return[2,e.apply(void 0,[c.sent().data])];case 2:return r={"Content-Type":"application/json"},a&&Object.assign(r,{Accept:"text/event-stream"}),[4,this.req.fetch({url:n,body:JSON.stringify(i),headers:aT(aT({},r),s),stream:a,method:t,timeout:u})];case 3:return[2,aN((o=c.sent()).data,o.header)]}})})},this.aiBaseUrl="".concat(t,"/ai"),this.aiBotBaseUrl="".concat(t,"/aibot"),this.bot=new sw(this.botRequest,this.aiBotBaseUrl)}return e.prototype.createModel=function(e,t){var r,n=aO[e];if(n)r=new n(this.modelRequest,this.aiBaseUrl);else{var o="string"==typeof(null==t?void 0:t.defaultModelSubUrl)?t.defaultModelSubUrl:"/chat/completions";r=new al(this.modelRequest,this.aiBaseUrl,"".concat(e).concat(o))}return new ag(r)},e.prototype.registerModel=function(e,t){if(null!=aO[e]){console.warn("AI model ".concat(e," already exists!"));return}aO[e]=t},e.prototype.registerFunctionTool=function(e){aw.has(e.name)&&console.warn("AI function tool ".concat(e.name," already exists and will be overwritten!")),aw.set(e.name,e.fn)},e}(),ax="请检查调用方式，或前往云开发 AI+ 首页查看文档：https://tcb.cloud.tencent.com/dev#/ai";function aN(e,t){var r,n;return aR(this,void 0,void 0,function(){var o;return aA(this,function(i){switch(i.label){case 0:if(!("object"==typeof e&&e&&"then"in e))return[3,2];return[4,e];case 1:if("object"==typeof(o=i.sent())&&o&&"code"in o&&"NORMAL"!==o.code)throw Error("AI+ 请求出错，错误码：".concat(o.code,"，错误信息：").concat(o.message,"\n").concat(ax,"\n").concat(JSON.stringify(o,null,2)));return[2,e];case 2:if(!(null===(n=null===(r=null==t?void 0:t.get)||void 0===r?void 0:r.call(t,"content-type"))||void 0===n?void 0:n.includes("application/json")))return[3,4];return[4,function(e){var t,r,n,o;return sa(this,void 0,void 0,function(){var i,s,a,u,c,l;return su(this,function(f){switch(f.label){case 0:i=sd(sf(e).pipeThrough(new sh)),s="",f.label=1;case 1:f.trys.push([1,6,7,12]),a=!0,u=sc(i),f.label=2;case 2:return[4,u.next()];case 3:if(t=(c=f.sent()).done)return[3,5];o=c.value,a=!1;try{l=o,s+=l}finally{a=!0}f.label=4;case 4:return[3,2];case 5:return[3,12];case 6:return r={error:f.sent()},[3,12];case 7:if(f.trys.push([7,,10,11]),!(!a&&!t&&(n=u.return)))return[3,9];return[4,n.call(u)];case 8:f.sent(),f.label=9;case 9:return[3,11];case 10:if(r)throw r.error;return[7];case 11:return[7];case 12:return[2,JSON.parse(s)]}})})}(e)];case 3:if("object"==typeof(o=i.sent())&&o&&"code"in o&&"NORMAL"!==o.code)throw Error("AI+ 请求出错，错误码：".concat(o.code,"，错误信息：").concat(o.message,"\n").concat(ax,"\n").concat(JSON.stringify(o,null,2)));i.label=4;case 4:return[2,e]}})})}var aC=function(){return(aC=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},aL=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},aj=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};aj(O,["MODELS"]);var aU=function(e){var t=e.getAccessToken,r=e.req;return{download:r.download,post:r.post,upload:r.upload,fetch:function(e){var n,o,i,s;return n=void 0,o=void 0,i=void 0,s=function(){var n,o,i,s,a;return aL(this,function(u){switch(u.label){case 0:if("function"!=typeof r.fetch)throw Error("req.fetch is not a function");if(n=e.token,i=void 0===(o=e.headers)?{}:o,s=aj(e,["token","headers"]),!(null!=n))return[3,1];return a=n,[3,3];case 1:return[4,t()];case 2:a=u.sent().accessToken,u.label=3;case 3:return[2,r.fetch(aC({headers:aC({Authorization:"Bearer ".concat(a)},i)},s))]}})},new(i||(i=Promise))(function(e,t){function r(e){try{u(s.next(e))}catch(e){t(e)}}function a(e){try{u(s.throw(e))}catch(e){t(e)}}function u(t){var n;t.done?e(t.value):((n=t.value)instanceof i?n:new i(function(e){e(n)})).then(r,a)}u((s=s.apply(n,o||[])).next())})}}},aD={name:"ai",entity:{ai:function(e){var t,r,n,o,i,s,a,u,c,l,f=this.request;if(null==f.fetch)throw Error("cloudbase.request.fetch() unimplemented!");return i=(o={req:f,baseUrl:null!==(l=null==e?void 0:e.baseUrl)&&void 0!==l?l:(r=(t=this.getEndPointWithKey("GATEWAY")).BASE_URL,n=t.PROTOCOL,"".concat(n).concat(r)),handleReqInstance:function(e){return e.req}}).env,s=o.baseUrl,a=o.req,u=o.getAccessToken,c=o.handleReqInstance,new aP(function(){if(null==c){if(null==u)throw Error("`getAccessToken` is required when `handleReqInstance` is not provided!");return aU({req:a,getAccessToken:u})}return c({req:a})}(),function(){if(null!=s)return s;if(null==i)throw Error("`env` is required when `baseUrl` is not provided!");return"https://".concat(i,".api.tcloudbasegateway.com/v1")}())}}};!function(e){e.DocIDError="文档ID不合法",e.CollNameError="集合名称不合法",e.OpStrError="操作符不合法",e.DirectionError="排序字符不合法",e.IntergerError="must be integer",e.QueryParamTypeError="查询参数必须为对象",e.QueryParamValueError="查询参数对象值不能均为undefined"}(m||(m={}));var ak={Number:"Number",Object:"Object",Array:"Array",GeoPoint:"GeoPoint",GeoLineString:"GeoLineString",GeoPolygon:"GeoPolygon",GeoMultiPoint:"GeoMultiPoint",GeoMultiLineString:"GeoMultiLineString",GeoMultiPolygon:"GeoMultiPolygon",Timestamp:"Date",ServerDate:"ServerDate",BsonDate:"BsonDate"},aq=["desc","asc"],aM=["<","<=","==",">=",">"];(function(e){e.lt="<",e.gt=">",e.lte="<=",e.gte=">=",e.eq="=="})(g||(g={})),(v={})[g.eq]="$eq",v[g.lt]="$lt",v[g.lte]="$lte",v[g.gt]="$gt",v[g.gte]="$gte",function(e){e.WHERE="WHERE",e.DOC="DOC"}(b||(b={}));var aB=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),aW=[],aF={},a$=function(e){function t(t,r){if(r!==aF)throw TypeError("InternalSymbol cannot be constructed with new operator");return e.call(this,t)||this}return aB(t,e),t.for=function(e){for(var r=0,n=aW.length;r<n;r++)if(aW[r].target===e)return aW[r].instance;var o=new t(e,aF);return aW.push({target:e,instance:o}),o},t}(function(e){Object.defineProperties(this,{target:{enumerable:!1,writable:!1,configurable:!1,value:e}})}),aV=a$.for("UNSET_FIELD_NAME"),aG=a$.for("UPDATE_COMMAND"),aH=a$.for("QUERY_COMMAND"),aK=a$.for("LOGIC_COMMAND"),aJ=a$.for("GEO_POINT"),az=a$.for("SYMBOL_GEO_LINE_STRING"),aY=a$.for("SYMBOL_GEO_POLYGON"),aQ=a$.for("SYMBOL_GEO_MULTI_POINT"),aZ=a$.for("SYMBOL_GEO_MULTI_LINE_STRING"),aX=a$.for("SYMBOL_GEO_MULTI_POLYGON"),a0=a$.for("SERVER_DATE"),a1=a$.for("REGEXP"),a2=function(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()},a3=function(e){return"object"===a2(e)},a4=function(e){return"number"===a2(e)},a5=function(e){return Array.isArray(e)},a6=function(e){return"date"===a2(e)},a8=function(e){return"regexp"===a2(e)},a7=function(e){return e&&e._internalType instanceof a$},a9=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},ue=function(){function e(e){if(!a5(e))throw TypeError('"points" must be of type Point[]. Received type '+typeof e);if(e.length<2)throw Error('"points" must contain 2 points at least');e.forEach(function(e){if(!(e instanceof up))throw TypeError('"points" must be of type Point[]. Received type '+typeof e+"[]")}),this.points=e}return e.prototype.parse=function(e){var t;return(t={})[e]={type:"LineString",coordinates:this.points.map(function(e){return e.toJSON().coordinates})},t},e.prototype.toJSON=function(){return{type:"LineString",coordinates:this.points.map(function(e){return e.toJSON().coordinates})}},e.validate=function(e){var t,r;if("LineString"!==e.type||!a5(e.coordinates))return!1;try{for(var n=a9(e.coordinates),o=n.next();!o.done;o=n.next()){var i=o.value;if(!a4(i[0])||!a4(i[1]))return!1}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}return!0},e.isClosed=function(e){var t=e.points[0],r=e.points[e.points.length-1];if(t.latitude===r.latitude&&t.longitude===r.longitude)return!0},Object.defineProperty(e.prototype,"_internalType",{get:function(){return az},enumerable:!0,configurable:!0}),e}(),ut=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},ur=function(){function e(e){if(!a5(e))throw TypeError('"lines" must be of type LineString[]. Received type '+typeof e);if(0===e.length)throw Error("Polygon must contain 1 linestring at least");e.forEach(function(e){if(!(e instanceof ue))throw TypeError('"lines" must be of type LineString[]. Received type '+typeof e+"[]");if(!ue.isClosed(e))throw Error("LineString "+e.points.map(function(e){return e.toReadableString()})+" is not a closed cycle")}),this.lines=e}return e.prototype.parse=function(e){var t;return(t={})[e]={type:"Polygon",coordinates:this.lines.map(function(e){return e.points.map(function(e){return[e.longitude,e.latitude]})})},t},e.prototype.toJSON=function(){return{type:"Polygon",coordinates:this.lines.map(function(e){return e.points.map(function(e){return[e.longitude,e.latitude]})})}},e.validate=function(e){var t,r,n,o;if("Polygon"!==e.type||!a5(e.coordinates))return!1;try{for(var i=ut(e.coordinates),s=i.next();!s.done;s=i.next()){var a=s.value;if(!this.isCloseLineString(a))return!1;try{for(var u=(n=void 0,ut(a)),c=u.next();!c.done;c=u.next()){var l=c.value;if(!a4(l[0])||!a4(l[1]))return!1}}catch(e){n={error:e}}finally{try{c&&!c.done&&(o=u.return)&&o.call(u)}finally{if(n)throw n.error}}}}catch(e){t={error:e}}finally{try{s&&!s.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}return!0},e.isCloseLineString=function(e){var t=e[0],r=e[e.length-1];return t[0]===r[0]&&t[1]===r[1]},Object.defineProperty(e.prototype,"_internalType",{get:function(){return aX},enumerable:!0,configurable:!0}),e}(),un=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},uo=function(){function e(e){if(!a5(e))throw TypeError('"points" must be of type Point[]. Received type '+typeof e);if(0===e.length)throw Error('"points" must contain 1 point at least');e.forEach(function(e){if(!(e instanceof up))throw TypeError('"points" must be of type Point[]. Received type '+typeof e+"[]")}),this.points=e}return e.prototype.parse=function(e){var t;return(t={})[e]={type:"MultiPoint",coordinates:this.points.map(function(e){return e.toJSON().coordinates})},t},e.prototype.toJSON=function(){return{type:"MultiPoint",coordinates:this.points.map(function(e){return e.toJSON().coordinates})}},e.validate=function(e){var t,r;if("MultiPoint"!==e.type||!a5(e.coordinates))return!1;try{for(var n=un(e.coordinates),o=n.next();!o.done;o=n.next()){var i=o.value;if(!a4(i[0])||!a4(i[1]))return!1}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}return!0},Object.defineProperty(e.prototype,"_internalType",{get:function(){return aQ},enumerable:!0,configurable:!0}),e}(),ui=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},us=function(){function e(e){if(!a5(e))throw TypeError('"lines" must be of type LineString[]. Received type '+typeof e);if(0===e.length)throw Error("Polygon must contain 1 linestring at least");e.forEach(function(e){if(!(e instanceof ue))throw TypeError('"lines" must be of type LineString[]. Received type '+typeof e+"[]")}),this.lines=e}return e.prototype.parse=function(e){var t;return(t={})[e]={type:"MultiLineString",coordinates:this.lines.map(function(e){return e.points.map(function(e){return[e.longitude,e.latitude]})})},t},e.prototype.toJSON=function(){return{type:"MultiLineString",coordinates:this.lines.map(function(e){return e.points.map(function(e){return[e.longitude,e.latitude]})})}},e.validate=function(e){var t,r,n,o;if("MultiLineString"!==e.type||!a5(e.coordinates))return!1;try{for(var i=ui(e.coordinates),s=i.next();!s.done;s=i.next()){var a=s.value;try{for(var u=(n=void 0,ui(a)),c=u.next();!c.done;c=u.next()){var l=c.value;if(!a4(l[0])||!a4(l[1]))return!1}}catch(e){n={error:e}}finally{try{c&&!c.done&&(o=u.return)&&o.call(u)}finally{if(n)throw n.error}}}}catch(e){t={error:e}}finally{try{s&&!s.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}return!0},Object.defineProperty(e.prototype,"_internalType",{get:function(){return aZ},enumerable:!0,configurable:!0}),e}(),ua=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},uu=function(){function e(e){var t,r;if(!a5(e))throw TypeError('"polygons" must be of type Polygon[]. Received type '+typeof e);if(0===e.length)throw Error("MultiPolygon must contain 1 polygon at least");try{for(var n=ua(e),o=n.next();!o.done;o=n.next()){var i=o.value;if(!(i instanceof ur))throw TypeError('"polygon" must be of type Polygon[]. Received type '+typeof i+"[]")}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}this.polygons=e}return e.prototype.parse=function(e){var t;return(t={})[e]={type:"MultiPolygon",coordinates:this.polygons.map(function(e){return e.lines.map(function(e){return e.points.map(function(e){return[e.longitude,e.latitude]})})})},t},e.prototype.toJSON=function(){return{type:"MultiPolygon",coordinates:this.polygons.map(function(e){return e.lines.map(function(e){return e.points.map(function(e){return[e.longitude,e.latitude]})})})}},e.validate=function(e){var t,r,n,o,i,s;if("MultiPolygon"!==e.type||!a5(e.coordinates))return!1;try{for(var a=ua(e.coordinates),u=a.next();!u.done;u=a.next()){var c=u.value;try{for(var l=(n=void 0,ua(c)),f=l.next();!f.done;f=l.next()){var h=f.value;try{for(var d=(i=void 0,ua(h)),p=d.next();!p.done;p=d.next()){var y=p.value;if(!a4(y[0])||!a4(y[1]))return!1}}catch(e){i={error:e}}finally{try{p&&!p.done&&(s=d.return)&&s.call(d)}finally{if(i)throw i.error}}}}catch(e){n={error:e}}finally{try{f&&!f.done&&(o=l.return)&&o.call(l)}finally{if(n)throw n.error}}}}catch(e){t={error:e}}finally{try{u&&!u.done&&(r=a.return)&&r.call(a)}finally{if(t)throw t.error}}return!0},Object.defineProperty(e.prototype,"_internalType",{get:function(){return aY},enumerable:!0,configurable:!0}),e}(),uc=function(){function e(e){var t=(void 0===e?{}:e).offset;this.offset=void 0===t?0:t}return Object.defineProperty(e.prototype,"_internalType",{get:function(){return a0},enumerable:!0,configurable:!0}),e.prototype.parse=function(){return{$date:{offset:this.offset}}},e}();function ul(e){return new uc(e)}var uf=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)s.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return s},uh=function(){function e(){}return e.formatResDocumentData=function(t){return t.map(function(t){return e.formatField(t)})},e.formatField=function(t){var r=Object.keys(t),n={};return Array.isArray(t)&&(n=[]),r.forEach(function(r){var o,i=t[r];switch(e.whichType(i)){case ak.GeoPoint:o=new up(i.coordinates[0],i.coordinates[1]);break;case ak.GeoLineString:o=new ue(i.coordinates.map(function(e){return new up(e[0],e[1])}));break;case ak.GeoPolygon:o=new ur(i.coordinates.map(function(e){return new ue(e.map(function(e){var t=uf(e,2);return new up(t[0],t[1])}))}));break;case ak.GeoMultiPoint:o=new uo(i.coordinates.map(function(e){return new up(e[0],e[1])}));break;case ak.GeoMultiLineString:o=new us(i.coordinates.map(function(e){return new ue(e.map(function(e){var t=uf(e,2);return new up(t[0],t[1])}))}));break;case ak.GeoMultiPolygon:o=new uu(i.coordinates.map(function(e){return new ur(e.map(function(e){return new ue(e.map(function(e){var t=uf(e,2);return new up(t[0],t[1])}))}))}));break;case ak.Timestamp:o=new Date(1e3*i.$timestamp);break;case ak.Object:case ak.Array:o=e.formatField(i);break;case ak.ServerDate:o=new Date(i.$date);break;default:o=i}Array.isArray(n)?n.push(o):n[r]=o}),n},e.whichType=function(e){var t=Object.prototype.toString.call(e).slice(8,-1);if(t===ak.Timestamp)return ak.BsonDate;if(t===ak.Object){if(e instanceof up)return ak.GeoPoint;if(e instanceof Date)return ak.Timestamp;if(e instanceof uc)return ak.ServerDate;e.$timestamp?t=ak.Timestamp:e.$date?t=ak.ServerDate:up.validate(e)?t=ak.GeoPoint:ue.validate(e)?t=ak.GeoLineString:ur.validate(e)?t=ak.GeoPolygon:uo.validate(e)?t=ak.GeoMultiPoint:us.validate(e)?t=ak.GeoMultiLineString:uu.validate(e)&&(t=ak.GeoMultiPolygon)}return t},e.generateDocId=function(){for(var e="ABCDEFabcdef0123456789",t="",r=0;r<24;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},e}(),ud=function(){function e(){}return e.isGeopoint=function(e,t){if(uh.whichType(t)!==ak.Number)throw Error("Geo Point must be number type");var r=Math.abs(t);if("latitude"===e&&r>90)throw Error("latitude should be a number ranges from -90 to 90");if("longitude"===e&&r>180)throw Error("longitude should be a number ranges from -180 to 180");return!0},e.isInteger=function(e,t){if(!Number.isInteger(t))throw Error(e+m.IntergerError);return!0},e.isFieldOrder=function(e){if(-1===aq.indexOf(e))throw Error(m.DirectionError);return!0},e.isFieldPath=function(e){if(!/^[a-zA-Z0-9-_\.]/.test(e))throw Error();return!0},e.isOperator=function(e){if(-1===aM.indexOf(e))throw Error(m.OpStrError);return!0},e.isCollName=function(e){if(!/^[a-zA-Z0-9]([a-zA-Z0-9-_]){1,32}$/.test(e))throw Error(m.CollNameError);return!0},e.isDocID=function(e){if(!/^([a-fA-F0-9]){24}$/.test(e))throw Error(m.DocIDError);return!0},e}(),up=function(){function e(e,t){ud.isGeopoint("longitude",e),ud.isGeopoint("latitude",t),this.longitude=e,this.latitude=t}return e.prototype.parse=function(e){var t;return(t={})[e]={type:"Point",coordinates:[this.longitude,this.latitude]},t},e.prototype.toJSON=function(){return{type:"Point",coordinates:[this.longitude,this.latitude]}},e.prototype.toReadableString=function(){return"["+this.longitude+","+this.latitude+"]"},e.validate=function(e){return"Point"===e.type&&a5(e.coordinates)&&ud.isGeopoint("longitude",e.coordinates[0])&&ud.isGeopoint("latitude",e.coordinates[1])},Object.defineProperty(e.prototype,"_internalType",{get:function(){return aJ},enumerable:!0,configurable:!0}),e}(),uy=function(){if(!Promise){(e=function(){}).promise={};var e,t=function(){throw Error('Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.')};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}var r=new Promise(function(t,r){e=function(e,n){return e?r(e):t(n)}});return e.promise=r,e};!function(e){e.SET="set",e.REMOVE="remove",e.INC="inc",e.MUL="mul",e.PUSH="push",e.PULL="pull",e.PULL_ALL="pullAll",e.POP="pop",e.SHIFT="shift",e.UNSHIFT="unshift",e.ADD_TO_SET="addToSet",e.BIT="bit",e.RENAME="rename",e.MAX="max",e.MIN="min"}(_||(_={}));var uv=function(){function e(e,t,r){this._internalType=aG,Object.defineProperties(this,{_internalType:{enumerable:!1,configurable:!1}}),this.operator=e,this.operands=t,this.fieldName=r||aV}return e.prototype._setFieldName=function(t){return new e(this.operator,this.operands,t)},e}();function um(e){return e&&e instanceof uv&&e._internalType===aG}!function(e){e.AND="and",e.OR="or",e.NOT="not",e.NOR="nor"}(w||(w={}));var ug=function(){function e(e,t,r){if(this._internalType=aK,Object.defineProperties(this,{_internalType:{enumerable:!1,configurable:!1}}),this.operator=e,this.operands=t,this.fieldName=r||aV,this.fieldName!==aV){if(Array.isArray(t)){t=t.slice(),this.operands=t;for(var n=0,o=t.length;n<o;n++){var i=t[n];(ub(i)||uS(i))&&(t[n]=i._setFieldName(this.fieldName))}}else{var i=t;(ub(i)||uS(i))&&(t=i._setFieldName(this.fieldName))}}}return e.prototype._setFieldName=function(t){var r=this.operands.map(function(r){return r instanceof e?r._setFieldName(t):r});return new e(this.operator,r,t)},e.prototype.and=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=Array.isArray(arguments[0])?arguments[0]:Array.from(arguments);return n.unshift(this),new e(w.AND,n,this.fieldName)},e.prototype.or=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=Array.isArray(arguments[0])?arguments[0]:Array.from(arguments);return n.unshift(this),new e(w.OR,n,this.fieldName)},e}();function ub(e){return e&&e instanceof ug&&e._internalType===aK}var u_=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();!function(e){e.EQ="eq",e.NEQ="neq",e.GT="gt",e.GTE="gte",e.LT="lt",e.LTE="lte",e.IN="in",e.NIN="nin",e.ALL="all",e.ELEM_MATCH="elemMatch",e.EXISTS="exists",e.SIZE="size",e.MOD="mod",e.GEO_NEAR="geoNear",e.GEO_WITHIN="geoWithin",e.GEO_INTERSECTS="geoIntersects"}(S||(S={}));var uw=function(e){function t(t,r,n){var o=e.call(this,t,r,n)||this;return o.operator=t,o._internalType=aH,o}return u_(t,e),t.prototype.toJSON=function(){var e,t;switch(this.operator){case S.IN:case S.NIN:return(e={})["$"+this.operator]=this.operands,e;default:return(t={})["$"+this.operator]=this.operands[0],t}},t.prototype._setFieldName=function(e){return new t(this.operator,this.operands,e)},t.prototype.eq=function(e){var r=new t(S.EQ,[e],this.fieldName);return this.and(r)},t.prototype.neq=function(e){var r=new t(S.NEQ,[e],this.fieldName);return this.and(r)},t.prototype.gt=function(e){var r=new t(S.GT,[e],this.fieldName);return this.and(r)},t.prototype.gte=function(e){var r=new t(S.GTE,[e],this.fieldName);return this.and(r)},t.prototype.lt=function(e){var r=new t(S.LT,[e],this.fieldName);return this.and(r)},t.prototype.lte=function(e){var r=new t(S.LTE,[e],this.fieldName);return this.and(r)},t.prototype.in=function(e){var r=new t(S.IN,e,this.fieldName);return this.and(r)},t.prototype.nin=function(e){var r=new t(S.NIN,e,this.fieldName);return this.and(r)},t.prototype.geoNear=function(e){if(!(e.geometry instanceof up))throw TypeError('"geometry" must be of type Point. Received type '+typeof e.geometry);if(void 0!==e.maxDistance&&!a4(e.maxDistance))throw TypeError('"maxDistance" must be of type Number. Received type '+typeof e.maxDistance);if(void 0!==e.minDistance&&!a4(e.minDistance))throw TypeError('"minDistance" must be of type Number. Received type '+typeof e.minDistance);var r=new t(S.GEO_NEAR,[e],this.fieldName);return this.and(r)},t.prototype.geoWithin=function(e){if(!(e.geometry instanceof uu)&&!(e.geometry instanceof ur))throw TypeError('"geometry" must be of type Polygon or MultiPolygon. Received type '+typeof e.geometry);var r=new t(S.GEO_WITHIN,[e],this.fieldName);return this.and(r)},t.prototype.geoIntersects=function(e){if(!(e.geometry instanceof up)&&!(e.geometry instanceof ue)&&!(e.geometry instanceof ur)&&!(e.geometry instanceof uo)&&!(e.geometry instanceof us)&&!(e.geometry instanceof uu))throw TypeError('"geometry" must be of type Point, LineString, Polygon, MultiPoint, MultiLineString or MultiPolygon. Received type '+typeof e.geometry);var r=new t(S.GEO_INTERSECTS,[e],this.fieldName);return this.and(r)},t}(ug);function uS(e){return e&&e instanceof uw&&e._internalType===aH}var uE={};for(var uI in S)uE[uI]="$"+uI;for(var uI in w)uE[uI]="$"+uI;for(var uI in _)uE[uI]="$"+uI;function uO(e){return uE[e]||"$"+e}uE[S.NEQ]="$ne",uE[_.REMOVE]="$unset",uE[_.SHIFT]="$pop",uE[_.UNSHIFT]="$push";var uT=function(){return(uT=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},uR=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)s.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return s},uA=function(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(uR(arguments[t]));return e};function uP(e){return function e(t,r){if(a7(t))switch(t._internalType){case aJ:return t.toJSON();case a0:case a1:return t.parse();default:return t.toJSON?t.toJSON():t}else{if(a6(t))return{$date:+t};if(a8(t))return{$regex:t.source,$options:t.flags};if(a5(t))return t.map(function(t){if(r.indexOf(t)>-1)throw Error("Cannot convert circular structure to JSON");return e(t,uA(r,[t]))});if(!a3(t))return t;var n=uT({},t);for(var o in n){if(r.indexOf(n[o])>-1)throw Error("Cannot convert circular structure to JSON");n[o]=e(n[o],uA(r,[n[o]]))}return n}}(e,[e])}var ux=function(){return(ux=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},uN=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)s.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return s},uC=function(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(uN(arguments[t]));return e};function uL(e){return function e(t,r,n,o){var i=ux({},t);for(var s in t)if(!/^\$/.test(s)){var a=t[s];if(a&&a3(a)&&!r(a)){if(o.indexOf(a)>-1)throw Error("Cannot convert circular structure to JSON");var u=uC(n,[s]),c=uC(o,[a]),l=e(a,r,u,c);i[s]=l;var f=!1;for(var h in l)/^\$/.test(h)?f=!0:(i[s+"."+h]=l[h],delete i[s][h]);f||delete i[s]}}return i}(e,uU,[],[e])}function uj(e,t,r){for(var n in t[r]||delete e[r],t)e[n]?a5(e[n])?e[n].push(t[n]):a3(e[n])?a3(t[n])?Object.assign(e[n],t[n]):(console.warn("unmergable condition, query is object but condition is "+a2(t)+", can only overwrite",t,r),e[n]=t[n]):(console.warn("to-merge query is of type "+a2(e)+", can only overwrite",e,t,r),e[n]=t[n]):e[n]=t[n]}function uU(e){return a7(e)||a6(e)||a8(e)}function uD(e){return uP(e)}var uk=function(){function e(){}return e.encode=function(t){return new e().encodeUpdate(t)},e.prototype.encodeUpdate=function(e){return um(e)?this.encodeUpdateCommand(e):"object"===a2(e)?this.encodeUpdateObject(e):e},e.prototype.encodeUpdateCommand=function(e){if(e.fieldName===aV)throw Error("Cannot encode a comparison command with unset field name");switch(e.operator){case _.PUSH:case _.PULL:case _.PULL_ALL:case _.POP:case _.SHIFT:case _.UNSHIFT:case _.ADD_TO_SET:return this.encodeArrayUpdateCommand(e);default:return this.encodeFieldUpdateCommand(e)}},e.prototype.encodeFieldUpdateCommand=function(e){var t,r,n,o,i=uO(e.operator);return e.operator===_.REMOVE?((t={})[i]=((r={})[e.fieldName]="",r),t):((n={})[i]=((o={})[e.fieldName]=e.operands[0],o),n)},e.prototype.encodeArrayUpdateCommand=function(e){var t,r,n,o,i,s,a,u,c,l,f=uO(e.operator);switch(e.operator){case _.PUSH:var h=void 0;return h=a5(e.operands)?{$each:e.operands.map(uD)}:e.operands,(t={})[f]=((r={})[e.fieldName]=h,r),t;case _.UNSHIFT:var h={$each:e.operands.map(uD),$position:0};return(n={})[f]=((o={})[e.fieldName]=h,o),n;case _.POP:return(i={})[f]=((s={})[e.fieldName]=1,s),i;case _.SHIFT:return(a={})[f]=((u={})[e.fieldName]=-1,u),a;default:return(c={})[f]=((l={})[e.fieldName]=uD(e.operands),l),c}},e.prototype.encodeUpdateObject=function(e){var t=uL(e);for(var r in t)if(!/^\$/.test(r)){var n=t[r];if(um(n)){t[r]=n._setFieldName(r);var o=this.encodeUpdateCommand(t[r]);uj(t,o,r)}else{t[r]=n=uD(n);var i=new uv(_.SET,[n],r),o=this.encodeUpdateCommand(i);uj(t,o,r)}}return t},e}(),uq={};function uM(e){if(!lE.wsClientClass)throw Error("to use realtime you must import realtime module first");var t=e.config.env;return uq[t]||(uq[t]=new lE.wsClientClass({context:{appConfig:{docSizeLimit:1e3,realtimePingInterval:1e4,realtimePongWaitTimeout:5e3,request:new lE.reqClass(e.config)}}})),uq[t]}var uB=function(){return(uB=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},uW=function(){function e(e,t,r,n){var o=this;void 0===n&&(n={}),this.watch=function(e){return uM(o._db).watch(uB(uB({},e),{envId:o._db.config.env,collectionName:o._coll,query:JSON.stringify({_id:o.id})}))},this._db=e,this._coll=t,this.id=r,this.request=new lE.reqClass(this._db.config),this.projection=n}return e.prototype.create=function(e,t){t=t||uy();var r={collectionName:this._coll,data:uP(e)};return this.id&&(r._id=this.id),this.request.send("database.addDocument",r).then(function(e){e.code?t(0,e):t(0,{id:e.data._id,requestId:e.requestId})}).catch(function(e){t(e)}),t.promise},e.prototype.set=function(e,t){if(t=t||uy(),!this.id)return Promise.resolve({code:"INVALID_PARAM",message:"docId不能为空"});if(!e||"object"!=typeof e)return Promise.resolve({code:"INVALID_PARAM",message:"参数必需是非空对象"});if(e.hasOwnProperty("_id"))return Promise.resolve({code:"INVALID_PARAM",message:"不能更新_id的值"});var r=!1,n=function(e){if("object"==typeof e)for(var t in e)e[t]instanceof uv?r=!0:"object"==typeof e[t]&&n(e[t])};if(n(e),r)return Promise.resolve({code:"DATABASE_REQUEST_FAILED",message:"update operator complicit"});var o={collectionName:this._coll,queryType:b.DOC,data:uP(e),multi:!1,merge:!1,upsert:!0};return this.id&&(o.query={_id:this.id}),this.request.send("database.updateDocument",o).then(function(e){e.code?t(0,e):t(0,{updated:e.data.updated,upsertedId:e.data.upserted_id,requestId:e.requestId})}).catch(function(e){t(e)}),t.promise},e.prototype.update=function(e,t){if(t=t||uy(),!e||"object"!=typeof e)return Promise.resolve({code:"INVALID_PARAM",message:"参数必需是非空对象"});if(e.hasOwnProperty("_id"))return Promise.resolve({code:"INVALID_PARAM",message:"不能更新_id的值"});var r={_id:this.id},n={collectionName:this._coll,data:uk.encode(e),query:r,queryType:b.DOC,multi:!1,merge:!0,upsert:!1};return this.request.send("database.updateDocument",n).then(function(e){e.code?t(0,e):t(0,{updated:e.data.updated,upsertedId:e.data.upserted_id,requestId:e.requestId})}).catch(function(e){t(e)}),t.promise},e.prototype.remove=function(e){e=e||uy();var t={_id:this.id},r={collectionName:this._coll,query:t,queryType:b.DOC,multi:!1};return this.request.send("database.deleteDocument",r).then(function(t){t.code?e(0,t):e(0,{deleted:t.data.deleted,requestId:t.requestId})}).catch(function(t){e(t)}),e.promise},e.prototype.get=function(e){e=e||uy();var t={_id:this.id},r={collectionName:this._coll,query:t,queryType:b.DOC,multi:!1,projection:this.projection};return this.request.send("database.queryDocument",r).then(function(t){if(t.code)e(0,t);else{var r=uh.formatResDocumentData(t.data.list);e(0,{data:r,requestId:t.requestId})}}).catch(function(t){e(t)}),e.promise},e.prototype.field=function(t){for(var r in t)t[r]?t[r]=1:t[r]=0;return new e(this._db,this._coll,this.id,t)},e}(),uF=function(){function e(){}return e.encode=function(e){return new u$().encodeQuery(e)},e}(),u$=function(){function e(){}return e.prototype.encodeQuery=function(e,t){var r;return uU(e)?ub(e)?this.encodeLogicCommand(e):uS(e)?this.encodeQueryCommand(e):((r={})[t]=this.encodeQueryObject(e),r):a3(e)?this.encodeQueryObject(e):e},e.prototype.encodeRegExp=function(e){return{$regex:e.source,$options:e.flags}},e.prototype.encodeLogicCommand=function(e){var t,r,n,o,i,s,a,u=this;switch(e.operator){case w.NOR:case w.AND:case w.OR:var c=uO(e.operator),l=e.operands.map(function(t){return u.encodeQuery(t,e.fieldName)});return(t={})[c]=l,t;case w.NOT:var c=uO(e.operator),f=e.operands[0];if(a8(f))return(r={})[e.fieldName]=((n={})[c]=this.encodeRegExp(f),n),r;var l=this.encodeQuery(f)[e.fieldName];return(o={})[e.fieldName]=((i={})[c]=l,i),o;default:var c=uO(e.operator);if(1===e.operands.length){var h=this.encodeQuery(e.operands[0]);return(s={})[c]=h,s}var l=e.operands.map(this.encodeQuery.bind(this));return(a={})[c]=l,a}},e.prototype.encodeQueryCommand=function(e){return uS(e),this.encodeComparisonCommand(e)},e.prototype.encodeComparisonCommand=function(e){if(e.fieldName===aV)throw Error("Cannot encode a comparison command with unset field name");var t,r,n,o,i,s,a,u,c,l=uO(e.operator);switch(e.operator){case S.EQ:case S.NEQ:case S.LT:case S.LTE:case S.GT:case S.GTE:case S.ELEM_MATCH:case S.EXISTS:case S.SIZE:case S.MOD:return(t={})[e.fieldName]=((r={})[l]=uD(e.operands[0]),r),t;case S.IN:case S.NIN:case S.ALL:return(n={})[e.fieldName]=((o={})[l]=uD(e.operands),o),n;case S.GEO_NEAR:var f=e.operands[0];return(i={})[e.fieldName]={$nearSphere:{$geometry:f.geometry.toJSON(),$maxDistance:f.maxDistance,$minDistance:f.minDistance}},i;case S.GEO_WITHIN:var f=e.operands[0];return(s={})[e.fieldName]={$geoWithin:{$geometry:f.geometry.toJSON()}},s;case S.GEO_INTERSECTS:var f=e.operands[0];return(a={})[e.fieldName]={$geoIntersects:{$geometry:f.geometry.toJSON()}},a;default:return(u={})[e.fieldName]=((c={})[l]=uD(e.operands[0]),c),u}},e.prototype.encodeQueryObject=function(e){var t=uL(e);for(var r in t){var n=t[r];if(ub(n)){t[r]=n._setFieldName(r);var o=this.encodeLogicCommand(t[r]);this.mergeConditionAfterEncode(t,o,r)}else if(uS(n)){t[r]=n._setFieldName(r);var o=this.encodeComparisonCommand(t[r]);this.mergeConditionAfterEncode(t,o,r)}else uU(n)&&(t[r]=uD(n))}return t},e.prototype.mergeConditionAfterEncode=function(e,t,r){for(var n in t[r]||delete e[r],t)e[n]?a5(e[n])?e[n]=e[n].concat(t[n]):a3(e[n])?a3(t[n])?Object.assign(e,t):(console.warn("unmergable condition, query is object but condition is "+a2(t)+", can only overwrite",t,r),e[n]=t[n]):(console.warn("to-merge query is of type "+a2(e)+", can only overwrite",e,t,r),e[n]=t[n]):e[n]=t[n]},e}(),uV=function(){return(uV=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},uG=function(){function e(e,t,r,n,o){var i=this;this.watch=function(e){return uM(i._db).watch(uV(uV({},e),{envId:i._db.config.env,collectionName:i._coll,query:JSON.stringify(i._fieldFilters||{}),limit:i._queryOptions.limit,orderBy:i._fieldOrders?i._fieldOrders.reduce(function(e,t){return e[t.field]=t.direction,e},{}):void 0}))},this._db=e,this._coll=t,this._fieldFilters=r,this._fieldOrders=n||[],this._queryOptions=o||{},this._request=new lE.reqClass(this._db.config)}return e.prototype.get=function(e){e=e||uy();var t=[];this._fieldOrders&&this._fieldOrders.forEach(function(e){t.push(e)});var r={collectionName:this._coll,queryType:b.WHERE};return this._fieldFilters&&(r.query=this._fieldFilters),t.length>0&&(r.order=t),this._queryOptions.offset&&(r.offset=this._queryOptions.offset),this._queryOptions.limit?r.limit=this._queryOptions.limit<1e3?this._queryOptions.limit:1e3:r.limit=100,this._queryOptions.projection&&(r.projection=this._queryOptions.projection),this._request.send("database.queryDocument",r).then(function(t){if(t.code)e(0,t);else{var r={data:uh.formatResDocumentData(t.data.list),requestId:t.requestId};t.total&&(r.total=t.total),t.limit&&(r.limit=t.limit),t.offset&&(r.offset=t.offset),e(0,r)}}).catch(function(t){e(t)}),e.promise},e.prototype.count=function(e){e=e||uy();var t={collectionName:this._coll,queryType:b.WHERE};return this._fieldFilters&&(t.query=this._fieldFilters),this._request.send("database.countDocument",t).then(function(t){t.code?e(0,t):e(0,{requestId:t.requestId,total:t.data.total})}).catch(function(t){e(t)}),e.promise},e.prototype.where=function(t){if("Object"!==Object.prototype.toString.call(t).slice(8,-1))throw Error(m.QueryParamTypeError);var r=Object.keys(t),n=r.some(function(e){return void 0!==t[e]});if(r.length&&!n)throw Error(m.QueryParamValueError);return new e(this._db,this._coll,uF.encode(t),this._fieldOrders,this._queryOptions)},e.prototype.orderBy=function(t,r){ud.isFieldPath(t),ud.isFieldOrder(r);var n=this._fieldOrders.concat({field:t,direction:r});return new e(this._db,this._coll,this._fieldFilters,n,this._queryOptions)},e.prototype.limit=function(t){ud.isInteger("limit",t);var r=uV({},this._queryOptions);return r.limit=t,new e(this._db,this._coll,this._fieldFilters,this._fieldOrders,r)},e.prototype.skip=function(t){ud.isInteger("offset",t);var r=uV({},this._queryOptions);return r.offset=t,new e(this._db,this._coll,this._fieldFilters,this._fieldOrders,r)},e.prototype.update=function(e,t){if(t=t||uy(),!e||"object"!=typeof e)return Promise.resolve({code:"INVALID_PARAM",message:"参数必需是非空对象"});if(e.hasOwnProperty("_id"))return Promise.resolve({code:"INVALID_PARAM",message:"不能更新_id的值"});var r={collectionName:this._coll,query:this._fieldFilters,queryType:b.WHERE,multi:!0,merge:!0,upsert:!1,data:uk.encode(e)};return this._request.send("database.updateDocument",r).then(function(e){e.code?t(0,e):t(0,{requestId:e.requestId,updated:e.data.updated,upsertId:e.data.upsert_id})}).catch(function(e){t(e)}),t.promise},e.prototype.field=function(t){for(var r in t)t[r]?"object"!=typeof t[r]&&(t[r]=1):t[r]=0;var n=uV({},this._queryOptions);return n.projection=t,new e(this._db,this._coll,this._fieldFilters,this._fieldOrders,n)},e.prototype.remove=function(e){e=e||uy(),Object.keys(this._queryOptions).length>0&&console.warn("`offset`, `limit` and `projection` are not supported in remove() operation"),this._fieldOrders.length>0&&console.warn("`orderBy` is not supported in remove() operation");var t={collectionName:this._coll,query:uF.encode(this._fieldFilters),queryType:b.WHERE,multi:!0};return this._request.send("database.deleteDocument",t).then(function(t){t.code?e(0,t):e(0,{requestId:t.requestId,deleted:t.data.deleted})}).catch(function(t){e(t)}),e.promise},e}(),uH=r(78893),uK=function(e,t){return(uK=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)};function uJ(e,t){function r(){this.constructor=e}uK(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var uz=function(e){function t(r){var n=e.call(this,r)||this;return Object.setPrototypeOf(n,t.prototype),n}return uJ(t,e),Object.defineProperty(t.prototype,"name",{get:function(){return"BSONError"},enumerable:!1,configurable:!0}),t}(Error),uY=function(e){function t(r){var n=e.call(this,r)||this;return Object.setPrototypeOf(n,t.prototype),n}return uJ(t,e),Object.defineProperty(t.prototype,"name",{get:function(){return"BSONTypeError"},enumerable:!1,configurable:!0}),t}(TypeError);function uQ(e){return e&&e.Math==Math&&e}function uZ(){return uQ("object"==typeof globalThis&&globalThis)||uQ("object"==typeof window&&window)||uQ("object"==typeof self&&self)||uQ("object"==typeof global&&global)||Function("return this")()}function uX(e){return e.toString().replace("function(","function (")}var u0=function(e){console.warn("object"==typeof(t=uZ()).navigator&&"ReactNative"===t.navigator.product?"BSON: For React Native please polyfill crypto.getRandomValues, e.g. using: https://www.npmjs.com/package/react-native-get-random-values.":"BSON: No cryptographic implementation for random bytes present, falling back to a less secure implementation.");for(var t,r=uH.Buffer.alloc(e),n=0;n<e;++n)r[n]=Math.floor(256*Math.random());return r},u1=function(){var e=void 0;try{e=r(84770).randomBytes}catch(e){}return e||u0}();function u2(e){return["[object ArrayBuffer]","[object SharedArrayBuffer]"].includes(Object.prototype.toString.call(e))}function u3(e){return"[object Uint8Array]"===Object.prototype.toString.call(e)}function u4(e){return"[object RegExp]"===Object.prototype.toString.call(e)}function u5(e){return u6(e)&&"[object Date]"===Object.prototype.toString.call(e)}function u6(e){return"object"==typeof e&&null!==e}function u8(e,t){var r=!1;return function(){for(var n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];return r||(console.warn(t),r=!0),e.apply(this,n)}}function u7(e){if(ArrayBuffer.isView(e))return uH.Buffer.from(e.buffer,e.byteOffset,e.byteLength);if(u2(e))return uH.Buffer.from(e);throw new uY("Must use either Buffer or TypedArray")}var u9=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|[0-9a-f]{12}4[0-9a-f]{3}[89ab][0-9a-f]{15})$/i,ce=function(e){return"string"==typeof e&&u9.test(e)},ct=function(e){if(!ce(e))throw new uY('UUID string representations must be a 32 or 36 character hex string (dashes excluded/included). Format: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" or "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx".');var t=e.replace(/-/g,"");return uH.Buffer.from(t,"hex")},cr=function(e,t){return void 0===t&&(t=!0),t?e.toString("hex",0,4)+"-"+e.toString("hex",4,6)+"-"+e.toString("hex",6,8)+"-"+e.toString("hex",8,10)+"-"+e.toString("hex",10,16):e.toString("hex")},cn=function(){function e(t,r){if(!(this instanceof e))return new e(t,r);if(null!=t&&"string"!=typeof t&&!ArrayBuffer.isView(t)&&!(t instanceof ArrayBuffer)&&!Array.isArray(t))throw new uY("Binary can only be constructed from string, Buffer, TypedArray, or Array<number>");this.sub_type=null!=r?r:e.BSON_BINARY_SUBTYPE_DEFAULT,null==t?(this.buffer=uH.Buffer.alloc(e.BUFFER_SIZE),this.position=0):("string"==typeof t?this.buffer=uH.Buffer.from(t,"binary"):Array.isArray(t)?this.buffer=uH.Buffer.from(t):this.buffer=u7(t),this.position=this.buffer.byteLength)}return e.prototype.put=function(t){if("string"==typeof t&&1!==t.length)throw new uY("only accepts single character String");if("number"!=typeof t&&1!==t.length)throw new uY("only accepts single character Uint8Array or Array");if((r="string"==typeof t?t.charCodeAt(0):"number"==typeof t?t:t[0])<0||r>255)throw new uY("only accepts number in a valid unsigned byte range 0-255");if(this.buffer.length>this.position)this.buffer[this.position++]=r;else{var r,n=uH.Buffer.alloc(e.BUFFER_SIZE+this.buffer.length);this.buffer.copy(n,0,0,this.buffer.length),this.buffer=n,this.buffer[this.position++]=r}},e.prototype.write=function(e,t){if(t="number"==typeof t?t:this.position,this.buffer.length<t+e.length){var r=uH.Buffer.alloc(this.buffer.length+e.length);this.buffer.copy(r,0,0,this.buffer.length),this.buffer=r}ArrayBuffer.isView(e)?(this.buffer.set(u7(e),t),this.position=t+e.byteLength>this.position?t+e.length:this.position):"string"==typeof e&&(this.buffer.write(e,t,e.length,"binary"),this.position=t+e.length>this.position?t+e.length:this.position)},e.prototype.read=function(e,t){return t=t&&t>0?t:this.position,this.buffer.slice(e,e+t)},e.prototype.value=function(e){return(e=!!e)&&this.buffer.length===this.position?this.buffer:e?this.buffer.slice(0,this.position):this.buffer.toString("binary",0,this.position)},e.prototype.length=function(){return this.position},e.prototype.toJSON=function(){return this.buffer.toString("base64")},e.prototype.toString=function(e){return this.buffer.toString(e)},e.prototype.toExtendedJSON=function(e){e=e||{};var t=this.buffer.toString("base64"),r=Number(this.sub_type).toString(16);return e.legacy?{$binary:t,$type:1===r.length?"0"+r:r}:{$binary:{base64:t,subType:1===r.length?"0"+r:r}}},e.prototype.toUUID=function(){if(this.sub_type===e.SUBTYPE_UUID)return new co(this.buffer.slice(0,this.position));throw new uz('Binary sub_type "'.concat(this.sub_type,'" is not supported for converting to UUID. Only "').concat(e.SUBTYPE_UUID,'" is currently supported.'))},e.fromExtendedJSON=function(t,r){var n,o;if(r=r||{},"$binary"in t?r.legacy&&"string"==typeof t.$binary&&"$type"in t?(o=t.$type?parseInt(t.$type,16):0,n=uH.Buffer.from(t.$binary,"base64")):"string"!=typeof t.$binary&&(o=t.$binary.subType?parseInt(t.$binary.subType,16):0,n=uH.Buffer.from(t.$binary.base64,"base64")):"$uuid"in t&&(o=4,n=ct(t.$uuid)),!n)throw new uY("Unexpected Binary Extended JSON format ".concat(JSON.stringify(t)));return 4===o?new co(n):new e(n,o)},e.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},e.prototype.inspect=function(){var e=this.value(!0);return'new Binary(Buffer.from("'.concat(e.toString("hex"),'", "hex"), ').concat(this.sub_type,")")},e.BSON_BINARY_SUBTYPE_DEFAULT=0,e.BUFFER_SIZE=256,e.SUBTYPE_DEFAULT=0,e.SUBTYPE_FUNCTION=1,e.SUBTYPE_BYTE_ARRAY=2,e.SUBTYPE_UUID_OLD=3,e.SUBTYPE_UUID=4,e.SUBTYPE_MD5=5,e.SUBTYPE_ENCRYPTED=6,e.SUBTYPE_COLUMN=7,e.SUBTYPE_USER_DEFINED=128,e}();Object.defineProperty(cn.prototype,"_bsontype",{value:"Binary"});var co=function(e){function t(r){var n,o,i=this;if(null==r)n=t.generate();else if(r instanceof t)n=uH.Buffer.from(r.buffer),o=r.__id;else if(ArrayBuffer.isView(r)&&16===r.byteLength)n=u7(r);else if("string"==typeof r)n=ct(r);else throw new uY("Argument passed in UUID constructor must be a UUID, a 16 byte Buffer or a 32/36 character hex string (dashes excluded/included, format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx).");return(i=e.call(this,n,4)||this).__id=o,i}return uJ(t,e),Object.defineProperty(t.prototype,"id",{get:function(){return this.buffer},set:function(e){this.buffer=e,t.cacheHexString&&(this.__id=cr(e))},enumerable:!1,configurable:!0}),t.prototype.toHexString=function(e){if(void 0===e&&(e=!0),t.cacheHexString&&this.__id)return this.__id;var r=cr(this.id,e);return t.cacheHexString&&(this.__id=r),r},t.prototype.toString=function(e){return e?this.id.toString(e):this.toHexString()},t.prototype.toJSON=function(){return this.toHexString()},t.prototype.equals=function(e){if(!e)return!1;if(e instanceof t)return e.id.equals(this.id);try{return new t(e).id.equals(this.id)}catch(e){return!1}},t.prototype.toBinary=function(){return new cn(this.id,cn.SUBTYPE_UUID)},t.generate=function(){var e=u1(16);return e[6]=15&e[6]|64,e[8]=63&e[8]|128,uH.Buffer.from(e)},t.isValid=function(e){return!!e&&(e instanceof t||("string"==typeof e?ce(e):!!u3(e)&&16===e.length&&(240&e[6])==64&&(128&e[8])==128))},t.createFromHexString=function(e){return new t(ct(e))},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){return'new UUID("'.concat(this.toHexString(),'")')},t}(cn),ci=function(){function e(t,r){if(!(this instanceof e))return new e(t,r);this.code=t,this.scope=r}return e.prototype.toJSON=function(){return{code:this.code,scope:this.scope}},e.prototype.toExtendedJSON=function(){return this.scope?{$code:this.code,$scope:this.scope}:{$code:this.code}},e.fromExtendedJSON=function(t){return new e(t.$code,t.$scope)},e.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},e.prototype.inspect=function(){var e=this.toJSON();return'new Code("'.concat(String(e.code),'"').concat(e.scope?", ".concat(JSON.stringify(e.scope)):"",")")},e}();Object.defineProperty(ci.prototype,"_bsontype",{value:"Code"});var cs=function(){function e(t,r,n,o){if(!(this instanceof e))return new e(t,r,n,o);var i=t.split(".");2===i.length&&(n=i.shift(),t=i.shift()),this.collection=t,this.oid=r,this.db=n,this.fields=o||{}}return Object.defineProperty(e.prototype,"namespace",{get:function(){return this.collection},set:function(e){this.collection=e},enumerable:!1,configurable:!0}),e.prototype.toJSON=function(){var e=Object.assign({$ref:this.collection,$id:this.oid},this.fields);return null!=this.db&&(e.$db=this.db),e},e.prototype.toExtendedJSON=function(e){e=e||{};var t={$ref:this.collection,$id:this.oid};return e.legacy?t:(this.db&&(t.$db=this.db),t=Object.assign(t,this.fields))},e.fromExtendedJSON=function(t){var r=Object.assign({},t);return delete r.$ref,delete r.$id,delete r.$db,new e(t.$ref,t.$id,t.$db,r)},e.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},e.prototype.inspect=function(){var e=void 0===this.oid||void 0===this.oid.toString?this.oid:this.oid.toString();return'new DBRef("'.concat(this.namespace,'", new ObjectId("').concat(String(e),'")').concat(this.db?', "'.concat(this.db,'"'):"",")")},e}();Object.defineProperty(cs.prototype,"_bsontype",{value:"DBRef"});var ca=void 0;try{ca=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(e){}var cu={},cc={},cl=function(){function e(t,r,n){if(void 0===t&&(t=0),!(this instanceof e))return new e(t,r,n);"bigint"==typeof t?Object.assign(this,e.fromBigInt(t,!!r)):"string"==typeof t?Object.assign(this,e.fromString(t,!!r)):(this.low=0|t,this.high=0|r,this.unsigned=!!n),Object.defineProperty(this,"__isLong__",{value:!0,configurable:!1,writable:!1,enumerable:!1})}return e.fromBits=function(t,r,n){return new e(t,r,n)},e.fromInt=function(t,r){var n,o,i;return r?(t>>>=0,(i=0<=t&&t<256)&&(o=cc[t]))?o:(n=e.fromBits(t,(0|t)<0?-1:0,!0),i&&(cc[t]=n),n):(t|=0,(i=-128<=t&&t<128)&&(o=cu[t]))?o:(n=e.fromBits(t,t<0?-1:0,!1),i&&(cu[t]=n),n)},e.fromNumber=function(t,r){if(isNaN(t))return r?e.UZERO:e.ZERO;if(r){if(t<0)return e.UZERO;if(t>=18446744073709552e3)return e.MAX_UNSIGNED_VALUE}else{if(t<=-0x8000000000000000)return e.MIN_VALUE;if(t+1>=0x7fffffffffffffff)return e.MAX_VALUE}return t<0?e.fromNumber(-t,r).neg():e.fromBits(t%4294967296|0,t/4294967296|0,r)},e.fromBigInt=function(t,r){return e.fromString(t.toString(),r)},e.fromString=function(t,r,n){if(0===t.length)throw Error("empty string");if("NaN"===t||"Infinity"===t||"+Infinity"===t||"-Infinity"===t)return e.ZERO;if("number"==typeof r?(n=r,r=!1):r=!!r,(n=n||10)<2||36<n)throw RangeError("radix");if((o=t.indexOf("-"))>0)throw Error("interior hyphen");if(0===o)return e.fromString(t.substring(1),r,n).neg();for(var o,i=e.fromNumber(Math.pow(n,8)),s=e.ZERO,a=0;a<t.length;a+=8){var u=Math.min(8,t.length-a),c=parseInt(t.substring(a,a+u),n);if(u<8){var l=e.fromNumber(Math.pow(n,u));s=s.mul(l).add(e.fromNumber(c))}else s=(s=s.mul(i)).add(e.fromNumber(c))}return s.unsigned=r,s},e.fromBytes=function(t,r,n){return n?e.fromBytesLE(t,r):e.fromBytesBE(t,r)},e.fromBytesLE=function(t,r){return new e(t[0]|t[1]<<8|t[2]<<16|t[3]<<24,t[4]|t[5]<<8|t[6]<<16|t[7]<<24,r)},e.fromBytesBE=function(t,r){return new e(t[4]<<24|t[5]<<16|t[6]<<8|t[7],t[0]<<24|t[1]<<16|t[2]<<8|t[3],r)},e.isLong=function(e){return u6(e)&&!0===e.__isLong__},e.fromValue=function(t,r){return"number"==typeof t?e.fromNumber(t,r):"string"==typeof t?e.fromString(t,r):e.fromBits(t.low,t.high,"boolean"==typeof r?r:t.unsigned)},e.prototype.add=function(t){e.isLong(t)||(t=e.fromValue(t));var r,n,o=this.high>>>16,i=65535&this.high,s=this.low>>>16,a=65535&this.low,u=t.high>>>16,c=65535&t.high,l=t.low>>>16,f=65535&t.low,h=0,d=0;return r=0+((n=0+(a+f))>>>16),n&=65535,r+=s+l,d+=r>>>16,r&=65535,d+=i+c,h+=d>>>16,d&=65535,h+=o+u,h&=65535,e.fromBits(r<<16|n,h<<16|d,this.unsigned)},e.prototype.and=function(t){return e.isLong(t)||(t=e.fromValue(t)),e.fromBits(this.low&t.low,this.high&t.high,this.unsigned)},e.prototype.compare=function(t){if(e.isLong(t)||(t=e.fromValue(t)),this.eq(t))return 0;var r=this.isNegative(),n=t.isNegative();return r&&!n?-1:!r&&n?1:this.unsigned?t.high>>>0>this.high>>>0||t.high===this.high&&t.low>>>0>this.low>>>0?-1:1:this.sub(t).isNegative()?-1:1},e.prototype.comp=function(e){return this.compare(e)},e.prototype.divide=function(t){if(e.isLong(t)||(t=e.fromValue(t)),t.isZero())throw Error("division by zero");if(ca){if(!this.unsigned&&-2147483648===this.high&&-1===t.low&&-1===t.high)return this;var r,n,o,i=(this.unsigned?ca.div_u:ca.div_s)(this.low,this.high,t.low,t.high);return e.fromBits(i,ca.get_high(),this.unsigned)}if(this.isZero())return this.unsigned?e.UZERO:e.ZERO;if(this.unsigned){if(t.unsigned||(t=t.toUnsigned()),t.gt(this))return e.UZERO;if(t.gt(this.shru(1)))return e.UONE;o=e.UZERO}else{if(this.eq(e.MIN_VALUE))return t.eq(e.ONE)||t.eq(e.NEG_ONE)?e.MIN_VALUE:t.eq(e.MIN_VALUE)?e.ONE:(r=this.shr(1).div(t).shl(1)).eq(e.ZERO)?t.isNegative()?e.ONE:e.NEG_ONE:(n=this.sub(t.mul(r)),o=r.add(n.div(t)));if(t.eq(e.MIN_VALUE))return this.unsigned?e.UZERO:e.ZERO;if(this.isNegative())return t.isNegative()?this.neg().div(t.neg()):this.neg().div(t).neg();if(t.isNegative())return this.div(t.neg()).neg();o=e.ZERO}for(n=this;n.gte(t);){for(var s=Math.ceil(Math.log(r=Math.max(1,Math.floor(n.toNumber()/t.toNumber())))/Math.LN2),a=s<=48?1:Math.pow(2,s-48),u=e.fromNumber(r),c=u.mul(t);c.isNegative()||c.gt(n);)r-=a,c=(u=e.fromNumber(r,this.unsigned)).mul(t);u.isZero()&&(u=e.ONE),o=o.add(u),n=n.sub(c)}return o},e.prototype.div=function(e){return this.divide(e)},e.prototype.equals=function(t){return e.isLong(t)||(t=e.fromValue(t)),(this.unsigned===t.unsigned||this.high>>>31!=1||t.high>>>31!=1)&&this.high===t.high&&this.low===t.low},e.prototype.eq=function(e){return this.equals(e)},e.prototype.getHighBits=function(){return this.high},e.prototype.getHighBitsUnsigned=function(){return this.high>>>0},e.prototype.getLowBits=function(){return this.low},e.prototype.getLowBitsUnsigned=function(){return this.low>>>0},e.prototype.getNumBitsAbs=function(){if(this.isNegative())return this.eq(e.MIN_VALUE)?64:this.neg().getNumBitsAbs();var t,r=0!==this.high?this.high:this.low;for(t=31;t>0&&(r&1<<t)==0;t--);return 0!==this.high?t+33:t+1},e.prototype.greaterThan=function(e){return this.comp(e)>0},e.prototype.gt=function(e){return this.greaterThan(e)},e.prototype.greaterThanOrEqual=function(e){return this.comp(e)>=0},e.prototype.gte=function(e){return this.greaterThanOrEqual(e)},e.prototype.ge=function(e){return this.greaterThanOrEqual(e)},e.prototype.isEven=function(){return(1&this.low)==0},e.prototype.isNegative=function(){return!this.unsigned&&this.high<0},e.prototype.isOdd=function(){return(1&this.low)==1},e.prototype.isPositive=function(){return this.unsigned||this.high>=0},e.prototype.isZero=function(){return 0===this.high&&0===this.low},e.prototype.lessThan=function(e){return 0>this.comp(e)},e.prototype.lt=function(e){return this.lessThan(e)},e.prototype.lessThanOrEqual=function(e){return 0>=this.comp(e)},e.prototype.lte=function(e){return this.lessThanOrEqual(e)},e.prototype.modulo=function(t){if(e.isLong(t)||(t=e.fromValue(t)),ca){var r=(this.unsigned?ca.rem_u:ca.rem_s)(this.low,this.high,t.low,t.high);return e.fromBits(r,ca.get_high(),this.unsigned)}return this.sub(this.div(t).mul(t))},e.prototype.mod=function(e){return this.modulo(e)},e.prototype.rem=function(e){return this.modulo(e)},e.prototype.multiply=function(t){if(this.isZero())return e.ZERO;if(e.isLong(t)||(t=e.fromValue(t)),ca){var r=ca.mul(this.low,this.high,t.low,t.high);return e.fromBits(r,ca.get_high(),this.unsigned)}if(t.isZero())return e.ZERO;if(this.eq(e.MIN_VALUE))return t.isOdd()?e.MIN_VALUE:e.ZERO;if(t.eq(e.MIN_VALUE))return this.isOdd()?e.MIN_VALUE:e.ZERO;if(this.isNegative())return t.isNegative()?this.neg().mul(t.neg()):this.neg().mul(t).neg();if(t.isNegative())return this.mul(t.neg()).neg();if(this.lt(e.TWO_PWR_24)&&t.lt(e.TWO_PWR_24))return e.fromNumber(this.toNumber()*t.toNumber(),this.unsigned);var n,o,i=this.high>>>16,s=65535&this.high,a=this.low>>>16,u=65535&this.low,c=t.high>>>16,l=65535&t.high,f=t.low>>>16,h=65535&t.low,d=0,p=0;return n=0+((o=0+u*h)>>>16),o&=65535,n+=a*h,p+=n>>>16,n&=65535,n+=u*f,p+=n>>>16,n&=65535,p+=s*h,d+=p>>>16,p&=65535,p+=a*f,d+=p>>>16,p&=65535,p+=u*l,d+=p>>>16,p&=65535,d+=i*h+s*f+a*l+u*c,d&=65535,e.fromBits(n<<16|o,d<<16|p,this.unsigned)},e.prototype.mul=function(e){return this.multiply(e)},e.prototype.negate=function(){return!this.unsigned&&this.eq(e.MIN_VALUE)?e.MIN_VALUE:this.not().add(e.ONE)},e.prototype.neg=function(){return this.negate()},e.prototype.not=function(){return e.fromBits(~this.low,~this.high,this.unsigned)},e.prototype.notEquals=function(e){return!this.equals(e)},e.prototype.neq=function(e){return this.notEquals(e)},e.prototype.ne=function(e){return this.notEquals(e)},e.prototype.or=function(t){return e.isLong(t)||(t=e.fromValue(t)),e.fromBits(this.low|t.low,this.high|t.high,this.unsigned)},e.prototype.shiftLeft=function(t){return(e.isLong(t)&&(t=t.toInt()),0==(t&=63))?this:t<32?e.fromBits(this.low<<t,this.high<<t|this.low>>>32-t,this.unsigned):e.fromBits(0,this.low<<t-32,this.unsigned)},e.prototype.shl=function(e){return this.shiftLeft(e)},e.prototype.shiftRight=function(t){return(e.isLong(t)&&(t=t.toInt()),0==(t&=63))?this:t<32?e.fromBits(this.low>>>t|this.high<<32-t,this.high>>t,this.unsigned):e.fromBits(this.high>>t-32,this.high>=0?0:-1,this.unsigned)},e.prototype.shr=function(e){return this.shiftRight(e)},e.prototype.shiftRightUnsigned=function(t){if(e.isLong(t)&&(t=t.toInt()),0==(t&=63))return this;var r=this.high;if(t<32){var n=this.low;return e.fromBits(n>>>t|r<<32-t,r>>>t,this.unsigned)}return 32===t?e.fromBits(r,0,this.unsigned):e.fromBits(r>>>t-32,0,this.unsigned)},e.prototype.shr_u=function(e){return this.shiftRightUnsigned(e)},e.prototype.shru=function(e){return this.shiftRightUnsigned(e)},e.prototype.subtract=function(t){return e.isLong(t)||(t=e.fromValue(t)),this.add(t.neg())},e.prototype.sub=function(e){return this.subtract(e)},e.prototype.toInt=function(){return this.unsigned?this.low>>>0:this.low},e.prototype.toNumber=function(){return this.unsigned?(this.high>>>0)*4294967296+(this.low>>>0):4294967296*this.high+(this.low>>>0)},e.prototype.toBigInt=function(){return BigInt(this.toString())},e.prototype.toBytes=function(e){return e?this.toBytesLE():this.toBytesBE()},e.prototype.toBytesLE=function(){var e=this.high,t=this.low;return[255&t,t>>>8&255,t>>>16&255,t>>>24,255&e,e>>>8&255,e>>>16&255,e>>>24]},e.prototype.toBytesBE=function(){var e=this.high,t=this.low;return[e>>>24,e>>>16&255,e>>>8&255,255&e,t>>>24,t>>>16&255,t>>>8&255,255&t]},e.prototype.toSigned=function(){return this.unsigned?e.fromBits(this.low,this.high,!1):this},e.prototype.toString=function(t){if((t=t||10)<2||36<t)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(!this.eq(e.MIN_VALUE))return"-"+this.neg().toString(t);var r=e.fromNumber(t),n=this.div(r),o=n.mul(r).sub(this);return n.toString(t)+o.toInt().toString(t)}for(var i=e.fromNumber(Math.pow(t,6),this.unsigned),s=this,a="";;){var u=s.div(i),c=(s.sub(u.mul(i)).toInt()>>>0).toString(t);if((s=u).isZero())return c+a;for(;c.length<6;)c="0"+c;a=""+c+a}},e.prototype.toUnsigned=function(){return this.unsigned?this:e.fromBits(this.low,this.high,!0)},e.prototype.xor=function(t){return e.isLong(t)||(t=e.fromValue(t)),e.fromBits(this.low^t.low,this.high^t.high,this.unsigned)},e.prototype.eqz=function(){return this.isZero()},e.prototype.le=function(e){return this.lessThanOrEqual(e)},e.prototype.toExtendedJSON=function(e){return e&&e.relaxed?this.toNumber():{$numberLong:this.toString()}},e.fromExtendedJSON=function(t,r){var n=e.fromString(t.$numberLong);return r&&r.relaxed?n.toNumber():n},e.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},e.prototype.inspect=function(){return'new Long("'.concat(this.toString(),'"').concat(this.unsigned?", true":"",")")},e.TWO_PWR_24=e.fromInt(16777216),e.MAX_UNSIGNED_VALUE=e.fromBits(-1,-1,!0),e.ZERO=e.fromInt(0),e.UZERO=e.fromInt(0,!0),e.ONE=e.fromInt(1),e.UONE=e.fromInt(1,!0),e.NEG_ONE=e.fromInt(-1),e.MAX_VALUE=e.fromBits(-1,2147483647,!1),e.MIN_VALUE=e.fromBits(0,-2147483648,!1),e}();Object.defineProperty(cl.prototype,"__isLong__",{value:!0}),Object.defineProperty(cl.prototype,"_bsontype",{value:"Long"});var cf=/^(\+|-)?(\d+|(\d*\.\d*))?(E|e)?([-+])?(\d+)?$/,ch=/^(\+|-)?(Infinity|inf)$/i,cd=/^(\+|-)?NaN$/i,cp=[124,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].reverse(),cy=[248,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].reverse(),cv=[120,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].reverse(),cm=/^([-+])?(\d+)?$/;function cg(e){return!isNaN(parseInt(e,10))}function c_(e,t){throw new uY('"'.concat(e,'" is not a valid Decimal128 string - ').concat(t))}var cw=function(){function e(t){if(!(this instanceof e))return new e(t);if("string"==typeof t)this.bytes=e.fromString(t).bytes;else if(u3(t)){if(16!==t.byteLength)throw new uY("Decimal128 must take a Buffer of 16 bytes");this.bytes=t}else throw new uY("Decimal128 must take a Buffer or string")}return e.fromString=function(t){var r,n,o,i,s=!1,a=!1,u=!1,c=0,l=0,f=0,h=0,d=0,p=[0],y=0,v=0,m=0,g=0,b=0,_=0,w=new cl(0,0),S=new cl(0,0),E=0,I=0;if(t.length>=7e3)throw new uY(""+t+" not a valid Decimal128 string");var O=t.match(cf),T=t.match(ch),R=t.match(cd);if(!O&&!T&&!R||0===t.length)throw new uY(""+t+" not a valid Decimal128 string");if(O){var A=O[2],P=O[4],x=O[5],N=O[6];P&&void 0===N&&c_(t,"missing exponent power"),P&&void 0===A&&c_(t,"missing exponent base"),void 0===P&&(x||N)&&c_(t,"missing e before exponent")}if(("+"===t[I]||"-"===t[I])&&(s="-"===t[I++]),!cg(t[I])&&"."!==t[I]){if("i"===t[I]||"I"===t[I])return new e(uH.Buffer.from(s?cy:cv));if("N"===t[I])return new e(uH.Buffer.from(cp))}for(;cg(t[I])||"."===t[I];){if("."===t[I]){a&&c_(t,"contains multiple periods"),a=!0,I+=1;continue}y<34&&("0"!==t[I]||u)&&(u||(d=l),u=!0,p[v++]=parseInt(t[I],10),y+=1),u&&(f+=1),a&&(h+=1),l+=1,I+=1}if(a&&!l)throw new uY(""+t+" not a valid Decimal128 string");if("e"===t[I]||"E"===t[I]){var C=t.substr(++I).match(cm);if(!C||!C[2])return new e(uH.Buffer.from(cp));b=parseInt(C[0],10),I+=C[0].length}if(t[I])return new e(uH.Buffer.from(cp));if(m=0,y){if(g=y-1,1!==(c=f))for(;0===p[d+c-1];)c-=1}else m=0,g=0,p[0]=0,f=1,y=1,c=0;for(b<=h&&h-b>16384?b=-6176:b-=h;b>6111;){if((g+=1)-m>34){var L=p.join("");if(L.match(/^0+$/)){b=6111;break}c_(t,"overflow")}b-=1}for(;b<-6176||y<f;){if(0===g&&c<y){b=-6176,c=0;break}if(y<f?f-=1:g-=1,b<6111)b+=1;else{var L=p.join("");if(L.match(/^0+$/)){b=6111;break}c_(t,"overflow")}}if(g-m+1<c){var j=l;a&&(d+=1,j+=1),s&&(d+=1,j+=1);var U=parseInt(t[d+g+1],10),D=0;if(U>=5&&(D=1,5===U)){for(D=p[g]%2==1?1:0,_=d+g+2;_<j;_++)if(parseInt(t[_],10)){D=1;break}}if(D){for(var k=g;k>=0;k--)if(++p[k]>9&&(p[k]=0,0===k)){if(!(b<6111))return new e(uH.Buffer.from(s?cy:cv));b+=1,p[k]=1}}}if(w=cl.fromNumber(0),S=cl.fromNumber(0),0===c)w=cl.fromNumber(0),S=cl.fromNumber(0);else if(g-m<17){var k=m;for(S=cl.fromNumber(p[k++]),w=new cl(0,0);k<=g;k++)S=(S=S.multiply(cl.fromNumber(10))).add(cl.fromNumber(p[k]))}else{var k=m;for(w=cl.fromNumber(p[k++]);k<=g-17;k++)w=(w=w.multiply(cl.fromNumber(10))).add(cl.fromNumber(p[k]));for(S=cl.fromNumber(p[k++]);k<=g;k++)S=(S=S.multiply(cl.fromNumber(10))).add(cl.fromNumber(p[k]))}var q=function(e,t){if(!e&&!t)return{high:cl.fromNumber(0),low:cl.fromNumber(0)};var r=e.shiftRightUnsigned(32),n=new cl(e.getLowBits(),0),o=t.shiftRightUnsigned(32),i=new cl(t.getLowBits(),0),s=r.multiply(o),a=r.multiply(i),u=n.multiply(o),c=n.multiply(i);return s=s.add(a.shiftRightUnsigned(32)),a=new cl(a.getLowBits(),0).add(u).add(c.shiftRightUnsigned(32)),{high:s=s.add(a.shiftRightUnsigned(32)),low:c=a.shiftLeft(32).add(new cl(c.getLowBits(),0))}}(w,cl.fromString("100000000000000000"));q.low=q.low.add(S),r=q.low,n=S,((o=r.high>>>0)<(i=n.high>>>0)||o===i&&r.low>>>0<n.low>>>0)&&(q.high=q.high.add(cl.fromNumber(1))),E=b+6176;var M={low:cl.fromNumber(0),high:cl.fromNumber(0)};q.high.shiftRightUnsigned(49).and(cl.fromNumber(1)).equals(cl.fromNumber(1))?(M.high=M.high.or(cl.fromNumber(3).shiftLeft(61)),M.high=M.high.or(cl.fromNumber(E).and(cl.fromNumber(16383).shiftLeft(47))),M.high=M.high.or(q.high.and(cl.fromNumber(0x7fffffffffff)))):(M.high=M.high.or(cl.fromNumber(16383&E).shiftLeft(49)),M.high=M.high.or(q.high.and(cl.fromNumber(562949953421311)))),M.low=q.low,s&&(M.high=M.high.or(cl.fromString("9223372036854775808")));var B=uH.Buffer.alloc(16);return I=0,B[I++]=255&M.low.low,B[I++]=M.low.low>>8&255,B[I++]=M.low.low>>16&255,B[I++]=M.low.low>>24&255,B[I++]=255&M.low.high,B[I++]=M.low.high>>8&255,B[I++]=M.low.high>>16&255,B[I++]=M.low.high>>24&255,B[I++]=255&M.high.low,B[I++]=M.high.low>>8&255,B[I++]=M.high.low>>16&255,B[I++]=M.high.low>>24&255,B[I++]=255&M.high.high,B[I++]=M.high.high>>8&255,B[I++]=M.high.high>>16&255,B[I++]=M.high.high>>24&255,new e(B)},e.prototype.toString=function(){for(var e,t,r,n,o=0,i=Array(36),s=0;s<i.length;s++)i[s]=0;var a=0,u=!1,c={parts:[0,0,0,0]},l=[];a=0;var f=this.bytes,h=f[a++]|f[a++]<<8|f[a++]<<16|f[a++]<<24,d=f[a++]|f[a++]<<8|f[a++]<<16|f[a++]<<24,p=f[a++]|f[a++]<<8|f[a++]<<16|f[a++]<<24,y=f[a++]|f[a++]<<8|f[a++]<<16|f[a++]<<24;a=0,({low:new cl(h,d),high:new cl(p,y)}).high.lessThan(cl.ZERO)&&l.push("-");var v=y>>26&31;if(v>>3==3){if(30===v)return l.join("")+"Infinity";if(31===v)return"NaN";e=y>>15&16383,t=8+(y>>14&1)}else t=y>>14&7,e=y>>17&16383;var m=e-6176;if(c.parts[0]=(16383&y)+((15&t)<<14),c.parts[1]=p,c.parts[2]=d,c.parts[3]=h,0===c.parts[0]&&0===c.parts[1]&&0===c.parts[2]&&0===c.parts[3])u=!0;else for(n=3;n>=0;n--){var g=0,b=function(e){var t=cl.fromNumber(1e9),r=cl.fromNumber(0);if(!e.parts[0]&&!e.parts[1]&&!e.parts[2]&&!e.parts[3])return{quotient:e,rem:r};for(var n=0;n<=3;n++)r=(r=r.shiftLeft(32)).add(new cl(e.parts[n],0)),e.parts[n]=r.div(t).low,r=r.modulo(t);return{quotient:e,rem:r}}(c);if(c=b.quotient,g=b.rem.low)for(r=8;r>=0;r--)i[9*n+r]=g%10,g=Math.floor(g/10)}if(u)o=1,i[a]=0;else for(o=36;!i[a];)o-=1,a+=1;var _=o-1+m;if(_>=34||_<=-7||m>0){if(o>34)return l.push("".concat(0)),m>0?l.push("E+".concat(m)):m<0&&l.push("E".concat(m)),l.join("");l.push("".concat(i[a++])),(o-=1)&&l.push(".");for(var s=0;s<o;s++)l.push("".concat(i[a++]));l.push("E"),_>0?l.push("+".concat(_)):l.push("".concat(_))}else if(m>=0)for(var s=0;s<o;s++)l.push("".concat(i[a++]));else{var w=o+m;if(w>0)for(var s=0;s<w;s++)l.push("".concat(i[a++]));else l.push("0");for(l.push(".");w++<0;)l.push("0");for(var s=0;s<o-Math.max(w-1,0);s++)l.push("".concat(i[a++]))}return l.join("")},e.prototype.toJSON=function(){return{$numberDecimal:this.toString()}},e.prototype.toExtendedJSON=function(){return{$numberDecimal:this.toString()}},e.fromExtendedJSON=function(t){return e.fromString(t.$numberDecimal)},e.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},e.prototype.inspect=function(){return'new Decimal128("'.concat(this.toString(),'")')},e}();Object.defineProperty(cw.prototype,"_bsontype",{value:"Decimal128"});var cS=function(){function e(t){if(!(this instanceof e))return new e(t);t instanceof Number&&(t=t.valueOf()),this.value=+t}return e.prototype.valueOf=function(){return this.value},e.prototype.toJSON=function(){return this.value},e.prototype.toString=function(e){return this.value.toString(e)},e.prototype.toExtendedJSON=function(e){return e&&(e.legacy||e.relaxed&&isFinite(this.value))?this.value:Object.is(Math.sign(this.value),-0)?{$numberDouble:"-".concat(this.value.toFixed(1))}:{$numberDouble:Number.isInteger(this.value)?this.value.toFixed(1):this.value.toString()}},e.fromExtendedJSON=function(t,r){var n=parseFloat(t.$numberDouble);return r&&r.relaxed?n:new e(n)},e.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},e.prototype.inspect=function(){var e=this.toExtendedJSON();return"new Double(".concat(e.$numberDouble,")")},e}();Object.defineProperty(cS.prototype,"_bsontype",{value:"Double"});var cE=function(){function e(t){if(!(this instanceof e))return new e(t);t instanceof Number&&(t=t.valueOf()),this.value=0|+t}return e.prototype.valueOf=function(){return this.value},e.prototype.toString=function(e){return this.value.toString(e)},e.prototype.toJSON=function(){return this.value},e.prototype.toExtendedJSON=function(e){return e&&(e.relaxed||e.legacy)?this.value:{$numberInt:this.value.toString()}},e.fromExtendedJSON=function(t,r){return r&&r.relaxed?parseInt(t.$numberInt,10):new e(t.$numberInt)},e.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},e.prototype.inspect=function(){return"new Int32(".concat(this.valueOf(),")")},e}();Object.defineProperty(cE.prototype,"_bsontype",{value:"Int32"});var cI=function(){function e(){if(!(this instanceof e))return new e}return e.prototype.toExtendedJSON=function(){return{$maxKey:1}},e.fromExtendedJSON=function(){return new e},e.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},e.prototype.inspect=function(){return"new MaxKey()"},e}();Object.defineProperty(cI.prototype,"_bsontype",{value:"MaxKey"});var cO=function(){function e(){if(!(this instanceof e))return new e}return e.prototype.toExtendedJSON=function(){return{$minKey:1}},e.fromExtendedJSON=function(){return new e},e.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},e.prototype.inspect=function(){return"new MinKey()"},e}();Object.defineProperty(cO.prototype,"_bsontype",{value:"MinKey"});var cT=RegExp("^[0-9a-fA-F]{24}$"),cR=null,cA=Symbol("id"),cP=function(){function e(t){if(!(this instanceof e))return new e(t);if("object"==typeof t&&t&&"id"in t){if("string"!=typeof t.id&&!ArrayBuffer.isView(t.id))throw new uY("Argument passed in must have an id that is of type string or Buffer");r="toHexString"in t&&"function"==typeof t.toHexString?uH.Buffer.from(t.toHexString(),"hex"):t.id}else r=t;if(null==r||"number"==typeof r)this[cA]=e.generate("number"==typeof r?r:void 0);else if(ArrayBuffer.isView(r)&&12===r.byteLength)this[cA]=r instanceof uH.Buffer?r:u7(r);else if("string"==typeof r){if(12===r.length){var r,n=uH.Buffer.from(r);if(12===n.byteLength)this[cA]=n;else throw new uY("Argument passed in must be a string of 12 bytes")}else if(24===r.length&&cT.test(r))this[cA]=uH.Buffer.from(r,"hex");else throw new uY("Argument passed in must be a string of 12 bytes or a string of 24 hex characters or an integer")}else throw new uY("Argument passed in does not match the accepted types");e.cacheHexString&&(this.__id=this.id.toString("hex"))}return Object.defineProperty(e.prototype,"id",{get:function(){return this[cA]},set:function(t){this[cA]=t,e.cacheHexString&&(this.__id=t.toString("hex"))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"generationTime",{get:function(){return this.id.readInt32BE(0)},set:function(e){this.id.writeUInt32BE(e,0)},enumerable:!1,configurable:!0}),e.prototype.toHexString=function(){if(e.cacheHexString&&this.__id)return this.__id;var t=this.id.toString("hex");return e.cacheHexString&&!this.__id&&(this.__id=t),t},e.getInc=function(){return e.index=(e.index+1)%16777215},e.generate=function(t){"number"!=typeof t&&(t=Math.floor(Date.now()/1e3));var r=e.getInc(),n=uH.Buffer.alloc(12);return n.writeUInt32BE(t,0),null===cR&&(cR=u1(5)),n[4]=cR[0],n[5]=cR[1],n[6]=cR[2],n[7]=cR[3],n[8]=cR[4],n[11]=255&r,n[10]=r>>8&255,n[9]=r>>16&255,n},e.prototype.toString=function(e){return e?this.id.toString(e):this.toHexString()},e.prototype.toJSON=function(){return this.toHexString()},e.prototype.equals=function(t){if(null==t)return!1;if(t instanceof e)return this[cA][11]===t[cA][11]&&this[cA].equals(t[cA]);if("string"==typeof t&&e.isValid(t)&&12===t.length&&u3(this.id))return t===uH.Buffer.prototype.toString.call(this.id,"latin1");if("string"==typeof t&&e.isValid(t)&&24===t.length)return t.toLowerCase()===this.toHexString();if("string"==typeof t&&e.isValid(t)&&12===t.length)return uH.Buffer.from(t).equals(this.id);if("object"==typeof t&&"toHexString"in t&&"function"==typeof t.toHexString){var r=t.toHexString(),n=this.toHexString().toLowerCase();return"string"==typeof r&&r.toLowerCase()===n}return!1},e.prototype.getTimestamp=function(){var e=new Date,t=this.id.readUInt32BE(0);return e.setTime(1e3*Math.floor(t)),e},e.createPk=function(){return new e},e.createFromTime=function(t){var r=uH.Buffer.from([0,0,0,0,0,0,0,0,0,0,0,0]);return r.writeUInt32BE(t,0),new e(r)},e.createFromHexString=function(t){if(void 0===t||null!=t&&24!==t.length)throw new uY("Argument passed in must be a single String of 12 bytes or a string of 24 hex characters");return new e(uH.Buffer.from(t,"hex"))},e.isValid=function(t){if(null==t)return!1;try{return new e(t),!0}catch(e){return!1}},e.prototype.toExtendedJSON=function(){return this.toHexString?{$oid:this.toHexString()}:{$oid:this.toString("hex")}},e.fromExtendedJSON=function(t){return new e(t.$oid)},e.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},e.prototype.inspect=function(){return'new ObjectId("'.concat(this.toHexString(),'")')},e.index=Math.floor(16777215*Math.random()),e}();Object.defineProperty(cP.prototype,"generate",{value:u8(function(e){return cP.generate(e)},"Please use the static `ObjectId.generate(time)` instead")}),Object.defineProperty(cP.prototype,"getInc",{value:u8(function(){return cP.getInc()},"Please use the static `ObjectId.getInc()` instead")}),Object.defineProperty(cP.prototype,"get_inc",{value:u8(function(){return cP.getInc()},"Please use the static `ObjectId.getInc()` instead")}),Object.defineProperty(cP,"get_inc",{value:u8(function(){return cP.getInc()},"Please use the static `ObjectId.getInc()` instead")}),Object.defineProperty(cP.prototype,"_bsontype",{value:"ObjectID"});var cx=function(){function e(t,r){if(!(this instanceof e))return new e(t,r);if(this.pattern=t,this.options=(null!=r?r:"").split("").sort().join(""),-1!==this.pattern.indexOf("\0"))throw new uz("BSON Regex patterns cannot contain null bytes, found: ".concat(JSON.stringify(this.pattern)));if(-1!==this.options.indexOf("\0"))throw new uz("BSON Regex options cannot contain null bytes, found: ".concat(JSON.stringify(this.options)));for(var n=0;n<this.options.length;n++)if(!("i"===this.options[n]||"m"===this.options[n]||"x"===this.options[n]||"l"===this.options[n]||"s"===this.options[n]||"u"===this.options[n]))throw new uz("The regular expression option [".concat(this.options[n],"] is not supported"))}return e.parseOptions=function(e){return e?e.split("").sort().join(""):""},e.prototype.toExtendedJSON=function(e){return(e=e||{}).legacy?{$regex:this.pattern,$options:this.options}:{$regularExpression:{pattern:this.pattern,options:this.options}}},e.fromExtendedJSON=function(t){if("$regex"in t){if("string"==typeof t.$regex)return new e(t.$regex,e.parseOptions(t.$options));if("BSONRegExp"===t.$regex._bsontype)return t}if("$regularExpression"in t)return new e(t.$regularExpression.pattern,e.parseOptions(t.$regularExpression.options));throw new uY("Unexpected BSONRegExp EJSON object form: ".concat(JSON.stringify(t)))},e}();Object.defineProperty(cx.prototype,"_bsontype",{value:"BSONRegExp"});var cN=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.valueOf=function(){return this.value},e.prototype.toString=function(){return this.value},e.prototype.inspect=function(){return'new BSONSymbol("'.concat(this.value,'")')},e.prototype.toJSON=function(){return this.value},e.prototype.toExtendedJSON=function(){return{$symbol:this.value}},e.fromExtendedJSON=function(t){return new e(t.$symbol)},e.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},e}();Object.defineProperty(cN.prototype,"_bsontype",{value:"Symbol"});var cC=function(e){function t(r,n){var o=this;return o instanceof t?(Object.defineProperty(o=cl.isLong(r)?e.call(this,r.low,r.high,!0)||this:u6(r)&&void 0!==r.t&&void 0!==r.i?e.call(this,r.i,r.t,!0)||this:e.call(this,r,n,!0)||this,"_bsontype",{value:"Timestamp",writable:!1,configurable:!1,enumerable:!1}),o):new t(r,n)}return uJ(t,e),t.prototype.toJSON=function(){return{$timestamp:this.toString()}},t.fromInt=function(e){return new t(cl.fromInt(e,!0))},t.fromNumber=function(e){return new t(cl.fromNumber(e,!0))},t.fromBits=function(e,r){return new t(e,r)},t.fromString=function(e,r){return new t(cl.fromString(e,!0,r))},t.prototype.toExtendedJSON=function(){return{$timestamp:{t:this.high>>>0,i:this.low>>>0}}},t.fromExtendedJSON=function(e){return new t(e.$timestamp)},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){return"new Timestamp({ t: ".concat(this.getHighBits(),", i: ").concat(this.getLowBits()," })")},t.MAX_VALUE=cl.MAX_UNSIGNED_VALUE,t}(cl);function cL(e){return u6(e)&&Reflect.has(e,"_bsontype")&&"string"==typeof e._bsontype}var cj={$oid:cP,$binary:cn,$uuid:cn,$symbol:cN,$numberInt:cE,$numberDecimal:cw,$numberDouble:cS,$numberLong:cl,$minKey:cO,$maxKey:cI,$regex:cx,$regularExpression:cx,$timestamp:cC};function cU(e){var t=e.toISOString();return 0!==e.getUTCMilliseconds()?t:t.slice(0,-5)+"Z"}var cD={Binary:function(e){return new cn(e.value(),e.sub_type)},Code:function(e){return new ci(e.code,e.scope)},DBRef:function(e){return new cs(e.collection||e.namespace,e.oid,e.db,e.fields)},Decimal128:function(e){return new cw(e.bytes)},Double:function(e){return new cS(e.value)},Int32:function(e){return new cE(e.value)},Long:function(e){return cl.fromBits(null!=e.low?e.low:e.low_,null!=e.low?e.high:e.high_,null!=e.low?e.unsigned:e.unsigned_)},MaxKey:function(){return new cI},MinKey:function(){return new cO},ObjectID:function(e){return new cP(e)},ObjectId:function(e){return new cP(e)},BSONRegExp:function(e){return new cx(e.pattern,e.options)},Symbol:function(e){return new cN(e.value)},Timestamp:function(e){return cC.fromBits(e.low,e.high)}};!function(e){function t(e,t){var r=Object.assign({},{relaxed:!0,legacy:!1},t);return"boolean"==typeof r.relaxed&&(r.strict=!r.relaxed),"boolean"==typeof r.strict&&(r.relaxed=!r.strict),JSON.parse(e,function(e,t){if(-1!==e.indexOf("\0"))throw new uz("BSON Document field names cannot contain null bytes, found: ".concat(JSON.stringify(e)));return function e(t,r){if(void 0===r&&(r={}),"number"==typeof t){if(r.relaxed||r.legacy)return t;if(Math.floor(t)===t){if(t>=-2147483648&&t<=2147483647)return new cE(t);if(t>=-0x8000000000000000&&t<=0x7fffffffffffffff)return cl.fromNumber(t)}return new cS(t)}if(null==t||"object"!=typeof t)return t;if(t.$undefined)return null;for(var n,o=Object.keys(t).filter(function(e){return e.startsWith("$")&&null!=t[e]}),i=0;i<o.length;i++){var s=cj[o[i]];if(s)return s.fromExtendedJSON(t,r)}if(null!=t.$date){var a=t.$date,u=new Date;return r.legacy?"number"==typeof a?u.setTime(a):"string"==typeof a&&u.setTime(Date.parse(a)):"string"==typeof a?u.setTime(Date.parse(a)):cl.isLong(a)?u.setTime(a.toNumber()):"number"==typeof a&&r.relaxed&&u.setTime(a),u}if(null!=t.$code){var c=Object.assign({},t);return t.$scope&&(c.$scope=e(t.$scope)),ci.fromExtendedJSON(t)}if(u6(n=t)&&null!=n.$id&&"string"==typeof n.$ref&&(null==n.$db||"string"==typeof n.$db)||t.$dbPointer){var l=t.$ref?t:t.$dbPointer;if(l instanceof cs)return l;var f=Object.keys(l).filter(function(e){return e.startsWith("$")}),h=!0;if(f.forEach(function(e){-1===["$ref","$id","$db"].indexOf(e)&&(h=!1)}),h)return cs.fromExtendedJSON(l)}return t}(t,r)})}function r(e,t,r,n){return null!=r&&"object"==typeof r&&(n=r,r=0),null==t||"object"!=typeof t||Array.isArray(t)||(n=t,t=void 0,r=0),JSON.stringify(function e(t,r){if(("object"==typeof t||"function"==typeof t)&&null!==t){var n=r.seenObjects.findIndex(function(e){return e.obj===t});if(-1!==n){var o=r.seenObjects.map(function(e){return e.propertyName}),i=o.slice(0,n).map(function(e){return"".concat(e," -> ")}).join(""),s=o[n],a=" -> "+o.slice(n+1,o.length-1).map(function(e){return"".concat(e," -> ")}).join(""),u=o[o.length-1],c=" ".repeat(i.length+s.length/2),l="-".repeat(a.length+(s.length+u.length)/2-1);throw new uY("Converting circular structure to EJSON:\n"+"    ".concat(i).concat(s).concat(a).concat(u,"\n")+"    ".concat(c,"\\").concat(l,"/"))}r.seenObjects[r.seenObjects.length-1].obj=t}if(Array.isArray(t))return t.map(function(t,n){r.seenObjects.push({propertyName:"index ".concat(n),obj:null});try{return e(t,r)}finally{r.seenObjects.pop()}});if(void 0===t)return null;if(t instanceof Date||u5(t)){var f=t.getTime(),h=f>-1&&f<2534023188e5;return r.legacy?r.relaxed&&h?{$date:t.getTime()}:{$date:cU(t)}:r.relaxed&&h?{$date:cU(t)}:{$date:{$numberLong:t.getTime().toString()}}}if("number"==typeof t&&(!r.relaxed||!isFinite(t))){if(Math.floor(t)===t){var d=t>=-2147483648&&t<=2147483647,p=t>=-0x8000000000000000&&t<=0x7fffffffffffffff;if(d)return{$numberInt:t.toString()};if(p)return{$numberLong:t.toString()}}return{$numberDouble:t.toString()}}if(t instanceof RegExp||u4(t)){var y=t.flags;if(void 0===y){var v=t.toString().match(/[gimuy]*$/);v&&(y=v[0])}return new cx(t.source,y).toExtendedJSON(r)}return null!=t&&"object"==typeof t?function(t,r){if(null==t||"object"!=typeof t)throw new uz("not an object instance");var n=t._bsontype;if(void 0===n){var o={};for(var i in t){r.seenObjects.push({propertyName:i,obj:null});try{var s=e(t[i],r);"__proto__"===i?Object.defineProperty(o,i,{value:s,writable:!0,enumerable:!0,configurable:!0}):o[i]=s}finally{r.seenObjects.pop()}}return o}if(cL(t)){var a=t;if("function"!=typeof a.toExtendedJSON){var u=cD[t._bsontype];if(!u)throw new uY("Unrecognized or invalid _bsontype: "+t._bsontype);a=u(a)}return"Code"===n&&a.scope?a=new ci(a.code,e(a.scope,r)):"DBRef"===n&&a.oid&&(a=new cs(e(a.collection,r),e(a.oid,r),e(a.db,r),e(a.fields,r))),a.toExtendedJSON(r)}throw new uz("_bsontype must be a string, but was: "+typeof n)}(t,r):t}(e,Object.assign({relaxed:!0,legacy:!1},n,{seenObjects:[{propertyName:"(root)",obj:null}]})),t,r)}e.parse=t,e.stringify=r,e.serialize=function(e,t){return JSON.parse(r(e,t=t||{}))},e.deserialize=function(e,r){return r=r||{},t(JSON.stringify(e),r)}}(E||(E={}));var ck=uZ();function cq(e,t,r){var n=5;if(Array.isArray(e))for(var o=0;o<e.length;o++)n+=cM(o.toString(),e[o],t,!0,r);else for(var i in"function"==typeof(null==e?void 0:e.toBSON)&&(e=e.toBSON()),e)n+=cM(i,e[i],t,!1,r);return n}function cM(e,t,r,n,o){switch(void 0===r&&(r=!1),void 0===n&&(n=!1),void 0===o&&(o=!1),"function"==typeof(null==t?void 0:t.toBSON)&&(t=t.toBSON()),typeof t){case"string":return 1+uH.Buffer.byteLength(e,"utf8")+1+4+uH.Buffer.byteLength(t,"utf8")+1;case"number":if(Math.floor(t)!==t||!(t>=-9007199254740992)||!(t<=9007199254740992))return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+9;if(t>=-2147483648&&t<=2147483647)return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+5;return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+9;case"undefined":if(n||!o)return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+1;break;case"boolean":return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+2;case"object":if(null==t||"MinKey"===t._bsontype||"MaxKey"===t._bsontype)return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+1;if("ObjectId"===t._bsontype||"ObjectID"===t._bsontype)return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+13;if(t instanceof Date||u5(t))return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+9;if(ArrayBuffer.isView(t)||t instanceof ArrayBuffer||u2(t))return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+6+t.byteLength;else if("Long"===t._bsontype||"Double"===t._bsontype||"Timestamp"===t._bsontype)return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+9;else if("Decimal128"===t._bsontype)return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+17;else if("Code"===t._bsontype){if(null!=t.scope&&Object.keys(t.scope).length>0)return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+1+4+4+uH.Buffer.byteLength(t.code.toString(),"utf8")+1+cq(t.scope,r,o);return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+1+4+uH.Buffer.byteLength(t.code.toString(),"utf8")+1}else if("Binary"===t._bsontype){var i=t;if(i.sub_type===cn.SUBTYPE_BYTE_ARRAY)return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+(i.position+1+4+1+4);return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+(i.position+1+4+1)}else if("Symbol"===t._bsontype)return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+uH.Buffer.byteLength(t.value,"utf8")+4+1+1;else if("DBRef"===t._bsontype){var s=Object.assign({$ref:t.collection,$id:t.oid},t.fields);return null!=t.db&&(s.$db=t.db),(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+1+cq(s,r,o)}else if(t instanceof RegExp||u4(t))return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+1+uH.Buffer.byteLength(t.source,"utf8")+1+(t.global?1:0)+(t.ignoreCase?1:0)+(t.multiline?1:0)+1;else if("BSONRegExp"===t._bsontype)return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+1+uH.Buffer.byteLength(t.pattern,"utf8")+1+uH.Buffer.byteLength(t.options,"utf8")+1;else return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+cq(t,r,o)+1;case"function":if(t instanceof RegExp||u4(t)||"[object RegExp]"===String.call(t))return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+1+uH.Buffer.byteLength(t.source,"utf8")+1+(t.global?1:0)+(t.ignoreCase?1:0)+(t.multiline?1:0)+1;if(r&&null!=t.scope&&Object.keys(t.scope).length>0)return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+1+4+4+uH.Buffer.byteLength(uX(t),"utf8")+1+cq(t.scope,r,o);if(r)return(null!=e?uH.Buffer.byteLength(e,"utf8")+1:0)+1+4+uH.Buffer.byteLength(uX(t),"utf8")+1}return 0}I=ck.Map?ck.Map:function(){function e(e){void 0===e&&(e=[]),this._keys=[],this._values={};for(var t=0;t<e.length;t++)if(null!=e[t]){var r=e[t],n=r[0],o=r[1];this._keys.push(n),this._values[n]={v:o,i:this._keys.length-1}}}return e.prototype.clear=function(){this._keys=[],this._values={}},e.prototype.delete=function(e){var t=this._values[e];return null!=t&&(delete this._values[e],this._keys.splice(t.i,1),!0)},e.prototype.entries=function(){var e=this,t=0;return{next:function(){var r=e._keys[t++];return{value:void 0!==r?[r,e._values[r].v]:void 0,done:void 0===r}}}},e.prototype.forEach=function(e,t){t=t||this;for(var r=0;r<this._keys.length;r++){var n=this._keys[r];e.call(t,this._values[n].v,n,t)}},e.prototype.get=function(e){return this._values[e]?this._values[e].v:void 0},e.prototype.has=function(e){return null!=this._values[e]},e.prototype.keys=function(){var e=this,t=0;return{next:function(){var r=e._keys[t++];return{value:void 0!==r?r:void 0,done:void 0===r}}}},e.prototype.set=function(e,t){return this._values[e]?this._values[e].v=t:(this._keys.push(e),this._values[e]={v:t,i:this._keys.length-1}),this},e.prototype.values=function(){var e=this,t=0;return{next:function(){var r=e._keys[t++];return{value:void 0!==r?e._values[r].v:void 0,done:void 0===r}}}},Object.defineProperty(e.prototype,"size",{get:function(){return this._keys.length},enumerable:!1,configurable:!0}),e}(),cl.fromNumber(9007199254740992),cl.fromNumber(-9007199254740992);var cB=/\x00/,cW=new Set(["$db","$ref","$id","$clusterTime"]);function cF(e,t,r,n,o){e[n++]=2;var i=o?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");e[(n=n+i+1)-1]=0;var s=e.write(r,n+4,void 0,"utf8");return e[n+3]=s+1>>24&255,e[n+2]=s+1>>16&255,e[n+1]=s+1>>8&255,e[n]=s+1&255,n=n+4+s,e[n++]=0,n}var c$=new Uint8Array(8),cV=new DataView(c$.buffer,c$.byteOffset,c$.byteLength);function cG(e,t,r,n,o){if(Number.isInteger(r)&&r>=-2147483648&&r<=2147483647){e[n++]=16;var i=o?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");n+=i,e[n++]=0,e[n++]=255&r,e[n++]=r>>8&255,e[n++]=r>>16&255,e[n++]=r>>24&255}else{e[n++]=1;var i=o?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");n+=i,e[n++]=0,cV.setFloat64(0,r,!0),e.set(c$,n),n+=8}return n}function cH(e,t,r,n,o){e[n++]=10;var i=o?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");return n+=i,e[n++]=0,n}function cK(e,t,r,n,o){e[n++]=8;var i=o?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");return n+=i,e[n++]=0,e[n++]=r?1:0,n}function cJ(e,t,r,n,o){e[n++]=9;var i=o?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");n+=i,e[n++]=0;var s=cl.fromNumber(r.getTime()),a=s.getLowBits(),u=s.getHighBits();return e[n++]=255&a,e[n++]=a>>8&255,e[n++]=a>>16&255,e[n++]=a>>24&255,e[n++]=255&u,e[n++]=u>>8&255,e[n++]=u>>16&255,e[n++]=u>>24&255,n}function cz(e,t,r,n,o){e[n++]=11;var i=o?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");if(n+=i,e[n++]=0,r.source&&null!=r.source.match(cB))throw Error("value "+r.source+" must not contain null bytes");return n+=e.write(r.source,n,void 0,"utf8"),e[n++]=0,r.ignoreCase&&(e[n++]=105),r.global&&(e[n++]=115),r.multiline&&(e[n++]=109),e[n++]=0,n}function cY(e,t,r,n,o){e[n++]=11;var i=o?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");if(n+=i,e[n++]=0,null!=r.pattern.match(cB))throw Error("pattern "+r.pattern+" must not contain null bytes");return n+=e.write(r.pattern,n,void 0,"utf8"),e[n++]=0,n+=e.write(r.options.split("").sort().join(""),n,void 0,"utf8"),e[n++]=0,n}function cQ(e,t,r,n,o){null===r?e[n++]=10:"MinKey"===r._bsontype?e[n++]=255:e[n++]=127;var i=o?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");return n+=i,e[n++]=0,n}function cZ(e,t,r,n,o){e[n++]=7;var i=o?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");if(n+=i,e[n++]=0,"string"==typeof r.id)e.write(r.id,n,void 0,"binary");else if(u3(r.id))e.set(r.id.subarray(0,12),n);else throw new uY("object ["+JSON.stringify(r)+"] is not a valid ObjectId");return n+12}function cX(e,t,r,n,o){e[n++]=5;var i=o?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");n+=i,e[n++]=0;var s=r.length;return e[n++]=255&s,e[n++]=s>>8&255,e[n++]=s>>16&255,e[n++]=s>>24&255,e[n++]=0,e.set(u7(r),n),n+=s}function c0(e,t,r,n,o,i,s,a,u,c){void 0===o&&(o=!1),void 0===i&&(i=0),void 0===s&&(s=!1),void 0===a&&(a=!0),void 0===u&&(u=!1),void 0===c&&(c=[]);for(var l=0;l<c.length;l++)if(c[l]===r)throw new uz("cyclic dependency detected");c.push(r),e[n++]=Array.isArray(r)?4:3;var f=u?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");n+=f,e[n++]=0;var h=le(e,r,o,n,i+1,s,a,c);return c.pop(),h}function c1(e,t,r,n,o){e[n++]=19;var i=o?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");return n+=i,e[n++]=0,e.set(r.bytes.subarray(0,16),n),n+16}function c2(e,t,r,n,o){e[n++]="Long"===r._bsontype?18:17;var i=o?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");n+=i,e[n++]=0;var s=r.getLowBits(),a=r.getHighBits();return e[n++]=255&s,e[n++]=s>>8&255,e[n++]=s>>16&255,e[n++]=s>>24&255,e[n++]=255&a,e[n++]=a>>8&255,e[n++]=a>>16&255,e[n++]=a>>24&255,n}function c3(e,t,r,n,o){r=r.valueOf(),e[n++]=16;var i=o?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");return n+=i,e[n++]=0,e[n++]=255&r,e[n++]=r>>8&255,e[n++]=r>>16&255,e[n++]=r>>24&255,n}function c4(e,t,r,n,o){e[n++]=1;var i=o?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");return n+=i,e[n++]=0,cV.setFloat64(0,r.value,!0),e.set(c$,n),n+=8}function c5(e,t,r,n,o,i,s){e[n++]=13;var a=s?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");n+=a,e[n++]=0;var u=uX(r),c=e.write(u,n+4,void 0,"utf8")+1;return e[n]=255&c,e[n+1]=c>>8&255,e[n+2]=c>>16&255,e[n+3]=c>>24&255,n=n+4+c-1,e[n++]=0,n}function c6(e,t,r,n,o,i,s,a,u){if(void 0===o&&(o=!1),void 0===i&&(i=0),void 0===s&&(s=!1),void 0===a&&(a=!0),void 0===u&&(u=!1),r.scope&&"object"==typeof r.scope){e[n++]=15;var c=u?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");n+=c,e[n++]=0;var l=n,f="string"==typeof r.code?r.code:r.code.toString();n+=4;var h=e.write(f,n+4,void 0,"utf8")+1;e[n]=255&h,e[n+1]=h>>8&255,e[n+2]=h>>16&255,e[n+3]=h>>24&255,e[n+4+h-1]=0,n=n+h+4;var d=le(e,r.scope,o,n,i+1,s,a);n=d-1;var p=d-l;e[l++]=255&p,e[l++]=p>>8&255,e[l++]=p>>16&255,e[l++]=p>>24&255,e[n++]=0}else{e[n++]=13;var c=u?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");n+=c,e[n++]=0;var f=r.code.toString(),y=e.write(f,n+4,void 0,"utf8")+1;e[n]=255&y,e[n+1]=y>>8&255,e[n+2]=y>>16&255,e[n+3]=y>>24&255,n=n+4+y-1,e[n++]=0}return n}function c8(e,t,r,n,o){e[n++]=5;var i=o?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");n+=i,e[n++]=0;var s=r.value(!0),a=r.position;return r.sub_type===cn.SUBTYPE_BYTE_ARRAY&&(a+=4),e[n++]=255&a,e[n++]=a>>8&255,e[n++]=a>>16&255,e[n++]=a>>24&255,e[n++]=r.sub_type,r.sub_type===cn.SUBTYPE_BYTE_ARRAY&&(a-=4,e[n++]=255&a,e[n++]=a>>8&255,e[n++]=a>>16&255,e[n++]=a>>24&255),e.set(s,n),n+=r.position}function c7(e,t,r,n,o){e[n++]=14;var i=o?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");n+=i,e[n++]=0;var s=e.write(r.value,n+4,void 0,"utf8")+1;return e[n]=255&s,e[n+1]=s>>8&255,e[n+2]=s>>16&255,e[n+3]=s>>24&255,n=n+4+s-1,e[n++]=0,n}function c9(e,t,r,n,o,i,s){e[n++]=3;var a=s?e.write(t,n,void 0,"ascii"):e.write(t,n,void 0,"utf8");n+=a,e[n++]=0;var u=n,c={$ref:r.collection||r.namespace,$id:r.oid};null!=r.db&&(c.$db=r.db);var l=le(e,c=Object.assign(c,r.fields),!1,n,o+1,i),f=l-u;return e[u++]=255&f,e[u++]=f>>8&255,e[u++]=f>>16&255,e[u++]=f>>24&255,l}function le(e,t,r,n,o,i,s,a){void 0===r&&(r=!1),void 0===n&&(n=0),void 0===o&&(o=0),void 0===i&&(i=!1),void 0===s&&(s=!0),void 0===a&&(a=[]),n=n||0,(a=a||[]).push(t);var u=n+4;if(Array.isArray(t))for(var c=0;c<t.length;c++){var l="".concat(c),f=t[c];if("function"==typeof(null==f?void 0:f.toBSON)&&(f=f.toBSON()),"string"==typeof f)u=cF(e,l,f,u,!0);else if("number"==typeof f)u=cG(e,l,f,u,!0);else if("bigint"==typeof f)throw new uY("Unsupported type BigInt, please use Decimal128");else if("boolean"==typeof f)u=cK(e,l,f,u,!0);else if(f instanceof Date||u5(f))u=cJ(e,l,f,u,!0);else if(void 0===f)u=cH(e,l,f,u,!0);else if(null===f)u=cH(e,l,f,u,!0);else if("ObjectId"===f._bsontype||"ObjectID"===f._bsontype)u=cZ(e,l,f,u,!0);else if(u3(f))u=cX(e,l,f,u,!0);else if(f instanceof RegExp||u4(f))u=cz(e,l,f,u,!0);else if("object"==typeof f&&null==f._bsontype)u=c0(e,l,f,u,r,o,i,s,!0,a);else if("object"==typeof f&&cL(f)&&"Decimal128"===f._bsontype)u=c1(e,l,f,u,!0);else if("Long"===f._bsontype||"Timestamp"===f._bsontype)u=c2(e,l,f,u,!0);else if("Double"===f._bsontype)u=c4(e,l,f,u,!0);else if("function"==typeof f&&i)u=c5(e,l,f,u,r,o,!0);else if("Code"===f._bsontype)u=c6(e,l,f,u,r,o,i,s,!0);else if("Binary"===f._bsontype)u=c8(e,l,f,u,!0);else if("Symbol"===f._bsontype)u=c7(e,l,f,u,!0);else if("DBRef"===f._bsontype)u=c9(e,l,f,u,o,i,!0);else if("BSONRegExp"===f._bsontype)u=cY(e,l,f,u,!0);else if("Int32"===f._bsontype)u=c3(e,l,f,u,!0);else if("MinKey"===f._bsontype||"MaxKey"===f._bsontype)u=cQ(e,l,f,u,!0);else if(void 0!==f._bsontype)throw new uY("Unrecognized or invalid _bsontype: ".concat(String(f._bsontype)))}else if(t instanceof I||(p=t,"[object Map]"===Object.prototype.toString.call(p)))for(var h=t.entries(),d=!1;!d;){var p,y,v,m=h.next();if(!(d=!!m.done)){var l=m.value[0],f=m.value[1],g=typeof f;if("string"==typeof l&&!cW.has(l)){if(null!=l.match(cB))throw Error("key "+l+" must not contain null bytes");if(r){if("$"===l[0])throw Error("key "+l+" must not start with '$'");if(~l.indexOf("."))throw Error("key "+l+" must not contain '.'")}}if("string"===g)u=cF(e,l,f,u);else if("number"===g)u=cG(e,l,f,u);else{if("bigint"===g||(y=f,"[object BigInt64Array]"===Object.prototype.toString.call(y))||(v=f,"[object BigUint64Array]"===Object.prototype.toString.call(v)))throw new uY("Unsupported type BigInt, please use Decimal128");if("boolean"===g)u=cK(e,l,f,u);else if(f instanceof Date||u5(f))u=cJ(e,l,f,u);else if(null===f||void 0===f&&!1===s)u=cH(e,l,f,u);else if("ObjectId"===f._bsontype||"ObjectID"===f._bsontype)u=cZ(e,l,f,u);else if(u3(f))u=cX(e,l,f,u);else if(f instanceof RegExp||u4(f))u=cz(e,l,f,u);else if("object"===g&&null==f._bsontype)u=c0(e,l,f,u,r,o,i,s,!1,a);else if("object"===g&&"Decimal128"===f._bsontype)u=c1(e,l,f,u);else if("Long"===f._bsontype||"Timestamp"===f._bsontype)u=c2(e,l,f,u);else if("Double"===f._bsontype)u=c4(e,l,f,u);else if("Code"===f._bsontype)u=c6(e,l,f,u,r,o,i,s);else if("function"==typeof f&&i)u=c5(e,l,f,u,r,o,i);else if("Binary"===f._bsontype)u=c8(e,l,f,u);else if("Symbol"===f._bsontype)u=c7(e,l,f,u);else if("DBRef"===f._bsontype)u=c9(e,l,f,u,o,i);else if("BSONRegExp"===f._bsontype)u=cY(e,l,f,u);else if("Int32"===f._bsontype)u=c3(e,l,f,u);else if("MinKey"===f._bsontype||"MaxKey"===f._bsontype)u=cQ(e,l,f,u);else if(void 0!==f._bsontype)throw new uY("Unrecognized or invalid _bsontype: ".concat(String(f._bsontype)))}}}else{if("function"==typeof(null==t?void 0:t.toBSON)&&null!=(t=t.toBSON())&&"object"!=typeof t)throw new uY("toBSON function did not return an object");for(var l in t){var f=t[l];"function"==typeof(null==f?void 0:f.toBSON)&&(f=f.toBSON());var g=typeof f;if("string"==typeof l&&!cW.has(l)){if(null!=l.match(cB))throw Error("key "+l+" must not contain null bytes");if(r){if("$"===l[0])throw Error("key "+l+" must not start with '$'");if(~l.indexOf("."))throw Error("key "+l+" must not contain '.'")}}if("string"===g)u=cF(e,l,f,u);else if("number"===g)u=cG(e,l,f,u);else if("bigint"===g)throw new uY("Unsupported type BigInt, please use Decimal128");else if("boolean"===g)u=cK(e,l,f,u);else if(f instanceof Date||u5(f))u=cJ(e,l,f,u);else if(void 0===f)!1===s&&(u=cH(e,l,f,u));else if(null===f)u=cH(e,l,f,u);else if("ObjectId"===f._bsontype||"ObjectID"===f._bsontype)u=cZ(e,l,f,u);else if(u3(f))u=cX(e,l,f,u);else if(f instanceof RegExp||u4(f))u=cz(e,l,f,u);else if("object"===g&&null==f._bsontype)u=c0(e,l,f,u,r,o,i,s,!1,a);else if("object"===g&&"Decimal128"===f._bsontype)u=c1(e,l,f,u);else if("Long"===f._bsontype||"Timestamp"===f._bsontype)u=c2(e,l,f,u);else if("Double"===f._bsontype)u=c4(e,l,f,u);else if("Code"===f._bsontype)u=c6(e,l,f,u,r,o,i,s);else if("function"==typeof f&&i)u=c5(e,l,f,u,r,o,i);else if("Binary"===f._bsontype)u=c8(e,l,f,u);else if("Symbol"===f._bsontype)u=c7(e,l,f,u);else if("DBRef"===f._bsontype)u=c9(e,l,f,u,o,i);else if("BSONRegExp"===f._bsontype)u=cY(e,l,f,u);else if("Int32"===f._bsontype)u=c3(e,l,f,u);else if("MinKey"===f._bsontype||"MaxKey"===f._bsontype)u=cQ(e,l,f,u);else if(void 0!==f._bsontype)throw new uY("Unrecognized or invalid _bsontype: ".concat(String(f._bsontype)))}}a.pop(),e[u++]=0;var b=u-n;return e[n++]=255&b,e[n++]=b>>8&255,e[n++]=b>>16&255,e[n++]=b>>24&255,u}uH.Buffer.alloc(17825792);var lt=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(r)throw TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,n=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}},lr=function(){function e(e,t){this._stages=[],e&&t&&(this._db=e,this._request=new lE.reqClass(this._db.config),this._collectionName=t)}return e.prototype.end=function(){var e,t,r,n;return e=this,t=void 0,r=void 0,n=function(){var e;return lt(this,function(t){switch(t.label){case 0:if(!this._collectionName||!this._db)throw Error("Aggregation pipeline cannot send request");return[4,this._request.send("database.aggregate",{collectionName:this._collectionName,stages:this._stages})];case 1:if((e=t.sent())&&e.data&&e.data.list)return[2,{requestId:e.requestId,data:JSON.parse(e.data.list).map(E.parse)}];return[2,e]}})},new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},e.prototype.unwrap=function(){return this._stages},e.prototype.done=function(){return this._stages.map(function(e){var t,r=e.stageKey,n=e.stageValue;return(t={})[r]=JSON.parse(n),t})},e.prototype._pipe=function(e,t){return this._stages.push({stageKey:"$"+e,stageValue:JSON.stringify(t)}),this},e.prototype.addFields=function(e){return this._pipe("addFields",e)},e.prototype.bucket=function(e){return this._pipe("bucket",e)},e.prototype.bucketAuto=function(e){return this._pipe("bucketAuto",e)},e.prototype.count=function(e){return this._pipe("count",e)},e.prototype.geoNear=function(e){return e.query&&(e.query=uF.encode(e.query)),e.distanceMultiplier&&"number"==typeof e.distanceMultiplier&&(e.distanceMultiplier=e.distanceMultiplier),e.near&&(e.near=new up(e.near.longitude,e.near.latitude).toJSON()),this._pipe("geoNear",e)},e.prototype.group=function(e){return this._pipe("group",e)},e.prototype.limit=function(e){return this._pipe("limit",e)},e.prototype.match=function(e){return this._pipe("match",uF.encode(e))},e.prototype.project=function(e){return this._pipe("project",e)},e.prototype.lookup=function(e){return this._pipe("lookup",e)},e.prototype.replaceRoot=function(e){return this._pipe("replaceRoot",e)},e.prototype.sample=function(e){return this._pipe("sample",e)},e.prototype.skip=function(e){return this._pipe("skip",e)},e.prototype.sort=function(e){return this._pipe("sort",e)},e.prototype.sortByCount=function(e){return this._pipe("sortByCount",e)},e.prototype.unwind=function(e){return this._pipe("unwind",e)},e}(),ln=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),lo=function(e){function t(t,r){return e.call(this,t,r)||this}return ln(t,e),Object.defineProperty(t.prototype,"name",{get:function(){return this._coll},enumerable:!0,configurable:!0}),t.prototype.doc=function(e){if("string"!=typeof e&&"number"!=typeof e)throw Error("docId必须为字符串或数字");return new uW(this._db,this._coll,e)},t.prototype.add=function(e,t){return new uW(this._db,this._coll,void 0).create(e,t)},t.prototype.aggregate=function(){return new lr(this._db,this._coll)},t}(uG),li={eq:function(e){return new uw(S.EQ,[e])},neq:function(e){return new uw(S.NEQ,[e])},lt:function(e){return new uw(S.LT,[e])},lte:function(e){return new uw(S.LTE,[e])},gt:function(e){return new uw(S.GT,[e])},gte:function(e){return new uw(S.GTE,[e])},in:function(e){return new uw(S.IN,e)},nin:function(e){return new uw(S.NIN,e)},all:function(e){return new uw(S.ALL,e)},elemMatch:function(e){return new uw(S.ELEM_MATCH,[e])},exists:function(e){return new uw(S.EXISTS,[e])},size:function(e){return new uw(S.SIZE,[e])},mod:function(e){return new uw(S.MOD,[e])},geoNear:function(e){return new uw(S.GEO_NEAR,[e])},geoWithin:function(e){return new uw(S.GEO_WITHIN,[e])},geoIntersects:function(e){return new uw(S.GEO_INTERSECTS,[e])},and:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=a5(arguments[0])?arguments[0]:Array.from(arguments);return new ug(w.AND,r)},nor:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=a5(arguments[0])?arguments[0]:Array.from(arguments);return new ug(w.NOR,r)},or:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=a5(arguments[0])?arguments[0]:Array.from(arguments);return new ug(w.OR,r)},not:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=a5(arguments[0])?arguments[0]:Array.from(arguments);return new ug(w.NOT,r)},set:function(e){return new uv(_.SET,[e])},remove:function(){return new uv(_.REMOVE,[])},inc:function(e){return new uv(_.INC,[e])},mul:function(e){return new uv(_.MUL,[e])},push:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];if(a3(t[0])&&t[0].hasOwnProperty("each")){var n=t[0];e={$each:n.each,$position:n.position,$sort:n.sort,$slice:n.slice}}else e=a5(t[0])?t[0]:Array.from(t);return new uv(_.PUSH,e)},pull:function(e){return new uv(_.PULL,e)},pullAll:function(e){return new uv(_.PULL_ALL,e)},pop:function(){return new uv(_.POP,[])},shift:function(){return new uv(_.SHIFT,[])},unshift:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=a5(arguments[0])?arguments[0]:Array.from(arguments);return new uv(_.UNSHIFT,r)},addToSet:function(e){return new uv(_.ADD_TO_SET,e)},rename:function(e){return new uv(_.RENAME,[e])},bit:function(e){return new uv(_.BIT,[e])},max:function(e){return new uv(_.MAX,[e])},min:function(e){return new uv(_.MIN,[e])},expr:function(e){return{$expr:e}},jsonSchema:function(e){return{$jsonSchema:e}},text:function(e){return"string"===a2(e)?{$search:e.search}:{$search:e.search,$language:e.language,$caseSensitive:e.caseSensitive,$diacriticSensitive:e.diacriticSensitive}},aggregate:{pipeline:function(){return new lr},abs:function(e){return new ls("abs",e)},add:function(e){return new ls("add",e)},ceil:function(e){return new ls("ceil",e)},divide:function(e){return new ls("divide",e)},exp:function(e){return new ls("exp",e)},floor:function(e){return new ls("floor",e)},ln:function(e){return new ls("ln",e)},log:function(e){return new ls("log",e)},log10:function(e){return new ls("log10",e)},mod:function(e){return new ls("mod",e)},multiply:function(e){return new ls("multiply",e)},pow:function(e){return new ls("pow",e)},sqrt:function(e){return new ls("sqrt",e)},subtract:function(e){return new ls("subtract",e)},trunc:function(e){return new ls("trunc",e)},arrayElemAt:function(e){return new ls("arrayElemAt",e)},arrayToObject:function(e){return new ls("arrayToObject",e)},concatArrays:function(e){return new ls("concatArrays",e)},filter:function(e){return new ls("filter",e)},in:function(e){return new ls("in",e)},indexOfArray:function(e){return new ls("indexOfArray",e)},isArray:function(e){return new ls("isArray",e)},map:function(e){return new ls("map",e)},range:function(e){return new ls("range",e)},reduce:function(e){return new ls("reduce",e)},reverseArray:function(e){return new ls("reverseArray",e)},size:function(e){return new ls("size",e)},slice:function(e){return new ls("slice",e)},zip:function(e){return new ls("zip",e)},and:function(e){return new ls("and",e)},not:function(e){return new ls("not",e)},or:function(e){return new ls("or",e)},cmp:function(e){return new ls("cmp",e)},eq:function(e){return new ls("eq",e)},gt:function(e){return new ls("gt",e)},gte:function(e){return new ls("gte",e)},lt:function(e){return new ls("lt",e)},lte:function(e){return new ls("lte",e)},neq:function(e){return new ls("ne",e)},cond:function(e){return new ls("cond",e)},ifNull:function(e){return new ls("ifNull",e)},switch:function(e){return new ls("switch",e)},dateFromParts:function(e){return new ls("dateFromParts",e)},dateFromString:function(e){return new ls("dateFromString",e)},dayOfMonth:function(e){return new ls("dayOfMonth",e)},dayOfWeek:function(e){return new ls("dayOfWeek",e)},dayOfYear:function(e){return new ls("dayOfYear",e)},isoDayOfWeek:function(e){return new ls("isoDayOfWeek",e)},isoWeek:function(e){return new ls("isoWeek",e)},isoWeekYear:function(e){return new ls("isoWeekYear",e)},millisecond:function(e){return new ls("millisecond",e)},minute:function(e){return new ls("minute",e)},month:function(e){return new ls("month",e)},second:function(e){return new ls("second",e)},hour:function(e){return new ls("hour",e)},week:function(e){return new ls("week",e)},year:function(e){return new ls("year",e)},literal:function(e){return new ls("literal",e)},mergeObjects:function(e){return new ls("mergeObjects",e)},objectToArray:function(e){return new ls("objectToArray",e)},allElementsTrue:function(e){return new ls("allElementsTrue",e)},anyElementTrue:function(e){return new ls("anyElementTrue",e)},setDifference:function(e){return new ls("setDifference",e)},setEquals:function(e){return new ls("setEquals",e)},setIntersection:function(e){return new ls("setIntersection",e)},setIsSubset:function(e){return new ls("setIsSubset",e)},setUnion:function(e){return new ls("setUnion",e)},concat:function(e){return new ls("concat",e)},dateToString:function(e){return new ls("dateToString",e)},indexOfBytes:function(e){return new ls("indexOfBytes",e)},indexOfCP:function(e){return new ls("indexOfCP",e)},split:function(e){return new ls("split",e)},strLenBytes:function(e){return new ls("strLenBytes",e)},strLenCP:function(e){return new ls("strLenCP",e)},strcasecmp:function(e){return new ls("strcasecmp",e)},substr:function(e){return new ls("substr",e)},substrBytes:function(e){return new ls("substrBytes",e)},substrCP:function(e){return new ls("substrCP",e)},toLower:function(e){return new ls("toLower",e)},toUpper:function(e){return new ls("toUpper",e)},meta:function(e){return new ls("meta",e)},addToSet:function(e){return new ls("addToSet",e)},avg:function(e){return new ls("avg",e)},first:function(e){return new ls("first",e)},last:function(e){return new ls("last",e)},max:function(e){return new ls("max",e)},min:function(e){return new ls("min",e)},push:function(e){return new ls("push",e)},stdDevPop:function(e){return new ls("stdDevPop",e)},stdDevSamp:function(e){return new ls("stdDevSamp",e)},sum:function(e){return new ls("sum",e)},let:function(e){return new ls("let",e)}},project:{slice:function(e){return new la("slice",e)},elemMatch:function(e){return new la("elemMatch",e)}}},ls=function(e,t){this["$"+e]=t},la=function(e,t){this["$"+e]=t},lu=function(){function e(e){var t=e.regexp,r=e.options;if(!t)throw TypeError("regexp must be a string");this.$regex=t,this.$options=r}return e.prototype.parse=function(){return{$regex:this.$regex,$options:this.$options}},Object.defineProperty(e.prototype,"_internalType",{get:function(){return a1},enumerable:!0,configurable:!0}),e}();function lc(e){return new lu(e)}var ll={INSERT_DOC_FAIL:{code:"INSERT_DOC_FAIL",message:"insert document failed"},DATABASE_TRANSACTION_CONFLICT:{code:"DATABASE_TRANSACTION_CONFLICT",message:"database transaction conflict"}},lf=function(){return(lf=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},lh=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},ld=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(r)throw TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,n=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}},lp="database.updateDocInTransaction",ly=function(){function e(e,t,r){this._coll=t,this.id=r,this._transaction=e,this._request=this._transaction.getRequestMethod(),this._transactionId=this._transaction.getTransactionId()}return e.prototype.create=function(e){return lh(this,void 0,void 0,function(){var t,r,n,o;return ld(this,function(i){switch(i.label){case 0:return t={collectionName:this._coll,transactionId:this._transactionId,data:E.stringify(uP(e),{relaxed:!1})},this.id&&(t._id=this.id),[4,this._request.send("database.insertDocInTransaction",t)];case 1:if((r=i.sent()).code)throw r;if(n=E.parse(r.inserted),1==(o=E.parse(r.ok))&&1==n)return[2,lf(lf({},r),{ok:o,inserted:n})];throw Error(ll.INSERT_DOC_FAIL.message)}})})},e.prototype.get=function(){return lh(this,void 0,void 0,function(){var e,t;return ld(this,function(r){switch(r.label){case 0:return e={collectionName:this._coll,transactionId:this._transactionId,query:{_id:{$eq:this.id}}},[4,this._request.send("database.getInTransaction",e)];case 1:if((t=r.sent()).code)throw t;return[2,{data:"null"!==t.data?uh.formatField(E.parse(t.data)):E.parse(t.data),requestId:t.requestId}]}})})},e.prototype.set=function(e){return lh(this,void 0,void 0,function(){var t,r;return ld(this,function(n){switch(n.label){case 0:return t={collectionName:this._coll,transactionId:this._transactionId,query:{_id:{$eq:this.id}},data:E.stringify(uP(e),{relaxed:!1}),upsert:!0},[4,this._request.send(lp,t)];case 1:if((r=n.sent()).code)throw r;return[2,lf(lf({},r),{updated:E.parse(r.updated),upserted:r.upserted?JSON.parse(r.upserted):null})]}})})},e.prototype.update=function(e){return lh(this,void 0,void 0,function(){var t,r;return ld(this,function(n){switch(n.label){case 0:return t={collectionName:this._coll,transactionId:this._transactionId,query:{_id:{$eq:this.id}},data:E.stringify(uk.encode(e),{relaxed:!1})},[4,this._request.send(lp,t)];case 1:if((r=n.sent()).code)throw r;return[2,lf(lf({},r),{updated:E.parse(r.updated)})]}})})},e.prototype.delete=function(){return lh(this,void 0,void 0,function(){var e,t;return ld(this,function(r){switch(r.label){case 0:return e={collectionName:this._coll,transactionId:this._transactionId,query:{_id:{$eq:this.id}}},[4,this._request.send("database.deleteDocInTransaction",e)];case 1:if((t=r.sent()).code)throw t;return[2,lf(lf({},t),{deleted:E.parse(t.deleted)})]}})})},e}(),lv=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),lm=function(e){function t(t,r){return e.call(this,t,r)||this}return lv(t,e),Object.defineProperty(t.prototype,"name",{get:function(){return this._coll},enumerable:!0,configurable:!0}),t.prototype.doc=function(e){if("string"!=typeof e&&"number"!=typeof e)throw Error("docId必须为字符串或数字");return new ly(this._transaction,this._coll,e)},t.prototype.add=function(e){var t;return void 0!==e._id&&(t=e._id),new ly(this._transaction,this._coll,t).create(e)},t}(function(e,t){this._coll=t,this._transaction=e}),lg=function(e,t,r,n){return new(r||(r=Promise))(function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},lb=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(r)throw TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,n=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}},l_=function(){function e(e){this._db=e,this._request=new lE.reqClass(this._db.config),this.aborted=!1,this.commited=!1,this.inited=!1}return e.prototype.init=function(){return lg(this,void 0,void 0,function(){var e;return lb(this,function(t){switch(t.label){case 0:return[4,this._request.send("database.startTransaction")];case 1:if((e=t.sent()).code)throw e;return this.inited=!0,this._id=e.transactionId,[2]}})})},e.prototype.collection=function(e){if(!e)throw Error("Collection name is required");return new lm(this,e)},e.prototype.getTransactionId=function(){return this._id},e.prototype.getRequestMethod=function(){return this._request},e.prototype.commit=function(){return lg(this,void 0,void 0,function(){var e,t;return lb(this,function(r){switch(r.label){case 0:return e={transactionId:this._id},[4,this._request.send("database.commitTransaction",e)];case 1:if((t=r.sent()).code)throw t;return this.commited=!0,[2,t]}})})},e.prototype.rollback=function(e){return lg(this,void 0,void 0,function(){var t,r;return lb(this,function(n){switch(n.label){case 0:return t={transactionId:this._id},[4,this._request.send("database.abortTransaction",t)];case 1:if((r=n.sent()).code)throw r;return this.aborted=!0,this.abortReason=e,[2,r]}})})},e}();function lw(){return lg(this,void 0,void 0,function(){var e;return lb(this,function(t){switch(t.label){case 0:return[4,(e=new l_(this)).init()];case 1:return t.sent(),[2,e]}})})}function lS(e,t){return void 0===t&&(t=3),lg(this,void 0,void 0,function(){var r,n,o,i,s=this;return lb(this,function(a){switch(a.label){case 0:return a.trys.push([0,4,,10]),[4,(r=new l_(this)).init()];case 1:return a.sent(),[4,e(r)];case 2:if(n=a.sent(),!0===r.aborted)throw r.abortReason;return[4,r.commit()];case 3:return a.sent(),[2,n];case 4:if(o=a.sent(),!1===r.inited)throw o;if(i=function(e){return lg(s,void 0,void 0,function(){return lb(this,function(t){switch(t.label){case 0:if(!(!r.aborted&&!r.commited))return[3,5];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,r.rollback()];case 2:case 3:return t.sent(),[3,4];case 4:throw e;case 5:if(!0===r.aborted)throw r.abortReason;throw e}})})},!(t<=0))return[3,6];return[4,i(o)];case 5:a.sent(),a.label=6;case 6:if(!(o&&o.code===ll.DATABASE_TRANSACTION_CONFLICT.code))return[3,8];return[4,lS.bind(this)(e,--t)];case 7:return[2,a.sent()];case 8:return[4,i(o)];case 9:return a.sent(),[3,10];case 10:return[2]}})})}var lE=function(){function e(e){this.config=e,this.Geo=T,this.serverDate=ul,this.command=li,this.RegExp=lc,this.startTransaction=lw,this.runTransaction=lS,this.logicCommand=ug,this.updateCommand=uv,this.queryCommand=uw}return e.prototype.collection=function(e){if(!e)throw Error("Collection name is required");return new lo(this,e)},e.prototype.createCollection=function(t){return new e.reqClass(this.config).send("database.addCollection",{collectionName:t})},e}(),lI=function(){return(lI=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},lO={name:"database",entity:{database:function(e){var t=this.platform,r=t.adapter,n=t.runtime;return lE.reqClass=this.request.constructor,lE.getAccessToken=this.authInstance?this.authInstance.getAccessToken.bind(this.authInstance):function(){return""},lE.runtime=n,this.wsClientClass&&(lE.wsClass=r.wsClass,lE.wsClientClass=this.wsClientClass),lE.ws||(lE.ws=null),new lE(lI(lI(lI({},this.config),{_fromApp:this}),e))}}};try{cloudbase.registerComponent(lO)}catch(e){}nX.registerVersion("2.17.6");try{(function(e){try{e.registerComponent(oL)}catch(e){console.warn(e)}})(nX),function(e){try{e.registerComponent(oK)}catch(e){console.warn(e)}}(nX),function(e){try{e.registerComponent(o1)}catch(e){console.warn(e)}}(nX),function(e){try{e.registerComponent(lO)}catch(e){console.warn(e)}}(nX),function(e){try{e.registerComponent(iT),e.registerHook(iO)}catch(e){console.warn(e)}}(nX),function(e){try{e.registerComponent(iL)}catch(e){console.warn(e)}}(nX),function(e){try{e.registerComponent(i2)}catch(e){console.warn(e)}}(nX),function(e){try{e.registerComponent(aD)}catch(e){console.warn(e)}}(nX),function(e){try{e.registerComponent(oB)}catch(e){console.warn(e)}}(nX)}catch(e){}try{window.cloudbase=nX}catch(e){}let lT=async()=>(console.log("非浏览器环境，跳过CloudBase初始化"),null)}};