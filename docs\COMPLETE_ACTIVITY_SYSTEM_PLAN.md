# 🎉 宠物平台完整活动系统规划

## 📋 概述

本文档详细规划了宠物平台的完整活动系统，包括活动类型、生命周期管理、数据清理策略和管理功能。

## 🎯 **活动系统核心特性**

### **1. 活动类型设计**

#### **A. 评选竞赛类活动**
```javascript
// 十大圣宠评选活动
const contestActivity = {
  type: 'CONTEST',
  title: '十大圣宠评选',
  
  // 灵活时间配置
  schedule: {
    frequency: 'CUSTOM',        // 可选：MONTHLY/QUARTERLY/HALF_YEARLY/CUSTOM
    duration: 7,                // 活动持续天数（完全可自定义）
    resultDisplayDays: 3,       // 结果展示天数（可管理员控制）
    startDate: '2025-02-01',
    endDate: '2025-02-08',
    resultEndDate: '2025-02-11' // 结果展示结束日期
  },
  
  // 评选规则
  rules: {
    selectionCriteria: 'STAR_RATING',  // 基于5星评分
    qualificationThreshold: {
      minStars: 5,                      // 最低5星
      topCount: 20                      // 前20名入选
    },
    finalSelection: {
      method: 'ADMIN_REVIEW',           // 管理员最终评选
      finalCount: 10                    // 最终10名获奖
    }
  },
  
  // 参与方式
  participation: {
    method: 'AUTO_QUALIFY',             // 自动入选（基于星级）
    userAction: 'NONE'                  // 用户无需额外操作
  }
};
```

#### **B. 投票话题类活动**
```javascript
// 养猫vs养狗投票活动
const votingActivity = {
  type: 'VOTING',
  title: '养猫好还是养狗好？',
  
  // 灵活时间配置
  schedule: {
    frequency: 'CUSTOM',
    duration: 14,               // 2周投票时间
    resultDisplayDays: 7,       // 结果展示1周
    startDate: '2025-02-15',
    endDate: '2025-03-01',
    resultEndDate: '2025-03-08'
  },
  
  // 投票选项
  options: [
    {
      id: 'cat',
      title: '养猫更好',
      description: '猫咪独立、优雅、适合忙碌生活',
      image: '/images/cat-option.jpg'
    },
    {
      id: 'dog', 
      title: '养狗更好',
      description: '狗狗忠诚、活泼、是最好的伙伴',
      image: '/images/dog-option.jpg'
    }
  ],
  
  // 参与方式
  participation: {
    method: 'USER_VOTE',        // 用户主动投票
    userAction: 'REQUIRED',     // 必须用户操作
    allowMultiple: false        // 不允许多选
  }
};
```

### **2. 活动生命周期管理**

#### **A. 活动状态流转**
```javascript
const activityLifecycle = {
  // 状态定义
  states: {
    DRAFT: '草稿状态',
    ACTIVE: '进行中',
    ENDED: '已结束（展示结果）',
    ARCHIVED: '已归档（仅保留结果）',
    DELETED: '已删除'
  },
  
  // 状态流转
  transitions: {
    'DRAFT → ACTIVE': '管理员发布活动',
    'ACTIVE → ENDED': '活动时间结束，自动转为结果展示',
    'ENDED → ARCHIVED': '结果展示期结束，清理详细数据',
    'ENDED → DELETED': '管理员手动删除活动',
    'ARCHIVED → DELETED': '管理员删除归档活动'
  },
  
  // 自动化任务
  automation: {
    endActivity: '活动结束时间到达时自动结束',
    archiveActivity: '结果展示期结束时自动归档',
    cleanupData: '归档时自动清理评论等临时数据'
  }
};
```

#### **B. 数据保留策略**
```javascript
const dataRetentionPolicy = {
  // 活动进行期间
  activePeriod: {
    keep: ['活动配置', '参与数据', '投票记录', '评论', '统计数据'],
    description: '保留所有数据以支持完整功能'
  },
  
  // 结果展示期间
  resultDisplayPeriod: {
    keep: ['活动配置', '最终结果', '获奖名单', '统计摘要'],
    cleanup: ['用户评论', '详细投票记录', '临时缓存'],
    description: '只保留结果相关数据，清理互动数据'
  },
  
  // 归档期间
  archivedPeriod: {
    keep: ['活动基本信息', '最终结果', '获奖名单'],
    cleanup: ['详细配置', '过程数据', '统计详情'],
    description: '最小化数据保留，仅供历史查询'
  },
  
  // 删除后
  deletedState: {
    keep: [],
    cleanup: ['所有相关数据'],
    description: '完全删除，不可恢复'
  }
};
```

### **3. 评论系统设计**

#### **A. 评论功能配置**
```javascript
const commentSystemConfig = {
  // 基础设置
  enabled: true,              // 是否启用评论
  requireVote: true,          // 是否需要投票后才能评论
  
  // 频率限制
  rateLimit: {
    interval: 10,             // 10秒间隔（推荐）
    perMinute: 4,             // 每分钟最多4条
    perHour: 30,              // 每小时最多30条
    perDay: 200               // 每天最多200条
  },
  
  // 内容限制
  content: {
    minLength: 2,             // 最少2个字符
    maxLength: 100,           // 最多100个字符
    duplicateCheck: true,     // 检查重复内容
    sensitiveWordFilter: true // 敏感词过滤
  },
  
  // 数据清理
  cleanup: {
    onActivityEnd: false,     // 活动结束时不立即清理
    onResultDisplayEnd: true, // 结果展示期结束时清理
    keepCount: 0              // 不保留任何评论
  }
};
```

#### **B. 评论界面设计**
```javascript
const commentInterface = {
  // 权限提示
  permissionPrompt: {
    notVoted: '投票后即可参与讨论 🗳️',
    activityEnded: '活动已结束，评论功能已关闭',
    systemDisabled: '评论功能暂未开放'
  },
  
  // 输入界面
  inputInterface: {
    quickPhrases: ['太可爱了！', '好漂亮', '想要同款', '萌化了'],
    placeholder: '分享你的想法... (2-100字)',
    charCounter: true,
    submitCooldown: true      // 显示提交冷却时间
  },
  
  // 显示界面
  displayInterface: {
    refreshInterval: 30,      // 30秒自动刷新
    newCommentNotification: true,
    pagination: 20,           // 每页20条评论
    sortBy: 'created_at'      // 按时间排序
  }
};
```

### **4. 活动入口控制**

#### **A. 系统级控制**
```javascript
const systemControl = {
  // 总开关
  activitySystemEnabled: false,    // 活动系统总开关
  
  // 显示控制
  display: {
    showInNavigation: false,       // 导航栏显示
    showOnHomepage: false,         // 首页横幅显示
    showInSidebar: false          // 侧边栏显示
  },
  
  // 功能控制
  features: {
    createNewActivity: true,       // 允许创建新活动
    commentSystem: true,           // 评论系统
    realTimeUpdates: false,        // 实时更新
    pushNotifications: false       // 推送通知
  }
};
```

#### **B. 活动级控制**
```javascript
const activityControl = {
  // 活动可见性
  visibility: {
    showToPublic: true,           // 对公众可见
    showInList: true,             // 在活动列表中显示
    allowParticipation: true,     // 允许参与
    allowViewing: true            // 允许查看
  },
  
  // 结果展示控制
  resultDisplay: {
    showResults: true,            // 显示结果
    showStatistics: true,         // 显示统计
    showParticipants: false,      // 显示参与者列表
    allowSharing: true            // 允许分享
  }
};
```

### **5. 数据库设计**

#### **A. 活动主表**
```sql
CREATE TABLE activities (
  id VARCHAR(50) PRIMARY KEY,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  type ENUM('CONTEST', 'VOTING', 'DISCUSSION') NOT NULL,
  
  -- 时间管理
  start_time DATETIME NOT NULL,
  end_time DATETIME NOT NULL,
  result_display_end_time DATETIME,     -- 结果展示结束时间
  duration_days INT NOT NULL,
  result_display_days INT DEFAULT 3,    -- 结果展示天数
  
  -- 状态管理
  status ENUM('DRAFT', 'ACTIVE', 'ENDED', 'ARCHIVED', 'DELETED') DEFAULT 'DRAFT',
  
  -- 配置信息
  config JSON NOT NULL,
  
  -- 结果数据（归档后只保留这部分）
  final_results JSON,
  statistics_summary JSON,
  
  -- 管理信息
  created_by VARCHAR(50),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  archived_at TIMESTAMP NULL,
  
  INDEX idx_status_time (status, start_time),
  INDEX idx_type_status (type, status)
);
```

#### **B. 活动结果表**
```sql
CREATE TABLE activity_results (
  id VARCHAR(50) PRIMARY KEY,
  activity_id VARCHAR(50) NOT NULL,
  result_type ENUM('WINNER', 'RANKING', 'STATISTICS') NOT NULL,
  
  -- 结果数据
  rank_position INT,
  participant_id VARCHAR(50),
  participant_name VARCHAR(100),
  score DECIMAL(10,2),
  votes INT,
  
  -- 奖励信息
  award_title VARCHAR(100),
  award_description TEXT,
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE,
  INDEX idx_activity_rank (activity_id, rank_position)
);
```

### **6. 管理后台功能**

#### **A. 活动管理界面**
```javascript
const adminInterface = {
  // 活动列表
  activityList: {
    filters: ['状态', '类型', '时间范围'],
    actions: ['查看', '编辑', '删除', '归档'],
    batchActions: ['批量删除', '批量归档'],
    sorting: ['创建时间', '开始时间', '参与人数']
  },
  
  // 活动创建向导
  creationWizard: {
    steps: [
      '选择活动类型',
      '基础信息设置', 
      '规则配置',
      '时间安排',
      '预览确认'
    ],
    templates: ['十大圣宠评选模板', '投票话题模板', '自定义模板']
  },
  
  // 活动监控
  monitoring: {
    realTimeStats: ['参与人数', '投票数', '评论数'],
    alerts: ['异常活动', '系统错误', '性能警告'],
    reports: ['活动效果报告', '用户参与分析', '系统性能报告']
  }
};
```

#### **B. 系统设置界面**
```javascript
const systemSettings = {
  // 全局设置
  globalSettings: {
    activitySystemEnabled: '活动系统总开关',
    defaultResultDisplayDays: '默认结果展示天数',
    maxConcurrentActivities: '最大并发活动数',
    autoArchiveEnabled: '自动归档功能'
  },
  
  // 评论设置
  commentSettings: {
    enabled: '启用评论功能',
    rateLimit: '评论频率限制',
    maxLength: '评论最大长度',
    requireVote: '要求投票后评论',
    autoCleanup: '自动清理评论'
  },
  
  // 通知设置
  notificationSettings: {
    emailNotifications: '邮件通知',
    pushNotifications: '推送通知',
    adminAlerts: '管理员警报',
    userReminders: '用户提醒'
  }
};
```

### **7. 自动化任务**

#### **A. 定时任务**
```javascript
const scheduledTasks = {
  // 每分钟执行
  everyMinute: {
    checkActivityStatus: '检查活动状态变更',
    updateRealTimeStats: '更新实时统计'
  },
  
  // 每小时执行
  hourly: {
    cleanupExpiredSessions: '清理过期会话',
    updateActivityRankings: '更新活动排名',
    generateHourlyReports: '生成小时报告'
  },
  
  // 每天执行
  daily: {
    archiveEndedActivities: '归档结束的活动',
    cleanupOldComments: '清理过期评论',
    generateDailyReports: '生成日报',
    backupActivityData: '备份活动数据'
  },
  
  // 每周执行
  weekly: {
    cleanupArchivedData: '清理归档数据',
    generateWeeklyAnalytics: '生成周度分析',
    optimizeDatabase: '优化数据库'
  }
};
```

#### **B. 事件触发任务**
```javascript
const eventTriggeredTasks = {
  // 活动状态变更
  onActivityStatusChange: {
    'ACTIVE → ENDED': [
      'stopAcceptingParticipation',
      'calculateFinalResults', 
      'sendResultNotifications'
    ],
    'ENDED → ARCHIVED': [
      'cleanupComments',
      'cleanupDetailedVotes',
      'compressActivityData'
    ]
  },
  
  // 用户操作
  onUserAction: {
    vote: ['updateRealTimeStats', 'checkQualificationThreshold'],
    comment: ['moderateContent', 'updateActivityHeat'],
    report: ['flagForReview', 'notifyModerators']
  }
};
```

## 🎯 **实施计划**

### **第一阶段：核心功能（2-3周）**
1. ✅ 实现基础的评选类和投票类活动
2. ✅ 活动生命周期管理
3. ✅ 基础的管理后台界面
4. ✅ 活动入口控制

### **第二阶段：完善功能（2-3周）**
1. ✅ 评论系统和频率限制
2. ✅ 结果展示和数据清理
3. ✅ 自动化任务和定时清理
4. ✅ 活动模板和创建向导

### **第三阶段：优化提升（1-2周）**
1. ✅ 性能优化和缓存策略
2. ✅ 监控告警和数据分析
3. ✅ 用户体验优化
4. ✅ 安全性加固

## 📊 **预期效果**

### **用户体验**
- 🎯 丰富的互动活动增强用户粘性
- 🎨 简洁的界面设计不影响主要功能
- ⚡ 流畅的参与体验提升满意度

### **管理效率**
- 🛠️ 灵活的活动配置满足各种需求
- 🤖 自动化清理减少人工维护
- 📈 详细的数据分析支持决策

### **系统性能**
- 🚀 优化的数据结构保证响应速度
- 💾 智能的数据清理节省存储空间
- 🔒 完善的权限控制保证安全性

## 🗑️ **数据清理策略详解**

### **A. 清理时机和内容**

| 阶段 | 保留数据 | 清理数据 | 清理时机 |
|------|----------|----------|----------|
| **活动进行中** | 全部数据 | 无 | - |
| **结果展示期** | 活动配置、最终结果、统计摘要 | 用户评论、详细投票记录 | 活动结束时 |
| **归档期** | 基本信息、获奖名单 | 详细配置、过程数据 | 结果展示期结束时 |
| **删除后** | 无 | 全部数据 | 管理员手动删除时 |

### **B. 自动清理实现**
```javascript
// 自动数据清理任务
const dataCleanupTasks = {
  // 活动结束时清理
  onActivityEnd: async (activityId) => {
    // 保留最终结果
    await saveActivityResults(activityId);

    // 清理评论数据
    await db.collection('activity_comments')
      .where({ activity_id: activityId })
      .remove();

    // 清理详细投票记录（保留统计）
    await cleanupDetailedVotes(activityId);

    console.log(`活动 ${activityId} 数据清理完成`);
  },

  // 归档时进一步清理
  onActivityArchive: async (activityId) => {
    // 只保留核心结果数据
    await compressActivityData(activityId);

    // 清理临时文件和缓存
    await cleanupTempFiles(activityId);

    console.log(`活动 ${activityId} 归档清理完成`);
  }
};
```

### **C. 管理员控制选项**
```javascript
// 管理员可配置的清理选项
const cleanupOptions = {
  resultDisplayDays: 3,           // 结果展示天数（可调整）
  autoArchive: true,              // 是否自动归档
  keepCommentCount: 0,            // 保留评论数量（0=全部删除）
  keepVoteDetails: false,         // 是否保留详细投票记录
  compressionLevel: 'high'        // 数据压缩级别
};
```

这个活动系统既满足了灵活性需求，又保证了系统的稳定性和可维护性！🎉
