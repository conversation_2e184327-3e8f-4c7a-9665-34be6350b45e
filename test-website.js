// 网站功能测试脚本
const cloudbase = require('@cloudbase/node-sdk');

// 初始化云开发
const app = cloudbase.init({
  env: 'yichongyuzhou-3g9112qwf5f3487b'
});

const db = app.database();

async function testWebsiteFunctions() {
  console.log('🧪 开始测试网站主要功能...\n');

  try {
    // 1. 测试数据库连接
    console.log('1️⃣ 测试数据库连接...');
    const testQuery = await db.collection('users').limit(1).get();
    console.log('✅ 数据库连接正常\n');

    // 2. 测试活动系统配置
    console.log('2️⃣ 测试活动系统配置...');
    const configResult = await db.collection('system_config')
      .where({ key: 'activity_system' })
      .get();
    
    if (configResult.data.length === 0) {
      console.log('⚠️  活动系统配置不存在，创建默认配置...');
      await db.collection('system_config').add({
        key: 'activity_system',
        value: {
          enabled: true,
          comments_enabled: true,
          rate_limit_interval: 10,
          max_comment_length: 100,
          default_result_display_days: 3
        },
        created_at: new Date(),
        updated_at: new Date()
      });
      console.log('✅ 活动系统配置已创建');
    } else {
      console.log('✅ 活动系统配置存在');
      console.log('   配置内容:', JSON.stringify(configResult.data[0].value, null, 2));
    }
    console.log('');

    // 3. 测试活动数据
    console.log('3️⃣ 测试活动数据...');
    const activitiesResult = await db.collection('activities').limit(5).get();
    console.log(`✅ 活动数据查询成功，共 ${activitiesResult.data.length} 个活动`);
    
    if (activitiesResult.data.length === 0) {
      console.log('⚠️  没有活动数据，创建测试活动...');
      const testActivity = {
        title: '测试投票活动：猫咪 VS 狗狗',
        description: '这是一个测试活动，用于验证投票功能是否正常工作。',
        type: 'VOTING',
        status: 'ACTIVE',
        start_time: new Date(),
        end_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后
        result_display_end_time: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000), // 10天后
        duration_days: 7,
        result_display_days: 3,
        config: {
          comments_enabled: true,
          comments_after_vote: true,
          rate_limit: { interval: 10 },
          options: [
            {
              id: 'cats',
              title: '猫咪更好',
              description: '独立、优雅、适合忙碌生活'
            },
            {
              id: 'dogs', 
              title: '狗狗更好',
              description: '忠诚、活泼、是最好的伙伴'
            }
          ]
        },
        statistics_summary: {
          total_votes: 0,
          total_comments: 0,
          votes_by_option: {}
        },
        created_at: new Date(),
        updated_at: new Date()
      };
      
      await db.collection('activities').add(testActivity);
      console.log('✅ 测试活动已创建');
    } else {
      activitiesResult.data.forEach((activity, index) => {
        console.log(`   活动 ${index + 1}: ${activity.title} (${activity.status})`);
      });
    }
    console.log('');

    // 4. 测试用户数据
    console.log('4️⃣ 测试用户数据...');
    const usersResult = await db.collection('users').limit(5).get();
    console.log(`✅ 用户数据查询成功，共 ${usersResult.data.length} 个用户`);
    console.log('');

    // 5. 测试帖子数据
    console.log('5️⃣ 测试帖子数据...');
    const postsResult = await db.collection('posts').limit(5).get();
    console.log(`✅ 帖子数据查询成功，共 ${postsResult.data.length} 个帖子`);
    console.log('');

    // 6. 测试管理员数据
    console.log('6️⃣ 测试管理员数据...');
    const adminsResult = await db.collection('admins').limit(5).get();
    console.log(`✅ 管理员数据查询成功，共 ${adminsResult.data.length} 个管理员`);
    
    // 检查超级管理员是否存在
    const superAdminResult = await db.collection('admins')
      .where({ username: 'superadminTT' })
      .get();
    
    if (superAdminResult.data.length === 0) {
      console.log('⚠️  超级管理员不存在，请检查管理员账户设置');
    } else {
      console.log('✅ 超级管理员账户存在');
    }
    console.log('');

    // 7. 测试云函数
    console.log('7️⃣ 测试云函数...');
    try {
      const functionResult = await app.callFunction({
        name: 'pet-api',
        data: {
          action: 'getSystemConfig'
        }
      });
      
      if (functionResult.result.success) {
        console.log('✅ 云函数调用成功');
        console.log('   系统配置:', JSON.stringify(functionResult.result.data, null, 2));
      } else {
        console.log('❌ 云函数调用失败:', functionResult.result.error);
      }
    } catch (error) {
      console.log('❌ 云函数调用异常:', error.message);
    }
    console.log('');

    console.log('🎉 网站功能测试完成！');
    console.log('\n📋 测试总结:');
    console.log('✅ 数据库连接正常');
    console.log('✅ 活动系统配置正常');
    console.log('✅ 基础数据表存在');
    console.log('✅ 云函数可以调用');
    console.log('\n🌐 网站访问地址:');
    console.log('   用户端: https://yichongyuzhou-3g9112qwf5f3487b-1368816056.tcloudbaseapp.com/pet-platform-final/');
    console.log('   管理端: https://yichongyuzhou-3g9112qwf5f3487b-1368816056.tcloudbaseapp.com/pet-platform-final/admin');
    console.log('   活动中心: https://yichongyuzhou-3g9112qwf5f3487b-1368816056.tcloudbaseapp.com/pet-platform-final/activities');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
testWebsiteFunctions().then(() => {
  process.exit(0);
}).catch(error => {
  console.error('测试失败:', error);
  process.exit(1);
});
