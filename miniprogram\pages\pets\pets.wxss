/* 宠物页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.category-scroll {
  white-space: nowrap;
  margin-bottom: 20rpx;
}

.category-item {
  display: inline-block;
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  background-color: white;
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #666;
  border: 2rpx solid #eee;
}

.category-item.active {
  background-color: #07c160;
  color: white;
  border-color: #07c160;
}

.pets-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.pet-card {
  width: 340rpx;
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.pet-image {
  width: 100%;
  height: 240rpx;
}

.pet-info {
  padding: 20rpx;
}

.pet-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.pet-breed {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
}

.pet-price {
  font-size: 36rpx;
  color: #ff6b6b;
  font-weight: bold;
}
