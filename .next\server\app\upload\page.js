(()=>{var e={};e.id=676,e.ids=[676],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},79794:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>i.a,__next_app__:()=>y,originalPathname:()=>m,pages:()=>d,routeModule:()=>g,tree:()=>l}),a(64929),a(16953),a(35866);var t=a(23191),s=a(88716),o=a(37922),i=a.n(o),n=a(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(r,c);let l=["",{children:["upload",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,64929)),"D:\\web-cloudbase-project\\src\\app\\upload\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\web-cloudbase-project\\src\\app\\upload\\page.tsx"],m="/upload/page",y={require:a,loadChunk:()=>Promise.resolve()},g=new t.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/upload/page",pathname:"/upload",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},98698:(e,r,a)=>{Promise.resolve().then(a.bind(a,1570))},86333:(e,r,a)=>{"use strict";a.d(r,{Z:()=>t});let t=(0,a(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},51896:(e,r,a)=>{"use strict";a.d(r,{Z:()=>t});let t=(0,a(76557).Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},71821:(e,r,a)=>{"use strict";a.d(r,{Z:()=>t});let t=(0,a(76557).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},67427:(e,r,a)=>{"use strict";a.d(r,{Z:()=>t});let t=(0,a(76557).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},77636:(e,r,a)=>{"use strict";a.d(r,{Z:()=>t});let t=(0,a(76557).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},39730:(e,r,a)=>{"use strict";a.d(r,{Z:()=>t});let t=(0,a(76557).Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},42887:(e,r,a)=>{"use strict";a.d(r,{Z:()=>t});let t=(0,a(76557).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},1570:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>H});var t=a(10326),s=a(17577),o=a(35047),i=a(86333),n=a(35659),c=a(89175),l=a(99837),d=a(94019),m=a(51896);let y={maxImageSize:5,maxImagesPerPost:9,allowedImageTypes:["image/jpeg","image/png","image/webp"]},g=()=>{try{return y}catch(e){return console.error("获取系统设置失败:",e),y}},p=e=>e>=1024?`${(e/1024).toFixed(1)}GB`:`${e}MB`,u=e=>{let r={"image/jpeg":"JPG","image/png":"PNG","image/webp":"WebP","image/gif":"GIF"};return e.map(e=>r[e]||e.replace("image/","").toUpperCase()).filter(Boolean).join("、")},h=(e,r)=>{if(!r.allowedImageTypes.includes(e.type)){let e=u(r.allowedImageTypes);return{valid:!1,error:`不支持的文件格式，请上传 ${e} 格式的图片`}}let a=1048576*r.maxImageSize;return e.size>a?{valid:!1,error:`文件大小超过限制，单张图片不能超过 ${p(r.maxImageSize)}`}:{valid:!0}};var f=a(28295);let x=({images:e,onImagesChange:r,maxImages:a=9,className:o,draftImages:i=[]})=>{let[n,c]=(0,s.useState)(()=>g());console.log("ImageUpload 组件渲染:",{imagesCount:e.length,draftImagesCount:i.length,draftImages:i.map(e=>e?e.substring(0,50)+"...":"null"),systemSettings:n});let[l,y]=(0,s.useState)(!1),x=(0,s.useRef)(null);(0,s.useEffect)(()=>{i.length>0&&(console.log("draftImages 更新:",i.length,"张图片"),y(e=>e))},[i]),(0,s.useEffect)(()=>{let e=()=>{c(g())};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[]);let C=t=>{if(!t)return;let s=[],o=[];Array.from(t).forEach(e=>{let r=h(e,n);r.valid?s.push(e):o.push(`${e.name}: ${r.error}`)}),o.length>0&&console.warn("文件验证失败:",o);let i=Math.min(a,n.maxImagesPerPost)-e.length,c=s.slice(0,i);c.length>0&&r([...e,...c]),s.length>i&&console.warn(`只能上传 ${i} 张图片，已忽略多余的文件`)},b=(e,r)=>{try{if(i[r])return console.log(`使用草稿图片数据 ${r}:`,i[r].substring(0,50)+"..."),i[r];if(e instanceof File&&e.size>0)return console.log(`使用File对象创建预览 ${r}:`,e.name,e.size),URL.createObjectURL(e);return console.log(`使用默认图片 ${r}`),"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center"}catch(e){if(console.error("获取图片预览URL失败:",e),i[r])return i[r];return"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center"}},N=a=>{r(e.filter((e,r)=>r!==a))};return(0,t.jsxs)("div",{className:(0,f.cn)("space-y-4",o),children:[(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-3",children:[e.map((e,r)=>(0,t.jsxs)("div",{className:"relative aspect-square",children:[t.jsx("img",{src:b(e,r),alt:`预览 ${r+1}`,className:"w-full h-full object-cover rounded-lg border border-gray-200",onError:e=>{e.currentTarget.src="https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center"}}),t.jsx("button",{onClick:()=>N(r),className:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors",children:t.jsx(d.Z,{className:"h-3 w-3"})}),t.jsx("div",{className:"absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded",children:r+1})]},r)),e.length<a&&(0,t.jsxs)("div",{onClick:()=>{x.current?.click()},onDragOver:e=>{e.preventDefault(),y(!0)},onDragLeave:e=>{e.preventDefault(),y(!1)},onDrop:e=>{e.preventDefault(),y(!1),C(e.dataTransfer.files)},className:(0,f.cn)("aspect-square border-2 border-dashed rounded-lg flex flex-col items-center justify-center cursor-pointer transition-colors",l?"border-primary-500 bg-primary-50":"border-gray-300 hover:border-primary-400 hover:bg-gray-50"),children:[t.jsx(m.Z,{className:"h-8 w-8 text-gray-400 mb-2"}),t.jsx("span",{className:"text-sm text-gray-500 text-center",children:0===e.length?"添加图片":"继续添加"}),(0,t.jsxs)("span",{className:"text-xs text-gray-400 mt-1",children:[e.length,"/",a]})]})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-500 space-y-1",children:[(0,t.jsxs)("p",{children:["• 最多可上传 ",Math.min(a,n.maxImagesPerPost)," 张图片"]}),(0,t.jsxs)("p",{children:["• 支持 ",u(n.allowedImageTypes)," 格式，单张图片不超过 ",p(n.maxImageSize)]}),t.jsx("p",{children:"• 第一张图片将作为封面显示"}),t.jsx("p",{children:"• 可拖拽图片到上传区域"})]}),t.jsx("input",{ref:x,type:"file",accept:n.allowedImageTypes.join(","),multiple:!0,onChange:e=>C(e.target.files),className:"hidden"})]})};var C=a(77636);let b=({value:e,onChange:r,error:a,userId:o})=>{let[i,n]=(0,s.useState)(""),[c,l]=(0,s.useState)([]),[d,m]=(0,s.useState)(!1),[y,g]=(0,s.useState)(""),p=["北京","上海","天津","重庆","广州","深圳","杭州","成都","武汉","西安","南京","苏州","长沙","郑州","济南","青岛","大连","沈阳","哈尔滨","长春","石家庄","太原","呼和浩特","兰州","西宁","银川","乌鲁木齐","合肥","福州","厦门","南昌","昆明","贵阳","拉萨","海口","南宁","宁波","温州","嘉兴","绍兴","金华","台州","丽水","湖州","衢州","舟山","无锡","常州","徐州","南通","连云港","淮安","盐城","扬州","镇江","泰州","宿迁"];(0,s.useEffect)(()=>{if(o){let a=localStorage.getItem(`address_${o}`);a&&(g(a),!e&&a&&(r(`#${a}`),n(a)))}},[o,r]),(0,s.useEffect)(()=>{e&&n(e.replace(/^#/,""))},[e]);let u=e=>{n(e),e.length>0?(l(p.filter(r=>r.includes(e)||r.toLowerCase().includes(e.toLowerCase())).slice(0,8)),m(!0)):(l([]),m(!1)),r(e?`#${e}`:"")},h=e=>{n(e),m(!1),r(`#${e}`)};return(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700",children:["发布地区 ",t.jsx("span",{className:"text-red-500",children:"*"})]}),y&&(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("div",{className:"text-xs text-gray-500",children:"您的常用地址："}),(0,t.jsxs)("button",{type:"button",onClick:()=>h(y),className:`px-3 py-1 text-sm rounded-full border transition-colors ${i===y?"bg-blue-500 text-white border-blue-500":"bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100"}`,children:["#",y]})]}),(0,t.jsxs)("div",{className:"relative",children:[t.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:t.jsx(C.Z,{className:"h-5 w-5 text-gray-400"})}),t.jsx("input",{type:"text",value:i,onChange:e=>u(e.target.value),onFocus:()=>i&&m(c.length>0),onBlur:()=>{setTimeout(()=>{m(!1)},200)},className:`block w-full pl-10 pr-3 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${a?"border-red-300":"border-gray-300"}`,placeholder:"输入发布地区，建议精确到县/区一级，如：北京市朝阳区"}),d&&c.length>0&&t.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto",children:c.map(e=>(0,t.jsxs)("button",{type:"button",onClick:()=>h(e),className:"w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center space-x-2",children:[t.jsx(C.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("span",{children:["#",e]})]},e))})]}),e&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-blue-50 rounded-lg",children:[t.jsx(C.Z,{className:"h-4 w-4 text-blue-600"}),(0,t.jsxs)("span",{className:"text-sm text-blue-700",children:["位置标签: ",t.jsx("span",{className:"font-medium",children:e})]})]}),a&&t.jsx("p",{className:"text-sm text-red-600",children:a}),t.jsx("div",{className:"text-xs text-gray-500",children:t.jsx("p",{children:"• 系统会自动添加#标签，便于搜索和筛选"})})]})};var N=a(39730),v=a(42887);let A=({value:e,onChange:r,petType:a="",required:o=!1,error:i,className:n})=>{let[l,d]=(0,s.useState)(()=>e.wechat?"wechat":e.phone?"phone":"wechat"),m=["breeding","selling","lost","wanted"].includes(a),y=o||m,g=[{type:"wechat",label:"微信号",icon:N.Z,placeholder:"请输入微信号",pattern:/^[a-zA-Z][\w-]{5,19}$/,errorMessage:"微信号格式：6-20位，字母开头，可包含字母、数字、下划线、减号"},{type:"phone",label:"手机号",icon:v.Z,placeholder:"请输入手机号",pattern:/^1[3-9]\d{9}$/,errorMessage:"请输入正确的手机号格式"}],p=g.find(e=>e.type===l),u=e[l]||"",h=a=>{if(d(a),!e[a]){let t={...e};g.forEach(e=>{e.type!==a&&delete t[e.type]}),r(t)}},x=a=>{let t={...e};g.forEach(e=>{e.type!==l&&delete t[e.type]}),a.trim()?t[l]=a.trim():delete t[l],r(t)},C=u.trim()?p.pattern.test(u.trim())?null:p.errorMessage:y?"请填写联系方式":null;return(0,t.jsxs)("div",{className:(0,f.cn)("space-y-4",n),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700",children:["联系方式 ",y&&t.jsx("span",{className:"text-red-500",children:"*"})]}),m&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["breeding"===a&&"配种需要联系方式","selling"===a&&"出售需要联系方式","lost"===a&&"寻回需要联系方式","wanted"===a&&"求购需要联系方式"]})]}),t.jsx("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg",children:g.map(e=>{let r=e.icon,a=l===e.type;return(0,t.jsxs)("button",{type:"button",onClick:()=>h(e.type),className:(0,f.cn)("flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-colors",a?"bg-white text-primary-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[t.jsx(r,{className:"h-4 w-4"}),t.jsx("span",{children:e.label})]},e.type)})}),t.jsx(c.I,{placeholder:p.placeholder,value:u,onChange:e=>x(e.target.value),leftIcon:t.jsx(p.icon,{className:"h-4 w-4"}),error:C||i,type:"phone"===l?"tel":"text"}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[!m&&t.jsx("p",{children:"• 如需要其他用户联系您，请填写联系方式"}),m&&t.jsx("p",{children:"• 请确保联系方式准确，以便买家/配种方/失主/卖家联系您"}),t.jsx("p",{children:"• 平台不会公开您的联系方式，仅在用户点击联系时显示"})]})]})};var w=a(76557);let j=(0,w.Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var _=a(71821),k=a(67427),P=a(88307);let D=(0,w.Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]),E=({value:e,onChange:r,gender:a="",onGenderChange:s,error:o,className:i})=>{let n=[{value:"",label:"展示分享",description:"分享宝贝照片，不涉及交易",icon:j,color:"gray"},{value:"selling",label:"出售",description:"出售宝贝，需要联系方式",icon:_.Z,color:"green"},{value:"breeding",label:"配种",description:"寻找配种对象，需要联系方式和性别",icon:k.Z,color:"pink"},{value:"lost",label:"寻回",description:"寻找走失宝贝，需要联系方式",icon:P.Z,color:"orange"},{value:"wanted",label:"求购",description:"发布求购信息，需要联系方式",icon:D,color:"purple"}],c=(e,r)=>{let a={gray:r?"bg-gray-100 border-gray-300 text-gray-900":"border-gray-200 text-gray-600 hover:border-gray-300",green:r?"bg-green-50 border-green-300 text-green-900":"border-gray-200 text-gray-600 hover:border-green-200",pink:r?"bg-pink-50 border-pink-300 text-pink-900":"border-gray-200 text-gray-600 hover:border-pink-200",orange:r?"bg-orange-50 border-orange-300 text-orange-900":"border-gray-200 text-gray-600 hover:border-orange-200",blue:r?"bg-blue-50 border-blue-300 text-blue-900":"border-gray-200 text-gray-600 hover:border-blue-200",purple:r?"bg-purple-50 border-purple-300 text-purple-900":"border-gray-200 text-gray-600 hover:border-purple-200"};return a[e]||a.gray};return(0,t.jsxs)("div",{className:(0,f.cn)("space-y-4",i),children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"发布类型"}),t.jsx("div",{className:"grid grid-cols-2 gap-3",children:n.map(a=>{let o=a.icon,i=e===a.value;return t.jsx("button",{type:"button",onClick:()=>{r(a.value),"breeding"!==a.value&&s&&s("")},className:(0,f.cn)("p-4 border-2 rounded-lg text-left transition-all duration-200",c(a.color,i),"hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"),children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[t.jsx(o,{className:"h-5 w-5 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[t.jsx("div",{className:"font-medium text-sm",children:a.label}),t.jsx("div",{className:"text-xs mt-1 opacity-75",children:a.description})]})]})},a.value)})}),"breeding"===e&&s&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700",children:["宝贝性别 ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx("div",{className:"flex space-x-3",children:[{value:"male",label:"雄性 ♂",color:"blue"},{value:"female",label:"雌性 ♀",color:"pink"}].map(e=>{let r=a===e.value;return t.jsx("button",{type:"button",onClick:()=>s(e.value),className:(0,f.cn)("flex-1 py-3 px-4 border-2 rounded-lg text-center transition-all duration-200",c(e.color,r),"hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"),children:t.jsx("div",{className:"font-medium text-sm",children:e.label})},e.value)})}),"breeding"===e&&!a&&t.jsx("p",{className:"text-sm text-red-600",children:"配种信息需要选择宝贝性别"})]}),o&&t.jsx("p",{className:"text-sm text-red-600",children:o}),t.jsx("div",{className:"text-xs text-gray-500",children:t.jsx("p",{children:"• 除展示分享外其他发布类型都需填写联系方式"})})]})};var S=a(20603),M=a(41828),I=a(88673);let T={柴犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"small_dogs",secondaryCategoryName:"小型犬",confidence:1},泰迪犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"small_dogs",secondaryCategoryName:"小型犬",confidence:1},贵宾犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"small_dogs",secondaryCategoryName:"小型犬",confidence:1},比熊犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"small_dogs",secondaryCategoryName:"小型犬",confidence:1},博美犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"small_dogs",secondaryCategoryName:"小型犬",confidence:1},吉娃娃:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"small_dogs",secondaryCategoryName:"小型犬",confidence:1},法国斗牛犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"small_dogs",secondaryCategoryName:"小型犬",confidence:1},巴哥犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"small_dogs",secondaryCategoryName:"小型犬",confidence:1},柯基犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"medium_dogs",secondaryCategoryName:"中型犬",confidence:1},边境牧羊犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"medium_dogs",secondaryCategoryName:"中型犬",confidence:1},可卡犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"medium_dogs",secondaryCategoryName:"中型犬",confidence:1},英国斗牛犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"medium_dogs",secondaryCategoryName:"中型犬",confidence:1},拉布拉多犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"large_dogs",secondaryCategoryName:"大型犬",confidence:1},金毛寻回犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"large_dogs",secondaryCategoryName:"大型犬",confidence:1},哈士奇:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"large_dogs",secondaryCategoryName:"大型犬",confidence:1},萨摩耶:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"large_dogs",secondaryCategoryName:"大型犬",confidence:1},阿拉斯加犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"large_dogs",secondaryCategoryName:"大型犬",confidence:1},德国牧羊犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"working_dogs",secondaryCategoryName:"工作犬",confidence:1},罗威纳犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"working_dogs",secondaryCategoryName:"工作犬",confidence:1},杜宾犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"working_dogs",secondaryCategoryName:"工作犬",confidence:1},中华田园犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},大黄狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},大白狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},大黑狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},花狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},草狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},唐狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},柴狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},大笨狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},太行犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},串串犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"mixed_dogs",secondaryCategoryName:"串串犬",confidence:1},混血犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"mixed_dogs",secondaryCategoryName:"串串犬",confidence:1},英国短毛猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"short_hair_cats",secondaryCategoryName:"短毛猫",confidence:1},美国短毛猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"short_hair_cats",secondaryCategoryName:"短毛猫",confidence:1},俄罗斯蓝猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"short_hair_cats",secondaryCategoryName:"短毛猫",confidence:1},暹罗猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"short_hair_cats",secondaryCategoryName:"短毛猫",confidence:1},银渐层:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"short_hair_cats",secondaryCategoryName:"短毛猫",confidence:1},金渐层:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"short_hair_cats",secondaryCategoryName:"短毛猫",confidence:1},波斯猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"long_hair_cats",secondaryCategoryName:"长毛猫",confidence:1},布偶猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"long_hair_cats",secondaryCategoryName:"长毛猫",confidence:1},缅因猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"long_hair_cats",secondaryCategoryName:"长毛猫",confidence:1},挪威森林猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"long_hair_cats",secondaryCategoryName:"长毛猫",confidence:1},狸花猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"domestic_cats",secondaryCategoryName:"田园猫",confidence:1},橘猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"domestic_cats",secondaryCategoryName:"田园猫",confidence:1},三花猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"domestic_cats",secondaryCategoryName:"田园猫",confidence:1},奶牛猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"domestic_cats",secondaryCategoryName:"田园猫",confidence:1},中华田园猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"domestic_cats",secondaryCategoryName:"田园猫",confidence:1},虎皮鹦鹉:{primaryCategory:"birds",primaryCategoryName:"鸟类",secondaryCategory:"parrots",secondaryCategoryName:"鸦鹉类",confidence:1},玄凤鹦鹉:{primaryCategory:"birds",primaryCategoryName:"鸟类",secondaryCategory:"parrots",secondaryCategoryName:"鸦鹉类",confidence:1},牡丹鹦鹉:{primaryCategory:"birds",primaryCategoryName:"鸟类",secondaryCategory:"parrots",secondaryCategoryName:"鸦鹉类",confidence:1},金丝雀:{primaryCategory:"birds",primaryCategoryName:"鸟类",secondaryCategory:"songbirds",secondaryCategoryName:"鸣禽类",confidence:1},文鸟:{primaryCategory:"birds",primaryCategoryName:"鸟类",secondaryCategory:"songbirds",secondaryCategoryName:"鸣禽类",confidence:1},金鱼:{primaryCategory:"aquatic",primaryCategoryName:"水族",secondaryCategory:"freshwater_fish",secondaryCategoryName:"淡水鱼",confidence:1},锦鲤:{primaryCategory:"aquatic",primaryCategoryName:"水族",secondaryCategory:"freshwater_fish",secondaryCategoryName:"淡水鱼",confidence:1},孔雀鱼:{primaryCategory:"aquatic",primaryCategoryName:"水族",secondaryCategory:"freshwater_fish",secondaryCategoryName:"淡水鱼",confidence:1},斗鱼:{primaryCategory:"aquatic",primaryCategoryName:"水族",secondaryCategory:"freshwater_fish",secondaryCategoryName:"淡水鱼",confidence:1},仓鼠:{primaryCategory:"small_pets",primaryCategoryName:"小宠",secondaryCategory:"hamsters",secondaryCategoryName:"仓鼠类",confidence:1},龙猫:{primaryCategory:"small_pets",primaryCategoryName:"小宠",secondaryCategory:"small_mammals",secondaryCategoryName:"小型哺乳",confidence:1},兔子:{primaryCategory:"small_pets",primaryCategoryName:"小宠",secondaryCategory:"rabbits",secondaryCategoryName:"兔子类",confidence:1},荷兰猪:{primaryCategory:"small_pets",primaryCategoryName:"小宠",secondaryCategory:"rodent_pets",secondaryCategoryName:"鼠类宠物",confidence:1},巴西龟:{primaryCategory:"reptiles",primaryCategoryName:"爬宠",secondaryCategory:"turtles",secondaryCategoryName:"龟类",confidence:1},草龟:{primaryCategory:"reptiles",primaryCategoryName:"爬宠",secondaryCategory:"turtles",secondaryCategoryName:"龟类",confidence:1},蜥蜴:{primaryCategory:"reptiles",primaryCategoryName:"爬宠",secondaryCategory:"lizards",secondaryCategoryName:"蜥蜴类",confidence:1},守宫:{primaryCategory:"reptiles",primaryCategoryName:"爬宠",secondaryCategory:"lizards",secondaryCategoryName:"蜥蜴类",confidence:1}},$={拉拉:"拉布拉多犬",拉布拉多:"拉布拉多犬",金毛:"金毛寻回犬",泰迪:"泰迪犬",贵宾:"贵宾犬",比熊:"比熊犬",博美:"博美犬",二哈:"哈士奇",萨摩:"萨摩耶",边牧:"边境牧羊犬",德牧:"德国牧羊犬",法斗:"法国斗牛犬",英斗:"英国斗牛犬",田园犬:"中华田园犬",土狗:"中华田园犬",本地狗:"中华田园犬",黄狗:"大黄狗",白狗:"大白狗",黑狗:"大黑狗",花花狗:"花狗",土狗子:"中华田园犬",英短:"英国短毛猫",美短:"美国短毛猫",布偶:"布偶猫",缅因:"缅因猫",俄蓝:"俄罗斯蓝猫",狸花:"狸花猫",三花:"三花猫",奶牛:"奶牛猫",虎皮:"虎皮鹦鹉",玄凤:"玄凤鹦鹉",牡丹:"牡丹鹦鹉",天竺鼠:"荷兰猪"},Z={狗狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",confidence:.8,isGeneralCategory:!0},狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",confidence:.8,isGeneralCategory:!0},犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",confidence:.8,isGeneralCategory:!0},猫咪:{primaryCategory:"cats",primaryCategoryName:"猫咪",confidence:.8,isGeneralCategory:!0},猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",confidence:.8,isGeneralCategory:!0},鸟类:{primaryCategory:"birds",primaryCategoryName:"鸟类",confidence:.8,isGeneralCategory:!0},鸟:{primaryCategory:"birds",primaryCategoryName:"鸟类",confidence:.8,isGeneralCategory:!0},水族:{primaryCategory:"aquatic",primaryCategoryName:"水族",confidence:.8,isGeneralCategory:!0},鱼:{primaryCategory:"aquatic",primaryCategoryName:"水族",confidence:.8,isGeneralCategory:!0},小宠:{primaryCategory:"small_pets",primaryCategoryName:"小宠",confidence:.8,isGeneralCategory:!0},爬宠:{primaryCategory:"reptiles",primaryCategoryName:"爬宠",confidence:.8,isGeneralCategory:!0},两栖:{primaryCategory:"amphibians",primaryCategoryName:"两栖",confidence:.8,isGeneralCategory:!0},昆虫:{primaryCategory:"insects",primaryCategoryName:"昆虫",confidence:.8,isGeneralCategory:!0}};var L=a(85137);let B="pet_breed_input_history",O=({value:e,onChange:r,error:a,placeholder:o="请输入宠物品种，如：拉布拉多、英短蓝猫等"})=>{let[i,n]=(0,s.useState)(null),[c,l]=(0,s.useState)([]),[d,m]=(0,s.useState)(!1),y=()=>{try{let e=localStorage.getItem(B);return e?JSON.parse(e):[]}catch{return[]}},g=e=>{try{localStorage.setItem(B,JSON.stringify(e))}catch{}},p=e=>{if(!e.trim())return;let r=[e,...y().filter(r=>r!==e)].slice(0,10);l(r),g(r)};(0,s.useEffect)(()=>{l(y())},[]),(0,s.useEffect)(()=>{if(!e||!e.trim()){n(null),r(e,null);return}let a=function(e){if(!e||!e.trim())return null;let r=e.trim();if(T[r])return T[r];let a=$[r];if(a&&T[a])return T[a];if(Z[r])return Z[r];for(let[e,a]of Object.entries(T))if(e.includes(r)||r.includes(e))return{...a,confidence:.9};for(let[e,a]of Object.entries($))if(e.includes(r)||r.includes(e))return{...T[a],confidence:.9};for(let[e,a]of Object.entries(Z))if(e.includes(r)||r.includes(e))return{...a,confidence:.7};return{primaryCategory:"others",primaryCategoryName:"其他",confidence:.5}}(e);n(a),r(e,a)},[e]);let u=e=>{r(e,null),m(!1)},h=()=>{e&&e.trim()&&p(e.trim())},f=(0,L.O)(()=>{m(!1)});return(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("div",{className:"flex-1",children:[t.jsx("input",{type:"text",value:e,onChange:e=>{r(e.target.value,null)},onKeyDown:e=>{"Enter"===e.key&&(e.preventDefault(),h())},onBlur:h,placeholder:o,className:`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${a?"border-red-500":"border-gray-300"}`}),a&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:a})]}),(0,t.jsxs)("div",{className:"relative",ref:f,children:[t.jsx("button",{type:"button",onClick:()=>{m(!d)},className:"px-3 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",title:"查看输入历史",children:t.jsx("svg",{className:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),d&&c.length>0&&(0,t.jsxs)("div",{className:"absolute right-0 top-full mt-1 w-64 bg-white border border-gray-300 rounded-lg shadow-lg z-10",children:[(0,t.jsxs)("div",{className:"px-4 py-2 bg-gray-50 border-b border-gray-200 flex items-center justify-between",children:[(0,t.jsxs)("span",{className:"text-sm text-gray-600 flex items-center",children:[t.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"输入历史"]}),t.jsx("button",{onClick:()=>{l([]),g([]),m(!1)},className:"text-xs text-red-500 hover:text-red-700 transition-colors",children:"清除"})]}),t.jsx("div",{className:"max-h-48 overflow-y-auto",children:c.map((e,r)=>t.jsx("div",{onClick:()=>u(e),className:"px-4 py-2 cursor-pointer hover:bg-gray-50 transition-colors",children:t.jsx("span",{className:"text-sm",children:e})},`${e}-${r}`))})]})]})]}),i&&(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:[t.jsx("div",{className:"flex items-center text-sm text-green-800",children:t.jsx("span",{className:"font-medium",children:"智能分类结果："})}),(0,t.jsxs)("div",{className:"mt-1 flex items-center",children:[t.jsx("span",{className:"text-green-600 mr-2",children:"✅"}),(0,t.jsxs)("span",{className:"font-medium text-green-800",children:[i.primaryCategoryName," > ",i.secondaryCategoryName||"其他"]}),(0,t.jsxs)("span",{className:"ml-2 text-green-600",children:["(",Math.round(100*i.confidence),"%匹配)"]})]}),t.jsx("div",{className:"mt-1 text-sm text-green-700",children:i.isGeneralCategory?"\uD83D\uDCA1 输入具体品种可以让其他用户更精准地找到您的宝贝":"\uD83D\uDCA1 分类识别成功！"})]}),!e&&(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[t.jsx("p",{children:"\uD83D\uDCA1 输入具体品种有助于其他用户更快找到您的宝贝"}),c.length>0&&t.jsx("p",{children:"\uD83D\uDD52 点击历史记录按钮查看之前的输入"})]})]})},G=()=>new Promise(e=>{let r=new Image;r.onload=()=>e(!0),r.onerror=()=>e(!1),r.src="data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A="}),R=async()=>{if(await G())return"avif";let e=document.createElement("canvas");return(e.width=1,e.height=1,0===e.toDataURL("image/webp").indexOf("data:image/webp"))?"webp":"jpeg"},z=async(e,r={})=>{let{maxWidth:a=1e3,maxHeight:t=1e3,quality:s=.75,outputFormat:o="auto",generateThumbnails:i=!1,thumbnailSizes:n=[150,300,600]}=r;return new Promise((r,c)=>{let l=document.createElement("canvas"),d=l.getContext("2d"),m=new Image;m.onload=async()=>{try{let{width:c,height:y}=m,{targetWidth:g,targetHeight:p,targetQuality:u}=q(c,y,a,t,s),h="auto"===o?await R():o,f=`image/${h}`;console.log(`选择的图片格式: ${h}`),l.width=g,l.height=p,d&&(d.imageSmoothingEnabled=!0,d.imageSmoothingQuality="high",d.drawImage(m,0,0,g,p));let x=await F(l,e.name,f,u,h),C=[];i&&(C=await U(m,e.name,n,h));let b=Math.round((1-x.size/e.size)*100);r({original:e,compressed:x,thumbnails:C,compressionRatio:b,outputFormat:h})}catch(e){c(e)}},m.onerror=()=>c(Error("图片加载失败")),m.src=URL.createObjectURL(e)})},q=(e,r,a,t,s)=>{let o=e,i=r,n=s,c=e/r;if(e>2e3||r>2e3||e*r*3>5e6){let c=Math.min(a/e,t/r,.6);o=Math.floor(e*c),i=Math.floor(r*c),n=Math.max(.6,s-.15)}else e>a||r>t?(c>1?i=(o=a)/c:o=(i=t)*c,n=Math.max(.65,s-.1)):e>800||r>800?(o=Math.floor(.8*e),i=Math.floor(.8*r),n=Math.max(.7,s-.05)):n=Math.max(.8,s);return{targetWidth:o,targetHeight:i,targetQuality:n}},F=(e,r,a,t,s)=>new Promise((o,i)=>{e.toBlob(e=>{e?o(new File([e],r.replace(/\.[^/.]+$/,`.${s}`),{type:a,lastModified:Date.now()})):i(Error("Canvas转换失败"))},a,t)}),U=async(e,r,a,t)=>{let s=[],o=`image/${t}`;for(let i of a){let a=document.createElement("canvas"),n=a.getContext("2d"),{width:c,height:l}=e,d=c/l,m=i,y=i;d>1?y=i/d:m=i*d,a.width=m,a.height=y,n&&(n.imageSmoothingEnabled=!0,n.imageSmoothingQuality="high",n.drawImage(e,0,0,m,y));let g=await F(a,r.replace(/\.[^/.]+$/,`_thumb_${i}.${t}`),o,.8,t);s.push(g)}return s};var W=a(89453);let H=()=>{let e=(0,o.useRouter)(),r=(0,o.useSearchParams)().get("draftId"),{user:d}=(0,n.E)(),[m,y]=(0,s.useState)(!1),[g,p]=(0,s.useState)({title:"",description:"",images:[],category:"",breed:"",type:"",gender:"",location:"",contact_info:{phone:"",wechat:""}}),[u,h]=(0,s.useState)([]),[f,C]=(0,s.useState)(null),[N,v]=(0,s.useState)({}),[w,j]=(0,s.useState)(!1),[_,k]=(0,s.useState)(0),[P,D]=(0,s.useState)(!1),[T,$]=(0,s.useState)(!1),[Z,L]=(0,s.useState)(!1);(0,s.useEffect)(()=>{},[r]),(0,s.useEffect)(()=>{if(d?._id&&!r){let e=localStorage.getItem(`contact_${d._id}`);if(e)try{let r=JSON.parse(e),a={phone:"phone"===r.type?r.value:"",wechat:"wechat"===r.type?r.value:""};p(e=>({...e,contact_info:a}))}catch(e){console.error("解析联系方式失败:",e)}}},[d?._id,r]);let B=(e,r)=>{p(a=>({...a,[e]:r})),N[e]&&v(r=>({...r,[e]:""}))},G=()=>{let e={};0===g.images.length&&(e.images="请至少上传一张图片"),g.breed&&g.breed.trim()||(e.breed="请输入宠物品种"),g.description.trim()?g.description.length>500&&(e.description="描述不能超过500个字符"):e.description="请输入宝贝描述","breeding"!==g.type||g.gender||(e.gender="配种信息需要选择宝贝性别"),g.location&&g.location.trim()?g.location.startsWith("#")||(e.location="位置格式错误，请重新选择"):e.location="请选择或输入发布地区";let r=["breeding","selling","lost","wanted"].includes(g.type||""),{phone:a,wechat:t}=g.contact_info||{};return r&&!(a||t)&&(e.contact="该类型发布需要填写联系方式"),v(e),0===Object.keys(e).length},R=async e=>{let r=Date.now(),a=e.map(async(r,a)=>{try{console.log(`开始优化图片 ${a+1}/${e.length}: ${r.name}`);let t=await z(r,{maxWidth:1200,maxHeight:1200,quality:.85,outputFormat:"auto"});return console.log(`图片 ${r.name} 优化完成，压缩率: ${t.compressionRatio}%`),console.log(`格式: ${t.outputFormat}, 原始大小: ${r.size}, 压缩后: ${t.compressed.size}`),await (0,M.uploadFile)(t.compressed)}catch(e){return console.error(`图片 ${r.name} 优化失败，使用原图:`,e),await (0,M.uploadFile)(r)}}),t=await Promise.all(a),s=Date.now()-r;return W.Bm.recordApiResponse("imageUpload",s),console.log(`所有图片上传完成，总耗时: ${s}ms`),t},q=async()=>{if(console.log("\uD83D\uDE80 开始发布，当前表单数据:",g),console.log("\uD83D\uDD0D 开始表单验证..."),!G()){console.log("❌ 表单验证失败"),S.C.error("请检查表单信息");return}console.log("✅ 表单验证通过，开始发布...");try{y(!0),S.C.loading("正在上传图片...");let r=await R(g.images);S.C.dismiss(),S.C.loading("正在发布...");let t={title:g.breed?.trim()||"宠物发布",description:g.description.trim(),images:r,category:g.category,breed:g.breed?.trim()||void 0,type:g.type||void 0,gender:"breeding"===g.type?g.gender:void 0,location:g.location?.trim()||void 0,contact_info:{phone:g.contact_info?.phone?.trim()||void 0,wechat:g.contact_info?.wechat?.trim()||void 0}},s=await M.petAPI.createPost(t);if(s.success){S.C.dismiss(),S.C.success("发布成功！");try{let{dataCache:e}=await a.e(746).then(a.bind(a,12670));e.clear(),["posts_all",`posts_${t.category}`,`posts_${t.type}`,"posts_latest"].forEach(r=>e.delete(r)),console.log("✅ 缓存已清理，新帖子将立即显示")}catch(e){console.warn("清理缓存失败:",e)}setTimeout(()=>{e.push("/")},1500)}else throw Error(s.message||"发布失败")}catch(e){console.error("发布失败:",e),S.C.dismiss(),S.C.error(e.message||"发布失败，请重试")}finally{y(!1)}},F=async()=>{if(!g.breed?.trim()){S.C.error("请至少填写宠物品种");return}try{y(!0),r?(await (0,I.XD)(r,g),S.C.success("草稿已更新")):(await (0,I.DO)(g),S.C.success("已保存到待发布")),e.back()}catch(e){console.error("保存草稿失败:",e),S.C.error("保存失败，请重试")}finally{y(!1)}};return t.jsx(n.Y,{children:(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[t.jsx("div",{className:"bg-white border-b border-gray-200 sticky top-0 z-10",children:t.jsx("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8",children:t.jsx("div",{className:"flex items-center h-16",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[t.jsx("button",{onClick:()=>e.back(),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:t.jsx(i.Z,{className:"h-5 w-5"})}),t.jsx("h1",{className:"text-lg font-semibold text-gray-900",children:"发布宝贝"})]})})})}),t.jsx("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:["宝贝图片 ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx(x,{images:g.images,onImagesChange:e=>B("images",e),draftImages:u}),N.images&&t.jsx("p",{className:"mt-1 text-sm text-red-600",children:N.images})]}),t.jsx(O,{value:g.breed||"",onChange:(e,r)=>{let a=r?.secondaryCategory||r?.primaryCategory||"";p(r=>({...r,breed:e,category:a})),C(r),N.breed&&v(e=>({...e,breed:""})),N.category&&v(e=>({...e,category:""}))},error:N.breed,placeholder:"请输入宠物品种，如：拉布拉多、英短蓝猫、虎皮鹦鹉等"}),t.jsx(c.g,{label:"宝贝描述",placeholder:"请简洁扼要的描述您宝贝的不凡",value:g.description,onChange:e=>B("description",e.target.value),error:N.description,rows:4,maxLength:500,required:!0}),t.jsx(b,{value:g.location||"",onChange:e=>B("location",e),error:N.location,userId:d?._id}),t.jsx(E,{value:g.type||"",onChange:e=>B("type",e),gender:g.gender||"",onGenderChange:e=>B("gender",e),error:N.gender}),t.jsx(A,{value:g.contact_info||{},onChange:e=>{p(r=>({...r,contact_info:e})),N.contact&&v(e=>({...e,contact:""}))},petType:g.type,error:N.contact}),(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-4",children:[(0,t.jsxs)("h4",{className:"font-medium text-red-900 mb-2 flex items-center",children:[t.jsx("span",{className:"text-red-500 mr-2",children:"⚠️"}),"法律提醒"]}),t.jsx("p",{className:"text-sm text-red-800 font-medium",children:"请遵守国家法律，禁止出售受保护的野生动物"})]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[t.jsx("h4",{className:"font-medium text-blue-900 mb-2",children:"发布须知"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[t.jsx("li",{children:"• 请确保发布信息真实有效，如实描述物品状况"}),t.jsx("li",{children:"• 禁止发布血腥、暴力、违法违规内容"}),t.jsx("li",{children:"• 交易时请注意安全，建议当面验货交易"}),t.jsx("li",{children:"• 平台仅提供信息展示服务，不参与具体交易过程"}),t.jsx("li",{children:"• 用户需自行承担交易风险，请谨慎甄别"})]})]}),t.jsx("div",{className:"pt-4 space-y-3",children:(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-3",children:[t.jsx(l.Z,{onClick:()=>{e.back()},variant:"outline",disabled:m,className:"w-full",children:"取消发布"}),t.jsx(l.Z,{onClick:F,variant:"outline",disabled:m,className:"w-full",children:"保存到待发布"}),t.jsx(l.Z,{onClick:q,loading:m,disabled:m,className:"w-full",children:m?"发布中...":"发布宝贝"})]})})]})})]})})}},99837:(e,r,a)=>{"use strict";a.d(r,{Z:()=>c});var t=a(10326),s=a(17577),o=a.n(s),i=a(28295);let n=o().forwardRef(({className:e,variant:r="primary",size:a="md",loading:s=!1,icon:o,children:n,disabled:c,...l},d)=>(0,t.jsxs)("button",{className:(0,i.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[r],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[a],e),ref:d,disabled:c||s,...l,children:[s&&(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[t.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),t.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!s&&o&&t.jsx("span",{className:"mr-2",children:o}),n]}));n.displayName="Button";let c=n},89175:(e,r,a)=>{"use strict";a.d(r,{I:()=>n,g:()=>c});var t=a(10326),s=a(17577),o=a.n(s),i=a(28295);let n=o().forwardRef(({className:e,type:r="text",label:a,error:s,helperText:o,leftIcon:n,rightIcon:c,variant:l="default",...d},m)=>{let y=s?"border-red-500 focus:ring-red-500 focus:border-red-500":"";return(0,t.jsxs)("div",{className:"w-full",children:[a&&t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:a}),(0,t.jsxs)("div",{className:"relative",children:[n&&t.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:t.jsx("span",{className:"text-gray-400",children:n})}),t.jsx("input",{type:r,className:(0,i.cn)("w-full px-3 py-2 text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{default:"border border-gray-300 rounded-md bg-white focus:ring-primary-500 focus:border-primary-500",filled:"border-0 bg-gray-100 rounded-md focus:ring-primary-500 focus:bg-white",outline:"border-2 border-gray-200 rounded-md bg-transparent focus:ring-primary-500 focus:border-primary-500"}[l],y,n&&"pl-10",c&&"pr-10",e),ref:m,...d}),c&&t.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:t.jsx("span",{className:"text-gray-400",children:c})})]}),(s||o)&&t.jsx("p",{className:(0,i.cn)("mt-1 text-xs",s?"text-red-600":"text-gray-500"),children:s||o})]})});n.displayName="Input";let c=o().forwardRef(({className:e,label:r,error:a,helperText:s,variant:o="default",...n},c)=>{let l=a?"border-red-500 focus:ring-red-500 focus:border-red-500":"";return(0,t.jsxs)("div",{className:"w-full",children:[r&&t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:r}),t.jsx("textarea",{className:(0,i.cn)("w-full px-3 py-2 text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed resize-none",{default:"border border-gray-300 rounded-md bg-white focus:ring-primary-500 focus:border-primary-500",filled:"border-0 bg-gray-100 rounded-md focus:ring-primary-500 focus:bg-white",outline:"border-2 border-gray-200 rounded-md bg-transparent focus:ring-primary-500 focus:border-primary-500"}[o],l,e),ref:c,...n}),(a||s)&&t.jsx("p",{className:(0,i.cn)("mt-1 text-xs",a?"text-red-600":"text-gray-500"),children:a||s})]})});c.displayName="Textarea"},85137:(e,r,a)=>{"use strict";a.d(r,{O:()=>s});var t=a(17577);function s(e){return(0,t.useRef)(null)}},88673:(e,r,a)=>{"use strict";a.d(r,{DO:()=>o,Oe:()=>t,XD:()=>i});let t=()=>{try{return[]}catch(e){return console.error("获取草稿失败:",e),[]}},s=async e=>{let r=[];for(let a of e)if(a instanceof File)try{let e=await new Promise((e,r)=>{let t=new FileReader;t.onload=()=>e(t.result),t.onerror=r,t.readAsDataURL(a)});r.push(e)}catch(e){console.error("转换图片失败:",e),r.push("https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center")}else"string"==typeof a&&r.push(a);return r},o=async e=>{try{throw Error("无法在服务器端保存草稿")}catch(e){throw console.error("保存草稿失败:",e),Error("保存草稿失败")}},i=async(e,r)=>{try{let a=t(),o=a.findIndex(r=>r.id===e);if(-1!==o){let e=await s(r.images);a[o]={...a[o],...r,images:e,updated_at:new Date().toISOString()},localStorage.setItem("petDrafts",JSON.stringify(a))}}catch(e){throw console.error("更新草稿失败:",e),Error("更新草稿失败")}}},89453:(e,r,a)=>{"use strict";a.d(r,{Bm:()=>s});class t{constructor(){this.observers=[],this.metrics={pageLoadTime:0,firstContentfulPaint:0,largestContentfulPaint:0,firstInputDelay:0,cumulativeLayoutShift:0,imageLoadTimes:[],apiResponseTimes:new Map},this.initializeObservers()}static getInstance(){return t.instance||(t.instance=new t),t.instance}initializeObservers(){}observeNavigation(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{"navigation"===e.entryType&&(this.metrics.pageLoadTime=e.loadEventEnd-e.fetchStart)})});e.observe({entryTypes:["navigation"]}),this.observers.push(e)}}observePaint(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{"first-contentful-paint"===e.name&&(this.metrics.firstContentfulPaint=e.startTime)})});e.observe({entryTypes:["paint"]}),this.observers.push(e)}if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{let r=e.getEntries(),a=r[r.length-1];this.metrics.largestContentfulPaint=a.startTime});e.observe({entryTypes:["largest-contentful-paint"]}),this.observers.push(e)}}observeLayoutShift(){if("PerformanceObserver"in window){let e=0,r=new PerformanceObserver(r=>{r.getEntries().forEach(r=>{r.hadRecentInput||(e+=r.value,this.metrics.cumulativeLayoutShift=e)})});r.observe({entryTypes:["layout-shift"]}),this.observers.push(r)}}observeFirstInputDelay(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{this.metrics.firstInputDelay=e.processingStart-e.startTime})});e.observe({entryTypes:["first-input"]}),this.observers.push(e)}}observeResources(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{if("img"===e.initiatorType){let r=e.responseEnd-e.startTime;this.metrics.imageLoadTimes.push(r)}})});e.observe({entryTypes:["resource"]}),this.observers.push(e)}}recordApiResponse(e,r){this.metrics.apiResponseTimes.has(e)||this.metrics.apiResponseTimes.set(e,[]),this.metrics.apiResponseTimes.get(e).push(r)}recordImageLoad(e){this.metrics.imageLoadTimes.push(e)}getMemoryUsage(){return"memory"in performance?performance.memory:null}getMetrics(){return{...this.metrics,memoryUsage:this.getMemoryUsage()||void 0}}getPerformanceReport(){let e={};this.metrics.apiResponseTimes.forEach((r,a)=>{let t=r.reduce((e,r)=>e+r,0)/r.length;e[a]={avgResponseTime:Math.round(t),callCount:r.length,maxResponseTime:Math.round(Math.max(...r)),minResponseTime:Math.round(Math.min(...r))}});let r=this.metrics.imageLoadTimes.length>0?this.metrics.imageLoadTimes.reduce((e,r)=>e+r,0)/this.metrics.imageLoadTimes.length:0;return{coreWebVitals:{lcp:Math.round(this.metrics.largestContentfulPaint),fid:Math.round(this.metrics.firstInputDelay),cls:Math.round(1e3*this.metrics.cumulativeLayoutShift)/1e3},loadingPerformance:{pageLoadTime:Math.round(this.metrics.pageLoadTime),fcp:Math.round(this.metrics.firstContentfulPaint),avgImageLoadTime:Math.round(r)},apiPerformance:e,memoryUsage:this.getMemoryUsage()||void 0}}getPerformanceScore(){let e=this.metrics.largestContentfulPaint<=2500?100:this.metrics.largestContentfulPaint<=4e3?50:0,r=this.metrics.firstInputDelay<=100?100:this.metrics.firstInputDelay<=300?50:0,a=this.metrics.cumulativeLayoutShift<=.1?100:this.metrics.cumulativeLayoutShift<=.25?50:0;return{overall:Math.round((e+r+a)/3),breakdown:{loading:e,interactivity:r,visualStability:a}}}cleanup(){this.observers.forEach(e=>e.disconnect()),this.observers=[]}exportData(){return JSON.stringify({timestamp:new Date().toISOString(),url:window.location.href,userAgent:navigator.userAgent,metrics:this.getMetrics(),report:this.getPerformanceReport(),score:this.getPerformanceScore()},null,2)}}let s=t.getInstance()},64929:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>t});let t=(0,a(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\upload\page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[276,201,240],()=>a(79794));module.exports=t})();