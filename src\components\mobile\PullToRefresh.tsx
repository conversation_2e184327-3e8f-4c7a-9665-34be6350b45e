'use client';

import { useState, useRef, useEffect, ReactNode } from 'react';
import { RefreshCw } from 'lucide-react';

interface PullToRefreshProps {
  children: ReactNode;
  onRefresh: () => Promise<void>;
  threshold?: number;
  className?: string;
}

const PullToRefresh: React.FC<PullToRefreshProps> = ({
  children,
  onRefresh,
  threshold = 80,
  className = ''
}) => {
  const [pullDistance, setPullDistance] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [canRefresh, setCanRefresh] = useState(false);
  
  const touchStartRef = useRef<number>(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const isAtTopRef = useRef(true);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      isAtTopRef.current = container.scrollTop === 0;
    };

    const handleTouchStart = (e: TouchEvent) => {
      if (!isAtTopRef.current || isRefreshing) return;
      touchStartRef.current = e.touches[0].clientY;
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (!isAtTopRef.current || isRefreshing) return;
      
      const currentY = e.touches[0].clientY;
      const distance = currentY - touchStartRef.current;
      
      if (distance > 0) {
        // 向下拉动
        e.preventDefault();
        const pullDist = Math.min(distance * 0.5, threshold * 1.5);
        setPullDistance(pullDist);
        setCanRefresh(pullDist >= threshold);
      }
    };

    const handleTouchEnd = async () => {
      if (!isAtTopRef.current || isRefreshing) return;
      
      if (canRefresh && pullDistance >= threshold) {
        setIsRefreshing(true);
        try {
          await onRefresh();
        } catch (error) {
          console.error('刷新失败:', error);
        } finally {
          setIsRefreshing(false);
        }
      }
      
      setPullDistance(0);
      setCanRefresh(false);
    };

    container.addEventListener('scroll', handleScroll, { passive: true });
    container.addEventListener('touchstart', handleTouchStart, { passive: true });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      container.removeEventListener('scroll', handleScroll);
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
    };
  }, [canRefresh, pullDistance, threshold, onRefresh, isRefreshing]);

  const refreshIndicatorHeight = Math.min(pullDistance, threshold);
  const refreshOpacity = Math.min(pullDistance / threshold, 1);

  return (
    <div className={`relative ${className}`}>
      {/* 刷新指示器 */}
      <div 
        className="absolute top-0 left-0 right-0 flex items-center justify-center bg-gray-50 transition-all duration-200 ease-out z-10"
        style={{ 
          height: `${refreshIndicatorHeight}px`,
          opacity: refreshOpacity,
          transform: `translateY(-${threshold - refreshIndicatorHeight}px)`
        }}
      >
        <div className="flex items-center space-x-2 text-gray-600">
          <RefreshCw 
            className={`w-5 h-5 ${isRefreshing ? 'animate-spin' : ''} ${canRefresh ? 'text-blue-500' : ''}`} 
          />
          <span className="text-sm">
            {isRefreshing ? '刷新中...' : canRefresh ? '松开刷新' : '下拉刷新'}
          </span>
        </div>
      </div>

      {/* 内容容器 */}
      <div 
        ref={containerRef}
        className="h-full overflow-auto"
        style={{
          transform: `translateY(${pullDistance > 0 ? pullDistance : 0}px)`,
          transition: pullDistance === 0 ? 'transform 0.2s ease-out' : 'none'
        }}
      >
        {children}
      </div>
    </div>
  );
};

export default PullToRefresh;
