// 城市数据类型
export interface City {
  name: string;
  code: string;
  lat: number;
  lng: number;
  isHot?: boolean;
}

// 扩展的地区类型（支持省市县）
export interface Region {
  name: string;
  code: string;
  lat?: number;
  lng?: number;
  fullName?: string; // 完整名称，如"广东省 广州市 天河区"
  level?: 'province' | 'city' | 'district'; // 行政级别
}

// 热门城市数据
export const hotCities: City[] = [
  { name: '北京市', code: '110000', lat: 39.9042, lng: 116.4074, isHot: true },
  { name: '上海市', code: '310000', lat: 31.2304, lng: 121.4737, isHot: true },
  { name: '广州市', code: '440100', lat: 23.1291, lng: 113.2644, isHot: true },
  { name: '深圳市', code: '440300', lat: 22.5431, lng: 114.0579, isHot: true },
  { name: '杭州市', code: '330100', lat: 30.2741, lng: 120.1551, isHot: true },
  { name: '成都市', code: '510100', lat: 30.5728, lng: 104.0668, isHot: true },
  { name: '武汉市', code: '420100', lat: 30.5928, lng: 114.3055, isHot: true },
  { name: '西安市', code: '610100', lat: 34.3416, lng: 108.9398, isHot: true },
  { name: '南京市', code: '320100', lat: 32.0603, lng: 118.7969, isHot: true },
  { name: '天津市', code: '120000', lat: 39.3434, lng: 117.3616, isHot: true },
  { name: '苏州市', code: '320500', lat: 31.2989, lng: 120.5853, isHot: true },
  { name: '重庆市', code: '500000', lat: 29.5647, lng: 106.5507, isHot: true },
];

// 全部城市数据（按拼音首字母分组）
export const allCities: Record<string, City[]> = {
  'A': [
    { name: '安庆市', code: '340800', lat: 30.5255, lng: 117.0568 },
    { name: '安阳市', code: '410500', lat: 36.1034, lng: 114.3924 },
    { name: '鞍山市', code: '210300', lat: 41.1087, lng: 122.9951 },
  ],
  'B': [
    { name: '北京市', code: '110000', lat: 39.9042, lng: 116.4074 },
    { name: '包头市', code: '150200', lat: 40.6562, lng: 109.8403 },
    { name: '保定市', code: '130600', lat: 38.8738, lng: 115.4648 },
    { name: '宝鸡市', code: '610300', lat: 34.3640, lng: 107.2372 },
  ],
  'C': [
    { name: '成都市', code: '510100', lat: 30.5728, lng: 104.0668 },
    { name: '长沙市', code: '430100', lat: 28.2282, lng: 112.9388 },
    { name: '长春市', code: '220100', lat: 43.8171, lng: 125.3235 },
    { name: '常州市', code: '320400', lat: 31.7976, lng: 119.9462 },
  ],
  'D': [
    { name: '大连市', code: '210200', lat: 38.9140, lng: 121.6147 },
    { name: '东莞市', code: '441900', lat: 23.0489, lng: 113.7447 },
    { name: '大庆市', code: '230600', lat: 46.5969, lng: 125.1038 },
  ],
  'F': [
    { name: '福州市', code: '350100', lat: 26.0745, lng: 119.2965 },
    { name: '佛山市', code: '440600', lat: 23.0350, lng: 113.1221 },
  ],
  'G': [
    { name: '广州市', code: '440100', lat: 23.1291, lng: 113.2644 },
    { name: '贵阳市', code: '520100', lat: 26.6470, lng: 106.6302 },
    { name: '桂林市', code: '450300', lat: 25.2342, lng: 110.1993 },
  ],
  'H': [
    { name: '杭州市', code: '330100', lat: 30.2741, lng: 120.1551 },
    { name: '哈尔滨市', code: '230100', lat: 45.8038, lng: 126.5349 },
    { name: '合肥市', code: '340100', lat: 31.8206, lng: 117.2272 },
    { name: '海口市', code: '460100', lat: 20.0444, lng: 110.1989 },
  ],
  'J': [
    { name: '济南市', code: '370100', lat: 36.6512, lng: 117.1201 },
    { name: '金华市', code: '330700', lat: 29.1028, lng: 119.6477 },
    { name: '嘉兴市', code: '330400', lat: 30.7739, lng: 120.7551 },
  ],
  'K': [
    { name: '昆明市', code: '530100', lat: 25.0389, lng: 102.7183 },
  ],
  'L': [
    { name: '兰州市', code: '620100', lat: 36.0611, lng: 103.8343 },
    { name: '洛阳市', code: '410300', lat: 34.6197, lng: 112.4540 },
  ],
  'M': [
    { name: '绵阳市', code: '510700', lat: 31.4678, lng: 104.6794 },
  ],
  'N': [
    { name: '南京市', code: '320100', lat: 32.0603, lng: 118.7969 },
    { name: '南昌市', code: '360100', lat: 28.6820, lng: 115.8579 },
    { name: '南宁市', code: '450100', lat: 22.8170, lng: 108.3669 },
    { name: '宁波市', code: '330200', lat: 29.8683, lng: 121.5440 },
  ],
  'Q': [
    { name: '青岛市', code: '370200', lat: 36.0671, lng: 120.3826 },
  ],
  'S': [
    { name: '上海市', code: '310000', lat: 31.2304, lng: 121.4737 },
    { name: '深圳市', code: '440300', lat: 22.5431, lng: 114.0579 },
    { name: '苏州市', code: '320500', lat: 31.2989, lng: 120.5853 },
    { name: '沈阳市', code: '210100', lat: 41.8057, lng: 123.4315 },
    { name: '石家庄市', code: '130100', lat: 38.0428, lng: 114.5149 },
  ],
  'T': [
    { name: '天津市', code: '120000', lat: 39.3434, lng: 117.3616 },
    { name: '太原市', code: '140100', lat: 37.8570, lng: 112.5489 },
    { name: '唐山市', code: '130200', lat: 39.6304, lng: 118.1944 },
  ],
  'W': [
    { name: '武汉市', code: '420100', lat: 30.5928, lng: 114.3055 },
    { name: '无锡市', code: '320200', lat: 31.4912, lng: 120.3119 },
    { name: '温州市', code: '330300', lat: 27.9944, lng: 120.6994 },
  ],
  'X': [
    { name: '西安市', code: '610100', lat: 34.3416, lng: 108.9398 },
    { name: '厦门市', code: '350200', lat: 24.4798, lng: 118.0894 },
    { name: '徐州市', code: '320300', lat: 34.2058, lng: 117.2840 },
  ],
  'Y': [
    { name: '银川市', code: '640100', lat: 38.4872, lng: 106.2309 },
    { name: '烟台市', code: '370600', lat: 37.4638, lng: 121.4478 },
  ],
  'Z': [
    { name: '郑州市', code: '410100', lat: 34.7466, lng: 113.6254 },
    { name: '珠海市', code: '440400', lat: 22.2707, lng: 113.5767 },
    { name: '中山市', code: '442000', lat: 22.5177, lng: 113.3960 },
  ]
};

// 获取所有城市的扁平列表
export const getAllCitiesList = (): City[] => {
  const cities: City[] = [];
  Object.values(allCities).forEach(cityGroup => {
    cities.push(...cityGroup);
  });
  return cities;
};

// 根据名称搜索城市
export const searchCities = (keyword: string): City[] => {
  if (!keyword.trim()) return [];
  
  const allCitiesList = getAllCitiesList();
  return allCitiesList.filter(city => 
    city.name.toLowerCase().includes(keyword.toLowerCase())
  );
};

// 计算两点之间的距离（简化版，单位：公里）
export const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
  const R = 6371; // 地球半径（公里）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

// 根据坐标找到最近的城市
export const findNearestCity = (lat: number, lng: number): City | null => {
  const allCitiesList = getAllCitiesList();
  let nearestCity: City | null = null;
  let minDistance = Infinity;
  
  allCitiesList.forEach(city => {
    const distance = calculateDistance(lat, lng, city.lat, city.lng);
    if (distance < minDistance) {
      minDistance = distance;
      nearestCity = city;
    }
  });
  
  return nearestCity;
};
