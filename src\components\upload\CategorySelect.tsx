import React, { useState, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import { Category } from '@/types';
import { petAPI } from '@/lib/cloudbase';
import { cn } from '@/utils';

interface CategorySelectProps {
  value: string;
  onChange: (categoryId: string) => void;
  error?: string;
  className?: string;
}

const CategorySelect: React.FC<CategorySelectProps> = ({
  value,
  onChange,
  error,
  className,
}) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedLevel1, setSelectedLevel1] = useState<string>('');
  const [selectedLevel2, setSelectedLevel2] = useState<string>('');
  const [selectedLevel3, setSelectedLevel3] = useState<string>('');

  // 获取分类数据
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const result = await petAPI.getCategories();
        if (result.success) {
          setCategories(result.data);
        }
      } catch (error) {
        console.error('获取分类失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // 根据当前选中值设置各级分类
  useEffect(() => {
    if (value && categories.length > 0) {
      const selectedCategory = categories.find(cat => cat.id === value);
      if (selectedCategory) {
        if (selectedCategory.level === 1) {
          setSelectedLevel1(selectedCategory.id);
          setSelectedLevel2('');
          setSelectedLevel3('');
        } else if (selectedCategory.level === 2) {
          setSelectedLevel1(selectedCategory.parent_id || '');
          setSelectedLevel2(selectedCategory.id);
          setSelectedLevel3('');
        } else if (selectedCategory.level === 3) {
          const level2Category = categories.find(cat => cat.id === selectedCategory.parent_id);
          setSelectedLevel1(level2Category?.parent_id || '');
          setSelectedLevel2(selectedCategory.parent_id || '');
          setSelectedLevel3(selectedCategory.id);
        }
      }
    }
  }, [value, categories]);

  // 获取各级分类
  const level1Categories = categories.filter(cat => cat.level === 1);
  const level2Categories = categories.filter(cat => cat.level === 2 && cat.parent_id === selectedLevel1);
  const level3Categories = categories.filter(cat => cat.level === 3 && cat.parent_id === selectedLevel2);

  // 处理一级分类选择
  const handleLevel1Change = (categoryId: string) => {
    setSelectedLevel1(categoryId);
    setSelectedLevel2('');
    setSelectedLevel3('');
    onChange(categoryId);
  };

  // 处理二级分类选择
  const handleLevel2Change = (categoryId: string) => {
    setSelectedLevel2(categoryId);
    setSelectedLevel3('');
    onChange(categoryId);
  };

  // 处理三级分类选择
  const handleLevel3Change = (categoryId: string) => {
    setSelectedLevel3(categoryId);
    onChange(categoryId);
  };

  // 获取选中分类的完整路径
  const getSelectedPath = () => {
    const paths = [];
    if (selectedLevel1) {
      const l1 = categories.find(cat => cat.id === selectedLevel1);
      if (l1) paths.push(l1.name);
    }
    if (selectedLevel2) {
      const l2 = categories.find(cat => cat.id === selectedLevel2);
      if (l2) paths.push(l2.name);
    }
    if (selectedLevel3) {
      const l3 = categories.find(cat => cat.id === selectedLevel3);
      if (l3) paths.push(l3.name);
    }
    return paths.join(' > ');
  };

  if (loading) {
    return (
      <div className={cn('space-y-3', className)}>
        <label className="block text-sm font-medium text-gray-700">
          宝贝分类
        </label>
        <div className="animate-pulse space-y-2">
          <div className="h-10 bg-gray-200 rounded-md" />
          <div className="h-10 bg-gray-200 rounded-md" />
          <div className="h-10 bg-gray-200 rounded-md" />
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-3', className)}>
      <label className="block text-sm font-medium text-gray-700">
        宝贝分类 <span className="text-red-500">*</span>
      </label>

      {/* 选中路径显示 */}
      {value && (
        <div className="text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-md">
          已选择：{getSelectedPath()}
        </div>
      )}

      {/* 一级分类 */}
      <div>
        <label className="block text-xs font-medium text-gray-600 mb-1">
          主分类
        </label>
        <div className="relative">
          <select
            value={selectedLevel1}
            onChange={(e) => handleLevel1Change(e.target.value)}
            className={cn(
              'w-full px-3 py-2 border rounded-md bg-white focus:ring-2 focus:ring-primary-500 focus:border-primary-500 appearance-none',
              error ? 'border-red-500' : 'border-gray-300'
            )}
          >
            <option value="">请选择主分类</option>
            {level1Categories.map(category => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
          <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
        </div>
      </div>

      {/* 二级分类 */}
      {selectedLevel1 && level2Categories.length > 0 && (
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            二级分类
          </label>
          <div className="relative">
            <select
              value={selectedLevel2}
              onChange={(e) => handleLevel2Change(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-primary-500 focus:border-primary-500 appearance-none"
            >
              <option value="">请选择二级分类</option>
              {level2Categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>
        </div>
      )}

      {/* 三级分类 */}
      {selectedLevel2 && level3Categories.length > 0 && (
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            详细分类
          </label>
          <div className="relative">
            <select
              value={selectedLevel3}
              onChange={(e) => handleLevel3Change(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-primary-500 focus:border-primary-500 appearance-none"
            >
              <option value="">请选择详细分类</option>
              {level3Categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}

      {/* 分类说明 */}
      <div className="text-xs text-gray-500">
        <p>请按照层级选择分类，选择越详细越容易被找到</p>
      </div>
    </div>
  );
};

export default CategorySelect;
