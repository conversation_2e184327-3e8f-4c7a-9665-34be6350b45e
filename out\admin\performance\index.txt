2:I[19107,[],"ClientPageRoot"]
3:I[53717,["554","static/chunks/554-28c36c49a11c28ce.js","170","static/chunks/170-1258087ee7e21918.js","652","static/chunks/app/admin/performance/page-a7ae6b678ecce2ea.js"],"default",1]
4:I[4707,[],""]
5:I[36423,[],""]
6:I[96631,["554","static/chunks/554-28c36c49a11c28ce.js","721","static/chunks/721-8e734d5a2d35160f.js","91","static/chunks/app/admin/layout-593afaa12d5e8be5.js"],"default",1]
8:I[88941,["649","static/chunks/594a7e55-aa83172b4804c738.js","19","static/chunks/4bdbddd8-bfdf4f2e8af6e1ae.js","347","static/chunks/347-caf54baf07bb236d.js","554","static/chunks/554-28c36c49a11c28ce.js","721","static/chunks/721-8e734d5a2d35160f.js","319","static/chunks/319-4fd95c20f587fb36.js","11","static/chunks/11-2fe77f07d5c34b2b.js","734","static/chunks/734-cc7e6ccc4de50eb9.js","185","static/chunks/app/layout-0f0cff4c2d2a9e80.js"],"AuthProvider"]
9:I[72138,["649","static/chunks/594a7e55-aa83172b4804c738.js","19","static/chunks/4bdbddd8-bfdf4f2e8af6e1ae.js","347","static/chunks/347-caf54baf07bb236d.js","554","static/chunks/554-28c36c49a11c28ce.js","721","static/chunks/721-8e734d5a2d35160f.js","319","static/chunks/319-4fd95c20f587fb36.js","11","static/chunks/11-2fe77f07d5c34b2b.js","734","static/chunks/734-cc7e6ccc4de50eb9.js","185","static/chunks/app/layout-0f0cff4c2d2a9e80.js"],"default"]
a:I[9356,["649","static/chunks/594a7e55-aa83172b4804c738.js","19","static/chunks/4bdbddd8-bfdf4f2e8af6e1ae.js","347","static/chunks/347-caf54baf07bb236d.js","554","static/chunks/554-28c36c49a11c28ce.js","721","static/chunks/721-8e734d5a2d35160f.js","319","static/chunks/319-4fd95c20f587fb36.js","11","static/chunks/11-2fe77f07d5c34b2b.js","734","static/chunks/734-cc7e6ccc4de50eb9.js","185","static/chunks/app/layout-0f0cff4c2d2a9e80.js"],"ToastProvider"]
7:{}
0:["build-1753782122991",[[["",{"children":["admin",{"children":["performance",{"children":["__PAGE__",{}]}]}]},"$undefined","$undefined",true],["",{"children":["admin",{"children":["performance",{"children":["__PAGE__",{},[["$L1",["$","$L2",null,{"props":{"params":{},"searchParams":{}},"Component":"$3"}],null],null],null]},[null,["$","$L4",null,{"parallelRouterKey":"children","segmentPath":["children","admin","children","performance","children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined"}]],null]},[[null,["$","$L6",null,{"children":["$","$L4",null,{"parallelRouterKey":"children","segmentPath":["children","admin","children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined"}],"params":"$7"}]],null],null]},[[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/91df093635add3b5.css","precedence":"next","crossOrigin":"$undefined"}]],["$","html",null,{"lang":"zh-CN","children":[["$","head",null,{}],["$","body",null,{"className":"font-sans","children":["$","$L8",null,{"children":[["$","div",null,{"className":"pb-16 md:pb-0","children":["$","$L4",null,{"parallelRouterKey":"children","segmentPath":["children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":"404"}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"notFoundStyles":[]}]}],["$","$L9",null,{}],["$","$La",null,{}]]}]}]]}]],null],null],["$Lb",null]]]]
b:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"name":"theme-color","content":"#3b82f6"}],["$","meta","2",{"charSet":"utf-8"}],["$","title","3",{"children":"宠物交易平台 - TikTok风格的现代化宠物交易平台"}],["$","meta","4",{"name":"description","content":"一个基于腾讯云开发的现代化宠物交易平台，采用TikTok风格的UI设计和完整的社交功能"}],["$","meta","5",{"name":"author","content":"Pet Trading Platform Team"}],["$","link","6",{"rel":"manifest","href":"/manifest.json","crossOrigin":"use-credentials"}],["$","meta","7",{"name":"keywords","content":"宠物,交易,平台,TikTok,社交,云开发"}],["$","meta","8",{"property":"og:title","content":"宠物交易平台"}],["$","meta","9",{"property":"og:description","content":"TikTok风格的现代化宠物交易平台"}],["$","meta","10",{"property":"og:site_name","content":"宠物交易平台"}],["$","meta","11",{"property":"og:locale","content":"zh_CN"}],["$","meta","12",{"property":"og:type","content":"website"}],["$","meta","13",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","14",{"name":"twitter:title","content":"宠物交易平台"}],["$","meta","15",{"name":"twitter:description","content":"TikTok风格的现代化宠物交易平台"}],["$","link","16",{"rel":"icon","href":"/favicon.ico"}],["$","link","17",{"rel":"apple-touch-icon","href":"/apple-touch-icon.png"}]]
1:null
