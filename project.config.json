{"description": "宠物交易平台 - 云开发项目配置", "packOptions": {"ignore": [{"value": ".eslintrc.js", "type": "file"}, {"value": ".giti<PERSON>re", "type": "file"}, {"value": "README.md", "type": "file"}, {"value": "node_modules", "type": "folder"}, {"value": ".git", "type": "folder"}, {"value": ".next", "type": "folder"}, {"value": "out", "type": "folder"}, {"value": "src", "type": "folder"}, {"value": "public", "type": "folder"}, {"value": "package.json", "type": "file"}, {"value": "package-lock.json", "type": "file"}, {"value": "next.config.js", "type": "file"}, {"value": "tailwind.config.js", "type": "file"}, {"value": "tsconfig.json", "type": "file"}], "include": []}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "compileWorklet": false, "localPlugins": false, "condition": true, "swc": false, "disableSWC": true}, "compileType": "cloudbase", "libVersion": "latest", "appid": "wx453409e8a70193cb", "projectname": "宠物交易平台", "cloudfunctionTemplateRoot": "cloudfunctions/", "miniprogramRoot": "miniprogram/", "isGameTourist": false, "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": []}}, "simulatorPluginLibVersion": {"wxext14566970e7e9f62": "2.27.3"}, "editorSetting": {}, "projectArchitecture": "multiPlatform"}