#!/usr/bin/env node

/**
 * 腾讯云开发 MCP 服务器测试
 * 用于验证 MCP 工具与腾讯云开发的集成
 */

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');

class CloudBaseMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'cloudbase-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
    this.setupErrorHandling();
  }

  setupToolHandlers() {
    // 列出可用工具
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'test_cloudbase_connection',
            description: '测试腾讯云开发连接状态',
            inputSchema: {
              type: 'object',
              properties: {},
            },
          },
          {
            name: 'deploy_function',
            description: '部署云函数',
            inputSchema: {
              type: 'object',
              properties: {
                functionName: {
                  type: 'string',
                  description: '云函数名称',
                },
                functionPath: {
                  type: 'string',
                  description: '云函数代码路径',
                },
              },
              required: ['functionName', 'functionPath'],
            },
          },
          {
            name: 'upload_static_files',
            description: '上传静态文件到托管',
            inputSchema: {
              type: 'object',
              properties: {
                localPath: {
                  type: 'string',
                  description: '本地文件路径',
                },
                remotePath: {
                  type: 'string',
                  description: '远程路径',
                },
              },
              required: ['localPath'],
            },
          },
        ],
      };
    });

    // 处理工具调用
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'test_cloudbase_connection':
            return await this.testCloudBaseConnection();
          
          case 'deploy_function':
            return await this.deployFunction(args.functionName, args.functionPath);
          
          case 'upload_static_files':
            return await this.uploadStaticFiles(args.localPath, args.remotePath);
          
          default:
            throw new Error(`未知工具: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `错误: ${error.message}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  async testCloudBaseConnection() {
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);

    try {
      // 测试环境连接
      const { stdout } = await execAsync('npx cloudbase env list');
      
      return {
        content: [
          {
            type: 'text',
            text: `✅ 腾讯云开发连接正常\n\n环境信息:\n${stdout}`,
          },
        ],
      };
    } catch (error) {
      throw new Error(`连接测试失败: ${error.message}`);
    }
  }

  async deployFunction(functionName, functionPath) {
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);

    try {
      const command = `npx cloudbase fn deploy ${functionName} --dir ${functionPath}`;
      const { stdout, stderr } = await execAsync(command);
      
      return {
        content: [
          {
            type: 'text',
            text: `✅ 云函数 ${functionName} 部署成功\n\n${stdout}${stderr ? '\n警告:\n' + stderr : ''}`,
          },
        ],
      };
    } catch (error) {
      throw new Error(`云函数部署失败: ${error.message}`);
    }
  }

  async uploadStaticFiles(localPath, remotePath = '/') {
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);

    try {
      const command = `npx cloudbase hosting deploy ${localPath} ${remotePath}`;
      const { stdout, stderr } = await execAsync(command);
      
      return {
        content: [
          {
            type: 'text',
            text: `✅ 静态文件上传成功\n\n${stdout}${stderr ? '\n警告:\n' + stderr : ''}`,
          },
        ],
      };
    } catch (error) {
      throw new Error(`静态文件上传失败: ${error.message}`);
    }
  }

  setupErrorHandling() {
    this.server.onerror = (error) => {
      console.error('[MCP Error]', error);
    };

    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('腾讯云开发 MCP 服务器已启动');
  }
}

// 启动服务器
if (require.main === module) {
  const server = new CloudBaseMCPServer();
  server.run().catch(console.error);
}

module.exports = CloudBaseMCPServer;
