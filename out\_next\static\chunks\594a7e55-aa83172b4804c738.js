"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[649],{75447:function(t,e,r){r.d(e,{dF:function(){return n}});for(var n,i,o=[],s=[],u="undefined"!=typeof Uint8Array?Uint8Array:Array,f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",h=0,a=f.length;h<a;++h)o[h]=f[h],s[f.charCodeAt(h)]=h;s["-".charCodeAt(0)]=62,s["_".charCodeAt(0)]=63;var p={toByteArray:function(t){var e,r,n=function(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var n=r===e?0:4-r%4;return[r,n]}(t),i=n[0],o=n[1],f=new u((i+o)*3/4-o),h=0,a=o>0?i-4:i;for(r=0;r<a;r+=4)e=s[t.charCodeAt(r)]<<18|s[t.charCodeAt(r+1)]<<12|s[t.charCodeAt(r+2)]<<6|s[t.charCodeAt(r+3)],f[h++]=e>>16&255,f[h++]=e>>8&255,f[h++]=255&e;return 2===o&&(e=s[t.charCodeAt(r)]<<2|s[t.charCodeAt(r+1)]>>4,f[h++]=255&e),1===o&&(e=s[t.charCodeAt(r)]<<10|s[t.charCodeAt(r+1)]<<4|s[t.charCodeAt(r+2)]>>2,f[h++]=e>>8&255,f[h++]=255&e),f},fromByteArray:function(t){for(var e,r=t.length,n=r%3,i=[],s=0,u=r-n;s<u;s+=16383)i.push(function(t,e,r){for(var n,i=[],s=e;s<r;s+=3)i.push(o[(n=(t[s]<<16&16711680)+(t[s+1]<<8&65280)+(255&t[s+2]))>>18&63]+o[n>>12&63]+o[n>>6&63]+o[63&n]);return i.join("")}(t,s,s+16383>u?u:s+16383));return 1===n?i.push(o[(e=t[r-1])>>2]+o[e<<4&63]+"=="):2===n&&i.push(o[(e=(t[r-2]<<8)+t[r-1])>>10]+o[e>>4&63]+o[e<<2&63]+"="),i.join("")}},c=function(t,e,r,n,i){var o,s,u=8*i-n-1,f=(1<<u)-1,h=f>>1,a=-7,p=r?i-1:0,c=r?-1:1,l=t[e+p];for(p+=c,o=l&(1<<-a)-1,l>>=-a,a+=u;a>0;o=256*o+t[e+p],p+=c,a-=8);for(s=o&(1<<-a)-1,o>>=-a,a+=n;a>0;s=256*s+t[e+p],p+=c,a-=8);if(0===o)o=1-h;else{if(o===f)return s?NaN:1/0*(l?-1:1);s+=Math.pow(2,n),o-=h}return(l?-1:1)*s*Math.pow(2,o-n)},l=function(t,e,r,n,i,o){var s,u,f,h=8*o-i-1,a=(1<<h)-1,p=a>>1,c=23===i?5960464477539062e-23:0,l=n?0:o-1,g=n?1:-1,y=e<0||0===e&&1/e<0?1:0;for(isNaN(e=Math.abs(e))||e===1/0?(u=isNaN(e)?1:0,s=a):(s=Math.floor(Math.log(e)/Math.LN2),e*(f=Math.pow(2,-s))<1&&(s--,f*=2),s+p>=1?e+=c/f:e+=c*Math.pow(2,1-p),e*f>=2&&(s++,f/=2),s+p>=a?(u=0,s=a):s+p>=1?(u=(e*f-1)*Math.pow(2,i),s+=p):(u=e*Math.pow(2,p-1)*Math.pow(2,i),s=0));i>=8;t[r+l]=255&u,l+=g,u/=256,i-=8);for(s=s<<i|u,h+=i;h>0;t[r+l]=255&s,l+=g,s/=256,h-=8);t[r+l-g]|=128*y},g=(function(t,e){var r="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function n(t){if(t>2147483647)throw RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,i.prototype),e}function i(t,e,r){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return u(t)}return o(t,e,r)}function o(t,e,r){if("string"==typeof t)return function(t,e){if(("string"!=typeof e||""===e)&&(e="utf8"),!i.isEncoding(e))throw TypeError("Unknown encoding: "+e);var r=0|g(t,e),o=n(r),s=o.write(t,e);return s!==r&&(o=o.slice(0,s)),o}(t,e);if(ArrayBuffer.isView(t))return function(t){if(A(t,Uint8Array)){var e=new Uint8Array(t);return h(e.buffer,e.byteOffset,e.byteLength)}return f(t)}(t);if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+babelHelpers.typeof(t));if(A(t,ArrayBuffer)||t&&A(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(A(t,SharedArrayBuffer)||t&&A(t.buffer,SharedArrayBuffer)))return h(t,e,r);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');var o=t.valueOf&&t.valueOf();if(null!=o&&o!==t)return i.from(o,e,r);var s=function(t){if(i.isBuffer(t)){var e,r=0|a(t.length),o=n(r);return 0===o.length||t.copy(o,0,0,r),o}return void 0!==t.length?"number"!=typeof t.length||(e=t.length)!=e?n(0):f(t):"Buffer"===t.type&&Array.isArray(t.data)?f(t.data):void 0}(t);if(s)return s;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return i.from(t[Symbol.toPrimitive]("string"),e,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+babelHelpers.typeof(t))}function s(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function u(t){return s(t),n(t<0?0:0|a(t))}function f(t){for(var e=t.length<0?0:0|a(t.length),r=n(e),i=0;i<e;i+=1)r[i]=255&t[i];return r}function h(t,e,r){var n;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),i.prototype),n}function a(t){if(t>=2147483647)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function g(t,e){if(i.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||A(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+babelHelpers.typeof(t));var r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var o=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return B(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return _(t).length;default:if(o)return n?-1:B(t).length;e=(""+e).toLowerCase(),o=!0}}function y(t,e,r){var n,i,o=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=e;o<r;++o)i+=I[t[o]];return i}(this,e,r);case"utf8":case"utf-8":return v(this,e,r);case"ascii":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}(this,e,r);case"latin1":case"binary":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}(this,e,r);case"base64":return n=e,i=r,0===n&&i===this.length?p.fromByteArray(this):p.fromByteArray(this.slice(n,i));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){for(var n=t.slice(e,r),i="",o=0;o<n.length-1;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}(this,e,r);default:if(o)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),o=!0}}function d(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function m(t,e,r,n,o){var s;if(0===t.length)return -1;if("string"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),(s=r=+r)!=s&&(r=o?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(o)return -1;r=t.length-1}else if(r<0){if(!o)return -1;r=0}if("string"==typeof e&&(e=i.from(e,n)),i.isBuffer(e))return 0===e.length?-1:b(t,e,r,n,o);if("number"==typeof e)return(e&=255,"function"==typeof Uint8Array.prototype.indexOf)?o?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):b(t,[e],r,n,o);throw TypeError("val must be string, number or Buffer")}function b(t,e,r,n,i){var o,s=1,u=t.length,f=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return -1;s=2,u/=2,f/=2,r/=2}function h(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(i){var a=-1;for(o=r;o<u;o++)if(h(t,o)===h(e,-1===a?0:o-a)){if(-1===a&&(a=o),o-a+1===f)return a*s}else -1!==a&&(o-=o-a),a=-1}else for(r+f>u&&(r=u-f),o=r;o>=0;o--){for(var p=!0,c=0;c<f;c++)if(h(t,o+c)!==h(e,c)){p=!1;break}if(p)return o}return -1}function v(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var o,s,u,f,h=t[i],a=null,p=h>239?4:h>223?3:h>191?2:1;if(i+p<=r)switch(p){case 1:h<128&&(a=h);break;case 2:(192&(o=t[i+1]))==128&&(f=(31&h)<<6|63&o)>127&&(a=f);break;case 3:o=t[i+1],s=t[i+2],(192&o)==128&&(192&s)==128&&(f=(15&h)<<12|(63&o)<<6|63&s)>2047&&(f<55296||f>57343)&&(a=f);break;case 4:o=t[i+1],s=t[i+2],u=t[i+3],(192&o)==128&&(192&s)==128&&(192&u)==128&&(f=(15&h)<<18|(63&o)<<12|(63&s)<<6|63&u)>65535&&f<1114112&&(a=f)}null===a?(a=65533,p=1):a>65535&&(a-=65536,n.push(a>>>10&1023|55296),a=56320|1023&a),n.push(a),i+=p}return function(t){var e=t.length;if(e<=4096)return String.fromCharCode.apply(String,t);for(var r="",n=0;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=4096));return r}(n)}function w(t,e,r){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>r)throw RangeError("Trying to access beyond buffer length")}function x(t,e,r,n,o,s){if(!i.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>o||e<s)throw RangeError('"value" argument is out of bounds');if(r+n>t.length)throw RangeError("Index out of range")}function E(t,e,r,n,i,o){if(r+n>t.length||r<0)throw RangeError("Index out of range")}function S(t,e,r,n,i){return e=+e,r>>>=0,i||E(t,e,r,4),l(t,e,r,n,23,4),r+4}function O(t,e,r,n,i){return e=+e,r>>>=0,i||E(t,e,r,8),l(t,e,r,n,52,8),r+8}e.Buffer=i,e.SlowBuffer=function(t){return+t!=t&&(t=0),i.alloc(+t)},e.INSPECT_MAX_BYTES=50,e.kMaxLength=2147483647,i.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),i.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(i.prototype,"parent",{enumerable:!0,get:function(){if(i.isBuffer(this))return this.buffer}}),Object.defineProperty(i.prototype,"offset",{enumerable:!0,get:function(){if(i.isBuffer(this))return this.byteOffset}}),i.poolSize=8192,i.from=function(t,e,r){return o(t,e,r)},Object.setPrototypeOf(i.prototype,Uint8Array.prototype),Object.setPrototypeOf(i,Uint8Array),i.alloc=function(t,e,r){return(s(t),t<=0)?n(t):void 0!==e?"string"==typeof r?n(t).fill(e,r):n(t).fill(e):n(t)},i.allocUnsafe=function(t){return u(t)},i.allocUnsafeSlow=function(t){return u(t)},i.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==i.prototype},i.compare=function(t,e){if(A(t,Uint8Array)&&(t=i.from(t,t.offset,t.byteLength)),A(e,Uint8Array)&&(e=i.from(e,e.offset,e.byteLength)),!i.isBuffer(t)||!i.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,o=0,s=Math.min(r,n);o<s;++o)if(t[o]!==e[o]){r=t[o],n=e[o];break}return r<n?-1:n<r?1:0},i.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},i.concat=function(t,e){if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return i.alloc(0);if(void 0===e)for(r=0,e=0;r<t.length;++r)e+=t[r].length;var r,n=i.allocUnsafe(e),o=0;for(r=0;r<t.length;++r){var s=t[r];if(A(s,Uint8Array))o+s.length>n.length?i.from(s).copy(n,o):Uint8Array.prototype.set.call(n,s,o);else if(i.isBuffer(s))s.copy(n,o);else throw TypeError('"list" argument must be an Array of Buffers');o+=s.length}return n},i.byteLength=g,i.prototype._isBuffer=!0,i.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)d(this,e,e+1);return this},i.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)d(this,e,e+3),d(this,e+1,e+2);return this},i.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)d(this,e,e+7),d(this,e+1,e+6),d(this,e+2,e+5),d(this,e+3,e+4);return this},i.prototype.toString=function(){var t=this.length;return 0===t?"":0==arguments.length?v(this,0,t):y.apply(this,arguments)},i.prototype.toLocaleString=i.prototype.toString,i.prototype.equals=function(t){if(!i.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===i.compare(this,t)},i.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},r&&(i.prototype[r]=i.prototype.inspect),i.prototype.compare=function(t,e,r,n,o){if(A(t,Uint8Array)&&(t=i.from(t,t.offset,t.byteLength)),!i.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+babelHelpers.typeof(t));if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===o&&(o=this.length),e<0||r>t.length||n<0||o>this.length)throw RangeError("out of range index");if(n>=o&&e>=r)return 0;if(n>=o)return -1;if(e>=r)return 1;if(e>>>=0,r>>>=0,n>>>=0,o>>>=0,this===t)return 0;for(var s=o-n,u=r-e,f=Math.min(s,u),h=this.slice(n,o),a=t.slice(e,r),p=0;p<f;++p)if(h[p]!==a[p]){s=h[p],u=a[p];break}return s<u?-1:u<s?1:0},i.prototype.includes=function(t,e,r){return -1!==this.indexOf(t,e,r)},i.prototype.indexOf=function(t,e,r){return m(this,t,e,r,!0)},i.prototype.lastIndexOf=function(t,e,r){return m(this,t,e,r,!1)},i.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,o,s,u,f,h,a,p,c=this.length-e;if((void 0===r||r>c)&&(r=c),t.length>0&&(r<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var l=!1;;)switch(n){case"hex":return function(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=e.length;n>o/2&&(n=o/2);for(var s=0;s<n;++s){var u=parseInt(e.substr(2*s,2),16);if(u!=u)break;t[r+s]=u}return s}(this,t,e,r);case"utf8":case"utf-8":return i=e,o=r,U(B(t,this.length-i),this,i,o);case"ascii":case"latin1":case"binary":return s=e,u=r,U(function(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(t),this,s,u);case"base64":return f=e,h=r,U(_(t),this,f,h);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return a=e,p=r,U(function(t,e){for(var r,n,i=[],o=0;o<t.length&&!((e-=2)<0);++o)n=(r=t.charCodeAt(o))>>8,i.push(r%256),i.push(n);return i}(t,this.length-a),this,a,p);default:if(l)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),l=!0}},i.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},i.prototype.slice=function(t,e){var r=this.length;t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return Object.setPrototypeOf(n,i.prototype),n},i.prototype.readUintLE=i.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||w(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n},i.prototype.readUintBE=i.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||w(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},i.prototype.readUint8=i.prototype.readUInt8=function(t,e){return t>>>=0,e||w(t,1,this.length),this[t]},i.prototype.readUint16LE=i.prototype.readUInt16LE=function(t,e){return t>>>=0,e||w(t,2,this.length),this[t]|this[t+1]<<8},i.prototype.readUint16BE=i.prototype.readUInt16BE=function(t,e){return t>>>=0,e||w(t,2,this.length),this[t]<<8|this[t+1]},i.prototype.readUint32LE=i.prototype.readUInt32LE=function(t,e){return t>>>=0,e||w(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},i.prototype.readUint32BE=i.prototype.readUInt32BE=function(t,e){return t>>>=0,e||w(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},i.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||w(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},i.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||w(t,e,this.length);for(var n=e,i=1,o=this[t+--n];n>0&&(i*=256);)o+=this[t+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},i.prototype.readInt8=function(t,e){return(t>>>=0,e||w(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},i.prototype.readInt16LE=function(t,e){t>>>=0,e||w(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},i.prototype.readInt16BE=function(t,e){t>>>=0,e||w(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},i.prototype.readInt32LE=function(t,e){return t>>>=0,e||w(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},i.prototype.readInt32BE=function(t,e){return t>>>=0,e||w(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},i.prototype.readFloatLE=function(t,e){return t>>>=0,e||w(t,4,this.length),c(this,t,!0,23,4)},i.prototype.readFloatBE=function(t,e){return t>>>=0,e||w(t,4,this.length),c(this,t,!1,23,4)},i.prototype.readDoubleLE=function(t,e){return t>>>=0,e||w(t,8,this.length),c(this,t,!0,52,8)},i.prototype.readDoubleBE=function(t,e){return t>>>=0,e||w(t,8,this.length),c(this,t,!1,52,8)},i.prototype.writeUintLE=i.prototype.writeUIntLE=function(t,e,r,n){if(t=+t,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;x(this,t,e,r,i,0)}var o=1,s=0;for(this[e]=255&t;++s<r&&(o*=256);)this[e+s]=t/o&255;return e+r},i.prototype.writeUintBE=i.prototype.writeUIntBE=function(t,e,r,n){if(t=+t,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;x(this,t,e,r,i,0)}var o=r-1,s=1;for(this[e+o]=255&t;--o>=0&&(s*=256);)this[e+o]=t/s&255;return e+r},i.prototype.writeUint8=i.prototype.writeUInt8=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,1,255,0),this[e]=255&t,e+1},i.prototype.writeUint16LE=i.prototype.writeUInt16LE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},i.prototype.writeUint16BE=i.prototype.writeUInt16BE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},i.prototype.writeUint32LE=i.prototype.writeUInt32LE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},i.prototype.writeUint32BE=i.prototype.writeUInt32BE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},i.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var i=Math.pow(2,8*r-1);x(this,t,e,r,i-1,-i)}var o=0,s=1,u=0;for(this[e]=255&t;++o<r&&(s*=256);)t<0&&0===u&&0!==this[e+o-1]&&(u=1),this[e+o]=(t/s>>0)-u&255;return e+r},i.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var i=Math.pow(2,8*r-1);x(this,t,e,r,i-1,-i)}var o=r-1,s=1,u=0;for(this[e+o]=255&t;--o>=0&&(s*=256);)t<0&&0===u&&0!==this[e+o+1]&&(u=1),this[e+o]=(t/s>>0)-u&255;return e+r},i.prototype.writeInt8=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},i.prototype.writeInt16LE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},i.prototype.writeInt16BE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},i.prototype.writeInt32LE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,4,2147483647,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},i.prototype.writeInt32BE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},i.prototype.writeFloatLE=function(t,e,r){return S(this,t,e,!0,r)},i.prototype.writeFloatBE=function(t,e,r){return S(this,t,e,!1,r)},i.prototype.writeDoubleLE=function(t,e,r){return O(this,t,e,!0,r)},i.prototype.writeDoubleBE=function(t,e,r){return O(this,t,e,!1,r)},i.prototype.copy=function(t,e,r,n){if(!i.isBuffer(t))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var o=n-r;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(e,r,n):Uint8Array.prototype.set.call(t,this.subarray(r,n),e),o},i.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!i.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===t.length){var o,s=t.charCodeAt(0);("utf8"===n&&s<128||"latin1"===n)&&(t=s)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw RangeError("Out of range index");if(r<=e)return this;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(o=e;o<r;++o)this[o]=t;else{var u=i.isBuffer(t)?t:i.from(t,n),f=u.length;if(0===f)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(o=0;o<r-e;++o)this[o+e]=u[o%f]}return this};var N=/[^+/0-9A-Za-z-_]/g;function B(t,e){e=e||1/0;for(var r,n=t.length,i=null,o=[],s=0;s<n;++s){if((r=t.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319||s+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return o}function _(t){return p.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(N,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function U(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length)&&!(i>=t.length);++i)e[i+r]=t[i];return i}function A(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}var I=function(){for(var t="0123456789abcdef",e=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)e[n+i]=t[r]+t[i];return e}()}(i={exports:{}},i.exports),i.exports),y=g.Buffer;g.SlowBuffer,g.INSPECT_MAX_BYTES,g.kMaxLength;var d=function(t,e){return(d=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)};function m(t,e){function r(){this.constructor=t}d(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}var b=function(t){function e(r){var n=t.call(this,r)||this;return Object.setPrototypeOf(n,e.prototype),n}return m(e,t),Object.defineProperty(e.prototype,"name",{get:function(){return"BSONError"},enumerable:!1,configurable:!0}),e}(Error),v=function(t){function e(r){var n=t.call(this,r)||this;return Object.setPrototypeOf(n,e.prototype),n}return m(e,t),Object.defineProperty(e.prototype,"name",{get:function(){return"BSONTypeError"},enumerable:!1,configurable:!0}),e}(TypeError);function w(t){return t&&t.Math==Math&&t}function x(){return w("object"==typeof globalThis&&globalThis)||w("object"==typeof window&&window)||w("object"==typeof self&&self)||w("object"==typeof r.g&&r.g)||Function("return this")()}var E=function(t){console.warn("object"==typeof(e=x()).navigator&&"ReactNative"===e.navigator.product?"BSON: For React Native please polyfill crypto.getRandomValues, e.g. using: https://www.npmjs.com/package/react-native-get-random-values.":"BSON: No cryptographic implementation for random bytes present, falling back to a less secure implementation.");for(var e,r=y.alloc(t),n=0;n<t;++n)r[n]=Math.floor(256*Math.random());return r},S=function(){if("undefined"!=typeof window){var t=window.crypto||window.msCrypto;if(t&&t.getRandomValues)return function(e){return t.getRandomValues(y.alloc(e))}}return void 0!==r.g&&r.g.crypto&&r.g.crypto.getRandomValues?function(t){return r.g.crypto.getRandomValues(y.alloc(t))}:E}();function O(t){return"[object Uint8Array]"===Object.prototype.toString.call(t)}function N(t){return"object"==typeof t&&null!==t}function B(t,e){var r=!1;return function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];return r||(console.warn(e),r=!0),t.apply(this,n)}}function _(t){if(ArrayBuffer.isView(t))return y.from(t.buffer,t.byteOffset,t.byteLength);if(["[object ArrayBuffer]","[object SharedArrayBuffer]"].includes(Object.prototype.toString.call(t)))return y.from(t);throw new v("Must use either Buffer or TypedArray")}var U=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|[0-9a-f]{12}4[0-9a-f]{3}[89ab][0-9a-f]{15})$/i,A=function(t){return"string"==typeof t&&U.test(t)},I=function(t){if(!A(t))throw new v('UUID string representations must be a 32 or 36 character hex string (dashes excluded/included). Format: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" or "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx".');var e=t.replace(/-/g,"");return y.from(e,"hex")},j=function(t,e){return void 0===e&&(e=!0),e?t.toString("hex",0,4)+"-"+t.toString("hex",4,6)+"-"+t.toString("hex",6,8)+"-"+t.toString("hex",8,10)+"-"+t.toString("hex",10,16):t.toString("hex")},L=function(){function t(e,r){if(!(this instanceof t))return new t(e,r);if(null!=e&&"string"!=typeof e&&!ArrayBuffer.isView(e)&&!(e instanceof ArrayBuffer)&&!Array.isArray(e))throw new v("Binary can only be constructed from string, Buffer, TypedArray, or Array<number>");this.sub_type=null!=r?r:t.BSON_BINARY_SUBTYPE_DEFAULT,null==e?(this.buffer=y.alloc(t.BUFFER_SIZE),this.position=0):("string"==typeof e?this.buffer=y.from(e,"binary"):Array.isArray(e)?this.buffer=y.from(e):this.buffer=_(e),this.position=this.buffer.byteLength)}return t.prototype.put=function(e){if("string"==typeof e&&1!==e.length)throw new v("only accepts single character String");if("number"!=typeof e&&1!==e.length)throw new v("only accepts single character Uint8Array or Array");if((r="string"==typeof e?e.charCodeAt(0):"number"==typeof e?e:e[0])<0||r>255)throw new v("only accepts number in a valid unsigned byte range 0-255");if(this.buffer.length>this.position)this.buffer[this.position++]=r;else{var r,n=y.alloc(t.BUFFER_SIZE+this.buffer.length);this.buffer.copy(n,0,0,this.buffer.length),this.buffer=n,this.buffer[this.position++]=r}},t.prototype.write=function(t,e){if(e="number"==typeof e?e:this.position,this.buffer.length<e+t.length){var r=y.alloc(this.buffer.length+t.length);this.buffer.copy(r,0,0,this.buffer.length),this.buffer=r}ArrayBuffer.isView(t)?(this.buffer.set(_(t),e),this.position=e+t.byteLength>this.position?e+t.length:this.position):"string"==typeof t&&(this.buffer.write(t,e,t.length,"binary"),this.position=e+t.length>this.position?e+t.length:this.position)},t.prototype.read=function(t,e){return e=e&&e>0?e:this.position,this.buffer.slice(t,t+e)},t.prototype.value=function(t){return(t=!!t)&&this.buffer.length===this.position?this.buffer:t?this.buffer.slice(0,this.position):this.buffer.toString("binary",0,this.position)},t.prototype.length=function(){return this.position},t.prototype.toJSON=function(){return this.buffer.toString("base64")},t.prototype.toString=function(t){return this.buffer.toString(t)},t.prototype.toExtendedJSON=function(t){t=t||{};var e=this.buffer.toString("base64"),r=Number(this.sub_type).toString(16);return t.legacy?{$binary:e,$type:1===r.length?"0"+r:r}:{$binary:{base64:e,subType:1===r.length?"0"+r:r}}},t.prototype.toUUID=function(){if(this.sub_type===t.SUBTYPE_UUID)return new $(this.buffer.slice(0,this.position));throw new b('Binary sub_type "'.concat(this.sub_type,'" is not supported for converting to UUID. Only "').concat(t.SUBTYPE_UUID,'" is currently supported.'))},t.fromExtendedJSON=function(e,r){var n,i;if(r=r||{},"$binary"in e?r.legacy&&"string"==typeof e.$binary&&"$type"in e?(i=e.$type?parseInt(e.$type,16):0,n=y.from(e.$binary,"base64")):"string"!=typeof e.$binary&&(i=e.$binary.subType?parseInt(e.$binary.subType,16):0,n=y.from(e.$binary.base64,"base64")):"$uuid"in e&&(i=4,n=I(e.$uuid)),!n)throw new v("Unexpected Binary Extended JSON format ".concat(JSON.stringify(e)));return 4===i?new $(n):new t(n,i)},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){var t=this.value(!0);return'new Binary(Buffer.from("'.concat(t.toString("hex"),'", "hex"), ').concat(this.sub_type,")")},t.BSON_BINARY_SUBTYPE_DEFAULT=0,t.BUFFER_SIZE=256,t.SUBTYPE_DEFAULT=0,t.SUBTYPE_FUNCTION=1,t.SUBTYPE_BYTE_ARRAY=2,t.SUBTYPE_UUID_OLD=3,t.SUBTYPE_UUID=4,t.SUBTYPE_MD5=5,t.SUBTYPE_ENCRYPTED=6,t.SUBTYPE_COLUMN=7,t.SUBTYPE_USER_DEFINED=128,t}();Object.defineProperty(L.prototype,"_bsontype",{value:"Binary"});var $=function(t){function e(r){var n,i,o=this;if(null==r)n=e.generate();else if(r instanceof e)n=y.from(r.buffer),i=r.__id;else if(ArrayBuffer.isView(r)&&16===r.byteLength)n=_(r);else if("string"==typeof r)n=I(r);else throw new v("Argument passed in UUID constructor must be a UUID, a 16 byte Buffer or a 32/36 character hex string (dashes excluded/included, format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx).");return(o=t.call(this,n,4)||this).__id=i,o}return m(e,t),Object.defineProperty(e.prototype,"id",{get:function(){return this.buffer},set:function(t){this.buffer=t,e.cacheHexString&&(this.__id=j(t))},enumerable:!1,configurable:!0}),e.prototype.toHexString=function(t){if(void 0===t&&(t=!0),e.cacheHexString&&this.__id)return this.__id;var r=j(this.id,t);return e.cacheHexString&&(this.__id=r),r},e.prototype.toString=function(t){return t?this.id.toString(t):this.toHexString()},e.prototype.toJSON=function(){return this.toHexString()},e.prototype.equals=function(t){if(!t)return!1;if(t instanceof e)return t.id.equals(this.id);try{return new e(t).id.equals(this.id)}catch(t){return!1}},e.prototype.toBinary=function(){return new L(this.id,L.SUBTYPE_UUID)},e.generate=function(){var t=S(16);return t[6]=15&t[6]|64,t[8]=63&t[8]|128,y.from(t)},e.isValid=function(t){return!!t&&(t instanceof e||("string"==typeof t?A(t):!!O(t)&&16===t.length&&(240&t[6])==64&&(128&t[8])==128))},e.createFromHexString=function(t){return new e(I(t))},e.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},e.prototype.inspect=function(){return'new UUID("'.concat(this.toHexString(),'")')},e}(L),T=function(){function t(e,r){if(!(this instanceof t))return new t(e,r);this.code=e,this.scope=r}return t.prototype.toJSON=function(){return{code:this.code,scope:this.scope}},t.prototype.toExtendedJSON=function(){return this.scope?{$code:this.code,$scope:this.scope}:{$code:this.code}},t.fromExtendedJSON=function(e){return new t(e.$code,e.$scope)},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){var t=this.toJSON();return'new Code("'.concat(String(t.code),'"').concat(t.scope?", ".concat(JSON.stringify(t.scope)):"",")")},t}();Object.defineProperty(T.prototype,"_bsontype",{value:"Code"});var R=function(){function t(e,r,n,i){if(!(this instanceof t))return new t(e,r,n,i);var o=e.split(".");2===o.length&&(n=o.shift(),e=o.shift()),this.collection=e,this.oid=r,this.db=n,this.fields=i||{}}return Object.defineProperty(t.prototype,"namespace",{get:function(){return this.collection},set:function(t){this.collection=t},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){var t=Object.assign({$ref:this.collection,$id:this.oid},this.fields);return null!=this.db&&(t.$db=this.db),t},t.prototype.toExtendedJSON=function(t){t=t||{};var e={$ref:this.collection,$id:this.oid};return t.legacy?e:(this.db&&(e.$db=this.db),e=Object.assign(e,this.fields))},t.fromExtendedJSON=function(e){var r=Object.assign({},e);return delete r.$ref,delete r.$id,delete r.$db,new t(e.$ref,e.$id,e.$db,r)},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){var t=void 0===this.oid||void 0===this.oid.toString?this.oid:this.oid.toString();return'new DBRef("'.concat(this.namespace,'", new ObjectId("').concat(String(t),'")').concat(this.db?', "'.concat(this.db,'"'):"",")")},t}();Object.defineProperty(R.prototype,"_bsontype",{value:"DBRef"});var M=void 0;try{M=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(t){}var P={},D={},J=function(){function t(e,r,n){if(void 0===e&&(e=0),!(this instanceof t))return new t(e,r,n);"bigint"==typeof e?Object.assign(this,t.fromBigInt(e,!!r)):"string"==typeof e?Object.assign(this,t.fromString(e,!!r)):(this.low=0|e,this.high=0|r,this.unsigned=!!n),Object.defineProperty(this,"__isLong__",{value:!0,configurable:!1,writable:!1,enumerable:!1})}return t.fromBits=function(e,r,n){return new t(e,r,n)},t.fromInt=function(e,r){var n,i,o;return r?(e>>>=0,(o=0<=e&&e<256)&&(i=D[e]))?i:(n=t.fromBits(e,(0|e)<0?-1:0,!0),o&&(D[e]=n),n):(e|=0,(o=-128<=e&&e<128)&&(i=P[e]))?i:(n=t.fromBits(e,e<0?-1:0,!1),o&&(P[e]=n),n)},t.fromNumber=function(e,r){if(isNaN(e))return r?t.UZERO:t.ZERO;if(r){if(e<0)return t.UZERO;if(e>=18446744073709552e3)return t.MAX_UNSIGNED_VALUE}else{if(e<=-0x8000000000000000)return t.MIN_VALUE;if(e+1>=0x7fffffffffffffff)return t.MAX_VALUE}return e<0?t.fromNumber(-e,r).neg():t.fromBits(e%4294967296|0,e/4294967296|0,r)},t.fromBigInt=function(e,r){return t.fromString(e.toString(),r)},t.fromString=function(e,r,n){if(0===e.length)throw Error("empty string");if("NaN"===e||"Infinity"===e||"+Infinity"===e||"-Infinity"===e)return t.ZERO;if("number"==typeof r?(n=r,r=!1):r=!!r,(n=n||10)<2||36<n)throw RangeError("radix");if((i=e.indexOf("-"))>0)throw Error("interior hyphen");if(0===i)return t.fromString(e.substring(1),r,n).neg();for(var i,o=t.fromNumber(Math.pow(n,8)),s=t.ZERO,u=0;u<e.length;u+=8){var f=Math.min(8,e.length-u),h=parseInt(e.substring(u,u+f),n);if(f<8){var a=t.fromNumber(Math.pow(n,f));s=s.mul(a).add(t.fromNumber(h))}else s=(s=s.mul(o)).add(t.fromNumber(h))}return s.unsigned=r,s},t.fromBytes=function(e,r,n){return n?t.fromBytesLE(e,r):t.fromBytesBE(e,r)},t.fromBytesLE=function(e,r){return new t(e[0]|e[1]<<8|e[2]<<16|e[3]<<24,e[4]|e[5]<<8|e[6]<<16|e[7]<<24,r)},t.fromBytesBE=function(e,r){return new t(e[4]<<24|e[5]<<16|e[6]<<8|e[7],e[0]<<24|e[1]<<16|e[2]<<8|e[3],r)},t.isLong=function(t){return N(t)&&!0===t.__isLong__},t.fromValue=function(e,r){return"number"==typeof e?t.fromNumber(e,r):"string"==typeof e?t.fromString(e,r):t.fromBits(e.low,e.high,"boolean"==typeof r?r:e.unsigned)},t.prototype.add=function(e){t.isLong(e)||(e=t.fromValue(e));var r,n,i=this.high>>>16,o=65535&this.high,s=this.low>>>16,u=65535&this.low,f=e.high>>>16,h=65535&e.high,a=e.low>>>16,p=65535&e.low,c=0,l=0;return r=0+((n=0+(u+p))>>>16),n&=65535,r+=s+a,l+=r>>>16,r&=65535,l+=o+h,c+=l>>>16,l&=65535,c+=i+f,c&=65535,t.fromBits(r<<16|n,c<<16|l,this.unsigned)},t.prototype.and=function(e){return t.isLong(e)||(e=t.fromValue(e)),t.fromBits(this.low&e.low,this.high&e.high,this.unsigned)},t.prototype.compare=function(e){if(t.isLong(e)||(e=t.fromValue(e)),this.eq(e))return 0;var r=this.isNegative(),n=e.isNegative();return r&&!n?-1:!r&&n?1:this.unsigned?e.high>>>0>this.high>>>0||e.high===this.high&&e.low>>>0>this.low>>>0?-1:1:this.sub(e).isNegative()?-1:1},t.prototype.comp=function(t){return this.compare(t)},t.prototype.divide=function(e){if(t.isLong(e)||(e=t.fromValue(e)),e.isZero())throw Error("division by zero");if(M){if(!this.unsigned&&-2147483648===this.high&&-1===e.low&&-1===e.high)return this;var r,n,i,o=(this.unsigned?M.div_u:M.div_s)(this.low,this.high,e.low,e.high);return t.fromBits(o,M.get_high(),this.unsigned)}if(this.isZero())return this.unsigned?t.UZERO:t.ZERO;if(this.unsigned){if(e.unsigned||(e=e.toUnsigned()),e.gt(this))return t.UZERO;if(e.gt(this.shru(1)))return t.UONE;i=t.UZERO}else{if(this.eq(t.MIN_VALUE))return e.eq(t.ONE)||e.eq(t.NEG_ONE)?t.MIN_VALUE:e.eq(t.MIN_VALUE)?t.ONE:(r=this.shr(1).div(e).shl(1)).eq(t.ZERO)?e.isNegative()?t.ONE:t.NEG_ONE:(n=this.sub(e.mul(r)),i=r.add(n.div(e)));if(e.eq(t.MIN_VALUE))return this.unsigned?t.UZERO:t.ZERO;if(this.isNegative())return e.isNegative()?this.neg().div(e.neg()):this.neg().div(e).neg();if(e.isNegative())return this.div(e.neg()).neg();i=t.ZERO}for(n=this;n.gte(e);){for(var s=Math.ceil(Math.log(r=Math.max(1,Math.floor(n.toNumber()/e.toNumber())))/Math.LN2),u=s<=48?1:Math.pow(2,s-48),f=t.fromNumber(r),h=f.mul(e);h.isNegative()||h.gt(n);)r-=u,h=(f=t.fromNumber(r,this.unsigned)).mul(e);f.isZero()&&(f=t.ONE),i=i.add(f),n=n.sub(h)}return i},t.prototype.div=function(t){return this.divide(t)},t.prototype.equals=function(e){return t.isLong(e)||(e=t.fromValue(e)),(this.unsigned===e.unsigned||this.high>>>31!=1||e.high>>>31!=1)&&this.high===e.high&&this.low===e.low},t.prototype.eq=function(t){return this.equals(t)},t.prototype.getHighBits=function(){return this.high},t.prototype.getHighBitsUnsigned=function(){return this.high>>>0},t.prototype.getLowBits=function(){return this.low},t.prototype.getLowBitsUnsigned=function(){return this.low>>>0},t.prototype.getNumBitsAbs=function(){if(this.isNegative())return this.eq(t.MIN_VALUE)?64:this.neg().getNumBitsAbs();var e,r=0!==this.high?this.high:this.low;for(e=31;e>0&&(r&1<<e)==0;e--);return 0!==this.high?e+33:e+1},t.prototype.greaterThan=function(t){return this.comp(t)>0},t.prototype.gt=function(t){return this.greaterThan(t)},t.prototype.greaterThanOrEqual=function(t){return this.comp(t)>=0},t.prototype.gte=function(t){return this.greaterThanOrEqual(t)},t.prototype.ge=function(t){return this.greaterThanOrEqual(t)},t.prototype.isEven=function(){return(1&this.low)==0},t.prototype.isNegative=function(){return!this.unsigned&&this.high<0},t.prototype.isOdd=function(){return(1&this.low)==1},t.prototype.isPositive=function(){return this.unsigned||this.high>=0},t.prototype.isZero=function(){return 0===this.high&&0===this.low},t.prototype.lessThan=function(t){return 0>this.comp(t)},t.prototype.lt=function(t){return this.lessThan(t)},t.prototype.lessThanOrEqual=function(t){return 0>=this.comp(t)},t.prototype.lte=function(t){return this.lessThanOrEqual(t)},t.prototype.modulo=function(e){if(t.isLong(e)||(e=t.fromValue(e)),M){var r=(this.unsigned?M.rem_u:M.rem_s)(this.low,this.high,e.low,e.high);return t.fromBits(r,M.get_high(),this.unsigned)}return this.sub(this.div(e).mul(e))},t.prototype.mod=function(t){return this.modulo(t)},t.prototype.rem=function(t){return this.modulo(t)},t.prototype.multiply=function(e){if(this.isZero())return t.ZERO;if(t.isLong(e)||(e=t.fromValue(e)),M){var r=M.mul(this.low,this.high,e.low,e.high);return t.fromBits(r,M.get_high(),this.unsigned)}if(e.isZero())return t.ZERO;if(this.eq(t.MIN_VALUE))return e.isOdd()?t.MIN_VALUE:t.ZERO;if(e.eq(t.MIN_VALUE))return this.isOdd()?t.MIN_VALUE:t.ZERO;if(this.isNegative())return e.isNegative()?this.neg().mul(e.neg()):this.neg().mul(e).neg();if(e.isNegative())return this.mul(e.neg()).neg();if(this.lt(t.TWO_PWR_24)&&e.lt(t.TWO_PWR_24))return t.fromNumber(this.toNumber()*e.toNumber(),this.unsigned);var n,i,o=this.high>>>16,s=65535&this.high,u=this.low>>>16,f=65535&this.low,h=e.high>>>16,a=65535&e.high,p=e.low>>>16,c=65535&e.low,l=0,g=0;return n=0+((i=0+f*c)>>>16),i&=65535,n+=u*c,g+=n>>>16,n&=65535,n+=f*p,g+=n>>>16,n&=65535,g+=s*c,l+=g>>>16,g&=65535,g+=u*p,l+=g>>>16,g&=65535,g+=f*a,l+=g>>>16,g&=65535,l+=o*c+s*p+u*a+f*h,l&=65535,t.fromBits(n<<16|i,l<<16|g,this.unsigned)},t.prototype.mul=function(t){return this.multiply(t)},t.prototype.negate=function(){return!this.unsigned&&this.eq(t.MIN_VALUE)?t.MIN_VALUE:this.not().add(t.ONE)},t.prototype.neg=function(){return this.negate()},t.prototype.not=function(){return t.fromBits(~this.low,~this.high,this.unsigned)},t.prototype.notEquals=function(t){return!this.equals(t)},t.prototype.neq=function(t){return this.notEquals(t)},t.prototype.ne=function(t){return this.notEquals(t)},t.prototype.or=function(e){return t.isLong(e)||(e=t.fromValue(e)),t.fromBits(this.low|e.low,this.high|e.high,this.unsigned)},t.prototype.shiftLeft=function(e){return(t.isLong(e)&&(e=e.toInt()),0==(e&=63))?this:e<32?t.fromBits(this.low<<e,this.high<<e|this.low>>>32-e,this.unsigned):t.fromBits(0,this.low<<e-32,this.unsigned)},t.prototype.shl=function(t){return this.shiftLeft(t)},t.prototype.shiftRight=function(e){return(t.isLong(e)&&(e=e.toInt()),0==(e&=63))?this:e<32?t.fromBits(this.low>>>e|this.high<<32-e,this.high>>e,this.unsigned):t.fromBits(this.high>>e-32,this.high>=0?0:-1,this.unsigned)},t.prototype.shr=function(t){return this.shiftRight(t)},t.prototype.shiftRightUnsigned=function(e){if(t.isLong(e)&&(e=e.toInt()),0==(e&=63))return this;var r=this.high;if(e<32){var n=this.low;return t.fromBits(n>>>e|r<<32-e,r>>>e,this.unsigned)}return 32===e?t.fromBits(r,0,this.unsigned):t.fromBits(r>>>e-32,0,this.unsigned)},t.prototype.shr_u=function(t){return this.shiftRightUnsigned(t)},t.prototype.shru=function(t){return this.shiftRightUnsigned(t)},t.prototype.subtract=function(e){return t.isLong(e)||(e=t.fromValue(e)),this.add(e.neg())},t.prototype.sub=function(t){return this.subtract(t)},t.prototype.toInt=function(){return this.unsigned?this.low>>>0:this.low},t.prototype.toNumber=function(){return this.unsigned?(this.high>>>0)*4294967296+(this.low>>>0):4294967296*this.high+(this.low>>>0)},t.prototype.toBigInt=function(){return BigInt(this.toString())},t.prototype.toBytes=function(t){return t?this.toBytesLE():this.toBytesBE()},t.prototype.toBytesLE=function(){var t=this.high,e=this.low;return[255&e,e>>>8&255,e>>>16&255,e>>>24,255&t,t>>>8&255,t>>>16&255,t>>>24]},t.prototype.toBytesBE=function(){var t=this.high,e=this.low;return[t>>>24,t>>>16&255,t>>>8&255,255&t,e>>>24,e>>>16&255,e>>>8&255,255&e]},t.prototype.toSigned=function(){return this.unsigned?t.fromBits(this.low,this.high,!1):this},t.prototype.toString=function(e){if((e=e||10)<2||36<e)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(!this.eq(t.MIN_VALUE))return"-"+this.neg().toString(e);var r=t.fromNumber(e),n=this.div(r),i=n.mul(r).sub(this);return n.toString(e)+i.toInt().toString(e)}for(var o=t.fromNumber(Math.pow(e,6),this.unsigned),s=this,u="";;){var f=s.div(o),h=(s.sub(f.mul(o)).toInt()>>>0).toString(e);if((s=f).isZero())return h+u;for(;h.length<6;)h="0"+h;u=""+h+u}},t.prototype.toUnsigned=function(){return this.unsigned?this:t.fromBits(this.low,this.high,!0)},t.prototype.xor=function(e){return t.isLong(e)||(e=t.fromValue(e)),t.fromBits(this.low^e.low,this.high^e.high,this.unsigned)},t.prototype.eqz=function(){return this.isZero()},t.prototype.le=function(t){return this.lessThanOrEqual(t)},t.prototype.toExtendedJSON=function(t){return t&&t.relaxed?this.toNumber():{$numberLong:this.toString()}},t.fromExtendedJSON=function(e,r){var n=t.fromString(e.$numberLong);return r&&r.relaxed?n.toNumber():n},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){return'new Long("'.concat(this.toString(),'"').concat(this.unsigned?", true":"",")")},t.TWO_PWR_24=t.fromInt(16777216),t.MAX_UNSIGNED_VALUE=t.fromBits(-1,-1,!0),t.ZERO=t.fromInt(0),t.UZERO=t.fromInt(0,!0),t.ONE=t.fromInt(1),t.UONE=t.fromInt(1,!0),t.NEG_ONE=t.fromInt(-1),t.MAX_VALUE=t.fromBits(-1,2147483647,!1),t.MIN_VALUE=t.fromBits(0,-2147483648,!1),t}();Object.defineProperty(J.prototype,"__isLong__",{value:!0}),Object.defineProperty(J.prototype,"_bsontype",{value:"Long"});var C=/^(\+|-)?(\d+|(\d*\.\d*))?(E|e)?([-+])?(\d+)?$/,V=/^(\+|-)?(Infinity|inf)$/i,k=/^(\+|-)?NaN$/i,q=[124,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].reverse(),H=[248,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].reverse(),Z=[120,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].reverse(),F=/^([-+])?(\d+)?$/;function Y(t){return!isNaN(parseInt(t,10))}function z(t,e){throw new v('"'.concat(t,'" is not a valid Decimal128 string - ').concat(e))}var W=function(){function t(e){if(!(this instanceof t))return new t(e);if("string"==typeof e)this.bytes=t.fromString(e).bytes;else if(O(e)){if(16!==e.byteLength)throw new v("Decimal128 must take a Buffer of 16 bytes");this.bytes=e}else throw new v("Decimal128 must take a Buffer or string")}return t.fromString=function(e){var r,n,i,o,s=!1,u=!1,f=!1,h=0,a=0,p=0,c=0,l=0,g=[0],d=0,m=0,b=0,w=0,x=0,E=0,S=new J(0,0),O=new J(0,0),N=0,B=0;if(e.length>=7e3)throw new v(""+e+" not a valid Decimal128 string");var _=e.match(C),U=e.match(V),A=e.match(k);if(!_&&!U&&!A||0===e.length)throw new v(""+e+" not a valid Decimal128 string");if(_){var I=_[2],j=_[4],L=_[5],$=_[6];j&&void 0===$&&z(e,"missing exponent power"),j&&void 0===I&&z(e,"missing exponent base"),void 0===j&&(L||$)&&z(e,"missing e before exponent")}if(("+"===e[B]||"-"===e[B])&&(s="-"===e[B++]),!Y(e[B])&&"."!==e[B]){if("i"===e[B]||"I"===e[B])return new t(y.from(s?H:Z));if("N"===e[B])return new t(y.from(q))}for(;Y(e[B])||"."===e[B];){if("."===e[B]){u&&z(e,"contains multiple periods"),u=!0,B+=1;continue}d<34&&("0"!==e[B]||f)&&(f||(l=a),f=!0,g[m++]=parseInt(e[B],10),d+=1),f&&(p+=1),u&&(c+=1),a+=1,B+=1}if(u&&!a)throw new v(""+e+" not a valid Decimal128 string");if("e"===e[B]||"E"===e[B]){var T=e.substr(++B).match(F);if(!T||!T[2])return new t(y.from(q));x=parseInt(T[0],10),B+=T[0].length}if(e[B])return new t(y.from(q));if(b=0,d){if(w=d-1,1!==(h=p))for(;0===g[l+h-1];)h-=1}else b=0,w=0,g[0]=0,p=1,d=1,h=0;for(x<=c&&c-x>16384?x=-6176:x-=c;x>6111;){if((w+=1)-b>34){var R=g.join("");if(R.match(/^0+$/)){x=6111;break}z(e,"overflow")}x-=1}for(;x<-6176||d<p;){if(0===w&&h<d){x=-6176,h=0;break}if(d<p?p-=1:w-=1,x<6111)x+=1;else{var R=g.join("");if(R.match(/^0+$/)){x=6111;break}z(e,"overflow")}}if(w-b+1<h){var M=a;u&&(l+=1,M+=1),s&&(l+=1,M+=1);var P=parseInt(e[l+w+1],10),D=0;if(P>=5&&(D=1,5===P)){for(D=g[w]%2==1?1:0,E=l+w+2;E<M;E++)if(parseInt(e[E],10)){D=1;break}}if(D){for(var W=w;W>=0;W--)if(++g[W]>9&&(g[W]=0,0===W)){if(!(x<6111))return new t(y.from(s?H:Z));x+=1,g[W]=1}}}if(S=J.fromNumber(0),O=J.fromNumber(0),0===h)S=J.fromNumber(0),O=J.fromNumber(0);else if(w-b<17){var W=b;for(O=J.fromNumber(g[W++]),S=new J(0,0);W<=w;W++)O=(O=O.multiply(J.fromNumber(10))).add(J.fromNumber(g[W]))}else{var W=b;for(S=J.fromNumber(g[W++]);W<=w-17;W++)S=(S=S.multiply(J.fromNumber(10))).add(J.fromNumber(g[W]));for(O=J.fromNumber(g[W++]);W<=w;W++)O=(O=O.multiply(J.fromNumber(10))).add(J.fromNumber(g[W]))}var K=function(t,e){if(!t&&!e)return{high:J.fromNumber(0),low:J.fromNumber(0)};var r=t.shiftRightUnsigned(32),n=new J(t.getLowBits(),0),i=e.shiftRightUnsigned(32),o=new J(e.getLowBits(),0),s=r.multiply(i),u=r.multiply(o),f=n.multiply(i),h=n.multiply(o);return s=s.add(u.shiftRightUnsigned(32)),u=new J(u.getLowBits(),0).add(f).add(h.shiftRightUnsigned(32)),{high:s=s.add(u.shiftRightUnsigned(32)),low:h=u.shiftLeft(32).add(new J(h.getLowBits(),0))}}(S,J.fromString("100000000000000000"));K.low=K.low.add(O),r=K.low,n=O,((i=r.high>>>0)<(o=n.high>>>0)||i===o&&r.low>>>0<n.low>>>0)&&(K.high=K.high.add(J.fromNumber(1))),N=x+6176;var X={low:J.fromNumber(0),high:J.fromNumber(0)};K.high.shiftRightUnsigned(49).and(J.fromNumber(1)).equals(J.fromNumber(1))?(X.high=X.high.or(J.fromNumber(3).shiftLeft(61)),X.high=X.high.or(J.fromNumber(N).and(J.fromNumber(16383).shiftLeft(47))),X.high=X.high.or(K.high.and(J.fromNumber(0x7fffffffffff)))):(X.high=X.high.or(J.fromNumber(16383&N).shiftLeft(49)),X.high=X.high.or(K.high.and(J.fromNumber(562949953421311)))),X.low=K.low,s&&(X.high=X.high.or(J.fromString("9223372036854775808")));var G=y.alloc(16);return B=0,G[B++]=255&X.low.low,G[B++]=X.low.low>>8&255,G[B++]=X.low.low>>16&255,G[B++]=X.low.low>>24&255,G[B++]=255&X.low.high,G[B++]=X.low.high>>8&255,G[B++]=X.low.high>>16&255,G[B++]=X.low.high>>24&255,G[B++]=255&X.high.low,G[B++]=X.high.low>>8&255,G[B++]=X.high.low>>16&255,G[B++]=X.high.low>>24&255,G[B++]=255&X.high.high,G[B++]=X.high.high>>8&255,G[B++]=X.high.high>>16&255,G[B++]=X.high.high>>24&255,new t(G)},t.prototype.toString=function(){for(var t,e,r,n,i=0,o=Array(36),s=0;s<o.length;s++)o[s]=0;var u=0,f=!1,h={parts:[0,0,0,0]},a=[];u=0;var p=this.bytes,c=p[u++]|p[u++]<<8|p[u++]<<16|p[u++]<<24,l=p[u++]|p[u++]<<8|p[u++]<<16|p[u++]<<24,g=p[u++]|p[u++]<<8|p[u++]<<16|p[u++]<<24,y=p[u++]|p[u++]<<8|p[u++]<<16|p[u++]<<24;u=0,({low:new J(c,l),high:new J(g,y)}).high.lessThan(J.ZERO)&&a.push("-");var d=y>>26&31;if(d>>3==3){if(30===d)return a.join("")+"Infinity";if(31===d)return"NaN";t=y>>15&16383,e=8+(y>>14&1)}else e=y>>14&7,t=y>>17&16383;var m=t-6176;if(h.parts[0]=(16383&y)+((15&e)<<14),h.parts[1]=g,h.parts[2]=l,h.parts[3]=c,0===h.parts[0]&&0===h.parts[1]&&0===h.parts[2]&&0===h.parts[3])f=!0;else for(n=3;n>=0;n--){var b=0,v=function(t){var e=J.fromNumber(1e9),r=J.fromNumber(0);if(!t.parts[0]&&!t.parts[1]&&!t.parts[2]&&!t.parts[3])return{quotient:t,rem:r};for(var n=0;n<=3;n++)r=(r=r.shiftLeft(32)).add(new J(t.parts[n],0)),t.parts[n]=r.div(e).low,r=r.modulo(e);return{quotient:t,rem:r}}(h);if(h=v.quotient,b=v.rem.low)for(r=8;r>=0;r--)o[9*n+r]=b%10,b=Math.floor(b/10)}if(f)i=1,o[u]=0;else for(i=36;!o[u];)i-=1,u+=1;var w=i-1+m;if(w>=34||w<=-7||m>0){if(i>34)return a.push("".concat(0)),m>0?a.push("E+".concat(m)):m<0&&a.push("E".concat(m)),a.join("");a.push("".concat(o[u++])),(i-=1)&&a.push(".");for(var s=0;s<i;s++)a.push("".concat(o[u++]));a.push("E"),w>0?a.push("+".concat(w)):a.push("".concat(w))}else if(m>=0)for(var s=0;s<i;s++)a.push("".concat(o[u++]));else{var x=i+m;if(x>0)for(var s=0;s<x;s++)a.push("".concat(o[u++]));else a.push("0");for(a.push(".");x++<0;)a.push("0");for(var s=0;s<i-Math.max(x-1,0);s++)a.push("".concat(o[u++]))}return a.join("")},t.prototype.toJSON=function(){return{$numberDecimal:this.toString()}},t.prototype.toExtendedJSON=function(){return{$numberDecimal:this.toString()}},t.fromExtendedJSON=function(e){return t.fromString(e.$numberDecimal)},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){return'new Decimal128("'.concat(this.toString(),'")')},t}();Object.defineProperty(W.prototype,"_bsontype",{value:"Decimal128"});var K=function(){function t(e){if(!(this instanceof t))return new t(e);e instanceof Number&&(e=e.valueOf()),this.value=+e}return t.prototype.valueOf=function(){return this.value},t.prototype.toJSON=function(){return this.value},t.prototype.toString=function(t){return this.value.toString(t)},t.prototype.toExtendedJSON=function(t){return t&&(t.legacy||t.relaxed&&isFinite(this.value))?this.value:Object.is(Math.sign(this.value),-0)?{$numberDouble:"-".concat(this.value.toFixed(1))}:{$numberDouble:Number.isInteger(this.value)?this.value.toFixed(1):this.value.toString()}},t.fromExtendedJSON=function(e,r){var n=parseFloat(e.$numberDouble);return r&&r.relaxed?n:new t(n)},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){var t=this.toExtendedJSON();return"new Double(".concat(t.$numberDouble,")")},t}();Object.defineProperty(K.prototype,"_bsontype",{value:"Double"});var X=function(){function t(e){if(!(this instanceof t))return new t(e);e instanceof Number&&(e=e.valueOf()),this.value=0|+e}return t.prototype.valueOf=function(){return this.value},t.prototype.toString=function(t){return this.value.toString(t)},t.prototype.toJSON=function(){return this.value},t.prototype.toExtendedJSON=function(t){return t&&(t.relaxed||t.legacy)?this.value:{$numberInt:this.value.toString()}},t.fromExtendedJSON=function(e,r){return r&&r.relaxed?parseInt(e.$numberInt,10):new t(e.$numberInt)},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){return"new Int32(".concat(this.valueOf(),")")},t}();Object.defineProperty(X.prototype,"_bsontype",{value:"Int32"});var G=function(){function t(){if(!(this instanceof t))return new t}return t.prototype.toExtendedJSON=function(){return{$maxKey:1}},t.fromExtendedJSON=function(){return new t},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){return"new MaxKey()"},t}();Object.defineProperty(G.prototype,"_bsontype",{value:"MaxKey"});var Q=function(){function t(){if(!(this instanceof t))return new t}return t.prototype.toExtendedJSON=function(){return{$minKey:1}},t.fromExtendedJSON=function(){return new t},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){return"new MinKey()"},t}();Object.defineProperty(Q.prototype,"_bsontype",{value:"MinKey"});var tt=RegExp("^[0-9a-fA-F]{24}$"),te=null,tr=Symbol("id"),tn=function(){function t(e){if(!(this instanceof t))return new t(e);if("object"==typeof e&&e&&"id"in e){if("string"!=typeof e.id&&!ArrayBuffer.isView(e.id))throw new v("Argument passed in must have an id that is of type string or Buffer");r="toHexString"in e&&"function"==typeof e.toHexString?y.from(e.toHexString(),"hex"):e.id}else r=e;if(null==r||"number"==typeof r)this[tr]=t.generate("number"==typeof r?r:void 0);else if(ArrayBuffer.isView(r)&&12===r.byteLength)this[tr]=r instanceof y?r:_(r);else if("string"==typeof r){if(12===r.length){var r,n=y.from(r);if(12===n.byteLength)this[tr]=n;else throw new v("Argument passed in must be a string of 12 bytes")}else if(24===r.length&&tt.test(r))this[tr]=y.from(r,"hex");else throw new v("Argument passed in must be a string of 12 bytes or a string of 24 hex characters or an integer")}else throw new v("Argument passed in does not match the accepted types");t.cacheHexString&&(this.__id=this.id.toString("hex"))}return Object.defineProperty(t.prototype,"id",{get:function(){return this[tr]},set:function(e){this[tr]=e,t.cacheHexString&&(this.__id=e.toString("hex"))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"generationTime",{get:function(){return this.id.readInt32BE(0)},set:function(t){this.id.writeUInt32BE(t,0)},enumerable:!1,configurable:!0}),t.prototype.toHexString=function(){if(t.cacheHexString&&this.__id)return this.__id;var e=this.id.toString("hex");return t.cacheHexString&&!this.__id&&(this.__id=e),e},t.getInc=function(){return t.index=(t.index+1)%16777215},t.generate=function(e){"number"!=typeof e&&(e=Math.floor(Date.now()/1e3));var r=t.getInc(),n=y.alloc(12);return n.writeUInt32BE(e,0),null===te&&(te=S(5)),n[4]=te[0],n[5]=te[1],n[6]=te[2],n[7]=te[3],n[8]=te[4],n[11]=255&r,n[10]=r>>8&255,n[9]=r>>16&255,n},t.prototype.toString=function(t){return t?this.id.toString(t):this.toHexString()},t.prototype.toJSON=function(){return this.toHexString()},t.prototype.equals=function(e){if(null==e)return!1;if(e instanceof t)return this[tr][11]===e[tr][11]&&this[tr].equals(e[tr]);if("string"==typeof e&&t.isValid(e)&&12===e.length&&O(this.id))return e===y.prototype.toString.call(this.id,"latin1");if("string"==typeof e&&t.isValid(e)&&24===e.length)return e.toLowerCase()===this.toHexString();if("string"==typeof e&&t.isValid(e)&&12===e.length)return y.from(e).equals(this.id);if("object"==typeof e&&"toHexString"in e&&"function"==typeof e.toHexString){var r=e.toHexString(),n=this.toHexString().toLowerCase();return"string"==typeof r&&r.toLowerCase()===n}return!1},t.prototype.getTimestamp=function(){var t=new Date,e=this.id.readUInt32BE(0);return t.setTime(1e3*Math.floor(e)),t},t.createPk=function(){return new t},t.createFromTime=function(e){var r=y.from([0,0,0,0,0,0,0,0,0,0,0,0]);return r.writeUInt32BE(e,0),new t(r)},t.createFromHexString=function(e){if(void 0===e||null!=e&&24!==e.length)throw new v("Argument passed in must be a single String of 12 bytes or a string of 24 hex characters");return new t(y.from(e,"hex"))},t.isValid=function(e){if(null==e)return!1;try{return new t(e),!0}catch(t){return!1}},t.prototype.toExtendedJSON=function(){return this.toHexString?{$oid:this.toHexString()}:{$oid:this.toString("hex")}},t.fromExtendedJSON=function(e){return new t(e.$oid)},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){return'new ObjectId("'.concat(this.toHexString(),'")')},t.index=Math.floor(16777215*Math.random()),t}();Object.defineProperty(tn.prototype,"generate",{value:B(function(t){return tn.generate(t)},"Please use the static `ObjectId.generate(time)` instead")}),Object.defineProperty(tn.prototype,"getInc",{value:B(function(){return tn.getInc()},"Please use the static `ObjectId.getInc()` instead")}),Object.defineProperty(tn.prototype,"get_inc",{value:B(function(){return tn.getInc()},"Please use the static `ObjectId.getInc()` instead")}),Object.defineProperty(tn,"get_inc",{value:B(function(){return tn.getInc()},"Please use the static `ObjectId.getInc()` instead")}),Object.defineProperty(tn.prototype,"_bsontype",{value:"ObjectID"});var ti=function(){function t(e,r){if(!(this instanceof t))return new t(e,r);if(this.pattern=e,this.options=(null!=r?r:"").split("").sort().join(""),-1!==this.pattern.indexOf("\0"))throw new b("BSON Regex patterns cannot contain null bytes, found: ".concat(JSON.stringify(this.pattern)));if(-1!==this.options.indexOf("\0"))throw new b("BSON Regex options cannot contain null bytes, found: ".concat(JSON.stringify(this.options)));for(var n=0;n<this.options.length;n++)if(!("i"===this.options[n]||"m"===this.options[n]||"x"===this.options[n]||"l"===this.options[n]||"s"===this.options[n]||"u"===this.options[n]))throw new b("The regular expression option [".concat(this.options[n],"] is not supported"))}return t.parseOptions=function(t){return t?t.split("").sort().join(""):""},t.prototype.toExtendedJSON=function(t){return(t=t||{}).legacy?{$regex:this.pattern,$options:this.options}:{$regularExpression:{pattern:this.pattern,options:this.options}}},t.fromExtendedJSON=function(e){if("$regex"in e){if("string"==typeof e.$regex)return new t(e.$regex,t.parseOptions(e.$options));if("BSONRegExp"===e.$regex._bsontype)return e}if("$regularExpression"in e)return new t(e.$regularExpression.pattern,t.parseOptions(e.$regularExpression.options));throw new v("Unexpected BSONRegExp EJSON object form: ".concat(JSON.stringify(e)))},t}();Object.defineProperty(ti.prototype,"_bsontype",{value:"BSONRegExp"});var to=function(){function t(e){if(!(this instanceof t))return new t(e);this.value=e}return t.prototype.valueOf=function(){return this.value},t.prototype.toString=function(){return this.value},t.prototype.inspect=function(){return'new BSONSymbol("'.concat(this.value,'")')},t.prototype.toJSON=function(){return this.value},t.prototype.toExtendedJSON=function(){return{$symbol:this.value}},t.fromExtendedJSON=function(e){return new t(e.$symbol)},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t}();Object.defineProperty(to.prototype,"_bsontype",{value:"Symbol"});var ts=function(t){function e(r,n){var i=this;return i instanceof e?(Object.defineProperty(i=J.isLong(r)?t.call(this,r.low,r.high,!0)||this:N(r)&&void 0!==r.t&&void 0!==r.i?t.call(this,r.i,r.t,!0)||this:t.call(this,r,n,!0)||this,"_bsontype",{value:"Timestamp",writable:!1,configurable:!1,enumerable:!1}),i):new e(r,n)}return m(e,t),e.prototype.toJSON=function(){return{$timestamp:this.toString()}},e.fromInt=function(t){return new e(J.fromInt(t,!0))},e.fromNumber=function(t){return new e(J.fromNumber(t,!0))},e.fromBits=function(t,r){return new e(t,r)},e.fromString=function(t,r){return new e(J.fromString(t,!0,r))},e.prototype.toExtendedJSON=function(){return{$timestamp:{t:this.high>>>0,i:this.low>>>0}}},e.fromExtendedJSON=function(t){return new e(t.$timestamp)},e.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},e.prototype.inspect=function(){return"new Timestamp({ t: ".concat(this.getHighBits(),", i: ").concat(this.getLowBits()," })")},e.MAX_VALUE=J.MAX_UNSIGNED_VALUE,e}(J),tu={$oid:tn,$binary:L,$uuid:L,$symbol:to,$numberInt:X,$numberDecimal:W,$numberDouble:K,$numberLong:J,$minKey:Q,$maxKey:G,$regex:ti,$regularExpression:ti,$timestamp:ts};function tf(t){var e=t.toISOString();return 0!==t.getUTCMilliseconds()?e:e.slice(0,-5)+"Z"}var th={Binary:function(t){return new L(t.value(),t.sub_type)},Code:function(t){return new T(t.code,t.scope)},DBRef:function(t){return new R(t.collection||t.namespace,t.oid,t.db,t.fields)},Decimal128:function(t){return new W(t.bytes)},Double:function(t){return new K(t.value)},Int32:function(t){return new X(t.value)},Long:function(t){return J.fromBits(null!=t.low?t.low:t.low_,null!=t.low?t.high:t.high_,null!=t.low?t.unsigned:t.unsigned_)},MaxKey:function(){return new G},MinKey:function(){return new Q},ObjectID:function(t){return new tn(t)},ObjectId:function(t){return new tn(t)},BSONRegExp:function(t){return new ti(t.pattern,t.options)},Symbol:function(t){return new to(t.value)},Timestamp:function(t){return ts.fromBits(t.low,t.high)}};!function(t){function e(t,e){var r=Object.assign({},{relaxed:!0,legacy:!1},e);return"boolean"==typeof r.relaxed&&(r.strict=!r.relaxed),"boolean"==typeof r.strict&&(r.relaxed=!r.strict),JSON.parse(t,function(t,e){if(-1!==t.indexOf("\0"))throw new b("BSON Document field names cannot contain null bytes, found: ".concat(JSON.stringify(t)));return function t(e,r){if(void 0===r&&(r={}),"number"==typeof e){if(r.relaxed||r.legacy)return e;if(Math.floor(e)===e){if(e>=-2147483648&&e<=2147483647)return new X(e);if(e>=-0x8000000000000000&&e<=0x7fffffffffffffff)return J.fromNumber(e)}return new K(e)}if(null==e||"object"!=typeof e)return e;if(e.$undefined)return null;for(var n=Object.keys(e).filter(function(t){return t.startsWith("$")&&null!=e[t]}),i=0;i<n.length;i++){var o=tu[n[i]];if(o)return o.fromExtendedJSON(e,r)}if(null!=e.$date){var s=e.$date,u=new Date;return r.legacy?"number"==typeof s?u.setTime(s):"string"==typeof s&&u.setTime(Date.parse(s)):"string"==typeof s?u.setTime(Date.parse(s)):J.isLong(s)?u.setTime(s.toNumber()):"number"==typeof s&&r.relaxed&&u.setTime(s),u}if(null!=e.$code){var f=Object.assign({},e);return e.$scope&&(f.$scope=t(e.$scope)),T.fromExtendedJSON(e)}if(N(e)&&null!=e.$id&&"string"==typeof e.$ref&&(null==e.$db||"string"==typeof e.$db)||e.$dbPointer){var h=e.$ref?e:e.$dbPointer;if(h instanceof R)return h;var a=Object.keys(h).filter(function(t){return t.startsWith("$")}),p=!0;if(a.forEach(function(t){-1===["$ref","$id","$db"].indexOf(t)&&(p=!1)}),p)return R.fromExtendedJSON(h)}return e}(e,r)})}function r(t,e,r,n){return null!=r&&"object"==typeof r&&(n=r,r=0),null==e||"object"!=typeof e||Array.isArray(e)||(n=e,e=void 0,r=0),JSON.stringify(function t(e,r){if(("object"==typeof e||"function"==typeof e)&&null!==e){var n=r.seenObjects.findIndex(function(t){return t.obj===e});if(-1!==n){var i=r.seenObjects.map(function(t){return t.propertyName}),o=i.slice(0,n).map(function(t){return"".concat(t," -> ")}).join(""),s=i[n],u=" -> "+i.slice(n+1,i.length-1).map(function(t){return"".concat(t," -> ")}).join(""),f=i[i.length-1],h=" ".repeat(o.length+s.length/2),a="-".repeat(u.length+(s.length+f.length)/2-1);throw new v("Converting circular structure to EJSON:\n"+"    ".concat(o).concat(s).concat(u).concat(f,"\n")+"    ".concat(h,"\\").concat(a,"/"))}r.seenObjects[r.seenObjects.length-1].obj=e}if(Array.isArray(e))return e.map(function(e,n){r.seenObjects.push({propertyName:"index ".concat(n),obj:null});try{return t(e,r)}finally{r.seenObjects.pop()}});if(void 0===e)return null;if(e instanceof Date||N(e)&&"[object Date]"===Object.prototype.toString.call(e)){var p=e.getTime(),c=p>-1&&p<2534023188e5;return r.legacy?r.relaxed&&c?{$date:e.getTime()}:{$date:tf(e)}:r.relaxed&&c?{$date:tf(e)}:{$date:{$numberLong:e.getTime().toString()}}}if("number"==typeof e&&(!r.relaxed||!isFinite(e))){if(Math.floor(e)===e){var l=e>=-2147483648&&e<=2147483647,g=e>=-0x8000000000000000&&e<=0x7fffffffffffffff;if(l)return{$numberInt:e.toString()};if(g)return{$numberLong:e.toString()}}return{$numberDouble:e.toString()}}if(e instanceof RegExp||"[object RegExp]"===Object.prototype.toString.call(e)){var y=e.flags;if(void 0===y){var d=e.toString().match(/[gimuy]*$/);d&&(y=d[0])}return new ti(e.source,y).toExtendedJSON(r)}return null!=e&&"object"==typeof e?function(e,r){if(null==e||"object"!=typeof e)throw new b("not an object instance");var n=e._bsontype;if(void 0===n){var i={};for(var o in e){r.seenObjects.push({propertyName:o,obj:null});try{var s=t(e[o],r);"__proto__"===o?Object.defineProperty(i,o,{value:s,writable:!0,enumerable:!0,configurable:!0}):i[o]=s}finally{r.seenObjects.pop()}}return i}if(N(e)&&Reflect.has(e,"_bsontype")&&"string"==typeof e._bsontype){var u=e;if("function"!=typeof u.toExtendedJSON){var f=th[e._bsontype];if(!f)throw new v("Unrecognized or invalid _bsontype: "+e._bsontype);u=f(u)}return"Code"===n&&u.scope?u=new T(u.code,t(u.scope,r)):"DBRef"===n&&u.oid&&(u=new R(t(u.collection,r),t(u.oid,r),t(u.db,r),t(u.fields,r))),u.toExtendedJSON(r)}throw new b("_bsontype must be a string, but was: "+typeof n)}(e,r):e}(t,Object.assign({relaxed:!0,legacy:!1},n,{seenObjects:[{propertyName:"(root)",obj:null}]})),e,r)}t.parse=e,t.stringify=r,t.serialize=function(t,e){return JSON.parse(r(t,e=e||{}))},t.deserialize=function(t,r){return r=r||{},e(JSON.stringify(t),r)}}(n||(n={}));var ta=x();ta.Map?ta.Map:function(){function t(t){void 0===t&&(t=[]),this._keys=[],this._values={};for(var e=0;e<t.length;e++)if(null!=t[e]){var r=t[e],n=r[0],i=r[1];this._keys.push(n),this._values[n]={v:i,i:this._keys.length-1}}}return t.prototype.clear=function(){this._keys=[],this._values={}},t.prototype.delete=function(t){var e=this._values[t];return null!=e&&(delete this._values[t],this._keys.splice(e.i,1),!0)},t.prototype.entries=function(){var t=this,e=0;return{next:function(){var r=t._keys[e++];return{value:void 0!==r?[r,t._values[r].v]:void 0,done:void 0===r}}}},t.prototype.forEach=function(t,e){e=e||this;for(var r=0;r<this._keys.length;r++){var n=this._keys[r];t.call(e,this._values[n].v,n,e)}},t.prototype.get=function(t){return this._values[t]?this._values[t].v:void 0},t.prototype.has=function(t){return null!=this._values[t]},t.prototype.keys=function(){var t=this,e=0;return{next:function(){var r=t._keys[e++];return{value:void 0!==r?r:void 0,done:void 0===r}}}},t.prototype.set=function(t,e){return this._values[t]?this._values[t].v=e:(this._keys.push(t),this._values[t]={v:e,i:this._keys.length-1}),this},t.prototype.values=function(){var t=this,e=0;return{next:function(){var r=t._keys[e++];return{value:void 0!==r?t._values[r].v:void 0,done:void 0===r}}}},Object.defineProperty(t.prototype,"size",{get:function(){return this._keys.length},enumerable:!1,configurable:!0}),t}(),J.fromNumber(9007199254740992),J.fromNumber(-9007199254740992);var tp=new Uint8Array(8);new DataView(tp.buffer,tp.byteOffset,tp.byteLength),y.alloc(17825792)}}]);