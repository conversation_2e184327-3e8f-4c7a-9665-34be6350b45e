// 系统设置相关工具函数

export interface SystemSettings {
  maxImageSize: number; // 单位：MB
  maxImagesPerPost: number;
  allowedImageTypes: string[];
}

// 默认系统设置
const DEFAULT_SETTINGS: SystemSettings = {
  maxImageSize: 5, // 默认5MB
  maxImagesPerPost: 9,
  allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp']
};

// 获取系统设置
export const getSystemSettings = (): SystemSettings => {
  try {
    if (typeof window === 'undefined') {
      return DEFAULT_SETTINGS;
    }

    const savedSettings = localStorage.getItem('systemSettings');
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      // 确保所有必需的字段都存在
      return {
        maxImageSize: settings.maxImageSize || DEFAULT_SETTINGS.maxImageSize,
        maxImagesPerPost: settings.maxImagesPerPost || DEFAULT_SETTINGS.maxImagesPerPost,
        allowedImageTypes: settings.allowedImageTypes || DEFAULT_SETTINGS.allowedImageTypes
      };
    }
    
    return DEFAULT_SETTINGS;
  } catch (error) {
    console.error('获取系统设置失败:', error);
    return DEFAULT_SETTINGS;
  }
};

// 保存系统设置
export const saveSystemSettings = (settings: SystemSettings): boolean => {
  try {
    if (typeof window === 'undefined') {
      return false;
    }

    localStorage.setItem('systemSettings', JSON.stringify(settings));
    return true;
  } catch (error) {
    console.error('保存系统设置失败:', error);
    return false;
  }
};

// 格式化文件大小显示
export const formatFileSize = (sizeInMB: number): string => {
  if (sizeInMB >= 1024) {
    return `${(sizeInMB / 1024).toFixed(1)}GB`;
  }
  return `${sizeInMB}MB`;
};

// 获取支持的文件类型显示文本
export const getFileTypesText = (allowedTypes: string[]): string => {
  const typeMap: { [key: string]: string } = {
    'image/jpeg': 'JPG',
    'image/png': 'PNG',
    'image/webp': 'WebP',
    'image/gif': 'GIF'
  };

  const displayTypes = allowedTypes
    .map(type => typeMap[type] || type.replace('image/', '').toUpperCase())
    .filter(Boolean);

  return displayTypes.join('、');
};

// 验证文件是否符合系统设置
export const validateFile = (file: File, settings: SystemSettings): { valid: boolean; error?: string } => {
  // 检查文件类型
  if (!settings.allowedImageTypes.includes(file.type)) {
    const supportedTypes = getFileTypesText(settings.allowedImageTypes);
    return {
      valid: false,
      error: `不支持的文件格式，请上传 ${supportedTypes} 格式的图片`
    };
  }

  // 检查文件大小
  const maxSizeInBytes = settings.maxImageSize * 1024 * 1024;
  if (file.size > maxSizeInBytes) {
    return {
      valid: false,
      error: `文件大小超过限制，单张图片不能超过 ${formatFileSize(settings.maxImageSize)}`
    };
  }

  return { valid: true };
};
