// 智能品种归类系统 - 基于现有9个一级分类和对应二级分类

export interface BreedClassification {
  primaryCategory: string;      // 一级分类ID：dogs、cats等
  primaryCategoryName: string;  // 一级分类名称：狗狗、猫咪等
  secondaryCategory?: string;   // 二级分类ID：small_dogs、short_hair_cats等
  secondaryCategoryName?: string; // 二级分类名称：小型犬、短毛猫等
  confidence: number;           // 匹配置信度 0-1
  isGeneralCategory?: boolean;  // 是否为大类匹配
}

// 精选品种归类数据库（核心品种）
const CORE_BREED_DB: Record<string, BreedClassification> = {
  // === 狗狗 - 小型犬 ===
  '柴犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'small_dogs', secondaryCategoryName: '小型犬', confidence: 1.0 },
  '泰迪犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'small_dogs', secondaryCategoryName: '小型犬', confidence: 1.0 },
  '贵宾犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'small_dogs', secondaryCategoryName: '小型犬', confidence: 1.0 },
  '比熊犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'small_dogs', secondaryCategoryName: '小型犬', confidence: 1.0 },
  '博美犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'small_dogs', secondaryCategoryName: '小型犬', confidence: 1.0 },
  '吉娃娃': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'small_dogs', secondaryCategoryName: '小型犬', confidence: 1.0 },
  '法国斗牛犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'small_dogs', secondaryCategoryName: '小型犬', confidence: 1.0 },
  '巴哥犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'small_dogs', secondaryCategoryName: '小型犬', confidence: 1.0 },
  
  // === 狗狗 - 中型犬 ===
  '柯基犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'medium_dogs', secondaryCategoryName: '中型犬', confidence: 1.0 },
  '边境牧羊犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'medium_dogs', secondaryCategoryName: '中型犬', confidence: 1.0 },
  '可卡犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'medium_dogs', secondaryCategoryName: '中型犬', confidence: 1.0 },
  '英国斗牛犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'medium_dogs', secondaryCategoryName: '中型犬', confidence: 1.0 },
  
  // === 狗狗 - 大型犬 ===
  '拉布拉多犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'large_dogs', secondaryCategoryName: '大型犬', confidence: 1.0 },
  '金毛寻回犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'large_dogs', secondaryCategoryName: '大型犬', confidence: 1.0 },
  '哈士奇': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'large_dogs', secondaryCategoryName: '大型犬', confidence: 1.0 },
  '萨摩耶': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'large_dogs', secondaryCategoryName: '大型犬', confidence: 1.0 },
  '阿拉斯加犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'large_dogs', secondaryCategoryName: '大型犬', confidence: 1.0 },
  
  // === 狗狗 - 工作犬 ===
  '德国牧羊犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'working_dogs', secondaryCategoryName: '工作犬', confidence: 1.0 },
  '罗威纳犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'working_dogs', secondaryCategoryName: '工作犬', confidence: 1.0 },
  '杜宾犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'working_dogs', secondaryCategoryName: '工作犬', confidence: 1.0 },
  
  // === 狗狗 - 田园犬 ===
  '中华田园犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'domestic_dogs', secondaryCategoryName: '田园犬', confidence: 1.0 },
  '大黄狗': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'domestic_dogs', secondaryCategoryName: '田园犬', confidence: 1.0 },
  '大白狗': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'domestic_dogs', secondaryCategoryName: '田园犬', confidence: 1.0 },
  '大黑狗': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'domestic_dogs', secondaryCategoryName: '田园犬', confidence: 1.0 },
  '花狗': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'domestic_dogs', secondaryCategoryName: '田园犬', confidence: 1.0 },
  '草狗': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'domestic_dogs', secondaryCategoryName: '田园犬', confidence: 1.0 },
  '唐狗': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'domestic_dogs', secondaryCategoryName: '田园犬', confidence: 1.0 },
  '柴狗': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'domestic_dogs', secondaryCategoryName: '田园犬', confidence: 1.0 },
  '大笨狗': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'domestic_dogs', secondaryCategoryName: '田园犬', confidence: 1.0 },
  '太行犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'domestic_dogs', secondaryCategoryName: '田园犬', confidence: 1.0 },

  // === 狗狗 - 串串犬 ===
  '串串犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'mixed_dogs', secondaryCategoryName: '串串犬', confidence: 1.0 },
  '混血犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', secondaryCategory: 'mixed_dogs', secondaryCategoryName: '串串犬', confidence: 1.0 },
  
  // === 猫咪 - 短毛猫 ===
  '英国短毛猫': { primaryCategory: 'cats', primaryCategoryName: '猫咪', secondaryCategory: 'short_hair_cats', secondaryCategoryName: '短毛猫', confidence: 1.0 },
  '美国短毛猫': { primaryCategory: 'cats', primaryCategoryName: '猫咪', secondaryCategory: 'short_hair_cats', secondaryCategoryName: '短毛猫', confidence: 1.0 },
  '俄罗斯蓝猫': { primaryCategory: 'cats', primaryCategoryName: '猫咪', secondaryCategory: 'short_hair_cats', secondaryCategoryName: '短毛猫', confidence: 1.0 },
  '暹罗猫': { primaryCategory: 'cats', primaryCategoryName: '猫咪', secondaryCategory: 'short_hair_cats', secondaryCategoryName: '短毛猫', confidence: 1.0 },
  '银渐层': { primaryCategory: 'cats', primaryCategoryName: '猫咪', secondaryCategory: 'short_hair_cats', secondaryCategoryName: '短毛猫', confidence: 1.0 },
  '金渐层': { primaryCategory: 'cats', primaryCategoryName: '猫咪', secondaryCategory: 'short_hair_cats', secondaryCategoryName: '短毛猫', confidence: 1.0 },
  
  // === 猫咪 - 长毛猫 ===
  '波斯猫': { primaryCategory: 'cats', primaryCategoryName: '猫咪', secondaryCategory: 'long_hair_cats', secondaryCategoryName: '长毛猫', confidence: 1.0 },
  '布偶猫': { primaryCategory: 'cats', primaryCategoryName: '猫咪', secondaryCategory: 'long_hair_cats', secondaryCategoryName: '长毛猫', confidence: 1.0 },
  '缅因猫': { primaryCategory: 'cats', primaryCategoryName: '猫咪', secondaryCategory: 'long_hair_cats', secondaryCategoryName: '长毛猫', confidence: 1.0 },
  '挪威森林猫': { primaryCategory: 'cats', primaryCategoryName: '猫咪', secondaryCategory: 'long_hair_cats', secondaryCategoryName: '长毛猫', confidence: 1.0 },
  
  // === 猫咪 - 田园猫 ===
  '狸花猫': { primaryCategory: 'cats', primaryCategoryName: '猫咪', secondaryCategory: 'domestic_cats', secondaryCategoryName: '田园猫', confidence: 1.0 },
  '橘猫': { primaryCategory: 'cats', primaryCategoryName: '猫咪', secondaryCategory: 'domestic_cats', secondaryCategoryName: '田园猫', confidence: 1.0 },
  '三花猫': { primaryCategory: 'cats', primaryCategoryName: '猫咪', secondaryCategory: 'domestic_cats', secondaryCategoryName: '田园猫', confidence: 1.0 },
  '奶牛猫': { primaryCategory: 'cats', primaryCategoryName: '猫咪', secondaryCategory: 'domestic_cats', secondaryCategoryName: '田园猫', confidence: 1.0 },
  '中华田园猫': { primaryCategory: 'cats', primaryCategoryName: '猫咪', secondaryCategory: 'domestic_cats', secondaryCategoryName: '田园猫', confidence: 1.0 },
  
  // === 鸟类 ===
  '虎皮鹦鹉': { primaryCategory: 'birds', primaryCategoryName: '鸟类', secondaryCategory: 'parrots', secondaryCategoryName: '鸦鹉类', confidence: 1.0 },
  '玄凤鹦鹉': { primaryCategory: 'birds', primaryCategoryName: '鸟类', secondaryCategory: 'parrots', secondaryCategoryName: '鸦鹉类', confidence: 1.0 },
  '牡丹鹦鹉': { primaryCategory: 'birds', primaryCategoryName: '鸟类', secondaryCategory: 'parrots', secondaryCategoryName: '鸦鹉类', confidence: 1.0 },
  '金丝雀': { primaryCategory: 'birds', primaryCategoryName: '鸟类', secondaryCategory: 'songbirds', secondaryCategoryName: '鸣禽类', confidence: 1.0 },
  '文鸟': { primaryCategory: 'birds', primaryCategoryName: '鸟类', secondaryCategory: 'songbirds', secondaryCategoryName: '鸣禽类', confidence: 1.0 },
  
  // === 水族 ===
  '金鱼': { primaryCategory: 'aquatic', primaryCategoryName: '水族', secondaryCategory: 'freshwater_fish', secondaryCategoryName: '淡水鱼', confidence: 1.0 },
  '锦鲤': { primaryCategory: 'aquatic', primaryCategoryName: '水族', secondaryCategory: 'freshwater_fish', secondaryCategoryName: '淡水鱼', confidence: 1.0 },
  '孔雀鱼': { primaryCategory: 'aquatic', primaryCategoryName: '水族', secondaryCategory: 'freshwater_fish', secondaryCategoryName: '淡水鱼', confidence: 1.0 },
  '斗鱼': { primaryCategory: 'aquatic', primaryCategoryName: '水族', secondaryCategory: 'freshwater_fish', secondaryCategoryName: '淡水鱼', confidence: 1.0 },
  
  // === 小宠 ===
  '仓鼠': { primaryCategory: 'small_pets', primaryCategoryName: '小宠', secondaryCategory: 'hamsters', secondaryCategoryName: '仓鼠类', confidence: 1.0 },
  '龙猫': { primaryCategory: 'small_pets', primaryCategoryName: '小宠', secondaryCategory: 'small_mammals', secondaryCategoryName: '小型哺乳', confidence: 1.0 },
  '兔子': { primaryCategory: 'small_pets', primaryCategoryName: '小宠', secondaryCategory: 'rabbits', secondaryCategoryName: '兔子类', confidence: 1.0 },
  '荷兰猪': { primaryCategory: 'small_pets', primaryCategoryName: '小宠', secondaryCategory: 'rodent_pets', secondaryCategoryName: '鼠类宠物', confidence: 1.0 },
  
  // === 爬宠 ===
  '巴西龟': { primaryCategory: 'reptiles', primaryCategoryName: '爬宠', secondaryCategory: 'turtles', secondaryCategoryName: '龟类', confidence: 1.0 },
  '草龟': { primaryCategory: 'reptiles', primaryCategoryName: '爬宠', secondaryCategory: 'turtles', secondaryCategoryName: '龟类', confidence: 1.0 },
  '蜥蜴': { primaryCategory: 'reptiles', primaryCategoryName: '爬宠', secondaryCategory: 'lizards', secondaryCategoryName: '蜥蜴类', confidence: 1.0 },
  '守宫': { primaryCategory: 'reptiles', primaryCategoryName: '爬宠', secondaryCategory: 'lizards', secondaryCategoryName: '蜥蜴类', confidence: 1.0 }
};

// 品种别名映射
const BREED_ALIASES: Record<string, string> = {
  // 狗狗别名
  '拉拉': '拉布拉多犬',
  '拉布拉多': '拉布拉多犬',
  '金毛': '金毛寻回犬',
  '泰迪': '泰迪犬',
  '贵宾': '贵宾犬',
  '比熊': '比熊犬',
  '博美': '博美犬',
  '二哈': '哈士奇',
  '萨摩': '萨摩耶',
  '边牧': '边境牧羊犬',
  '德牧': '德国牧羊犬',
  '法斗': '法国斗牛犬',
  '英斗': '英国斗牛犬',

  // 田园犬别名
  '田园犬': '中华田园犬',
  '土狗': '中华田园犬',
  '本地狗': '中华田园犬',
  '黄狗': '大黄狗',
  '白狗': '大白狗',
  '黑狗': '大黑狗',
  '花花狗': '花狗',
  '土狗子': '中华田园犬',
  
  // 猫咪别名
  '英短': '英国短毛猫',
  '美短': '美国短毛猫',
  '布偶': '布偶猫',
  '缅因': '缅因猫',
  '俄蓝': '俄罗斯蓝猫',
  '狸花': '狸花猫',
  '三花': '三花猫',
  '奶牛': '奶牛猫',
  
  // 其他别名
  '虎皮': '虎皮鹦鹉',
  '玄凤': '玄凤鹦鹉',
  '牡丹': '牡丹鹦鹉',
  '天竺鼠': '荷兰猪'
};

// 大类匹配映射
const CATEGORY_MAPPING: Record<string, BreedClassification> = {
  '狗狗': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', confidence: 0.8, isGeneralCategory: true },
  '狗': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', confidence: 0.8, isGeneralCategory: true },
  '犬': { primaryCategory: 'dogs', primaryCategoryName: '狗狗', confidence: 0.8, isGeneralCategory: true },
  '猫咪': { primaryCategory: 'cats', primaryCategoryName: '猫咪', confidence: 0.8, isGeneralCategory: true },
  '猫': { primaryCategory: 'cats', primaryCategoryName: '猫咪', confidence: 0.8, isGeneralCategory: true },
  '鸟类': { primaryCategory: 'birds', primaryCategoryName: '鸟类', confidence: 0.8, isGeneralCategory: true },
  '鸟': { primaryCategory: 'birds', primaryCategoryName: '鸟类', confidence: 0.8, isGeneralCategory: true },
  '水族': { primaryCategory: 'aquatic', primaryCategoryName: '水族', confidence: 0.8, isGeneralCategory: true },
  '鱼': { primaryCategory: 'aquatic', primaryCategoryName: '水族', confidence: 0.8, isGeneralCategory: true },
  '小宠': { primaryCategory: 'small_pets', primaryCategoryName: '小宠', confidence: 0.8, isGeneralCategory: true },
  '爬宠': { primaryCategory: 'reptiles', primaryCategoryName: '爬宠', confidence: 0.8, isGeneralCategory: true },
  '两栖': { primaryCategory: 'amphibians', primaryCategoryName: '两栖', confidence: 0.8, isGeneralCategory: true },
  '昆虫': { primaryCategory: 'insects', primaryCategoryName: '昆虫', confidence: 0.8, isGeneralCategory: true }
};

/**
 * 智能品种归类函数
 * @param breedInput 用户输入的品种名称
 * @returns 归类结果
 */
export function classifyBreed(breedInput: string): BreedClassification | null {
  if (!breedInput || !breedInput.trim()) {
    return null;
  }

  const input = breedInput.trim();
  
  // 1. 直接匹配品种数据库
  if (CORE_BREED_DB[input]) {
    return CORE_BREED_DB[input];
  }
  
  // 2. 别名匹配
  const standardName = BREED_ALIASES[input];
  if (standardName && CORE_BREED_DB[standardName]) {
    return CORE_BREED_DB[standardName];
  }
  
  // 3. 大类匹配
  if (CATEGORY_MAPPING[input]) {
    return CATEGORY_MAPPING[input];
  }
  
  // 4. 模糊匹配（包含关系）
  for (const [breedName, classification] of Object.entries(CORE_BREED_DB)) {
    if (breedName.includes(input) || input.includes(breedName)) {
      return { ...classification, confidence: 0.9 };
    }
  }
  
  // 5. 别名模糊匹配
  for (const [alias, standardName] of Object.entries(BREED_ALIASES)) {
    if (alias.includes(input) || input.includes(alias)) {
      return { ...CORE_BREED_DB[standardName], confidence: 0.9 };
    }
  }
  
  // 6. 大类模糊匹配
  for (const [category, classification] of Object.entries(CATEGORY_MAPPING)) {
    if (category.includes(input) || input.includes(category)) {
      return { ...classification, confidence: 0.7 };
    }
  }
  
  // 7. 无法识别，归类到"其他"
  return {
    primaryCategory: 'others',
    primaryCategoryName: '其他',
    confidence: 0.5
  };
}

/**
 * 获取用户引导提示
 * @param classification 归类结果
 * @returns 引导提示文本
 */
export function getGuidanceText(classification: BreedClassification | null): string {
  if (!classification) {
    return '请输入您宠物的品种，如：拉布拉多、英短蓝猫、虎皮鹦鹉等';
  }
  
  if (classification.isGeneralCategory) {
    return '输入具体品种可以让其他用户更精准地找到您的宝贝';
  }
  
  if (classification.confidence < 0.8) {
    return '如果分类不准确，您可以修改品种名称';
  }
  
  return '分类识别成功！';
}
