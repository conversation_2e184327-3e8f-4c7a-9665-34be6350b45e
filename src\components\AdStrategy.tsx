'use client';

import { useState, useEffect } from 'react';

interface AdFrequencyConfig {
  maxDailyViews: number;        // 每日最大展示次数
  minInterval: number;          // 最小展示间隔（分钟）
  respectUserChoice: boolean;   // 是否尊重用户选择
  adaptiveFrequency: boolean;   // 是否自适应频率
}

interface UserAdPreference {
  userId?: string;
  hiddenPositions: string[];    // 用户隐藏的广告位
  clickHistory: string[];       // 点击历史
  lastInteraction: number;      // 最后交互时间
  adTolerance: 'low' | 'medium' | 'high'; // 广告容忍度
}

class AdStrategyManager {
  private static instance: AdStrategyManager;
  private userPreference: UserAdPreference;
  private frequencyConfig: AdFrequencyConfig;

  constructor() {
    this.userPreference = this.loadUserPreference();
    this.frequencyConfig = {
      maxDailyViews: 10,
      minInterval: 30,
      respectUserChoice: true,
      adaptiveFrequency: true
    };
  }

  static getInstance(): AdStrategyManager {
    if (!AdStrategyManager.instance) {
      AdStrategyManager.instance = new AdStrategyManager();
    }
    return AdStrategyManager.instance;
  }

  // 加载用户偏好
  private loadUserPreference(): UserAdPreference {
    const stored = localStorage.getItem('user_ad_preference');
    if (stored) {
      return JSON.parse(stored);
    }
    
    return {
      hiddenPositions: [],
      clickHistory: [],
      lastInteraction: 0,
      adTolerance: 'medium'
    };
  }

  // 保存用户偏好
  private saveUserPreference(): void {
    localStorage.setItem('user_ad_preference', JSON.stringify(this.userPreference));
  }

  // 检查是否应该显示广告
  shouldShowAd(positionId: string): boolean {
    // 1. 检查用户是否隐藏了此广告位
    if (this.userPreference.hiddenPositions.includes(positionId)) {
      return false;
    }

    // 2. 检查频率限制
    if (!this.checkFrequencyLimit(positionId)) {
      return false;
    }

    // 3. 检查用户容忍度
    if (!this.checkUserTolerance()) {
      return false;
    }

    // 4. 检查时间间隔
    if (!this.checkTimeInterval(positionId)) {
      return false;
    }

    return true;
  }

  // 检查频率限制
  private checkFrequencyLimit(positionId: string): boolean {
    const today = new Date().toDateString();
    const viewKey = `ad_views_${positionId}_${today}`;
    const viewCount = parseInt(localStorage.getItem(viewKey) || '0');
    
    return viewCount < this.frequencyConfig.maxDailyViews;
  }

  // 检查用户容忍度
  private checkUserTolerance(): boolean {
    const recentHides = this.userPreference.hiddenPositions.length;
    const recentClicks = this.userPreference.clickHistory.length;
    
    // 根据用户行为调整容忍度
    if (recentHides > recentClicks * 3) {
      this.userPreference.adTolerance = 'low';
    } else if (recentClicks > recentHides * 2) {
      this.userPreference.adTolerance = 'high';
    }

    // 根据容忍度决定是否显示
    switch (this.userPreference.adTolerance) {
      case 'low':
        return Math.random() < 0.3; // 30% 概率显示
      case 'medium':
        return Math.random() < 0.7; // 70% 概率显示
      case 'high':
        return Math.random() < 0.9; // 90% 概率显示
      default:
        return true;
    }
  }

  // 检查时间间隔
  private checkTimeInterval(positionId: string): boolean {
    const lastShowKey = `ad_last_show_${positionId}`;
    const lastShow = parseInt(localStorage.getItem(lastShowKey) || '0');
    const now = Date.now();
    const minIntervalMs = this.frequencyConfig.minInterval * 60 * 1000;
    
    return (now - lastShow) >= minIntervalMs;
  }

  // 记录广告展示
  recordAdView(positionId: string): void {
    // 记录展示次数
    const today = new Date().toDateString();
    const viewKey = `ad_views_${positionId}_${today}`;
    const viewCount = parseInt(localStorage.getItem(viewKey) || '0');
    localStorage.setItem(viewKey, (viewCount + 1).toString());

    // 记录展示时间
    const lastShowKey = `ad_last_show_${positionId}`;
    localStorage.setItem(lastShowKey, Date.now().toString());
  }

  // 记录用户隐藏广告
  recordAdHide(positionId: string): void {
    if (!this.userPreference.hiddenPositions.includes(positionId)) {
      this.userPreference.hiddenPositions.push(positionId);
    }
    this.userPreference.lastInteraction = Date.now();
    this.saveUserPreference();
  }

  // 记录广告点击
  recordAdClick(adId: string): void {
    this.userPreference.clickHistory.push(adId);
    // 只保留最近50次点击记录
    if (this.userPreference.clickHistory.length > 50) {
      this.userPreference.clickHistory = this.userPreference.clickHistory.slice(-50);
    }
    this.userPreference.lastInteraction = Date.now();
    this.saveUserPreference();
  }

  // 获取推荐的广告位置
  getRecommendedPositions(): string[] {
    const allPositions = ['home_banner', 'list_feed', 'detail_bottom'];
    
    return allPositions.filter(position => {
      // 过滤掉用户隐藏的位置
      if (this.userPreference.hiddenPositions.includes(position)) {
        return false;
      }
      
      // 根据用户容忍度推荐
      switch (this.userPreference.adTolerance) {
        case 'low':
          return position === 'detail_bottom'; // 只推荐最不干扰的位置
        case 'medium':
          return ['list_feed', 'detail_bottom'].includes(position);
        case 'high':
          return true; // 所有位置都可以
        default:
          return true;
      }
    });
  }

  // 重置用户隐藏的广告位（24小时后）
  resetHiddenPositions(): void {
    const now = Date.now();
    const resetInterval = 24 * 60 * 60 * 1000; // 24小时
    
    if (now - this.userPreference.lastInteraction > resetInterval) {
      this.userPreference.hiddenPositions = [];
      this.saveUserPreference();
    }
  }

  // 获取用户统计信息
  getUserStats(): {
    adTolerance: string;
    hiddenCount: number;
    clickCount: number;
    dailyViews: number;
  } {
    const today = new Date().toDateString();
    let dailyViews = 0;
    
    // 计算今日总展示次数
    ['home_banner', 'list_feed', 'detail_bottom'].forEach(position => {
      const viewKey = `ad_views_${position}_${today}`;
      dailyViews += parseInt(localStorage.getItem(viewKey) || '0');
    });

    return {
      adTolerance: this.userPreference.adTolerance,
      hiddenCount: this.userPreference.hiddenPositions.length,
      clickCount: this.userPreference.clickHistory.length,
      dailyViews
    };
  }
}

// React Hook for using ad strategy
export function useAdStrategy() {
  const [strategy] = useState(() => AdStrategyManager.getInstance());

  useEffect(() => {
    // 定期重置隐藏的广告位
    const interval = setInterval(() => {
      strategy.resetHiddenPositions();
    }, 60 * 60 * 1000); // 每小时检查一次

    return () => clearInterval(interval);
  }, [strategy]);

  return {
    shouldShowAd: (positionId: string) => strategy.shouldShowAd(positionId),
    recordAdView: (positionId: string) => strategy.recordAdView(positionId),
    recordAdHide: (positionId: string) => strategy.recordAdHide(positionId),
    recordAdClick: (adId: string) => strategy.recordAdClick(adId),
    getRecommendedPositions: () => strategy.getRecommendedPositions(),
    getUserStats: () => strategy.getUserStats()
  };
}

// 广告策略配置组件
export default function AdStrategyConfig() {
  const { getUserStats } = useAdStrategy();
  const [stats, setStats] = useState(getUserStats());

  useEffect(() => {
    const interval = setInterval(() => {
      setStats(getUserStats());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">广告策略状态</h3>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">{stats.dailyViews}</div>
          <div className="text-sm text-gray-600">今日展示</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">{stats.clickCount}</div>
          <div className="text-sm text-gray-600">历史点击</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-red-600">{stats.hiddenCount}</div>
          <div className="text-sm text-gray-600">隐藏广告位</div>
        </div>
        
        <div className="text-center">
          <div className={`text-2xl font-bold ${
            stats.adTolerance === 'high' ? 'text-green-600' :
            stats.adTolerance === 'medium' ? 'text-yellow-600' : 'text-red-600'
          }`}>
            {stats.adTolerance === 'high' ? '高' : 
             stats.adTolerance === 'medium' ? '中' : '低'}
          </div>
          <div className="text-sm text-gray-600">容忍度</div>
        </div>
      </div>
      
      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-600">
          系统会根据用户行为自动调整广告展示策略，确保最佳的用户体验。
        </p>
      </div>
    </div>
  );
}
