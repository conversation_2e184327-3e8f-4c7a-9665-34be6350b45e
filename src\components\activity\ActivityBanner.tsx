'use client';

import React, { useState, useEffect } from 'react';
import { Sparkles } from 'lucide-react';
import { Activity } from '@/types';
import { activityAPI } from '@/lib/cloudbase';

const ActivityBanner: React.FC = () => {
  const [activeActivities, setActiveActivities] = useState<Activity[]>([]);
  const [showBanner, setShowBanner] = useState(false);

  useEffect(() => {
    const fetchActiveActivities = async () => {
      try {
        const response = await activityAPI.getActiveActivities();
        if (response.success && response.data.length > 0) {
          setActiveActivities(response.data);
          setShowBanner(true);
        }
      } catch (error) {
        console.error('获取活动失败:', error);
      }
    };

    fetchActiveActivities();
  }, []);

  if (!showBanner || activeActivities.length === 0) {
    return null;
  }

  const handleClick = () => {
    window.location.href = '/activities';
  };

  return (
    <div className="hidden md:flex items-center">
      <button
        onClick={handleClick}
        className="flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-sm hover:shadow-md"
      >
        <Sparkles className="h-4 w-4" />
        <span className="max-w-32 truncate">
          {activeActivities.length > 1
            ? `${activeActivities.length}个活动`
            : activeActivities[0]?.title
          }
        </span>
      </button>
    </div>
  );
};

export default ActivityBanner;
