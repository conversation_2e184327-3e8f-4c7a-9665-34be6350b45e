'use client';

import React from 'react';
import { X } from 'lucide-react';
import PetCard from '@/components/home/<USER>';
import { useLongPress } from '@/hooks/useLongPress';

interface PostCardWithLongPressProps {
  post: any;
  onLongPress: () => void;
  onDelete: () => void;
  onEdit?: () => void;
  type: 'published' | 'draft' | 'history';
  isDraft?: boolean;
  onClick?: () => void;
}

const PostCardWithLongPress: React.FC<PostCardWithLongPressProps> = ({
  post,
  onLongPress,
  onDelete,
  onEdit,
  type,
  isDraft = false,
  onClick
}) => {
  const longPressHandlers = useLongPress({
    onLongPress,
    onPress: onClick,
    delay: 500
  });

  const getDeleteButtonTitle = () => {
    switch (type) {
      case 'published':
        return '下架宝贝并移至待发布';
      case 'draft':
        return '删除草稿';
      case 'history':
        return '从浏览历史中移除';
      default:
        return '删除';
    }
  };

  return (
    <div className="relative">
      <div
        {...longPressHandlers}
        className={`relative transition-all duration-200 ${
          longPressHandlers.isLongPressing 
            ? 'scale-95 opacity-80 ring-2 ring-blue-500 ring-opacity-50' 
            : ''
        } ${onClick ? 'cursor-pointer' : ''}`}
      >
        <PetCard
          post={post}
          isDraft={isDraft}
        />
        {/* 长按提示 - 仅在移动端显示 */}
        {longPressHandlers.isLongPressing && (
          <div className="absolute inset-0 bg-blue-500 bg-opacity-10 rounded-lg flex items-center justify-center md:hidden">
            <div className="bg-white bg-opacity-90 px-3 py-1 rounded-full text-sm font-medium text-blue-600">
              松开显示选项
            </div>
          </div>
        )}
      </div>
      
      {/* 删除按钮 - 桌面端显示，移动端隐藏 */}
      <button
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onDelete();
        }}
        className="absolute top-2 right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-colors shadow-lg z-10 md:flex hidden"
        title={getDeleteButtonTitle()}
      >
        <X className="w-3 h-3" />
      </button>
    </div>
  );
};

export default PostCardWithLongPress;
