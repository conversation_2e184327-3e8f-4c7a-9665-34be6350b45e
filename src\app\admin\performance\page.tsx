'use client';

import React, { useState, useEffect } from 'react';
import { Activity, Clock, Database, Image, Zap, TrendingUp } from 'lucide-react';
import { performanceMonitor } from '@/utils/performanceMonitor';
import { dataCache, imageCache } from '@/utils/cacheManager';
import Button from '@/components/ui/Button';
import CostMonitor from '@/components/admin/CostMonitor';

const PerformancePage: React.FC = () => {
  const [performanceData, setPerformanceData] = useState<any>(null);
  const [cacheStats, setCacheStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'performance' | 'cost'>('performance');

  useEffect(() => {
    loadPerformanceData();
    const interval = setInterval(loadPerformanceData, 5000); // 每5秒更新
    return () => clearInterval(interval);
  }, []);

  const loadPerformanceData = () => {
    try {
      const metrics = performanceMonitor.getMetrics();
      const report = performanceMonitor.getPerformanceReport();
      const score = performanceMonitor.getPerformanceScore();
      
      setPerformanceData({
        metrics,
        report,
        score
      });

      setCacheStats({
        dataCache: dataCache.getStats(),
        imageCache: imageCache.getStats()
      });

      setLoading(false);
    } catch (error) {
      console.error('加载性能数据失败:', error);
      setLoading(false);
    }
  };

  const exportPerformanceData = () => {
    const data = performanceMonitor.exportData();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-report-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const clearCaches = () => {
    dataCache.clear();
    imageCache.clear();
    setCacheStats({
      dataCache: dataCache.getStats(),
      imageCache: imageCache.getStats()
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Activity className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-500" />
          <p className="text-gray-600">加载性能数据中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">性能监控中心</h1>
          <p className="text-gray-600">实时监控应用性能指标、缓存状态和成本分析</p>

          {/* 标签切换 */}
          <div className="mt-4 flex gap-1 bg-gray-100 p-1 rounded-lg w-fit">
            <button
              onClick={() => setActiveTab('performance')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'performance'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              性能监控
            </button>
            <button
              onClick={() => setActiveTab('cost')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'cost'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              成本分析
            </button>
          </div>

          <div className="mt-4 flex gap-4">
            <Button onClick={loadPerformanceData} variant="outline">
              <Activity className="w-4 h-4 mr-2" />
              刷新数据
            </Button>
            <Button onClick={exportPerformanceData} variant="outline">
              <TrendingUp className="w-4 h-4 mr-2" />
              导出报告
            </Button>
            <Button onClick={clearCaches} variant="outline">
              <Database className="w-4 h-4 mr-2" />
              清理缓存
            </Button>
          </div>
        </div>

        {/* 性能监控标签内容 */}
        {activeTab === 'performance' && (
          <>
            {/* 性能评分 */}
            {performanceData?.score && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <Zap className="w-5 h-5 mr-2 text-yellow-500" />
              性能评分
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className={`text-3xl font-bold mb-2 ${
                  performanceData.score.overall >= 80 ? 'text-green-500' :
                  performanceData.score.overall >= 60 ? 'text-yellow-500' : 'text-red-500'
                }`}>
                  {performanceData.score.overall}
                </div>
                <div className="text-sm text-gray-600">总体评分</div>
              </div>
              
              <div className="text-center">
                <div className={`text-2xl font-bold mb-2 ${
                  performanceData.score.breakdown.loading >= 80 ? 'text-green-500' :
                  performanceData.score.breakdown.loading >= 60 ? 'text-yellow-500' : 'text-red-500'
                }`}>
                  {performanceData.score.breakdown.loading}
                </div>
                <div className="text-sm text-gray-600">加载性能</div>
              </div>
              
              <div className="text-center">
                <div className={`text-2xl font-bold mb-2 ${
                  performanceData.score.breakdown.interactivity >= 80 ? 'text-green-500' :
                  performanceData.score.breakdown.interactivity >= 60 ? 'text-yellow-500' : 'text-red-500'
                }`}>
                  {performanceData.score.breakdown.interactivity}
                </div>
                <div className="text-sm text-gray-600">交互性能</div>
              </div>
              
              <div className="text-center">
                <div className={`text-2xl font-bold mb-2 ${
                  performanceData.score.breakdown.visualStability >= 80 ? 'text-green-500' :
                  performanceData.score.breakdown.visualStability >= 60 ? 'text-yellow-500' : 'text-red-500'
                }`}>
                  {performanceData.score.breakdown.visualStability}
                </div>
                <div className="text-sm text-gray-600">视觉稳定性</div>
              </div>
            </div>
          </div>
        )}

        {/* Core Web Vitals */}
        {performanceData?.report?.coreWebVitals && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <Clock className="w-5 h-5 mr-2 text-blue-500" />
              Core Web Vitals
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 mb-2">
                  {performanceData.report.coreWebVitals.lcp}ms
                </div>
                <div className="text-sm text-gray-600 mb-1">LCP (最大内容绘制)</div>
                <div className={`text-xs ${
                  performanceData.report.coreWebVitals.lcp <= 2500 ? 'text-green-500' :
                  performanceData.report.coreWebVitals.lcp <= 4000 ? 'text-yellow-500' : 'text-red-500'
                }`}>
                  {performanceData.report.coreWebVitals.lcp <= 2500 ? '优秀' :
                   performanceData.report.coreWebVitals.lcp <= 4000 ? '需要改进' : '较差'}
                </div>
              </div>
              
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600 mb-2">
                  {performanceData.report.coreWebVitals.fid}ms
                </div>
                <div className="text-sm text-gray-600 mb-1">FID (首次输入延迟)</div>
                <div className={`text-xs ${
                  performanceData.report.coreWebVitals.fid <= 100 ? 'text-green-500' :
                  performanceData.report.coreWebVitals.fid <= 300 ? 'text-yellow-500' : 'text-red-500'
                }`}>
                  {performanceData.report.coreWebVitals.fid <= 100 ? '优秀' :
                   performanceData.report.coreWebVitals.fid <= 300 ? '需要改进' : '较差'}
                </div>
              </div>
              
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600 mb-2">
                  {performanceData.report.coreWebVitals.cls}
                </div>
                <div className="text-sm text-gray-600 mb-1">CLS (累积布局偏移)</div>
                <div className={`text-xs ${
                  performanceData.report.coreWebVitals.cls <= 0.1 ? 'text-green-500' :
                  performanceData.report.coreWebVitals.cls <= 0.25 ? 'text-yellow-500' : 'text-red-500'
                }`}>
                  {performanceData.report.coreWebVitals.cls <= 0.1 ? '优秀' :
                   performanceData.report.coreWebVitals.cls <= 0.25 ? '需要改进' : '较差'}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 缓存统计 */}
        {cacheStats && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <Database className="w-5 h-5 mr-2 text-green-500" />
              缓存统计
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-semibold mb-3">数据缓存</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>缓存大小:</span>
                    <span className="font-medium">{cacheStats.dataCache.size}/{cacheStats.dataCache.maxSize}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>命中率:</span>
                    <span className="font-medium text-green-600">{cacheStats.dataCache.hitRate.toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>过期条目:</span>
                    <span className="font-medium text-yellow-600">{cacheStats.dataCache.expired}</span>
                  </div>
                </div>
              </div>
              
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-semibold mb-3">图片缓存</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>缓存大小:</span>
                    <span className="font-medium">{cacheStats.imageCache.size}/{cacheStats.imageCache.maxSize}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>命中率:</span>
                    <span className="font-medium text-green-600">{cacheStats.imageCache.hitRate.toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>过期条目:</span>
                    <span className="font-medium text-yellow-600">{cacheStats.imageCache.expired}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* API性能 */}
        {performanceData?.report?.apiPerformance && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <Activity className="w-5 h-5 mr-2 text-orange-500" />
              API性能统计
            </h2>
            
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">API名称</th>
                    <th className="text-right py-2">调用次数</th>
                    <th className="text-right py-2">平均响应时间</th>
                    <th className="text-right py-2">最大响应时间</th>
                    <th className="text-right py-2">最小响应时间</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(performanceData.report.apiPerformance).map(([apiName, stats]: [string, any]) => (
                    <tr key={apiName} className="border-b">
                      <td className="py-2 font-medium">{apiName}</td>
                      <td className="text-right py-2">{stats.callCount}</td>
                      <td className="text-right py-2">
                        <span className={`${
                          stats.avgResponseTime <= 500 ? 'text-green-600' :
                          stats.avgResponseTime <= 1000 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {stats.avgResponseTime}ms
                        </span>
                      </td>
                      <td className="text-right py-2">{stats.maxResponseTime}ms</td>
                      <td className="text-right py-2">{stats.minResponseTime}ms</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
          </>
        )}

        {/* 成本监控标签内容 */}
        {activeTab === 'cost' && (
          <CostMonitor />
        )}
      </div>
    </div>
  );
};

export default PerformancePage;
