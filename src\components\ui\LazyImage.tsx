'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useInView } from 'react-intersection-observer';
import { cn } from '@/utils';

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  placeholder?: string;
  fallback?: string;
  onLoad?: () => void;
  onError?: () => void;
  priority?: boolean; // 是否优先加载
  quality?: number; // 图片质量 (1-100)
  sizes?: string; // 响应式图片尺寸
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  className,
  placeholder,
  fallback = '/images/placeholder.jpg',
  onLoad,
  onError,
  priority = false,
  quality = 75,
  sizes
}) => {
  const [imageState, setImageState] = useState<'loading' | 'loaded' | 'error'>('loading');
  const [imageSrc, setImageSrc] = useState<string>(placeholder || '');
  const imgRef = useRef<HTMLImageElement>(null);

  // 使用 Intersection Observer 检测图片是否进入视口（优化版）
  const { ref, inView } = useInView({
    threshold: 0,
    rootMargin: '200px', // 提前200px开始加载，减少用户等待
    triggerOnce: true, // 只触发一次
    skip: priority // 如果是优先加载，跳过懒加载
  });

  // 优化图片URL（增强版）
  const optimizeImageUrl = (url: string, quality: number = 75) => {
    if (!url) return url;

    // 如果是腾讯云COS图片，添加图片处理参数
    if (url.includes('.myqcloud.com') || url.includes('.cos.')) {
      const separator = url.includes('?') ? '&' : '?';
      return `${url}${separator}imageMogr2/quality/${quality}/format/webp`;
    }

    // 如果是腾讯云开发静态托管，添加优化参数
    if (url.includes('.tcloudbaseapp.com')) {
      // 静态托管暂不支持实时处理，返回原URL
      return url;
    }

    // 如果是其他云存储，可以添加相应的优化参数
    return url;
  };

  // 生成响应式图片URL
  const generateResponsiveUrls = (baseUrl: string) => {
    if (!baseUrl) return { src: baseUrl };

    const optimizedUrl = optimizeImageUrl(baseUrl, quality);

    // 如果支持多尺寸，可以生成不同尺寸的URL
    return {
      src: optimizedUrl,
      srcSet: sizes ? generateSrcSet(optimizedUrl, sizes) : undefined
    };
  };

  // 生成srcSet
  const generateSrcSet = (baseUrl: string, sizesStr: string): string => {
    // 简化的srcSet生成，实际项目中可以根据需要扩展
    const sizes = [400, 800, 1200];
    return sizes.map(size => `${baseUrl} ${size}w`).join(', ');
  };

  // 预加载图片
  const preloadImage = (url: string) => {
    return new Promise<void>((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        setImageState('loaded');
        setImageSrc(url);
        onLoad?.();
        resolve();
      };
      
      img.onerror = () => {
        setImageState('error');
        setImageSrc(fallback);
        onError?.();
        reject(new Error('Image load failed'));
      };
      
      img.src = url;
    });
  };

  // 当图片进入视口或优先加载时，开始加载图片（优化版）
  useEffect(() => {
    if (priority || inView) {
      const optimizedSrc = optimizeImageUrl(src, quality);

      // 记录加载开始时间
      const loadStartTime = performance.now();

      preloadImage(optimizedSrc)
        .then(() => {
          const loadTime = performance.now() - loadStartTime;
          console.log(`图片加载完成: ${src}, 耗时: ${loadTime.toFixed(2)}ms`);
        })
        .catch(() => {
          // 如果优化后的图片加载失败，尝试加载原图
          if (optimizedSrc !== src) {
            console.warn('优化图片加载失败，尝试原图:', src);
            preloadImage(src).catch(() => {
              // 原图也失败，使用fallback
              console.error('原图加载也失败:', src);
              setImageState('error');
              setImageSrc(fallback);
            });
          } else {
            setImageState('error');
            setImageSrc(fallback);
          }
        });
    }
  }, [inView, priority, src, quality, fallback, onLoad, onError]);

  // 智能预加载：鼠标悬停时预加载（仅在未加载时）
  const handleMouseEnter = useCallback(() => {
    if (!priority && !inView && imageState === 'loading') {
      const optimizedSrc = optimizeImageUrl(src, quality);
      console.log('鼠标悬停预加载:', src);
      preloadImage(optimizedSrc).catch(() => {
        // 静默失败，不影响用户体验
      });
    }
  }, [priority, inView, imageState, src, quality]);

  // 渲染占位符
  const renderPlaceholder = () => (
    <div 
      className={cn(
        'bg-gray-200 animate-pulse flex items-center justify-center',
        className
      )}
    >
      <svg
        className="w-8 h-8 text-gray-400"
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path
          fillRule="evenodd"
          d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
          clipRule="evenodd"
        />
      </svg>
    </div>
  );

  // 渲染错误状态
  const renderError = () => (
    <div 
      className={cn(
        'bg-gray-100 flex items-center justify-center text-gray-400',
        className
      )}
    >
      <svg
        className="w-8 h-8"
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path
          fillRule="evenodd"
          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
          clipRule="evenodd"
        />
      </svg>
    </div>
  );

  return (
    <div
      ref={ref}
      className="relative overflow-hidden"
      onMouseEnter={handleMouseEnter}
    >
      {imageState === 'loading' && !imageSrc && renderPlaceholder()}

      {imageState === 'error' && renderError()}

      {(imageState === 'loaded' || imageSrc) && (
        <img
          ref={imgRef}
          src={imageSrc}
          alt={alt}
          className={cn(
            'transition-opacity duration-300',
            imageState === 'loaded' ? 'opacity-100' : 'opacity-0',
            className
          )}
          sizes={sizes}
          loading={priority ? 'eager' : 'lazy'}
          decoding="async"
        />
      )}

      {/* 加载状态覆盖层 */}
      {imageState === 'loading' && imageSrc && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse" />
      )}
    </div>
  );
};

export default LazyImage;
