(()=>{var e={};e.id=3,e.ids=[3],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},54257:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c}),s(44753),s(9457),s(16953),s(35866);var t=s(23191),a=s(88716),l=s(37922),i=s.n(l),n=s(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(r,o);let c=["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,44753)),"D:\\web-cloudbase-project\\src\\app\\admin\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,9457)),"D:\\web-cloudbase-project\\src\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\web-cloudbase-project\\src\\app\\admin\\page.tsx"],u="/admin/page",m={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},64271:(e,r,s)=>{Promise.resolve().then(s.bind(s,5264))},73230:(e,r,s)=>{Promise.resolve().then(s.bind(s,14189))},9015:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(76557).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},58038:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},5264:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>l});var t=s(10326);s(17577);var a=s(20603);function l({children:e}){return(0,t.jsxs)("div",{className:"admin-layout",children:[t.jsx(a.ToastProvider,{}),e]})}s(23824)},14189:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>m});var t=s(10326),a=s(17577),l=s(35047),i=s(99837),n=s(20603),o=s(58038),c=s(79635),d=s(9015),u=s(41828);function m(){let e=(0,l.useRouter)(),[r,s]=(0,a.useState)({username:"",password:""}),[m,x]=(0,a.useState)(!1),[p,g]=(0,a.useState)(!0);if(p)return t.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx(o.Z,{className:"w-16 h-16 text-blue-600 mx-auto mb-4 animate-pulse"}),t.jsx("p",{className:"text-gray-600",children:"检查登录状态..."})]})});let b=async s=>{s.preventDefault(),x(!0);try{let s=await u.petAPI.adminLogin({username:r.username,password:r.password});s.success?(localStorage.setItem("adminToken",s.data.token),localStorage.setItem("adminUser",JSON.stringify(s.data)),n.C.success("登录成功"),e.push("/admin/dashboard")):n.C.error(s.message||"登录失败")}catch(e){console.error("登录失败:",e),n.C.error(e.message||"登录失败，请重试")}finally{x(!1)}};return t.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"max-w-md w-full",children:[(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[t.jsx("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4",children:t.jsx(o.Z,{className:"w-8 h-8 text-blue-600"})}),t.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"管理员登录"}),t.jsx("p",{className:"text-gray-600",children:"请输入管理员账号和密码"})]}),(0,t.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"用户名"}),(0,t.jsxs)("div",{className:"relative",children:[t.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:t.jsx(c.Z,{className:"h-5 w-5 text-gray-400"})}),t.jsx("input",{type:"text",value:r.username,onChange:e=>s(r=>({...r,username:e.target.value})),className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"请输入用户名",required:!0})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"密码"}),(0,t.jsxs)("div",{className:"relative",children:[t.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:t.jsx(d.Z,{className:"h-5 w-5 text-gray-400"})}),t.jsx("input",{type:"password",value:r.password,onChange:e=>s(r=>({...r,password:e.target.value})),className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"请输入密码",required:!0})]})]}),t.jsx(i.Z,{type:"submit",loading:m,disabled:m,className:"w-full",size:"lg",children:m?"登录中...":"登录"})]}),(0,t.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[t.jsx("h3",{className:"text-sm font-medium text-blue-800 mb-2",children:"超级管理员"}),(0,t.jsxs)("div",{className:"text-sm text-blue-700",children:[t.jsx("p",{children:"只有超级管理员可以登录此系统"}),t.jsx("p",{children:"普通管理员账号由超级管理员创建"})]})]})]}),t.jsx("div",{className:"text-center mt-6",children:t.jsx("button",{onClick:()=>e.push("/"),className:"text-gray-600 hover:text-gray-900 text-sm",children:"← 返回首页"})})]})})}},99837:(e,r,s)=>{"use strict";s.d(r,{Z:()=>o});var t=s(10326),a=s(17577),l=s.n(a),i=s(28295);let n=l().forwardRef(({className:e,variant:r="primary",size:s="md",loading:a=!1,icon:l,children:n,disabled:o,...c},d)=>(0,t.jsxs)("button",{className:(0,i.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[r],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[s],e),ref:d,disabled:o||a,...c,children:[a&&(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[t.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),t.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!a&&l&&t.jsx("span",{className:"mr-2",children:l}),n]}));n.displayName="Button";let o=n},9457:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\admin\layout.tsx#default`)},44753:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\admin\page.tsx#default`)},23824:()=>{}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[276,201,240],()=>s(54257));module.exports=t})();