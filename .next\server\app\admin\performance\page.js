(()=>{var e={};e.id=652,e.ids=[652,746],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},97339:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,originalPathname:()=>f,pages:()=>u,routeModule:()=>h,tree:()=>c}),r(39941),r(9457),r(16953),r(35866);var n=r(23191),i=r(88716),a=r(37922),o=r.n(a),l=r(95231),s={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>l[e]);r.d(t,s);let c=["",{children:["admin",{children:["performance",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,39941)),"D:\\web-cloudbase-project\\src\\app\\admin\\performance\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,9457)),"D:\\web-cloudbase-project\\src\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],u=["D:\\web-cloudbase-project\\src\\app\\admin\\performance\\page.tsx"],f="/admin/performance/page",d={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/admin/performance/page",pathname:"/admin/performance",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},64271:(e,t,r)=>{Promise.resolve().then(r.bind(r,5264))},15136:(e,t,r)=>{Promise.resolve().then(r.bind(r,54939))},41438:(e,t,r)=>{e.exports=r(85251).get},18260:(e,t,r)=>{e.exports=r(79344).isEqual},21729:(e,t,r)=>{e.exports=r(91058).isPlainObject},35157:(e,t,r)=>{e.exports=r(48471).last},37307:(e,t,r)=>{e.exports=r(57572).range},74098:(e,t,r)=>{e.exports=r(8178).sortBy},59802:(e,t,r)=>{e.exports=r(67480).throttle},70028:(e,t,r)=>{e.exports=r(86006).uniqBy},20169:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},98117:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){let r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){let o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}},49891:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},29378:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},74844:(e,t)=>{"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:e!=e?4:0}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.compareValues=(e,t,n)=>{if(e!==t){let i=r(e),a=r(t);if(i===a&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},59115:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},53803:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},23031:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},49578:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},32982:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(49578),i=r(18979),a=r(96356),o=r(33883);t.isIterateeCall=function(e,t,r){return!!a.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&o.eq(r[t],e)}},46806:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(82020),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(a.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e))}},99198:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},93534:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},36915:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},48471:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(49891),i=r(93534),a=r(18979);t.last=function(e){if(a.isArrayLike(e))return n.last(i.toArray(e))}},86309:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(74844),i=r(46806),a=r(86187);t.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},s=(e,t)=>null==t||null==e?t:"object"==typeof e&&"key"in e?Object.hasOwn(t,e.key)?t[e.key]:l(t,e.path):"function"==typeof e?e(t):Array.isArray(e)?l(t,e):"object"==typeof t?t[e]:t,c=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e))?e:{key:e,path:a.toPath(e)});return e.map(e=>({original:e,criteria:c.map(t=>s(t,e))})).slice().sort((e,t)=>{for(let i=0;i<c.length;i++){let a=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==a)return a}return 0}).map(e=>e.original)}},8178:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(86309),i=r(98117),a=r(32982);t.sortBy=function(e,...t){let r=t.length;return r>1&&a.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&a.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,i.flatten(t),["asc"])}},86006:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(29378),i=r(36266),a=r(68345),o=r(60784);t.uniqBy=function(e,t=i.identity){return a.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},86044:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t=0,r={}){let n;"object"!=typeof r&&(r={});let i=null,a=null,o=null,l=0,s=null,{leading:c=!1,trailing:u=!0,maxWait:f}=r,d="maxWait"in r,h=d?Math.max(Number(f)||0,t):0,p=t=>(null!==i&&(n=e.apply(a,i)),i=a=null,l=t,n),y=e=>(l=e,s=setTimeout(b,t),c&&null!==i)?p(e):n,v=e=>(s=null,u&&null!==i)?p(e):n,g=e=>{if(null===o)return!0;let r=e-o,n=d&&e-l>=h;return r>=t||r<0||n},m=e=>{let r=t-(null===o?0:e-o),n=h-(e-l);return d?Math.min(r,n):r},b=()=>{let e=Date.now();if(g(e))return v(e);s=setTimeout(b,m(e))},x=function(...e){let r=Date.now(),l=g(r);if(i=e,a=this,o=r,l){if(null===s)return y(r);if(d)return clearTimeout(s),s=setTimeout(b,t),p(r)}return null===s&&(s=setTimeout(b,t)),n};return x.cancel=()=>{null!==s&&clearTimeout(s),l=0,o=i=a=s=null},x.flush=()=>null===s?n:v(Date.now()),x}},67480:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(86044);t.throttle=function(e,t=0,r={}){let{leading:i=!0,trailing:a=!0}=r;return n.debounce(e,t,{leading:i,maxWait:t,trailing:a})}},57572:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(32982),i=r(42423);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);let a=Math.max(Math.ceil((t-e)/(r||1)),0),o=Array(a);for(let t=0;t<a;t++)o[t]=e,e+=r;return o}},36656:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(48520);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},48520:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(55319),i=r(99198);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,a,o,l)=>{let s=t?.(r,a,o,l);if(null!=s)return s;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{let t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},85251:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(20169),i=r(23031),a=r(36915),o=r(86187);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let a=t[r];if(void 0===a){if(i.isDeepKey(r))return e(t,o.toPath(r),l);return l}return a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));let e=t[r];if(void 0===e)return l;return e}default:{if(Array.isArray(r))return function(e,t,r){if(0===t.length)return r;let i=e;for(let e=0;e<t.length;e++){if(null==i||n.isUnsafeProperty(t[e]))return r;i=i[t[e]]}return void 0===i?r:i}(t,r,l);if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let e=t[r];if(void 0===e)return l;return e}}}},39874:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(23031),i=r(49578),a=r(3659),o=r(86187);t.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&e?.[t]==null?o.toPath(t):[t]).length)return!1;let l=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==l||!Object.hasOwn(l,t))&&!((Array.isArray(l)||a.isArguments(l))&&i.isIndex(t)&&t<l.length))return!1;l=l[t]}return!0}},25598:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(85251);t.property=function(e){return function(t){return n.get(t,e)}}},3659:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(53803);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},18979:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(85417);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},68345:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(18979),i=r(21375);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},15655:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(86550);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},86550:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(15655),i=r(96356),a=r(38625),o=r(33883);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return s(e,t,r,n);if(t instanceof Map)return function(e,t,r,n){if(0===t.size)return!0;if(!(e instanceof Map))return!1;for(let[i,a]of t.entries())if(!1===r(e.get(i),a,i,e,t,n))return!1;return!0}(e,t,r,n);if(t instanceof Set)return c(e,t,r,n);let i=Object.keys(t);if(null==e)return 0===i.length;if(0===i.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let o=0;o<i.length;o++){let l=i[o];if(!a.isPrimitive(e)&&!(l in e)||void 0===t[l]&&void 0!==e[l]||null===t[l]&&null!==e[l]||!r(e[l],t[l],l,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return l(e,{...t},r,n);return o.eq(e,t);default:if(!i.isObject(e))return o.eq(e,t);if("string"==typeof t)return""===t;return!0}}function s(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let i=new Set;for(let a=0;a<t.length;a++){let o=t[a],l=!1;for(let s=0;s<e.length;s++){if(i.has(s))continue;let c=e[s],u=!1;if(r(c,o,a,e,t,n)&&(u=!0),u){i.add(s),l=!0;break}}if(!l)return!1}return!0}function c(e,t,r,n){return 0===t.size||e instanceof Set&&s([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,function e(t,n,i,a,o,s){let c=r(t,n,i,a,o,s);return void 0!==c?!!c:l(t,n,e,s)},new Map)},t.isSetMatch=c},96356:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},21375:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}},91058:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){let t=e[Symbol.toStringTag];return!!(null!=t&&Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable)&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},82020:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},42638:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(15655),i=r(74979);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},98763:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(15655),i=r(36915),a=r(36656),o=r(85251),l=r(39874);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=a.cloneDeep(t),function(r){let i=o.get(r,e);return void 0===i?l.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},33883:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},60784:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(36266),i=r(25598),a=r(42638),o=r(98763);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return o.matchesProperty(e[0],e[1]);return a.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},42423:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98384);t.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},98384:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(82020);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},86187:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let l=e[n];a?"\\"===l&&n+1<r?i+=e[++n]:l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,t.push(i),i=""):i+=l:"["===l?(o=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}},36266:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},27300:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},74979:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(55319);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},55319:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(59115),i=r(53803),a=r(99198),o=r(38625),l=r(59963);function s(e,t,r,n=new Map,u){let f=u?.(e,t,r,n);if(null!=f)return f;if(o.isPrimitive(e))return e;if(n.has(e))return n.get(e);if(Array.isArray(e)){let t=Array(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=s(e[i],i,r,n,u);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[i,a]of(n.set(e,t),e))t.set(i,s(a,i,r,n,u));return t}if(e instanceof Set){let t=new Set;for(let i of(n.set(e,t),e))t.add(s(i,void 0,r,n,u));return t}if("undefined"!=typeof Buffer&&Buffer.isBuffer(e))return e.subarray();if(l.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=s(e[i],i,r,n,u);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return n.set(e,t),c(t,e,r,n,u),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return n.set(e,t),c(t,e,r,n,u),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return n.set(e,t),c(t,e,r,n,u),t}if(e instanceof Error){let t=new e.constructor;return n.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,c(t,e,r,n,u),t}if("object"==typeof e&&function(e){switch(i.getTag(e)){case a.argumentsTag:case a.arrayTag:case a.arrayBufferTag:case a.dataViewTag:case a.booleanTag:case a.dateTag:case a.float32ArrayTag:case a.float64ArrayTag:case a.int8ArrayTag:case a.int16ArrayTag:case a.int32ArrayTag:case a.mapTag:case a.numberTag:case a.objectTag:case a.regexpTag:case a.setTag:case a.stringTag:case a.symbolTag:case a.uint8ArrayTag:case a.uint8ClampedArrayTag:case a.uint16ArrayTag:case a.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return n.set(e,t),c(t,e,r,n,u),t}return e}function c(e,t,r=e,i,a){let o=[...Object.keys(t),...n.getSymbols(t)];for(let n=0;n<o.length;n++){let l=o[n],c=Object.getOwnPropertyDescriptor(e,l);(null==c||c.writable)&&(e[l]=s(t[l],l,r,i,a))}}t.cloneDeepWith=function(e,t){return s(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=s,t.copyProperties=c},79344:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(68372),i=r(27300);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},68372:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(87636),i=r(59115),a=r(53803),o=r(99198),l=r(33883);t.isEqualWith=function(e,t,r){return function e(t,r,s,c,u,f,d){let h=d(t,r,s,c,u,f);if(void 0!==h)return h;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,s,c,u){if(Object.is(r,s))return!0;let f=a.getTag(r),d=a.getTag(s);if(f===o.argumentsTag&&(f=o.objectTag),d===o.argumentsTag&&(d=o.objectTag),f!==d)return!1;switch(f){case o.stringTag:return r.toString()===s.toString();case o.numberTag:{let e=r.valueOf(),t=s.valueOf();return l.eq(e,t)}case o.booleanTag:case o.dateTag:case o.symbolTag:return Object.is(r.valueOf(),s.valueOf());case o.regexpTag:return r.source===s.source&&r.flags===s.flags;case o.functionTag:return r===s}let h=(c=c??new Map).get(r),p=c.get(s);if(null!=h&&null!=p)return h===s;c.set(r,s),c.set(s,r);try{switch(f){case o.mapTag:if(r.size!==s.size)return!1;for(let[t,n]of r.entries())if(!s.has(t)||!e(n,s.get(t),t,r,s,c,u))return!1;return!0;case o.setTag:{if(r.size!==s.size)return!1;let t=Array.from(r.values()),n=Array.from(s.values());for(let i=0;i<t.length;i++){let a=t[i],o=n.findIndex(t=>e(a,t,void 0,r,s,c,u));if(-1===o)return!1;n.splice(o,1)}return!0}case o.arrayTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:case o.bigUint64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.bigInt64ArrayTag:case o.float32ArrayTag:case o.float64ArrayTag:if("undefined"!=typeof Buffer&&Buffer.isBuffer(r)!==Buffer.isBuffer(s)||r.length!==s.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],s[t],t,r,s,c,u))return!1;return!0;case o.arrayBufferTag:if(r.byteLength!==s.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(s),c,u);case o.dataViewTag:if(r.byteLength!==s.byteLength||r.byteOffset!==s.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(s),c,u);case o.errorTag:return r.name===s.name&&r.message===s.message;case o.objectTag:{if(!(t(r.constructor,s.constructor,c,u)||n.isPlainObject(r)&&n.isPlainObject(s)))return!1;let a=[...Object.keys(r),...i.getSymbols(r)],o=[...Object.keys(s),...i.getSymbols(s)];if(a.length!==o.length)return!1;for(let t=0;t<a.length;t++){let n=a[t],i=r[n];if(!Object.hasOwn(s,n))return!1;let o=s[n];if(!e(i,o,n,r,s,c,u))return!1}return!0}default:return!1}}finally{c.delete(r),c.delete(s)}}(t,r,f,d)}(e,t,void 0,void 0,void 0,void 0,r)}},85417:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},87636:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},38625:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},59963:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},66697:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},37202:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},48998:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},88319:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},71821:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},17069:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},20745:(e,t)=>{"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,i=r?Symbol.for("react.portal"):60106,a=r?Symbol.for("react.fragment"):60107,o=r?Symbol.for("react.strict_mode"):60108,l=r?Symbol.for("react.profiler"):60114,s=r?Symbol.for("react.provider"):60109,c=r?Symbol.for("react.context"):60110,u=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,d=r?Symbol.for("react.forward_ref"):60112,h=r?Symbol.for("react.suspense"):60113,p=(r&&Symbol.for("react.suspense_list"),r?Symbol.for("react.memo"):60115),y=r?Symbol.for("react.lazy"):60116;r&&Symbol.for("react.block"),r&&Symbol.for("react.fundamental"),r&&Symbol.for("react.responder"),r&&Symbol.for("react.scope"),t.isFragment=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case u:case f:case a:case l:case o:case h:return e;default:switch(e=e&&e.$$typeof){case c:case d:case y:case p:case s:return e;default:return t}}case i:return t}}}(e)===a}},16777:(e,t,r)=>{"use strict";e.exports=r(20745)},65442:(e,t,r)=>{"use strict";var n=r(17577),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,l=n.useLayoutEffect,s=n.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,u=n[1];return l(function(){i.value=r,i.getSnapshot=t,c(i)&&u({inst:i})},[e,r,t]),o(function(){return c(i)&&u({inst:i}),e(function(){c(i)&&u({inst:i})})},[e]),s(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:u},79251:(e,t,r)=>{"use strict";var n=r(17577),i=r(94095),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,l=n.useRef,s=n.useEffect,c=n.useMemo,u=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=l(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var h=o(e,(f=c(function(){function e(e){if(!s){if(s=!0,o=e,e=n(e),void 0!==i&&d.hasValue){var t=d.value;if(i(t,e))return l=t}return l=e}if(t=l,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,l=r)}var o,l,s=!1,c=void 0===r?null:r;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,r,n,i]))[0],f[1]);return s(function(){d.hasValue=!0,d.value=h},[h]),u(h),h}},19509:(e,t,r)=>{"use strict";var n=r(17577);"function"==typeof Object.is&&Object.is,n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},94095:(e,t,r)=>{"use strict";e.exports=r(65442)},21508:(e,t,r)=>{"use strict";e.exports=r(79251)},9911:(e,t,r)=>{"use strict";r(19509)},5264:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(10326);r(17577);var i=r(20603);function a({children:e}){return(0,n.jsxs)("div",{className:"admin-layout",children:[n.jsx(i.ToastProvider,{}),e]})}r(23824)},54939:(e,t,r)=>{"use strict";let n;r.r(t),r.d(t,{default:()=>bu});var i={};r.r(i),r.d(i,{scaleBand:()=>nF,scaleDiverging:()=>function e(){var t=i6(lu()(iU));return t.copy=function(){return ll(t,e())},nL.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=an(lu()).domain([.1,1,10]);return t.copy=function(){return ll(t,e()).base(t.base())},nL.apply(t,arguments)},scaleDivergingPow:()=>lf,scaleDivergingSqrt:()=>ld,scaleDivergingSymlog:()=>function e(){var t=ao(lu());return t.copy=function(){return ll(t,e()).constant(t.constant())},nL.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e=+e)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,iB),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,iB):[0,1],i6(n)},scaleImplicit:()=>nU,scaleLinear:()=>function e(){var t=iH();return t.copy=function(){return iq(t,e())},nR.apply(t,arguments),i6(t)},scaleLog:()=>function e(){let t=an(iV()).domain([1,10]);return t.copy=()=>iq(t,e()).base(t.base()),nR.apply(t,arguments),t},scaleOrdinal:()=>nK,scalePoint:()=>nW,scalePow:()=>af,scaleQuantile:()=>function e(){var t,r=[],n=[],i=[];function a(){var e=0,t=Math.max(1,n.length);for(i=Array(t-1);++e<t;)i[e-1]=function(e,t,r=n2){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(+r(e[a+1],a+1,e)-o)*(i-a)}}(r,e/t);return o}function o(e){return null==e||isNaN(e=+e)?t:n[n3(i,e)]}return o.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:r[0],t<i.length?i[t]:r[r.length-1]]},o.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t=+t)||r.push(t);return r.sort(nJ),a()},o.range=function(e){return arguments.length?(n=Array.from(e),a()):n.slice()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.quantiles=function(){return i.slice()},o.copy=function(){return e().domain(r).range(n).unknown(t)},nR.apply(o,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,i=1,a=[.5],o=[0,1];function l(e){return null!=e&&e<=e?o[n3(a,e,0,i)]:t}function s(){var e=-1;for(a=Array(i);++e<i;)a[e]=((e+1)*n-(e-i)*r)/(i+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r=+r,n=+n,s()):[r,n]},l.range=function(e){return arguments.length?(i=(o=Array.from(e)).length-1,s()):o.slice()},l.invertExtent=function(e){var t=o.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,a[0]]:t>=i?[a[i-1],n]:[a[t-1],a[t]]},l.unknown=function(e){return arguments.length&&(t=e),l},l.thresholds=function(){return a.slice()},l.copy=function(){return e().domain([r,n]).range(o).unknown(t)},nR.apply(i6(l),arguments)},scaleRadial:()=>function e(){var t,r=iH(),n=[0,1],i=!1;function a(e){var n,a=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(a)?t:i?Math.round(a):a}return a.invert=function(e){return r.invert(ah(e))},a.domain=function(e){return arguments.length?(r.domain(e),a):r.domain()},a.range=function(e){return arguments.length?(r.range((n=Array.from(e,iB)).map(ah)),a):n.slice()},a.rangeRound=function(e){return a.range(e).round(!0)},a.round=function(e){return arguments.length?(i=!!e,a):i},a.clamp=function(e){return arguments.length?(r.clamp(e),a):r.clamp()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e(r.domain(),n).round(i).clamp(r.clamp()).unknown(t)},nR.apply(a,arguments),i6(a)},scaleSequential:()=>function e(){var t=i6(lo()(iU));return t.copy=function(){return ll(t,e())},nL.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=an(lo()).domain([1,10]);return t.copy=function(){return ll(t,e()).base(t.base())},nL.apply(t,arguments)},scaleSequentialPow:()=>ls,scaleSequentialQuantile:()=>function e(){var t=[],r=iU;function n(e){if(null!=e&&!isNaN(e=+e))return r((n3(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r=+r)||t.push(r);return t.sort(nJ),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t=+t)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n=+n)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t=+t))){if(t<=0||n<2)return ay(e);if(t>=1)return ap(e);var n,i=(n-1)*t,a=Math.floor(i),o=ap((function e(t,r,n=0,i=1/0,a){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(t.length-1,i)),!(n<=r&&r<=i))return t;for(a=void 0===a?av:function(e=nJ){if(e===nJ)return av;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(a);i>n;){if(i-n>600){let o=i-n+1,l=r-n+1,s=Math.log(o),c=.5*Math.exp(2*s/3),u=.5*Math.sqrt(s*c*(o-c)/o)*(l-o/2<0?-1:1),f=Math.max(n,Math.floor(r-l*c/o+u)),d=Math.min(i,Math.floor(r+(o-l)*c/o+u));e(t,r,f,d,a)}let o=t[r],l=n,s=i;for(ag(t,n,r),a(t[i],o)>0&&ag(t,n,i);l<s;){for(ag(t,l,s),++l,--s;0>a(t[l],o);)++l;for(;a(t[s],o)>0;)--s}0===a(t[n],o)?ag(t,n,s):ag(t,++s,i),s<=r&&(n=s+1),r<=s&&(i=s-1)}return t})(e,a).subarray(0,a+1));return o+(ay(e.subarray(a+1))-o)*(i-a)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},nL.apply(n,arguments)},scaleSequentialSqrt:()=>lc,scaleSequentialSymlog:()=>function e(){var t=ao(lo());return t.copy=function(){return ll(t,e()).constant(t.constant())},nL.apply(t,arguments)},scaleSqrt:()=>ad,scaleSymlog:()=>function e(){var t=ao(iV());return t.copy=function(){return iq(t,e()).constant(t.constant())},nR.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],i=1;function a(e){return null!=e&&e<=e?n[n3(r,e,0,i)]:t}return a.domain=function(e){return arguments.length?(i=Math.min((r=Array.from(e)).length,n.length-1),a):r.slice()},a.range=function(e){return arguments.length?(n=Array.from(e),i=Math.min(r.length,n.length-1),a):n.slice()},a.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e().domain(r).range(n).unknown(t)},nR.apply(a,arguments)},scaleTime:()=>li,scaleUtc:()=>la,tickFormat:()=>i3});var a=r(10326),o=r(17577),l=r(66697),s=r(17069),c=r(88319),u=r(76557);let f=(0,u.Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);var d=r(48998),h=r(89453),p=r(12670),y=r(99837),v=r(41135),g=r(59802),m=r.n(g),b=r(41438),x=r.n(b),w=e=>0===e?0:e>0?1:-1,O=e=>"number"==typeof e&&e!=+e,j=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,P=e=>("number"==typeof e||e instanceof Number)&&!O(e),S=e=>P(e)||"string"==typeof e,A=0,E=e=>{var t=++A;return"".concat(e||"").concat(t)},M=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!P(e)&&"string"!=typeof e)return n;if(j(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return O(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},k=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++){if(r[e[n]])return!0;r[e[n]]=!0}return!1},T=(e,t)=>P(e)&&P(t)?r=>e+r*(t-e):()=>t,_=e=>null==e,N=e=>_(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1)),C=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]};function D(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function I(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?D(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):D(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var R=(0,o.forwardRef)((e,t)=>{var{aspect:r,initialDimension:n={width:-1,height:-1},width:i="100%",height:a="100%",minWidth:l=0,minHeight:s,maxHeight:c,children:u,debounce:f=0,id:d,className:h,onResize:p,style:y={}}=e,g=(0,o.useRef)(null),b=(0,o.useRef)();b.current=p,(0,o.useImperativeHandle)(t,()=>g.current);var[x,w]=(0,o.useState)({containerWidth:n.width,containerHeight:n.height}),O=(0,o.useCallback)((e,t)=>{w(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,o.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;O(r,n),null===(t=b.current)||void 0===t||t.call(b,r,n)};f>0&&(e=m()(e,f,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=g.current.getBoundingClientRect();return O(r,n),t.observe(g.current),()=>{t.disconnect()}},[O,f]);var P=(0,o.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=x;if(e<0||t<0)return null;C(j(i)||j(a),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",i,a),C(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=j(i)?e:i,f=j(a)?t:a;return r&&r>0&&(n?f=n/r:f&&(n=f*r),c&&f>c&&(f=c)),C(n>0||f>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,f,i,a,l,s,r),o.Children.map(u,e=>(0,o.cloneElement)(e,{width:n,height:f,style:I({width:n,height:f},e.props.style)}))},[r,u,a,c,s,l,x,i]);return o.createElement("div",{id:d?"".concat(d):void 0,className:(0,v.W)("recharts-responsive-container",h),style:I(I({},y),{},{width:i,height:a,minWidth:l,minHeight:s,maxHeight:c}),ref:g},o.createElement("div",{style:{width:0,height:0,overflow:"visible"}},P))});function L(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var z="function"==typeof Symbol&&Symbol.observable||"@@observable",B=()=>Math.random().toString(36).substring(7).split("").join("."),$={INIT:`@@redux/INIT${B()}`,REPLACE:`@@redux/REPLACE${B()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${B()}`};function U(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function K(e){let t;let r=Object.keys(e),n={};for(let t=0;t<r.length;t++){let i=r[t];"function"==typeof e[i]&&(n[i]=e[i])}let i=Object.keys(n);try{!function(e){Object.keys(e).forEach(t=>{let r=e[t];if(void 0===r(void 0,{type:$.INIT}))throw Error(L(12));if(void 0===r(void 0,{type:$.PROBE_UNKNOWN_ACTION()}))throw Error(L(13))})}(n)}catch(e){t=e}return function(e={},r){if(t)throw t;let a=!1,o={};for(let t=0;t<i.length;t++){let l=i[t],s=n[l],c=e[l],u=s(c,r);if(void 0===u)throw r&&r.type,Error(L(14));o[l]=u,a=a||u!==c}return(a=a||i.length!==Object.keys(e).length)?o:e}}function F(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}function W(e){return U(e)&&"type"in e&&"string"==typeof e.type}function q(e){return({dispatch:t,getState:r})=>n=>i=>"function"==typeof i?i(t,r,e):n(i)}var V=q(),H=Symbol.for("immer-nothing"),Z=Symbol.for("immer-draftable"),Y=Symbol.for("immer-state");function G(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var X=Object.getPrototypeOf;function J(e){return!!e&&!!e[Y]}function Q(e){return!!e&&(et(e)||Array.isArray(e)||!!e[Z]||!!e.constructor?.[Z]||eo(e)||el(e))}var ee=Object.prototype.constructor.toString();function et(e){if(!e||"object"!=typeof e)return!1;let t=X(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===ee}function er(e,t){0===en(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function en(e){let t=e[Y];return t?t.type_:Array.isArray(e)?1:eo(e)?2:el(e)?3:0}function ei(e,t){return 2===en(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function ea(e,t,r){let n=en(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function eo(e){return e instanceof Map}function el(e){return e instanceof Set}function es(e){return e.copy_||e.base_}function ec(e,t){if(eo(e))return new Map(e);if(el(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=et(e);if(!0!==t&&("class_only"!==t||r)){let t=X(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[Y];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(X(e),t)}}function eu(e,t=!1){return ed(e)||J(e)||!Q(e)||(en(e)>1&&(e.set=e.add=e.clear=e.delete=ef),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>eu(t,!0))),e}function ef(){G(2)}function ed(e){return Object.isFrozen(e)}var eh={};function ep(e){let t=eh[e];return t||G(0,e),t}function ey(e,t){t&&(ep("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function ev(e){eg(e),e.drafts_.forEach(eb),e.drafts_=null}function eg(e){e===lx&&(lx=e.parent_)}function em(e){return lx={drafts_:[],parent_:lx,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function eb(e){let t=e[Y];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function ex(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[Y].modified_&&(ev(t),G(4)),Q(e)&&(e=ew(t,e),t.parent_||ej(t,e)),t.patches_&&ep("Patches").generateReplacementPatches_(r[Y].base_,e,t.patches_,t.inversePatches_)):e=ew(t,r,[]),ev(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==H?e:void 0}function ew(e,t,r){if(ed(t))return t;let n=t[Y];if(!n)return er(t,(i,a)=>eO(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return ej(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),er(i,(i,o)=>eO(e,n,t,i,o,r,a)),ej(e,t,!1),r&&e.patches_&&ep("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function eO(e,t,r,n,i,a,o){if(J(i)){let o=ew(e,i,a&&t&&3!==t.type_&&!ei(t.assigned_,n)?a.concat(n):void 0);if(ea(r,n,o),!J(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(Q(i)&&!ed(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;ew(e,i),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&ej(e,i)}}function ej(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&eu(t,r)}var eP={get(e,t){if(t===Y)return e;let r=es(e);if(!ei(r,t))return function(e,t,r){let n=eE(t,r);return n?"value"in n?n.value:n.get?.call(e.draft_):void 0}(e,r,t);let n=r[t];return e.finalized_||!Q(n)?n:n===eA(e.base_,t)?(ek(e),e.copy_[t]=eT(n,e)):n},has:(e,t)=>t in es(e),ownKeys:e=>Reflect.ownKeys(es(e)),set(e,t,r){let n=eE(es(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=eA(es(e),t),i=n?.[Y];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||ei(e.base_,t)))return!0;ek(e),eM(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==eA(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,ek(e),eM(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=es(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){G(11)},getPrototypeOf:e=>X(e.base_),setPrototypeOf(){G(12)}},eS={};function eA(e,t){let r=e[Y];return(r?es(r):e)[t]}function eE(e,t){if(!(t in e))return;let r=X(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=X(r)}}function eM(e){!e.modified_&&(e.modified_=!0,e.parent_&&eM(e.parent_))}function ek(e){e.copy_||(e.copy_=ec(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function eT(e,t){let r=eo(e)?ep("MapSet").proxyMap_(e,t):el(e)?ep("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),n={type_:r?1:0,scope_:t?t.scope_:lx,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},i=n,a=eP;r&&(i=[n],a=eS);let{revoke:o,proxy:l}=Proxy.revocable(i,a);return n.draft_=l,n.revoke_=o,l}(e,t);return(t?t.scope_:lx).drafts_.push(r),r}function e_(e){return J(e)||G(10,e),function e(t){let r;if(!Q(t)||ed(t))return t;let n=t[Y];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=ec(t,n.scope_.immer_.useStrictShallowCopy_)}else r=ec(t,!0);return er(r,(t,n)=>{ea(r,t,e(n))}),n&&(n.finalized_=!1),r}(e)}er(eP,(e,t)=>{eS[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),eS.deleteProperty=function(e,t){return eS.set.call(this,e,t,void 0)},eS.set=function(e,t,r){return eP.set.call(this,e[0],t,r,e[0])};var eN=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...i){return n.produce(e,e=>t.call(this,e,...i))}}if("function"!=typeof t&&G(6),void 0!==r&&"function"!=typeof r&&G(7),Q(e)){let i=em(this),a=eT(e,void 0),o=!0;try{n=t(a),o=!1}finally{o?ev(i):eg(i)}return ey(i,r),ex(n,i)}if(e&&"object"==typeof e)G(1,e);else{if(void 0===(n=t(e))&&(n=e),n===H&&(n=void 0),this.autoFreeze_&&eu(n,!0),r){let t=[],i=[];ep("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){Q(e)||G(8),J(e)&&(e=e_(e));let t=em(this),r=eT(e,void 0);return r[Y].isManual_=!0,eg(t),r}finishDraft(e,t){let r=e&&e[Y];r&&r.isManual_||G(9);let{scope_:n}=r;return ey(n,t),ex(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=ep("Patches").applyPatches_;return J(e)?n(e,t):this.produce(e,e=>n(e,t))}},eC=eN.produce;eN.produceWithPatches.bind(eN),eN.setAutoFreeze.bind(eN),eN.setUseStrictShallowCopy.bind(eN),eN.applyPatches.bind(eN),eN.createDraft.bind(eN),eN.finishDraft.bind(eN);var eD="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?F:F.apply(null,arguments)};"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var eI=e=>e&&"function"==typeof e.match;function eR(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(tP(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>W(t)&&t.type===e,r}function eL(e){return["type","payload","error","meta"].indexOf(e)>-1}var ez=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function eB(e){return Q(e)?eC(e,()=>{}):e}function e$(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var eU=()=>function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:i=!0}=e??{},a=new ez;return t&&("boolean"==typeof t?a.push(V):a.push(q(t.extraArgument))),a},eK=e=>t=>{setTimeout(t,e)},eF=(e={type:"raf"})=>t=>(...r)=>{let n=t(...r),i=!0,a=!1,o=!1,l=new Set,s="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:eK(10):"callback"===e.type?e.queueNotification:eK(e.timeout),c=()=>{o=!1,a&&(a=!1,l.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>i&&e());return l.add(e),()=>{t(),l.delete(e)}},dispatch(e){try{return(a=!(i=!e?.meta?.RTK_autoBatch))&&!o&&(o=!0,s(c)),n.dispatch(e)}finally{i=!0}}})},eW=e=>function(t){let{autoBatch:r=!0}=t??{},n=new ez(e);return r&&n.push(eF("object"==typeof r?r:void 0)),n};function eq(e){let t;let r={},n=[],i={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(tP(28));if(n in r)throw Error(tP(29));return r[n]=t,i},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),i),addDefaultCase:e=>(t=e,i)};return e(i),[r,n,t]}var eV=(e,t)=>eI(e)?e.match(t):e(t),eH=(e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},eZ=["name","message","stack","code"],eY=Symbol.for("rtk-slice-createasyncthunk"),eG=((n=eG||{}).reducer="reducer",n.reducerWithPrepare="reducerWithPrepare",n.asyncThunk="asyncThunk",n),eX=function({creators:e}={}){let t=e?.asyncThunk?.[eY];return function(e){let r;let{name:n,reducerPath:i=n}=e;if(!n)throw Error(tP(11));let a=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},o=Object.keys(a),l={},s={},c={},u=[],f={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(tP(12));if(r in s)throw Error(tP(13));return s[r]=t,f},addMatcher:(e,t)=>(u.push({matcher:e,reducer:t}),f),exposeAction:(e,t)=>(c[e]=t,f),exposeCaseReducer:(e,t)=>(l[e]=t,f)};function d(){let[t={},r=[],n]="function"==typeof e.extraReducers?eq(e.extraReducers):[e.extraReducers],i={...t,...s};return function(e,t){let r;let[n,i,a]=eq(t);if("function"==typeof e)r=()=>eB(e());else{let t=eB(e);r=()=>t}function o(e=r(),t){let o=[n[t.type],...i.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===o.filter(e=>!!e).length&&(o=[a]),o.reduce((e,r)=>{if(r){if(J(e)){let n=r(e,t);return void 0===n?e:n}if(Q(e))return eC(e,e=>r(e,t));{let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}}return e},e)}return o.getInitialState=r,o}(e.initialState,e=>{for(let t in i)e.addCase(t,i[t]);for(let t of u)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}o.forEach(r=>{let i=a[r],o={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:e,reducerName:t},r,n,i){if(!i)throw Error(tP(18));let{payloadCreator:a,fulfilled:o,pending:l,rejected:s,settled:c,options:u}=r,f=i(e,a,u);n.exposeAction(t,f),o&&n.addCase(f.fulfilled,o),l&&n.addCase(f.pending,l),s&&n.addCase(f.rejected,s),c&&n.addMatcher(f.settled,c),n.exposeCaseReducer(t,{fulfilled:o||eJ,pending:l||eJ,rejected:s||eJ,settled:c||eJ})}(o,i,f,t):function({type:e,reducerName:t,createNotation:r},n,i){let a,o;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(tP(17));a=n.reducer,o=n.prepare}else a=n;i.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,o?eR(e,o):eR(e))}(o,i,f)});let h=e=>e,p=new Map,y=new WeakMap;function v(e,t){return r||(r=d()),r(e,t)}function g(){return r||(r=d()),r.getInitialState()}function m(t,r=!1){function n(e){let i=e[t];return void 0===i&&r&&(i=e$(y,n,g)),i}function i(t=h){let n=e$(p,r,()=>new WeakMap);return e$(n,t,()=>{let n={};for(let[i,a]of Object.entries(e.selectors??{}))n[i]=function(e,t,r,n){function i(a,...o){let l=t(a);return void 0===l&&n&&(l=r()),e(l,...o)}return i.unwrapped=e,i}(a,t,()=>e$(y,t,g),r);return n})}return{reducerPath:t,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let b={name:n,reducer:v,actions:c,caseReducers:l,getInitialState:g,...m(i),injectInto(e,{reducerPath:t,...r}={}){let n=t??i;return e.inject({reducerPath:n,reducer:v},r),{...b,...m(n,!0)}}};return b}}();function eJ(){}var eQ="listener",e0="completed",e1="cancelled",e2=`task-${e1}`,e5=`task-${e0}`,e3=`${eQ}-${e1}`,e6=`${eQ}-${e0}`,e4=class{constructor(e){this.code=e,this.message=`task ${e1} (reason: ${e})`}name="TaskAbortError";message},e7=(e,t)=>{if("function"!=typeof e)throw TypeError(tP(32))},e8=()=>{},e9=(e,t=e8)=>(e.catch(t),e),te=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),tt=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},tr=e=>{if(e.aborted){let{reason:t}=e;throw new e4(t)}};function tn(e,t){let r=e8;return new Promise((n,i)=>{let a=()=>i(new e4(e.reason));if(e.aborted){a();return}r=te(e,a),t.finally(()=>r()).then(n,i)}).finally(()=>{r=e8})}var ti=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof e4?"cancelled":"rejected",error:e}}finally{t?.()}},ta=e=>t=>e9(tn(e,t).then(t=>(tr(e),t))),to=e=>{let t=ta(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:tl}=Object,ts={},tc="listenerMiddleware",tu=(e,t)=>{let r=t=>te(e,()=>tt(t,e.reason));return(n,i)=>{e7(n,"taskExecutor");let a=new AbortController;r(a);let o=ti(async()=>{tr(e),tr(a.signal);let t=await n({pause:ta(a.signal),delay:to(a.signal),signal:a.signal});return tr(a.signal),t},()=>tt(a,e5));return i?.autoJoin&&t.push(o.catch(e8)),{result:ta(e)(o),cancel(){tt(a,e2)}}}},tf=(e,t)=>{let r=async(r,n)=>{tr(t);let i=()=>{},a=[new Promise((t,n)=>{let a=e({predicate:r,effect:(e,r)=>{r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=()=>{a(),n()}})];null!=n&&a.push(new Promise(e=>setTimeout(e,n,null)));try{let e=await tn(t,Promise.race(a));return tr(t),e}finally{i()}};return(e,t)=>e9(r(e,t))},td=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:a}=e;if(t)i=eR(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(tP(21));return e7(a,"options.listener"),{predicate:i,type:t,effect:a}},th=tl(e=>{let{type:t,predicate:r,effect:n}=td(e);return{id:eH(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(tP(22))}}},{withTypes:()=>th}),tp=(e,t)=>{let{type:r,effect:n,predicate:i}=td(t);return Array.from(e.values()).find(e=>("string"==typeof r?e.type===r:e.predicate===i)&&e.effect===n)},ty=e=>{e.pending.forEach(e=>{tt(e,e3)})},tv=e=>()=>{e.forEach(ty),e.clear()},tg=(e,t,r)=>{try{e(t,r)}catch(e){setTimeout(()=>{throw e},0)}},tm=tl(eR(`${tc}/add`),{withTypes:()=>tm}),tb=eR(`${tc}/removeAll`),tx=tl(eR(`${tc}/remove`),{withTypes:()=>tx}),tw=(...e)=>{console.error(`${tc}/error`,...e)},tO=(e={})=>{let t=new Map,{extra:r,onError:n=tw}=e;e7(n,"onError");let i=e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),t?.cancelActive&&ty(e)}),a=e=>i(tp(t,e)??th(e));tl(a,{withTypes:()=>a});let o=e=>{let r=tp(t,e);return r&&(r.unsubscribe(),e.cancelActive&&ty(r)),!!r};tl(o,{withTypes:()=>o});let l=async(e,i,o,l)=>{let s=new AbortController,c=tf(a,s.signal),u=[];try{e.pending.add(s),await Promise.resolve(e.effect(i,tl({},o,{getOriginalState:l,condition:(e,t)=>c(e,t).then(Boolean),take:c,delay:to(s.signal),pause:ta(s.signal),extra:r,signal:s.signal,fork:tu(s.signal,u),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,r)=>{e!==s&&(tt(e,e3),r.delete(e))})},cancel:()=>{tt(s,e3),e.pending.delete(s)},throwIfCancelled:()=>{tr(s.signal)}})))}catch(e){e instanceof e4||tg(n,e,{raisedBy:"effect"})}finally{await Promise.all(u),tt(s,e6),e.pending.delete(s)}},s=tv(t);return{middleware:e=>r=>i=>{let c;if(!W(i))return r(i);if(tm.match(i))return a(i.payload);if(tb.match(i)){s();return}if(tx.match(i))return o(i.payload);let u=e.getState(),f=()=>{if(u===ts)throw Error(tP(23));return u};try{if(c=r(i),t.size>0){let r=e.getState();for(let a of Array.from(t.values())){let t=!1;try{t=a.predicate(i,r,u)}catch(e){t=!1,tg(n,e,{raisedBy:"predicate"})}t&&l(a,i,e,f)}}}finally{u=ts}return c},startListening:a,stopListening:o,clearListeners:s}},tj=Symbol.for("rtk-state-proxy-original");function tP(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}function tS(e,t){if(t){var r=Number.parseInt(t,10);if(!O(r))return null==e?void 0:e[r]}}var tA=eX({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),tE=tA.reducer,{createEventEmitter:tM}=tA.actions;r(9911);var tk=Symbol.for("react.forward_ref"),tT=Symbol.for("react.memo"),t_={notify(){},get:()=>[]},tN=!!("undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement),tC="undefined"!=typeof navigator&&"ReactNative"===navigator.product,tD=tN||tC?o.useLayoutEffect:o.useEffect,tI={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},tR={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},tL={[tk]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[tT]:tR};Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var tz=Symbol.for("react-redux-context"),tB="undefined"!=typeof globalThis?globalThis:{},t$=function(){if(!o.createContext)return{};let e=tB[tz]??=new Map,t=e.get(o.createContext);return t||(t=o.createContext(null),e.set(o.createContext,t)),t}(),tU=function(e){let{children:t,context:r,serverState:n,store:i}=e,a=o.useMemo(()=>{let e=function(e,t){let r;let n=t_,i=0,a=!1;function o(){c.onStateChange&&c.onStateChange()}function l(){if(i++,!r){let t,i;r=e.subscribe(o),t=null,i=null,n={clear(){t=null,i=null},notify(){(()=>{let e=t;for(;e;)e.callback(),e=e.next})()},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=i={callback:e,next:null,prev:i};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function s(){i--,r&&0===i&&(r(),r=void 0,n.clear(),n=t_)}let c={addNestedSub:function(e){l();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),s())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,l())},tryUnsubscribe:function(){a&&(a=!1,s())},getListeners:()=>n};return c}(i);return{store:i,subscription:e,getServerState:n?()=>n:void 0}},[i,n]),l=o.useMemo(()=>i.getState(),[i]);return tD(()=>{let{subscription:e}=a;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),l!==i.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[a,l]),o.createElement((r||t$).Provider,{value:a},t)},tK={active:!1,index:null,dataKey:void 0,coordinate:void 0},tF=eX({name:"tooltip",initialState:{itemInteraction:{click:tK,hover:tK},axisInteraction:{click:tK,hover:tK},keyboardInteraction:tK,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push(t.payload)},removeTooltipEntrySettings(e,t){var r=e_(e).tooltipItemPayloads.indexOf(t.payload);r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:tW,removeTooltipEntrySettings:tq,setTooltipSettingsState:tV,setActiveMouseOverItemIndex:tH,mouseLeaveItem:tZ,mouseLeaveChart:tY,setActiveClickItemIndex:tG,setMouseOverAxisIndex:tX,setMouseClickAxisIndex:tJ,setSyncInteraction:tQ,setKeyboardInteraction:t0}=tF.actions,t1=tF.reducer,t2=eX({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:t5,setDataStartEndIndexes:t3,setComputedData:t6}=t2.actions,t4=t2.reducer,t7=eX({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:t8,setLayout:t9,setChartSize:re,setScale:rt}=t7.actions,rr=t7.reducer,rn=e=>Array.isArray(e)?e:[e],ri=0,ra=class{revision=ri;_value;_lastValue;_isEqual=ro;constructor(e,t=ro){this._value=this._lastValue=e,this._isEqual=t}get value(){return this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++ri)}};function ro(e,t){return e===t}function rl(e){return e instanceof ra||console.warn("Not a valid cell! ",e),e.value}var rs=(e,t)=>!1;function rc(){return function(e,t=ro){return new ra(null,t)}(0,rs)}var ru=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=rc()),rl(t)};Symbol();var rf=0,rd=Object.getPrototypeOf({}),rh=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,rp);tag=rc();tags={};children={};collectionTag=null;id=rf++},rp={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in rd)return n;if("object"==typeof n&&null!==n){var i;let r=e.children[t];return void 0===r&&(r=e.children[t]=Array.isArray(i=n)?new ry(i):new rh(i)),r.tag&&rl(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=rc()).value=n),rl(r),n}})(),ownKeys:e=>(ru(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},ry=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],rv);tag=rc();tags={};children={};collectionTag=null;id=rf++},rv={get:([e],t)=>("length"===t&&ru(e),rp.get(e,t)),ownKeys:([e])=>rp.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>rp.getOwnPropertyDescriptor(e,t),has:([e],t)=>rp.has(e,t)},rg="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function rm(){return{s:0,v:void 0,o:null,p:null}}function rb(e,t={}){let r,n=rm(),{resultEqualityCheck:i}=t,a=0;function o(){let t,o=n,{length:l}=arguments;for(let e=0;e<l;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);let r=e.get(t);void 0===r?(o=rm(),e.set(t,o)):o=r}else{let e=o.p;null===e&&(o.p=e=new Map);let r=e.get(t);void 0===r?(o=rm(),e.set(t,o)):o=r}}let s=o;if(1===o.s)t=o.v;else if(t=e.apply(null,arguments),a++,i){let e=r?.deref?.()??r;null!=e&&i(e,t)&&(t=e,0!==a&&a--),r="object"==typeof t&&null!==t||"function"==typeof t?new rg(t):t}return s.s=1,s.v=t,t}return o.clearCache=()=>{n=rm(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}var rx=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,n=(...e)=>{let t,n=0,i=0,a={},o=e.pop();"object"==typeof o&&(a=o,o=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(o,`createSelector expects an output function after the inputs, but received: [${typeof o}]`);let{memoize:l,memoizeOptions:s=[],argsMemoize:c=rb,argsMemoizeOptions:u=[],devModeChecks:f={}}={...r,...a},d=rn(s),h=rn(u),p=function(e){let t=Array.isArray(e[0])?e[0]:e;return function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),y=l(function(){return n++,o.apply(null,arguments)},...d);return Object.assign(c(function(){i++;let e=function(e,t){let r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(p,arguments);return t=y.apply(null,e)},...h),{resultFunc:o,memoizedResultFunc:y,dependencies:p,dependencyRecomputations:()=>i,resetDependencyRecomputations:()=>{i=0},lastResult:()=>t,recomputations:()=>n,resetRecomputations:()=>{n=0},memoize:l,argsMemoize:c})};return Object.assign(n,{withTypes:()=>n}),n}(rb),rw=Object.assign((e,t=rx)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>rw}),rO=r(21508),rj=(0,o.createContext)(null),rP=e=>e,rS=()=>{var e=(0,o.useContext)(rj);return e?e.store.dispatch:rP},rA=()=>{},rE=()=>rA,rM=(e,t)=>e===t;function rk(e){var t=(0,o.useContext)(rj);return(0,rO.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:rE,t?t.store.getState:rA,t?t.store.getState:rA,t?e:rA,rM)}var rT=r(74098),r_=r.n(rT),rN=e=>e.legend.settings;function rC(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],l=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function rD(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function rI(e){return function(){return e}}function rR(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function rL(e,t){return e[t]}function rz(e){let t=[];return t.key=e,t}function rB(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function r$(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rB(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rB(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}rx([e=>e.legend.payload,rN],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?r_()(n,r):n}),Array.prototype.slice;var rU=Math.PI/180,rK=e=>180*e/Math.PI,rF=(e,t,r,n)=>({x:e+Math.cos(-rU*n)*r,y:t+Math.sin(-rU*n)*r}),rW=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},rq=(e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)},rV=(e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,o=rq({x:r,y:n},{x:i,y:a});if(o<=0)return{radius:o,angle:0};var l=Math.acos((r-i)/o);return n>a&&(l=2*Math.PI-l),{radius:o,angle:rK(l),angleInRadian:l}},rH=e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},rZ=(e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))},rY=(e,t)=>{var r,{x:n,y:i}=e,{radius:a,angle:o}=rV({x:n,y:i},t),{innerRadius:l,outerRadius:s}=t;if(a<l||a>s||0===a)return null;var{startAngle:c,endAngle:u}=rH(t),f=o;if(c<=u){for(;f>u;)f-=360;for(;f<c;)f+=360;r=f>=c&&f<=u}else{for(;f>c;)f-=360;for(;f<u;)f+=360;r=f>=u&&f<=c}return r?r$(r$({},t),{},{radius:a,angle:rZ(f,t)}):null};function rG(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rG(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rG(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function rJ(e,t,r){return _(e)||_(t)?r:S(t)?x()(e,t,r):"function"==typeof t?t(e):r}var rQ=(e,t,r,n,i)=>{var a,o=-1,l=null!==(a=null==t?void 0:t.length)&&void 0!==a?a:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var s=0;s<l;s++){var c=s>0?r[s-1].coordinate:r[l-1].coordinate,u=r[s].coordinate,f=s>=l-1?r[0].coordinate:r[s+1].coordinate,d=void 0;if(w(u-c)!==w(f-u)){var h=[];if(w(f-u)===w(i[1]-i[0])){d=f;var p=u+i[1]-i[0];h[0]=Math.min(p,(p+c)/2),h[1]=Math.max(p,(p+c)/2)}else{d=c;var y=f+i[1]-i[0];h[0]=Math.min(u,(y+u)/2),h[1]=Math.max(u,(y+u)/2)}var v=[Math.min(u,(d+u)/2),Math.max(u,(d+u)/2)];if(e>v[0]&&e<=v[1]||e>=h[0]&&e<=h[1]){({index:o}=r[s]);break}}else{var g=Math.min(c,f),m=Math.max(c,f);if(e>(g+u)/2&&e<=(m+u)/2){({index:o}=r[s]);break}}}else if(t){for(var b=0;b<l;b++)if(0===b&&e<=(t[b].coordinate+t[b+1].coordinate)/2||b>0&&b<l-1&&e>(t[b].coordinate+t[b-1].coordinate)/2&&e<=(t[b].coordinate+t[b+1].coordinate)/2||b===l-1&&e>(t[b].coordinate+t[b-1].coordinate)/2){({index:o}=t[b]);break}}return o},r0=(e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&P(e[a]))return rX(rX({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&P(e[o]))return rX(rX({},e),{},{[o]:e[o]+(i||0)})}return e},r1=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,r2=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var i,a,o=e.map(e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(a=!0),e.coordinate));return i||o.push(t),a||o.push(r),o},r5=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:l,isCategorical:s,categoricalDomain:c,tickCount:u,ticks:f,niceTicks:d,axisType:h}=e;if(!o)return null;var p="scaleBand"===l&&o.bandwidth?o.bandwidth()/2:2,y=(t||r)&&"category"===i&&o.bandwidth?o.bandwidth()/p:0;return(y="angleAxis"===h&&a&&a.length>=2?2*w(a[0]-a[1])*y:y,t&&(f||d))?(f||d||[]).map((e,t)=>({coordinate:o(n?n.indexOf(e):e)+y,value:e,offset:y,index:t})).filter(e=>!O(e.coordinate)):s&&c?c.map((e,t)=>({coordinate:o(e)+y,value:e,index:t,offset:y})):o.ticks&&!r&&null!=u?o.ticks(u).map((e,t)=>({coordinate:o(e)+y,value:e,offset:y,index:t})):o.domain().map((e,t)=>({coordinate:o(e)+y,value:n?n[e]:e,index:t,offset:y}))},r3=e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-1e-4,a=Math.max(n[0],n[1])+1e-4,o=e(t[0]),l=e(t[r-1]);(o<i||o>a||l<i||l>a)&&e.domain([t[0],t[r-1]])}},r6=(e,t)=>{if(!t||2!==t.length||!P(t[0])||!P(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!P(e[0])||e[0]<r)&&(i[0]=r),(!P(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},r4={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var l=O(e[o][r][1])?e[o][r][0]:e[o][r][1];l>=0?(e[o][r][0]=i,e[o][r][1]=i+l,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+l,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}rC(e,t)}},none:rC,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,l=0;o<r;++o)l+=e[o][n][1]||0;i[n][1]+=i[n][0]=-l/2}rC(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var l=0,s=0,c=0;l<i;++l){for(var u=e[t[l]],f=u[o][1]||0,d=(f-(u[o-1][1]||0))/2,h=0;h<l;++h){var p=e[t[h]];d+=(p[o][1]||0)-(p[o-1][1]||0)}s+=f,c+=d*f}r[o-1][1]+=r[o-1][0]=a,s&&(a-=c/s)}r[o-1][1]+=r[o-1][0]=a,rC(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=O(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}},r7=(e,t,r)=>{var n=r4[r];return(function(){var e=rI([]),t=rR,r=rC,n=rL;function i(i){var a,o,l=Array.from(e.apply(this,arguments),rz),s=l.length,c=-1;for(let e of i)for(a=0,++c;a<s;++a)(l[a][c]=[0,+n(e,l[a].key,c,i)]).data=e;for(a=0,o=rD(t(l));a<s;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:rI(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:rI(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?rR:"function"==typeof e?e:rI(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?rC:e,i):r},i})().keys(t).value((e,t)=>+rJ(e,t,0)).order(rR).offset(n)(e)},r8=e=>{var{axis:t,ticks:r,offset:n,bandSize:i,entry:a,index:o}=e;if("category"===t.type)return r[o]?r[o].coordinate+n:null;var l=rJ(a,t.dataKey,t.scale.domain()[o]);return _(l)?null:t.scale(l)-i/2+n},r9=e=>{var{numericAxis:t}=e,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},ne=e=>{var t=e.flat(2).filter(P);return[Math.min(...t),Math.max(...t)]},nt=e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]],nr=(e,t,r)=>{if(null!=e)return nt(Object.keys(e).reduce((n,i)=>{var{stackedData:a}=e[i],o=a.reduce((e,n)=>{var i=ne(n.slice(t,r+1));return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))},nn=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,ni=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,na=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var i=r_()(t,e=>e.coordinate),a=1/0,o=1,l=i.length;o<l;o++){var s=i[o],c=i[o-1];a=Math.min((s.coordinate||0)-(c.coordinate||0),a)}return a===1/0?0:a}return r?void 0:0};function no(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return rX(rX({},t),{},{dataKey:r,payload:n,value:i,name:a})}function nl(e,t){return e?String(e):"string"==typeof t?t:void 0}var ns=(e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return rX(rX(rX({},n),rF(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:s}=n;return rX(rX(rX({},n),rF(n.cx,n.cy,l,s)),{},{angle:s,radius:l})}return{x:0,y:0}},nc=(e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius,nu=e=>e.layout.width,nf=e=>e.layout.height,nd=e=>e.layout.scale,nh=e=>e.layout.margin,np=rx(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),ny=rx(e=>e.cartesianAxis.yAxis,e=>Object.values(e)),nv="data-recharts-item-index",ng="data-recharts-item-data-key";function nm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nb(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nm(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nm(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var nx=rx([nu,nf,nh,e=>e.brush.height,np,ny,rN,e=>e.legend.size],(e,t,r,n,i,a,o,l)=>{var s=a.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:60;return nb(nb({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),c=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:nb(nb({},e),{},{[r]:x()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),u=nb(nb({},c),s),f=u.bottom;u.bottom+=n;var d=e-(u=r0(u,o,l)).left-u.right,h=t-u.top-u.bottom;return nb(nb({brushBottom:f},u),{},{width:Math.max(d,0),height:Math.max(h,0)})}),nw=rx(nx,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),nO=rx(nu,nf,(e,t)=>({x:0,y:0,width:e,height:t})),nj=(0,o.createContext)(null),nP=()=>null!=(0,o.useContext)(nj),nS=e=>e.brush,nA=rx([nS,nx,nh],(e,t,r)=>({height:e.height,x:P(e.x)?e.x:t.left,y:P(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:P(e.width)?e.width:t.width})),nE=()=>{var e,t=nP(),r=rk(nw),n=rk(nA),i=null===(e=rk(nS))||void 0===e?void 0:e.padding;return t&&n&&i?{width:n.width-i.left-i.right,height:n.height-i.top-i.bottom,x:i.left,y:i.top}:r},nM={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},nk=()=>{var e;return null!==(e=rk(nx))&&void 0!==e?e:nM},nT=()=>rk(nu),n_=()=>rk(nf),nN=e=>e.layout.layoutType,nC=()=>rk(nN),nD=r(37307),nI=r.n(nD);function nR(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function nL(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class nz extends Map{constructor(e,t=n$){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(nB(this,e))}has(e){return super.has(nB(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function nB({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function n$(e){return null!==e&&"object"==typeof e?e.valueOf():e}let nU=Symbol("implicit");function nK(){var e=new nz,t=[],r=[],n=nU;function i(i){let a=e.get(i);if(void 0===a){if(n!==nU)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new nz,r))e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return nK(t,r).unknown(n)},nR.apply(i,arguments),i}function nF(){var e,t,r=nK().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,l=!1,s=0,c=0,u=.5;function f(){var r=n().length,f=o<a,d=f?o:a,h=f?a:o;e=(h-d)/Math.max(1,r-s+2*c),l&&(e=Math.floor(e)),d+=(h-d-e*(r-s))*u,t=e*(1-s),l&&(d=Math.round(d),t=Math.round(t));var p=(function(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=Array(i);++n<i;)a[n]=e+n*r;return a})(r).map(function(t){return d+e*t});return i(f?p.reverse():p)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([a,o]=e,a=+a,o=+o,f()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a=+a,o=+o,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(s=Math.min(1,c=+e),f()):s},r.paddingInner=function(e){return arguments.length?(s=Math.min(1,e),f()):s},r.paddingOuter=function(e){return arguments.length?(c=+e,f()):c},r.align=function(e){return arguments.length?(u=Math.max(0,Math.min(1,e)),f()):u},r.copy=function(){return nF(n(),[a,o]).round(l).paddingInner(s).paddingOuter(c).align(u)},nR.apply(f(),arguments)}function nW(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(nF.apply(null,arguments).paddingInner(1))}let nq=Math.sqrt(50),nV=Math.sqrt(10),nH=Math.sqrt(2);function nZ(e,t,r){let n,i,a;let o=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(o)),s=o/Math.pow(10,l),c=s>=nq?10:s>=nV?5:s>=nH?2:1;return(l<0?(n=Math.round(e*(a=Math.pow(10,-l)/c)),i=Math.round(t*a),n/a<e&&++n,i/a>t&&--i,a=-a):(n=Math.round(e/(a=Math.pow(10,l)*c)),i=Math.round(t/a),n*a<e&&++n,i*a>t&&--i),i<n&&.5<=r&&r<2)?nZ(e,t,2*r):[n,i,a]}function nY(e,t,r){if(t=+t,e=+e,!((r=+r)>0))return[];if(e===t)return[e];let n=t<e,[i,a,o]=n?nZ(t,e,r):nZ(e,t,r);if(!(a>=i))return[];let l=a-i+1,s=Array(l);if(n){if(o<0)for(let e=0;e<l;++e)s[e]=-((a-e)/o);else for(let e=0;e<l;++e)s[e]=(a-e)*o}else if(o<0)for(let e=0;e<l;++e)s[e]=-((i+e)/o);else for(let e=0;e<l;++e)s[e]=(i+e)*o;return s}function nG(e,t,r){return nZ(e=+e,t=+t,r=+r)[2]}function nX(e,t,r){t=+t,e=+e,r=+r;let n=t<e,i=n?nG(t,e,r):nG(e,t,r);return(n?-1:1)*(i<0?-(1/i):i)}function nJ(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function nQ(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function n0(e){let t,r,n;function i(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>r(e[t],n)?i=t+1:a=t}while(i<a)}return i}return 2!==e.length?(t=nJ,r=(t,r)=>nJ(e(t),r),n=(t,r)=>e(t)-r):(t=e===nJ||e===nQ?e:n1,r=e,n=e),{left:i,center:function(e,t,r=0,a=e.length){let o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>=r(e[t],n)?i=t+1:a=t}while(i<a)}return i}}}function n1(){return 0}function n2(e){return null===e?NaN:+e}let n5=n0(nJ),n3=n5.right;function n6(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function n4(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function n7(){}n5.left,n0(n2).center;var n8="\\s*([+-]?\\d+)\\s*",n9="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",ie="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",it=/^#([0-9a-f]{3,8})$/,ir=RegExp(`^rgb\\(${n8},${n8},${n8}\\)$`),ii=RegExp(`^rgb\\(${ie},${ie},${ie}\\)$`),ia=RegExp(`^rgba\\(${n8},${n8},${n8},${n9}\\)$`),io=RegExp(`^rgba\\(${ie},${ie},${ie},${n9}\\)$`),il=RegExp(`^hsl\\(${n9},${ie},${ie}\\)$`),is=RegExp(`^hsla\\(${n9},${ie},${ie},${n9}\\)$`),ic={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function iu(){return this.rgb().formatHex()}function id(){return this.rgb().formatRgb()}function ih(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=it.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?ip(t):3===r?new ig(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?iy(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?iy(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=ir.exec(e))?new ig(t[1],t[2],t[3],1):(t=ii.exec(e))?new ig(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=ia.exec(e))?iy(t[1],t[2],t[3],t[4]):(t=io.exec(e))?iy(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=il.exec(e))?ij(t[1],t[2]/100,t[3]/100,1):(t=is.exec(e))?ij(t[1],t[2]/100,t[3]/100,t[4]):ic.hasOwnProperty(e)?ip(ic[e]):"transparent"===e?new ig(NaN,NaN,NaN,0):null}function ip(e){return new ig(e>>16&255,e>>8&255,255&e,1)}function iy(e,t,r,n){return n<=0&&(e=t=r=NaN),new ig(e,t,r,n)}function iv(e,t,r,n){var i;return 1==arguments.length?((i=e)instanceof n7||(i=ih(i)),i)?new ig((i=i.rgb()).r,i.g,i.b,i.opacity):new ig:new ig(e,t,r,null==n?1:n)}function ig(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function im(){return`#${iO(this.r)}${iO(this.g)}${iO(this.b)}`}function ib(){let e=ix(this.opacity);return`${1===e?"rgb(":"rgba("}${iw(this.r)}, ${iw(this.g)}, ${iw(this.b)}${1===e?")":`, ${e})`}`}function ix(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function iw(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function iO(e){return((e=iw(e))<16?"0":"")+e.toString(16)}function ij(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new iS(e,t,r,n)}function iP(e){if(e instanceof iS)return new iS(e.h,e.s,e.l,e.opacity);if(e instanceof n7||(e=ih(e)),!e)return new iS;if(e instanceof iS)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,l=a-i,s=(a+i)/2;return l?(o=t===a?(r-n)/l+(r<n)*6:r===a?(n-t)/l+2:(t-r)/l+4,l/=s<.5?a+i:2-a-i,o*=60):l=s>0&&s<1?0:o,new iS(o,l,s,e.opacity)}function iS(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function iA(e){return(e=(e||0)%360)<0?e+360:e}function iE(e){return Math.max(0,Math.min(1,e||0))}function iM(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function ik(e,t,r,n,i){var a=e*e,o=a*e;return((1-3*e+3*a-o)*t+(4-6*a+3*o)*r+(1+3*e+3*a-3*o)*n+o*i)/6}n6(n7,ih,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:iu,formatHex:iu,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return iP(this).formatHsl()},formatRgb:id,toString:id}),n6(ig,iv,n4(n7,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new ig(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new ig(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new ig(iw(this.r),iw(this.g),iw(this.b),ix(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:im,formatHex:im,formatHex8:function(){return`#${iO(this.r)}${iO(this.g)}${iO(this.b)}${iO((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:ib,toString:ib})),n6(iS,function(e,t,r,n){return 1==arguments.length?iP(e):new iS(e,t,r,null==n?1:n)},n4(n7,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new iS(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new iS(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new ig(iM(e>=240?e-240:e+120,i,n),iM(e,i,n),iM(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new iS(iA(this.h),iE(this.s),iE(this.l),ix(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=ix(this.opacity);return`${1===e?"hsl(":"hsla("}${iA(this.h)}, ${100*iE(this.s)}%, ${100*iE(this.l)}%${1===e?")":`, ${e})`}`}}));let iT=e=>()=>e;function i_(e,t){var r=t-e;return r?function(t){return e+t*r}:iT(isNaN(e)?t:e)}let iN=function e(t){var r,n=1==(r=+(r=t))?i_:function(e,t){var n,i,a;return t-e?(n=e,i=t,n=Math.pow(n,a=r),i=Math.pow(i,a)-n,a=1/a,function(e){return Math.pow(n+e*i,a)}):iT(isNaN(e)?t:e)};function i(e,t){var r=n((e=iv(e)).r,(t=iv(t)).r),i=n(e.g,t.g),a=n(e.b,t.b),o=i_(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return i.gamma=e,i}(1);function iC(e){return function(t){var r,n,i=t.length,a=Array(i),o=Array(i),l=Array(i);for(r=0;r<i;++r)n=iv(t[r]),a[r]=n.r||0,o[r]=n.g||0,l[r]=n.b||0;return a=e(a),o=e(o),l=e(l),n.opacity=1,function(e){return n.r=a(e),n.g=o(e),n.b=l(e),n+""}}}function iD(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}iC(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],a=e[n+1],o=n>0?e[n-1]:2*i-a,l=n<t-1?e[n+2]:2*a-i;return ik((r-n/t)*t,o,i,a,l)}}),iC(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],a=e[n%t],o=e[(n+1)%t],l=e[(n+2)%t];return ik((r-n/t)*t,i,a,o,l)}});var iI=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,iR=RegExp(iI.source,"g");function iL(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?iT(t):("number"===i?iD:"string"===i?(n=ih(t))?(t=n,iN):function(e,t){var r,n,i,a,o,l=iI.lastIndex=iR.lastIndex=0,s=-1,c=[],u=[];for(e+="",t+="";(i=iI.exec(e))&&(a=iR.exec(t));)(o=a.index)>l&&(o=t.slice(l,o),c[s]?c[s]+=o:c[++s]=o),(i=i[0])===(a=a[0])?c[s]?c[s]+=a:c[++s]=a:(c[++s]=null,u.push({i:s,x:iD(i,a)})),l=iR.lastIndex;return l<t.length&&(o=t.slice(l),c[s]?c[s]+=o:c[++s]=o),c.length<2?u[0]?(r=u[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=u.length,function(e){for(var r,n=0;n<t;++n)c[(r=u[n]).i]=r.x(e);return c.join("")})}:t instanceof ih?iN:t instanceof Date?function(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=Array(i),o=Array(n);for(r=0;r<i;++r)a[r]=iL(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},i={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=iL(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}:iD:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}})(e,t)}function iz(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function iB(e){return+e}var i$=[0,1];function iU(e){return e}function iK(e,t){var r;return(t-=e=+e)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function iF(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=iK(i,n),a=r(o,a)):(n=iK(n,i),a=r(a,o)),function(e){return a(n(e))}}function iW(e,t,r){var n=Math.min(e.length,t.length)-1,i=Array(n),a=Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=iK(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=n3(e,t,1,n)-1;return a[r](i[r](t))}}function iq(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function iV(){var e,t,r,n,i,a,o=i$,l=i$,s=iL,c=iU;function u(){var e,t,r,s=Math.min(o.length,l.length);return c!==iU&&(e=o[0],t=o[s-1],e>t&&(r=e,e=t,t=r),c=function(r){return Math.max(e,Math.min(t,r))}),n=s>2?iW:iF,i=a=null,f}function f(t){return null==t||isNaN(t=+t)?r:(i||(i=n(o.map(e),l,s)))(e(c(t)))}return f.invert=function(r){return c(t((a||(a=n(l,o.map(e),iD)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,iB),u()):o.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),u()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),s=iz,u()},f.clamp=function(e){return arguments.length?(c=!!e||iU,u()):c!==iU},f.interpolate=function(e){return arguments.length?(s=e,u()):s},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,u()}}function iH(){return iV()(iU,iU)}var iZ=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function iY(e){var t;if(!(t=iZ.exec(e)))throw Error("invalid format: "+e);return new iG({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function iG(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function iX(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function iJ(e){return(e=iX(Math.abs(e)))?e[1]:NaN}function iQ(e,t){var r=iX(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}iY.prototype=iG.prototype,iG.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let i0={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>iQ(100*e,t),r:iQ,s:function(e,t){var r=iX(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(lw=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,o=n.length;return a===o?n:a>o?n+Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+Array(1-a).join("0")+iX(e,Math.max(0,t+a-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function i1(e){return e}var i2=Array.prototype.map,i5=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function i3(e,t,r,n){var i,a,o=nX(e,t,r);switch((n=iY(null==n?",f":n)).type){case"s":var l=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(iJ(l)/3)))-iJ(Math.abs(o))))||(n.precision=a),lP(n,l);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(a=Math.max(0,iJ(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(i=Math.abs(i=o)))-iJ(i))+1)||(n.precision=a-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(a=Math.max(0,-iJ(Math.abs(o))))||(n.precision=a-("%"===n.type)*2)}return lj(n)}function i6(e){var t=e.domain;return e.ticks=function(e){var r=t();return nY(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return i3(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,l=a.length-1,s=a[o],c=a[l],u=10;for(c<s&&(i=s,s=c,c=i,i=o,o=l,l=i);u-- >0;){if((i=nG(s,c,r))===n)return a[o]=s,a[l]=c,t(a);if(i>0)s=Math.floor(s/i)*i,c=Math.ceil(c/i)*i;else if(i<0)s=Math.ceil(s*i)/i,c=Math.floor(c*i)/i;else break;n=i}return e},e}function i4(e,t){e=e.slice();var r,n=0,i=e.length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function i7(e){return Math.log(e)}function i8(e){return Math.exp(e)}function i9(e){return-Math.log(-e)}function ae(e){return-Math.exp(-e)}function at(e){return isFinite(e)?+("1e"+e):e<0?0:e}function ar(e){return(t,r)=>-e(-t,r)}function an(e){let t,r;let n=e(i7,i8),i=n.domain,a=10;function o(){var o,l;return t=(o=a)===Math.E?Math.log:10===o&&Math.log10||2===o&&Math.log2||(o=Math.log(o),e=>Math.log(e)/o),r=10===(l=a)?at:l===Math.E?Math.exp:e=>Math.pow(l,e),i()[0]<0?(t=ar(t),r=ar(r),e(i9,ae)):e(i7,i8),n}return n.base=function(e){return arguments.length?(a=+e,o()):a},n.domain=function(e){return arguments.length?(i(e),o()):i()},n.ticks=e=>{let n,o;let l=i(),s=l[0],c=l[l.length-1],u=c<s;u&&([s,c]=[c,s]);let f=t(s),d=t(c),h=null==e?10:+e,p=[];if(!(a%1)&&d-f<h){if(f=Math.floor(f),d=Math.ceil(d),s>0){for(;f<=d;++f)for(n=1;n<a;++n)if(!((o=f<0?n/r(-f):n*r(f))<s)){if(o>c)break;p.push(o)}}else for(;f<=d;++f)for(n=a-1;n>=1;--n)if(!((o=f>0?n/r(-f):n*r(f))<s)){if(o>c)break;p.push(o)}2*p.length<h&&(p=nY(s,c,h))}else p=nY(f,d,Math.min(d-f,h)).map(r);return u?p.reverse():p},n.tickFormat=(e,i)=>{if(null==e&&(e=10),null==i&&(i=10===a?"s":","),"function"!=typeof i&&(a%1||null!=(i=iY(i)).precision||(i.trim=!0),i=lj(i)),e===1/0)return i;let o=Math.max(1,a*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*a<a-.5&&(n*=a),n<=o?i(e):""}},n.nice=()=>i(i4(i(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function ai(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function aa(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function ao(e){var t=1,r=e(ai(1),aa(t));return r.constant=function(r){return arguments.length?e(ai(t=+r),aa(t)):t},i6(r)}function al(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function as(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function ac(e){return e<0?-e*e:e*e}function au(e){var t=e(iU,iU),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(iU,iU):.5===r?e(as,ac):e(al(r),al(1/r)):r},i6(t)}function af(){var e=au(iV());return e.copy=function(){return iq(e,af()).exponent(e.exponent())},nR.apply(e,arguments),e}function ad(){return af.apply(null,arguments).exponent(.5)}function ah(e){return Math.sign(e)*e*e}function ap(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function ay(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function av(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:e>t?1:0)}function ag(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}lj=(lO=function(e){var t,r,n,i=void 0===e.grouping||void 0===e.thousands?i1:(t=i2.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,l=t[0],s=0;i>0&&l>0&&(s+l+1>n&&(l=Math.max(1,n-s)),a.push(e.substring(i-=l,i+l)),!((s+=l+1)>n));)l=t[o=(o+1)%t.length];return a.reverse().join(r)}),a=void 0===e.currency?"":e.currency[0]+"",o=void 0===e.currency?"":e.currency[1]+"",l=void 0===e.decimal?".":e.decimal+"",s=void 0===e.numerals?i1:(n=i2.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return n[+e]})}),c=void 0===e.percent?"%":e.percent+"",u=void 0===e.minus?"−":e.minus+"",f=void 0===e.nan?"NaN":e.nan+"";function d(e){var t=(e=iY(e)).fill,r=e.align,n=e.sign,d=e.symbol,h=e.zero,p=e.width,y=e.comma,v=e.precision,g=e.trim,m=e.type;"n"===m?(y=!0,m="g"):i0[m]||(void 0===v&&(v=12),g=!0,m="g"),(h||"0"===t&&"="===r)&&(h=!0,t="0",r="=");var b="$"===d?a:"#"===d&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",x="$"===d?o:/[%p]/.test(m)?c:"",w=i0[m],O=/[defgprs%]/.test(m);function j(e){var a,o,c,d=b,j=x;if("c"===m)j=w(e)+j,e="";else{var P=(e=+e)<0||1/e<0;if(e=isNaN(e)?f:w(Math.abs(e),v),g&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),P&&0==+e&&"+"!==n&&(P=!1),d=(P?"("===n?n:u:"-"===n||"("===n?"":n)+d,j=("s"===m?i5[8+lw/3]:"")+j+(P&&"("===n?")":""),O){for(a=-1,o=e.length;++a<o;)if(48>(c=e.charCodeAt(a))||c>57){j=(46===c?l+e.slice(a+1):e.slice(a))+j,e=e.slice(0,a);break}}}y&&!h&&(e=i(e,1/0));var S=d.length+e.length+j.length,A=S<p?Array(p-S+1).join(t):"";switch(y&&h&&(e=i(A+e,A.length?p-j.length:1/0),A=""),r){case"<":e=d+e+j+A;break;case"=":e=d+A+e+j;break;case"^":e=A.slice(0,S=A.length>>1)+d+e+j+A.slice(S);break;default:e=A+d+e+j}return s(e)}return v=void 0===v?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return e+""},j}return{format:d,formatPrefix:function(e,t){var r=d(((e=iY(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(iJ(t)/3))),i=Math.pow(10,-n),a=i5[8+n/3];return function(e){return r(i*e)+a}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,lP=lO.formatPrefix;let am=new Date,ab=new Date;function ax(e,t,r,n){function i(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{let t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{let o;let l=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n)||!(a>0))return l;do l.push(o=new Date(+r)),t(r,a),e(r);while(o<r&&r<n);return l},i.filter=r=>ax(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e){if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}}),r&&(i.count=(t,n)=>(am.setTime(+t),ab.setTime(+n),e(am),e(ab),Math.floor(r(am,ab))),i.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null),i}let aw=ax(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);aw.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?ax(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):aw:null,aw.range;let aO=ax(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());aO.range;let aj=ax(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());aj.range;let aP=ax(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());aP.range;let aS=ax(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());aS.range;let aA=ax(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());aA.range;let aE=ax(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);aE.range;let aM=ax(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);aM.range;let ak=ax(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function aT(e){return ax(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}ak.range;let a_=aT(0),aN=aT(1),aC=aT(2),aD=aT(3),aI=aT(4),aR=aT(5),aL=aT(6);function az(e){return ax(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}a_.range,aN.range,aC.range,aD.range,aI.range,aR.range,aL.range;let aB=az(0),a$=az(1),aU=az(2),aK=az(3),aF=az(4),aW=az(5),aq=az(6);aB.range,a$.range,aU.range,aK.range,aF.range,aW.range,aq.range;let aV=ax(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());aV.range;let aH=ax(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());aH.range;let aZ=ax(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());aZ.every=e=>isFinite(e=Math.floor(e))&&e>0?ax(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,aZ.range;let aY=ax(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function aG(e,t,r,n,i,a){let o=[[aO,1,1e3],[aO,5,5e3],[aO,15,15e3],[aO,30,3e4],[a,1,6e4],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let i=Math.abs(r-t)/n,a=n0(([,,e])=>e).right(o,i);if(a===o.length)return e.every(nX(t/31536e6,r/31536e6,n));if(0===a)return aw.every(Math.max(nX(t,r,n),1));let[l,s]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(s)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let i=r&&"function"==typeof r.range?r:l(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},l]}aY.every=e=>isFinite(e=Math.floor(e))&&e>0?ax(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,aY.range;let[aX,aJ]=aG(aY,aH,aB,ak,aA,aP),[aQ,a0]=aG(aZ,aV,a_,aE,aS,aj);function a1(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function a2(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function a5(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var a3={"-":"",_:" ",0:"0"},a6=/^\s*\d+/,a4=/^%/,a7=/[\\^$*+?|[\]().{}]/g;function a8(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?Array(r-a+1).join(t)+i:i)}function a9(e){return e.replace(a7,"\\$&")}function oe(e){return RegExp("^(?:"+e.map(a9).join("|")+")","i")}function ot(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function or(e,t,r){var n=a6.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function on(e,t,r){var n=a6.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function oi(e,t,r){var n=a6.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function oa(e,t,r){var n=a6.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function oo(e,t,r){var n=a6.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function ol(e,t,r){var n=a6.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function os(e,t,r){var n=a6.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function oc(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function ou(e,t,r){var n=a6.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function of(e,t,r){var n=a6.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function od(e,t,r){var n=a6.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function oh(e,t,r){var n=a6.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function op(e,t,r){var n=a6.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function oy(e,t,r){var n=a6.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function ov(e,t,r){var n=a6.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function og(e,t,r){var n=a6.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function om(e,t,r){var n=a6.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function ob(e,t,r){var n=a4.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function ox(e,t,r){var n=a6.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function ow(e,t,r){var n=a6.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function oO(e,t){return a8(e.getDate(),t,2)}function oj(e,t){return a8(e.getHours(),t,2)}function oP(e,t){return a8(e.getHours()%12||12,t,2)}function oS(e,t){return a8(1+aE.count(aZ(e),e),t,3)}function oA(e,t){return a8(e.getMilliseconds(),t,3)}function oE(e,t){return oA(e,t)+"000"}function oM(e,t){return a8(e.getMonth()+1,t,2)}function ok(e,t){return a8(e.getMinutes(),t,2)}function oT(e,t){return a8(e.getSeconds(),t,2)}function o_(e){var t=e.getDay();return 0===t?7:t}function oN(e,t){return a8(a_.count(aZ(e)-1,e),t,2)}function oC(e){var t=e.getDay();return t>=4||0===t?aI(e):aI.ceil(e)}function oD(e,t){return e=oC(e),a8(aI.count(aZ(e),e)+(4===aZ(e).getDay()),t,2)}function oI(e){return e.getDay()}function oR(e,t){return a8(aN.count(aZ(e)-1,e),t,2)}function oL(e,t){return a8(e.getFullYear()%100,t,2)}function oz(e,t){return a8((e=oC(e)).getFullYear()%100,t,2)}function oB(e,t){return a8(e.getFullYear()%1e4,t,4)}function o$(e,t){var r=e.getDay();return a8((e=r>=4||0===r?aI(e):aI.ceil(e)).getFullYear()%1e4,t,4)}function oU(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+a8(t/60|0,"0",2)+a8(t%60,"0",2)}function oK(e,t){return a8(e.getUTCDate(),t,2)}function oF(e,t){return a8(e.getUTCHours(),t,2)}function oW(e,t){return a8(e.getUTCHours()%12||12,t,2)}function oq(e,t){return a8(1+aM.count(aY(e),e),t,3)}function oV(e,t){return a8(e.getUTCMilliseconds(),t,3)}function oH(e,t){return oV(e,t)+"000"}function oZ(e,t){return a8(e.getUTCMonth()+1,t,2)}function oY(e,t){return a8(e.getUTCMinutes(),t,2)}function oG(e,t){return a8(e.getUTCSeconds(),t,2)}function oX(e){var t=e.getUTCDay();return 0===t?7:t}function oJ(e,t){return a8(aB.count(aY(e)-1,e),t,2)}function oQ(e){var t=e.getUTCDay();return t>=4||0===t?aF(e):aF.ceil(e)}function o0(e,t){return e=oQ(e),a8(aF.count(aY(e),e)+(4===aY(e).getUTCDay()),t,2)}function o1(e){return e.getUTCDay()}function o2(e,t){return a8(a$.count(aY(e)-1,e),t,2)}function o5(e,t){return a8(e.getUTCFullYear()%100,t,2)}function o3(e,t){return a8((e=oQ(e)).getUTCFullYear()%100,t,2)}function o6(e,t){return a8(e.getUTCFullYear()%1e4,t,4)}function o4(e,t){var r=e.getUTCDay();return a8((e=r>=4||0===r?aF(e):aF.ceil(e)).getUTCFullYear()%1e4,t,4)}function o7(){return"+0000"}function o8(){return"%"}function o9(e){return+e}function le(e){return Math.floor(+e/1e3)}function lt(e){return new Date(e)}function lr(e){return e instanceof Date?+e:+new Date(+e)}function ln(e,t,r,n,i,a,o,l,s,c){var u=iH(),f=u.invert,d=u.domain,h=c(".%L"),p=c(":%S"),y=c("%I:%M"),v=c("%I %p"),g=c("%a %d"),m=c("%b %d"),b=c("%B"),x=c("%Y");function w(e){return(s(e)<e?h:l(e)<e?p:o(e)<e?y:a(e)<e?v:n(e)<e?i(e)<e?g:m:r(e)<e?b:x)(e)}return u.invert=function(e){return new Date(f(e))},u.domain=function(e){return arguments.length?d(Array.from(e,lr)):d().map(lt)},u.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},u.tickFormat=function(e,t){return null==t?w:c(t)},u.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(i4(r,e)):u},u.copy=function(){return iq(u,ln(e,t,r,n,i,a,o,l,s,c))},u}function li(){return nR.apply(ln(aQ,a0,aZ,aV,a_,aE,aS,aj,aO,lA).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function la(){return nR.apply(ln(aX,aJ,aY,aH,aB,aM,aA,aP,aO,lE).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function lo(){var e,t,r,n,i,a=0,o=1,l=iU,s=!1;function c(t){return null==t||isNaN(t=+t)?i:l(0===r?.5:(t=(n(t)-e)*r,s?Math.max(0,Math.min(1,t)):t))}function u(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),c):[l(0),l(1)]}}return c.domain=function(i){return arguments.length?([a,o]=i,e=n(a=+a),t=n(o=+o),r=e===t?0:1/(t-e),c):[a,o]},c.clamp=function(e){return arguments.length?(s=!!e,c):s},c.interpolator=function(e){return arguments.length?(l=e,c):l},c.range=u(iL),c.rangeRound=u(iz),c.unknown=function(e){return arguments.length?(i=e,c):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),c}}function ll(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function ls(){var e=au(lo());return e.copy=function(){return ll(e,ls()).exponent(e.exponent())},nL.apply(e,arguments)}function lc(){return ls.apply(null,arguments).exponent(.5)}function lu(){var e,t,r,n,i,a,o,l=0,s=.5,c=1,u=1,f=iU,d=!1;function h(e){return isNaN(e=+e)?o:(e=.5+((e=+a(e))-t)*(u*e<u*t?n:i),f(d?Math.max(0,Math.min(1,e)):e))}function p(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=iL);for(var r=0,n=t.length-1,i=t[0],a=Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),h):[f(0),f(.5),f(1)]}}return h.domain=function(o){return arguments.length?([l,s,c]=o,e=a(l=+l),t=a(s=+s),r=a(c=+c),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),u=t<e?-1:1,h):[l,s,c]},h.clamp=function(e){return arguments.length?(d=!!e,h):d},h.interpolator=function(e){return arguments.length?(f=e,h):f},h.range=p(iL),h.rangeRound=p(iz),h.unknown=function(e){return arguments.length?(o=e,h):o},function(o){return a=o,e=o(l),t=o(s),r=o(c),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),u=t<e?-1:1,h}}function lf(){var e=au(lu());return e.copy=function(){return ll(e,lf()).exponent(e.exponent())},nL.apply(e,arguments)}function ld(){return lf.apply(null,arguments).exponent(.5)}lA=(lS=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,l=e.months,s=e.shortMonths,c=oe(i),u=ot(i),f=oe(a),d=ot(a),h=oe(o),p=ot(o),y=oe(l),v=ot(l),g=oe(s),m=ot(s),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return s[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:oO,e:oO,f:oE,g:oz,G:o$,H:oj,I:oP,j:oS,L:oA,m:oM,M:ok,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:o9,s:le,S:oT,u:o_,U:oN,V:oD,w:oI,W:oR,x:null,X:null,y:oL,Y:oB,Z:oU,"%":o8},x={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return s[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:oK,e:oK,f:oH,g:o3,G:o4,H:oF,I:oW,j:oq,L:oV,m:oZ,M:oY,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:o9,s:le,S:oG,u:oX,U:oJ,V:o0,w:o1,W:o2,x:null,X:null,y:o5,Y:o6,Z:o7,"%":o8},w={a:function(e,t,r){var n=h.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=g.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return P(e,t,r,n)},d:od,e:od,f:om,g:os,G:ol,H:op,I:op,j:oh,L:og,m:of,M:oy,p:function(e,t,r){var n=c.exec(t.slice(r));return n?(e.p=u.get(n[0].toLowerCase()),r+n[0].length):-1},q:ou,Q:ox,s:ow,S:ov,u:on,U:oi,V:oa,w:or,W:oo,x:function(e,t,n){return P(e,r,t,n)},X:function(e,t,r){return P(e,n,t,r)},y:os,Y:ol,Z:oc,"%":ob};function O(e,t){return function(r){var n,i,a,o=[],l=-1,s=0,c=e.length;for(r instanceof Date||(r=new Date(+r));++l<c;)37===e.charCodeAt(l)&&(o.push(e.slice(s,l)),null!=(i=a3[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),s=l+1);return o.push(e.slice(s,l)),o.join("")}}function j(e,t){return function(r){var n,i,a=a5(1900,void 0,1);if(P(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(!t||"Z"in a||(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(n=(i=(n=a2(a5(a.y,0,1))).getUTCDay())>4||0===i?a$.ceil(n):a$(n),n=aM.offset(n,(a.V-1)*7),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(n=(i=(n=a1(a5(a.y,0,1))).getDay())>4||0===i?aN.ceil(n):aN(n),n=aE.offset(n,(a.V-1)*7),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:"W"in a?1:0),i="Z"in a?a2(a5(a.y,0,1)).getUTCDay():a1(a5(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,a2(a)):a1(a)}}function P(e,t,r,n){for(var i,a,o=0,l=t.length,s=r.length;o<l;){if(n>=s)return -1;if(37===(i=t.charCodeAt(o++))){if(!(a=w[(i=t.charAt(o++))in a3?t.charAt(o++):i])||(n=a(e,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=j(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=j(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,lS.parse,lE=lS.utcFormat,lS.utcParse;var lh=e=>e.chartData,lp=rx([lh],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),ly=(e,t,r,n)=>n?lp(e):lh(e);function lv(e){return Number.isFinite(e)}function lg(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}function lm(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if(lv(t)&&lv(r))return!0}return!1}function lb(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var lx,lw,lO,lj,lP,lS,lA,lE,lM,lk,lT=!0,l_="[DecimalError] ",lN=l_+"Invalid argument: ",lC=l_+"Exponent out of range: ",lD=Math.floor,lI=Math.pow,lR=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,lL=lD(1286742750677284.5),lz={};function lB(e,t){var r,n,i,a,o,l,s,c,u=e.constructor,f=u.precision;if(!e.s||!t.s)return t.s||(t=new u(e)),lT?lY(t,f):t;if(s=e.d,c=t.d,o=e.e,i=t.e,s=s.slice(),a=o-i){for(a<0?(n=s,a=-a,l=c.length):(n=c,i=o,l=s.length),a>(l=(o=Math.ceil(f/7))>l?o+1:l+1)&&(a=l,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((l=s.length)-(a=c.length)<0&&(a=l,n=c,c=s,s=n),r=0;a;)r=(s[--a]=s[a]+c[a]+r)/1e7|0,s[a]%=1e7;for(r&&(s.unshift(r),++i),l=s.length;0==s[--l];)s.pop();return t.d=s,t.e=i,lT?lY(t,f):t}function l$(e,t,r){if(e!==~~e||e<t||e>r)throw Error(lN+e)}function lU(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=lV(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=lV(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}lz.absoluteValue=lz.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},lz.comparedTo=lz.cmp=function(e){var t,r,n,i;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(i=e.d.length)?n:i;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},lz.decimalPlaces=lz.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},lz.dividedBy=lz.div=function(e){return lK(this,new this.constructor(e))},lz.dividedToIntegerBy=lz.idiv=function(e){var t=this.constructor;return lY(lK(this,new t(e),0,1),t.precision)},lz.equals=lz.eq=function(e){return!this.cmp(e)},lz.exponent=function(){return lW(this)},lz.greaterThan=lz.gt=function(e){return this.cmp(e)>0},lz.greaterThanOrEqualTo=lz.gte=function(e){return this.cmp(e)>=0},lz.isInteger=lz.isint=function(){return this.e>this.d.length-2},lz.isNegative=lz.isneg=function(){return this.s<0},lz.isPositive=lz.ispos=function(){return this.s>0},lz.isZero=function(){return 0===this.s},lz.lessThan=lz.lt=function(e){return 0>this.cmp(e)},lz.lessThanOrEqualTo=lz.lte=function(e){return 1>this.cmp(e)},lz.logarithm=lz.log=function(e){var t,r=this.constructor,n=r.precision,i=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(lk))throw Error(l_+"NaN");if(this.s<1)throw Error(l_+(this.s?"NaN":"-Infinity"));return this.eq(lk)?new r(0):(lT=!1,t=lK(lH(this,i),lH(e,i),i),lT=!0,lY(t,n))},lz.minus=lz.sub=function(e){return e=new this.constructor(e),this.s==e.s?lG(this,e):lB(this,(e.s=-e.s,e))},lz.modulo=lz.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(l_+"NaN");return this.s?(lT=!1,t=lK(this,e,0,1).times(e),lT=!0,this.minus(t)):lY(new r(this),n)},lz.naturalExponential=lz.exp=function(){return lF(this)},lz.naturalLogarithm=lz.ln=function(){return lH(this)},lz.negated=lz.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},lz.plus=lz.add=function(e){return e=new this.constructor(e),this.s==e.s?lB(this,e):lG(this,(e.s=-e.s,e))},lz.precision=lz.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(lN+e);if(t=lW(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},lz.squareRoot=lz.sqrt=function(){var e,t,r,n,i,a,o,l=this.constructor;if(this.s<1){if(!this.s)return new l(0);throw Error(l_+"NaN")}for(e=lW(this),lT=!1,0==(i=Math.sqrt(+this))||i==1/0?(((t=lU(this.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=lD((e+1)/2)-(e<0||e%2),n=new l(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new l(i.toString()),i=o=(r=l.precision)+3;;)if(n=(a=n).plus(lK(this,a,o+2)).times(.5),lU(a.d).slice(0,o)===(t=lU(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(lY(a,r+1,0),a.times(a).eq(this)){n=a;break}}else if("9999"!=t)break;o+=4}return lT=!0,lY(n,r)},lz.times=lz.mul=function(e){var t,r,n,i,a,o,l,s,c,u=this.constructor,f=this.d,d=(e=new u(e)).d;if(!this.s||!e.s)return new u(0);for(e.s*=this.s,r=this.e+e.e,(s=f.length)<(c=d.length)&&(a=f,f=d,d=a,o=s,s=c,c=o),a=[],n=o=s+c;n--;)a.push(0);for(n=c;--n>=0;){for(t=0,i=s+n;i>n;)l=a[i]+d[n]*f[i-n-1]+t,a[i--]=l%1e7|0,t=l/1e7|0;a[i]=(a[i]+t)%1e7|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,lT?lY(e,u.precision):e},lz.toDecimalPlaces=lz.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(l$(e,0,1e9),void 0===t?t=n.rounding:l$(t,0,8),lY(r,e+lW(r)+1,t))},lz.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=lX(n,!0):(l$(e,0,1e9),void 0===t?t=i.rounding:l$(t,0,8),r=lX(n=lY(new i(n),e+1,t),!0,e+1)),r},lz.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?lX(this):(l$(e,0,1e9),void 0===t?t=i.rounding:l$(t,0,8),r=lX((n=lY(new i(this),e+lW(this)+1,t)).abs(),!1,e+lW(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},lz.toInteger=lz.toint=function(){var e=this.constructor;return lY(new e(this),lW(this)+1,e.rounding)},lz.toNumber=function(){return+this},lz.toPower=lz.pow=function(e){var t,r,n,i,a,o,l=this,s=l.constructor,c=+(e=new s(e));if(!e.s)return new s(lk);if(!(l=new s(l)).s){if(e.s<1)throw Error(l_+"Infinity");return l}if(l.eq(lk))return l;if(n=s.precision,e.eq(lk))return lY(l,n);if(o=(t=e.e)>=(r=e.d.length-1),a=l.s,o){if((r=c<0?-c:c)<=9007199254740991){for(i=new s(lk),t=Math.ceil(n/7+4),lT=!1;r%2&&lJ((i=i.times(l)).d,t),0!==(r=lD(r/2));)lJ((l=l.times(l)).d,t);return lT=!0,e.s<0?new s(lk).div(i):lY(i,n)}}else if(a<0)throw Error(l_+"NaN");return a=a<0&&1&e.d[Math.max(t,r)]?-1:1,l.s=1,lT=!1,i=e.times(lH(l,n+12)),lT=!0,(i=lF(i)).s=a,i},lz.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?(r=lW(i),n=lX(i,r<=a.toExpNeg||r>=a.toExpPos)):(l$(e,1,1e9),void 0===t?t=a.rounding:l$(t,0,8),r=lW(i=lY(new a(i),e,t)),n=lX(i,e<=r||r<=a.toExpNeg,e)),n},lz.toSignificantDigits=lz.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(l$(e,1,1e9),void 0===t?t=r.rounding:l$(t,0,8)),lY(new r(this),e,t)},lz.toString=lz.valueOf=lz.val=lz.toJSON=lz[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=lW(this),t=this.constructor;return lX(this,e<=t.toExpNeg||e>=t.toExpPos)};var lK=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=e[r]<t[r]?1:0,e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var l,s,c,u,f,d,h,p,y,v,g,m,b,x,w,O,j,P,S=n.constructor,A=n.s==i.s?1:-1,E=n.d,M=i.d;if(!n.s)return new S(n);if(!i.s)throw Error(l_+"Division by zero");for(c=0,s=n.e-i.e,j=M.length,w=E.length,p=(h=new S(A)).d=[];M[c]==(E[c]||0);)++c;if(M[c]>(E[c]||0)&&--s,(m=null==a?a=S.precision:o?a+(lW(n)-lW(i))+1:a)<0)return new S(0);if(m=m/7+2|0,c=0,1==j)for(u=0,M=M[0],m++;(c<w||u)&&m--;c++)b=1e7*u+(E[c]||0),p[c]=b/M|0,u=b%M|0;else{for((u=1e7/(M[0]+1)|0)>1&&(M=e(M,u),E=e(E,u),j=M.length,w=E.length),x=j,v=(y=E.slice(0,j)).length;v<j;)y[v++]=0;(P=M.slice()).unshift(0),O=M[0],M[1]>=1e7/2&&++O;do u=0,(l=t(M,y,j,v))<0?(g=y[0],j!=v&&(g=1e7*g+(y[1]||0)),(u=g/O|0)>1?(u>=1e7&&(u=1e7-1),d=(f=e(M,u)).length,v=y.length,1==(l=t(f,y,d,v))&&(u--,r(f,j<d?P:M,d))):(0==u&&(l=u=1),f=M.slice()),(d=f.length)<v&&f.unshift(0),r(y,f,v),-1==l&&(v=y.length,(l=t(M,y,j,v))<1&&(u++,r(y,j<v?P:M,v))),v=y.length):0===l&&(u++,y=[0]),p[c++]=u,l&&y[0]?y[v++]=E[x]||0:(y=[E[x]],v=1);while((x++<w||void 0!==y[0])&&m--)}return p[0]||p.shift(),h.e=s,lY(h,o?a+lW(h)+1:a)}}();function lF(e,t){var r,n,i,a,o,l=0,s=0,c=e.constructor,u=c.precision;if(lW(e)>16)throw Error(lC+lW(e));if(!e.s)return new c(lk);for(null==t?(lT=!1,o=u):o=t,a=new c(.03125);e.abs().gte(.1);)e=e.times(a),s+=5;for(o+=Math.log(lI(2,s))/Math.LN10*2+5|0,r=n=i=new c(lk),c.precision=o;;){if(n=lY(n.times(e),o),r=r.times(++l),lU((a=i.plus(lK(n,r,o))).d).slice(0,o)===lU(i.d).slice(0,o)){for(;s--;)i=lY(i.times(i),o);return c.precision=u,null==t?(lT=!0,lY(i,u)):i}i=a}}function lW(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function lq(e,t,r){if(t>e.LN10.sd())throw lT=!0,r&&(e.precision=r),Error(l_+"LN10 precision limit exceeded");return lY(new e(e.LN10),t)}function lV(e){for(var t="";e--;)t+="0";return t}function lH(e,t){var r,n,i,a,o,l,s,c,u,f=1,d=e,h=d.d,p=d.constructor,y=p.precision;if(d.s<1)throw Error(l_+(d.s?"NaN":"-Infinity"));if(d.eq(lk))return new p(0);if(null==t?(lT=!1,c=y):c=t,d.eq(10))return null==t&&(lT=!0),lq(p,c);if(c+=10,p.precision=c,n=(r=lU(h)).charAt(0),!(15e14>Math.abs(a=lW(d))))return s=lq(p,c+2,y).times(a+""),d=lH(new p(n+"."+r.slice(1)),c-10).plus(s),p.precision=y,null==t?(lT=!0,lY(d,y)):d;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=lU((d=d.times(e)).d)).charAt(0),f++;for(a=lW(d),n>1?(d=new p("0."+r),a++):d=new p(n+"."+r.slice(1)),l=o=d=lK(d.minus(lk),d.plus(lk),c),u=lY(d.times(d),c),i=3;;){if(o=lY(o.times(u),c),lU((s=l.plus(lK(o,new p(i),c))).d).slice(0,c)===lU(l.d).slice(0,c))return l=l.times(2),0!==a&&(l=l.plus(lq(p,c+2,y).times(a+""))),l=lK(l,new p(f),c),p.precision=y,null==t?(lT=!0,lY(l,y)):l;l=s,i+=2}}function lZ(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,r=r-n-1,e.e=lD(r/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),lT&&(e.e>lL||e.e<-lL))throw Error(lC+r)}else e.s=0,e.e=0,e.d=[0];return e}function lY(e,t,r){var n,i,a,o,l,s,c,u,f=e.d;for(o=1,a=f[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,c=f[u=0];else{if((u=Math.ceil((n+1)/7))>=(a=f.length))return e;for(o=1,c=a=f[u];a>=10;a/=10)o++;n%=7,i=n-7+o}if(void 0!==r&&(l=c/(a=lI(10,o-i-1))%10|0,s=t<0||void 0!==f[u+1]||c%a,s=r<4?(l||s)&&(0==r||r==(e.s<0?3:2)):l>5||5==l&&(4==r||s||6==r&&(n>0?i>0?c/lI(10,o-i):0:f[u-1])%10&1||r==(e.s<0?8:7))),t<1||!f[0])return s?(a=lW(e),f.length=1,t=t-a-1,f[0]=lI(10,(7-t%7)%7),e.e=lD(-t/7)||0):(f.length=1,f[0]=e.e=e.s=0),e;if(0==n?(f.length=u,a=1,u--):(f.length=u+1,a=lI(10,7-n),f[u]=i>0?(c/lI(10,o-i)%lI(10,i)|0)*a:0),s)for(;;){if(0==u){1e7==(f[0]+=a)&&(f[0]=1,++e.e);break}if(f[u]+=a,1e7!=f[u])break;f[u--]=0,a=1}for(n=f.length;0===f[--n];)f.pop();if(lT&&(e.e>lL||e.e<-lL))throw Error(lC+lW(e));return e}function lG(e,t){var r,n,i,a,o,l,s,c,u,f,d=e.constructor,h=d.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new d(e),lT?lY(t,h):t;if(s=e.d,f=t.d,n=t.e,c=e.e,s=s.slice(),o=c-n){for((u=o<0)?(r=s,o=-o,l=f.length):(r=f,n=c,l=s.length),o>(i=Math.max(Math.ceil(h/7),l)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((u=(i=s.length)<(l=f.length))&&(l=i),i=0;i<l;i++)if(s[i]!=f[i]){u=s[i]<f[i];break}o=0}for(u&&(r=s,s=f,f=r,t.s=-t.s),l=s.length,i=f.length-l;i>0;--i)s[l++]=0;for(i=f.length;i>o;){if(s[--i]<f[i]){for(a=i;a&&0===s[--a];)s[a]=1e7-1;--s[a],s[i]+=1e7}s[i]-=f[i]}for(;0===s[--l];)s.pop();for(;0===s[0];s.shift())--n;return s[0]?(t.d=s,t.e=n,lT?lY(t,h):t):new d(0)}function lX(e,t,r){var n,i=lW(e),a=lU(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+lV(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+lV(-i-1)+a,r&&(n=r-o)>0&&(a+=lV(n))):i>=o?(a+=lV(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+lV(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=lV(n))),e.s<0?"-"+a:a}function lJ(e,t){if(e.length>t)return e.length=t,!0}function lQ(e){if(!e||"object"!=typeof e)throw Error(l_+"Object expected");var t,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]])){if(lD(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(lN+r+": "+n)}if(void 0!==(n=e[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(lN+r+": "+n)}return this}var lM=function e(t){var r,n,i;function a(e){if(!(this instanceof a))return new a(e);if(this.constructor=a,e instanceof a){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(lN+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return lZ(this,e.toString())}if("string"!=typeof e)throw Error(lN+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,lR.test(e))lZ(this,e);else throw Error(lN+e)}if(a.prototype=lz,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=lQ,void 0===t&&(t={}),t)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});lk=new lM(1);let l0=lM;var l1=e=>e,l2={},l5=e=>e===l2,l3=e=>function t(){return 0==arguments.length||1==arguments.length&&l5(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},l6=(e,t)=>1===e?t:l3(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(e=>e!==l2).length;return a>=e?t(...n):l6(e-a,l3(function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t(...n.map(e=>l5(e)?r.shift():e),...r)}))}),l4=e=>l6(e.length,e),l7=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},l8=l4((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),l9=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return l1;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((e,t)=>t(e),i(...arguments))}},se=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),st=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((e,r)=>{var n;return e===(null===(n=t)||void 0===n?void 0:n[r])})?r:(t=i,r=e(...i))}};function sr(e){return 0===e?1:Math.floor(new l0(e).abs().log(10).toNumber())+1}function sn(e,t,r){for(var n=new l0(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}l4((e,t,r)=>{var n=+e;return n+r*(+t-n)}),l4((e,t,r)=>{var n=t-+e;return(r-e)/(n=n||1/0)}),l4((e,t,r)=>{var n=t-+e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var si=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},sa=(e,t,r)=>{if(e.lte(0))return new l0(0);var n=sr(e.toNumber()),i=new l0(10).pow(n),a=e.div(i),o=1!==n?.05:.1,l=new l0(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return new l0(t?l.toNumber():Math.ceil(l.toNumber()))},so=(e,t,r)=>{var n=new l0(1),i=new l0(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new l0(10).pow(sr(e)-1),i=new l0(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new l0(Math.floor(e)))}else 0===e?i=new l0(Math.floor((t-1)/2)):r||(i=new l0(Math.floor(e)));var o=Math.floor((t-1)/2);return l9(l8(e=>i.add(new l0(e-o).mul(n)).toNumber()),l7)(0,t)},sl=function(e,t,r,n){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new l0(0),tickMin:new l0(0),tickMax:new l0(0)};var o=sa(new l0(t).sub(e).div(r-1),n,a),l=Math.ceil((i=e<=0&&t>=0?new l0(0):(i=new l0(e).add(t).div(2)).sub(new l0(i).mod(o))).sub(e).div(o).toNumber()),s=Math.ceil(new l0(t).sub(i).div(o).toNumber()),c=l+s+1;return c>r?sl(e,t,r,n,a+1):(c<r&&(s=t>0?s+(r-c):s,l=t>0?l:l+(r-c)),{step:o,tickMin:i.sub(new l0(l).mul(o)),tickMax:i.add(new l0(s).mul(o))})},ss=st(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(n,2),[o,l]=si([t,r]);if(o===-1/0||l===1/0){var s=l===1/0?[o,...l7(0,n-1).map(()=>1/0)]:[...l7(0,n-1).map(()=>-1/0),l];return t>r?se(s):s}if(o===l)return so(o,n,i);var{step:c,tickMin:u,tickMax:f}=sl(o,l,a,i,0),d=sn(u,f.add(new l0(.1).mul(c)),c);return t>r?se(d):d}),sc=st(function(e,t){var[r,n]=e,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[a,o]=si([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=sa(new l0(o).sub(a).div(Math.max(t,2)-1),i,0),s=[...sn(new l0(a),new l0(o),l),o];return!1===i&&(s=s.map(e=>Math.round(e))),r>n?se(s):s}),su=e=>e.rootProps.maxBarSize,sf=e=>e.rootProps.barCategoryGap,sd=e=>e.rootProps.stackOffset,sh=e=>e.options.chartName,sp=e=>e.rootProps.syncId,sy=e=>e.rootProps.syncMethod,sv=e=>e.options.eventEmitter,sg={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},sm={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},sb=(e,t)=>e&&t?null!=e&&e.reversed?[t[1],t[0]]:t:void 0,sx={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:sg.angleAxisId,includeHidden:!1,name:void 0,reversed:sg.reversed,scale:sg.scale,tick:sg.tick,tickCount:void 0,ticks:void 0,type:sg.type,unit:void 0},sw={allowDataOverflow:sm.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:sm.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:sm.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:sm.scale,tick:sm.tick,tickCount:sm.tickCount,ticks:void 0,type:sm.type,unit:void 0},sO={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:sg.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:sg.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:sg.scale,tick:sg.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},sj={allowDataOverflow:sm.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:sm.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:sm.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:sm.scale,tick:sm.tick,tickCount:sm.tickCount,ticks:void 0,type:"category",unit:void 0},sP=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?sO:sx,sS=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?sj:sw,sA=e=>e.polarOptions,sE=rx([nu,nf,nx],rW),sM=rx([sA,sE],(e,t)=>{if(null!=e)return M(e.innerRadius,t,0)}),sk=rx([sA,sE],(e,t)=>{if(null!=e)return M(e.outerRadius,t,.8*t)}),sT=rx([sA],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});rx([sP,sT],sb);var s_=rx([sE,sM,sk],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});rx([sS,s_],sb);var sN=rx([nN,sA,sM,sk,nu,nf],(e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:l,startAngle:s,endAngle:c}=t;return{cx:M(o,i,i/2),cy:M(l,a,a/2),innerRadius:r,outerRadius:n,startAngle:s,endAngle:c,clockWise:!1}}}),sC=(e,t)=>t,sD=(e,t,r)=>r;function sI(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function sR(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sI(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sI(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var sL=[0,"auto"],sz={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},sB=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?sz:r},s$={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:sL,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:60},sU=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?s$:r},sK={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},sF=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?sK:r},sW=(e,t,r)=>{switch(t){case"xAxis":return sB(e,r);case"yAxis":return sU(e,r);case"zAxis":return sF(e,r);case"angleAxis":return sP(e,r);case"radiusAxis":return sS(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},sq=(e,t,r)=>{switch(t){case"xAxis":return sB(e,r);case"yAxis":return sU(e,r);case"angleAxis":return sP(e,r);case"radiusAxis":return sS(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},sV=e=>e.graphicalItems.countOfBars>0;function sH(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var sZ=e=>e.graphicalItems.cartesianItems,sY=rx([sC,sD],sH),sG=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),sX=rx([sZ,sW,sY],sG),sJ=e=>e.filter(e=>void 0===e.stackId),sQ=rx([sX],sJ),s0=e=>e.map(e=>e.data).filter(Boolean).flat(1),s1=rx([sX],s0),s2=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},s5=rx([s1,ly],s2),s3=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:rJ(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:rJ(e,t)}))):e.map(e=>({value:e})),s6=rx([s5,sW,sX],s3);function s4(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function s7(e){return e.filter(e=>S(e)||e instanceof Date).map(Number).filter(e=>!1===O(e))}var s8=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,i]=t;return[n,{stackedData:r7(e,i.map(e=>e.dataKey),r),graphicalItems:i}]})),s9=rx([s5,sX,sd],s8),ce=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=nr(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},ct=rx([s9,lh,sC],ce),cr=(e,t,r,n)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var i,a,o=null===(i=r.errorBars)||void 0===i?void 0:i.filter(e=>s4(n,e)),l=rJ(e,null!==(a=t.dataKey)&&void 0!==a?a:r.dataKey);return{value:l,errorDomain:function(e,t,r){return!r||"number"!=typeof t||O(t)||!r.length?[]:s7(r.flatMap(r=>{var n,i,a=rJ(e,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,lv(n)&&lv(i))return[t-n,t+i]}))}(e,l,o)}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:rJ(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),cn=rx(s5,sW,sQ,sC,cr);function ci(e){var{value:t}=e;if(S(t)||t instanceof Date)return t}var ca=e=>{var t=s7(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]},co=(e,t,r)=>{var n=e.map(ci).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&k(n))?nI()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))},cl=e=>{var t;if(null==e||!("domain"in e))return sL;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=s7(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!==(t=null==e?void 0:e.domain)&&void 0!==t?t:sL},cs=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},cc=e=>e.referenceElements.dots,cu=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),cf=rx([cc,sC,sD],cu),cd=e=>e.referenceElements.areas,ch=rx([cd,sC,sD],cu),cp=e=>e.referenceElements.lines,cy=rx([cp,sC,sD],cu),cv=(e,t)=>{var r=s7(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},cg=rx(cf,sC,cv),cm=(e,t)=>{var r=s7(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},cb=rx([ch,sC],cm),cx=(e,t)=>{var r=s7(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},cw=rx(cy,sC,cx),cO=rx(cg,cw,cb,(e,t,r)=>cs(e,r,t)),cj=rx([sW],cl),cP=(e,t,r,n,i)=>{var a=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if(lv(i))r=i;else if("function"==typeof i)return;if(lv(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(lm(o))return o}}(t,e.allowDataOverflow);return null!=a?a:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(lm(n))return lb(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,a,[o,l]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if(P(o))i=o;else if("function"==typeof o)try{null!=t&&(i=o(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof o&&nn.test(o)){var s=nn.exec(o);if(null==s||null==t)i=void 0;else{var c=+s[1];i=t[0]-c}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(a=Math.max(...t));else if(P(l))a=l;else if("function"==typeof l)try{null!=t&&(a=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&ni.test(l)){var u=ni.exec(l);if(null==u||null==t)a=void 0;else{var f=+u[1];a=t[1]+f}}else a=null==t?void 0:t[1];var d=[i,a];if(lm(d))return null==t?d:lb(d,t,r)}}}(t,cs(r,i,ca(n)),e.allowDataOverflow)},cS=rx([sW,cj,ct,cn,cO],cP),cA=[0,1],cE=(e,t,r,n,i,a,o)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:l,type:s}=e,c=r1(t,a);return c&&null==l?nI()(0,r.length):"category"===s?co(n,e,c):"expand"===i?cA:o}},cM=rx([sW,nN,s5,s6,sd,sC,cS],cE),ck=(e,t,r,n,a)=>{if(null!=e){var{scale:o,type:l}=e;if("auto"===o)return"radial"===t&&"radiusAxis"===a?"band":"radial"===t&&"angleAxis"===a?"linear":"category"===l&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":"category"===l?"band":"linear";if("string"==typeof o){var s="scale".concat(N(o));return s in i?s:"point"}}},cT=rx([sW,nN,sV,sh,sC],ck);function c_(e,t,r,n){if(null!=r&&null!=n){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(n);var a=function(e){if(null!=e){if(e in i)return i[e]();var t="scale".concat(N(e));if(t in i)return i[t]()}}(t);if(null!=a){var o=a.domain(r).range(n);return r3(o),o}}}var cN=(e,t,r)=>{var n=cl(t);return"auto"!==r&&"linear"!==r?void 0:null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&lm(e)?ss(e,t.tickCount,t.allowDecimals):null!=t&&t.tickCount&&"number"===t.type&&lm(e)?sc(e,t.tickCount,t.allowDecimals):void 0},cC=rx([cM,sq,cT],cN),cD=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&lm(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,cI=rx([sW,cM,cC,sC],cD),cR=rx(s6,sW,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(s7(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++)r=Math.min(r,n[a+1]-n[a]);return r/i}}),cL=rx(cR,nN,sf,nx,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!lv(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=M(r,e*a),l=e*a/2;return l-o-(l-o)/a*o}return 0}),cz=rx(sB,(e,t)=>{var r=sB(e,t);return null==r||"string"!=typeof r.padding?0:cL(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!==(r=i.left)&&void 0!==r?r:0)+t,right:(null!==(n=i.right)&&void 0!==n?n:0)+t}}),cB=rx(sU,(e,t)=>{var r=sU(e,t);return null==r||"string"!=typeof r.padding?0:cL(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!==(r=i.top)&&void 0!==r?r:0)+t,bottom:(null!==(n=i.bottom)&&void 0!==n?n:0)+t}}),c$=rx([nx,cz,nA,nS,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),cU=rx([nx,nN,cB,nA,nS,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),cK=(e,t,r,n)=>{var i;switch(t){case"xAxis":return c$(e,r,n);case"yAxis":return cU(e,r,n);case"zAxis":return null===(i=sF(e,r))||void 0===i?void 0:i.range;case"angleAxis":return sT(e);case"radiusAxis":return s_(e,r);default:return}},cF=rx([sW,cK],sb),cW=rx([sW,cT,cI,cF],c_);function cq(e,t){return e.id<t.id?-1:e.id>t.id?1:0}rx(sX,sC,(e,t)=>e.flatMap(e=>{var t;return null!==(t=e.errorBars)&&void 0!==t?t:[]}).filter(e=>s4(t,e)));var cV=(e,t)=>t,cH=(e,t,r)=>r,cZ=rx(np,cV,cH,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(cq)),cY=rx(ny,cV,cH,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(cq)),cG=(e,t)=>({width:e.width,height:t.height}),cX=(e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height}),cJ=rx(nx,sB,cG),cQ=(e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}},c0=(e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}},c1=rx(nf,nx,cZ,cV,cH,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=cG(t,r);null==a&&(a=cQ(t,n,e));var s="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(s)*l.height,a+=(s?-1:1)*l.height}),o}),c2=rx(nu,nx,cY,cV,cH,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=cX(t,r);null==a&&(a=c0(t,n,e));var s="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(s)*l.width,a+=(s?-1:1)*l.width}),o}),c5=(e,t)=>{var r=nx(e),n=sB(e,t);if(null!=n){var i=c1(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},c3=(e,t)=>{var r=nx(e),n=sU(e,t);if(null!=n){var i=c2(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},c6=rx(nx,sU,(e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height})),c4=(e,t,r)=>{switch(t){case"xAxis":return cJ(e,r).width;case"yAxis":return c6(e,r).height;default:return}},c7=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=r1(e,n),s=t.map(e=>e.value);if(o&&l&&"category"===a&&i&&k(s))return s}},c8=rx([nN,s6,sW,sC],c7),c9=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;if(r1(e,n)&&("number"===i||"auto"!==a))return t.map(e=>e.value)}},ue=rx([nN,s6,sq,sC],c9),ut=rx([nN,(e,t,r)=>{switch(t){case"xAxis":return sB(e,r);case"yAxis":return sU(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},cT,cW,c8,ue,cK,cC,sC],(e,t,r,n,i,a,o,l,s)=>{if(null==t)return null;var c=r1(e,s);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:s,categoricalDomain:a,duplicateDomain:i,isCategorical:c,niceTicks:l,range:o,realScaleType:r,scale:n}}),ur=rx([nN,sq,cT,cW,cC,cK,c8,ue,sC],(e,t,r,n,i,a,o,l,s)=>{if(null!=t&&null!=n){var c=r1(e,s),{type:u,ticks:f,tickCount:d}=t,h="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,p="category"===u&&n.bandwidth?n.bandwidth()/h:0;p="angleAxis"===s&&null!=a&&a.length>=2?2*w(a[0]-a[1])*p:p;var y=f||i;return y?y.map((e,t)=>({index:t,coordinate:n(o?o.indexOf(e):e)+p,value:e,offset:p})).filter(e=>!O(e.coordinate)):c&&l?l.map((e,t)=>({coordinate:n(e)+p,value:e,index:t,offset:p})):n.ticks?n.ticks(d).map(e=>({coordinate:n(e)+p,value:e,offset:p})):n.domain().map((e,t)=>({coordinate:n(e)+p,value:o?o[e]:e,index:t,offset:p}))}}),un=rx([nN,sq,cW,cK,c8,ue,sC],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=r1(e,o),{tickCount:s}=t,c=0;return(c="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*w(n[0]-n[1])*c:c,l&&a)?a.map((e,t)=>({coordinate:r(e)+c,value:e,index:t,offset:c})):r.ticks?r.ticks(s).map(e=>({coordinate:r(e)+c,value:e,offset:c})):r.domain().map((e,t)=>({coordinate:r(e)+c,value:i?i[e]:e,index:t,offset:c}))}}),ui=rx(sW,cW,(e,t)=>{if(null!=e&&null!=t)return sR(sR({},e),{},{scale:t})}),ua=rx([sW,cT,cM,cF],c_);rx((e,t,r)=>sF(e,r),ua,(e,t)=>{if(null!=e&&null!=t)return sR(sR({},e),{},{scale:t})});var uo=rx([nN,np,ny],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}}),ul=e=>e.options.defaultTooltipEventType,us=e=>e.options.validateTooltipEventTypes;function uc(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function uu(e,t){return uc(t,ul(e),us(e))}var uf=(e,t)=>{var r,n=Number(t);if(!O(n)&&null!=t)return n>=0?null==e||null===(r=e[n])||void 0===r?void 0:r.value:void 0};function ud(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function uh(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ud(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ud(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var up=(e,t,r,n)=>{if(null==t)return tK;var i=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==i)return tK;if(i.active)return i;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var a=!0===e.settings.active;if(null!=i.index){if(a)return uh(uh({},i),{},{active:!0})}else if(null!=n)return{active:!0,coordinate:void 0,dataKey:void 0,index:n};return uh(uh({},tK),{},{coordinate:i.coordinate})},uy=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var n=Number(r);if(!lv(n))return r;var i=Infinity;return t.length>0&&(i=t.length-1),String(Math.max(0,Math.min(n,i)))},uv=(e,t,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var s=o[0],c=null==s?void 0:l(s.positions,a);if(null!=c)return c;var u=null==i?void 0:i[Number(a)];if(u)return"horizontal"===r?{x:u.coordinate,y:(n.top+t)/2}:{x:(n.left+e)/2,y:u.coordinate}}},ug=(e,t,r,n)=>{var i;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null===(t=e.settings)||void 0===t?void 0:t.dataKey)===i})},um=e=>e.options.tooltipPayloadSearcher,ub=e=>e.tooltip;function ux(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function uw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ux(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ux(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var uO=(e,t,r,n,i,a,o)=>{if(null!=t&&null!=a){var{chartData:l,computedData:s,dataStartIndex:c,dataEndIndex:u}=r;return e.reduce((e,r)=>{var f,d,h,{dataDefinedOnItem:p,settings:y}=r,v=function(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}(null!=p?p:l,c,u),g=null!==(f=null==y?void 0:y.dataKey)&&void 0!==f?f:null==n?void 0:n.dataKey,m=null==y?void 0:y.nameKey;return Array.isArray(d=null!=n&&n.dataKey&&Array.isArray(v)&&!Array.isArray(v[0])&&"axis"===o?function(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):x()(e,t))===r)}(v,n.dataKey,i):a(v,t,s,m))?d.forEach(t=>{var r=uw(uw({},y),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push(no({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:rJ(t.payload,t.dataKey),name:t.name}))}):e.push(no({tooltipEntrySettings:y,dataKey:g,payload:d,value:rJ(d,g),name:null!==(h=rJ(d,m))&&void 0!==h?h:null==y?void 0:y.name})),e},[])}},uj=e=>{var t=nN(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},uP=e=>e.tooltip.settings.axisId,uS=e=>{var t=uj(e),r=uP(e);return sq(e,t,r)},uA=rx([uS,nN,sV,sh,uj],ck),uE=rx([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),uM=rx([uj,uP],sH),uk=rx([uE,uS,uM],sG),uT=rx([uk],s0),u_=rx([uT,lh],s2),uN=rx([u_,uS,uk],s3),uC=rx([uS],cl),uD=rx([u_,uk,sd],s8),uI=rx([uD,lh,uj],ce),uR=rx([uk],sJ),uL=rx([u_,uS,uR,uj],cr),uz=rx([cc,uj,uP],cu),uB=rx([uz,uj],cv),u$=rx([cd,uj,uP],cu),uU=rx([u$,uj],cm),uK=rx([cp,uj,uP],cu),uF=rx([uK,uj],cx),uW=rx([uB,uF,uU],cs),uq=rx([uS,uC,uI,uL,uW],cP),uV=rx([uS,nN,u_,uN,sd,uj,uq],cE),uH=rx([uV,uS,uA],cN),uZ=rx([uS,uV,uH,uj],cD),uY=e=>{var t=uj(e),r=uP(e);return cK(e,t,r,!1)},uG=rx([uS,uY],sb),uX=rx([uS,uA,uZ,uG],c_),uJ=rx([nN,uN,uS,uj],c7),uQ=rx([nN,uN,uS,uj],c9),u0=rx([nN,uS,uA,uX,uY,uJ,uQ,uj],(e,t,r,n,i,a,o,l)=>{if(t){var{type:s}=t,c=r1(e,l);if(n){var u="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,f="category"===s&&n.bandwidth?n.bandwidth()/u:0;return(f="angleAxis"===l&&null!=i&&(null==i?void 0:i.length)>=2?2*w(i[0]-i[1])*f:f,c&&o)?o.map((e,t)=>({coordinate:n(e)+f,value:e,index:t,offset:f})):n.domain().map((e,t)=>({coordinate:n(e)+f,value:a?a[e]:e,index:t,offset:f}))}}}),u1=rx([ul,us,e=>e.tooltip.settings],(e,t,r)=>uc(r.shared,e,t)),u2=e=>e.tooltip.settings.trigger,u5=e=>e.tooltip.settings.defaultIndex,u3=rx([ub,u1,u2,u5],up),u6=rx([u3,u_],uy),u4=rx([u0,u6],uf),u7=rx([u3],e=>{if(e)return e.dataKey}),u8=rx([ub,u1,u2,u5],ug),u9=rx([nu,nf,nN,nx,u0,u5,u8,um],uv),fe=rx([u3,u9],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),ft=rx([u3],e=>e.active),fr=rx([u8,u6,lh,uS,u4,um,u1],uO);rx([fr],e=>{if(null!=e)return Array.from(new Set(e.map(e=>e.payload).filter(e=>null!=e)))});var fn=()=>rk(sh),fi=(e,t)=>t,fa=(e,t,r)=>r,fo=(e,t,r,n)=>n,fl=rx(u0,e=>r_()(e,e=>e.coordinate)),fs=rx([ub,fi,fa,fo],up),fc=rx([fs,u_],uy),fu=(e,t,r)=>{if(null!=t){var n=ub(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},ff=rx([ub,fi,fa,fo],ug),fd=rx([nu,nf,nN,nx,u0,fo,ff,um],uv),fh=rx([fs,fd],(e,t)=>{var r;return null!==(r=e.coordinate)&&void 0!==r?r:t}),fp=rx(u0,fc,uf),fy=rx([ff,fc,lh,uS,fp,um,fi],uO),fv=rx([fs],e=>({isActive:e.active,activeIndex:e.index})),fg=rx([(e,t)=>t,nN,sN,uj,uG,u0,fl,nx],(e,t,r,n,i,a,o,l)=>{if(e&&t&&n&&i&&a){var s=function(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?rY({x:e,y:t},n):null}(e.chartX,e.chartY,t,r,l);if(s){var c=rQ(nc(s,t),o,a,n,i),u=ns(t,a,c,s);return{activeIndex:String(c),activeCoordinate:u}}}}),fm=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}},fb=eR("mouseClick"),fx=tO();fx.startListening({actionCreator:fb,effect:(e,t)=>{var r=e.payload,n=fg(t.getState(),fm(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch(tJ({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var fw=eR("mouseMove"),fO=tO();function fj(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}function fP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fP(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fP(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}fO.startListening({actionCreator:fw,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=uu(n,n.tooltip.settings.shared),a=fg(n,fm(r));"axis"===i&&((null==a?void 0:a.activeIndex)!=null?t.dispatch(tX({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate})):t.dispatch(tY()))}});var fA=eX({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=t.payload},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=t.payload},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=t.payload},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=fS(fS({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:fE,removeXAxis:fM,addYAxis:fk,removeYAxis:fT,addZAxis:f_,removeZAxis:fN,updateYAxisWidth:fC}=fA.actions,fD=fA.reducer,fI=eX({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push(t.payload)},replaceCartesianGraphicalItem(e,t){var{prev:r,next:n}=t.payload,i=e_(e).cartesianItems.indexOf(r);i>-1&&(e.cartesianItems[i]=n)},removeCartesianGraphicalItem(e,t){var r=e_(e).cartesianItems.indexOf(t.payload);r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push(t.payload)},removePolarGraphicalItem(e,t){var r=e_(e).polarItems.indexOf(t.payload);r>-1&&e.polarItems.splice(r,1)}}}),{addBar:fR,removeBar:fL,addCartesianGraphicalItem:fz,replaceCartesianGraphicalItem:fB,removeCartesianGraphicalItem:f$,addPolarGraphicalItem:fU,removePolarGraphicalItem:fK}=fI.actions,fF=fI.reducer,fW=eX({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=e_(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=e_(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=e_(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:fq,removeDot:fV,addArea:fH,removeArea:fZ,addLine:fY,removeLine:fG}=fW.actions,fX=fW.reducer,fJ={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},fQ=eX({name:"brush",initialState:fJ,reducers:{setBrushSettings:(e,t)=>null==t.payload?fJ:t.payload}}),{setBrushSettings:f0}=fQ.actions,f1=fQ.reducer,f2=eX({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push(t.payload)},removeLegendPayload(e,t){var r=e_(e).payload.indexOf(t.payload);r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:f5,setLegendSettings:f3,addLegendPayload:f6,removeLegendPayload:f4}=f2.actions,f7=f2.reducer,f8={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},f9=eX({name:"rootProps",initialState:f8,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!==(r=t.payload.barGap)&&void 0!==r?r:f8.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),de=f9.reducer,{updateOptions:dt}=f9.actions,dr=eX({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=t.payload},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=t.payload},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:dn,removeRadiusAxis:di,addAngleAxis:da,removeAngleAxis:dl}=dr.actions,ds=dr.reducer,dc=eX({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:du}=dc.actions,df=dc.reducer,dd=eR("keyDown"),dh=eR("focus"),dp=tO();dp.startListening({actionCreator:dd,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,i=e.payload;if("ArrowRight"===i||"ArrowLeft"===i||"Enter"===i){var a=Number(uy(n,u_(r))),o=u0(r);if("Enter"===i){var l=fd(r,"axis","hover",String(n.index));t.dispatch(t0({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:l}));return}var s=a+("ArrowRight"===i?1:-1)*("left-to-right"===uo(r)?1:-1);if(null!=o&&!(s>=o.length)&&!(s<0)){var c=fd(r,"axis","hover",String(s));t.dispatch(t0({active:!0,activeIndex:s.toString(),activeDataKey:void 0,activeCoordinate:c}))}}}}}),dp.startListening({actionCreator:dh,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var i=fd(r,"axis","hover",String("0"));t.dispatch(t0({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:i}))}}}});var dy=eR("externalEvent"),dv=tO();dv.startListening({actionCreator:dy,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:fe(r),activeDataKey:u7(r),activeIndex:u6(r),activeLabel:u4(r),activeTooltipIndex:u6(r),isTooltipActive:ft(r)};e.payload.handler(n,e.payload.reactEvent)}}});var dg=rx([ub],e=>e.tooltipItemPayloads),dm=rx([dg,um,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}}),db=eR("touchMove"),dx=tO();dx.startListening({actionCreator:db,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=uu(n,n.tooltip.settings.shared);if("axis"===i){var a=fg(n,fm({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==a?void 0:a.activeIndex)!=null&&t.dispatch(tX({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate}))}else if("item"===i){var o,l=r.touches[0],s=document.elementFromPoint(l.clientX,l.clientY);if(!s||!s.getAttribute)return;var c=s.getAttribute(nv),u=null!==(o=s.getAttribute(ng))&&void 0!==o?o:void 0,f=dm(t.getState(),c,u);t.dispatch(tH({activeDataKey:u,activeIndex:c,activeCoordinate:f}))}}});var dw=K({brush:f1,cartesianAxis:fD,chartData:t4,graphicalItems:fF,layout:rr,legend:f7,options:tE,polarAxis:ds,polarOptions:df,referenceElements:fX,rootProps:de,tooltip:t1}),dO=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return function(e){let t,r;let n=eU(),{reducer:i,middleware:a,devTools:o=!0,duplicateMiddlewareCheck:l=!0,preloadedState:s,enhancers:c}=e||{};if("function"==typeof i)t=i;else if(U(i))t=K(i);else throw Error(tP(1));r="function"==typeof a?a(n):n();let u=F;o&&(u=eD({trace:!1,..."object"==typeof o&&o}));let f=eW(function(...e){return t=>(r,n)=>{let i=t(r,n),a=()=>{throw Error(L(15))},o={getState:i.getState,dispatch:(e,...t)=>a(e,...t)};return a=F(...e.map(e=>e(o)))(i.dispatch),{...i,dispatch:a}}}(...r));return function e(t,r,n){if("function"!=typeof t)throw Error(L(2));if("function"==typeof r&&"function"==typeof n||"function"==typeof n&&"function"==typeof arguments[3])throw Error(L(0));if("function"==typeof r&&void 0===n&&(n=r,r=void 0),void 0!==n){if("function"!=typeof n)throw Error(L(1));return n(e)(t,r)}let i=t,a=r,o=new Map,l=o,s=0,c=!1;function u(){l===o&&(l=new Map,o.forEach((e,t)=>{l.set(t,e)}))}function f(){if(c)throw Error(L(3));return a}function d(e){if("function"!=typeof e)throw Error(L(4));if(c)throw Error(L(5));let t=!0;u();let r=s++;return l.set(r,e),function(){if(t){if(c)throw Error(L(6));t=!1,u(),l.delete(r),o=null}}}function h(e){if(!U(e))throw Error(L(7));if(void 0===e.type)throw Error(L(8));if("string"!=typeof e.type)throw Error(L(17));if(c)throw Error(L(9));try{c=!0,a=i(a,e)}finally{c=!1}return(o=l).forEach(e=>{e()}),e}return h({type:$.INIT}),{dispatch:h,subscribe:d,getState:f,replaceReducer:function(e){if("function"!=typeof e)throw Error(L(10));i=e,h({type:$.REPLACE})},[z]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(L(11));function t(){e.next&&e.next(f())}return t(),{unsubscribe:d(t)}},[z](){return this}}}}}(t,s,u(..."function"==typeof c?c(f):f()))}({reducer:dw,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([fx.middleware,fO.middleware,dp.middleware,dv.middleware,dx.middleware]),devTools:{serialize:{replacer:fj},name:"recharts-".concat(t)}})};function dj(e){var{preloadedState:t,children:r,reduxStoreName:n}=e,i=nP(),a=(0,o.useRef)(null);return i?r:(null==a.current&&(a.current=dO(t,n)),o.createElement(tU,{context:rj,store:a.current},r))}var dP=e=>{var{chartData:t}=e,r=rS(),n=nP();return(0,o.useEffect)(()=>n?()=>{}:(r(t5(t)),()=>{r(t5(void 0))}),[t,r,n]),null};function dS(e){var{layout:t,width:r,height:n,margin:i}=e;return rS(),nP(),null}function dA(e){return rS(),null}var dE=r(16777),dM=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],dk=["points","pathLength"],dT={svg:["viewBox","children"],polygon:dk,polyline:dk},d_=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],dN=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,o.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var n={};return Object.keys(r).forEach(e=>{d_.includes(e)&&(n[e]=t||(t=>r[e](r,t)))}),n},dC=(e,t,r)=>n=>(e(t,r,n),null),dD=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(i=>{var a=e[i];d_.includes(i)&&"function"==typeof a&&(n||(n={}),n[i]=dC(a,t,r))}),n},dI=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",dR=null,dL=null,dz=e=>{if(e===dR&&Array.isArray(dL))return dL;var t=[];return o.Children.forEach(e,e=>{_(e)||((0,dE.isFragment)(e)?t=t.concat(dz(e.props.children)):t.push(e))}),dL=t,dR=e,t};function dB(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>dI(e)):[dI(t)],dz(e).forEach(e=>{var t=x()(e,"type.displayName")||x()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var d$=(e,t,r,n)=>{var i,a=null!==(i=n&&(null==dT?void 0:dT[n]))&&void 0!==i?i:[];return t.startsWith("data-")||"function"!=typeof e&&(n&&a.includes(t)||dM.includes(t))||r&&d_.includes(t)},dU=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,o.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var a;d$(null===(a=n)||void 0===a?void 0:a[e],e,t,r)&&(i[e]=n[e])}),i},dK=()=>rk(e=>e.rootProps.accessibilityLayer),dF=["children","width","height","viewBox","className","style","title","desc"];function dW(){return(dW=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var dq=(0,o.forwardRef)((e,t)=>{var{children:r,width:n,height:i,viewBox:a,className:l,style:s,title:c,desc:u}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,dF),d=a||{width:n,height:i,x:0,y:0},h=(0,v.W)("recharts-surface",l);return o.createElement("svg",dW({},dU(f,!0,"svg"),{className:h,width:n,height:i,style:s,viewBox:"".concat(d.x," ").concat(d.y," ").concat(d.width," ").concat(d.height),ref:t}),o.createElement("title",null,c),o.createElement("desc",null,u),r)}),dV=["children"];function dH(){return(dH=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var dZ={width:"100%",height:"100%"},dY=(0,o.forwardRef)((e,t)=>{var r,n,i=nT(),a=n_(),l=dK();if(!lg(i)||!lg(a))return null;var{children:s,otherAttributes:c,title:u,desc:f}=e;return r="number"==typeof c.tabIndex?c.tabIndex:l?0:void 0,n="string"==typeof c.role?c.role:l?"application":void 0,o.createElement(dq,dH({},c,{title:u,desc:f,role:n,tabIndex:r,width:i,height:a,style:dZ,ref:t}),s)}),dG=e=>{var{children:t}=e,r=rk(nA);if(!r)return null;var{width:n,height:i,y:a,x:l}=r;return o.createElement(dq,{width:n,height:i,x:l,y:a},t)},dX=(0,o.forwardRef)((e,t)=>{var{children:r}=e,n=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,dV);return nP()?o.createElement(dG,null,r):o.createElement(dY,dH({ref:t},n),r)});function dJ(e){return e.tooltip.syncInteraction}var dQ=(0,o.createContext)(null),d0=()=>(0,o.useContext)(dQ),d1=(0,o.createContext)(null);function d2(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var d5=(0,o.forwardRef)((e,t)=>{var{children:r,className:n,height:i,onClick:a,onContextMenu:l,onDoubleClick:s,onMouseDown:c,onMouseEnter:u,onMouseLeave:f,onMouseMove:d,onMouseUp:h,onTouchEnd:p,onTouchMove:y,onTouchStart:g,style:m,width:b}=e,x=rS(),[w,O]=(0,o.useState)(null),[j,P]=(0,o.useState)(null);rS(),rk(sp),rk(sv),rS(),rk(sy),rk(u0),nC(),nE(),rk(e=>e.rootProps.className),rk(sp),rk(sv),rS();var S=function(){rS();var[e,t]=(0,o.useState)(null);return rk(nd),t}(),A=(0,o.useCallback)(e=>{S(e),"function"==typeof t&&t(e),O(e),P(e)},[S,t,O,P]),E=(0,o.useCallback)(e=>{x(fb(e)),x(dy({handler:a,reactEvent:e}))},[x,a]),M=(0,o.useCallback)(e=>{x(fw(e)),x(dy({handler:u,reactEvent:e}))},[x,u]),k=(0,o.useCallback)(e=>{x(tY()),x(dy({handler:f,reactEvent:e}))},[x,f]),T=(0,o.useCallback)(e=>{x(fw(e)),x(dy({handler:d,reactEvent:e}))},[x,d]),_=(0,o.useCallback)(()=>{x(dh())},[x]),N=(0,o.useCallback)(e=>{x(dd(e.key))},[x]),C=(0,o.useCallback)(e=>{x(dy({handler:l,reactEvent:e}))},[x,l]),D=(0,o.useCallback)(e=>{x(dy({handler:s,reactEvent:e}))},[x,s]),I=(0,o.useCallback)(e=>{x(dy({handler:c,reactEvent:e}))},[x,c]),R=(0,o.useCallback)(e=>{x(dy({handler:h,reactEvent:e}))},[x,h]),L=(0,o.useCallback)(e=>{x(dy({handler:g,reactEvent:e}))},[x,g]),z=(0,o.useCallback)(e=>{x(db(e)),x(dy({handler:y,reactEvent:e}))},[x,y]),B=(0,o.useCallback)(e=>{x(dy({handler:p,reactEvent:e}))},[x,p]);return o.createElement(dQ.Provider,{value:w},o.createElement(d1.Provider,{value:j},o.createElement("div",{className:(0,v.W)("recharts-wrapper",n),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d2(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d2(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:b,height:i},m),onClick:E,onContextMenu:C,onDoubleClick:D,onFocus:_,onKeyDown:N,onMouseDown:I,onMouseEnter:M,onMouseLeave:k,onMouseMove:T,onMouseUp:R,onTouchEnd:B,onTouchMove:z,onTouchStart:L,ref:A},r)))}),d3=rx([nx],e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}}),d6=rx([d3,nu,nf],(e,t,r)=>{if(e&&null!=t&&null!=r)return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}}),d4=e=>{var t=nP();return rk(r=>ui(r,"xAxis",e,t))},d7=e=>{var t=nP();return rk(r=>ui(r,"yAxis",e,t))},d8=()=>rk(d6),d9=(0,o.createContext)(void 0),he=e=>{var{children:t}=e,[r]=(0,o.useState)("".concat(E("recharts"),"-clip")),n=d8();if(null==n)return null;var{x:i,y:a,width:l,height:s}=n;return o.createElement(d9.Provider,{value:r},o.createElement("defs",null,o.createElement("clipPath",{id:r},o.createElement("rect",{x:i,y:a,height:s,width:l}))),t)},ht=["children","className","width","height","style","compact","title","desc"],hr=(0,o.forwardRef)((e,t)=>{var{children:r,className:n,width:i,height:a,style:l,compact:s,title:c,desc:u}=e,f=dU(function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,ht),!1);return s?o.createElement(dX,{otherAttributes:f,title:c,desc:u},r):o.createElement(d5,{className:n,style:l,width:i,height:a,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},o.createElement(dX,{otherAttributes:f,title:c,desc:u,ref:t},o.createElement(he,null,r)))});function hn(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hi(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hn(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hn(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}var ha=["width","height"];function ho(){return(ho=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var hl={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},hs=(0,o.forwardRef)(function(e,t){var r,n=hi(e.categoricalChartProps,hl),{width:i,height:a}=n,l=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(n,ha);if(!lg(i)||!lg(a))return null;var{chartName:s,defaultTooltipEventType:c,validateTooltipEventTypes:u,tooltipPayloadSearcher:f,categoricalChartProps:d}=e;return o.createElement(dj,{preloadedState:{options:{chartName:s,defaultTooltipEventType:c,validateTooltipEventTypes:u,tooltipPayloadSearcher:f,eventEmitter:void 0}},reduxStoreName:null!==(r=d.id)&&void 0!==r?r:s},o.createElement(dP,{chartData:d.data}),o.createElement(dS,{width:i,height:a,layout:n.layout,margin:n.margin}),o.createElement(dA,{accessibilityLayer:n.accessibilityLayer,barCategoryGap:n.barCategoryGap,maxBarSize:n.maxBarSize,stackOffset:n.stackOffset,barGap:n.barGap,barSize:n.barSize,syncId:n.syncId,syncMethod:n.syncMethod,className:n.className}),o.createElement(hr,ho({},l,{width:i,height:a,ref:t})))}),hc=["axis","item"],hu=(0,o.forwardRef)((e,t)=>o.createElement(hs,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:hc,tooltipPayloadSearcher:tS,categoricalChartProps:e,ref:t})),hf={isSsr:!0};function hd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hh(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hd(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hd(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var hp={widthCache:{},cacheCount:0},hy={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},hv="recharts_measurement_span",hg=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||hf.isSsr)return{width:0,height:0};var n=(Object.keys(t=hh({},r)).forEach(e=>{t[e]||delete t[e]}),t),i=JSON.stringify({text:e,copyStyle:n});if(hp.widthCache[i])return hp.widthCache[i];try{var a=document.getElementById(hv);a||((a=document.createElement("span")).setAttribute("id",hv),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=hh(hh({},hy),n);Object.assign(a.style,o),a.textContent="".concat(e);var l=a.getBoundingClientRect(),s={width:l.width,height:l.height};return hp.widthCache[i]=s,++hp.cacheCount>2e3&&(hp.cacheCount=0,hp.widthCache={}),s}catch(e){return{width:0,height:0}}};class hm{static create(e){return new hm(e)}constructor(e){this.scale=e}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}(function(e,t,r){var n;(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:1e-4,enumerable:!0,configurable:!0,writable:!0}):e[t]=r})(hm,"EPS",1e-4);var hb=function(e){var{width:t,height:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,a=Math.atan(r/t);return Math.abs(i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i))};function hx(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t){if(void 0!==r&&!0!==r(e[i]))return;n.push(e[i])}return n}function hw(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function hO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hO(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hP(e,t,r){var n,{tick:i,ticks:a,viewBox:o,minTickGap:l,orientation:s,interval:c,tickFormatter:u,unit:f,angle:d}=e;if(!a||!a.length||!i)return[];if(P(c)||hf.isSsr)return null!==(n=hx(a,(P(c)?c:0)+1))&&void 0!==n?n:[];var h="top"===s||"bottom"===s?"width":"height",p=f&&"width"===h?hg(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},y=(e,n)=>{var i,a="function"==typeof u?u(e.value,n):e.value;return"width"===h?hb({width:(i=hg(a,{fontSize:t,letterSpacing:r})).width+p.width,height:i.height+p.height},d):hg(a,{fontSize:t,letterSpacing:r})[h]},v=a.length>=2?w(a[1].coordinate-a[0].coordinate):1,g=function(e,t,r){var n="width"===r,{x:i,y:a,width:o,height:l}=e;return 1===t?{start:n?i:a,end:n?i+o:a+l}:{start:n?i+o:a+l,end:n?i:a}}(o,v,h);return"equidistantPreserveStart"===c?function(e,t,r,n,i){for(var a,o=(n||[]).slice(),{start:l,end:s}=t,c=0,u=1,f=l;u<=o.length;)if(a=function(){var t,a=null==n?void 0:n[c];if(void 0===a)return{v:hx(n,u)};var o=c,d=()=>(void 0===t&&(t=r(a,o)),t),h=a.coordinate,p=0===c||hw(e,h,d,f,s);p||(c=0,f=l,u+=1),p&&(f=h+e*(d()/2+i),c+=u)}())return a.v;return[]}(v,g,y,a,l):("preserveStart"===c||"preserveStartEnd"===c?function(e,t,r,n,i,a){var o=(n||[]).slice(),l=o.length,{start:s,end:c}=t;if(a){var u=n[l-1],f=r(u,l-1),d=e*(u.coordinate+e*f/2-c);o[l-1]=u=hj(hj({},u),{},{tickCoord:d>0?u.coordinate-d*e:u.coordinate}),hw(e,u.tickCoord,()=>f,s,c)&&(c=u.tickCoord-e*(f/2+i),o[l-1]=hj(hj({},u),{},{isShow:!0}))}for(var h=a?l-1:l,p=function(t){var n,a=o[t],l=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var u=e*(a.coordinate-e*l()/2-s);o[t]=a=hj(hj({},a),{},{tickCoord:u<0?a.coordinate-u*e:a.coordinate})}else o[t]=a=hj(hj({},a),{},{tickCoord:a.coordinate});hw(e,a.tickCoord,l,s,c)&&(s=a.tickCoord+e*(l()/2+i),o[t]=hj(hj({},a),{},{isShow:!0}))},y=0;y<h;y++)p(y);return o}(v,g,y,a,l,"preserveStartEnd"===c):function(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:l}=t,{end:s}=t,c=function(t){var n,c=a[t],u=()=>(void 0===n&&(n=r(c,t)),n);if(t===o-1){var f=e*(c.coordinate+e*u()/2-s);a[t]=c=hj(hj({},c),{},{tickCoord:f>0?c.coordinate-f*e:c.coordinate})}else a[t]=c=hj(hj({},c),{},{tickCoord:c.coordinate});hw(e,c.tickCoord,u,l,s)&&(s=c.tickCoord-e*(u()/2+i),a[t]=hj(hj({},c),{},{isShow:!0}))},u=o-1;u>=0;u--)c(u);return a}(v,g,y,a,l)).filter(e=>e.isShow)}function hS(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}var hA=["children","className"];function hE(){return(hE=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var hM=o.forwardRef((e,t)=>{var{children:r,className:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,hA),a=(0,v.W)("recharts-layer",n);return o.createElement("g",hE({className:a},dU(i,!0),{ref:t}),r)}),hk=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,hT=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,h_=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,hN=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,hC={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},hD=Object.keys(hC);class hI{static parse(e){var t,[,r,n]=null!==(t=hN.exec(e))&&void 0!==t?t:[];return new hI(parseFloat(r),null!=n?n:"")}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,O(e)&&(this.unit=""),""===t||h_.test(t)||(this.num=NaN,this.unit=""),hD.includes(t)&&(this.num=e*hC[t],this.unit="px")}add(e){return this.unit!==e.unit?new hI(NaN,""):new hI(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new hI(NaN,""):new hI(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new hI(NaN,""):new hI(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new hI(NaN,""):new hI(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return O(this.num)}}function hR(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!==(r=hk.exec(t))&&void 0!==r?r:[],o=hI.parse(null!=n?n:""),l=hI.parse(null!=a?a:""),s="*"===i?o.multiply(l):o.divide(l);if(s.isNaN())return"NaN";t=t.replace(hk,s.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var c,[,u,f,d]=null!==(c=hT.exec(t))&&void 0!==c?c:[],h=hI.parse(null!=u?u:""),p=hI.parse(null!=d?d:""),y="+"===f?h.add(p):h.subtract(p);if(y.isNaN())return"NaN";t=t.replace(hT,y.toString())}return t}var hL=/\(([^()]*)\)/;function hz(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=hL.exec(r));){var[,n]=t;r=r.replace(hL,hR(n))}return r}(t),t=hR(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var hB=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],h$=["dx","dy","angle","className","breakAll"];function hU(){return(hU=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function hK(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var hF=/[ \f\n\r\t\v\u2028\u2029]+/,hW=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];_(t)||(i=r?t.toString().split(""):t.toString().split(hF));var a=i.map(e=>({word:e,width:hg(e,n).width})),o=r?0:hg("\xa0",n).width;return{wordsWithComputedWidth:a,spaceWidth:o}}catch(e){return null}},hq=(e,t,r,n,i)=>{var a,{maxLines:o,children:l,style:s,breakAll:c}=e,u=P(o),f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:a,width:o}=t,l=e[e.length-1];return l&&(null==n||i||l.width+o+r<Number(n))?(l.words.push(a),l.width+=o+r):e.push({words:[a],width:o}),e},[])},d=f(t),h=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!u||i||!(d.length>o||h(d).width>Number(n)))return d;for(var p=e=>{var t=f(hW({breakAll:c,style:s,children:l.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>o||h(t).width>Number(n),t]},y=0,v=l.length-1,g=0;y<=v&&g<=l.length-1;){var m=Math.floor((y+v)/2),[b,x]=p(m-1),[w]=p(m);if(b||w||(y=m+1),b&&w&&(v=m-1),!b&&w){a=x;break}g++}return a||d},hV=e=>[{words:_(e)?[]:e.toString().split(hF)}],hH=e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:o}=e;if((t||r)&&!hf.isSsr){var l=hW({breakAll:a,children:n,style:i});if(!l)return hV(n);var{wordsWithComputedWidth:s,spaceWidth:c}=l;return hq({breakAll:a,children:n,maxLines:o,style:i},s,c,t,r)}return hV(n)},hZ="#808080",hY=(0,o.forwardRef)((e,t)=>{var r,{x:n=0,y:i=0,lineHeight:a="1em",capHeight:l="0.71em",scaleToFit:s=!1,textAnchor:c="start",verticalAnchor:u="end",fill:f=hZ}=e,d=hK(e,hB),h=(0,o.useMemo)(()=>hH({breakAll:d.breakAll,children:d.children,maxLines:d.maxLines,scaleToFit:s,style:d.style,width:d.width}),[d.breakAll,d.children,d.maxLines,s,d.style,d.width]),{dx:p,dy:y,angle:g,className:m,breakAll:b}=d,x=hK(d,h$);if(!S(n)||!S(i))return null;var w=n+(P(p)?p:0),O=i+(P(y)?y:0);switch(u){case"start":r=hz("calc(".concat(l,")"));break;case"middle":r=hz("calc(".concat((h.length-1)/2," * -").concat(a," + (").concat(l," / 2))"));break;default:r=hz("calc(".concat(h.length-1," * -").concat(a,")"))}var j=[];if(s){var A=h[0].width,{width:E}=d;j.push("scale(".concat(P(E)?E/A:1,")"))}return g&&j.push("rotate(".concat(g,", ").concat(w,", ").concat(O,")")),j.length&&(x.transform=j.join(" ")),o.createElement("text",hU({},dU(x,!0),{ref:t,x:w,y:O,className:(0,v.W)("recharts-text",m),textAnchor:c,fill:f.includes("url")?hZ:f}),h.map((e,t)=>{var n=e.words.join(b?"":" ");return o.createElement("tspan",{x:w,dy:0===t?r:a,key:"".concat(n,"-").concat(t)},n)}))});hY.displayName="Text";var hG=["offset"],hX=["labelRef"];function hJ(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function hQ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h0(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hQ(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hQ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function h1(){return(h1=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var h2=e=>{var{value:t,formatter:r}=e,n=_(e.children)?t:e.children;return"function"==typeof r?r(n):n},h5=e=>null!=e&&"function"==typeof e,h3=(e,t)=>w(t-e)*Math.min(Math.abs(t-e),360),h6=(e,t,r)=>{var n,i,{position:a,viewBox:l,offset:s,className:c}=e,{cx:u,cy:f,innerRadius:d,outerRadius:h,startAngle:p,endAngle:y,clockWise:g}=l,m=(d+h)/2,b=h3(p,y),x=b>=0?1:-1;"insideStart"===a?(n=p+x*s,i=g):"insideEnd"===a?(n=y-x*s,i=!g):"end"===a&&(n=y+x*s,i=g),i=b<=0?i:!i;var w=rF(u,f,m,n),O=rF(u,f,m,n+(i?1:-1)*359),j="M".concat(w.x,",").concat(w.y,"\n    A").concat(m,",").concat(m,",0,1,").concat(i?0:1,",\n    ").concat(O.x,",").concat(O.y),P=_(e.id)?E("recharts-radial-line-"):e.id;return o.createElement("text",h1({},r,{dominantBaseline:"central",className:(0,v.W)("recharts-radial-bar-label",c)}),o.createElement("defs",null,o.createElement("path",{id:P,d:j})),o.createElement("textPath",{xlinkHref:"#".concat(P)},t))},h4=e=>{var{viewBox:t,offset:r,position:n}=e,{cx:i,cy:a,innerRadius:o,outerRadius:l,startAngle:s,endAngle:c}=t,u=(s+c)/2;if("outside"===n){var{x:f,y:d}=rF(i,a,l+r,u);return{x:f,y:d,textAnchor:f>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var{x:h,y:p}=rF(i,a,(o+l)/2,u);return{x:h,y:p,textAnchor:"middle",verticalAnchor:"middle"}},h7=(e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:l,height:s}=t,c=s>=0?1:-1,u=c*n,f=c>0?"end":"start",d=c>0?"start":"end",h=l>=0?1:-1,p=h*n,y=h>0?"end":"start",v=h>0?"start":"end";if("top"===i)return h0(h0({},{x:a+l/2,y:o-c*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(o-r.y,0),width:l}:{});if("bottom"===i)return h0(h0({},{x:a+l/2,y:o+s+u,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(r.y+r.height-(o+s),0),width:l}:{});if("left"===i){var g={x:a-p,y:o+s/2,textAnchor:y,verticalAnchor:"middle"};return h0(h0({},g),r?{width:Math.max(g.x-r.x,0),height:s}:{})}if("right"===i){var m={x:a+l+p,y:o+s/2,textAnchor:v,verticalAnchor:"middle"};return h0(h0({},m),r?{width:Math.max(r.x+r.width-m.x,0),height:s}:{})}var b=r?{width:l,height:s}:{};return"insideLeft"===i?h0({x:a+p,y:o+s/2,textAnchor:v,verticalAnchor:"middle"},b):"insideRight"===i?h0({x:a+l-p,y:o+s/2,textAnchor:y,verticalAnchor:"middle"},b):"insideTop"===i?h0({x:a+l/2,y:o+u,textAnchor:"middle",verticalAnchor:d},b):"insideBottom"===i?h0({x:a+l/2,y:o+s-u,textAnchor:"middle",verticalAnchor:f},b):"insideTopLeft"===i?h0({x:a+p,y:o+u,textAnchor:v,verticalAnchor:d},b):"insideTopRight"===i?h0({x:a+l-p,y:o+u,textAnchor:y,verticalAnchor:d},b):"insideBottomLeft"===i?h0({x:a+p,y:o+s-u,textAnchor:v,verticalAnchor:f},b):"insideBottomRight"===i?h0({x:a+l-p,y:o+s-u,textAnchor:y,verticalAnchor:f},b):i&&"object"==typeof i&&(P(i.x)||j(i.x))&&(P(i.y)||j(i.y))?h0({x:a+M(i.x,l),y:o+M(i.y,s),textAnchor:"end",verticalAnchor:"end"},b):h0({x:a+l/2,y:o+s/2,textAnchor:"middle",verticalAnchor:"middle"},b)},h8=e=>"cx"in e&&P(e.cx);function h9(e){var t,{offset:r=5}=e,n=h0({offset:r},hJ(e,hG)),{viewBox:i,position:a,value:l,children:s,content:c,className:u="",textBreakAll:f,labelRef:d}=n,h=nE(),p=i||h;if(!p||_(l)&&_(s)&&!(0,o.isValidElement)(c)&&"function"!=typeof c)return null;if((0,o.isValidElement)(c)){var{labelRef:y}=n,g=hJ(n,hX);return(0,o.cloneElement)(c,g)}if("function"==typeof c){if(t=(0,o.createElement)(c,n),(0,o.isValidElement)(t))return t}else t=h2(n);var m=h8(p),b=dU(n,!0);if(m&&("insideStart"===a||"insideEnd"===a||"end"===a))return h6(n,t,b);var x=m?h4(n):h7(n,p);return o.createElement(hY,h1({ref:d,className:(0,v.W)("recharts-label",u)},b,x,{breakAll:f}),t)}h9.displayName="Label";var pe=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:l,innerRadius:s,outerRadius:c,x:u,y:f,top:d,left:h,width:p,height:y,clockWise:v,labelViewBox:g}=e;if(g)return g;if(P(p)&&P(y)){if(P(u)&&P(f))return{x:u,y:f,width:p,height:y};if(P(d)&&P(h))return{x:d,y:h,width:p,height:y}}return P(u)&&P(f)?{x:u,y:f,width:0,height:0}:P(t)&&P(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:s||0,outerRadius:c||l||o||0,clockWise:v}:e.viewBox?e.viewBox:void 0},pt=(e,t,r)=>{if(!e)return null;var n={viewBox:t,labelRef:r};return!0===e?o.createElement(h9,h1({key:"label-implicit"},n)):S(e)?o.createElement(h9,h1({key:"label-implicit",value:e},n)):(0,o.isValidElement)(e)?e.type===h9?(0,o.cloneElement)(e,h0({key:"label-implicit"},n)):o.createElement(h9,h1({key:"label-implicit",content:e},n)):h5(e)?o.createElement(h9,h1({key:"label-implicit",content:e},n)):e&&"object"==typeof e?o.createElement(h9,h1({},e,{key:"label-implicit"},n)):null};h9.parseViewBox=pe,h9.renderCallByParent=function(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:n,labelRef:i}=e,a=pe(e),l=dB(n,h9).map((e,r)=>(0,o.cloneElement)(e,{viewBox:t||a,key:"label-".concat(r)}));return r?[pt(e.label,t||a,i),...l]:l};var pr=["viewBox"],pn=["viewBox"];function pi(){return(pi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function pa(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function po(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pa(Object(r),!0).forEach(function(t){ps(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pa(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pl(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function ps(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class pc extends o.Component{constructor(e){super(e),this.tickRefs=o.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(e,t){var{viewBox:r}=e,n=pl(e,pr),i=this.props,{viewBox:a}=i,o=pl(i,pn);return!hS(r,a)||!hS(n,o)||!hS(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,o,{x:l,y:s,width:c,height:u,orientation:f,tickSize:d,mirror:h,tickMargin:p}=this.props,y=h?-1:1,v=e.tickSize||d,g=P(e.tickCoord)?e.tickCoord:e.coordinate;switch(f){case"top":t=r=e.coordinate,o=(n=(i=s+ +!h*u)-y*v)-y*p,a=g;break;case"left":n=i=e.coordinate,a=(t=(r=l+ +!h*c)-y*v)-y*p,o=g;break;case"right":n=i=e.coordinate,a=(t=(r=l+ +h*c)+y*v)+y*p,o=g;break;default:t=r=e.coordinate,o=(n=(i=s+ +h*u)+y*v)+y*p,a=g}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:n,orientation:i,mirror:a,axisLine:l}=this.props,s=po(po(po({},dU(this.props,!1)),dU(l,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var c=+("top"===i&&!a||"bottom"===i&&a);s=po(po({},s),{},{x1:e,y1:t+c*n,x2:e+r,y2:t+c*n})}else{var u=+("left"===i&&!a||"right"===i&&a);s=po(po({},s),{},{x1:e+u*r,y1:t,x2:e+u*r,y2:t+n})}return o.createElement("line",pi({},s,{className:(0,v.W)("recharts-cartesian-axis-line",x()(l,"className"))}))}static renderTickItem(e,t,r){var n,i=(0,v.W)(t.className,"recharts-cartesian-axis-tick-value");if(o.isValidElement(e))n=o.cloneElement(e,po(po({},t),{},{className:i}));else if("function"==typeof e)n=e(po(po({},t),{},{className:i}));else{var a="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(a=(0,v.W)(a,e.className)),n=o.createElement(hY,pi({},t,{className:a}),r)}return n}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:n,stroke:i,tick:a,tickFormatter:l,unit:s}=this.props,c=hP(po(po({},this.props),{},{ticks:r}),e,t),u=this.getTickTextAnchor(),f=this.getTickVerticalAnchor(),d=dU(this.props,!1),h=dU(a,!1),p=po(po({},d),{},{fill:"none"},dU(n,!1)),y=c.map((e,t)=>{var{line:r,tick:y}=this.getTickLineCoord(e),g=po(po(po(po({textAnchor:u,verticalAnchor:f},d),{},{stroke:"none",fill:i},h),y),{},{index:t,payload:e,visibleTicksCount:c.length,tickFormatter:l});return o.createElement(hM,pi({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},dD(this.props,e,t)),n&&o.createElement("line",pi({},p,r,{className:(0,v.W)("recharts-cartesian-axis-tick-line",x()(n,"className"))})),a&&pc.renderTickItem(a,g,"".concat("function"==typeof l?l(e.value,t):e.value).concat(s||"")))});return y.length>0?o.createElement("g",{className:"recharts-cartesian-axis-ticks"},y):null}render(){var{axisLine:e,width:t,height:r,className:n,hide:i}=this.props;if(i)return null;var{ticks:a}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:o.createElement(hM,{className:(0,v.W)("recharts-cartesian-axis",n),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,a),h9.renderCallByParent(this.props))}}ps(pc,"displayName","CartesianAxis"),ps(pc,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var pu=["x1","y1","x2","y2","key"],pf=["offset"],pd=["xAxisId","yAxisId"],ph=["xAxisId","yAxisId"];function pp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function py(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pp(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pp(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pv(){return(pv=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function pg(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var pm=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:n,y:i,width:a,height:l,ry:s}=e;return o.createElement("rect",{x:n,y:i,ry:s,width:a,height:l,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function pb(e,t){var r;if(o.isValidElement(e))r=o.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:n,y1:i,x2:a,y2:l,key:s}=t,c=dU(pg(t,pu),!1),{offset:u}=c,f=pg(c,pf);r=o.createElement("line",pv({},f,{x1:n,y1:i,x2:a,y2:l,fill:"none",key:s}))}return r}function px(e){var{x:t,width:r,horizontal:n=!0,horizontalPoints:i}=e;if(!n||!i||!i.length)return null;var{xAxisId:a,yAxisId:l}=e,s=pg(e,pd),c=i.map((e,i)=>pb(n,py(py({},s),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(i),index:i})));return o.createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function pw(e){var{y:t,height:r,vertical:n=!0,verticalPoints:i}=e;if(!n||!i||!i.length)return null;var{xAxisId:a,yAxisId:l}=e,s=pg(e,ph),c=i.map((e,i)=>pb(n,py(py({},s),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(i),index:i})));return o.createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function pO(e){var{horizontalFill:t,fillOpacity:r,x:n,y:i,width:a,height:l,horizontalPoints:s,horizontal:c=!0}=e;if(!c||!t||!t.length)return null;var u=s.map(e=>Math.round(e+i-i)).sort((e,t)=>e-t);i!==u[0]&&u.unshift(0);var f=u.map((e,s)=>{var c=u[s+1]?u[s+1]-e:i+l-e;if(c<=0)return null;var f=s%t.length;return o.createElement("rect",{key:"react-".concat(s),y:e,x:n,height:c,width:a,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return o.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function pj(e){var{vertical:t=!0,verticalFill:r,fillOpacity:n,x:i,y:a,width:l,height:s,verticalPoints:c}=e;if(!t||!r||!r.length)return null;var u=c.map(e=>Math.round(e+i-i)).sort((e,t)=>e-t);i!==u[0]&&u.unshift(0);var f=u.map((e,t)=>{var c=u[t+1]?u[t+1]-e:i+l-e;if(c<=0)return null;var f=t%r.length;return o.createElement("rect",{key:"react-".concat(t),x:e,y:a,width:c,height:s,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return o.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var pP=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return r2(hP(py(py(py({},pc.defaultProps),r),{},{ticks:r5(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},pS=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return r2(hP(py(py(py({},pc.defaultProps),r),{},{ticks:r5(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},pA={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function pE(e){var t=nT(),r=n_(),n=nk(),i=py(py({},hi(e,pA)),{},{x:P(e.x)?e.x:n.left,y:P(e.y)?e.y:n.top,width:P(e.width)?e.width:n.width,height:P(e.height)?e.height:n.height}),{xAxisId:a,yAxisId:l,x:s,y:c,width:u,height:f,syncWithTicks:d,horizontalValues:h,verticalValues:p}=i,y=nP(),v=rk(e=>ut(e,"xAxis",a,y)),g=rk(e=>ut(e,"yAxis",l,y));if(!P(u)||u<=0||!P(f)||f<=0||!P(s)||s!==+s||!P(c)||c!==+c)return null;var m=i.verticalCoordinatesGenerator||pP,b=i.horizontalCoordinatesGenerator||pS,{horizontalPoints:x,verticalPoints:w}=i;if((!x||!x.length)&&"function"==typeof b){var O=h&&h.length,j=b({yAxis:g?py(py({},g),{},{ticks:O?h:g.ticks}):void 0,width:t,height:r,offset:n},!!O||d);C(Array.isArray(j),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof j,"]")),Array.isArray(j)&&(x=j)}if((!w||!w.length)&&"function"==typeof m){var S=p&&p.length,A=m({xAxis:v?py(py({},v),{},{ticks:S?p:v.ticks}):void 0,width:t,height:r,offset:n},!!S||d);C(Array.isArray(A),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof A,"]")),Array.isArray(A)&&(w=A)}return o.createElement("g",{className:"recharts-cartesian-grid"},o.createElement(pm,{fill:i.fill,fillOpacity:i.fillOpacity,x:i.x,y:i.y,width:i.width,height:i.height,ry:i.ry}),o.createElement(pO,pv({},i,{horizontalPoints:x})),o.createElement(pj,pv({},i,{verticalPoints:w})),o.createElement(px,pv({},i,{offset:n,horizontalPoints:x,xAxis:v,yAxis:g})),o.createElement(pw,pv({},i,{offset:n,verticalPoints:w,xAxis:v,yAxis:g})))}pE.displayName="CartesianGrid";var pM=["children"],pk=["dangerouslySetInnerHTML","ticks"];function pT(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p_(){return(p_=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function pN(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function pC(e){rS();var t=(0,o.useMemo)(()=>{var{children:t}=e;return pN(e,pM)},[e]),r=rk(e=>sB(e,t.id));return t===r?e.children:null}var pD=e=>{var{xAxisId:t,className:r}=e,n=rk(nO),i=nP(),a="xAxis",l=rk(e=>cW(e,a,t,i)),s=rk(e=>ur(e,a,t,i)),c=rk(e=>cJ(e,t)),u=rk(e=>c5(e,t));if(null==c||null==u)return null;var{dangerouslySetInnerHTML:f,ticks:d}=e,h=pN(e,pk);return o.createElement(pc,p_({},h,{scale:l,x:u.x,y:u.y,width:c.width,height:c.height,className:(0,v.W)("recharts-".concat(a," ").concat(a),r),viewBox:n,ticks:s}))},pI=e=>{var t,r,n,i,a;return o.createElement(pC,{interval:null!==(t=e.interval)&&void 0!==t?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!==(r=e.includeHidden)&&void 0!==r&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!==(n=e.angle)&&void 0!==n?n:0,minTickGap:null!==(i=e.minTickGap)&&void 0!==i?i:5,tick:null===(a=e.tick)||void 0===a||a,tickFormatter:e.tickFormatter},o.createElement(pD,e))};class pR extends o.Component{render(){return o.createElement(pI,this.props)}}pT(pR,"displayName","XAxis"),pT(pR,"defaultProps",{allowDataOverflow:sz.allowDataOverflow,allowDecimals:sz.allowDecimals,allowDuplicatedCategory:sz.allowDuplicatedCategory,height:sz.height,hide:!1,mirror:sz.mirror,orientation:sz.orientation,padding:sz.padding,reversed:sz.reversed,scale:sz.scale,tickCount:sz.tickCount,type:sz.type,xAxisId:0});var pL=e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>o&&(o=t.width)}});var l=r?r.getBoundingClientRect().width:0;return Math.round(o+(i+a)+l+(r?n:0))}return 0},pz=["dangerouslySetInnerHTML","ticks"];function pB(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p$(){return(p$=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function pU(e){return rS(),null}var pK=e=>{var t,{yAxisId:r,className:n,width:i,label:a}=e,l=(0,o.useRef)(null),s=(0,o.useRef)(null),c=rk(nO),u=nP(),f=rS(),d="yAxis",h=rk(e=>cW(e,d,r,u)),p=rk(e=>c6(e,r)),y=rk(e=>c3(e,r)),g=rk(e=>ur(e,d,r,u));if((0,o.useLayoutEffect)(()=>{if(!("auto"!==i||!p||h5(a)||(0,o.isValidElement)(a))){var e,t=l.current,n=null==t||null===(e=t.tickRefs)||void 0===e?void 0:e.current,{tickSize:c,tickMargin:u}=t.props,d=pL({ticks:n,label:s.current,labelGapWithTick:5,tickSize:c,tickMargin:u});Math.round(p.width)!==Math.round(d)&&f(fC({id:r,width:d}))}},[l,null==l||null===(t=l.current)||void 0===t||null===(t=t.tickRefs)||void 0===t?void 0:t.current,null==p?void 0:p.width,p,f,a,r,i]),null==p||null==y)return null;var{dangerouslySetInnerHTML:m,ticks:b}=e,x=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,pz);return o.createElement(pc,p$({},x,{ref:l,labelRef:s,scale:h,x:y.x,y:y.y,width:p.width,height:p.height,className:(0,v.W)("recharts-".concat(d," ").concat(d),n),viewBox:c,ticks:g}))},pF=e=>{var t,r,n,i,a;return o.createElement(o.Fragment,null,o.createElement(pU,{interval:null!==(t=e.interval)&&void 0!==t?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!==(r=e.includeHidden)&&void 0!==r&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!==(n=e.angle)&&void 0!==n?n:0,minTickGap:null!==(i=e.minTickGap)&&void 0!==i?i:5,tick:null===(a=e.tick)||void 0===a||a,tickFormatter:e.tickFormatter}),o.createElement(pK,e))},pW={allowDataOverflow:s$.allowDataOverflow,allowDecimals:s$.allowDecimals,allowDuplicatedCategory:s$.allowDuplicatedCategory,hide:!1,mirror:s$.mirror,orientation:s$.orientation,padding:s$.padding,reversed:s$.reversed,scale:s$.scale,tickCount:s$.tickCount,type:s$.type,width:s$.width,yAxisId:0};class pq extends o.Component{render(){return o.createElement(pF,this.props)}}pB(pq,"displayName","YAxis"),pB(pq,"defaultProps",pW);var pV=r(60962);function pH(){return(pH=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function pZ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pZ(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pZ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pG(e){return Array.isArray(e)&&S(e[0])&&S(e[1])?e.join(" ~ "):e}var pX=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:n={},labelStyle:i={},payload:a,formatter:l,itemSorter:s,wrapperClassName:c,labelClassName:u,label:f,labelFormatter:d,accessibilityLayer:h=!1}=e,p=pY({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),y=pY({margin:0},i),g=!_(f),m=g?f:"",b=(0,v.W)("recharts-default-tooltip",c),x=(0,v.W)("recharts-tooltip-label",u);return g&&d&&null!=a&&(m=d(f,a)),o.createElement("div",pH({className:b,style:p},h?{role:"status","aria-live":"assertive"}:{}),o.createElement("p",{className:x,style:y},o.isValidElement(m)?m:"".concat(m)),(()=>{if(a&&a.length){var e=(s?r_()(a,s):a).map((e,r)=>{if("none"===e.type)return null;var i=e.formatter||l||pG,{value:s,name:c}=e,u=s,f=c;if(i){var d=i(s,c,e,r,a);if(Array.isArray(d))[u,f]=d;else{if(null==d)return null;u=d}}var h=pY({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},n);return o.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:h},S(f)?o.createElement("span",{className:"recharts-tooltip-item-name"},f):null,S(f)?o.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,o.createElement("span",{className:"recharts-tooltip-item-value"},u),o.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return o.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},pJ="recharts-tooltip-wrapper",pQ={visibility:"hidden"};function p0(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:l,viewBox:s,viewBoxDimension:c}=e;if(a&&P(a[n]))return a[n];var u=r[n]-l-(i>0?i:0),f=r[n]+i;if(t[n])return o[n]?u:f;var d=s[n];return null==d?0:o[n]?u<d?Math.max(f,d):Math.max(u,d):null==c?0:f+l>d+c?Math.max(u,d):Math.max(f,d)}function p1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p1(Object(r),!0).forEach(function(t){p5(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p1(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function p5(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class p3 extends o.PureComponent{constructor(){super(...arguments),p5(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),p5(this,"handleKeyDown",e=>{if("Escape"===e.key){var t,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(t=null===(r=this.props.coordinate)||void 0===r?void 0:r.x)&&void 0!==t?t:0,y:null!==(n=null===(i=this.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==n?n:0}})}})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null===(e=this.props.coordinate)||void 0===e?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null===(t=this.props.coordinate)||void 0===t?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:n,children:i,coordinate:a,hasPayload:l,isAnimationActive:s,offset:c,position:u,reverseDirection:f,useTranslate3d:d,viewBox:h,wrapperStyle:p,lastBoundingBox:y,innerRef:g,hasPortalFromProps:m}=this.props,{cssClasses:b,cssProperties:x}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:l,reverseDirection:s,tooltipBox:c,useTranslate3d:u,viewBox:f}=e;return{cssProperties:c.height>0&&c.width>0&&a?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=p0({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:l,reverseDirection:s,tooltipDimension:c.width,viewBox:f,viewBoxDimension:f.width}),translateY:n=p0({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:l,reverseDirection:s,tooltipDimension:c.height,viewBox:f,viewBoxDimension:f.height}),useTranslate3d:u}):pQ,cssClasses:function(e){var{coordinate:t,translateX:r,translateY:n}=e;return(0,v.W)(pJ,{["".concat(pJ,"-right")]:P(r)&&t&&P(t.x)&&r>=t.x,["".concat(pJ,"-left")]:P(r)&&t&&P(t.x)&&r<t.x,["".concat(pJ,"-bottom")]:P(n)&&t&&P(t.y)&&n>=t.y,["".concat(pJ,"-top")]:P(n)&&t&&P(t.y)&&n<t.y})}({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:t,coordinate:a,offsetTopLeft:c,position:u,reverseDirection:f,tooltipBox:{height:y.height,width:y.width},useTranslate3d:d,viewBox:h}),w=m?{}:p2(p2({transition:s&&e?"transform ".concat(r,"ms ").concat(n):void 0},x),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&l?"visible":"hidden",position:"absolute",top:0,left:0}),O=p2(p2({},w),{},{visibility:!this.state.dismissed&&e&&l?"visible":"hidden"},p);return o.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:b,style:O,ref:g},i)}}var p6=r(70028),p4=r.n(p6);function p7(){}function p8(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function p9(e){this._context=e}function ye(e){this._context=e}function yt(e){this._context=e}p9.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:p8(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:p8(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},ye.prototype={areaStart:p7,areaEnd:p7,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:p8(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},yt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:p8(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class yr{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function yn(e){this._context=e}function yi(e){this._context=e}function ya(e){return new yi(e)}function yo(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0);return((a<0?-1:1)+(o<0?-1:1))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs((a*i+o*n)/(n+i)))||0}function yl(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function ys(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,l=(a-n)/3;e._context.bezierCurveTo(n+l,i+l*t,a-l,o-l*r,a,o)}function yc(e){this._context=e}function yu(e){this._context=new yf(e)}function yf(e){this._context=e}function yd(e){this._context=e}function yh(e){var t,r,n=e.length-1,i=Array(n),a=Array(n),o=Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(t=0,a[n-1]=(e[n]+i[n-1])/2;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function yp(e,t){this._context=e,this._t=t}yn.prototype={areaStart:p7,areaEnd:p7,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}},yi.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},yc.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:ys(this,this._t0,yl(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t=+t,(e=+e)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,ys(this,yl(this,r=yo(this,e,t)),r);break;default:ys(this,this._t0,r=yo(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(yu.prototype=Object.create(yc.prototype)).point=function(e,t){yc.prototype.point.call(this,t,e)},yf.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},yd.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r){if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=yh(e),i=yh(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},yp.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};let yy=Math.PI,yv=2*yy,yg=yv-1e-6;function ym(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class yb{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?ym:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return ym;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,n,i){if(e=+e,t=+t,r=+r,n=+n,(i=+i)<0)throw Error(`negative radius: ${i}`);let a=this._x1,o=this._y1,l=r-e,s=n-t,c=a-e,u=o-t,f=c*c+u*u;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>1e-6){if(Math.abs(u*l-s*c)>1e-6&&i){let d=r-a,h=n-o,p=l*l+s*s,y=Math.sqrt(p),v=Math.sqrt(f),g=i*Math.tan((yy-Math.acos((p+f-(d*d+h*h))/(2*y*v)))/2),m=g/v,b=g/y;Math.abs(m-1)>1e-6&&this._append`L${e+m*c},${t+m*u}`,this._append`A${i},${i},0,0,${+(u*d>c*h)},${this._x1=e+b*l},${this._y1=t+b*s}`}else this._append`L${this._x1=e},${this._y1=t}`}}arc(e,t,r,n,i,a){if(e=+e,t=+t,a=!!a,(r=+r)<0)throw Error(`negative radius: ${r}`);let o=r*Math.cos(n),l=r*Math.sin(n),s=e+o,c=t+l,u=1^a,f=a?n-i:i-n;null===this._x1?this._append`M${s},${c}`:(Math.abs(this._x1-s)>1e-6||Math.abs(this._y1-c)>1e-6)&&this._append`L${s},${c}`,r&&(f<0&&(f=f%yv+yv),f>yg?this._append`A${r},${r},0,1,${u},${e-o},${t-l}A${r},${r},0,1,${u},${this._x1=s},${this._y1=c}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=yy)},${u},${this._x1=e+r*Math.cos(i)},${this._y1=t+r*Math.sin(i)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function yx(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new yb(t)}function yw(e){return e[0]}function yO(e){return e[1]}function yj(e,t){var r=rI(!0),n=null,i=ya,a=null,o=yx(l);function l(l){var s,c,u,f=(l=rD(l)).length,d=!1;for(null==n&&(a=i(u=o())),s=0;s<=f;++s)!(s<f&&r(c=l[s],s,l))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(c,s,l),+t(c,s,l));if(u)return a=null,u+""||null}return e="function"==typeof e?e:void 0===e?yw:rI(e),t="function"==typeof t?t:void 0===t?yO:rI(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:rI(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:rI(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:rI(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),l):n},l}function yP(e,t,r){var n=null,i=rI(!0),a=null,o=ya,l=null,s=yx(c);function c(c){var u,f,d,h,p,y=(c=rD(c)).length,v=!1,g=Array(y),m=Array(y);for(null==a&&(l=o(p=s())),u=0;u<=y;++u){if(!(u<y&&i(h=c[u],u,c))===v){if(v=!v)f=u,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),d=u-1;d>=f;--d)l.point(g[d],m[d]);l.lineEnd(),l.areaEnd()}}v&&(g[u]=+e(h,u,c),m[u]=+t(h,u,c),l.point(n?+n(h,u,c):g[u],r?+r(h,u,c):m[u]))}if(p)return l=null,p+""||null}function u(){return yj().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?yw:rI(+e),t="function"==typeof t?t:void 0===t?rI(0):rI(+t),r="function"==typeof r?r:void 0===r?yO:rI(+r),c.x=function(t){return arguments.length?(e="function"==typeof t?t:rI(+t),n=null,c):e},c.x0=function(t){return arguments.length?(e="function"==typeof t?t:rI(+t),c):e},c.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:rI(+e),c):n},c.y=function(e){return arguments.length?(t="function"==typeof e?e:rI(+e),r=null,c):t},c.y0=function(e){return arguments.length?(t="function"==typeof e?e:rI(+e),c):t},c.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:rI(+e),c):r},c.lineX0=c.lineY0=function(){return u().x(e).y(t)},c.lineY1=function(){return u().x(e).y(r)},c.lineX1=function(){return u().x(n).y(t)},c.defined=function(e){return arguments.length?(i="function"==typeof e?e:rI(!!e),c):i},c.curve=function(e){return arguments.length?(o=e,null!=a&&(l=o(a)),c):o},c.context=function(e){return arguments.length?(null==e?a=l=null:l=o(a=e),c):a},c}function yS(){return(yS=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function yA(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yE(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yA(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yA(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}yb.prototype;var yM={curveBasisClosed:function(e){return new ye(e)},curveBasisOpen:function(e){return new yt(e)},curveBasis:function(e){return new p9(e)},curveBumpX:function(e){return new yr(e,!0)},curveBumpY:function(e){return new yr(e,!1)},curveLinearClosed:function(e){return new yn(e)},curveLinear:ya,curveMonotoneX:function(e){return new yc(e)},curveMonotoneY:function(e){return new yu(e)},curveNatural:function(e){return new yd(e)},curveStep:function(e){return new yp(e,.5)},curveStepAfter:function(e){return new yp(e,1)},curveStepBefore:function(e){return new yp(e,0)}},yk=e=>lv(e.x)&&lv(e.y),yT=e=>e.x,y_=e=>e.y,yN=(e,t)=>{if("function"==typeof e)return e;var r="curve".concat(N(e));return("curveMonotone"===r||"curveBump"===r)&&t?yM["".concat(r).concat("vertical"===t?"Y":"X")]:yM[r]||ya},yC=e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,l=yN(r,a),s=o?n.filter(yk):n;if(Array.isArray(i)){var c=o?i.filter(e=>yk(e)):i,u=s.map((e,t)=>yE(yE({},e),{},{base:c[t]}));return(t="vertical"===a?yP().y(y_).x1(yT).x0(e=>e.base.x):yP().x(yT).y1(y_).y0(e=>e.base.y)).defined(yk).curve(l),t(u)}return(t="vertical"===a&&P(i)?yP().y(y_).x1(yT).x0(i):P(i)?yP().x(yT).y1(y_).y0(i):yj().x(yT).y(y_)).defined(yk).curve(l),t(s)},yD=e=>{var{className:t,points:r,path:n,pathRef:i}=e;if((!r||!r.length)&&!n)return null;var a=r&&r.length?yC(e):n;return o.createElement("path",yS({},dU(e,!1),dN(e),{className:(0,v.W)("recharts-curve",t),d:null===a?void 0:a,ref:i}))},yI=["x","y","top","left","width","height","className"];function yR(){return(yR=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function yL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var yz=(e,t,r,n,i,a)=>"M".concat(e,",").concat(i,"v").concat(n,"M").concat(a,",").concat(t,"h").concat(r),yB=e=>{var{x:t=0,y:r=0,top:n=0,left:i=0,width:a=0,height:l=0,className:s}=e,c=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yL(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:n,left:i,width:a,height:l},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,yI));return P(t)&&P(r)&&P(a)&&P(l)&&P(n)&&P(i)?o.createElement("path",yR({},dU(c,!0),{className:(0,v.W)("recharts-cross",s),d:yz(t,r,a,l,n,i)})):null},y$=r(18260),yU=r.n(y$),yK=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],yF=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),yW=(e,t)=>r=>yF(yK(e,t),r),yq=(e,t)=>r=>yF([...yK(e,t).map((e,t)=>e*t).slice(1),0],r),yV=function(){for(var e,t,r,n,i=arguments.length,a=Array(i),o=0;o<i;o++)a[o]=arguments[o];if(1===a.length)switch(a[0]){case"linear":[e,r,t,n]=[0,0,1,1];break;case"ease":[e,r,t,n]=[.25,.1,.25,1];break;case"ease-in":[e,r,t,n]=[.42,0,1,1];break;case"ease-out":[e,r,t,n]=[.42,0,.58,1];break;case"ease-in-out":[e,r,t,n]=[0,0,.58,1];break;default:var l=a[0].split("(");"cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length&&([e,r,t,n]=l[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===a.length&&([e,r,t,n]=a);var s=yW(e,t),c=yW(r,n),u=yq(e,t),f=e=>e>1?1:e<0?0:e,d=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=s(r)-t,a=u(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=f(r-i/a)}return c(r)};return d.isStepper=!1,d},yH=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(o)?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i},yZ=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return yV(e);case"spring":return yH();default:if("cubic-bezier"===e.split("(")[0])return yV(e)}return"function"==typeof e?e:null};function yY(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yG(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yY(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yY(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var yX=e=>e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())),yJ=(e,t,r)=>e.map(e=>"".concat(yX(e)," ").concat(t,"ms ").concat(r)).join(","),yQ=(e,t)=>[Object.keys(e),Object.keys(t)].reduce((e,t)=>e.filter(e=>t.includes(e))),y0=(e,t)=>Object.keys(t).reduce((r,n)=>yG(yG({},r),{},{[n]:e(n,t[n])}),{});function y1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y1(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y1(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y5=(e,t,r)=>e+(t-e)*r,y3=e=>{var{from:t,to:r}=e;return t!==r},y6=(e,t,r)=>{var n=y0((t,r)=>{if(y3(r)){var[n,i]=e(r.from,r.to,r.velocity);return y2(y2({},r),{},{from:n,velocity:i})}return r},t);return r<1?y0((e,t)=>y3(t)?y2(y2({},t),{},{velocity:y5(t.velocity,n[e].velocity,r),from:y5(t.from,n[e].from,r)}):t,t):y6(e,n,r-1)};let y4=(e,t,r,n,i,a)=>{var o=yQ(e,t);return!0===r.isStepper?function(e,t,r,n,i,a){var o,l=n.reduce((r,n)=>y2(y2({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}}),{}),s=()=>y0((e,t)=>t.from,l),c=()=>!Object.values(l).filter(y3).length,u=null,f=n=>{o||(o=n);var d=(n-o)/r.dt;l=y6(r,l,d),i(y2(y2(y2({},e),t),s())),o=n,c()||(u=a.setTimeout(f))};return()=>(u=a.setTimeout(f),()=>{u()})}(e,t,r,o,i,a):function(e,t,r,n,i,a,o){var l,s=null,c=i.reduce((r,n)=>y2(y2({},r),{},{[n]:[e[n],t[n]]}),{}),u=i=>{l||(l=i);var f=(i-l)/n,d=y0((e,t)=>y5(...t,r(f)),c);if(a(y2(y2(y2({},e),t),d)),f<1)s=o.setTimeout(u);else{var h=y0((e,t)=>y5(...t,r(1)),c);a(y2(y2(y2({},e),t),h))}};return()=>(s=o.setTimeout(u),()=>{s()})}(e,t,r,n,o,i,a)};class y7{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var y8=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function y9(){return(y9=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function ve(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ve(Object(r),!0).forEach(function(t){vr(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ve(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vr(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class vn extends o.PureComponent{constructor(e,t){super(e,t),vr(this,"mounted",!1),vr(this,"manager",null),vr(this,"stopJSAnimation",null),vr(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:s}=this.props;if(this.manager=s,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:a});return}if(i){if("function"==typeof o){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:a,from:o}=this.props,{style:l}=this.state;if(r){if(!t){this.state&&l&&(n&&l[n]!==a||!n&&l!==a)&&this.setState({style:n?{[n]:a}:a});return}if(!yU()(e.to,a)||!e.canBegin||!e.isActive){var s=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var c=s||i?o:e.to;this.state&&l&&(n&&l[n]!==c||!n&&l!==c)&&this.setState({style:n?{[n]:c}:c}),this.runAnimation(vt(vt({},this.props),{},{from:c,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:i,begin:a,onAnimationEnd:o,onAnimationStart:l}=e,s=y4(t,r,yZ(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([l,a,()=>{this.stopJSAnimation=s()},n,o])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:i,easing:a,onAnimationStart:o,onAnimationEnd:l,children:s}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof a||"function"==typeof s||"spring"===a){this.runJSAnimation(e);return}var c=n?{[n]:i}:i,u=yJ(Object.keys(c),r,a);this.manager.start([o,t,vt(vt({},c),{},{transition:u}),r,l])}render(){var e=this.props,{children:t,begin:r,duration:n,attributeName:i,easing:a,isActive:l,from:s,to:c,canBegin:u,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:h,animationManager:p}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,y8),v=o.Children.count(t),g=this.state.style;if("function"==typeof t)return t(g);if(!l||0===v||n<=0)return t;var m=e=>{var{style:t={},className:r}=e.props;return(0,o.cloneElement)(e,vt(vt({},y),{},{style:vt(vt({},t),g),className:r}))};return 1===v?m(o.Children.only(t)):o.createElement("div",null,o.Children.map(t,e=>m(e)))}}vr(vn,"displayName","Animate"),vr(vn,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var vi=(0,o.createContext)(null);function va(e){var t,r,n,i,a,l,s,c=(0,o.useContext)(vi);return o.createElement(vn,y9({},e,{animationManager:null!==(l=null!==(s=e.animationManager)&&void 0!==s?s:c)&&void 0!==l?l:(t=new y7,r=()=>null,n=!1,i=null,a=e=>{if(!n){if(Array.isArray(e)){if(!e.length)return;var[o,...l]=e;if("number"==typeof o){i=t.setTimeout(a.bind(null,l),o);return}a(o),i=t.setTimeout(a.bind(null,l));return}"object"==typeof e&&r(e),"function"==typeof e&&e()}},{stop:()=>{n=!0},start:e=>{n=!1,i&&(i(),i=null),a(e)},subscribe:e=>(r=e,()=>{r=()=>null}),getTimeoutController:()=>t})}))}function vo(){return(vo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var vl=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,s=r>=0?1:-1,c=n>=0&&r>=0||n<0&&r<0?1:0;if(o>0&&i instanceof Array){for(var u=[0,0,0,0],f=0;f<4;f++)u[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+l*u[0]),u[0]>0&&(a+="A ".concat(u[0],",").concat(u[0],",0,0,").concat(c,",").concat(e+s*u[0],",").concat(t)),a+="L ".concat(e+r-s*u[1],",").concat(t),u[1]>0&&(a+="A ".concat(u[1],",").concat(u[1],",0,0,").concat(c,",\n        ").concat(e+r,",").concat(t+l*u[1])),a+="L ".concat(e+r,",").concat(t+n-l*u[2]),u[2]>0&&(a+="A ".concat(u[2],",").concat(u[2],",0,0,").concat(c,",\n        ").concat(e+r-s*u[2],",").concat(t+n)),a+="L ".concat(e+s*u[3],",").concat(t+n),u[3]>0&&(a+="A ".concat(u[3],",").concat(u[3],",0,0,").concat(c,",\n        ").concat(e,",").concat(t+n-l*u[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var d=Math.min(o,i);a="M ".concat(e,",").concat(t+l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+s*d,",").concat(t,"\n            L ").concat(e+r-s*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+r,",").concat(t+l*d,"\n            L ").concat(e+r,",").concat(t+n-l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+r-s*d,",").concat(t+n,"\n            L ").concat(e+s*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e,",").concat(t+n-l*d," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},vs={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},vc=e=>{var t=hi(e,vs),r=(0,o.useRef)(null),[n,i]=(0,o.useState)(-1);(0,o.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&i(e)}catch(e){}},[]);var{x:a,y:l,width:s,height:c,radius:u,className:f}=t,{animationEasing:d,animationDuration:h,animationBegin:p,isAnimationActive:y,isUpdateAnimationActive:g}=t;if(a!==+a||l!==+l||s!==+s||c!==+c||0===s||0===c)return null;var m=(0,v.W)("recharts-rectangle",f);return g?o.createElement(va,{canBegin:n>0,from:{width:s,height:c,x:a,y:l},to:{width:s,height:c,x:a,y:l},duration:h,animationEasing:d,isActive:g},e=>{var{width:i,height:a,x:l,y:s}=e;return o.createElement(va,{canBegin:n>0,from:"0px ".concat(-1===n?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:h,isActive:y,easing:d},o.createElement("path",vo({},dU(t,!0),{className:m,d:vl(l,s,i,a,u),ref:r})))}):o.createElement("path",vo({},dU(t,!0),{className:m,d:vl(a,l,s,c,u)}))};function vu(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e;return{points:[rF(t,r,n,i),rF(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function vf(){return(vf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var vd=(e,t)=>w(t-e)*Math.min(Math.abs(t-e),359.999),vh=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:o,cornerRadius:l,cornerIsExternal:s}=e,c=l*(o?1:-1)+n,u=Math.asin(l/c)/rU,f=s?i:i+a*u;return{center:rF(t,r,c,f),circleTangency:rF(t,r,n,f),lineTangency:rF(t,r,c*Math.cos(u*rU),s?i-a*u:i),theta:u}},vp=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:o}=e,l=vd(a,o),s=a+l,c=rF(t,r,i,a),u=rF(t,r,i,s),f="M ".concat(c.x,",").concat(c.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(a>s),",\n    ").concat(u.x,",").concat(u.y,"\n  ");if(n>0){var d=rF(t,r,n,a),h=rF(t,r,n,s);f+="L ".concat(h.x,",").concat(h.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(a<=s),",\n            ").concat(d.x,",").concat(d.y," Z")}else f+="L ".concat(t,",").concat(r," Z");return f},vy=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:l,startAngle:s,endAngle:c}=e,u=w(c-s),{circleTangency:f,lineTangency:d,theta:h}=vh({cx:t,cy:r,radius:i,angle:s,sign:u,cornerRadius:a,cornerIsExternal:l}),{circleTangency:p,lineTangency:y,theta:v}=vh({cx:t,cy:r,radius:i,angle:c,sign:-u,cornerRadius:a,cornerIsExternal:l}),g=l?Math.abs(s-c):Math.abs(s-c)-h-v;if(g<0)return o?"M ".concat(d.x,",").concat(d.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):vp({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:s,endAngle:c});var m="M ".concat(d.x,",").concat(d.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(f.x,",").concat(f.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(g>180),",").concat(+(u<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(y.x,",").concat(y.y,"\n  ");if(n>0){var{circleTangency:b,lineTangency:x,theta:O}=vh({cx:t,cy:r,radius:n,angle:s,sign:u,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),{circleTangency:j,lineTangency:P,theta:S}=vh({cx:t,cy:r,radius:n,angle:c,sign:-u,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),A=l?Math.abs(s-c):Math.abs(s-c)-O-S;if(A<0&&0===a)return"".concat(m,"L").concat(t,",").concat(r,"Z");m+="L".concat(P.x,",").concat(P.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(j.x,",").concat(j.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(A>180),",").concat(+(u>0),",").concat(b.x,",").concat(b.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(x.x,",").concat(x.y,"Z")}else m+="L".concat(t,",").concat(r,"Z");return m},vv={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},vg=e=>{var t,r=hi(e,vv),{cx:n,cy:i,innerRadius:a,outerRadius:l,cornerRadius:s,forceCornerRadius:c,cornerIsExternal:u,startAngle:f,endAngle:d,className:h}=r;if(l<a||f===d)return null;var p=(0,v.W)("recharts-sector",h),y=l-a,g=M(s,y,0,!0);return t=g>0&&360>Math.abs(f-d)?vy({cx:n,cy:i,innerRadius:a,outerRadius:l,cornerRadius:Math.min(g,y/2),forceCornerRadius:c,cornerIsExternal:u,startAngle:f,endAngle:d}):vp({cx:n,cy:i,innerRadius:a,outerRadius:l,startAngle:f,endAngle:d}),o.createElement("path",vf({},dU(r,!0),{className:p,d:t}))};function vm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vb(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vm(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vm(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var vx=()=>rk(uS),vw=()=>{var e=vx(),t=rk(u0),r=rk(uX);return na(vb(vb({},e),{},{scale:r}),t)};function vO(){return(vO=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function vj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vj(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vj(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vS(e){var t,r,n,{coordinate:i,payload:a,index:l,offset:s,tooltipAxisBandSize:c,layout:u,cursor:f,tooltipEventType:d,chartName:h}=e;if(!f||!i||"ScatterChart"!==h&&"axis"!==d)return null;if("ScatterChart"===h)r=i,n=yB;else if("BarChart"===h)t=c/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===u?i.x-t:s.left+.5,y:"horizontal"===u?s.top+.5:i.y-t,width:"horizontal"===u?c:s.width-1,height:"horizontal"===u?s.height-1:c},n=vc;else if("radial"===u){var{cx:p,cy:y,radius:g,startAngle:m,endAngle:b}=vu(i);r={cx:p,cy:y,startAngle:m,endAngle:b,innerRadius:g,outerRadius:g},n=vg}else r={points:function(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy){if("centric"!==e)return vu(t);var{cx:l,cy:s,innerRadius:c,outerRadius:u,angle:f}=t,d=rF(l,s,c,f),h=rF(l,s,u,f);n=d.x,i=d.y,a=h.x,o=h.y}return[{x:n,y:i},{x:a,y:o}]}(u,i,s)},n=yD;var x="object"==typeof f&&"className"in f?f.className:void 0,w=vP(vP(vP(vP({stroke:"#ccc",pointerEvents:"none"},s),r),dU(f,!1)),{},{payload:a,payloadIndex:l,className:(0,v.W)("recharts-tooltip-cursor",x)});return(0,o.isValidElement)(f)?(0,o.cloneElement)(f,w):(0,o.createElement)(n,w)}function vA(e){var t=vw(),r=nk(),n=nC(),i=fn();return o.createElement(vS,vO({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:r,layout:n,tooltipAxisBandSize:t,chartName:i}))}function vE(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vM(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vE(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vE(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vk(e){return e.dataKey}var vT=[],v_={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!hf.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function vN(e){var t,r,n,i=hi(e,v_),{active:a,allowEscapeViewBox:l,animationDuration:s,animationEasing:c,content:u,filterNull:f,isAnimationActive:d,offset:h,payloadUniqBy:p,position:y,reverseDirection:v,useTranslate3d:g,wrapperStyle:m,cursor:b,shared:x,trigger:w,defaultIndex:O,portal:j,axisId:P}=i;rS();var S="number"==typeof O?String(O):O,A=nE(),E=dK(),M=rk(e=>uu(e,x)),{activeIndex:k,isActive:T}=rk(e=>fv(e,M,w,S)),_=rk(e=>fy(e,M,w,S)),N=rk(e=>fp(e,M,w,S)),C=rk(e=>fh(e,M,w,S)),D=d0(),I=null!=a?a:T,[R,L]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,o.useState)({height:0,left:0,top:0,width:0}),n=(0,o.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-t.height)>1||Math.abs(i.left-t.left)>1||Math.abs(i.top-t.top)>1||Math.abs(i.width-t.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,n]}([_,I]),z="axis"===M?N:void 0;rk(e=>fu(e,M,w)),rk(sv),rk(sp),rk(sy),null==(t=rk(dJ))||t.active;var B=null!=j?j:D;if(null==B)return null;var $=null!=_?_:vT;I||($=vT),f&&$.length&&(r=_.filter(e=>null!=e.value&&(!0!==e.hide||i.includeHidden)),$=!0===p?p4()(r,vk):"function"==typeof p?p4()(r,p):r);var U=$.length>0,K=o.createElement(p3,{allowEscapeViewBox:l,animationDuration:s,animationEasing:c,isAnimationActive:d,active:I,coordinate:C,hasPayload:U,offset:h,position:y,reverseDirection:v,useTranslate3d:g,viewBox:A,wrapperStyle:m,lastBoundingBox:R,innerRef:L,hasPortalFromProps:!!j},(n=vM(vM({},i),{},{payload:$,label:z,active:I,coordinate:C,accessibilityLayer:E}),o.isValidElement(u)?o.cloneElement(u,n):"function"==typeof u?o.createElement(u,n):o.createElement(pX,n)));return o.createElement(o.Fragment,null,(0,pV.createPortal)(K,B),I&&o.createElement(vA,{cursor:b,tooltipEventType:M,coordinate:C,payload:_,index:k}))}function vC(e){return rS(),(0,o.useRef)(null),null}function vD(e){return rS(),null}var vI=["children"],vR=()=>{},vL=(0,o.createContext)({addErrorBar:vR,removeErrorBar:vR}),vz=(0,o.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function vB(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,vI);return o.createElement(vz.Provider,{value:r},t)}var v$=()=>(0,o.useContext)(vz),vU=e=>{var{children:t,xAxisId:r,yAxisId:n,zAxisId:i,dataKey:a,data:l,stackId:s,hide:c,type:u,barSize:f}=e,[d,h]=o.useState([]),p=(0,o.useCallback)(e=>{h(t=>[...t,e])},[h]),y=(0,o.useCallback)(e=>{h(t=>t.filter(t=>t!==e))},[h]),v=nP();return o.createElement(vL.Provider,{value:{addErrorBar:p,removeErrorBar:y}},o.createElement(vC,{type:u,data:l,xAxisId:r,yAxisId:n,zAxisId:i,dataKey:a,errorBars:d,stackId:s,hide:c,barSize:f,isPanorama:v}),t)};function vK(e){var{addErrorBar:t,removeErrorBar:r}=(0,o.useContext)(vL);return null}var vF=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function vW(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function vq(){return(vq=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function vV(e){var{direction:t,width:r,dataKey:n,isAnimationActive:i,animationBegin:a,animationDuration:l,animationEasing:s}=e,c=dU(function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,vF),!1),{data:u,dataPointFormatter:f,xAxisId:d,yAxisId:h,errorBarOffset:p}=v$(),y=d4(d),v=d7(h);if((null==y?void 0:y.scale)==null||(null==v?void 0:v.scale)==null||null==u||"x"===t&&"number"!==y.type)return null;var g=u.map(e=>{var u,d,{x:h,y:g,value:m,errorVal:b}=f(e,n,t);if(!b)return null;var x=[];if(Array.isArray(b)?[u,d]=b:u=d=b,"x"===t){var{scale:w}=y,O=g+p,j=O+r,P=O-r,S=w(m-u),A=w(m+d);x.push({x1:A,y1:j,x2:A,y2:P}),x.push({x1:S,y1:O,x2:A,y2:O}),x.push({x1:S,y1:j,x2:S,y2:P})}else if("y"===t){var{scale:E}=v,M=h+p,k=M-r,T=M+r,_=E(m-u),N=E(m+d);x.push({x1:k,y1:N,x2:T,y2:N}),x.push({x1:M,y1:_,x2:M,y2:N}),x.push({x1:k,y1:_,x2:T,y2:_})}var C="".concat(h+p,"px ").concat(g+p,"px");return o.createElement(hM,vq({className:"recharts-errorBar",key:"bar-".concat(x.map(e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)))},c),x.map(e=>{var t=i?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return o.createElement(va,{from:{transform:"scaleY(0)",transformOrigin:C},to:{transform:"scaleY(1)",transformOrigin:C},begin:a,easing:s,isActive:i,duration:l,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:C}},o.createElement("line",vq({},e,{style:t})))}))});return o.createElement(hM,{className:"recharts-errorBars"},g)}var vH=(0,o.createContext)(void 0);function vZ(e){var{direction:t,children:r}=e;return o.createElement(vH.Provider,{value:t},r)}var vY={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function vG(e){var t,r,n=(t=e.direction,r=(0,o.useContext)(vH),null!=t?t:null!=r?r:"x"),{width:i,isAnimationActive:a,animationBegin:l,animationDuration:s,animationEasing:c}=hi(e,vY);return o.createElement(o.Fragment,null,o.createElement(vK,{dataKey:e.dataKey,direction:n}),o.createElement(vV,vq({},e,{direction:n,width:i,isAnimationActive:a,animationBegin:l,animationDuration:s,animationEasing:c})))}class vX extends o.Component{render(){return o.createElement(vG,this.props)}}vW(vX,"defaultProps",vY),vW(vX,"displayName","ErrorBar");var vJ=e=>null;vJ.displayName="Cell";var vQ=r(35157),v0=r.n(vQ),v1=["valueAccessor"],v2=["data","dataKey","clockWise","id","textBreakAll"];function v5(){return(v5=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v3(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v6(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v3(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v3(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function v4(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var v7=e=>Array.isArray(e.value)?v0()(e.value):e.value;function v8(e){var{valueAccessor:t=v7}=e,r=v4(e,v1),{data:n,dataKey:i,clockWise:a,id:l,textBreakAll:s}=r,c=v4(r,v2);return n&&n.length?o.createElement(hM,{className:"recharts-label-list"},n.map((e,r)=>{var n=_(i)?t(e,r):rJ(e&&e.payload,i),u=_(l)?{}:{id:"".concat(l,"-").concat(r)};return o.createElement(h9,v5({},dU(e,!0),c,u,{parentViewBox:e.parentViewBox,value:n,textBreakAll:s,viewBox:h9.parseViewBox(_(a)?e:v6(v6({},e),{},{clockWise:a})),key:"label-".concat(r),index:r}))})):null}v8.displayName="LabelList",v8.renderCallByParent=function(e,t){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var{children:i}=e,a=dB(i,v8).map((e,r)=>(0,o.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return n?[(r=e.label)?!0===r?o.createElement(v8,{key:"labelList-implicit",data:t}):o.isValidElement(r)||h5(r)?o.createElement(v8,{key:"labelList-implicit",data:t,content:r}):"object"==typeof r?o.createElement(v8,v5({data:t},r,{key:"labelList-implicit"})):null:null,...a]:a};var v9=r(21729),ge=r.n(v9);function gt(){return(gt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var gr=(e,t,r,n,i)=>{var a=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-a/2,",").concat(t+i)+"L ".concat(e+r-a/2-n,",").concat(t+i)+"L ".concat(e,",").concat(t," Z")},gn={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},gi=e=>{var t=hi(e,gn),r=(0,o.useRef)(),[n,i]=(0,o.useState)(-1);(0,o.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&i(e)}catch(e){}},[]);var{x:a,y:l,upperWidth:s,lowerWidth:c,height:u,className:f}=t,{animationEasing:d,animationDuration:h,animationBegin:p,isUpdateAnimationActive:y}=t;if(a!==+a||l!==+l||s!==+s||c!==+c||u!==+u||0===s&&0===c||0===u)return null;var g=(0,v.W)("recharts-trapezoid",f);return y?o.createElement(va,{canBegin:n>0,from:{upperWidth:0,lowerWidth:0,height:u,x:a,y:l},to:{upperWidth:s,lowerWidth:c,height:u,x:a,y:l},duration:h,animationEasing:d,isActive:y},e=>{var{upperWidth:i,lowerWidth:a,height:l,x:s,y:c}=e;return o.createElement(va,{canBegin:n>0,from:"0px ".concat(-1===n?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:h,easing:d},o.createElement("path",gt({},dU(t,!0),{className:g,d:gr(s,c,i,a,l),ref:r})))}):o.createElement("g",null,o.createElement("path",gt({},dU(t,!0),{className:g,d:gr(a,l,s,c,u)})))};let ga=Math.cos,go=Math.sin,gl=Math.sqrt,gs=Math.PI,gc=2*gs,gu={draw(e,t){let r=gl(t/gs);e.moveTo(r,0),e.arc(0,0,r,0,gc)}},gf=gl(1/3),gd=2*gf,gh=go(gs/10)/go(7*gs/10),gp=go(gc/10)*gh,gy=-ga(gc/10)*gh,gv=gl(3),gg=gl(3)/2,gm=1/gl(12),gb=(gm/2+1)*3;gl(3),gl(3);var gx=["type","size","sizeType"];function gw(){return(gw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function gO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gO(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var gP={symbolCircle:gu,symbolCross:{draw(e,t){let r=gl(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=gl(t/gd),n=r*gf;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=gl(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=gl(.8908130915292852*t),n=gp*r,i=gy*r;e.moveTo(0,-r),e.lineTo(n,i);for(let t=1;t<5;++t){let a=gc*t/5,o=ga(a),l=go(a);e.lineTo(l*r,-o*r),e.lineTo(o*n-l*i,l*n+o*i)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-gl(t/(3*gv));e.moveTo(0,2*r),e.lineTo(-gv*r,-r),e.lineTo(gv*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=gl(t/gb),n=r/2,i=r*gm,a=r*gm+r,o=-n;e.moveTo(n,i),e.lineTo(n,a),e.lineTo(o,a),e.lineTo(-.5*n-gg*i,gg*n+-.5*i),e.lineTo(-.5*n-gg*a,gg*n+-.5*a),e.lineTo(-.5*o-gg*a,gg*o+-.5*a),e.lineTo(-.5*n+gg*i,-.5*i-gg*n),e.lineTo(-.5*n+gg*a,-.5*a-gg*n),e.lineTo(-.5*o+gg*a,-.5*a-gg*o),e.closePath()}}},gS=Math.PI/180,gA=e=>gP["symbol".concat(N(e))]||gu,gE=(e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*gS;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},gM=e=>{var{type:t="circle",size:r=64,sizeType:n="area"}=e,i=gj(gj({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,gx)),{},{type:t,size:r,sizeType:n}),{className:a,cx:l,cy:s}=i,c=dU(i,!0);return l===+l&&s===+s&&r===+r?o.createElement("path",gw({},c,{className:(0,v.W)("recharts-symbols",a),transform:"translate(".concat(l,", ").concat(s,")"),d:(()=>{var e=gA(t);return(function(e,t){let r=null,n=yx(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:rI(e||gu),t="function"==typeof t?t:rI(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:rI(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:rI(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i})().type(e).size(gE(r,n,t))()})()})):null};gM.registerSymbol=(e,t)=>{gP["symbol".concat(N(e))]=t};var gk=["option","shapeType","propTransformer","activeClassName","isActive"];function gT(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function g_(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gT(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gT(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gN(e,t){return g_(g_({},t),e)}function gC(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return o.createElement(vc,r);case"trapezoid":return o.createElement(gi,r);case"sector":return o.createElement(vg,r);case"symbols":if("symbols"===t)return o.createElement(gM,r);break;default:return null}}function gD(e){var t,{option:r,shapeType:n,propTransformer:i=gN,activeClassName:a="recharts-active-shape",isActive:l}=e,s=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,gk);if((0,o.isValidElement)(r))t=(0,o.cloneElement)(r,g_(g_({},s),(0,o.isValidElement)(r)?r.props:r));else if("function"==typeof r)t=r(s);else if(ge()(r)&&"boolean"!=typeof r){var c=i(r,s);t=o.createElement(gC,{shapeType:n,elementProps:c})}else t=o.createElement(gC,{shapeType:n,elementProps:s});return l?o.createElement(hM,{className:a},t):t}var gI=["x","y"];function gR(){return(gR=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function gL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gL(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gB(e,t){var{x:r,y:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,gI),a=parseInt("".concat(r),10),o=parseInt("".concat(n),10),l=parseInt("".concat(t.height||i.height),10),s=parseInt("".concat(t.width||i.width),10);return gz(gz(gz(gz(gz({},t),i),a?{x:a}:{}),o?{y:o}:{}),{},{height:l,width:s,name:t.name,radius:t.radius})}function g$(e){return o.createElement(gD,gR({shapeType:"rectangle",propTransformer:gB,activeClassName:"recharts-active-bar"},e))}var gU=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if(P(e))return e;var i=P(r)||_(r);return i?e(r,n):(i||function(e,t){if(!e)throw Error("Invariant failed")}(!1),t)}},gK=(e,t)=>{var r=rS();return(n,i)=>a=>{null==e||e(n,i,a),r(tH({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},gF=e=>{var t=rS();return(r,n)=>i=>{null==e||e(r,n,i),t(tZ())}},gW=(e,t)=>{var r=rS();return(n,i)=>a=>{null==e||e(n,i,a),r(tG({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}};function gq(e){var{fn:t,args:r}=e;return rS(),nP(),null}var gV=()=>{var e=rS();return(0,o.useEffect)(()=>(e(fR()),()=>{e(fL())})),null};function gH(e,t){var r,n,i=rk(t=>sB(t,e)),a=rk(e=>sU(e,t)),o=null!==(r=null==i?void 0:i.allowDataOverflow)&&void 0!==r?r:sz.allowDataOverflow,l=null!==(n=null==a?void 0:a.allowDataOverflow)&&void 0!==n?n:s$.allowDataOverflow;return{needClip:o||l,needClipX:o,needClipY:l}}function gZ(e){var{xAxisId:t,yAxisId:r,clipPathId:n}=e,i=d8(),{needClipX:a,needClipY:l,needClip:s}=gH(t,r);if(!s)return null;var{x:c,y:u,width:f,height:d}=i;return o.createElement("clipPath",{id:"clipPath-".concat(n)},o.createElement("rect",{x:a?c:c-f/2,y:l?u:u-d/2,width:a?f:2*f,height:l?d:2*d}))}function gY(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gG(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gY(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gY(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var gX=(e,t,r,n,i)=>i,gJ=(e,t,r)=>{var n=null!=r?r:e;if(!_(n))return M(n,t,0)},gQ=rx([nN,sZ,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],(e,t,r,n,i)=>t.filter(t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n).filter(e=>e.isPanorama===i).filter(e=>!1===e.hide).filter(e=>"bar"===e.type));function g0(e){return null!=e.stackId&&null!=e.dataKey}var g1=rx([gQ,e=>e.rootProps.barSize,(e,t,r)=>"horizontal"===nN(e)?c4(e,"xAxis",t):c4(e,"yAxis",r)],(e,t,r)=>{var n=e.filter(g0),i=e.filter(e=>null==e.stackId);return[...Object.entries(n.reduce((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e),{})).map(e=>{var[n,i]=e;return{stackId:n,dataKeys:i.map(e=>e.dataKey),barSize:gJ(t,r,i[0].barSize)}}),...i.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:gJ(t,r,e.barSize)}))]}),g2=(e,t,r,n)=>{var i,a;return"horizontal"===nN(e)?(i=ui(e,"xAxis",t,n),a=un(e,"xAxis",t,n)):(i=ui(e,"yAxis",r,n),a=un(e,"yAxis",r,n)),na(i,a)},g5=rx([g1,su,e=>e.rootProps.barGap,sf,(e,t,r,n,i)=>{var a,o,l,s,c=nN(e),u=su(e),{maxBarSize:f}=i,d=_(f)?u:f;return"horizontal"===c?(l=ui(e,"xAxis",t,n),s=un(e,"xAxis",t,n)):(l=ui(e,"yAxis",r,n),s=un(e,"yAxis",r,n)),null!==(a=null!==(o=na(l,s,!0))&&void 0!==o?o:d)&&void 0!==a?a:0},g2,(e,t,r,n,i)=>i.maxBarSize],(e,t,r,n,i,a,o)=>{var l=function(e,t,r,n,i){var a,o=n.length;if(!(o<1)){var l=M(e,r,0,!0),s=[];if(lv(n[0].barSize)){var c=!1,u=r/o,f=n.reduce((e,t)=>e+(t.barSize||0),0);(f+=(o-1)*l)>=r&&(f-=(o-1)*l,l=0),f>=r&&u>0&&(c=!0,u*=.9,f=o*u);var d={offset:((r-f)/2>>0)-l,size:0};a=n.reduce((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:d.offset+d.size+l,size:c?u:null!==(r=t.barSize)&&void 0!==r?r:0}}];return d=n[n.length-1].position,n},s)}else{var h=M(t,r,0,!0);r-2*h-(o-1)*l<=0&&(l=0);var p=(r-2*h-(o-1)*l)/o;p>1&&(p>>=0);var y=lv(i)?Math.min(p,i):p;a=n.reduce((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:h+(p+l)*r+(p-y)/2,size:y}}],s)}return a}}(r,n,i!==a?i:a,e,_(o)?t:o);return i!==a&&null!=l&&(l=l.map(e=>gG(gG({},e),{},{position:gG(gG({},e.position),{},{offset:e.position.offset-i/2})}))),l}),g3=rx([g5,gX],(e,t)=>{if(null!=e){var r=e.find(e=>e.stackId===t.stackId&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),g6=rx([sZ,gX],(e,t)=>{if(e.some(e=>"bar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId&&t.stackId===e.stackId))return t}),g4=rx([(e,t,r,n)=>"horizontal"===nN(e)?s9(e,"yAxis",r,n):s9(e,"xAxis",t,n),gX],(e,t)=>{if(e&&(null==t?void 0:t.dataKey)!=null){var{stackId:r}=t;if(null!=r){var n=e[r];if(n){var{stackedData:i}=n;if(i)return i.find(e=>e.key===t.dataKey)}}}}),g7=rx([nx,(e,t,r,n)=>ui(e,"xAxis",t,n),(e,t,r,n)=>ui(e,"yAxis",r,n),(e,t,r,n)=>un(e,"xAxis",t,n),(e,t,r,n)=>un(e,"yAxis",r,n),g3,nN,ly,g2,g4,g6,(e,t,r,n,i,a)=>a],(e,t,r,n,i,a,o,l,s,c,u,f)=>{var d,{chartData:h,dataStartIndex:p,dataEndIndex:y}=l;if(null!=u&&null!=a&&("horizontal"===o||"vertical"===o)&&null!=t&&null!=r&&null!=n&&null!=i&&null!=s){var{data:v}=u;if(null!=(d=null!=v&&v.length>0?v:null==h?void 0:h.slice(p,y+1)))return function(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:o,yAxis:l,xAxisTicks:s,yAxisTicks:c,stackedData:u,displayedData:f,offset:d,cells:h}=e,p="horizontal"===t?l:o,y=u?p.scale.domain():null,v=r9({numericAxis:p});return f.map((e,f)=>{u?g=r6(u[f],y):Array.isArray(g=rJ(e,r))||(g=[v,g]);var p=gU(n,0)(g[1],f);if("horizontal"===t){var g,m,b,x,j,P,S,[A,E]=[l.scale(g[0]),l.scale(g[1])];m=r8({axis:o,ticks:s,bandSize:a,offset:i.offset,entry:e,index:f}),b=null!==(S=null!=E?E:A)&&void 0!==S?S:void 0,x=i.size;var M=A-E;if(j=O(M)?0:M,P={x:m,y:d.top,width:x,height:d.height},Math.abs(p)>0&&Math.abs(j)<Math.abs(p)){var k=w(j||p)*(Math.abs(p)-Math.abs(j));b-=k,j+=k}}else{var[T,_]=[o.scale(g[0]),o.scale(g[1])];if(m=T,b=r8({axis:l,ticks:c,bandSize:a,offset:i.offset,entry:e,index:f}),x=_-T,j=i.size,P={x:d.left,y:b,width:d.width,height:j},Math.abs(p)>0&&Math.abs(x)<Math.abs(p)){var N=w(x||p)*(Math.abs(p)-Math.abs(x));x+=N}}return mo(mo({},e),{},{x:m,y:b,width:x,height:j,value:u?g:g[1],payload:e,background:P,tooltipPosition:{x:m+x/2,y:b+j/2}},h&&h[f]&&h[f].props)})}({layout:o,barSettings:u,pos:a,bandSize:s,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:c,displayedData:d,offset:e,cells:f})}});function g8(e){var{legendPayload:t}=e;return rS(),nP(),null}function g9(e){var{legendPayload:t}=e;return rS(),rk(nN),null}function me(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,o.useRef)(E(t)),n=(0,o.useRef)(e);return n.current!==e&&(r.current=E(t),n.current=e),r.current}var mt=["onMouseEnter","onMouseLeave","onClick"],mr=["value","background","tooltipPosition"],mn=["onMouseEnter","onClick","onMouseLeave"];function mi(){return(mi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function ma(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mo(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ma(Object(r),!0).forEach(function(t){ml(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ma(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ml(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ms(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var mc=e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:nl(r,t),payload:e}]};function mu(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:nl(a,t),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function mf(e){var t=rk(u6),{data:r,dataKey:n,background:i,allOtherBarProps:a}=e,{onMouseEnter:l,onMouseLeave:s,onClick:c}=a,u=ms(a,mt),f=gK(l,n),d=gF(s),h=gW(c,n);if(!i||null==r)return null;var p=dU(i,!1);return o.createElement(o.Fragment,null,r.map((e,r)=>{var{value:a,background:l,tooltipPosition:s}=e,c=ms(e,mr);if(!l)return null;var y=f(e,r),v=d(e,r),g=h(e,r),m=mo(mo(mo(mo(mo({option:i,isActive:String(r)===t},c),{},{fill:"#eee"},l),p),dD(u,e,r)),{},{onMouseEnter:y,onMouseLeave:v,onClick:g,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return o.createElement(g$,mi({key:"background-bar-".concat(r)},m))}))}function md(e){var{data:t,props:r,showLabels:n}=e,i=dU(r,!1),{shape:a,dataKey:l,activeBar:s}=r,c=rk(u6),u=rk(u7),{onMouseEnter:f,onClick:d,onMouseLeave:h}=r,p=ms(r,mn),y=gK(f,l),v=gF(h),g=gW(d,l);return t?o.createElement(o.Fragment,null,t.map((e,t)=>{var r=s&&String(t)===c&&(null==u||l===u),n=mo(mo(mo({},i),e),{},{isActive:r,option:r?s:a,index:t,dataKey:l});return o.createElement(hM,mi({className:"recharts-bar-rectangle"},dD(p,e,t),{onMouseEnter:y(e,t),onMouseLeave:v(e,t),onClick:g(e,t),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(t)}),o.createElement(g$,n))}),n&&v8.renderCallByParent(r,t)):null}function mh(e){var{props:t,previousRectanglesRef:r}=e,{data:n,layout:i,isAnimationActive:a,animationBegin:l,animationDuration:s,animationEasing:c,onAnimationEnd:u,onAnimationStart:f}=t,d=r.current,h=me(t,"recharts-bar-"),[p,y]=(0,o.useState)(!1),v=(0,o.useCallback)(()=>{"function"==typeof u&&u(),y(!1)},[u]),g=(0,o.useCallback)(()=>{"function"==typeof f&&f(),y(!0)},[f]);return o.createElement(va,{begin:l,duration:s,isActive:a,easing:c,from:{t:0},to:{t:1},onAnimationEnd:v,onAnimationStart:g,key:h},e=>{var{t:a}=e,l=1===a?n:n.map((e,t)=>{var r=d&&d[t];if(r){var n=T(r.x,e.x),o=T(r.y,e.y),l=T(r.width,e.width),s=T(r.height,e.height);return mo(mo({},e),{},{x:n(a),y:o(a),width:l(a),height:s(a)})}if("horizontal"===i){var c=T(0,e.height)(a);return mo(mo({},e),{},{y:e.y+e.height-c,height:c})}var u=T(0,e.width)(a);return mo(mo({},e),{},{width:u})});return a>0&&(r.current=l),o.createElement(hM,null,o.createElement(md,{props:t,data:l,showLabels:!p}))})}function mp(e){var{data:t,isAnimationActive:r}=e,n=(0,o.useRef)(null);return r&&t&&t.length&&(null==n.current||n.current!==t)?o.createElement(mh,{previousRectanglesRef:n,props:e}):o.createElement(md,{props:e,data:t,showLabels:!0})}var my=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:rJ(e,t)}};class mv extends o.PureComponent{constructor(){super(...arguments),ml(this,"id",E("recharts-bar-"))}render(){var{hide:e,data:t,dataKey:r,className:n,xAxisId:i,yAxisId:a,needClip:l,background:s,id:c,layout:u}=this.props;if(e)return null;var f=(0,v.W)("recharts-bar",n),d=_(c)?this.id:c;return o.createElement(hM,{className:f},l&&o.createElement("defs",null,o.createElement(gZ,{clipPathId:d,xAxisId:i,yAxisId:a})),o.createElement(hM,{className:"recharts-bar-rectangles",clipPath:l?"url(#clipPath-".concat(d,")"):null},o.createElement(mf,{data:t,dataKey:r,background:s,allOtherBarProps:this.props}),o.createElement(mp,this.props)),o.createElement(vZ,{direction:"horizontal"===u?"y":"x"},this.props.children))}}var mg={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!hf.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function mm(e){var t,{xAxisId:r,yAxisId:n,hide:i,legendType:a,minPointSize:l,activeBar:s,animationBegin:c,animationDuration:u,animationEasing:f,isAnimationActive:d}=hi(e,mg),{needClip:h}=gH(r,n),p=nC(),y=nP(),v=(0,o.useMemo)(()=>{var t;return{barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:l,stackId:null==(t=e.stackId)?void 0:String(t)}},[e.barSize,e.dataKey,e.maxBarSize,l,e.stackId]),g=dB(e.children,vJ),m=rk(e=>g7(e,r,n,y,v,g));if("vertical"!==p&&"horizontal"!==p)return null;var b=null==m?void 0:m[0];return t=null==b||null==b.height||null==b.width?0:"vertical"===p?b.height/2:b.width/2,o.createElement(vB,{xAxisId:r,yAxisId:n,data:m,dataPointFormatter:my,errorBarOffset:t},o.createElement(mv,mi({},e,{layout:p,needClip:h,data:m,xAxisId:r,yAxisId:n,hide:i,legendType:a,minPointSize:l,activeBar:s,animationBegin:c,animationDuration:u,animationEasing:f,isAnimationActive:d})))}class mb extends o.PureComponent{render(){return o.createElement(vU,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},o.createElement(gV,null),o.createElement(g8,{legendPayload:mc(this.props)}),o.createElement(gq,{fn:mu,args:this.props}),o.createElement(mm,this.props))}}function mx(e){return rS(),null}ml(mb,"displayName","Bar"),ml(mb,"defaultProps",mg);var mw=["width","height","layout"];function mO(){return(mO=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var mj={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},mP=(0,o.forwardRef)(function(e,t){var r,n=hi(e.categoricalChartProps,mj),{width:i,height:a,layout:l}=n,s=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(n,mw);if(!lg(i)||!lg(a))return null;var{chartName:c,defaultTooltipEventType:u,validateTooltipEventTypes:f,tooltipPayloadSearcher:d}=e;return o.createElement(dj,{preloadedState:{options:{chartName:c,defaultTooltipEventType:u,validateTooltipEventTypes:f,tooltipPayloadSearcher:d,eventEmitter:void 0}},reduxStoreName:null!==(r=n.id)&&void 0!==r?r:c},o.createElement(dP,{chartData:n.data}),o.createElement(dS,{width:i,height:a,layout:l,margin:n.margin}),o.createElement(dA,{accessibilityLayer:n.accessibilityLayer,barCategoryGap:n.barCategoryGap,maxBarSize:n.maxBarSize,stackOffset:n.stackOffset,barGap:n.barGap,barSize:n.barSize,syncId:n.syncId,syncMethod:n.syncMethod,className:n.className}),o.createElement(mx,{cx:n.cx,cy:n.cy,startAngle:n.startAngle,endAngle:n.endAngle,innerRadius:n.innerRadius,outerRadius:n.outerRadius}),o.createElement(hr,mO({width:i,height:a},s,{ref:t})))}),mS=["item"],mA={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},mE=(0,o.forwardRef)((e,t)=>{var r=hi(e,mA);return o.createElement(mP,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:mS,tooltipPayloadSearcher:tS,categoricalChartProps:r,ref:t})}),mM=e=>e.graphicalItems.polarItems,mk=rx([sC,sD],sH),mT=rx([mM,sW,mk],sG),m_=rx([mT],s0),mN=rx([m_,lp],s2),mC=rx([mN,sW,mT],s3),mD=rx([mN,sW,mT],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var n;return{value:rJ(e,null!==(n=t.dataKey)&&void 0!==n?n:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:rJ(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),mI=()=>void 0,mR=rx([sW,cj,mI,mD,mI],cP),mL=rx([sW,nN,mN,mC,sd,sC,mR],cE),mz=rx([mL,sW,cT],cN);function mB(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m$(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mB(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mB(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}rx([sW,mL,mz,sC],cD);var mU=(e,t)=>t,mK=[],mF=(e,t,r)=>(null==r?void 0:r.length)===0?mK:r,mW=rx([lp,mU,mF],(e,t,r)=>{var n,{chartData:i}=e;if((n=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:i)&&n.length||null==r||(n=r.map(e=>m$(m$({},t.presentationProps),e.props))),null!=n)return n}),mq=rx([mW,mU,mF],(e,t,r)=>{if(null!=e)return e.map((e,n)=>{var i,a,o=rJ(e,t.nameKey,t.name);return a=null!=r&&null!==(i=r[n])&&void 0!==i&&null!==(i=i.props)&&void 0!==i&&i.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:nl(o,t.dataKey),color:a,payload:e,type:t.legendType}})}),mV=rx([mM,mU],(e,t)=>{if(e.some(e=>"pie"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),mH=rx([mW,mV,mF,nx],(e,t,r,n)=>{if(null!=t&&null!=e)return function(e){var t,r,n,{pieSettings:i,displayedData:a,cells:o,offset:l}=e,{cornerRadius:s,startAngle:c,endAngle:u,dataKey:f,nameKey:d,tooltipType:h}=i,p=Math.abs(i.minAngle),y=m3(c,u),v=Math.abs(y),g=a.length<=1?0:null!==(t=i.paddingAngle)&&void 0!==t?t:0,m=a.filter(e=>0!==rJ(e,f,0)).length,b=v-m*p-(v>=360?m:m-1)*g,x=a.reduce((e,t)=>{var r=rJ(t,f,0);return e+(P(r)?r:0)},0);return x>0&&(r=a.map((e,t)=>{var r,a=rJ(e,f,0),u=rJ(e,d,t),v=m5(i,l,e),m=(P(a)?a:0)/x,O=mG(mG({},e),o&&o[t]&&o[t].props),j=(r=t?n.endAngle+w(y)*g*(0!==a?1:0):c)+w(y)*((0!==a?p:0)+m*b),S=(r+j)/2,A=(v.innerRadius+v.outerRadius)/2,E=[{name:u,value:a,payload:O,dataKey:f,type:h}],M=rF(v.cx,v.cy,A,S);return n=mG(mG(mG(mG({},i.presentationProps),{},{percent:m,cornerRadius:s,name:u,tooltipPayload:E,midAngle:S,middleRadius:A,tooltipPosition:M},O),v),{},{value:rJ(e,f),startAngle:r,endAngle:j,payload:O,paddingAngle:w(y)*g})})),r}({offset:n,pieSettings:t,displayedData:e,cells:r})}),mZ=["onMouseEnter","onClick","onMouseLeave"];function mY(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mG(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mY(Object(r),!0).forEach(function(t){mX(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mY(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function mX(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mJ(){return(mJ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function mQ(e){var t=(0,o.useMemo)(()=>dU(e,!1),[e]),r=(0,o.useMemo)(()=>dB(e.children,vJ),[e.children]),n=(0,o.useMemo)(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:t}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,t]),i=rk(e=>mq(e,n,r));return o.createElement(g9,{legendPayload:i})}function m0(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:a,fill:o,name:l,hide:s,tooltipType:c}=e;return{dataDefinedOnItem:null==n?void 0:n.map(e=>e.tooltipPayload),positions:null==n?void 0:n.map(e=>e.tooltipPosition),settings:{stroke:i,strokeWidth:a,fill:o,dataKey:t,nameKey:r,name:nl(l,t),hide:s,type:c,color:o,unit:""}}}var m1=(e,t)=>e>t?"start":e<t?"end":"middle",m2=(e,t,r)=>"function"==typeof t?t(e):M(t,r,.8*r),m5=(e,t,r)=>{var{top:n,left:i,width:a,height:o}=t,l=rW(a,o),s=i+M(e.cx,a,a/2),c=n+M(e.cy,o,o/2);return{cx:s,cy:c,innerRadius:M(e.innerRadius,l,0),outerRadius:m2(r,e.outerRadius,l),maxRadius:e.maxRadius||Math.sqrt(a*a+o*o)/2}},m3=(e,t)=>w(t-e)*Math.min(Math.abs(t-e),360),m6=(e,t)=>{if(o.isValidElement(e))return o.cloneElement(e,t);if("function"==typeof e)return e(t);var r=(0,v.W)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return o.createElement(yD,mJ({},t,{type:"linear",className:r}))},m4=(e,t,r)=>{if(o.isValidElement(e))return o.cloneElement(e,t);var n=r;if("function"==typeof e&&(n=e(t),o.isValidElement(n)))return n;var i=(0,v.W)("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return o.createElement(hY,mJ({},t,{alignmentBaseline:"middle",className:i}),n)};function m7(e){var{sectors:t,props:r,showLabels:n}=e,{label:i,labelLine:a,dataKey:l}=r;if(!n||!i||!t)return null;var s=dU(r,!1),c=dU(i,!1),u=dU(a,!1),f="object"==typeof i&&"offsetRadius"in i&&i.offsetRadius||20,d=t.map((e,t)=>{var r=(e.startAngle+e.endAngle)/2,n=rF(e.cx,e.cy,e.outerRadius+f,r),d=mG(mG(mG(mG({},s),e),{},{stroke:"none"},c),{},{index:t,textAnchor:m1(n.x,e.cx)},n),h=mG(mG(mG(mG({},s),e),{},{fill:"none",stroke:e.fill},u),{},{index:t,points:[rF(e.cx,e.cy,e.outerRadius,r),n],key:"line"});return o.createElement(hM,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},a&&m6(a,h),m4(i,d,rJ(e,l)))});return o.createElement(hM,{className:"recharts-pie-labels"},d)}function m8(e){var{sectors:t,activeShape:r,inactiveShape:n,allOtherPieProps:i,showLabels:a}=e,l=rk(u6),{onMouseEnter:s,onClick:c,onMouseLeave:u}=i,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,mZ),d=gK(s,i.dataKey),h=gF(u),p=gW(c,i.dataKey);return null==t?null:o.createElement(o.Fragment,null,t.map((e,a)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var s=r&&String(a)===l,c=s?r:l?n:null,u=mG(mG({},e),{},{stroke:e.stroke,tabIndex:-1,[nv]:a,[ng]:i.dataKey});return o.createElement(hM,mJ({tabIndex:-1,className:"recharts-pie-sector"},dD(f,e,a),{onMouseEnter:d(e,a),onMouseLeave:h(e,a),onClick:p(e,a),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(a)}),o.createElement(gD,mJ({option:c,isActive:s,shapeType:"sector"},u)))}),o.createElement(m7,{sectors:t,props:i,showLabels:a}))}function m9(e){var{props:t,previousSectorsRef:r}=e,{sectors:n,isAnimationActive:i,animationBegin:a,animationDuration:l,animationEasing:s,activeShape:c,inactiveShape:u,onAnimationStart:f,onAnimationEnd:d}=t,h=me(t,"recharts-pie-"),p=r.current,[y,v]=(0,o.useState)(!0),g=(0,o.useCallback)(()=>{"function"==typeof d&&d(),v(!1)},[d]),m=(0,o.useCallback)(()=>{"function"==typeof f&&f(),v(!0)},[f]);return o.createElement(va,{begin:a,duration:l,isActive:i,easing:s,from:{t:0},to:{t:1},onAnimationStart:m,onAnimationEnd:g,key:h},e=>{var{t:i}=e,a=[],l=(n&&n[0]).startAngle;return n.forEach((e,t)=>{var r=p&&p[t],n=t>0?x()(e,"paddingAngle",0):0;if(r){var o=T(r.endAngle-r.startAngle,e.endAngle-e.startAngle),s=mG(mG({},e),{},{startAngle:l+n,endAngle:l+o(i)+n});a.push(s),l=s.endAngle}else{var{endAngle:c,startAngle:u}=e,f=T(0,c-u)(i),d=mG(mG({},e),{},{startAngle:l+n,endAngle:l+f+n});a.push(d),l=d.endAngle}}),r.current=a,o.createElement(hM,null,o.createElement(m8,{sectors:a,activeShape:c,inactiveShape:u,allOtherPieProps:t,showLabels:!y}))})}function be(e){var{sectors:t,isAnimationActive:r,activeShape:n,inactiveShape:i}=e,a=(0,o.useRef)(null),l=a.current;return r&&t&&t.length&&(!l||l!==t)?o.createElement(m9,{props:e,previousSectorsRef:a}):o.createElement(m8,{sectors:t,activeShape:n,inactiveShape:i,allOtherPieProps:e,showLabels:!0})}function bt(e){var{hide:t,className:r,rootTabIndex:n}=e,i=(0,v.W)("recharts-pie",r);return t?null:o.createElement(hM,{tabIndex:n,className:i},o.createElement(be,e))}var br={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!hf.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function bn(e){var t=hi(e,br),r=(0,o.useMemo)(()=>dB(e.children,vJ),[e.children]),n=dU(t,!1),i=(0,o.useMemo)(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:n}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,n]),a=rk(e=>mH(e,i,r));return o.createElement(o.Fragment,null,o.createElement(gq,{fn:m0,args:mG(mG({},t),{},{sectors:a})}),o.createElement(bt,mJ({},t,{sectors:a})))}class bi extends o.PureComponent{constructor(){super(...arguments),mX(this,"id",E("recharts-pie-"))}render(){return o.createElement(o.Fragment,null,o.createElement(vD,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),o.createElement(mQ,this.props),o.createElement(bn,this.props),this.props.children)}}mX(bi,"displayName","Pie"),mX(bi,"defaultProps",br);var ba=r(37202),bo=r(71821);let bl=(0,u.Z)("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]),bs=(0,u.Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),bc=()=>{let[e,t]=(0,o.useState)(null),[r,n]=(0,o.useState)(!0);(0,o.useEffect)(()=>{i();let e=setInterval(i,6e4);return()=>clearInterval(e)},[]);let i=async()=>{try{t({storage:{used:3.2,limit:5,cost:0},traffic:{used:12.5,limit:5,cost:7.5*.18},requests:{used:85e4,limit:1e7,cost:0},totalCost:1.35,projectedCost:4.05}),n(!1)}catch(e){console.error("加载成本数据失败:",e),n(!1)}},l=e=>e>=1e6?`${(e/1e6).toFixed(1)}M`:e>=1e3?`${(e/1e3).toFixed(1)}K`:e.toString(),c=(e,t)=>{let r=e/t*100;return r>=90?"text-red-600":r>=70?"text-yellow-600":"text-green-600"},u=(e,t)=>{let r=e/t*100;return r>=90?"bg-red-500":r>=70?"bg-yellow-500":"bg-green-500"};if(r)return a.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[a.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx("div",{className:"h-3 bg-gray-200 rounded"}),a.jsx("div",{className:"h-3 bg-gray-200 rounded w-5/6"}),a.jsx("div",{className:"h-3 bg-gray-200 rounded w-4/6"})]})]})});if(!e)return a.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,a.jsxs)("div",{className:"text-center text-gray-500",children:[a.jsx(ba.Z,{className:"w-8 h-8 mx-auto mb-2"}),a.jsx("p",{children:"无法加载成本数据"})]})});let f=[{name:"存储",免费额度:e.storage.limit,已使用:e.storage.used},{name:"流量",免费额度:e.traffic.limit,已使用:e.traffic.used},{name:"请求",免费额度:e.requests.limit/1e6,已使用:e.requests.used/1e6}],d=[{name:"存储费用",value:e.storage.cost,color:"#8884d8"},{name:"流量费用",value:e.traffic.cost,color:"#82ca9d"},{name:"请求费用",value:e.requests.cost,color:"#ffc658"}];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[a.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(bo.Z,{className:"w-8 h-8 text-green-500 mr-3"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-600",children:"本月成本"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["\xa5",e.totalCost.toFixed(2)]})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(s.Z,{className:"w-8 h-8 text-blue-500 mr-3"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-600",children:"预测成本"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["\xa5",e.projectedCost.toFixed(2)]})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(bl,{className:"w-8 h-8 text-purple-500 mr-3"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-600",children:"存储使用"}),(0,a.jsxs)("p",{className:`text-2xl font-bold ${c(e.storage.used,e.storage.limit)}`,children:[(e.storage.used/e.storage.limit*100).toFixed(1),"%"]})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(bs,{className:"w-8 h-8 text-orange-500 mr-3"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-600",children:"流量使用"}),(0,a.jsxs)("p",{className:`text-2xl font-bold ${c(e.traffic.used,e.traffic.limit)}`,children:[(e.traffic.used/e.traffic.limit*100).toFixed(1),"%"]})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[a.jsx("h3",{className:"text-lg font-semibold mb-4",children:"资源使用情况"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[a.jsx("span",{className:"text-sm font-medium",children:"存储空间"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[e.storage.used.toFixed(2),"GB / ",e.storage.limit,"GB"]})]}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:a.jsx("div",{className:`h-2 rounded-full ${u(e.storage.used,e.storage.limit)}`,style:{width:`${Math.min(e.storage.used/e.storage.limit*100,100)}%`}})}),e.storage.cost>0&&(0,a.jsxs)("p",{className:"text-xs text-red-600 mt-1",children:["超出免费额度，费用: \xa5",e.storage.cost.toFixed(2)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[a.jsx("span",{className:"text-sm font-medium",children:"CDN流量"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[e.traffic.used.toFixed(2),"GB / ",e.traffic.limit,"GB"]})]}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:a.jsx("div",{className:`h-2 rounded-full ${u(e.traffic.used,e.traffic.limit)}`,style:{width:`${Math.min(e.traffic.used/e.traffic.limit*100,100)}%`}})}),e.traffic.cost>0&&(0,a.jsxs)("p",{className:"text-xs text-red-600 mt-1",children:["超出免费额度，费用: \xa5",e.traffic.cost.toFixed(2)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[a.jsx("span",{className:"text-sm font-medium",children:"API请求"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[l(e.requests.used)," / ",l(e.requests.limit)]})]}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:a.jsx("div",{className:`h-2 rounded-full ${u(e.requests.used,e.requests.limit)}`,style:{width:`${e.requests.used/e.requests.limit*100}%`}})}),e.requests.cost>0&&(0,a.jsxs)("p",{className:"text-xs text-red-600 mt-1",children:["超出免费额度，费用: \xa5",e.requests.cost.toFixed(2)]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[a.jsx("h3",{className:"text-lg font-semibold mb-4",children:"使用量对比"}),a.jsx(R,{width:"100%",height:250,children:(0,a.jsxs)(hu,{data:f,children:[a.jsx(pE,{strokeDasharray:"3 3"}),a.jsx(pR,{dataKey:"name"}),a.jsx(pq,{}),a.jsx(vN,{}),a.jsx(mb,{dataKey:"免费额度",fill:"#e5e7eb"}),a.jsx(mb,{dataKey:"已使用",fill:"#3b82f6"})]})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[a.jsx("h3",{className:"text-lg font-semibold mb-4",children:"成本构成"}),a.jsx(R,{width:"100%",height:200,children:(0,a.jsxs)(mE,{children:[a.jsx(bi,{data:d.filter(e=>e.value>0),cx:"50%",cy:"50%",outerRadius:80,dataKey:"value",label:({name:e,value:t})=>`${e}: \xa5${(t||0).toFixed(2)}`,children:d.map((e,t)=>a.jsx(vJ,{fill:e.color},`cell-${t}`))}),a.jsx(vN,{})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[a.jsx("h3",{className:"text-lg font-semibold mb-4",children:"优化建议"}),(0,a.jsxs)("div",{className:"space-y-3",children:[e.storage.used/e.storage.limit>.8&&a.jsx("div",{className:"p-3 bg-yellow-50 border border-yellow-200 rounded",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-800",children:[a.jsx("strong",{children:"存储空间即将用完"}),a.jsx("br",{}),"建议启用图片压缩或迁移到云存储"]})}),e.traffic.used>e.traffic.limit&&a.jsx("div",{className:"p-3 bg-red-50 border border-red-200 rounded",children:(0,a.jsxs)("p",{className:"text-sm text-red-800",children:[a.jsx("strong",{children:"流量已超出免费额度"}),a.jsx("br",{}),"建议启用CDN缓存或图片懒加载"]})}),0===e.totalCost&&a.jsx("div",{className:"p-3 bg-green-50 border border-green-200 rounded",children:(0,a.jsxs)("p",{className:"text-sm text-green-800",children:[a.jsx("strong",{children:"当前在免费额度内"}),a.jsx("br",{}),"继续保持当前的使用模式"]})})]})]})]})]})},bu=()=>{let[e,t]=(0,o.useState)(null),[r,n]=(0,o.useState)(null),[i,u]=(0,o.useState)(!0),[v,g]=(0,o.useState)("performance");(0,o.useEffect)(()=>{m();let e=setInterval(m,5e3);return()=>clearInterval(e)},[]);let m=()=>{try{let e=h.Bm.getMetrics(),r=h.Bm.getPerformanceReport(),i=h.Bm.getPerformanceScore();t({metrics:e,report:r,score:i}),n({dataCache:p.dataCache.getStats(),imageCache:p.U_.getStats()}),u(!1)}catch(e){console.error("加载性能数据失败:",e),u(!1)}};return i?a.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(l.Z,{className:"w-8 h-8 animate-spin mx-auto mb-4 text-blue-500"}),a.jsx("p",{className:"text-gray-600",children:"加载性能数据中..."})]})}):a.jsx("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"性能监控中心"}),a.jsx("p",{className:"text-gray-600",children:"实时监控应用性能指标、缓存状态和成本分析"}),(0,a.jsxs)("div",{className:"mt-4 flex gap-1 bg-gray-100 p-1 rounded-lg w-fit",children:[a.jsx("button",{onClick:()=>g("performance"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${"performance"===v?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:"性能监控"}),a.jsx("button",{onClick:()=>g("cost"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${"cost"===v?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:"成本分析"})]}),(0,a.jsxs)("div",{className:"mt-4 flex gap-4",children:[(0,a.jsxs)(y.Z,{onClick:m,variant:"outline",children:[a.jsx(l.Z,{className:"w-4 h-4 mr-2"}),"刷新数据"]}),(0,a.jsxs)(y.Z,{onClick:()=>{let e=new Blob([h.Bm.exportData()],{type:"application/json"}),t=URL.createObjectURL(e),r=document.createElement("a");r.href=t,r.download=`performance-report-${new Date().toISOString().split("T")[0]}.json`,r.click(),URL.revokeObjectURL(t)},variant:"outline",children:[a.jsx(s.Z,{className:"w-4 h-4 mr-2"}),"导出报告"]}),(0,a.jsxs)(y.Z,{onClick:()=>{p.dataCache.clear(),p.U_.clear(),n({dataCache:p.dataCache.getStats(),imageCache:p.U_.getStats()})},variant:"outline",children:[a.jsx(c.Z,{className:"w-4 h-4 mr-2"}),"清理缓存"]})]})]}),"performance"===v&&(0,a.jsxs)(a.Fragment,{children:[e?.score&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[a.jsx(f,{className:"w-5 h-5 mr-2 text-yellow-500"}),"性能评分"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:`text-3xl font-bold mb-2 ${e.score.overall>=80?"text-green-500":e.score.overall>=60?"text-yellow-500":"text-red-500"}`,children:e.score.overall}),a.jsx("div",{className:"text-sm text-gray-600",children:"总体评分"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:`text-2xl font-bold mb-2 ${e.score.breakdown.loading>=80?"text-green-500":e.score.breakdown.loading>=60?"text-yellow-500":"text-red-500"}`,children:e.score.breakdown.loading}),a.jsx("div",{className:"text-sm text-gray-600",children:"加载性能"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:`text-2xl font-bold mb-2 ${e.score.breakdown.interactivity>=80?"text-green-500":e.score.breakdown.interactivity>=60?"text-yellow-500":"text-red-500"}`,children:e.score.breakdown.interactivity}),a.jsx("div",{className:"text-sm text-gray-600",children:"交互性能"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:`text-2xl font-bold mb-2 ${e.score.breakdown.visualStability>=80?"text-green-500":e.score.breakdown.visualStability>=60?"text-yellow-500":"text-red-500"}`,children:e.score.breakdown.visualStability}),a.jsx("div",{className:"text-sm text-gray-600",children:"视觉稳定性"})]})]})]}),e?.report?.coreWebVitals&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[a.jsx(d.Z,{className:"w-5 h-5 mr-2 text-blue-500"}),"Core Web Vitals"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-blue-600 mb-2",children:[e.report.coreWebVitals.lcp,"ms"]}),a.jsx("div",{className:"text-sm text-gray-600 mb-1",children:"LCP (最大内容绘制)"}),a.jsx("div",{className:`text-xs ${e.report.coreWebVitals.lcp<=2500?"text-green-500":e.report.coreWebVitals.lcp<=4e3?"text-yellow-500":"text-red-500"}`,children:e.report.coreWebVitals.lcp<=2500?"优秀":e.report.coreWebVitals.lcp<=4e3?"需要改进":"较差"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-green-600 mb-2",children:[e.report.coreWebVitals.fid,"ms"]}),a.jsx("div",{className:"text-sm text-gray-600 mb-1",children:"FID (首次输入延迟)"}),a.jsx("div",{className:`text-xs ${e.report.coreWebVitals.fid<=100?"text-green-500":e.report.coreWebVitals.fid<=300?"text-yellow-500":"text-red-500"}`,children:e.report.coreWebVitals.fid<=100?"优秀":e.report.coreWebVitals.fid<=300?"需要改进":"较差"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[a.jsx("div",{className:"text-2xl font-bold text-purple-600 mb-2",children:e.report.coreWebVitals.cls}),a.jsx("div",{className:"text-sm text-gray-600 mb-1",children:"CLS (累积布局偏移)"}),a.jsx("div",{className:`text-xs ${e.report.coreWebVitals.cls<=.1?"text-green-500":e.report.coreWebVitals.cls<=.25?"text-yellow-500":"text-red-500"}`,children:e.report.coreWebVitals.cls<=.1?"优秀":e.report.coreWebVitals.cls<=.25?"需要改进":"较差"})]})]})]}),r&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[a.jsx(c.Z,{className:"w-5 h-5 mr-2 text-green-500"}),"缓存统计"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[a.jsx("h3",{className:"font-semibold mb-3",children:"数据缓存"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{children:"缓存大小:"}),(0,a.jsxs)("span",{className:"font-medium",children:[r.dataCache.size,"/",r.dataCache.maxSize]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{children:"命中率:"}),(0,a.jsxs)("span",{className:"font-medium text-green-600",children:[r.dataCache.hitRate.toFixed(1),"%"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{children:"过期条目:"}),a.jsx("span",{className:"font-medium text-yellow-600",children:r.dataCache.expired})]})]})]}),(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[a.jsx("h3",{className:"font-semibold mb-3",children:"图片缓存"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{children:"缓存大小:"}),(0,a.jsxs)("span",{className:"font-medium",children:[r.imageCache.size,"/",r.imageCache.maxSize]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{children:"命中率:"}),(0,a.jsxs)("span",{className:"font-medium text-green-600",children:[r.imageCache.hitRate.toFixed(1),"%"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{children:"过期条目:"}),a.jsx("span",{className:"font-medium text-yellow-600",children:r.imageCache.expired})]})]})]})]})]}),e?.report?.apiPerformance&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[a.jsx(l.Z,{className:"w-5 h-5 mr-2 text-orange-500"}),"API性能统计"]}),a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-sm",children:[a.jsx("thead",{children:(0,a.jsxs)("tr",{className:"border-b",children:[a.jsx("th",{className:"text-left py-2",children:"API名称"}),a.jsx("th",{className:"text-right py-2",children:"调用次数"}),a.jsx("th",{className:"text-right py-2",children:"平均响应时间"}),a.jsx("th",{className:"text-right py-2",children:"最大响应时间"}),a.jsx("th",{className:"text-right py-2",children:"最小响应时间"})]})}),a.jsx("tbody",{children:Object.entries(e.report.apiPerformance).map(([e,t])=>(0,a.jsxs)("tr",{className:"border-b",children:[a.jsx("td",{className:"py-2 font-medium",children:e}),a.jsx("td",{className:"text-right py-2",children:t.callCount}),a.jsx("td",{className:"text-right py-2",children:(0,a.jsxs)("span",{className:`${t.avgResponseTime<=500?"text-green-600":t.avgResponseTime<=1e3?"text-yellow-600":"text-red-600"}`,children:[t.avgResponseTime,"ms"]})}),(0,a.jsxs)("td",{className:"text-right py-2",children:[t.maxResponseTime,"ms"]}),(0,a.jsxs)("td",{className:"text-right py-2",children:[t.minResponseTime,"ms"]})]},e))})]})})]})]}),"cost"===v&&a.jsx(bc,{})]})})}},99837:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var n=r(10326),i=r(17577),a=r.n(i),o=r(28295);let l=a().forwardRef(({className:e,variant:t="primary",size:r="md",loading:i=!1,icon:a,children:l,disabled:s,...c},u)=>(0,n.jsxs)("button",{className:(0,o.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[t],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[r],e),ref:u,disabled:s||i,...c,children:[i&&(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[n.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),n.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!i&&a&&n.jsx("span",{className:"mr-2",children:a}),l]}));l.displayName="Button";let s=l},12670:(e,t,r)=>{"use strict";r.d(t,{U_:()=>a,dataCache:()=>o});class n{constructor(e={}){this.cache=new Map,this.config={ttl:3e5,maxSize:100,strategy:"LRU",...e}}set(e,t,r){let n=Date.now(),i=r||this.config.ttl;this.cache.size>=this.config.maxSize&&this.evict(),this.cache.set(e,{data:t,timestamp:n,ttl:i,accessCount:0,lastAccess:n})}get(e){let t=this.cache.get(e);if(!t)return null;let r=Date.now();return r-t.timestamp>t.ttl?(this.cache.delete(e),null):(t.accessCount++,t.lastAccess=r,t.data)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}evict(){let e;if(0!==this.cache.size){switch(this.config.strategy){case"LRU":e=this.findLRUKey();break;case"FIFO":e=this.findFIFOKey();break;case"TTL":e=this.findExpiredKey();break;default:e=this.cache.keys().next().value}e&&this.cache.delete(e)}}findLRUKey(){let e;let t=Date.now();return this.cache.forEach((r,n)=>{r.lastAccess<t&&(t=r.lastAccess,e=n)}),e}findFIFOKey(){let e;let t=Date.now();return this.cache.forEach((r,n)=>{r.timestamp<t&&(t=r.timestamp,e=n)}),e}findExpiredKey(){let e;let t=Date.now();return(this.cache.forEach((r,n)=>{if(t-r.timestamp>r.ttl){e=n;return}}),e)?e:this.findLRUKey()}getStats(){let e=Date.now(),t=0,r=0;return this.cache.forEach(n=>{e-n.timestamp>n.ttl&&t++,r+=JSON.stringify(n.data).length}),{size:this.cache.size,maxSize:this.config.maxSize,expired:t,totalSize:r,hitRate:this.calculateHitRate()}}calculateHitRate(){let e=0;return this.cache.forEach(t=>{e+=t.accessCount}),e>0?this.cache.size/e*100:0}}class i{constructor(e="pet_cache_",t=!1){this.prefix=e,this.storage=null}set(e,t,r=864e5){if(!this.storage)return;let n={data:t,timestamp:Date.now(),ttl:r};try{this.storage.setItem(this.prefix+e,JSON.stringify(n))}catch(t){console.warn("浏览器存储空间不足，清理过期缓存"),this.cleanup();try{this.storage.setItem(this.prefix+e,JSON.stringify(n))}catch(e){console.error("缓存设置失败:",e)}}}get(e){if(!this.storage)return null;try{let t=this.storage.getItem(this.prefix+e);if(!t)return null;let r=JSON.parse(t);if(Date.now()-r.timestamp>r.ttl)return this.storage.removeItem(this.prefix+e),null;return r.data}catch(e){return console.error("缓存读取失败:",e),null}}delete(e){this.storage&&this.storage.removeItem(this.prefix+e)}cleanup(){if(!this.storage)return;let e=Date.now(),t=[];for(let r=0;r<this.storage.length;r++){let n=this.storage.key(r);if(n&&n.startsWith(this.prefix))try{let r=this.storage.getItem(n);if(r){let i=JSON.parse(r);e-i.timestamp>i.ttl&&t.push(n)}}catch(e){t.push(n)}}t.forEach(e=>this.storage.removeItem(e))}clear(){if(!this.storage)return;let e=[];for(let t=0;t<this.storage.length;t++){let r=this.storage.key(t);r&&r.startsWith(this.prefix)&&e.push(r)}e.forEach(e=>this.storage.removeItem(e))}}let a=new n({ttl:18e5,maxSize:50,strategy:"LRU"}),o=new n({ttl:3e5,maxSize:100,strategy:"LRU"});new i("pet_app_")},89453:(e,t,r)=>{"use strict";r.d(t,{Bm:()=>i});class n{constructor(){this.observers=[],this.metrics={pageLoadTime:0,firstContentfulPaint:0,largestContentfulPaint:0,firstInputDelay:0,cumulativeLayoutShift:0,imageLoadTimes:[],apiResponseTimes:new Map},this.initializeObservers()}static getInstance(){return n.instance||(n.instance=new n),n.instance}initializeObservers(){}observeNavigation(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{"navigation"===e.entryType&&(this.metrics.pageLoadTime=e.loadEventEnd-e.fetchStart)})});e.observe({entryTypes:["navigation"]}),this.observers.push(e)}}observePaint(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{"first-contentful-paint"===e.name&&(this.metrics.firstContentfulPaint=e.startTime)})});e.observe({entryTypes:["paint"]}),this.observers.push(e)}if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{let t=e.getEntries(),r=t[t.length-1];this.metrics.largestContentfulPaint=r.startTime});e.observe({entryTypes:["largest-contentful-paint"]}),this.observers.push(e)}}observeLayoutShift(){if("PerformanceObserver"in window){let e=0,t=new PerformanceObserver(t=>{t.getEntries().forEach(t=>{t.hadRecentInput||(e+=t.value,this.metrics.cumulativeLayoutShift=e)})});t.observe({entryTypes:["layout-shift"]}),this.observers.push(t)}}observeFirstInputDelay(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{this.metrics.firstInputDelay=e.processingStart-e.startTime})});e.observe({entryTypes:["first-input"]}),this.observers.push(e)}}observeResources(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{if("img"===e.initiatorType){let t=e.responseEnd-e.startTime;this.metrics.imageLoadTimes.push(t)}})});e.observe({entryTypes:["resource"]}),this.observers.push(e)}}recordApiResponse(e,t){this.metrics.apiResponseTimes.has(e)||this.metrics.apiResponseTimes.set(e,[]),this.metrics.apiResponseTimes.get(e).push(t)}recordImageLoad(e){this.metrics.imageLoadTimes.push(e)}getMemoryUsage(){return"memory"in performance?performance.memory:null}getMetrics(){return{...this.metrics,memoryUsage:this.getMemoryUsage()||void 0}}getPerformanceReport(){let e={};this.metrics.apiResponseTimes.forEach((t,r)=>{let n=t.reduce((e,t)=>e+t,0)/t.length;e[r]={avgResponseTime:Math.round(n),callCount:t.length,maxResponseTime:Math.round(Math.max(...t)),minResponseTime:Math.round(Math.min(...t))}});let t=this.metrics.imageLoadTimes.length>0?this.metrics.imageLoadTimes.reduce((e,t)=>e+t,0)/this.metrics.imageLoadTimes.length:0;return{coreWebVitals:{lcp:Math.round(this.metrics.largestContentfulPaint),fid:Math.round(this.metrics.firstInputDelay),cls:Math.round(1e3*this.metrics.cumulativeLayoutShift)/1e3},loadingPerformance:{pageLoadTime:Math.round(this.metrics.pageLoadTime),fcp:Math.round(this.metrics.firstContentfulPaint),avgImageLoadTime:Math.round(t)},apiPerformance:e,memoryUsage:this.getMemoryUsage()||void 0}}getPerformanceScore(){let e=this.metrics.largestContentfulPaint<=2500?100:this.metrics.largestContentfulPaint<=4e3?50:0,t=this.metrics.firstInputDelay<=100?100:this.metrics.firstInputDelay<=300?50:0,r=this.metrics.cumulativeLayoutShift<=.1?100:this.metrics.cumulativeLayoutShift<=.25?50:0;return{overall:Math.round((e+t+r)/3),breakdown:{loading:e,interactivity:t,visualStability:r}}}cleanup(){this.observers.forEach(e=>e.disconnect()),this.observers=[]}exportData(){return JSON.stringify({timestamp:new Date().toISOString(),url:window.location.href,userAgent:navigator.userAgent,metrics:this.getMetrics(),report:this.getPerformanceReport(),score:this.getPerformanceScore()},null,2)}}let i=n.getInstance()},9457:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\admin\layout.tsx#default`)},39941:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\admin\performance\page.tsx#default`)},23824:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[276,201,240],()=>r(97339));module.exports=n})();