const cloudbase = require('@cloudbase/node-sdk');

const app = cloudbase.init({
  env: cloudbase.SYMBOL_CURRENT_ENV
});

const db = app.database();

// 创建数据库索引以优化查询性能
async function createOptimizedIndexes() {
  try {
    const results = [];

    // 1. posts集合的复合索引
    const postIndexes = [
      // 状态 + 创建时间（最常用的查询）
      {
        name: 'status_created_at',
        keys: [
          { name: 'status', direction: '1' },
          { name: 'created_at', direction: '-1' }
        ],
        unique: false
      },
      
      // 分类 + 状态 + 创建时间
      {
        name: 'category_status_created_at',
        keys: [
          { name: 'category', direction: '1' },
          { name: 'status', direction: '1' },
          { name: 'created_at', direction: '-1' }
        ],
        unique: false
      },
      
      // 类型 + 状态 + 创建时间
      {
        name: 'type_status_created_at',
        keys: [
          { name: 'type', direction: '1' },
          { name: 'status', direction: '1' },
          { name: 'created_at', direction: '-1' }
        ],
        unique: false
      },
      
      // 地理位置 + 状态 + 创建时间
      {
        name: 'location_status_created_at',
        keys: [
          { name: 'location', direction: '1' },
          { name: 'status', direction: '1' },
          { name: 'created_at', direction: '-1' }
        ],
        unique: false
      },
      
      // 优先级分数 + 状态 + 创建时间
      {
        name: 'priority_score_status_created_at',
        keys: [
          { name: 'priority_score', direction: '-1' },
          { name: 'status', direction: '1' },
          { name: 'created_at', direction: '-1' }
        ],
        unique: false
      },
      
      // 点赞数 + 状态 + 创建时间
      {
        name: 'likes_count_status_created_at',
        keys: [
          { name: 'likes_count', direction: '-1' },
          { name: 'status', direction: '1' },
          { name: 'created_at', direction: '-1' }
        ],
        unique: false
      },
      
      // 想要数 + 状态 + 创建时间
      {
        name: 'wants_count_status_created_at',
        keys: [
          { name: 'wants_count', direction: '-1' },
          { name: 'status', direction: '1' },
          { name: 'created_at', direction: '-1' }
        ],
        unique: false
      },
      
      // 平均评分 + 状态 + 创建时间
      {
        name: 'avg_rating_status_created_at',
        keys: [
          { name: 'avg_rating', direction: '-1' },
          { name: 'status', direction: '1' },
          { name: 'created_at', direction: '-1' }
        ],
        unique: false
      },
      
      // 用户ID + 状态 + 创建时间（用户帖子查询）
      {
        name: 'user_id_status_created_at',
        keys: [
          { name: 'user_id', direction: '1' },
          { name: 'status', direction: '1' },
          { name: 'created_at', direction: '-1' }
        ],
        unique: false
      },
      
      // 复合筛选索引：分类 + 类型 + 状态 + 创建时间
      {
        name: 'category_type_status_created_at',
        keys: [
          { name: 'category', direction: '1' },
          { name: 'type', direction: '1' },
          { name: 'status', direction: '1' },
          { name: 'created_at', direction: '-1' }
        ],
        unique: false
      }
    ];

    // 创建posts集合索引
    for (const index of postIndexes) {
      try {
        await db.collection('posts').createIndex({
          name: index.name,
          keys: index.keys,
          unique: index.unique
        });
        results.push({
          collection: 'posts',
          index: index.name,
          status: 'created'
        });
      } catch (error) {
        if (error.message && error.message.includes('already exists')) {
          results.push({
            collection: 'posts',
            index: index.name,
            status: 'already_exists'
          });
        } else {
          results.push({
            collection: 'posts',
            index: index.name,
            status: 'error',
            error: error.message
          });
        }
      }
    }

    // 2. categories集合索引
    const categoryIndexes = [
      {
        name: 'level_parent_id_order',
        keys: [
          { name: 'level', direction: '1' },
          { name: 'parent_id', direction: '1' },
          { name: 'order', direction: '1' }
        ],
        unique: false
      },
      {
        name: 'id_unique',
        keys: [
          { name: 'id', direction: '1' }
        ],
        unique: true
      }
    ];

    for (const index of categoryIndexes) {
      try {
        await db.collection('categories').createIndex({
          name: index.name,
          keys: index.keys,
          unique: index.unique
        });
        results.push({
          collection: 'categories',
          index: index.name,
          status: 'created'
        });
      } catch (error) {
        if (error.message && error.message.includes('already exists')) {
          results.push({
            collection: 'categories',
            index: index.name,
            status: 'already_exists'
          });
        } else {
          results.push({
            collection: 'categories',
            index: index.name,
            status: 'error',
            error: error.message
          });
        }
      }
    }

    // 3. users集合索引
    const userIndexes = [
      {
        name: 'email_unique',
        keys: [
          { name: 'email', direction: '1' }
        ],
        unique: true
      },
      {
        name: 'created_at',
        keys: [
          { name: 'created_at', direction: '-1' }
        ],
        unique: false
      }
    ];

    for (const index of userIndexes) {
      try {
        await db.collection('users').createIndex({
          name: index.name,
          keys: index.keys,
          unique: index.unique
        });
        results.push({
          collection: 'users',
          index: index.name,
          status: 'created'
        });
      } catch (error) {
        if (error.message && error.message.includes('already exists')) {
          results.push({
            collection: 'users',
            index: index.name,
            status: 'already_exists'
          });
        } else {
          results.push({
            collection: 'users',
            index: index.name,
            status: 'error',
            error: error.message
          });
        }
      }
    }

    return {
      success: true,
      message: '数据库索引优化完成',
      results
    };

  } catch (error) {
    console.error('创建数据库索引失败:', error);
    return {
      success: false,
      message: '创建数据库索引失败',
      error: error.message
    };
  }
}

// 获取集合索引信息
async function getCollectionIndexes(collectionName) {
  try {
    // 注意：云开发数据库可能不支持直接获取索引信息
    // 这里返回一个模拟的响应
    return {
      success: true,
      message: `${collectionName}集合索引信息`,
      data: {
        collection: collectionName,
        note: '请在云开发控制台查看具体的索引信息'
      }
    };
  } catch (error) {
    return {
      success: false,
      message: '获取索引信息失败',
      error: error.message
    };
  }
}

// 云函数入口
exports.main = async (event, context) => {
  const { action = 'createIndexes', collectionName } = event;

  switch (action) {
    case 'createIndexes':
      return await createOptimizedIndexes();
    
    case 'getIndexes':
      if (!collectionName) {
        return {
          success: false,
          message: '请提供集合名称'
        };
      }
      return await getCollectionIndexes(collectionName);
    
    default:
      return {
        success: false,
        message: '不支持的操作'
      };
  }
};
