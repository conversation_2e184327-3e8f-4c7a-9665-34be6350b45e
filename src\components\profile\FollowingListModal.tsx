'use client';

import { useState, useEffect } from 'react';
import { X, User, UserX, Heart, Shield } from 'lucide-react';
import Button from '@/components/ui/Button';
import { showToast } from '@/components/ui/Toast';

interface FollowingUser {
  _id: string;
  nickname: string;
  avatar_url?: string;
  bio?: string;
  isFollowing: boolean;
}

interface BlockedUser {
  _id: string;
  nickname: string;
  avatar_url?: string;
  bio?: string;
  blockedAt: string;
}

interface FollowingListModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
}

type TabType = 'following' | 'blocked';

export default function FollowingListModal({ isOpen, onClose, userId }: FollowingListModalProps) {
  const [activeTab, setActiveTab] = useState<TabType>('following');
  const [followingList, setFollowingList] = useState<FollowingUser[]>([]);
  const [blockedList, setBlockedList] = useState<BlockedUser[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      if (activeTab === 'following') {
        loadFollowingList();
      } else {
        loadBlockedList();
      }
    }
  }, [isOpen, userId, activeTab]);

  const loadFollowingList = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      const mockFollowingList: FollowingUser[] = [
        {
          _id: '1',
          nickname: '宠物达人小王',
          avatar_url: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
          bio: '专业宠物训练师',
          isFollowing: true
        },
        {
          _id: '2',
          nickname: '猫咪爱好者',
          avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
          bio: '家有三只猫咪',
          isFollowing: true
        },
        {
          _id: '3',
          nickname: '狗狗繁殖专家',
          bio: '专业金毛繁殖',
          isFollowing: true
        }
      ];
      
      setFollowingList(mockFollowingList);
    } catch (error) {
      showToast.error('加载关注列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleUnfollow = async (targetUserId: string) => {
    try {
      // 模拟取消关注API调用
      setFollowingList(prev => 
        prev.map(user => 
          user._id === targetUserId 
            ? { ...user, isFollowing: false }
            : user
        )
      );
      showToast.success('已取消关注');
    } catch (error) {
      showToast.error('取消关注失败');
    }
  };

  const handleFollow = async (targetUserId: string) => {
    try {
      // 模拟关注API调用
      setFollowingList(prev => 
        prev.map(user => 
          user._id === targetUserId 
            ? { ...user, isFollowing: true }
            : user
        )
      );
      showToast.success('关注成功');
    } catch (error) {
      showToast.error('关注失败');
    }
  };

  const loadBlockedList = async () => {
    setLoading(true);
    try {
      const result = await petAPI.getBlockedUsers();
      if (result.success) {
        setBlockedList(result.data || []);
      } else {
        showToast.error(result.message || '加载黑名单失败');
      }
    } catch (error) {
      showToast.error('加载黑名单失败');
    } finally {
      setLoading(false);
    }
  };

  const handleUnblock = async (targetUserId: string) => {
    try {
      const result = await petAPI.unblockUser({ targetUserId });
      if (result.success) {
        setBlockedList(prev => prev.filter(user => user._id !== targetUserId));
        showToast.success('已取消拉黑');
      } else {
        showToast.error(result.message || '取消拉黑失败');
      }
    } catch (error) {
      showToast.error('取消拉黑失败');
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div
        className="bg-white rounded-lg w-[480px] h-[500px] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b flex-shrink-0">
          <h2 className="text-xl font-bold text-gray-900">
            {activeTab === 'following' ? '我的关注' : '黑名单'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 标签页导航 */}
        <div className="border-b border-gray-200 flex-shrink-0">
          <div className="flex">
            <button
              onClick={() => setActiveTab('following')}
              className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors ${
                activeTab === 'following'
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
            >
              <Heart className="w-4 h-4" />
              <span>我的关注</span>
            </button>
            <button
              onClick={() => setActiveTab('blocked')}
              className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors ${
                activeTab === 'blocked'
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
            >
              <Shield className="w-4 h-4" />
              <span>黑名单</span>
            </button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : activeTab === 'following' ? (
            followingList.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <Heart className="w-16 h-16 mx-auto" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">还没有关注任何人</h3>
                <p className="text-gray-600">去发现更多有趣的用户吧！</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {followingList.map((user) => (
                  <div key={user._id} className="p-4 hover:bg-gray-50">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 rounded-full bg-gray-200 overflow-hidden flex-shrink-0">
                        {user.avatar_url ? (
                          <img 
                            src={user.avatar_url} 
                            alt={user.nickname}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <User className="w-6 h-6 text-gray-400" />
                          </div>
                        )}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {user.nickname}
                        </h3>
                        {user.bio && (
                          <p className="text-sm text-gray-500 truncate">
                            {user.bio}
                          </p>
                        )}
                      </div>
                      
                      <div className="flex-shrink-0">
                        {user.isFollowing ? (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleUnfollow(user._id)}
                          >
                            取消关注
                          </Button>
                        ) : (
                          <Button
                            size="sm"
                            onClick={() => handleFollow(user._id)}
                          >
                            关注
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )
          ) : (
            blockedList.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <Shield className="w-16 h-16 mx-auto" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">黑名单为空</h3>
                <p className="text-gray-600">您还没有拉黑任何用户</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {blockedList.map((user) => (
                  <div key={user._id} className="p-4 hover:bg-gray-50">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 rounded-full bg-gray-200 overflow-hidden flex-shrink-0">
                        {user.avatar_url ? (
                          <img 
                            src={user.avatar_url} 
                            alt={user.nickname}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <UserX className="w-6 h-6 text-gray-400" />
                          </div>
                        )}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {user.nickname}
                        </h3>
                        <p className="text-sm text-gray-500 truncate">
                          拉黑时间：{new Date(user.blockedAt).toLocaleDateString()}
                        </p>
                      </div>
                      
                      <div className="flex-shrink-0">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleUnblock(user._id)}
                          className="text-red-600 border-red-200 hover:bg-red-50"
                        >
                          取消拉黑
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )
          )}
        </div>
      </div>
    </div>
  );
}
