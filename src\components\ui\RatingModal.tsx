import React, { useState } from 'react';
import { X } from 'lucide-react';
import StarRating from './StarRating';
import Button from './Button';
import { cn } from '@/utils';

interface RatingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (rating: number, comment?: string) => void;
  petTitle: string;
  loading?: boolean;
}

const RatingModal: React.FC<RatingModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  petTitle,
  loading = false
}) => {
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');

  const handleSubmit = () => {
    if (rating > 0) {
      onSubmit(rating, comment.trim() || undefined);
      // 重置表单
      setRating(0);
      setComment('');
    }
  };

  const handleClose = () => {
    setRating(0);
    setComment('');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            为宠物评分
          </h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={loading}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 内容 */}
        <div className="p-4 space-y-4">
          {/* 宠物标题 */}
          <div className="text-center">
            <p className="text-gray-600 mb-2">您对以下宠物的满意度：</p>
            <p className="font-medium text-gray-900">{petTitle}</p>
          </div>

          {/* 评分 */}
          <div className="text-center space-y-2">
            <p className="text-sm text-gray-600">请选择评分：</p>
            <StarRating
              rating={rating}
              onRatingChange={setRating}
              size="lg"
              showText={true}
              className="justify-center"
            />
          </div>

          {/* 评论 */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              评价内容（可选）
            </label>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="分享您的使用体验..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
              maxLength={200}
              disabled={loading}
            />
            <p className="text-xs text-gray-500 text-right">
              {comment.length}/200
            </p>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex space-x-3 p-4 border-t border-gray-200">
          <Button
            variant="outline"
            onClick={handleClose}
            className="flex-1"
            disabled={loading}
          >
            取消
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            className="flex-1"
            disabled={rating === 0 || loading}
            loading={loading}
          >
            提交评分
          </Button>
        </div>
      </div>
    </div>
  );
};

export default RatingModal;
