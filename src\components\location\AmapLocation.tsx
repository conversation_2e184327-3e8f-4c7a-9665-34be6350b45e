'use client';

import { useEffect, useState } from 'react';
import { City, findNearestCity } from '@/data/cities';

// 高德地图配置
const AMAP_CONFIG = {
  key: 'bba0d64f074ce7c7248393b88defe180', // 您的高德地图API Key
  version: '2.0',
  plugins: ['AMap.Geolocation']
};

// 位置信息类型
export interface LocationInfo {
  city: City;
  address: string;
  lat: number;
  lng: number;
}

interface AmapLocationProps {
  onLocationSuccess: (location: LocationInfo) => void;
  onLocationError: (error: string) => void;
  children: (params: {
    getCurrentLocation: () => void;
    loading: boolean;
    error: string | null;
  }) => React.ReactNode;
}

// 声明全局AMap类型
declare global {
  interface Window {
    AMap: any;
    _AMapSecurityConfig: any;
  }
}

const AmapLocation: React.FC<AmapLocationProps> = ({
  onLocationSuccess,
  onLocationError,
  children
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);

  // 加载高德地图API
  useEffect(() => {
    if (window.AMap) {
      setMapLoaded(true);
      return;
    }

    // 设置安全密钥（如果需要）
    window._AMapSecurityConfig = {
      securityJsCode: '', // 如果使用安全密钥，在这里设置
    };

    const script = document.createElement('script');
    script.src = `https://webapi.amap.com/maps?v=${AMAP_CONFIG.version}&key=${AMAP_CONFIG.key}&plugin=${AMAP_CONFIG.plugins.join(',')}`;
    script.async = true;
    script.onload = () => {
      setMapLoaded(true);
    };
    script.onerror = () => {
      setError('高德地图API加载失败');
    };

    document.head.appendChild(script);

    return () => {
      // 清理脚本
      const existingScript = document.querySelector(`script[src*="webapi.amap.com"]`);
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, []);

  // 获取当前位置
  const getCurrentLocation = async () => {
    if (!mapLoaded) {
      setError('地图API未加载完成');
      onLocationError('地图API未加载完成');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 创建地理定位实例
      const geolocation = new window.AMap.Geolocation({
        enableHighAccuracy: true, // 是否使用高精度定位
        timeout: 10000, // 超时时间
        maximumAge: 0, // 定位结果缓存0毫秒
        convert: true, // 自动偏移坐标
        showButton: false, // 不显示定位按钮
        buttonPosition: 'LB', // 定位按钮停靠位置
        buttonOffset: new window.AMap.Pixel(10, 20), // 定位按钮与设置的停靠位置的偏移量
        showMarker: false, // 定位成功后在定位到的位置显示点标记
        showCircle: false, // 定位成功后用圆圈表示定位精度范围
        panToLocation: false, // 定位成功后将定位到的位置作为地图中心点
        zoomToAccuracy: false // 定位成功后调整地图视野范围使定位位置及精度范围视野内可见
      });

      // 执行定位
      geolocation.getCurrentPosition((status: string, result: any) => {
        setLoading(false);

        if (status === 'complete') {
          // 定位成功
          const { position, formattedAddress, addressComponent } = result;
          const lat = position.lat;
          const lng = position.lng;

          // 尝试从高德返回的地址信息中获取城市
          let cityName = addressComponent?.city || addressComponent?.province || '';
          
          // 如果高德没有返回城市信息，使用我们的城市数据查找最近的城市
          let nearestCity = findNearestCity(lat, lng);
          
          if (!nearestCity) {
            // 如果找不到最近的城市，使用默认城市（北京）
            nearestCity = {
              name: '北京市',
              code: '110000',
              lat: 39.9042,
              lng: 116.4074
            };
          }

          // 如果高德返回了城市名，尝试匹配我们的城市数据
          if (cityName && !cityName.endsWith('市')) {
            cityName += '市';
          }

          const locationInfo: LocationInfo = {
            city: nearestCity,
            address: formattedAddress || '未知地址',
            lat,
            lng
          };

          onLocationSuccess(locationInfo);
        } else {
          // 定位失败
          const errorMsg = result?.message || '定位失败，请检查定位权限';
          setError(errorMsg);
          onLocationError(errorMsg);
        }
      });

    } catch (err: any) {
      setLoading(false);
      const errorMsg = err.message || '定位服务异常';
      setError(errorMsg);
      onLocationError(errorMsg);
    }
  };

  // 浏览器原生定位（备用方案）
  const getBrowserLocation = () => {
    if (!navigator.geolocation) {
      const errorMsg = '浏览器不支持定位功能';
      setError(errorMsg);
      onLocationError(errorMsg);
      return;
    }

    setLoading(true);
    setError(null);

    navigator.geolocation.getCurrentPosition(
      (position) => {
        setLoading(false);
        const lat = position.coords.latitude;
        const lng = position.coords.longitude;

        // 使用我们的城市数据查找最近的城市
        const nearestCity = findNearestCity(lat, lng);

        if (nearestCity) {
          const locationInfo: LocationInfo = {
            city: nearestCity,
            address: '当前位置',
            lat,
            lng
          };
          onLocationSuccess(locationInfo);
        } else {
          const errorMsg = '无法确定当前城市';
          setError(errorMsg);
          onLocationError(errorMsg);
        }
      },
      (error) => {
        setLoading(false);
        let errorMsg = '定位失败';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMsg = '用户拒绝了定位请求';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMsg = '位置信息不可用';
            break;
          case error.TIMEOUT:
            errorMsg = '定位请求超时';
            break;
        }
        setError(errorMsg);
        onLocationError(errorMsg);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      }
    );
  };

  // 组合定位方法：优先使用高德，失败时使用浏览器原生
  const getLocationWithFallback = () => {
    if (mapLoaded) {
      getCurrentLocation();
    } else {
      getBrowserLocation();
    }
  };

  return (
    <>
      {children({
        getCurrentLocation: getLocationWithFallback,
        loading,
        error
      })}
    </>
  );
};

export default AmapLocation;
