// 使用NPM包的CloudBase配置
import cloudbase from '@cloudbase/js-sdk';

// CloudBase实例
let app: any = null;
let auth: any = null;
let db: any = null;
let initPromise: Promise<any> | null = null;

// 初始化CloudBase（使用NPM包）
const initCloudBase = async () => {
  if (typeof window === 'undefined') {
    console.log('非浏览器环境，跳过CloudBase初始化');
    return null;
  }
  
  // 如果已经初始化过，直接返回
  if (app) {
    console.log('CloudBase已初始化，直接返回');
    return app;
  }
  
  // 如果正在初始化，等待初始化完成
  if (initPromise) {
    console.log('CloudBase正在初始化，等待完成...');
    return initPromise;
  }
  
  initPromise = (async () => {
    try {
      console.log('开始CloudBase NPM包初始化...');
      console.log('CloudBase SDK版本:', cloudbase);
      
      app = cloudbase.init({
        env: 'yichongyuzhou-3g9112qwf5f3487b', // 您的环境ID
        region: 'ap-shanghai' // 指定地域
      });
      
      console.log('CloudBase应用初始化完成，初始化服务...');
      auth = app.auth();
      db = app.database();

      // 检查登录状态，如果未登录则进行匿名登录
      console.log('检查登录状态...');
      const loginState = await auth.getLoginState();
      console.log('当前登录状态:', loginState);

      if (!loginState || !loginState.isLoggedIn) {
        console.log('未登录，开始匿名登录...');
        try {
          await auth.signInAnonymously();
          console.log('匿名登录成功！');
          const newLoginState = await auth.getLoginState();
          console.log('新登录状态:', newLoginState);
        } catch (authError: any) {
          console.error('匿名登录失败:', authError);
          throw new Error(`匿名登录失败: ${authError?.message || '未知错误'}`);
        }
      } else {
        console.log('已登录，跳过匿名登录');
      }

      console.log('CloudBase NPM包初始化成功！');
      console.log('应用实例:', app);
      console.log('认证服务:', auth);
      console.log('数据库服务:', db);
      
      return app;
    } catch (error: any) {
      console.error('CloudBase NPM包初始化失败:', error);
      console.error('错误详情:', error?.message);
      initPromise = null; // 重置，允许重试
      throw new Error(`CloudBase初始化失败: ${error?.message || '未知错误'}`);
    }
  })();
  
  return initPromise;
};

// 检查用户是否已登录
const checkUserLogin = () => {
  try {
    const savedUser = localStorage.getItem('pet_platform_user');
    const isLoggedIn = localStorage.getItem('pet_platform_logged_in') === 'true';
    return savedUser && isLoggedIn;
  } catch (error) {
    return false;
  }
};

// 需要登录的操作列表
const REQUIRE_LOGIN_ACTIONS = [
  'toggleLike', 'toggleDislike', 'toggleBookmark', 'ratePet', 'exchangeContact',
  'toggleFollow', 'reportPost', 'reportUser', 'wantPet', 'submitAppeal',
  'getUserBookmarks', 'getUserPosts', 'getUserFollowing', 'getUserPermissions'
];

// 通用API调用函数
const callCloudFunction = async (functionName: string, action: string, data?: any) => {
  try {
    console.log(`🚀 开始调用云函数: ${functionName}.${action}`, data);

    // 检查是否需要登录
    if (REQUIRE_LOGIN_ACTIONS.includes(action)) {
      const isLoggedIn = checkUserLogin();
      if (!isLoggedIn) {
        console.log('❌ 操作需要登录，但用户未登录');
        throw new Error('请先登录后再进行此操作');
      }
      console.log('✅ 用户已登录，可以执行操作');
    }

    // 检查CloudBase初始化状态
    console.log('🔍 检查CloudBase初始化状态...');
    const cloudbaseApp = await initCloudBase();
    if (!cloudbaseApp) {
      console.error('❌ CloudBase未初始化');
      throw new Error('CloudBase未初始化');
    }

    // 检查用户登录状态（不自动匿名登录）
    console.log('🔐 检查用户登录状态...');
    // 使用全局的auth实例，避免重复创建
    if (!auth) {
      auth = cloudbaseApp.auth();
    }

    console.log('✅ CloudBase已初始化，准备调用云函数');

    // 获取用户ID（优先使用注册用户ID）
    let userId = null;
    let openId = null;

    try {
      // 首先尝试从localStorage获取注册用户信息
      const savedUser = localStorage.getItem('pet_platform_user');
      if (savedUser) {
        const userData = JSON.parse(savedUser);
        userId = userData._id;
        openId = userData._id; // 使用注册用户ID作为openId
        console.log('✅ 使用注册用户ID:', userId);
      } else {
        console.log('❌ 未找到注册用户信息');
      }
    } catch (error) {
      console.error('获取注册用户ID失败:', error);
    }

    // 准备调用参数，包含用户ID
    // 对于某些操作，不自动添加当前用户ID，避免覆盖目标用户ID
    const shouldAddCurrentUserId = !['getUserInfo', 'getUserPosts', 'getUserBookmarks', 'getUserFollowing'].includes(action);

    const callParams = {
      name: functionName,
      data: {
        action,
        data: shouldAddCurrentUserId ? {
          ...data,
          userId: userId,
          openId: openId  // 显式传递OPENID
        } : {
          ...data,
          currentUserId: userId,  // 使用不同的参数名传递当前用户ID
          openId: openId  // 显式传递OPENID
        }
      }
    };
    console.log('📋 云函数调用参数:', callParams);

    // 调用云函数
    console.log('📡 正在调用云函数...');
    const result = await cloudbaseApp.callFunction(callParams);

    console.log(`✅ ${functionName}.${action} 调用成功:`, result);

    // 检查返回结果
    if (result.result) {
      console.log('📦 返回result字段:', result.result);
      return result.result;
    } else {
      console.log('📦 返回完整结果:', result);
      return result;
    }
  } catch (error: any) {
    console.error(`❌ ${functionName}.${action} 调用失败:`, error);

    // 详细的错误信息展开
    console.error('❌ 错误类型:', typeof error);
    console.error('❌ 错误构造函数:', error?.constructor?.name);

    if (error?.message) {
      console.error(`❌ 错误消息: ${error.message}`);
    }
    if (error?.code) {
      console.error(`❌ 错误代码: ${error.code}`);
    }
    if (error?.name) {
      console.error(`❌ 错误名称: ${error.name}`);
    }
    if (error?.stack) {
      console.error(`❌ 错误堆栈:`, error.stack);
    }

    // 尝试序列化完整错误对象
    try {
      console.error('❌ 错误对象JSON:', JSON.stringify(error, null, 2));
    } catch (jsonError) {
      console.error('❌ 无法序列化错误对象:', jsonError);
      console.error('❌ 错误对象属性:', Object.keys(error));
      console.error('❌ 错误对象值:', Object.values(error));
    }

    // 如果错误有特殊属性
    if (error?.response) {
      console.error('❌ 响应错误:', error.response);
    }
    if (error?.request) {
      console.error('❌ 请求错误:', error.request);
    }

    throw error;
  }
};

// 宠物交易平台API
export const petAPI = {
  // 获取帖子列表
  async getPosts(params: any = {}) {
    return callCloudFunction('pet-api', 'getPosts', params);
  },

  // 优化的帖子查询（支持缓存和性能优化）
  async getOptimizedPosts(params: {
    page?: number;
    limit?: number;
    sortBy?: 'created_at' | 'likes_count' | 'wants_count' | 'avg_rating' | 'priority';
    category?: string;
    type?: 'all' | 'breeding' | 'selling' | 'lost';
    location?: string;
    includeUserInfo?: boolean;
    includeCategoryInfo?: boolean;
    currentUserId?: string; // 添加当前用户ID参数
  } = {}) {
    return callCloudFunction('optimizedPostQuery', 'query', params);
  },

  // 获取帖子详情
  async getPostDetail(params: { postId: string; userId?: string | null }) {
    return callCloudFunction('pet-api', 'getPostDetail', params);
  },

  // 创建帖子
  async createPost(postData: any) {
    return callCloudFunction('pet-api', 'createPost', postData);
  },

  // 点赞帖子
  async likePost(params: { postId: string; userId: string }) {
    return callCloudFunction('pet-api', 'likePost', params);
  },

  // 切换点赞状态
  async toggleLike(params: { postId: string }) {
    return callCloudFunction('pet-api', 'toggleLike', params);
  },

  // 添加点赞（永久）
  async addLike(params: { postId: string }) {
    return callCloudFunction('pet-api', 'addLike', params);
  },

  // 切换不喜欢状态
  async toggleDislike(params: { postId: string }) {
    return callCloudFunction('pet-api', 'toggleDislike', params);
  },

  // 添加不喜欢（永久）
  async addDislike(params: { postId: string }) {
    return callCloudFunction('pet-api', 'addDislike', params);
  },

  // 切换收藏状态
  async toggleBookmark(params: { postId: string }) {
    return callCloudFunction('pet-api', 'toggleBookmark', params);
  },

  // 收藏帖子
  async bookmarkPost(params: { postId: string; userId: string }) {
    return callCloudFunction('pet-api', 'bookmarkPost', params);
  },

  // 交换联系方式
  async exchangeContact(params: { postId: string; contactInfo: any }) {
    return callCloudFunction('pet-api', 'exchangeContact', params);
  },

  // 评分帖子
  async ratePost(params: { postId: string; userId: string; rating: number }) {
    return callCloudFunction('pet-api', 'ratePost', params);
  },

  // 切换关注状态
  async toggleFollow(params: { targetUserId: string }) {
    return callCloudFunction('pet-api', 'toggleFollow', params);
  },

  // 获取分类列表
  async getCategories() {
    return callCloudFunction('pet-api', 'getCategories');
  },

  // 评分宠物
  async ratePet(params: { postId: string; rating: number }) {
    return callCloudFunction('pet-api', 'ratePet', params);
  },

  // 举报帖子
  async reportPost(params: { postId: string; reason: string }) {
    return callCloudFunction('pet-api', 'reportPost', params);
  },

  // 获取用户收藏列表
  async getUserBookmarks(params: { limit?: number; offset?: number } = {}) {
    return callCloudFunction('pet-api', 'getUserBookmarks', params);
  },

  // 获取用户发布列表
  async getUserPosts(params: { limit?: number; offset?: number; targetUserId?: string } = {}) {
    return callCloudFunction('pet-api', 'getUserPosts', params);
  },

  // 获取用户关注列表
  async getUserFollowing(params: { limit?: number; offset?: number; targetUserId?: string } = {}) {
    return callCloudFunction('pet-api', 'getUserFollowing', params);
  },

  // 获取用户粉丝列表
  async getUserFollowers(params: { limit?: number; offset?: number; targetUserId?: string } = {}) {
    return callCloudFunction('pet-api', 'getUserFollowers', params);
  },

  // 获取用户通知列表
  async getUserNotifications(params: { limit?: number; offset?: number; type?: string } = {}) {
    return callCloudFunction('pet-api', 'getUserNotifications', params);
  },

  // 标记通知为已读
  async markNotificationRead(params: { notificationId: string }) {
    return callCloudFunction('pet-api', 'markNotificationRead', params);
  },

  // 删除通知
  async deleteNotification(params: { notificationId: string }) {
    return callCloudFunction('pet-api', 'deleteNotification', params);
  },

  // 批量删除通知
  async bulkDeleteNotifications(params: { notificationIds: string[] }) {
    return callCloudFunction('pet-api', 'bulkDeleteNotifications', params);
  },

  // 更新用户资料
  async updateProfile(params: {
    nickname?: string;
    password?: string;
    oldPassword?: string;
    contactInfo?: { type: 'wechat' | 'phone'; value: string };
    address?: string;
  }) {
    return callCloudFunction('pet-api', 'updateProfile', params);
  },

  // 发送联系通知
  async sendContactNotification(params: {
    postId: string;
    authorId: string;
    userContact: any;
    authorContact: any;
  }) {
    return callCloudFunction('pet-api', 'sendContactNotification', params);
  },

  // 移除搜索功能 - 只保留筛选功能

  // 管理员功能 - 获取举报列表
  async getReports(params: {
    type?: 'post' | 'user';
    status?: 'pending' | 'approved' | 'rejected' | 'all';
    limit?: number;
    offset?: number
  } = {}) {
    return callCloudFunction('pet-api', 'getReports', params);
  },

  // 管理员功能 - 处理举报
  async handleReport(params: { reportId: string; action: string; reason?: string }) {
    return callCloudFunction('pet-api', 'handleReport', params);
  },

  // 管理员功能 - 封禁用户
  async banUser(params: { targetUserId: string; reason: string; duration?: number }) {
    return callCloudFunction('pet-api', 'banUser', params);
  },

  // 获取用户信息
  async getUserInfo(params: { userId: string }) {
    return callCloudFunction('pet-api', 'getUserInfo', params);
  },

  // 上传文件到静态托管
  async uploadToStatic(params: { fileName: string; fileData: string; contentType: string }) {
    return callCloudFunction('pet-api', 'uploadToStatic', params);
  },

  // 更新用户头像
  async updateAvatar(params: { avatarUrl: string }) {
    return callCloudFunction('pet-api', 'updateAvatar', params);
  },

  // 获取图片URL
  async getImage(params: { fileId: string }) {
    return callCloudFunction('pet-api', 'getImage', params);
  },

  // 拉黑用户
  async blockUser(params: { targetUserId: string; reason?: string }) {
    return callCloudFunction('pet-api', 'blockUser', params);
  },

  // 取消拉黑用户
  async unblockUser(params: { targetUserId: string }) {
    return callCloudFunction('pet-api', 'unblockUser', params);
  },

  // 取消拉黑用户
  async unblockUser(params: { targetUserId: string }) {
    return callCloudFunction('pet-api', 'unblockUser', params);
  },

  // 获取拉黑列表
  async getBlockedUsers(params: { limit?: number; offset?: number } = {}) {
    return callCloudFunction('pet-api', 'getBlockedUsers', params);
  },

  // 举报用户
  async reportUser(params: { targetUserId: string; reason: string }) {
    return callCloudFunction('pet-api', 'reportUser', params);
  },

  // 提交申诉
  async submitAppeal(params: { reason: string }) {
    return callCloudFunction('pet-api', 'submitAppeal', params);
  },

  // 获取用户权限状态
  async getUserPermissions(params: {}) {
    return callCloudFunction('pet-api', 'getUserPermissions', params);
  },

  // 管理员功能 - 获取申诉列表
  async getAppeals(params: {
    status?: 'pending' | 'approved' | 'rejected' | 'all';
    limit?: number;
    offset?: number
  } = {}) {
    return callCloudFunction('pet-api', 'getAppeals', params);
  },

  // 管理员功能 - 处理申诉
  async handleAppeal(params: { appealId: string; action: 'approved' | 'rejected'; adminReason?: string }) {
    return callCloudFunction('pet-api', 'handleAppeal', params);
  },

  // 管理员认证
  async adminLogin(params: { username: string; password: string }) {
    return callCloudFunction('pet-api', 'adminLogin', params);
  },

  // 管理员功能 - 获取管理员列表
  async getAdmins(params: {
    level_greater_than?: number;
    limit?: number;
    offset?: number
  } = {}) {
    return callCloudFunction('pet-api', 'getAdmins', params);
  },

  // 管理员功能 - 创建管理员
  async createAdmin(params: {
    username: string;
    password: string;
    role: string;
    level: number;
    permissions?: string[];
    can_create_admin?: boolean;
    can_delete_admin?: boolean;
    max_admin_level?: number;
  }) {
    return callCloudFunction('pet-api', 'createAdmin', params);
  },

  // 管理员功能 - 更新管理员
  async updateAdmin(params: { adminId: string; updateData: any }) {
    return callCloudFunction('pet-api', 'updateAdmin', params);
  },

  // 管理员功能 - 删除管理员
  async deleteAdmin(params: { adminId: string }) {
    return callCloudFunction('pet-api', 'deleteAdmin', params);
  },

  // 广告管理功能
  async getAds(params?: { status?: string; position_id?: string; limit?: number; offset?: number }) {
    return callCloudFunction('pet-api', 'getAds', params || {});
  },

  async getAdPositions(params?: { status?: string; limit?: number; offset?: number }) {
    return callCloudFunction('pet-api', 'getAdPositions', params || {});
  },

  async createAd(params: {
    title: string;
    advertiser_id: string;
    position_id: string;
    ad_type: string;
    content: string;
    target_url: string;
    start_date: string;
    end_date: string;
    budget: number;
    priority?: number;
    image_url?: string;
    video_url?: string;
  }) {
    return callCloudFunction('pet-api', 'createAd', params);
  },

  async updateAd(params: {
    ad_id: string;
    title?: string;
    content?: string;
    target_url?: string;
    start_date?: string;
    end_date?: string;
    budget?: number;
    priority?: number;
    status?: string;
    image_url?: string;
    video_url?: string;
  }) {
    return callCloudFunction('pet-api', 'updateAd', params);
  },

  async deleteAd(params: { ad_id: string }) {
    return callCloudFunction('pet-api', 'deleteAd', params);
  },

  async getAdStatistics(params?: {
    ad_id?: string;
    start_date?: string;
    end_date?: string;
    group_by?: string
  }) {
    return callCloudFunction('pet-api', 'getAdStatistics', params || {});
  },

  // 帖子优先级管理功能
  async updatePostPriority(params: {
    post_id: string;
    priority_level: 'high' | 'normal' | 'low' | 'hidden';
    reason?: string;
  }) {
    return callCloudFunction('pet-api', 'updatePostPriority', params);
  },

  async getPostQualityScore(params: { post_id: string }) {
    return callCloudFunction('pet-api', 'getPostQualityScore', params);
  },

  async batchUpdatePostPriority(params: {
    post_ids: string[];
    priority_level: 'high' | 'normal' | 'low' | 'hidden';
    reason?: string;
  }) {
    return callCloudFunction('pet-api', 'batchUpdatePostPriority', params);
  },

  async getPostsByPriority(params?: {
    priority_level?: 'high' | 'normal' | 'low' | 'hidden' | 'all';
    limit?: number;
    offset?: number;
    include_scores?: boolean;
  }) {
    return callCloudFunction('pet-api', 'getPostsByPriority', params || {});
  },

  async batchUpdateAllPostsQuality() {
    return callCloudFunction('pet-api', 'batchUpdateAllPostsQuality', {});
  },

  // 活动系统功能
  async createActivity(params: {
    title: string;
    description?: string;
    type: 'CONTEST' | 'VOTING' | 'DISCUSSION';
    startTime: string;
    endTime: string;
    durationDays: number;
    resultDisplayDays?: number;
    config: any;
  }) {
    return callCloudFunction('pet-api', 'createActivity', params);
  },

  async getActivities(params?: {
    status?: 'all' | 'DRAFT' | 'ACTIVE' | 'ENDED' | 'ARCHIVED';
    type?: 'all' | 'CONTEST' | 'VOTING' | 'DISCUSSION';
    limit?: number;
    offset?: number;
    includeArchived?: boolean;
  }) {
    return callCloudFunction('pet-api', 'getActivities', params || {});
  },

  async getActivityDetail(params: { activity_id: string }) {
    return callCloudFunction('pet-api', 'getActivityDetail', params);
  },

  async updateActivity(params: {
    activity_id: string;
    title?: string;
    description?: string;
    config?: any;
    status?: 'DRAFT' | 'ACTIVE' | 'ENDED' | 'ARCHIVED';
  }) {
    return callCloudFunction('pet-api', 'updateActivity', params);
  },

  async deleteActivity(params: { activity_id: string }) {
    return callCloudFunction('pet-api', 'deleteActivity', params);
  },

  async voteInActivity(params: {
    activity_id: string;
    option_id?: string;
    target_id?: string;
  }) {
    return callCloudFunction('pet-api', 'voteInActivity', params);
  },

  async addActivityComment(params: {
    activity_id: string;
    content: string;
    parent_id?: string;
  }) {
    return callCloudFunction('pet-api', 'addActivityComment', params);
  },

  async getActivityComments(params: {
    activity_id: string;
    page?: number;
    limit?: number;
    parent_id?: string;
  }) {
    return callCloudFunction('pet-api', 'getActivityComments', params);
  },

  async getSystemConfig() {
    return callCloudFunction('pet-api', 'getSystemConfig', {});
  },

  async updateSystemConfig(params: { config: any }) {
    return callCloudFunction('pet-api', 'updateSystemConfig', params);
  },

  // 管理员功能 - 获取权限列表
  async getPermissions() {
    return callCloudFunction('pet-api', 'getPermissions', {});
  },

  // 获取用户评分的帖子
  async getUserRatedPosts(params: { limit?: number; offset?: number }) {
    return callCloudFunction('pet-api', 'getUserRatedPosts', params);
  },

  // 更新帖子状态
  async updatePostStatus(params: { postId: string; status: 'published' | 'draft' }) {
    return callCloudFunction('pet-api', 'updatePostStatus', params);
  },

  // 删除帖子（用户删除自己的帖子或草稿）
  async deletePost(params: { postId: string }) {
    return callCloudFunction('pet-api', 'deleteMyPost', params);
  },

  // 管理员功能 - 删除帖子
  async adminDeletePost(params: { postId: string; reason: string }) {
    return callCloudFunction('pet-api', 'adminDeletePost', params);
  },

  // 管理员功能 - 删除云存储文件
  async deleteCloudFile(params: { fileId: string }) {
    return callCloudFunction('pet-api', 'deleteCloudFile', params);
  },

  // 管理员功能 - 获取帖子管理列表
  async getPostsForAdmin(params: {
    status?: string;
    category?: string;
    author_id?: string;
    limit?: number;
    offset?: number;
    keyword?: string;
  } = {}) {
    return callCloudFunction('pet-api', 'getPostsForAdmin', params);
  }
};

// 活动API
export const activityAPI = {
  // 获取正在进行的活动
  async getActiveActivities() {
    return callCloudFunction('pet-api', 'getActiveActivities');
  },

  // 获取活动列表
  async getActivities(params: {
    status?: string;
    limit?: number;
    offset?: number;
  } = {}) {
    return callCloudFunction('pet-api', 'getActivities', params);
  },

  // 获取系统配置
  async getSystemConfig() {
    return callCloudFunction('pet-api', 'getSystemConfig');
  },

  // 获取仪表板统计数据
  async getDashboardStats() {
    return callCloudFunction('pet-api', 'getDashboardStats');
  }
};

// 用户认证API
export const authAPI = {
  // 发送验证码
  async sendVerificationCode(email: string, type: 'register' | 'reset_password' | 'change_password' = 'register') {
    return callCloudFunction('email-auth', 'sendVerificationCode', { email, type });
  },

  // 验证验证码
  async verifyCode(email: string, code: string, type: 'register' | 'reset_password' | 'change_password' = 'register') {
    return callCloudFunction('email-auth', 'verifyCode', { email, code, type });
  },

  // 邮箱注册
  async registerWithEmail(email: string, password: string, nickname: string, verificationCode: string) {
    return callCloudFunction('email-auth', 'registerWithEmail', { email, password, nickname, verificationCode });
  },

  // 邮箱登录
  async loginWithEmail(email: string, password: string) {
    return callCloudFunction('email-auth', 'loginWithEmail', { email, password });
  },

  // 重置密码
  async resetPassword(email: string, verificationCode: string, newPassword: string) {
    return callCloudFunction('email-auth', 'resetPassword', { email, verificationCode, newPassword });
  },

  // 修改密码
  async changePassword(email: string, verificationCode: string, oldPassword: string, newPassword: string) {
    return callCloudFunction('email-auth', 'changePassword', { email, verificationCode, oldPassword, newPassword });
  },

  // 获取当前用户信息
  async getCurrentUser(email?: string) {
    return callCloudFunction('user-auth', 'getCurrentUser', email ? { email } : {});
  },

  // 登出
  async logout() {
    return callCloudFunction('user-auth', 'logout');
  }
};

// 图片压缩功能
const compressImage = async (file: File, maxWidth: number = 800, quality: number = 0.8): Promise<File> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 智能压缩策略
      let { width, height } = img;
      let targetWidth = width;
      let targetHeight = height;
      let targetQuality = quality;

      // 根据原图大小调整压缩策略
      if (width > 2000 || height > 2000) {
        // 超大图片：更激进的压缩
        targetWidth = Math.min(width, maxWidth);
        targetHeight = (height * targetWidth) / width;
        targetQuality = 0.8;
      } else if (width > maxWidth) {
        // 大图片：标准压缩
        targetHeight = (height * maxWidth) / width;
        targetWidth = maxWidth;
        targetQuality = quality;
      } else {
        // 小图片：保持原尺寸，只压缩质量
        targetQuality = Math.max(0.9, quality);
      }

      // 设置canvas尺寸
      canvas.width = targetWidth;
      canvas.height = targetHeight;

      // 绘制压缩后的图片
      ctx?.drawImage(img, 0, 0, targetWidth, targetHeight);

      // 转换为blob
      canvas.toBlob((blob) => {
        if (blob) {
          const compressedFile = new File([blob], file.name, {
            type: 'image/jpeg',
            lastModified: Date.now()
          });

          const compressionRatio = Math.round((1 - compressedFile.size / file.size) * 100);
          console.log(`图片压缩完成: ${file.size} -> ${compressedFile.size} (${compressionRatio}% 减少)`);
          console.log(`压缩参数: ${width}x${height} -> ${targetWidth}x${targetHeight}, 质量: ${Math.round(targetQuality * 100)}%`);

          resolve(compressedFile);
        } else {
          resolve(file);
        }
      }, 'image/jpeg', targetQuality);
    };

    img.src = URL.createObjectURL(file);
  });
};

// 通过云函数获取图片URL
export const getImageUrl = async (fileId: string): Promise<string> => {
  try {
    const cloudbaseApp = await initCloudBase();
    if (!cloudbaseApp) {
      throw new Error('CloudBase未初始化');
    }

    const result = await cloudbaseApp.callFunction({
      name: 'pet-api',
      data: {
        action: 'getImage',
        data: { fileId }
      }
    });

    if (result.result?.success && result.result?.data?.url) {
      return result.result.data.url;
    } else {
      console.error('获取图片URL失败:', result.result?.message);
      // 如果获取失败，返回一个占位符图片
      return '/placeholder-image.png';
    }
  } catch (error) {
    console.error('获取图片URL失败:', error);
    return '/placeholder-image.png';
  }
};

// 上传文件到静态托管（免费且公开访问）
export const uploadFileToStatic = async (file: File): Promise<string> => {
  try {
    console.log('开始上传文件到静态托管:', file.name, '原始大小:', file.size);

    // 如果是图片文件，先进行压缩
    let fileToUpload = file;
    if (file.type.startsWith('image/')) {
      fileToUpload = await compressImage(file);
    }

    // 生成唯一文件名
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2);
    const fileExtension = file.name.split('.').pop();
    const fileName = `${timestamp}_${randomStr}.${fileExtension}`;

    console.log('压缩后大小:', fileToUpload.size);

    // 将文件转换为base64
    const base64 = await fileToBase64(fileToUpload);

    // 通过云函数上传到静态托管
    const cloudbaseApp = await initCloudBase();
    if (!cloudbaseApp) {
      throw new Error('CloudBase未初始化');
    }

    const result = await cloudbaseApp.callFunction({
      name: 'pet-api',
      data: {
        action: 'uploadToStatic',
        data: {
          fileName,
          fileData: base64,
          contentType: fileToUpload.type
        }
      }
    });

    if (result.result?.success && result.result?.data?.url) {
      console.log('文件上传成功，URL:', result.result.data.url);
      return result.result.data.url;
    } else {
      throw new Error(result.result?.message || '上传失败');
    }
  } catch (error: any) {
    console.error('文件上传失败:', error);
    throw error;
  }
};

// 文件转base64
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      const base64 = reader.result as string;
      // 移除data:image/jpeg;base64,前缀
      const base64Data = base64.split(',')[1];
      resolve(base64Data);
    };
    reader.onerror = error => reject(error);
  });
};

// 文件上传功能（带压缩）- 保持向后兼容
export const uploadFile = async (file: File): Promise<string> => {
  // 现在使用静态托管上传
  return await uploadFileToStatic(file);
};

// 导出CloudBase实例和初始化函数
export { app, auth, db, initCloudBase };
export default { petAPI, authAPI, activityAPI, uploadFile };
