'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { showToast } from '@/components/ui/Toast';
import Button from '@/components/ui/Button';
import {
  ArrowLeft,
  Settings,
  Image,
  Save,
  RefreshCw
} from 'lucide-react';

interface SystemSettings {
  maxImageSize: number; // 单位：MB
  maxImagesPerPost: number;
  allowedImageTypes: string[];
}

export default function AdminSettingsPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<SystemSettings>({
    maxImageSize: 5, // 默认5MB
    maxImagesPerPost: 9,
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp']
  });

  // 检查管理员权限
  useEffect(() => {
    const adminToken = localStorage.getItem('adminToken');
    const adminUser = localStorage.getItem('adminUser');

    if (!adminToken || !adminUser) {
      showToast.error('请先登录管理员账号');
      router.push('/admin');
      return;
    }

    loadSettings();
  }, [router]);

  // 加载系统设置
  const loadSettings = async () => {
    try {
      setLoading(true);
      
      // 从localStorage读取设置，如果没有则使用默认值
      const savedSettings = localStorage.getItem('systemSettings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('加载设置失败:', error);
      showToast.error('加载设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存系统设置
  const saveSettings = async () => {
    try {
      setSaving(true);
      
      // 验证设置
      if (settings.maxImageSize <= 0 || settings.maxImageSize > 100) {
        showToast.error('图片大小限制必须在1-100MB之间');
        return;
      }
      
      if (settings.maxImagesPerPost <= 0 || settings.maxImagesPerPost > 20) {
        showToast.error('每帖图片数量必须在1-20张之间');
        return;
      }

      // 保存到localStorage（在实际项目中应该保存到数据库）
      localStorage.setItem('systemSettings', JSON.stringify(settings));
      
      showToast.success('设置保存成功');
    } catch (error) {
      console.error('保存设置失败:', error);
      showToast.error('保存设置失败');
    } finally {
      setSaving(false);
    }
  };

  // 重置为默认设置
  const resetToDefault = () => {
    if (confirm('确定要重置为默认设置吗？')) {
      setSettings({
        maxImageSize: 5,
        maxImagesPerPost: 9,
        allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp']
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Settings className="w-16 h-16 text-blue-600 mx-auto mb-4 animate-pulse" />
          <p className="text-gray-600">加载设置中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部导航 */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/admin/dashboard')}
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-5 h-5" />
                <span>返回控制台</span>
              </button>
              <div className="h-6 w-px bg-gray-300" />
              <h1 className="text-xl font-semibold text-gray-900">系统设置</h1>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow">
          {/* 设置标题 */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <Image className="w-6 h-6 text-blue-600" />
              <h2 className="text-lg font-medium text-gray-900">图片上传设置</h2>
            </div>
            <p className="mt-1 text-sm text-gray-500">
              配置用户上传图片的限制和规则
            </p>
          </div>

          {/* 设置表单 */}
          <div className="px-6 py-6 space-y-6">
            {/* 单张图片大小限制 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                单张图片大小限制 (MB)
              </label>
              <div className="flex items-center space-x-4">
                <input
                  type="number"
                  min="1"
                  max="100"
                  step="1"
                  value={settings.maxImageSize}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    maxImageSize: parseInt(e.target.value) || 1
                  }))}
                  className="block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <span className="text-sm text-gray-500">
                  当前限制：{settings.maxImageSize}MB
                </span>
              </div>
              <p className="mt-1 text-xs text-gray-500">
                建议设置在5-30MB之间，过大会影响上传速度
              </p>
            </div>

            {/* 每帖最大图片数量 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                每帖最大图片数量
              </label>
              <div className="flex items-center space-x-4">
                <input
                  type="number"
                  min="1"
                  max="20"
                  step="1"
                  value={settings.maxImagesPerPost}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    maxImagesPerPost: parseInt(e.target.value) || 1
                  }))}
                  className="block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <span className="text-sm text-gray-500">
                  当前限制：{settings.maxImagesPerPost}张
                </span>
              </div>
            </div>

            {/* 支持的图片格式 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                支持的图片格式
              </label>
              <div className="space-y-2">
                {['image/jpeg', 'image/png', 'image/webp', 'image/gif'].map((type) => (
                  <label key={type} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.allowedImageTypes.includes(type)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSettings(prev => ({
                            ...prev,
                            allowedImageTypes: [...prev.allowedImageTypes, type]
                          }));
                        } else {
                          setSettings(prev => ({
                            ...prev,
                            allowedImageTypes: prev.allowedImageTypes.filter(t => t !== type)
                          }));
                        }
                      }}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      {type.replace('image/', '').toUpperCase()}
                    </span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between">
            <Button
              variant="outline"
              onClick={resetToDefault}
              className="flex items-center space-x-2"
            >
              <RefreshCw className="w-4 h-4" />
              <span>重置默认</span>
            </Button>
            
            <Button
              onClick={saveSettings}
              loading={saving}
              disabled={saving}
              className="flex items-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>{saving ? '保存中...' : '保存设置'}</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
