// 用户类型
export interface User {
  _id: string;
  openid: string;
  email?: string;
  nickname: string;
  avatar_url?: string;
  bio?: string;
  role: 'user' | 'admin';
  created_at: Date;
  last_login_at: Date;
  is_active: boolean;
  posts_count?: number;
  followers_count?: number;
  following_count?: number;
  total_likes?: number;
}

// 分类类型
export interface Category {
  _id: string;
  id: string;
  name: string;
  level: 1 | 2 | 3;
  parent_id: string | null;
  order: number;
}

// 宠物帖子类型
export interface Post {
  _id: string;
  title: string;
  description: string;
  images: string[];
  category: string;
  breed?: string; // 宠物品种（用户自由输入）
  breed_tags?: string[]; // 品种标签（系统自动生成，用于搜索优化）
  type?: 'breeding' | 'selling' | 'lost' | 'wanted'; // 宠物类型：配种、出售、寻回、求购
  gender?: 'male' | 'female'; // 宠物性别（配种时必填）
  location?: string;
  contact_info?: {
    phone?: string;
    wechat?: string;
    qq?: string;
  };
  tags: string[];
  author_id: string;
  author?: User;
  user?: User; // 用户信息（优化查询返回）
  created_at: Date;
  updated_at: Date;
  likes_count: number;
  wants_count: number;
  bookmarks_count: number;
  ratings_count: number;
  avg_rating: number;
  user_interactions?: {
    hasLiked: boolean;
    hasWanted: boolean;
    hasBookmarked: boolean;
    hasRated: boolean;
    hasContacted: boolean;
    userRating: number;
  };
  rating_stats?: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

// 点赞类型
export interface Like {
  _id: string;
  post_id: string;
  user_id: string;
  created_at: Date;
}

// 想买类型
export interface Want {
  _id: string;
  post_id: string;
  user_id: string;
  created_at: Date;
}

// 评分类型
export interface Rating {
  _id: string;
  post_id: string;
  user_id: string;
  rating: 1 | 2 | 3 | 4 | 5;
  created_at: Date;
}

// 关注类型
export interface Follow {
  _id: string;
  follower_id: string;
  following_id: string;
  created_at: Date;
}

// 活动类型
export interface Activity {
  _id: string;
  title: string;
  description: string;
  type: 'voting' | 'rating' | 'contest';
  status: 'DRAFT' | 'ACTIVE' | 'ENDED';
  start_time: Date;
  end_time: Date;
  created_at: Date;
  updated_at: Date;
}

// 举报类型
export interface PostReport {
  _id: string;
  post_id: string;
  reporter_id: string;
  reason: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8; // 1-疑似虐待动物内容 2-发布非法野生动物 3-图片与描述严重不符 4-重复发布相同内容 5-不当内容（暴力色情等） 6-商业广告或垃圾信息 7-虚假配种/找回/求购信息 8-其他问题
  status: 'pending' | 'resolved' | 'rejected';
  created_at: Date;
}

export interface UserReport {
  _id: string;
  reported_user_id: string;
  reporter_id: string;
  reason: 1 | 2 | 3 | 4 | 5; // 1-经常发布虚假信息 2-恶意用户行为 3-疑似商业机构伪装个人 4-频繁发布低质量内容 5-其他问题
  status: 'pending' | 'resolved' | 'rejected';
  created_at: Date;
}

// API响应类型
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    hasMore: boolean;
  };
}

// 分页参数类型
export interface PaginationParams {
  page?: number;
  limit?: number;
}

// 宠物列表查询参数
export interface PostsQueryParams extends PaginationParams {
  category?: string;
  type?: 'breeding' | 'selling' | 'lost';
  sortBy?: 'created_at' | 'likes_count' | 'wants_count' | 'avg_rating';
  location?: string;
}

// 用户帖子查询参数
export interface UserPostsQueryParams extends PaginationParams {
  userId: string;
  sortBy?: 'created_at' | 'likes_count' | 'wants_count';
}

// 上传文件类型
export interface UploadResult {
  fileID: string;
  requestId: string;
}

// 登录状态类型
export interface LoginState {
  isLoggedIn: boolean;
  user?: User;
  credential?: any;
}

// 表单数据类型
export interface PostFormData {
  title?: string; // 可选，后端会使用品种作为标题
  description: string;
  images: File[];
  category: string;
  breed?: string; // 宠物品种（用户自由输入）
  type?: 'breeding' | 'selling' | 'lost' | 'wanted' | ''; // 宠物类型：配种、出售、寻回、求购
  gender?: 'male' | 'female' | ''; // 宠物性别（配种时必填）
  location?: string; // 位置标签，自动添加#前缀
  contact_info?: {
    phone?: string;
    wechat?: string;
    qq?: string;
  };
}

export interface UserProfileFormData {
  nickname: string;
  bio?: string;
  avatar?: File;
}

// 评分等级映射
export const RATING_LEVELS = {
  1: '普通',
  2: '极品',
  3: '神品',
  4: '仙品',
  5: '圣品'
} as const;

// 宠物分类映射
export const PET_CATEGORIES = {
  dogs: '狗狗',
  cats: '猫咪',
  birds: '鸟类',
  fish: '鱼类',
  reptiles: '爬虫',
  small_pets: '小宠',
  farm_animals: '农场动物',
  exotic_pets: '异宠',
  others: '其他'
} as const;

// 帖子类型映射
export const POST_TYPES = {
  selling: '出售',
  breeding: '配种',
  lost: '寻回',
  wanted: '求购'
} as const;

// 举报原因映射 - 针对宠物展示生活交流平台
export const POST_REPORT_REASONS = {
  1: '违法违规图片（血腥暴力色情等）',
  2: '图片模糊看不清'
} as const;

export const USER_REPORT_REASONS = {
  1: '疑似骗子'
} as const;

// 用户权限类型
export interface UserPermissions {
  canLike: boolean;
  canDislike: boolean;
  canReportPost: boolean;
  canReportUser: boolean;
  canContact: boolean;
  canPublishPost: boolean;
  bannedUntil?: Date;
  banReason?: string;
}

// 系统设置类型
export interface SystemSettings {
  autoReportThreshold: number; // 自动处罚阈值
  maxImageSize: number;
  maxImagesPerPost: number;
  allowedImageTypes: string[];
}

// 申诉类型
export interface Appeal {
  _id: string;
  report_id: string;
  appellant_id: string;
  reason: string;
  type: 'post' | 'user';
  status: 'pending' | 'approved' | 'rejected';
  admin_id?: string;
  admin_reason?: string;
  created_at: Date;
  handled_at?: Date;
}
