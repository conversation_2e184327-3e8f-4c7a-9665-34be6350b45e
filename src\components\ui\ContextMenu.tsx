'use client';

import React, { useEffect, useRef } from 'react';
import { Trash2, Edit, Share, Eye } from 'lucide-react';

interface ContextMenuItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  onClick: () => void;
  variant?: 'default' | 'danger';
}

interface ContextMenuProps {
  isOpen: boolean;
  onClose: () => void;
  items: ContextMenuItem[];
  position?: { x: number; y: number };
  className?: string;
}

const ContextMenu: React.FC<ContextMenuProps> = ({
  isOpen,
  onClose,
  items,
  position,
  className = ''
}) => {
  const menuRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: Event) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  // 阻止背景滚动
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const handleItemClick = (item: ContextMenuItem) => {
    item.onClick();
    onClose();
  };

  return (
    <>
      {/* 背景遮罩 */}
      <div
        className="fixed inset-0 z-40 bg-black bg-opacity-30"
        onClick={onClose}
        aria-hidden="true"
      />

      {/* 菜单内容 */}
      <div
        ref={menuRef}
        className={`
          fixed z-50 bg-white rounded-xl shadow-2xl border border-gray-200
          min-w-48 py-3 mx-4 transform transition-all duration-200 ease-out
          ${position ? '' : 'bottom-6 left-0 right-0 md:bottom-auto md:left-auto md:right-auto md:mx-0'}
          ${isOpen ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}
          ${className}
        `}
        style={position ? {
          left: position.x,
          top: position.y,
          transform: 'translate(-50%, -100%)'
        } : undefined}
        role="menu"
        aria-orientation="vertical"
      >
        {items.map((item, index) => (
          <button
            key={item.id}
            onClick={() => handleItemClick(item)}
            className={`
              w-full px-5 py-4 text-left flex items-center space-x-3
              transition-all duration-150 font-medium text-base
              ${item.variant === 'danger'
                ? 'text-red-600 hover:bg-red-50 active:bg-red-100 hover:scale-[1.02]'
                : 'text-gray-700 hover:bg-gray-50 active:bg-gray-100 hover:scale-[1.02]'
              }
              ${index === 0 ? 'rounded-t-xl' : ''}
              ${index === items.length - 1 ? 'rounded-b-xl' : ''}
              ${index > 0 ? 'border-t border-gray-100' : ''}
            `}
            role="menuitem"
            tabIndex={0}
          >
            {item.icon && (
              <span className="flex-shrink-0 w-5 h-5 flex items-center justify-center">
                {item.icon}
              </span>
            )}
            <span className="flex-1">{item.label}</span>
          </button>
        ))}
      </div>
    </>
  );
};

export default ContextMenu;
