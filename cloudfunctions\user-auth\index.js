const cloud = require('wx-server-sdk');

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 用户认证和管理云函数
 * 处理用户注册、登录、资料更新等
 */
exports.main = async (event, context) => {
  const { action, data } = event;
  const { OPENID } = cloud.getWXContext();

  try {
    switch (action) {
      // 用户注册/登录
      case 'login':
        return await loginUser(data, OPENID);
      
      // 更新用户资料
      case 'updateProfile':
        return await updateProfile(data, OPENID);
      
      // 获取用户资料
      case 'getProfile':
        return await getProfile(data.userId || OPENID);
      
      // 获取用户发布的宠物
      case 'getUserPosts':
        return await getUserPosts(data);
      
      // 获取用户关注列表
      case 'getFollowing':
        return await getFollowing(data);
      
      // 获取用户粉丝列表
      case 'getFollowers':
        return await getFollowers(data);
      
      // 检查用户是否存在
      case 'checkUser':
        return await checkUser(OPENID);

      // 获取当前用户信息（支持邮箱登录）
      case 'getCurrentUser':
        return await getCurrentUser(data, OPENID);

      // 登出
      case 'logout':
        return await logout(data, OPENID);

      default:
        return {
          success: false,
          message: '未知的操作类型'
        };
    }
  } catch (error) {
    console.error('用户云函数执行错误:', error);
    return {
      success: false,
      message: error.message || '服务器内部错误'
    };
  }
};

// 用户登录/注册
async function loginUser(data, openid) {
  const { email, nickname, avatar_url, login_type = 'anonymous' } = data;

  // 对于匿名登录，如果没有 openid，生成一个唯一标识
  const userId = openid || `anonymous_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // 检查用户是否已存在
  const existingUser = await db.collection('users')
    .where({
      $or: [
        { openid: userId },
        { email: email && email.trim() ? email : null }
      ].filter(condition => Object.values(condition).some(val => val !== null && val !== undefined))
    })
    .get();
  
  if (existingUser.data.length > 0) {
    // 用户已存在，更新最后登录时间
    const user = existingUser.data[0];
    await db.collection('users').doc(user._id).update({
      data: {
        last_login_at: new Date(),
        login_type: login_type
      }
    });

    return {
      success: true,
      data: {
        ...user,
        last_login_at: new Date(),
        isNewUser: false
      },
      message: '登录成功'
    };
  } else {
    // 新用户，创建账户
    const userData = {
      openid: userId,
      email: email || '',
      nickname: nickname || '神秘用户',
      avatar_url: avatar_url || '',
      bio: '',
      role: 'user',
      login_type: login_type,
      created_at: new Date(),
      last_login_at: new Date(),
      is_active: true
    };

    const result = await db.collection('users').add({
      data: userData
    });

    return {
      success: true,
      data: {
        _id: result._id,
        ...userData,
        isNewUser: true
      },
      message: '注册成功'
    };
  }
}

// 更新用户资料
async function updateProfile(data, openid) {
  const { nickname, bio, avatar_url } = data;

  // 如果没有 openid，返回错误
  if (!openid) {
    throw new Error('用户未登录');
  }

  const updateData = {
    updated_at: new Date()
  };

  if (nickname !== undefined) updateData.nickname = nickname;
  if (bio !== undefined) updateData.bio = bio;
  if (avatar_url !== undefined) updateData.avatar_url = avatar_url;

  const user = await db.collection('users')
    .where({ openid: openid })
    .get();

  if (user.data.length === 0) {
    throw new Error('用户不存在');
  }

  await db.collection('users').doc(user.data[0]._id).update({
    data: updateData
  });

  return {
    success: true,
    message: '资料更新成功'
  };
}

// 获取用户资料
async function getProfile(userId) {
  let user;

  // 如果没有 userId，返回错误
  if (!userId) {
    throw new Error('用户ID不能为空');
  }

  // 如果是openid，按openid查询；否则按_id查询
  if (userId.startsWith('o') || userId.startsWith('anonymous_')) {
    const result = await db.collection('users')
      .where({ openid: userId })
      .get();
    user = result.data[0];
  } else {
    const result = await db.collection('users').doc(userId).get();
    user = result.data;
  }

  if (!user) {
    throw new Error('用户不存在');
  }
  
  // 获取用户统计信息
  const [posts, followers, following, totalLikes] = await Promise.all([
    db.collection('posts').where({ author_id: user.openid }).count(),
    db.collection('follows').where({ following_id: user._id }).count(),
    db.collection('follows').where({ follower_id: user._id }).count(),
    // 获取用户发布的帖子的总点赞数
    getUserTotalLikes(user.openid)
  ]);
  
  return {
    success: true,
    data: {
      ...user,
      posts_count: posts.total,
      followers_count: followers.total,
      following_count: following.total,
      total_likes: totalLikes
    }
  };
}

// 获取用户发布的宠物
async function getUserPosts({ userId, page = 1, limit = 20, sortBy = 'created_at' }) {
  const skip = (page - 1) * limit;
  
  const result = await db.collection('posts')
    .where({ author_id: userId })
    .orderBy(sortBy, 'desc')
    .skip(skip)
    .limit(limit)
    .get();
  
  // 获取每个帖子的统计信息
  const postsWithStats = await Promise.all(
    result.data.map(async (post) => {
      const [likes, wants, ratings] = await Promise.all([
        db.collection('likes').where({ post_id: post._id }).count(),
        db.collection('wants').where({ post_id: post._id }).count(),
        db.collection('ratings').where({ post_id: post._id }).get()
      ]);
      
      const avgRating = ratings.data.length > 0 
        ? ratings.data.reduce((sum, r) => sum + r.rating, 0) / ratings.data.length 
        : 0;
      
      return {
        ...post,
        likes_count: likes.total,
        wants_count: wants.total,
        ratings_count: ratings.data.length,
        avg_rating: Math.round(avgRating * 10) / 10
      };
    })
  );
  
  return {
    success: true,
    data: postsWithStats,
    pagination: {
      page,
      limit,
      hasMore: result.data.length === limit
    }
  };
}

// 获取用户关注列表
async function getFollowing({ userId, page = 1, limit = 20 }) {
  const skip = (page - 1) * limit;
  
  const result = await db.collection('follows')
    .where({ follower_id: userId })
    .orderBy('created_at', 'desc')
    .skip(skip)
    .limit(limit)
    .get();
  
  // 获取被关注用户的详细信息
  const followingUsers = await Promise.all(
    result.data.map(async (follow) => {
      const user = await db.collection('users').doc(follow.following_id).get();
      return {
        ...user.data,
        followed_at: follow.created_at
      };
    })
  );
  
  return {
    success: true,
    data: followingUsers,
    pagination: {
      page,
      limit,
      hasMore: result.data.length === limit
    }
  };
}

// 获取用户粉丝列表
async function getFollowers({ userId, page = 1, limit = 20 }) {
  const skip = (page - 1) * limit;
  
  const result = await db.collection('follows')
    .where({ following_id: userId })
    .orderBy('created_at', 'desc')
    .skip(skip)
    .limit(limit)
    .get();
  
  // 获取粉丝用户的详细信息
  const followers = await Promise.all(
    result.data.map(async (follow) => {
      const user = await db.collection('users').doc(follow.follower_id).get();
      return {
        ...user.data,
        followed_at: follow.created_at
      };
    })
  );
  
  return {
    success: true,
    data: followers,
    pagination: {
      page,
      limit,
      hasMore: result.data.length === limit
    }
  };
}

// 检查用户是否存在
async function checkUser(openid) {
  // 如果没有 openid，返回用户不存在
  if (!openid) {
    return {
      success: true,
      exists: false,
      data: null
    };
  }

  const result = await db.collection('users')
    .where({ openid: openid })
    .get();

  return {
    success: true,
    exists: result.data.length > 0,
    data: result.data[0] || null
  };
}

// 获取当前用户信息（支持邮箱登录）
async function getCurrentUser(data, openid) {
  try {
    console.log('getCurrentUser 调用参数:', { data, openid });

    let user;

    // 优先通过邮箱查找用户（邮箱登录）
    if (data?.email) {
      console.log('通过邮箱查找用户:', data.email);
      const emailResult = await db.collection('users')
        .where({ email: data.email })
        .get();

      console.log('邮箱查询结果:', emailResult.data.length, '条记录');
      if (emailResult.data.length > 0) {
        user = emailResult.data[0];
        console.log('找到用户:', user.nickname, user._id);
      } else {
        console.log('未找到邮箱对应的用户');
      }
    }

    // 如果没有通过邮箱找到，尝试通过openid或userId查找（微信登录）
    if (!user) {
      const userId = data?.userId || openid;
      console.log('尝试通过 userId 查找:', userId);

      if (!userId) {
        console.log('userId 为空，返回用户未登录');
        return {
          success: false,
          message: '用户未登录'
        };
      }
      if (userId.startsWith('o') || userId.startsWith('anonymous_')) {
        const result = await db.collection('users')
          .where({ openid: userId })
          .get();
        user = result.data[0];
      } else {
        // 通过_id查找
        const result = await db.collection('users').doc(userId).get();
        user = result.data;
      }
    }

    if (!user) {
      console.log('最终未找到用户，返回用户不存在');
      return {
        success: false,
        message: '用户不存在'
      };
    }

    console.log('成功找到用户，准备返回用户信息');

    // 不返回密码等敏感信息
    delete user.password;

    return {
      success: true,
      data: user,
      message: '获取用户信息成功'
    };
  } catch (error) {
    console.error('获取当前用户信息失败:', error);
    return {
      success: false,
      message: error.message || '获取用户信息失败'
    };
  }
}

// 登出
async function logout(data, openid) {
  try {
    // 对于邮箱登录，前端会清除本地存储
    // 这里可以记录登出日志或更新最后活动时间
    const userId = data?.userId || openid;

    if (userId) {
      // 更新最后活动时间
      let user;
      if (data?.email) {
        const emailResult = await db.collection('users')
          .where({ email: data.email })
          .get();
        user = emailResult.data[0];
      } else if (userId.startsWith('o') || userId.startsWith('anonymous_')) {
        const result = await db.collection('users')
          .where({ openid: userId })
          .get();
        user = result.data[0];
      } else {
        const result = await db.collection('users').doc(userId).get();
        user = result.data;
      }

      if (user) {
        await db.collection('users').doc(user._id).update({
          data: {
            last_logout_at: new Date(),
            updated_at: new Date()
          }
        });
      }
    }

    return {
      success: true,
      message: '登出成功'
    };
  } catch (error) {
    console.error('登出失败:', error);
    return {
      success: false,
      message: error.message || '登出失败'
    };
  }
}

// 获取用户发布帖子的总点赞数
async function getUserTotalLikes(userId) {
  try {
    // 获取用户所有帖子
    const posts = await db.collection('posts')
      .where({ author_id: userId })
      .field({ _id: true })
      .get();

    if (posts.data.length === 0) {
      return 0;
    }

    // 获取所有帖子的点赞总数
    const postIds = posts.data.map(post => post._id);
    let totalLikes = 0;

    // 分批查询点赞数（避免in查询限制）
    const batchSize = 10;
    for (let i = 0; i < postIds.length; i += batchSize) {
      const batch = postIds.slice(i, i + batchSize);
      const likes = await db.collection('likes')
        .where({
          post_id: db.command.in(batch)
        })
        .count();
      totalLikes += likes.total;
    }

    return totalLikes;
  } catch (error) {
    console.error('获取用户总点赞数失败:', error);
    return 0;
  }
}
