import { useState, useEffect } from 'react';
import { petAPI } from '@/lib/cloudbase';
import { UserPermissions } from '@/types';

export const useUserPermissions = (userId?: string) => {
  const [permissions, setPermissions] = useState<UserPermissions | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPermissions = async () => {
    if (!userId) {
      setPermissions(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const result = await petAPI.getUserPermissions({});

      if (result.success) {
        setPermissions(result.data);
      } else {
        setError(result.message || '获取权限失败');
        // 设置默认权限
        setPermissions({
          canLike: true,
          canDislike: true,
          canReportPost: true,
          canReportUser: true,
          canContact: true,
          canPublishPost: true,
          bannedUntil: null,
          banReason: null
        });
      }
    } catch (err: any) {
      console.error('获取用户权限失败:', err);
      setError(err.message || '获取权限失败');
      // 设置默认权限
      setPermissions({
        canLike: true,
        canDislike: true,
        canReportPost: true,
        canReportUser: true,
        canContact: true,
        canPublishPost: true,
        bannedUntil: null,
        banReason: null
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPermissions();
  }, [userId]);

  const refreshPermissions = () => {
    fetchPermissions();
  };

  // 检查是否被处罚
  const isPunished = permissions && (!permissions.canPublishPost || !permissions.canContact);

  // 检查是否可以申诉
  const canAppeal = isPunished && permissions?.banReason;

  return {
    permissions,
    loading,
    error,
    refreshPermissions,
    isPunished,
    canAppeal
  };
};
