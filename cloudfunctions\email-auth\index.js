const cloud = require('wx-server-sdk');
const crypto = require('crypto');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 延迟加载nodemailer
let nodemailer = null;
let transporter = null;

// 初始化邮件发送器
async function initMailer() {
  if (transporter) return transporter;

  try {
    console.log('开始初始化邮件发送器...');
    nodemailer = require('nodemailer');

    transporter = nodemailer.createTransport({
      host: 'smtp.qq.com',
      port: 465,        // 使用SSL端口465
      secure: true,     // 启用SSL加密
      auth: {
        user: process.env.EMAIL_USER || '<EMAIL>',
        pass: process.env.EMAIL_PASS || 'your-app-password'
      }
    });

    console.log('邮件发送器初始化成功');
    return transporter;
  } catch (error) {
    console.error('邮件发送器初始化失败:', error);
    throw error;
  }
}



// 生成6位数字验证码
function generateVerificationCode() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// 验证邮箱格式
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 密码加密
function hashPassword(password) {
  return crypto.createHash('sha256').update(password + 'pet_platform_salt').digest('hex');
}

// 验证密码
function verifyPassword(password, hashedPassword) {
  return hashPassword(password) === hashedPassword;
}

exports.main = async (event, context) => {
  console.log('云函数被调用，参数:', event);
  const { action, data } = event;

  try {
    switch (action) {
      case 'sendVerificationCode':
        return await sendVerificationCode(data);
      case 'verifyCode':
        return await verifyCode(data);
      case 'registerWithEmail':
        return await registerWithEmail(data);
      case 'loginWithEmail':
        return await loginWithEmail(data);
      case 'resetPassword':
        return await resetPassword(data);
      case 'changePassword':
        return await changePassword(data);
      default:
        return {
          success: false,
          message: '未知操作'
        };
    }
  } catch (error) {
    console.error('邮箱认证错误:', error);
    return {
      success: false,
      message: error.message || '服务器错误'
    };
  }
};

// 发送验证码
async function sendVerificationCode({ email, type = 'register' }) {
  console.log('开始发送验证码，邮箱:', email, '类型:', type);

  if (!email || !isValidEmail(email)) {
    return {
      success: false,
      message: '请输入有效的邮箱地址'
    };
  }

  try {
    // 检查是否在1分钟内已发送过验证码
    console.log('检查最近发送记录...');
    const recentCode = await db.collection('email_codes')
      .where({
        email: email,
        createdAt: db.command.gte(new Date(Date.now() - 60 * 1000))
      })
      .get();

    if (recentCode.data.length > 0) {
      return {
        success: false,
        message: '验证码发送过于频繁，请1分钟后再试'
      };
    }

    // 生成验证码
    const code = generateVerificationCode();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5分钟后过期
    console.log('生成验证码:', code);

    // 保存验证码到数据库
    console.log('保存验证码到数据库...');
    await db.collection('email_codes').add({
      data: {
        email: email,
        code: code,
        type: type, // register, reset_password, change_password
        expiresAt: expiresAt,
        used: false,
        createdAt: new Date()
      }
    });
    console.log('验证码保存成功');

    // 根据类型设置邮件内容
    const emailTemplates = {
      register: {
        subject: '宠物交易平台 - 注册验证码',
        title: '欢迎注册宠物交易平台',
        description: '您正在注册宠物交易平台账号，请使用以下验证码完成注册：'
      },
      reset_password: {
        subject: '宠物交易平台 - 重置密码验证码',
        title: '重置您的密码',
        description: '您正在重置密码，请使用以下验证码继续操作：'
      },
      change_password: {
        subject: '宠物交易平台 - 修改密码验证码',
        title: '修改您的密码',
        description: '您正在修改密码，请使用以下验证码确认身份：'
      }
    };

    const template = emailTemplates[type] || emailTemplates.register;

    // 发送邮件
    const mailOptions = {
      from: process.env.EMAIL_USER || '<EMAIL>',
      to: email,
      subject: template.subject,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center;">
            <h1 style="color: white; margin: 0;">🐕 宠物交易平台</h1>
          </div>
          <div style="padding: 30px; background: #f9f9f9;">
            <h2 style="color: #333;">${template.title}</h2>
            <p style="color: #666; line-height: 1.6;">${template.description}</p>
            <div style="background: white; padding: 20px; border-radius: 8px; text-align: center; margin: 20px 0;">
              <div style="font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 8px;">${code}</div>
            </div>
            <p style="color: #666; line-height: 1.6;">
              验证码有效期为 <strong>5分钟</strong>。
              <br>如果这不是您的操作，请忽略此邮件。
            </p>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #999; font-size: 12px;">
              此邮件由系统自动发送，请勿回复。
            </div>
          </div>
        </div>
      `
    };

    // 发送邮件
    console.log('开始发送邮件...');
    try {
      const mailer = await initMailer();
      await mailer.sendMail(mailOptions);
      console.log('邮件发送成功');

      return {
        success: true,
        message: '验证码已发送到您的邮箱，请查收'
      };
    } catch (emailError) {
      console.error('邮件发送失败:', emailError);
      // 即使邮件发送失败，验证码已保存，可以提供备用方案
      return {
        success: true,
        message: `验证码生成成功，但邮件发送失败。测试验证码: ${code}`,
        testCode: code // 开发阶段提供测试验证码
      };
    }

  } catch (error) {
    console.error('发送验证码失败:', error);
    return {
      success: false,
      message: '验证码发送失败，请稍后重试'
    };
  }
}

// 验证验证码
async function verifyCode({ email, code, type = 'register' }) {
  if (!email || !code) {
    return {
      success: false,
      message: '请输入邮箱和验证码'
    };
  }

  try {
    // 查找有效的验证码
    const result = await db.collection('email_codes')
      .where({
        email: email,
        code: code,
        type: type,
        used: false,
        expiresAt: db.command.gte(new Date())
      })
      .orderBy('createdAt', 'desc')
      .limit(1)
      .get();

    if (result.data.length === 0) {
      return {
        success: false,
        message: '验证码无效或已过期'
      };
    }

    // 标记验证码为已使用
    await db.collection('email_codes').doc(result.data[0]._id).update({
      data: {
        used: true,
        usedAt: new Date()
      }
    });

    return {
      success: true,
      message: '验证码验证成功'
    };

  } catch (error) {
    console.error('验证码验证失败:', error);
    return {
      success: false,
      message: '验证失败，请重试'
    };
  }
}

// 邮箱注册
async function registerWithEmail({ email, password, nickname, verificationCode }) {
  // 先验证验证码
  const verifyResult = await verifyCode({ email, code: verificationCode, type: 'register' });
  if (!verifyResult.success) {
    return verifyResult;
  }

  try {
    // 检查邮箱是否已注册
    const existingUser = await db.collection('users')
      .where({
        email: email
      })
      .get();

    if (existingUser.data.length > 0) {
      return {
        success: false,
        message: '该邮箱已注册，请直接登录'
      };
    }

    // 创建新用户
    const hashedPassword = hashPassword(password);
    const newUser = {
      email: email,
      password: hashedPassword,
      nickname: nickname || email.split('@')[0],
      avatar: '',
      loginType: 'email',
      createdAt: new Date(),
      updatedAt: new Date(),
      status: 'active'
    };

    const createResult = await db.collection('users').add({
      data: newUser
    });

    const userData = {
      _id: createResult._id,
      ...newUser
    };
    delete userData.password; // 不返回密码

    return {
      success: true,
      message: '注册成功！',
      data: {
        ...userData,
        isNewUser: true
      }
    };

  } catch (error) {
    console.error('邮箱注册失败:', error);
    return {
      success: false,
      message: '注册失败，请重试'
    };
  }
}

// 邮箱登录
async function loginWithEmail({ email, password }) {
  if (!email || !password) {
    return {
      success: false,
      message: '请输入邮箱和密码'
    };
  }

  try {
    // 查找用户
    const userResult = await db.collection('users')
      .where({
        email: email
      })
      .get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '邮箱或密码错误'
      };
    }

    const userData = userResult.data[0];

    // 验证密码
    if (!verifyPassword(password, userData.password)) {
      return {
        success: false,
        message: '邮箱或密码错误'
      };
    }

    // 更新登录时间
    await db.collection('users').doc(userData._id).update({
      data: {
        lastLoginAt: new Date(),
        updatedAt: new Date()
      }
    });

    delete userData.password; // 不返回密码

    return {
      success: true,
      message: '登录成功！',
      data: userData
    };

  } catch (error) {
    console.error('邮箱登录失败:', error);
    return {
      success: false,
      message: '登录失败，请重试'
    };
  }
}

// 重置密码
async function resetPassword({ email, verificationCode, newPassword }) {
  // 先验证验证码
  const verifyResult = await verifyCode({ email, code: verificationCode, type: 'reset_password' });
  if (!verifyResult.success) {
    return verifyResult;
  }

  try {
    // 查找用户
    const userResult = await db.collection('users')
      .where({
        email: email
      })
      .get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }

    // 更新密码
    const hashedPassword = hashPassword(newPassword);
    await db.collection('users').doc(userResult.data[0]._id).update({
      data: {
        password: hashedPassword,
        updatedAt: new Date()
      }
    });

    return {
      success: true,
      message: '密码重置成功，请使用新密码登录'
    };

  } catch (error) {
    console.error('重置密码失败:', error);
    return {
      success: false,
      message: '重置密码失败，请重试'
    };
  }
}

// 修改密码
async function changePassword({ email, verificationCode, oldPassword, newPassword }) {
  // 先验证验证码
  const verifyResult = await verifyCode({ email, code: verificationCode, type: 'change_password' });
  if (!verifyResult.success) {
    return verifyResult;
  }

  try {
    // 查找用户
    const userResult = await db.collection('users')
      .where({
        email: email
      })
      .get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }

    const userData = userResult.data[0];

    // 验证旧密码
    if (!verifyPassword(oldPassword, userData.password)) {
      return {
        success: false,
        message: '原密码错误'
      };
    }

    // 更新密码
    const hashedPassword = hashPassword(newPassword);
    await db.collection('users').doc(userData._id).update({
      data: {
        password: hashedPassword,
        updatedAt: new Date()
      }
    });

    return {
      success: true,
      message: '密码修改成功'
    };

  } catch (error) {
    console.error('修改密码失败:', error);
    return {
      success: false,
      message: '修改密码失败，请重试'
    };
  }
}
