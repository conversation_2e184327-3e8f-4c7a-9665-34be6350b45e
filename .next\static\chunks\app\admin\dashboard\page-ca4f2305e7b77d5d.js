(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[427],{30804:function(e,s,t){Promise.resolve().then(t.bind(t,11558))},42488:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},32660:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},91723:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},50091:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},42208:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},46211:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]])},53113:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},94630:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},99397:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},82431:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},83229:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},98728:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},88906:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},18930:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},37806:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},99376:function(e,s,t){"use strict";var a=t(35475);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},11558:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return D}});var a=t(57437),r=t(2265),l=t(99376),i=t(98011),c=t(9356),n=t(56334),d=t(32660),x=t(88906),o=t(39763);let m=(0,o.Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),h=(0,o.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),p=(0,o.Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);var u=t(37806);let g=(0,o.Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]);var y=t(98728),j=t(99397),N=t(46211),b=t(42488);let v=(0,o.Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var f=t(18930),w=t(65302),k=t(45131),_=t(42208),C=t(50091),Z=t(94630),S=t(91723),T=t(53113),I=t(82431),P=t(83229);function D(){let e=(0,l.useRouter)(),[s,t]=(0,r.useState)("dashboard"),[o,D]=(0,r.useState)(null),[M,A]=(0,r.useState)(null),[z,L]=(0,r.useState)([]),[R,V]=(0,r.useState)([]),[U,E]=(0,r.useState)(!0),[O,F]=(0,r.useState)(null),[H,B]=(0,r.useState)(null),[q,W]=(0,r.useState)(""),[G,J]=(0,r.useState)(!1),[$,K]=(0,r.useState)("approved"),[Q,X]=(0,r.useState)(!1),[Y,ee]=(0,r.useState)({username:"",password:"",role:"admin",level:1,permissions:[]}),[es,et]=(0,r.useState)([]),[ea,er]=(0,r.useState)(!1),[el,ei]=(0,r.useState)(!1),[ec,en]=(0,r.useState)(null),[ed,ex]=(0,r.useState)(""),[eo,em]=(0,r.useState)([]),[eh,ep]=(0,r.useState)([]),[eu,eg]=(0,r.useState)(!1),[ey,ej]=(0,r.useState)("ads"),[eN,eb]=(0,r.useState)(!1),[ev,ef]=(0,r.useState)([]),[ew,ek]=(0,r.useState)(!1),[e_,eC]=(0,r.useState)(null),[eZ,eS]=(0,r.useState)(!1),[eT,eI]=(0,r.useState)(!1),[eP,eD]=(0,r.useState)("all"),[eM,eA]=(0,r.useState)("all"),[ez,eL]=(0,r.useState)({maxImageSize:5,maxImagesPerPost:9,allowedImageTypes:["image/jpeg","image/png","image/webp"],autoReportThreshold:10}),[eR,eV]=(0,r.useState)(!1),[eU,eE]=(0,r.useState)([]),[eO,eF]=(0,r.useState)(!1),[eH,eB]=(0,r.useState)(""),[eq,eW]=(0,r.useState)(null),[eG,eJ]=(0,r.useState)(!1),[e$,eK]=(0,r.useState)(null),[eQ,eX]=(0,r.useState)(!1),[eY,e0]=(0,r.useState)(null),[e2,e1]=(0,r.useState)(!1),e3=e=>{e0(e),eX(!0)};(0,r.useEffect)(()=>{let s=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");if(!s||!t){e.push("/admin");return}try{let e=JSON.parse(t);D(e),e4()}catch(s){console.error("解析管理员信息失败:",s),e.push("/admin")}},[e]);let e4=async()=>{try{E(!0);try{var e,s;let t=await (0,i.initCloudBase)();if(!t)throw Error("CloudBase未初始化");let a=await t.callFunction({name:"pet-api",data:{action:"getDashboardStats",data:{}}});(null===(e=a.result)||void 0===e?void 0:e.success)?A(a.result.data):(console.error("获取统计数据失败:",null===(s=a.result)||void 0===s?void 0:s.message),A({totalUsers:0,totalPosts:0,totalReports:0,totalAppeals:0,yesterdayNewUsers:0,yesterdayNewPosts:0,activeUsers:0,onlineUsers:0}))}catch(e){console.error("获取统计数据异常:",e),A({totalUsers:0,totalPosts:0,totalReports:0,totalAppeals:0,yesterdayNewUsers:0,yesterdayNewPosts:0,activeUsers:0,onlineUsers:0})}try{await e5()}catch(e){console.log("无权限加载管理员列表或不是超级管理员")}await e9(),await e6(),await sl(),await sd(),await sx(),await so()}catch(e){console.error("加载仪表板数据失败:",e),c.C.error("加载数据失败")}finally{E(!1)}},e5=async()=>{try{let e=await i.petAPI.getAdmins({limit:50,offset:0});e.success&&L(e.data||[])}catch(e){console.error("加载管理员列表失败:",e)}},e6=async()=>{er(!0);try{let e=await i.petAPI.getPostsForAdmin({limit:50});e.success&&et(e.data)}catch(e){console.error("加载帖子列表失败:",e),c.C.error("加载帖子列表失败")}finally{er(!1)}},e9=async()=>{try{let e=await i.petAPI.getAppeals({status:"all",limit:50,offset:0});e.success&&V(e.data||[])}catch(e){console.error("加载申诉列表失败:",e)}},e8=async()=>{if(!Y.username||!Y.password){c.C.error("请填写用户名和密码");return}try{let e=await i.petAPI.createAdmin({...Y,permissions:["*"]});e.success?(c.C.success("管理员创建成功"),X(!1),ee({username:"",password:"",role:"admin",level:1,permissions:[]}),e5()):c.C.error(e.message||"创建失败")}catch(e){console.error("创建管理员失败:",e),c.C.error(e.message||"创建管理员失败")}},e7=async e=>{e3({title:"删除管理员",message:"确定要删除这个管理员吗？此操作不可恢复。",confirmText:"删除",cancelText:"取消",type:"danger",onConfirm:async()=>{try{let s=await i.petAPI.deleteAdmin({adminId:e});s.success?(c.C.success("管理员删除成功"),e5()):c.C.error(s.message||"删除失败")}catch(e){console.error("删除管理员失败:",e),c.C.error(e.message||"删除管理员失败")}}})},se=async()=>{if(H)try{F(H._id);let e=await i.petAPI.handleAppeal({appealId:H._id,action:$,adminReason:q});e.success?(c.C.success("申诉处理成功"),J(!1),B(null),W(""),e9()):c.C.error(e.message||"处理失败")}catch(e){console.error("处理申诉失败:",e),c.C.error(e.message||"处理申诉失败")}finally{F(null)}},ss=e=>new Date(e).toLocaleString("zh-CN"),st=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},sa=e=>{switch(e){case"pending":return"待处理";case"approved":return"已通过";case"rejected":return"已驳回";default:return"未知"}},sr=async()=>{if(!ec||!ed.trim()){c.C.error("请填写删除原因");return}try{let e=await i.petAPI.adminDeletePost({postId:ec._id,reason:ed});e.success?(c.C.success("帖子删除成功"),ei(!1),en(null),ex(""),e6()):c.C.error(e.message||"删除失败")}catch(e){console.error("删除帖子失败:",e),c.C.error(e.message||"删除帖子失败")}},sl=async()=>{eg(!0);try{em([{_id:"ad_001",title:"优质宠物用品推荐",advertiser_id:"advertiser_001",advertiser_name:"宠物之家商城",position_id:"home_banner",position_name:"首页横幅广告",ad_type:"banner",content:"为您的爱宠提供最好的生活用品，健康快乐每一天！全场8折优惠中。",target_url:"https://example.com/pet-products",start_date:"2025-01-01T00:00:00.000Z",end_date:"2025-03-31T23:59:59.000Z",status:"active",priority:1,budget:5e3,spent:1250.5,impressions:15420,clicks:342,ctr:.0222,created_at:"2025-01-01T00:00:00.000Z"},{_id:"ad_002",title:"专业宠物医院",advertiser_id:"advertiser_002",advertiser_name:"爱宠医疗中心",position_id:"home_feed",position_name:"首页信息流广告",ad_type:"feed",content:"24小时宠物医疗服务，专业医师团队，让您的爱宠健康无忧。",target_url:"https://example.com/pet-hospital",start_date:"2025-01-10T00:00:00.000Z",end_date:"2025-02-28T23:59:59.000Z",status:"active",priority:2,budget:3e3,spent:890.25,impressions:8750,clicks:156,ctr:.0178,created_at:"2025-01-10T00:00:00.000Z"},{_id:"ad_003",title:"宠物美容服务",advertiser_id:"advertiser_003",advertiser_name:"美宠工坊",position_id:"home_feed",position_name:"首页信息流广告",ad_type:"feed",content:"专业宠物美容，让您的爱宠更加美丽动人。新客户首次服务7折。",target_url:"https://example.com/pet-grooming",start_date:"2025-01-15T00:00:00.000Z",end_date:"2025-04-15T23:59:59.000Z",status:"paused",priority:3,budget:2e3,spent:450.75,impressions:4200,clicks:89,ctr:.0212,created_at:"2025-01-15T00:00:00.000Z"}]),ep([{position_id:"home_banner",name:"首页横幅广告",page:"home",location:"top",width:728,height:90,ad_type:"banner",max_ads:3,rotation_interval:5e3,status:"active"},{position_id:"home_feed",name:"首页信息流广告",page:"home",location:"feed",width:300,height:200,ad_type:"feed",max_ads:5,rotation_interval:0,status:"active"},{position_id:"sidebar_banner",name:"侧边栏广告",page:"all",location:"sidebar",width:300,height:250,ad_type:"banner",max_ads:2,rotation_interval:8e3,status:"inactive"}])}catch(e){console.error("加载广告数据失败:",e),c.C.error("加载广告数据失败")}finally{eg(!1)}},si=e=>{let s={active:{label:"投放中",color:"bg-green-100 text-green-800"},paused:{label:"已暂停",color:"bg-yellow-100 text-yellow-800"},expired:{label:"已过期",color:"bg-red-100 text-red-800"},pending:{label:"待审核",color:"bg-blue-100 text-blue-800"},inactive:{label:"未启用",color:"bg-gray-100 text-gray-800"}},t=s[e]||s.pending;return(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(t.color),children:t.label})},sc=e=>"\xa5".concat(e.toFixed(2)),sn=e=>"".concat((100*e).toFixed(2),"%"),sd=async()=>{ek(!0);try{ef([{_id:"activity_001",title:"最萌宠物评选大赛",description:"展示你的宠物，参与最萌宠物评选活动，赢取丰厚奖品！",type:"CONTEST",status:"ACTIVE",start_time:"2025-01-01T00:00:00.000Z",end_time:"2025-01-31T23:59:59.000Z",result_display_end_time:"2025-02-03T23:59:59.000Z",duration_days:31,result_display_days:3,config:{comments_enabled:!0},statistics_summary:{total_votes:1250,total_comments:340},created_at:"2024-12-25T00:00:00.000Z"},{_id:"activity_002",title:"猫咪 VS 狗狗投票",description:"你更喜欢猫咪还是狗狗？快来投票表达你的观点！",type:"VOTING",status:"ACTIVE",start_time:"2025-01-15T00:00:00.000Z",end_time:"2025-02-15T23:59:59.000Z",result_display_end_time:"2025-02-18T23:59:59.000Z",duration_days:31,result_display_days:3,config:{comments_enabled:!0},statistics_summary:{total_votes:890,total_comments:156},created_at:"2025-01-10T00:00:00.000Z"},{_id:"activity_003",title:"宠物护理经验分享",description:"分享你的宠物护理经验，帮助更多宠物主人",type:"DISCUSSION",status:"ENDED",start_time:"2024-12-01T00:00:00.000Z",end_time:"2024-12-31T23:59:59.000Z",result_display_end_time:"2025-01-03T23:59:59.000Z",duration_days:31,result_display_days:3,config:{comments_enabled:!0},statistics_summary:{total_votes:0,total_comments:245},created_at:"2024-11-25T00:00:00.000Z"}]),eC({enabled:!0,comments_enabled:!0,rate_limit_interval:10,max_comment_length:100,default_result_display_days:3})}catch(e){console.error("加载活动数据失败:",e),c.C.error("加载活动数据失败")}finally{ek(!1)}},sx=async()=>{eV(!0);try{let e=localStorage.getItem("systemSettings");e&&eL(JSON.parse(e))}catch(e){console.error("加载设置失败:",e),c.C.error("加载设置失败")}finally{eV(!1)}},so=async()=>{eF(!0);try{eE([{_id:"user_001",nickname:"宠物爱好者",avatar_url:"/default-avatar.png",created_at:"2024-01-15T00:00:00.000Z",last_login:"2025-01-20T10:30:00.000Z",posts_count:15,reports_count:0,permissions:{canLike:!0,canDislike:!0,canReportPost:!0,canReportUser:!0,canContact:!0,canPublishPost:!0,bannedUntil:null,banReason:null}},{_id:"user_002",nickname:"猫咪专家",avatar_url:"/default-avatar.png",created_at:"2024-02-10T00:00:00.000Z",last_login:"2025-01-19T15:20:00.000Z",posts_count:8,reports_count:2,permissions:{canLike:!0,canDislike:!0,canReportPost:!0,canReportUser:!0,canContact:!0,canPublishPost:!0,bannedUntil:null,banReason:null}},{_id:"user_003",nickname:"被处罚用户",avatar_url:"/default-avatar.png",created_at:"2024-03-05T00:00:00.000Z",last_login:"2025-01-18T09:15:00.000Z",posts_count:3,reports_count:12,permissions:{canLike:!0,canDislike:!0,canReportPost:!0,canReportUser:!0,canContact:!1,canPublishPost:!1,bannedUntil:null,banReason:"被多次举报为疑似骗子（12次），系统自动处罚"}}])}catch(e){console.error("加载用户列表失败:",e),c.C.error("加载用户列表失败")}finally{eF(!1)}},sm=e=>{switch(e){case"CONTEST":default:return"\uD83C\uDFC6";case"VOTING":return"\uD83D\uDDF3️";case"DISCUSSION":return"\uD83D\uDCAC"}},sh=e=>{switch(e){case"CONTEST":return"评选竞赛";case"VOTING":return"投票话题";case"DISCUSSION":return"讨论活动";default:return"未知类型"}},sp=e=>{let s={DRAFT:{label:"草稿",color:"bg-yellow-100 text-yellow-800"},ACTIVE:{label:"进行中",color:"bg-green-100 text-green-800"},ENDED:{label:"已结束",color:"bg-blue-100 text-blue-800"},ARCHIVED:{label:"已归档",color:"bg-gray-100 text-gray-800"}},t=s[e]||s.DRAFT;return(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(t.color),children:t.label})},su=e=>new Date(e).toLocaleDateString("zh-CN"),sg=async()=>{try{if(e1(!0),ez.maxImageSize<=0||ez.maxImageSize>100){c.C.error("图片大小限制必须在1-100MB之间");return}if(ez.maxImagesPerPost<=0||ez.maxImagesPerPost>20){c.C.error("每帖图片数量必须在1-20张之间");return}if(ez.autoReportThreshold<=0||ez.autoReportThreshold>50){c.C.error("自动处罚阈值必须在1-50之间");return}localStorage.setItem("systemSettings",JSON.stringify(ez)),c.C.success("设置保存成功")}catch(e){console.error("保存设置失败:",e),c.C.error("保存设置失败")}finally{e1(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white border-b border-gray-200 shadow-sm",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>e.push("/admin"),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(d.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(x.Z,{className:"w-8 h-8 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"超级管理员控制台"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["欢迎，",(null==o?void 0:o.username)||"管理员"]})]})]})]}),(0,a.jsx)(n.Z,{variant:"outline",onClick:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),e.push("/admin")},children:"退出登录"})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-6",children:[(0,a.jsxs)("div",{className:"flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1 overflow-x-auto",children:[(0,a.jsxs)("button",{onClick:()=>t("dashboard"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("dashboard"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(m,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"数据概览"})]}),(0,a.jsxs)("button",{onClick:()=>t("admins"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("admins"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(h,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"管理员管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("appeals"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("appeals"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(p,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"申诉管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("users"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("users"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(u.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"用户管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("ads"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("ads"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})}),(0,a.jsx)("span",{children:"广告管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("posts"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("posts"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"})}),(0,a.jsx)("span",{children:"帖子管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("activities"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("activities"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,a.jsx)("span",{children:"活动管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("posts"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("posts"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(g,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"内容管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("settings"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("settings"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(y.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"系统设置"})]})]}),"dashboard"===s&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,a.jsx)(h,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总用户数"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==M?void 0:M.totalUsers)||0})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,a.jsx)(g,{className:"w-6 h-6 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总宝贝数"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==M?void 0:M.totalPosts)||0})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-orange-100 rounded-lg",children:(0,a.jsx)(u.Z,{className:"w-6 h-6 text-orange-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"昨日新增用户"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==M?void 0:M.yesterdayNewUsers)||0})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-teal-100 rounded-lg",children:(0,a.jsx)(j.Z,{className:"w-6 h-6 text-teal-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"昨日新增宝贝"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==M?void 0:M.yesterdayNewPosts)||0})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,a.jsx)(N.Z,{className:"w-6 h-6 text-red-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"待处理举报"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==M?void 0:M.totalReports)||0})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,a.jsx)(p,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"待处理申诉"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==M?void 0:M.totalAppeals)||0})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-indigo-100 rounded-lg",children:(0,a.jsx)(b.Z,{className:"w-6 h-6 text-indigo-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"活跃用户"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==M?void 0:M.activeUsers)||0})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-pink-100 rounded-lg",children:(0,a.jsx)(v,{className:"w-6 h-6 text-pink-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"在线用户"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==M?void 0:M.onlineUsers)||0})]})]})})]})]}),"admins"===s&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"管理员列表"}),(null==o?void 0:o.role)==="super_admin"&&(0,a.jsxs)(n.Z,{onClick:()=>X(!0),className:"flex items-center space-x-2",children:[(0,a.jsx)(j.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"创建管理员"})]})]}),U?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-500 mt-4",children:"加载中..."})]}):0===z.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(h,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无管理员"}),(0,a.jsx)("p",{className:"text-gray-500",children:"点击上方按钮创建第一个管理员"})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"管理员信息"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"角色权限"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"创建时间"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:z.filter(e=>"superadminTT"!==e.username).map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.username}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"管理员账号"})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:"super_admin"===e.role?"超级管理员":"普通管理员"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"super_admin"===e.role?"拥有所有权限":"拥有所有业务权限"})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("active"===e.status?"bg-green-100 text-green-800":"suspended"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:"active"===e.status?"正常":"suspended"===e.status?"暂停":"禁用"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString("zh-CN")}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(null==o?void 0:o.role)==="super_admin"&&!e.is_system_account&&(0,a.jsx)("button",{onClick:()=>e7(e._id),className:"text-red-600 hover:text-red-900",title:"删除管理员",children:(0,a.jsx)(f.Z,{className:"w-4 h-4"})}),!((null==o?void 0:o.role)==="super_admin"&&!e.is_system_account)&&(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"无操作权限"})]})})]},e._id))})]})})]}),"appeals"===s&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"申诉列表"})}),U?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-500 mt-4",children:"加载中..."})]}):0===R.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(p,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无申诉"}),(0,a.jsx)("p",{className:"text-gray-500",children:"目前没有需要处理的申诉"})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:R.map(e=>(0,a.jsx)("div",{className:"p-6 hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(st(e.status)),children:sa(e.status)}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"post"===e.type?"帖子申诉":"用户申诉"}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:ss(e.created_at)})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-900 mb-2",children:[(0,a.jsx)("span",{className:"font-medium",children:"申诉理由："}),e.reason]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["举报ID: ",e.report_id," | 申诉人ID: ",e.appellant_id]}),e.admin_reason&&(0,a.jsx)("div",{className:"mt-3 p-3 bg-blue-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-900",children:[(0,a.jsx)("span",{className:"font-medium",children:"管理员回复："}),e.admin_reason]})})]}),"pending"===e.status&&(0,a.jsxs)("div",{className:"flex space-x-2 ml-4",children:[(0,a.jsxs)(n.Z,{size:"sm",variant:"outline",onClick:()=>{B(e),K("approved"),J(!0)},disabled:O===e._id,children:[(0,a.jsx)(w.Z,{className:"w-4 h-4 mr-1"}),"通过"]}),(0,a.jsxs)(n.Z,{size:"sm",variant:"outline",onClick:()=>{B(e),K("rejected"),J(!0)},disabled:O===e._id,children:[(0,a.jsx)(k.Z,{className:"w-4 h-4 mr-1"}),"驳回"]})]})]})},e._id))})]}),"users"===s&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"用户管理"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)("input",{type:"text",placeholder:"搜索用户昵称或ID...",value:eH,onChange:e=>eB(e.target.value),className:"w-64 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("label",{className:"text-sm text-gray-600",children:"自动处罚阈值:"}),(0,a.jsx)("input",{type:"number",min:"1",max:"50",value:ez.autoReportThreshold,onChange:e=>eL(s=>({...s,autoReportThreshold:parseInt(e.target.value)||10})),className:"w-16 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"人"})]})]})]})}),eO?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-500 mt-4",children:"加载中..."})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"用户信息"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"权限状态"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"统计信息"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"最后登录"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:eU.filter(e=>!eH||e.nickname.toLowerCase().includes(eH.toLowerCase())||e._id.toLowerCase().includes(eH.toLowerCase())).map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("img",{src:e.avatar_url,alt:e.nickname,className:"h-10 w-10 rounded-full mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.nickname}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["ID: ",e._id]})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"space-y-1",children:[e.permissions.canPublishPost&&e.permissions.canContact?(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"正常"}):(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:"受限"}),e.permissions.banReason&&(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-1 max-w-xs truncate",children:e.permissions.banReason})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["发布: ",e.permissions.canPublishPost?"✓":"✗"," | 联系: ",e.permissions.canContact?"✓":"✗"]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{children:["帖子: ",e.posts_count]}),(0,a.jsxs)("div",{className:e.reports_count>5?"text-red-600":"",children:["举报: ",e.reports_count]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.last_login).toLocaleDateString("zh-CN")}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsx)("button",{onClick:()=>{eW(e),eK(e.permissions),eJ(!0)},className:"text-blue-600 hover:text-blue-900 mr-3",children:"管理权限"})})]},e._id))})]})})]}),"posts"===s&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"帖子管理"})}),ea?(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-gray-500",children:"加载中..."})]}):0===es.length?(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)(g,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无帖子"}),(0,a.jsx)("p",{className:"text-gray-500",children:"还没有任何帖子"})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"帖子信息"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"作者"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"统计"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"发布时间"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:es.map(e=>{var s,t,r;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.images&&e.images[0]&&(0,a.jsx)("img",{src:e.images[0],alt:e.title,className:"h-12 w-12 rounded-lg object-cover mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 max-w-xs truncate",children:e.title}),(0,a.jsx)("div",{className:"text-sm text-gray-500 max-w-xs truncate",children:e.description}),(0,a.jsxs)("div",{className:"text-xs text-gray-400",children:[e.category," • \xa5",e.price]})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("img",{src:(null===(s=e.author_info)||void 0===s?void 0:s.avatar_url)||"/default-avatar.png",alt:null===(t=e.author_info)||void 0===t?void 0:t.nickname,className:"h-8 w-8 rounded-full mr-2"}),(0,a.jsx)("div",{className:"text-sm text-gray-900",children:(null===(r=e.author_info)||void 0===r?void 0:r.nickname)||"未知用户"})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{children:["\uD83D\uDC4D ",e.likes_count]}),(0,a.jsxs)("div",{children:["\uD83D\uDC96 ",e.wants_count]}),(0,a.jsxs)("div",{children:["⭐ ",e.avg_rating.toFixed(1)]}),e.reports_count>0&&(0,a.jsxs)("div",{className:"text-red-600",children:["\uD83D\uDEA8 ",e.reports_count]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.timeAgo}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsx)("button",{onClick:()=>{en(e),ei(!0)},className:"text-red-600 hover:text-red-900 mr-3",children:"删除"})})]},e._id)})})]})})]}),"ads"===s&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,a.jsx)(_.Z,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总展示量"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:eo.reduce((e,s)=>e+s.impressions,0).toLocaleString()})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,a.jsx)(m,{className:"w-6 h-6 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总点击量"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:eo.reduce((e,s)=>e+s.clicks,0).toLocaleString()})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,a.jsx)(C.Z,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总收益"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:sc(eo.reduce((e,s)=>e+s.spent,0))})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,a.jsx)(b.Z,{className:"w-6 h-6 text-purple-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"活跃广告"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:eo.filter(e=>"active"===e.status).length})]})]})})]}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{key:"ads",label:"广告列表",icon:_.Z},{key:"positions",label:"广告位管理",icon:m},{key:"statistics",label:"数据统计",icon:C.Z}].map(e=>(0,a.jsxs)("button",{onClick:()=>ej(e.key),className:"".concat(ey===e.key?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"," whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2"),children:[(0,a.jsx)(e.icon,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:e.label})]},e.key))})}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("div",{children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"ads"===ey?"广告列表":"positions"===ey?"广告位管理":"数据统计"})}),"ads"===ey&&(0,a.jsxs)(n.Z,{onClick:()=>eb(!0),className:"flex items-center space-x-2",children:[(0,a.jsx)(j.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"创建广告"})]})]}),"ads"===ey&&(0,a.jsx)("div",{className:"bg-white shadow rounded-lg",children:eu?(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-gray-500",children:"加载中..."})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"广告信息"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"广告位"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"数据"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"收益"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:0===eo.length?(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:6,className:"px-6 py-12 text-center text-gray-500",children:"暂无广告数据"})}):eo.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.image_url&&(0,a.jsx)("img",{src:e.image_url,alt:e.title,className:"h-10 w-10 rounded object-cover mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.advertiser_name})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.position_name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.ad_type})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:si(e.status)}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["展示: ",e.impressions.toLocaleString()]}),(0,a.jsxs)("div",{children:["点击: ",e.clicks.toLocaleString()]}),(0,a.jsxs)("div",{children:["CTR: ",sn(e.ctr)]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["预算: ",sc(e.budget)]}),(0,a.jsxs)("div",{children:["已花费: ",sc(e.spent)]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-900",children:(0,a.jsx)(_.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-green-600 hover:text-green-900",children:(0,a.jsx)(Z.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-yellow-600 hover:text-yellow-900",children:"active"===e.status?(0,a.jsx)(S.Z,{className:"w-4 h-4"}):(0,a.jsx)(b.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-red-600 hover:text-red-900",children:(0,a.jsx)(f.Z,{className:"w-4 h-4"})})]})})]},e._id))})]})})}),"positions"===ey&&(0,a.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"广告位信息"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"位置"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"尺寸"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"类型"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:eh.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.page}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.location})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.width," \xd7 ",e.height]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.ad_type}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:si(e.status)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"text-green-600 hover:text-green-900",children:(0,a.jsx)(Z.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-yellow-600 hover:text-yellow-900",children:"active"===e.status?(0,a.jsx)(S.Z,{className:"w-4 h-4"}):(0,a.jsx)(b.Z,{className:"w-4 h-4"})})]})})]},e.position_id))})]})})}),"statistics"===ey&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"用户体验策略"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"广告频率控制"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"每日最大展示次数"}),(0,a.jsx)("input",{type:"number",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",defaultValue:10,min:1,max:50})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最小展示间隔（分钟）"}),(0,a.jsx)("input",{type:"number",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",defaultValue:30,min:5,max:120})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"respectUserChoice",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",defaultChecked:!0}),(0,a.jsx)("label",{htmlFor:"respectUserChoice",className:"ml-2 block text-sm text-gray-900",children:"尊重用户隐藏选择"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"adaptiveFrequency",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",defaultChecked:!0}),(0,a.jsx)("label",{htmlFor:"adaptiveFrequency",className:"ml-2 block text-sm text-gray-900",children:"自适应展示频率"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"广告位策略"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"首页横幅"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"用户友好度：高"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"启用"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"信息流广告"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"用户友好度：中"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"启用"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"详情页底部"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"用户友好度：高"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"启用"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-red-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"启动弹窗"}),(0,a.jsx)("div",{className:"text-sm text-red-600",children:"用户友好度：低"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"禁用"}),(0,a.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-blue-600"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"用户体验建议"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"基于用户行为数据的智能推荐"})]}),(0,a.jsx)(n.Z,{className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:"保存设置"})]}),(0,a.jsxs)("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-800",children:"推荐"})]}),(0,a.jsx)("p",{className:"text-sm text-green-700 mt-1",children:"原生信息流广告，用户接受度高"})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-yellow-800",children:"谨慎"})]}),(0,a.jsx)("p",{className:"text-sm text-yellow-700 mt-1",children:"横幅广告需要控制频率"})]}),(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-red-800",children:"避免"})]}),(0,a.jsx)("p",{className:"text-sm text-red-700 mt-1",children:"弹窗广告容易引起用户反感"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"广告效果分析"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"92%"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"用户满意度"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"3.2%"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"平均点击率"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-yellow-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:"15s"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"平均停留时间"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:"8%"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"广告隐藏率"})]})]})]})]})]}),"activities"===s&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"活动管理"}),(0,a.jsx)("p",{className:"text-gray-600",children:"管理社区活动和系统配置"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)(n.Z,{onClick:()=>eS(!0),variant:"outline",className:"flex items-center space-x-2",children:[(0,a.jsx)(y.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"系统设置"})]}),(0,a.jsxs)(n.Z,{onClick:()=>eI(!0),className:"flex items-center space-x-2",children:[(0,a.jsx)(j.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"创建活动"})]})]})]}),(0,a.jsx)("div",{className:"p-4 rounded-lg ".concat((null==e_?void 0:e_.enabled)?"bg-green-50 border border-green-200":"bg-yellow-50 border border-yellow-200"),children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-3 w-3 rounded-full mr-3 ".concat((null==e_?void 0:e_.enabled)?"bg-green-500":"bg-yellow-500")}),(0,a.jsxs)("span",{className:"font-medium ".concat((null==e_?void 0:e_.enabled)?"text-green-800":"text-yellow-800"),children:["活动系统状态：",(null==e_?void 0:e_.enabled)?"已启用":"已禁用"]}),!(null==e_?void 0:e_.enabled)&&(0,a.jsx)("span",{className:"ml-2 text-yellow-700",children:"（用户端不显示活动入口）"})]})}),(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow flex space-x-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"状态筛选"}),(0,a.jsxs)("select",{value:eP,onChange:e=>eD(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 text-sm",children:[(0,a.jsx)("option",{value:"all",children:"全部状态"}),(0,a.jsx)("option",{value:"DRAFT",children:"草稿"}),(0,a.jsx)("option",{value:"ACTIVE",children:"进行中"}),(0,a.jsx)("option",{value:"ENDED",children:"已结束"}),(0,a.jsx)("option",{value:"ARCHIVED",children:"已归档"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"类型筛选"}),(0,a.jsxs)("select",{value:eM,onChange:e=>eA(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 text-sm",children:[(0,a.jsx)("option",{value:"all",children:"全部类型"}),(0,a.jsx)("option",{value:"CONTEST",children:"评选竞赛"}),(0,a.jsx)("option",{value:"VOTING",children:"投票话题"}),(0,a.jsx)("option",{value:"DISCUSSION",children:"讨论活动"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"活动列表"})}),ew?(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-gray-500",children:"加载中..."})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"活动信息"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"类型"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"时间"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"参与数据"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:0===ev.length?(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:6,className:"px-6 py-12 text-center text-gray-500",children:"暂无活动数据"})}):ev.map(e=>{var s,t;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 max-w-xs truncate",children:e.title}),(0,a.jsx)("div",{className:"text-sm text-gray-500 max-w-xs truncate",children:e.description})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-lg mr-2",children:sm(e.type)}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:sh(e.type)})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:sp(e.status)}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["开始：",su(e.start_time)]}),(0,a.jsxs)("div",{children:["结束：",su(e.end_time)]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["持续 ",e.duration_days," 天"]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["投票：",(null===(s=e.statistics_summary)||void 0===s?void 0:s.total_votes)||0]}),(0,a.jsxs)("div",{children:["评论：",(null===(t=e.statistics_summary)||void 0===t?void 0:t.total_comments)||0]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>window.open("/activities/".concat(e._id),"_blank"),className:"text-blue-600 hover:text-blue-900",title:"查看活动",children:(0,a.jsx)(_.Z,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>c.C.info("编辑功能开发中"),className:"text-green-600 hover:text-green-900",title:"编辑活动",children:(0,a.jsx)(Z.Z,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>{e3({title:"删除活动",message:"确定要删除这个活动吗？此操作不可恢复。",confirmText:"删除",cancelText:"取消",type:"danger",onConfirm:()=>{c.C.info("删除功能开发中")}})},className:"text-red-600 hover:text-red-900",title:"删除活动",children:(0,a.jsx)(f.Z,{className:"h-4 w-4"})})]})})]},e._id)})})]})})]})]}),"settings"===s&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"系统设置"}),(0,a.jsx)("p",{className:"text-gray-600",children:"配置系统参数和规则"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(T.Z,{className:"w-6 h-6 text-blue-600"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"图片上传设置"})]}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"配置用户上传图片的限制和规则"})]}),eR?(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-gray-500",children:"加载中..."})]}):(0,a.jsxs)("div",{className:"px-6 py-6 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"单张图片大小限制 (MB)"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("input",{type:"number",min:"1",max:"100",step:"1",value:ez.maxImageSize,onChange:e=>eL(s=>({...s,maxImageSize:parseInt(e.target.value)||1})),className:"block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["当前限制：",ez.maxImageSize,"MB"]})]}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"建议设置在5-30MB之间，过大会影响上传速度"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"每帖最大图片数量"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("input",{type:"number",min:"1",max:"20",step:"1",value:ez.maxImagesPerPost,onChange:e=>eL(s=>({...s,maxImagesPerPost:parseInt(e.target.value)||1})),className:"block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["当前限制：",ez.maxImagesPerPost,"张"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"支持的图片格式"}),(0,a.jsx)("div",{className:"space-y-2",children:["image/jpeg","image/png","image/webp","image/gif"].map(e=>(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:ez.allowedImageTypes.includes(e),onChange:s=>{s.target.checked?eL(s=>({...s,allowedImageTypes:[...s.allowedImageTypes,e]})):eL(s=>({...s,allowedImageTypes:s.allowedImageTypes.filter(s=>s!==e)}))},className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:e.replace("image/","").toUpperCase()})]},e))})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"自动处罚设置"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"配置自动处罚的触发条件"})]}),eR?(0,a.jsxs)("div",{className:"px-6 py-8 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-500 mt-2",children:"加载中..."})]}):(0,a.jsxs)("div",{className:"px-6 py-4 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"疑似骗子举报阈值"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("input",{type:"number",min:"1",max:"50",value:ez.autoReportThreshold,onChange:e=>eL(s=>({...s,autoReportThreshold:parseInt(e.target.value)||10})),className:"w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"人举报后自动禁止发布和联系权限"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:'当用户被举报"疑似骗子"达到此数量时，系统将自动禁止其发布宝贝和联系功能'})]}),(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(AlertTriangle,{className:"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"注意事项"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,a.jsx)("li",{children:"阈值设置过低可能导致误封，建议设置为5-15人"}),(0,a.jsx)("li",{children:"被处罚用户可以通过申诉系统申请恢复权限"}),(0,a.jsx)("li",{children:"管理员可以在用户管理中手动调整用户权限"})]})]})]})})]}),(0,a.jsxs)("div",{className:"px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between",children:[(0,a.jsxs)(n.Z,{variant:"outline",onClick:()=>{e3({title:"重置设置",message:"确定要重置为默认设置吗？当前的自定义设置将会丢失。",confirmText:"重置",cancelText:"取消",type:"warning",onConfirm:()=>{eL({maxImageSize:5,maxImagesPerPost:9,allowedImageTypes:["image/jpeg","image/png","image/webp"],autoReportThreshold:10}),c.C.success("已重置为默认设置")}})},className:"flex items-center space-x-2",children:[(0,a.jsx)(I.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"重置默认"})]}),(0,a.jsxs)(n.Z,{onClick:sg,loading:e2,disabled:e2,className:"flex items-center space-x-2",children:[(0,a.jsx)(P.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e2?"保存中...":"保存设置"})]})]})]})]})]}),Q&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"创建管理员"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["用户名 ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{type:"text",value:Y.username,onChange:e=>ee(s=>({...s,username:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入用户名"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["密码 ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{type:"password",value:Y.password,onChange:e=>ee(s=>({...s,password:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入密码"})]}),(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-700",children:"角色权限"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:"普通管理员 - 拥有所有业务权限（除删除其他管理员外）"})]})})]}),(0,a.jsxs)("div",{className:"flex space-x-3 mt-6",children:[(0,a.jsx)(n.Z,{variant:"outline",onClick:()=>{X(!1),ee({username:"",password:"",role:"admin",level:1,permissions:[]})},className:"flex-1",children:"取消"}),(0,a.jsx)(n.Z,{onClick:e8,className:"flex-1",children:"创建管理员"})]})]})}),G&&H&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"approved"===$?"通过申诉":"驳回申诉"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"申诉内容："}),(0,a.jsx)("p",{className:"text-sm bg-gray-100 p-3 rounded-lg",children:H.reason})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"处理说明"}),(0,a.jsx)("textarea",{value:q,onChange:e=>W(e.target.value),placeholder:"请说明".concat("approved"===$?"通过":"驳回","的理由..."),className:"w-full h-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(n.Z,{variant:"outline",onClick:()=>{J(!1),B(null),W("")},className:"flex-1",children:"取消"}),(0,a.jsx)(n.Z,{onClick:se,disabled:O===H._id,className:"flex-1",children:O===H._id?"处理中...":"确认"})]})]})}),el&&ec&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"删除帖子"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center mb-3",children:[ec.images&&ec.images[0]&&(0,a.jsx)("img",{src:ec.images[0],alt:ec.title,className:"h-16 w-16 rounded-lg object-cover mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:ec.title}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:ec.description})]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["删除原因 ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("textarea",{value:ed,onChange:e=>ex(e.target.value),placeholder:"请输入删除原因...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none",rows:3})]}),(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"警告"}),(0,a.jsxs)("div",{className:"mt-2 text-sm text-red-700",children:[(0,a.jsx)("p",{children:"删除帖子将同时删除："}),(0,a.jsxs)("ul",{className:"list-disc list-inside mt-1",children:[(0,a.jsx)("li",{children:"帖子的所有图片文件"}),(0,a.jsx)("li",{children:"所有点赞、收藏、评分记录"}),(0,a.jsx)("li",{children:"所有相关的举报和联系记录"})]}),(0,a.jsx)("p",{className:"mt-2 font-medium",children:"此操作不可撤销！"})]})]})]})})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(n.Z,{variant:"outline",onClick:()=>{ei(!1),en(null),ex("")},className:"flex-1",children:"取消"}),(0,a.jsx)(n.Z,{onClick:sr,className:"flex-1 bg-red-600 hover:bg-red-700 text-white",disabled:!ed.trim(),children:"确认删除"})]})]})}),eQ&&eY&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsxs)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center mr-3 ".concat("danger"===eY.type?"bg-red-100":"warning"===eY.type?"bg-yellow-100":"bg-blue-100"),children:["danger"===eY.type&&(0,a.jsx)("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),"warning"===eY.type&&(0,a.jsx)("svg",{className:"w-5 h-5 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),(!eY.type||"info"===eY.type)&&(0,a.jsx)("svg",{className:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})]}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:eY.title})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("p",{className:"text-gray-600",children:eY.message})}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(n.Z,{variant:"outline",onClick:()=>{(null==eY?void 0:eY.onCancel)&&eY.onCancel(),eX(!1),e0(null)},className:"flex-1",children:eY.cancelText||"取消"}),(0,a.jsx)(n.Z,{onClick:()=>{(null==eY?void 0:eY.onConfirm)&&eY.onConfirm(),eX(!1),e0(null)},className:"flex-1 ".concat("danger"===eY.type?"bg-red-600 hover:bg-red-700 text-white":"warning"===eY.type?"bg-yellow-600 hover:bg-yellow-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white"),children:eY.confirmText||"确认"})]})]})})]})}},56334:function(e,s,t){"use strict";var a=t(57437),r=t(2265),l=t(68661);let i=r.forwardRef((e,s)=>{let{className:t,variant:r="primary",size:i="md",loading:c=!1,icon:n,children:d,disabled:x,...o}=e;return(0,a.jsxs)("button",{className:(0,l.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[r],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[i],t),ref:s,disabled:x||c,...o,children:[c&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!c&&n&&(0,a.jsx)("span",{className:"mr-2",children:n}),d]})});i.displayName="Button",s.Z=i},9356:function(e,s,t){"use strict";t.d(s,{C:function(){return h},ToastProvider:function(){return p}});var a=t(57437);t(2265);var r=t(69064),l=t(65302),i=t(45131),c=t(22252),n=t(33245),d=t(32489),x=t(68661);let o={duration:4e3,position:"top-center",style:{background:"#fff",color:"#374151",border:"1px solid #e5e7eb",borderRadius:"8px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",padding:"12px 16px",fontSize:"14px",maxWidth:"400px"}},m=e=>{let{message:s,type:t,onDismiss:r}=e,o={success:(0,a.jsx)(l.Z,{className:"h-5 w-5 text-green-500"}),error:(0,a.jsx)(i.Z,{className:"h-5 w-5 text-red-500"}),warning:(0,a.jsx)(c.Z,{className:"h-5 w-5 text-yellow-500"}),info:(0,a.jsx)(n.Z,{className:"h-5 w-5 text-blue-500"})};return(0,a.jsxs)("div",{className:(0,x.cn)("flex items-center space-x-3 p-3 rounded-lg border shadow-lg",{success:"bg-green-50 border-green-200",error:"bg-red-50 border-red-200",warning:"bg-yellow-50 border-yellow-200",info:"bg-blue-50 border-blue-200"}[t]),children:[o[t],(0,a.jsx)("span",{className:"flex-1 text-sm font-medium",children:s}),(0,a.jsx)("button",{onClick:r,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(d.Z,{className:"h-4 w-4"})})]})},h={success:e=>{r.Am.custom(s=>(0,a.jsx)(m,{message:e,type:"success",onDismiss:()=>r.Am.dismiss(s.id)}),o)},error:e=>{r.Am.custom(s=>(0,a.jsx)(m,{message:e,type:"error",onDismiss:()=>r.Am.dismiss(s.id)}),{...o,duration:6e3})},warning:e=>{r.Am.custom(s=>(0,a.jsx)(m,{message:e,type:"warning",onDismiss:()=>r.Am.dismiss(s.id)}),o)},info:e=>{r.Am.custom(s=>(0,a.jsx)(m,{message:e,type:"info",onDismiss:()=>r.Am.dismiss(s.id)}),o)},loading:e=>r.Am.loading(e,{style:o.style,position:o.position}),dismiss:e=>{r.Am.dismiss(e)},promise:(e,s)=>r.Am.promise(e,s,{style:o.style,position:o.position})},p=()=>(0,a.jsx)(r.x7,{position:"top-center",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"transparent",boxShadow:"none",padding:0}}})},68661:function(e,s,t){"use strict";t.d(s,{cn:function(){return l},uf:function(){return i},vV:function(){return c}});var a=t(61994),r=t(53335);function l(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.m6)((0,a.W)(s))}function i(e){return e<1e3?e.toString():e<1e4?"".concat((e/1e3).toFixed(1),"k"):e<1e5?"".concat((e/1e4).toFixed(1),"w"):"".concat(Math.floor(e/1e4),"w")}function c(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}}},function(e){e.O(0,[649,19,347,554,721,11,971,117,744],function(){return e(e.s=30804)}),_N_E=e.O()}]);