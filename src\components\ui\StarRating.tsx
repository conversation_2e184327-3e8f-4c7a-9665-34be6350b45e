import React, { useState } from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/utils';

interface StarRatingProps {
  rating: number;
  onRatingChange?: (rating: number) => void;
  readonly?: boolean;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  className?: string;
}

const StarRating: React.FC<StarRatingProps> = ({
  rating,
  onRatingChange,
  readonly = false,
  size = 'md',
  showText = false,
  className
}) => {
  const [hoverRating, setHoverRating] = useState(0);

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  const getRatingText = (rating: number) => {
    if (rating >= 4.5) return '非常满意';
    if (rating >= 3.5) return '满意';
    if (rating >= 2.5) return '一般';
    if (rating >= 1.5) return '不满意';
    if (rating >= 0.5) return '很不满意';
    return '未评分';
  };

  const handleStarClick = (starRating: number) => {
    if (!readonly && onRatingChange) {
      onRatingChange(starRating);
    }
  };

  const handleStarHover = (starRating: number) => {
    if (!readonly) {
      setHoverRating(starRating);
    }
  };

  const handleMouseLeave = () => {
    if (!readonly) {
      setHoverRating(0);
    }
  };

  const displayRating = hoverRating || rating;

  return (
    <div className={cn('flex items-center space-x-1', className)}>
      <div className="flex items-center space-x-0.5" onMouseLeave={handleMouseLeave}>
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            disabled={readonly}
            onClick={() => handleStarClick(star)}
            onMouseEnter={() => handleStarHover(star)}
            className={cn(
              'transition-colors duration-150',
              readonly ? 'cursor-default' : 'cursor-pointer hover:scale-110 transform transition-transform'
            )}
          >
            <Star
              className={cn(
                sizeClasses[size],
                star <= displayRating
                  ? 'text-yellow-400 fill-yellow-400'
                  : 'text-gray-300 fill-gray-300'
              )}
            />
          </button>
        ))}
      </div>
      
      {showText && (
        <span className={cn('text-gray-600 ml-2', textSizeClasses[size])}>
          {rating > 0 ? `${rating.toFixed(1)} (${getRatingText(rating)})` : getRatingText(0)}
        </span>
      )}
    </div>
  );
};

export default StarRating;
