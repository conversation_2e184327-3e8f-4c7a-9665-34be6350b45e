'use client';

import React, { useState } from 'react';
import { petAPI } from '@/lib/cloudbase';

const DebugPage: React.FC = () => {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testGetPosts = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('开始调用 getPosts...');
      const response = await petAPI.getPosts({
        page: 1,
        limit: 10
      });
      console.log('getPosts 响应:', response);
      setResult(response);
    } catch (err: any) {
      console.error('getPosts 错误:', err);
      setError(err.message || '调用失败');
    } finally {
      setLoading(false);
    }
  };

  const testCloudbaseInit = async () => {
    try {
      setError('');
      setResult(null);

      // 检查环境变量
      const envId = process.env.NEXT_PUBLIC_CLOUDBASE_ENV_ID;
      console.log('环境ID:', envId);

      if (!envId) {
        throw new Error('环境ID未配置');
      }

      // 检查浏览器环境
      if (typeof window === 'undefined') {
        throw new Error('非浏览器环境');
      }

      console.log('浏览器环境检查通过');

      // 使用我们的 CloudBase 初始化函数
      const cloudbaseModule = await import('@/lib/cloudbase');
      const app = cloudbaseModule.app;

      console.log('CloudBase App:', app);

      setResult({
        envId,
        sdkLoaded: !!app,
        appInitialized: !!app
      });
    } catch (err: any) {
      console.error('CloudBase 初始化检查失败:', err);
      setError(err.message);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">调试页面</h1>
        
        <div className="space-y-6">
          {/* CloudBase 初始化测试 */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">CloudBase 初始化测试</h2>
            <button
              onClick={testCloudbaseInit}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
            >
              检查 CloudBase 初始化
            </button>
          </div>

          {/* 宠物列表测试 */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">宠物列表 API 测试</h2>
            <button
              onClick={testGetPosts}
              disabled={loading}
              className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-4 py-2 rounded"
            >
              {loading ? '调用中...' : '测试 getPosts'}
            </button>
          </div>

          {/* 结果显示 */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h3 className="text-red-800 font-semibold mb-2">错误信息:</h3>
              <pre className="text-red-700 text-sm">{error}</pre>
            </div>
          )}

          {result && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="text-green-800 font-semibold mb-2">调用结果:</h3>
              <pre className="text-green-700 text-sm overflow-auto max-h-96">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DebugPage;
