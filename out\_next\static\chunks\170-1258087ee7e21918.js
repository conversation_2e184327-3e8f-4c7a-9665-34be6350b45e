(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[170],{59196:function(t,e){"use strict";e.byteLength=function(t){var e=c(t),r=e[0],n=e[1];return(r+n)*3/4-n},e.toByteArray=function(t){var e,r,o=c(t),a=o[0],u=o[1],l=new i((a+u)*3/4-u),s=0,f=u>0?a-4:a;for(r=0;r<f;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],l[s++]=e>>16&255,l[s++]=e>>8&255,l[s++]=255&e;return 2===u&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,l[s++]=255&e),1===u&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,l[s++]=e>>8&255,l[s++]=255&e),l},e.fromByteArray=function(t){for(var e,n=t.length,i=n%3,o=[],a=0,u=n-i;a<u;a+=16383)o.push(function(t,e,n){for(var i,o=[],a=e;a<n;a+=3)o.push(r[(i=(t[a]<<16&16711680)+(t[a+1]<<8&65280)+(255&t[a+2]))>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return o.join("")}(t,a,a+16383>u?u:a+16383));return 1===i?o.push(r[(e=t[n-1])>>2]+r[e<<4&63]+"=="):2===i&&o.push(r[(e=(t[n-2]<<8)+t[n-1])>>10]+r[e>>4&63]+r[e<<2&63]+"="),o.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,u=o.length;a<u;++a)r[a]=o[a],n[o.charCodeAt(a)]=a;function c(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var n=r===e?0:4-r%4;return[r,n]}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},82957:function(t,e,r){"use strict";var n=r(59196),i=r(68848),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function a(t){if(t>2147483647)throw RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,u.prototype),e}function u(t,e,r){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return s(t)}return c(t,e,r)}function c(t,e,r){if("string"==typeof t)return function(t,e){if(("string"!=typeof e||""===e)&&(e="utf8"),!u.isEncoding(e))throw TypeError("Unknown encoding: "+e);var r=0|p(t,e),n=a(r),i=n.write(t,e);return i!==r&&(n=n.slice(0,i)),n}(t,e);if(ArrayBuffer.isView(t))return function(t){if(k(t,Uint8Array)){var e=new Uint8Array(t);return h(e.buffer,e.byteOffset,e.byteLength)}return f(t)}(t);if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(k(t,ArrayBuffer)||t&&k(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(k(t,SharedArrayBuffer)||t&&k(t.buffer,SharedArrayBuffer)))return h(t,e,r);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');var n=t.valueOf&&t.valueOf();if(null!=n&&n!==t)return u.from(n,e,r);var i=function(t){if(u.isBuffer(t)){var e,r=0|d(t.length),n=a(r);return 0===n.length||t.copy(n,0,0,r),n}return void 0!==t.length?"number"!=typeof t.length||(e=t.length)!=e?a(0):f(t):"Buffer"===t.type&&Array.isArray(t.data)?f(t.data):void 0}(t);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return u.from(t[Symbol.toPrimitive]("string"),e,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function l(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function s(t){return l(t),a(t<0?0:0|d(t))}function f(t){for(var e=t.length<0?0:0|d(t.length),r=a(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}function h(t,e,r){var n;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),u.prototype),n}function d(t){if(t>=2147483647)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function p(t,e){if(u.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||k(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return E(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return S(t).length;default:if(i)return n?-1:E(t).length;e=(""+e).toLowerCase(),i=!0}}function y(t,e,r){var i,o,a=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=e;o<r;++o)i+=T[t[o]];return i}(this,e,r);case"utf8":case"utf-8":return b(this,e,r);case"ascii":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}(this,e,r);case"latin1":case"binary":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}(this,e,r);case"base64":return i=e,o=r,0===i&&o===this.length?n.fromByteArray(this):n.fromByteArray(this.slice(i,o));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){for(var n=t.slice(e,r),i="",o=0;o<n.length-1;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}(this,e,r);default:if(a)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),a=!0}}function v(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function g(t,e,r,n,i){var o;if(0===t.length)return -1;if("string"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),(o=r=+r)!=o&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(i)return -1;r=t.length-1}else if(r<0){if(!i)return -1;r=0}if("string"==typeof e&&(e=u.from(e,n)),u.isBuffer(e))return 0===e.length?-1:m(t,e,r,n,i);if("number"==typeof e)return(e&=255,"function"==typeof Uint8Array.prototype.indexOf)?i?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):m(t,[e],r,n,i);throw TypeError("val must be string, number or Buffer")}function m(t,e,r,n,i){var o,a=1,u=t.length,c=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return -1;a=2,u/=2,c/=2,r/=2}function l(t,e){return 1===a?t[e]:t.readUInt16BE(e*a)}if(i){var s=-1;for(o=r;o<u;o++)if(l(t,o)===l(e,-1===s?0:o-s)){if(-1===s&&(s=o),o-s+1===c)return s*a}else -1!==s&&(o-=o-s),s=-1}else for(r+c>u&&(r=u-c),o=r;o>=0;o--){for(var f=!0,h=0;h<c;h++)if(l(t,o+h)!==l(e,h)){f=!1;break}if(f)return o}return -1}function b(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var o,a,u,c,l=t[i],s=null,f=l>239?4:l>223?3:l>191?2:1;if(i+f<=r)switch(f){case 1:l<128&&(s=l);break;case 2:(192&(o=t[i+1]))==128&&(c=(31&l)<<6|63&o)>127&&(s=c);break;case 3:o=t[i+1],a=t[i+2],(192&o)==128&&(192&a)==128&&(c=(15&l)<<12|(63&o)<<6|63&a)>2047&&(c<55296||c>57343)&&(s=c);break;case 4:o=t[i+1],a=t[i+2],u=t[i+3],(192&o)==128&&(192&a)==128&&(192&u)==128&&(c=(15&l)<<18|(63&o)<<12|(63&a)<<6|63&u)>65535&&c<1114112&&(s=c)}null===s?(s=65533,f=1):s>65535&&(s-=65536,n.push(s>>>10&1023|55296),s=56320|1023&s),n.push(s),i+=f}return function(t){var e=t.length;if(e<=4096)return String.fromCharCode.apply(String,t);for(var r="",n=0;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=4096));return r}(n)}function w(t,e,r){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>r)throw RangeError("Trying to access beyond buffer length")}function x(t,e,r,n,i,o){if(!u.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw RangeError('"value" argument is out of bounds');if(r+n>t.length)throw RangeError("Index out of range")}function O(t,e,r,n,i,o){if(r+n>t.length||r<0)throw RangeError("Index out of range")}function P(t,e,r,n,o){return e=+e,r>>>=0,o||O(t,e,r,4,34028234663852886e22,-34028234663852886e22),i.write(t,e,r,n,23,4),r+4}function j(t,e,r,n,o){return e=+e,r>>>=0,o||O(t,e,r,8,17976931348623157e292,-17976931348623157e292),i.write(t,e,r,n,52,8),r+8}e.lW=u,e.h2=50,u.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),u.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(u.prototype,"parent",{enumerable:!0,get:function(){if(u.isBuffer(this))return this.buffer}}),Object.defineProperty(u.prototype,"offset",{enumerable:!0,get:function(){if(u.isBuffer(this))return this.byteOffset}}),u.poolSize=8192,u.from=function(t,e,r){return c(t,e,r)},Object.setPrototypeOf(u.prototype,Uint8Array.prototype),Object.setPrototypeOf(u,Uint8Array),u.alloc=function(t,e,r){return(l(t),t<=0)?a(t):void 0!==e?"string"==typeof r?a(t).fill(e,r):a(t).fill(e):a(t)},u.allocUnsafe=function(t){return s(t)},u.allocUnsafeSlow=function(t){return s(t)},u.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==u.prototype},u.compare=function(t,e){if(k(t,Uint8Array)&&(t=u.from(t,t.offset,t.byteLength)),k(e,Uint8Array)&&(e=u.from(e,e.offset,e.byteLength)),!u.isBuffer(t)||!u.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:n<r?1:0},u.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},u.concat=function(t,e){if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return u.alloc(0);if(void 0===e)for(r=0,e=0;r<t.length;++r)e+=t[r].length;var r,n=u.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var o=t[r];if(k(o,Uint8Array))i+o.length>n.length?u.from(o).copy(n,i):Uint8Array.prototype.set.call(n,o,i);else if(u.isBuffer(o))o.copy(n,i);else throw TypeError('"list" argument must be an Array of Buffers');i+=o.length}return n},u.byteLength=p,u.prototype._isBuffer=!0,u.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)v(this,e,e+1);return this},u.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)v(this,e,e+3),v(this,e+1,e+2);return this},u.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)v(this,e,e+7),v(this,e+1,e+6),v(this,e+2,e+5),v(this,e+3,e+4);return this},u.prototype.toString=function(){var t=this.length;return 0===t?"":0==arguments.length?b(this,0,t):y.apply(this,arguments)},u.prototype.toLocaleString=u.prototype.toString,u.prototype.equals=function(t){if(!u.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===u.compare(this,t)},u.prototype.inspect=function(){var t="",r=e.h2;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},o&&(u.prototype[o]=u.prototype.inspect),u.prototype.compare=function(t,e,r,n,i){if(k(t,Uint8Array)&&(t=u.from(t,t.offset,t.byteLength)),!u.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return -1;if(e>=r)return 1;if(e>>>=0,r>>>=0,n>>>=0,i>>>=0,this===t)return 0;for(var o=i-n,a=r-e,c=Math.min(o,a),l=this.slice(n,i),s=t.slice(e,r),f=0;f<c;++f)if(l[f]!==s[f]){o=l[f],a=s[f];break}return o<a?-1:a<o?1:0},u.prototype.includes=function(t,e,r){return -1!==this.indexOf(t,e,r)},u.prototype.indexOf=function(t,e,r){return g(this,t,e,r,!0)},u.prototype.lastIndexOf=function(t,e,r){return g(this,t,e,r,!1)},u.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,o,a,u,c,l,s,f,h=this.length-e;if((void 0===r||r>h)&&(r=h),t.length>0&&(r<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var d=!1;;)switch(n){case"hex":return function(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=e.length;n>o/2&&(n=o/2);for(var a=0;a<n;++a){var u=parseInt(e.substr(2*a,2),16);if(u!=u)break;t[r+a]=u}return a}(this,t,e,r);case"utf8":case"utf-8":return i=e,o=r,M(E(t,this.length-i),this,i,o);case"ascii":case"latin1":case"binary":return a=e,u=r,M(function(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(t),this,a,u);case"base64":return c=e,l=r,M(S(t),this,c,l);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return s=e,f=r,M(function(t,e){for(var r,n,i=[],o=0;o<t.length&&!((e-=2)<0);++o)n=(r=t.charCodeAt(o))>>8,i.push(r%256),i.push(n);return i}(t,this.length-s),this,s,f);default:if(d)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),d=!0}},u.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},u.prototype.slice=function(t,e){var r=this.length;t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return Object.setPrototypeOf(n,u.prototype),n},u.prototype.readUintLE=u.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||w(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n},u.prototype.readUintBE=u.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||w(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},u.prototype.readUint8=u.prototype.readUInt8=function(t,e){return t>>>=0,e||w(t,1,this.length),this[t]},u.prototype.readUint16LE=u.prototype.readUInt16LE=function(t,e){return t>>>=0,e||w(t,2,this.length),this[t]|this[t+1]<<8},u.prototype.readUint16BE=u.prototype.readUInt16BE=function(t,e){return t>>>=0,e||w(t,2,this.length),this[t]<<8|this[t+1]},u.prototype.readUint32LE=u.prototype.readUInt32LE=function(t,e){return t>>>=0,e||w(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},u.prototype.readUint32BE=u.prototype.readUInt32BE=function(t,e){return t>>>=0,e||w(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},u.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||w(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},u.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||w(t,e,this.length);for(var n=e,i=1,o=this[t+--n];n>0&&(i*=256);)o+=this[t+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},u.prototype.readInt8=function(t,e){return(t>>>=0,e||w(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},u.prototype.readInt16LE=function(t,e){t>>>=0,e||w(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt16BE=function(t,e){t>>>=0,e||w(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt32LE=function(t,e){return t>>>=0,e||w(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},u.prototype.readInt32BE=function(t,e){return t>>>=0,e||w(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},u.prototype.readFloatLE=function(t,e){return t>>>=0,e||w(t,4,this.length),i.read(this,t,!0,23,4)},u.prototype.readFloatBE=function(t,e){return t>>>=0,e||w(t,4,this.length),i.read(this,t,!1,23,4)},u.prototype.readDoubleLE=function(t,e){return t>>>=0,e||w(t,8,this.length),i.read(this,t,!0,52,8)},u.prototype.readDoubleBE=function(t,e){return t>>>=0,e||w(t,8,this.length),i.read(this,t,!1,52,8)},u.prototype.writeUintLE=u.prototype.writeUIntLE=function(t,e,r,n){if(t=+t,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;x(this,t,e,r,i,0)}var o=1,a=0;for(this[e]=255&t;++a<r&&(o*=256);)this[e+a]=t/o&255;return e+r},u.prototype.writeUintBE=u.prototype.writeUIntBE=function(t,e,r,n){if(t=+t,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;x(this,t,e,r,i,0)}var o=r-1,a=1;for(this[e+o]=255&t;--o>=0&&(a*=256);)this[e+o]=t/a&255;return e+r},u.prototype.writeUint8=u.prototype.writeUInt8=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,1,255,0),this[e]=255&t,e+1},u.prototype.writeUint16LE=u.prototype.writeUInt16LE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},u.prototype.writeUint16BE=u.prototype.writeUInt16BE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},u.prototype.writeUint32LE=u.prototype.writeUInt32LE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},u.prototype.writeUint32BE=u.prototype.writeUInt32BE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},u.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var i=Math.pow(2,8*r-1);x(this,t,e,r,i-1,-i)}var o=0,a=1,u=0;for(this[e]=255&t;++o<r&&(a*=256);)t<0&&0===u&&0!==this[e+o-1]&&(u=1),this[e+o]=(t/a>>0)-u&255;return e+r},u.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var i=Math.pow(2,8*r-1);x(this,t,e,r,i-1,-i)}var o=r-1,a=1,u=0;for(this[e+o]=255&t;--o>=0&&(a*=256);)t<0&&0===u&&0!==this[e+o+1]&&(u=1),this[e+o]=(t/a>>0)-u&255;return e+r},u.prototype.writeInt8=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},u.prototype.writeInt16LE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},u.prototype.writeInt16BE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},u.prototype.writeInt32LE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,4,2147483647,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},u.prototype.writeInt32BE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},u.prototype.writeFloatLE=function(t,e,r){return P(this,t,e,!0,r)},u.prototype.writeFloatBE=function(t,e,r){return P(this,t,e,!1,r)},u.prototype.writeDoubleLE=function(t,e,r){return j(this,t,e,!0,r)},u.prototype.writeDoubleBE=function(t,e,r){return j(this,t,e,!1,r)},u.prototype.copy=function(t,e,r,n){if(!u.isBuffer(t))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i=n-r;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(e,r,n):Uint8Array.prototype.set.call(t,this.subarray(r,n),e),i},u.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!u.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===t.length){var i,o=t.charCodeAt(0);("utf8"===n&&o<128||"latin1"===n)&&(t=o)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw RangeError("Out of range index");if(r<=e)return this;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(i=e;i<r;++i)this[i]=t;else{var a=u.isBuffer(t)?t:u.from(t,n),c=a.length;if(0===c)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<r-e;++i)this[i+e]=a[i%c]}return this};var A=/[^+/0-9A-Za-z-_]/g;function E(t,e){e=e||1/0;for(var r,n=t.length,i=null,o=[],a=0;a<n;++a){if((r=t.charCodeAt(a))>55295&&r<57344){if(!i){if(r>56319||a+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return o}function S(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(A,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function M(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length)&&!(i>=t.length);++i)e[i+r]=t[i];return i}function k(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}var T=function(){for(var t="0123456789abcdef",e=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)e[n+i]=t[r]+t[i];return e}()},61134:function(t,e,r){var n;!function(i){"use strict";var o,a={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},u=!0,c="[DecimalError] ",l=c+"Invalid argument: ",s=c+"Exponent out of range: ",f=Math.floor,h=Math.pow,d=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,p=f(1286742750677284.5),y={};function v(t,e){var r,n,i,o,a,c,l,s,f=t.constructor,h=f.precision;if(!t.s||!e.s)return e.s||(e=new f(t)),u?E(e,h):e;if(l=t.d,s=e.d,a=t.e,i=e.e,l=l.slice(),o=a-i){for(o<0?(n=l,o=-o,c=s.length):(n=s,i=a,c=l.length),o>(c=(a=Math.ceil(h/7))>c?a+1:c+1)&&(o=c,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for((c=l.length)-(o=s.length)<0&&(o=c,n=s,s=l,l=n),r=0;o;)r=(l[--o]=l[o]+s[o]+r)/1e7|0,l[o]%=1e7;for(r&&(l.unshift(r),++i),c=l.length;0==l[--c];)l.pop();return e.d=l,e.e=i,u?E(e,h):e}function g(t,e,r){if(t!==~~t||t<e||t>r)throw Error(l+t)}function m(t){var e,r,n,i=t.length-1,o="",a=t[0];if(i>0){for(o+=a,e=1;e<i;e++)(r=7-(n=t[e]+"").length)&&(o+=P(r)),o+=n;(r=7-(n=(a=t[e])+"").length)&&(o+=P(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return o+a}y.absoluteValue=y.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},y.comparedTo=y.cmp=function(t){var e,r,n,i;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(i=t.d.length)?n:i;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},y.dividedBy=y.div=function(t){return b(this,new this.constructor(t))},y.dividedToIntegerBy=y.idiv=function(t){var e=this.constructor;return E(b(this,new e(t),0,1),e.precision)},y.equals=y.eq=function(t){return!this.cmp(t)},y.exponent=function(){return x(this)},y.greaterThan=y.gt=function(t){return this.cmp(t)>0},y.greaterThanOrEqualTo=y.gte=function(t){return this.cmp(t)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(t){return 0>this.cmp(t)},y.lessThanOrEqualTo=y.lte=function(t){return 1>this.cmp(t)},y.logarithm=y.log=function(t){var e,r=this.constructor,n=r.precision,i=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(o))throw Error(c+"NaN");if(this.s<1)throw Error(c+(this.s?"NaN":"-Infinity"));return this.eq(o)?new r(0):(u=!1,e=b(j(this,i),j(t,i),i),u=!0,E(e,n))},y.minus=y.sub=function(t){return t=new this.constructor(t),this.s==t.s?S(this,t):v(this,(t.s=-t.s,t))},y.modulo=y.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(c+"NaN");return this.s?(u=!1,e=b(this,t,0,1).times(t),u=!0,this.minus(e)):E(new r(this),n)},y.naturalExponential=y.exp=function(){return w(this)},y.naturalLogarithm=y.ln=function(){return j(this)},y.negated=y.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},y.plus=y.add=function(t){return t=new this.constructor(t),this.s==t.s?v(this,t):S(this,(t.s=-t.s,t))},y.precision=y.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(l+t);if(e=x(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},y.squareRoot=y.sqrt=function(){var t,e,r,n,i,o,a,l=this.constructor;if(this.s<1){if(!this.s)return new l(0);throw Error(c+"NaN")}for(t=x(this),u=!1,0==(i=Math.sqrt(+this))||i==1/0?(((e=m(this.d)).length+t)%2==0&&(e+="0"),i=Math.sqrt(e),t=f((t+1)/2)-(t<0||t%2),n=new l(e=i==1/0?"5e"+t:(e=i.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new l(i.toString()),i=a=(r=l.precision)+3;;)if(n=(o=n).plus(b(this,o,a+2)).times(.5),m(o.d).slice(0,a)===(e=m(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),i==a&&"4999"==e){if(E(o,r+1,0),o.times(o).eq(this)){n=o;break}}else if("9999"!=e)break;a+=4}return u=!0,E(n,r)},y.times=y.mul=function(t){var e,r,n,i,o,a,c,l,s,f=this.constructor,h=this.d,d=(t=new f(t)).d;if(!this.s||!t.s)return new f(0);for(t.s*=this.s,r=this.e+t.e,(l=h.length)<(s=d.length)&&(o=h,h=d,d=o,a=l,l=s,s=a),o=[],n=a=l+s;n--;)o.push(0);for(n=s;--n>=0;){for(e=0,i=l+n;i>n;)c=o[i]+d[n]*h[i-n-1]+e,o[i--]=c%1e7|0,e=c/1e7|0;o[i]=(o[i]+e)%1e7|0}for(;!o[--a];)o.pop();return e?++r:o.shift(),t.d=o,t.e=r,u?E(t,f.precision):t},y.toDecimalPlaces=y.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(g(t,0,1e9),void 0===e?e=n.rounding:g(e,0,8),E(r,t+x(r)+1,e))},y.toExponential=function(t,e){var r,n=this,i=n.constructor;return void 0===t?r=M(n,!0):(g(t,0,1e9),void 0===e?e=i.rounding:g(e,0,8),r=M(n=E(new i(n),t+1,e),!0,t+1)),r},y.toFixed=function(t,e){var r,n,i=this.constructor;return void 0===t?M(this):(g(t,0,1e9),void 0===e?e=i.rounding:g(e,0,8),r=M((n=E(new i(this),t+x(this)+1,e)).abs(),!1,t+x(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var t=this.constructor;return E(new t(this),x(this)+1,t.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(t){var e,r,n,i,a,l,s=this,h=s.constructor,d=+(t=new h(t));if(!t.s)return new h(o);if(!(s=new h(s)).s){if(t.s<1)throw Error(c+"Infinity");return s}if(s.eq(o))return s;if(n=h.precision,t.eq(o))return E(s,n);if(l=(e=t.e)>=(r=t.d.length-1),a=s.s,l){if((r=d<0?-d:d)<=9007199254740991){for(i=new h(o),e=Math.ceil(n/7+4),u=!1;r%2&&k((i=i.times(s)).d,e),0!==(r=f(r/2));)k((s=s.times(s)).d,e);return u=!0,t.s<0?new h(o).div(i):E(i,n)}}else if(a<0)throw Error(c+"NaN");return a=a<0&&1&t.d[Math.max(e,r)]?-1:1,s.s=1,u=!1,i=t.times(j(s,n+12)),u=!0,(i=w(i)).s=a,i},y.toPrecision=function(t,e){var r,n,i=this,o=i.constructor;return void 0===t?(r=x(i),n=M(i,r<=o.toExpNeg||r>=o.toExpPos)):(g(t,1,1e9),void 0===e?e=o.rounding:g(e,0,8),r=x(i=E(new o(i),t,e)),n=M(i,t<=r||r<=o.toExpNeg,t)),n},y.toSignificantDigits=y.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(g(t,1,1e9),void 0===e?e=r.rounding:g(e,0,8)),E(new r(this),t,e)},y.toString=y.valueOf=y.val=y.toJSON=function(){var t=x(this),e=this.constructor;return M(this,t<=e.toExpNeg||t>=e.toExpPos)};var b=function(){function t(t,e){var r,n=0,i=t.length;for(t=t.slice();i--;)r=t[i]*e+n,t[i]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var i,o;if(r!=n)o=r>n?1:-1;else for(i=o=0;i<r;i++)if(t[i]!=e[i]){o=t[i]>e[i]?1:-1;break}return o}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=t[r]<e[r]?1:0,t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,i,o,a){var u,l,s,f,h,d,p,y,v,g,m,b,w,O,P,j,A,S,M=n.constructor,k=n.s==i.s?1:-1,T=n.d,C=i.d;if(!n.s)return new M(n);if(!i.s)throw Error(c+"Division by zero");for(s=0,l=n.e-i.e,A=C.length,P=T.length,y=(p=new M(k)).d=[];C[s]==(T[s]||0);)++s;if(C[s]>(T[s]||0)&&--l,(b=null==o?o=M.precision:a?o+(x(n)-x(i))+1:o)<0)return new M(0);if(b=b/7+2|0,s=0,1==A)for(f=0,C=C[0],b++;(s<P||f)&&b--;s++)w=1e7*f+(T[s]||0),y[s]=w/C|0,f=w%C|0;else{for((f=1e7/(C[0]+1)|0)>1&&(C=t(C,f),T=t(T,f),A=C.length,P=T.length),O=A,g=(v=T.slice(0,A)).length;g<A;)v[g++]=0;(S=C.slice()).unshift(0),j=C[0],C[1]>=1e7/2&&++j;do f=0,(u=e(C,v,A,g))<0?(m=v[0],A!=g&&(m=1e7*m+(v[1]||0)),(f=m/j|0)>1?(f>=1e7&&(f=1e7-1),d=(h=t(C,f)).length,g=v.length,1==(u=e(h,v,d,g))&&(f--,r(h,A<d?S:C,d))):(0==f&&(u=f=1),h=C.slice()),(d=h.length)<g&&h.unshift(0),r(v,h,g),-1==u&&(g=v.length,(u=e(C,v,A,g))<1&&(f++,r(v,A<g?S:C,g))),g=v.length):0===u&&(f++,v=[0]),y[s++]=f,u&&v[0]?v[g++]=T[O]||0:(v=[T[O]],g=1);while((O++<P||void 0!==v[0])&&b--)}return y[0]||y.shift(),p.e=l,E(p,a?o+x(p)+1:o)}}();function w(t,e){var r,n,i,a,c,l=0,f=0,d=t.constructor,p=d.precision;if(x(t)>16)throw Error(s+x(t));if(!t.s)return new d(o);for(null==e?(u=!1,c=p):c=e,a=new d(.03125);t.abs().gte(.1);)t=t.times(a),f+=5;for(c+=Math.log(h(2,f))/Math.LN10*2+5|0,r=n=i=new d(o),d.precision=c;;){if(n=E(n.times(t),c),r=r.times(++l),m((a=i.plus(b(n,r,c))).d).slice(0,c)===m(i.d).slice(0,c)){for(;f--;)i=E(i.times(i),c);return d.precision=p,null==e?(u=!0,E(i,p)):i}i=a}}function x(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function O(t,e,r){if(e>t.LN10.sd())throw u=!0,r&&(t.precision=r),Error(c+"LN10 precision limit exceeded");return E(new t(t.LN10),e)}function P(t){for(var e="";t--;)e+="0";return e}function j(t,e){var r,n,i,a,l,s,f,h,d,p=1,y=t,v=y.d,g=y.constructor,w=g.precision;if(y.s<1)throw Error(c+(y.s?"NaN":"-Infinity"));if(y.eq(o))return new g(0);if(null==e?(u=!1,h=w):h=e,y.eq(10))return null==e&&(u=!0),O(g,h);if(h+=10,g.precision=h,n=(r=m(v)).charAt(0),!(15e14>Math.abs(a=x(y))))return f=O(g,h+2,w).times(a+""),y=j(new g(n+"."+r.slice(1)),h-10).plus(f),g.precision=w,null==e?(u=!0,E(y,w)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=m((y=y.times(t)).d)).charAt(0),p++;for(a=x(y),n>1?(y=new g("0."+r),a++):y=new g(n+"."+r.slice(1)),s=l=y=b(y.minus(o),y.plus(o),h),d=E(y.times(y),h),i=3;;){if(l=E(l.times(d),h),m((f=s.plus(b(l,new g(i),h))).d).slice(0,h)===m(s.d).slice(0,h))return s=s.times(2),0!==a&&(s=s.plus(O(g,h+2,w).times(a+""))),s=b(s,new g(p),h),g.precision=w,null==e?(u=!0,E(s,w)):s;s=f,i+=2}}function A(t,e){var r,n,i;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(i=e.length;48===e.charCodeAt(i-1);)--i;if(e=e.slice(n,i)){if(i-=n,r=r-n-1,t.e=f(r/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&t.d.push(+e.slice(0,n)),i-=7;n<i;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=i;for(;n--;)e+="0";if(t.d.push(+e),u&&(t.e>p||t.e<-p))throw Error(s+r)}else t.s=0,t.e=0,t.d=[0];return t}function E(t,e,r){var n,i,o,a,c,l,d,y,v=t.d;for(a=1,o=v[0];o>=10;o/=10)a++;if((n=e-a)<0)n+=7,i=e,d=v[y=0];else{if((y=Math.ceil((n+1)/7))>=(o=v.length))return t;for(a=1,d=o=v[y];o>=10;o/=10)a++;n%=7,i=n-7+a}if(void 0!==r&&(c=d/(o=h(10,a-i-1))%10|0,l=e<0||void 0!==v[y+1]||d%o,l=r<4?(c||l)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||l||6==r&&(n>0?i>0?d/h(10,a-i):0:v[y-1])%10&1||r==(t.s<0?8:7))),e<1||!v[0])return l?(o=x(t),v.length=1,e=e-o-1,v[0]=h(10,(7-e%7)%7),t.e=f(-e/7)||0):(v.length=1,v[0]=t.e=t.s=0),t;if(0==n?(v.length=y,o=1,y--):(v.length=y+1,o=h(10,7-n),v[y]=i>0?(d/h(10,a-i)%h(10,i)|0)*o:0),l)for(;;){if(0==y){1e7==(v[0]+=o)&&(v[0]=1,++t.e);break}if(v[y]+=o,1e7!=v[y])break;v[y--]=0,o=1}for(n=v.length;0===v[--n];)v.pop();if(u&&(t.e>p||t.e<-p))throw Error(s+x(t));return t}function S(t,e){var r,n,i,o,a,c,l,s,f,h,d=t.constructor,p=d.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new d(t),u?E(e,p):e;if(l=t.d,h=e.d,n=e.e,s=t.e,l=l.slice(),a=s-n){for((f=a<0)?(r=l,a=-a,c=h.length):(r=h,n=s,c=l.length),a>(i=Math.max(Math.ceil(p/7),c)+2)&&(a=i,r.length=1),r.reverse(),i=a;i--;)r.push(0);r.reverse()}else{for((f=(i=l.length)<(c=h.length))&&(c=i),i=0;i<c;i++)if(l[i]!=h[i]){f=l[i]<h[i];break}a=0}for(f&&(r=l,l=h,h=r,e.s=-e.s),c=l.length,i=h.length-c;i>0;--i)l[c++]=0;for(i=h.length;i>a;){if(l[--i]<h[i]){for(o=i;o&&0===l[--o];)l[o]=1e7-1;--l[o],l[i]+=1e7}l[i]-=h[i]}for(;0===l[--c];)l.pop();for(;0===l[0];l.shift())--n;return l[0]?(e.d=l,e.e=n,u?E(e,p):e):new d(0)}function M(t,e,r){var n,i=x(t),o=m(t.d),a=o.length;return e?(r&&(n=r-a)>0?o=o.charAt(0)+"."+o.slice(1)+P(n):a>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+P(-i-1)+o,r&&(n=r-a)>0&&(o+=P(n))):i>=a?(o+=P(i+1-a),r&&(n=r-i-1)>0&&(o=o+"."+P(n))):((n=i+1)<a&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-a)>0&&(i+1===a&&(o+="."),o+=P(n))),t.s<0?"-"+o:o}function k(t,e){if(t.length>e)return t.length=e,!0}function T(t){if(!t||"object"!=typeof t)throw Error(c+"Object expected");var e,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<i.length;e+=3)if(void 0!==(n=t[r=i[e]])){if(f(n)===n&&n>=i[e+1]&&n<=i[e+2])this[r]=n;else throw Error(l+r+": "+n)}if(void 0!==(n=t[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(l+r+": "+n)}return this}(a=function t(e){var r,n,i;function o(t){if(!(this instanceof o))return new o(t);if(this.constructor=o,t instanceof o){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(l+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return A(this,t.toString())}if("string"!=typeof t)throw Error(l+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,d.test(t))A(this,t);else throw Error(l+t)}if(o.prototype=y,o.ROUND_UP=0,o.ROUND_DOWN=1,o.ROUND_CEIL=2,o.ROUND_FLOOR=3,o.ROUND_HALF_UP=4,o.ROUND_HALF_DOWN=5,o.ROUND_HALF_EVEN=6,o.ROUND_HALF_CEIL=7,o.ROUND_HALF_FLOOR=8,o.clone=t,o.config=o.set=T,void 0===e&&(e={}),e)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)e.hasOwnProperty(n=i[r++])||(e[n]=this[n]);return o.config(e),o}(a)).default=a.Decimal=a,o=new a(1),void 0!==(n=(function(){return a}).call(e,r,e,t))&&(t.exports=n)}(0)},15870:function(t,e,r){t.exports=r(84073).get},38450:function(t,e,r){t.exports=r(7972).isEqual},46802:function(t,e,r){t.exports=r(49483).isPlainObject},37618:function(t,e,r){t.exports=r(17858).last},41664:function(t,e,r){t.exports=r(11480).range},31104:function(t,e,r){t.exports=r(26273).sortBy},34926:function(t,e,r){t.exports=r(9313).throttle},36841:function(t,e,r){t.exports=r(62992).uniqBy},95491:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isUnsafeProperty=function(t){return"__proto__"===t}},949:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.flatten=function(t,e=1){let r=[],n=Math.floor(e),i=(t,e)=>{for(let o=0;o<t.length;o++){let a=t[o];Array.isArray(a)&&e<n?i(a,e+1):r.push(a)}};return i(t,0),r}},68509:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.last=function(t){return t[t.length-1]}},65745:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.uniqBy=function(t,e){let r=new Map;for(let n=0;n<t.length;n++){let i=t[n],o=e(i);r.has(o)||r.set(o,i)}return Array.from(r.values())}},1260:function(t,e){"use strict";function r(t){return"symbol"==typeof t?1:null===t?2:void 0===t?3:t!=t?4:0}Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.compareValues=(t,e,n)=>{if(t!==e){let i=r(t),o=r(e);if(i===o&&0===i){if(t<e)return"desc"===n?1:-1;if(t>e)return"desc"===n?-1:1}return"desc"===n?o-i:i-o}return 0}},59924:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.getSymbols=function(t){return Object.getOwnPropertySymbols(t).filter(e=>Object.prototype.propertyIsEnumerable.call(t,e))}},83984:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.getTag=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}},35159:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isDeepKey=function(t){switch(typeof t){case"number":case"symbol":return!1;case"string":return t.includes(".")||t.includes("[")||t.includes("]")}}},84623:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;e.isIndex=function(t,e=Number.MAX_SAFE_INTEGER){switch(typeof t){case"number":return Number.isInteger(t)&&t>=0&&t<e;case"symbol":return!1;case"string":return r.test(t)}}},34893:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(84623),i=r(98934),o=r(13400),a=r(95037);e.isIterateeCall=function(t,e,r){return!!o.isObject(r)&&(!!("number"==typeof e&&i.isArrayLike(r)&&n.isIndex(e))&&e<r.length||"string"==typeof e&&e in r)&&a.eq(r[e],t)}},45799:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(74675),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,o=/^\w*$/;e.isKey=function(t,e){return!Array.isArray(t)&&(!!("number"==typeof t||"boolean"==typeof t||null==t||n.isSymbol(t))||"string"==typeof t&&(o.test(t)||!i.test(t))||null!=e&&Object.hasOwn(e,t))}},91116:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.argumentsTag="[object Arguments]",e.arrayBufferTag="[object ArrayBuffer]",e.arrayTag="[object Array]",e.bigInt64ArrayTag="[object BigInt64Array]",e.bigUint64ArrayTag="[object BigUint64Array]",e.booleanTag="[object Boolean]",e.dataViewTag="[object DataView]",e.dateTag="[object Date]",e.errorTag="[object Error]",e.float32ArrayTag="[object Float32Array]",e.float64ArrayTag="[object Float64Array]",e.functionTag="[object Function]",e.int16ArrayTag="[object Int16Array]",e.int32ArrayTag="[object Int32Array]",e.int8ArrayTag="[object Int8Array]",e.mapTag="[object Map]",e.numberTag="[object Number]",e.objectTag="[object Object]",e.regexpTag="[object RegExp]",e.setTag="[object Set]",e.stringTag="[object String]",e.symbolTag="[object Symbol]",e.uint16ArrayTag="[object Uint16Array]",e.uint32ArrayTag="[object Uint32Array]",e.uint8ArrayTag="[object Uint8Array]",e.uint8ClampedArrayTag="[object Uint8ClampedArray]"},77003:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.toArray=function(t){return Array.isArray(t)?t:Array.from(t)}},49670:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.toKey=function(t){return"string"==typeof t||"symbol"==typeof t?t:Object.is(t?.valueOf?.(),-0)?"-0":String(t)}},17858:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(68509),i=r(77003),o=r(98934);e.last=function(t){if(o.isArrayLike(t))return n.last(i.toArray(t))}},46688:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(1260),i=r(45799),o=r(49027);e.orderBy=function(t,e,r,a){if(null==t)return[];r=a?void 0:r,Array.isArray(t)||(t=Object.values(t)),Array.isArray(e)||(e=null==e?[null]:[e]),0===e.length&&(e=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(t=>String(t));let u=(t,e)=>{let r=t;for(let t=0;t<e.length&&null!=r;++t)r=r[e[t]];return r},c=(t,e)=>null==e||null==t?e:"object"==typeof t&&"key"in t?Object.hasOwn(e,t.key)?e[t.key]:u(e,t.path):"function"==typeof t?t(e):Array.isArray(t)?u(e,t):"object"==typeof e?e[t]:e,l=e.map(t=>(Array.isArray(t)&&1===t.length&&(t=t[0]),null==t||"function"==typeof t||Array.isArray(t)||i.isKey(t))?t:{key:t,path:o.toPath(t)});return t.map(t=>({original:t,criteria:l.map(e=>c(e,t))})).slice().sort((t,e)=>{for(let i=0;i<l.length;i++){let o=n.compareValues(t.criteria[i],e.criteria[i],r[i]);if(0!==o)return o}return 0}).map(t=>t.original)}},26273:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(46688),i=r(949),o=r(34893);e.sortBy=function(t,...e){let r=e.length;return r>1&&o.isIterateeCall(t,e[0],e[1])?e=[]:r>2&&o.isIterateeCall(e[0],e[1],e[2])&&(e=[e[0]]),n.orderBy(t,i.flatten(e),["asc"])}},62992:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(65745),i=r(50601),o=r(79311),a=r(55586);e.uniqBy=function(t,e=i.identity){return o.isArrayLikeObject(t)?n.uniqBy(Array.from(t),a.iteratee(e)):[]}},12801:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.debounce=function(t,e=0,r={}){let n;"object"!=typeof r&&(r={});let i=null,o=null,a=null,u=0,c=null,{leading:l=!1,trailing:s=!0,maxWait:f}=r,h="maxWait"in r,d=h?Math.max(Number(f)||0,e):0,p=e=>(null!==i&&(n=t.apply(o,i)),i=o=null,u=e,n),y=t=>(u=t,c=setTimeout(b,e),l&&null!==i)?p(t):n,v=t=>(c=null,s&&null!==i)?p(t):n,g=t=>{if(null===a)return!0;let r=t-a,n=h&&t-u>=d;return r>=e||r<0||n},m=t=>{let r=e-(null===a?0:t-a),n=d-(t-u);return h?Math.min(r,n):r},b=()=>{let t=Date.now();if(g(t))return v(t);c=setTimeout(b,m(t))},w=function(...t){let r=Date.now(),u=g(r);if(i=t,o=this,a=r,u){if(null===c)return y(r);if(h)return clearTimeout(c),c=setTimeout(b,e),p(r)}return null===c&&(c=setTimeout(b,e)),n};return w.cancel=()=>{null!==c&&clearTimeout(c),u=0,a=i=o=c=null},w.flush=()=>null===c?n:v(Date.now()),w}},9313:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(12801);e.throttle=function(t,e=0,r={}){let{leading:i=!0,trailing:o=!0}=r;return n.debounce(t,e,{leading:i,maxWait:e,trailing:o})}},11480:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(34893),i=r(95810);e.range=function(t,e,r){r&&"number"!=typeof r&&n.isIterateeCall(t,e,r)&&(e=r=void 0),t=i.toFinite(t),void 0===e?(e=t,t=0):e=i.toFinite(e),r=void 0===r?t<e?1:-1:i.toFinite(r);let o=Math.max(Math.ceil((e-t)/(r||1)),0),a=Array(o);for(let e=0;e<o;e++)a[e]=t,t+=r;return a}},5076:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(887);e.cloneDeep=function(t){return n.cloneDeepWith(t)}},887:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(16737),i=r(91116);e.cloneDeepWith=function(t,e){return n.cloneDeepWith(t,(r,o,a,u)=>{let c=e?.(r,o,a,u);if(null!=c)return c;if("object"==typeof t)switch(Object.prototype.toString.call(t)){case i.numberTag:case i.stringTag:case i.booleanTag:{let e=new t.constructor(t?.valueOf());return n.copyProperties(e,t),e}case i.argumentsTag:{let e={};return n.copyProperties(e,t),e.length=t.length,e[Symbol.iterator]=t[Symbol.iterator],e}default:return}})}},84073:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(95491),i=r(35159),o=r(49670),a=r(49027);e.get=function t(e,r,u){if(null==e)return u;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return u;let o=e[r];if(void 0===o){if(i.isDeepKey(r))return t(e,a.toPath(r),u);return u}return o}case"number":case"symbol":{"number"==typeof r&&(r=o.toKey(r));let t=e[r];if(void 0===t)return u;return t}default:{if(Array.isArray(r))return function(t,e,r){if(0===e.length)return r;let i=t;for(let t=0;t<e.length;t++){if(null==i||n.isUnsafeProperty(e[t]))return r;i=i[e[t]]}return void 0===i?r:i}(e,r,u);if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return u;let t=e[r];if(void 0===t)return u;return t}}}},18141:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(35159),i=r(84623),o=r(62094),a=r(49027);e.has=function(t,e){let r;if(0===(r=Array.isArray(e)?e:"string"==typeof e&&n.isDeepKey(e)&&t?.[e]==null?a.toPath(e):[e]).length)return!1;let u=t;for(let t=0;t<r.length;t++){let e=r[t];if((null==u||!Object.hasOwn(u,e))&&!((Array.isArray(u)||o.isArguments(u))&&i.isIndex(e)&&e<u.length))return!1;u=u[e]}return!0}},83752:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(84073);e.property=function(t){return function(e){return n.get(e,t)}}},62094:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(83984);e.isArguments=function(t){return null!==t&&"object"==typeof t&&"[object Arguments]"===n.getTag(t)}},98934:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(48973);e.isArrayLike=function(t){return null!=t&&"function"!=typeof t&&n.isLength(t.length)}},79311:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(98934),i=r(77475);e.isArrayLikeObject=function(t){return i.isObjectLike(t)&&n.isArrayLike(t)}},16643:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(80757);e.isMatch=function(t,e){return n.isMatchWith(t,e,()=>void 0)}},80757:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(16643),i=r(13400),o=r(34526),a=r(95037);function u(t,e,r,n){if(e===t)return!0;switch(typeof e){case"object":return function(t,e,r,n){if(null==e)return!0;if(Array.isArray(e))return c(t,e,r,n);if(e instanceof Map)return function(t,e,r,n){if(0===e.size)return!0;if(!(t instanceof Map))return!1;for(let[i,o]of e.entries())if(!1===r(t.get(i),o,i,t,e,n))return!1;return!0}(t,e,r,n);if(e instanceof Set)return l(t,e,r,n);let i=Object.keys(e);if(null==t)return 0===i.length;if(0===i.length)return!0;if(n&&n.has(e))return n.get(e)===t;n&&n.set(e,t);try{for(let a=0;a<i.length;a++){let u=i[a];if(!o.isPrimitive(t)&&!(u in t)||void 0===e[u]&&void 0!==t[u]||null===e[u]&&null!==t[u]||!r(t[u],e[u],u,t,e,n))return!1}return!0}finally{n&&n.delete(e)}}(t,e,r,n);case"function":if(Object.keys(e).length>0)return u(t,{...e},r,n);return a.eq(t,e);default:if(!i.isObject(t))return a.eq(t,e);if("string"==typeof e)return""===e;return!0}}function c(t,e,r,n){if(0===e.length)return!0;if(!Array.isArray(t))return!1;let i=new Set;for(let o=0;o<e.length;o++){let a=e[o],u=!1;for(let c=0;c<t.length;c++){if(i.has(c))continue;let l=t[c],s=!1;if(r(l,a,o,t,e,n)&&(s=!0),s){i.add(c),u=!0;break}}if(!u)return!1}return!0}function l(t,e,r,n){return 0===e.size||t instanceof Set&&c([...t],[...e],r,n)}e.isMatchWith=function(t,e,r){return"function"!=typeof r?n.isMatch(t,e):u(t,e,function t(e,n,i,o,a,c){let l=r(e,n,i,o,a,c);return void 0!==l?!!l:u(e,n,t,c)},new Map)},e.isSetMatch=l},13400:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isObject=function(t){return null!==t&&("object"==typeof t||"function"==typeof t)}},77475:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isObjectLike=function(t){return"object"==typeof t&&null!==t}},49483:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isPlainObject=function(t){if("object"!=typeof t||null==t)return!1;if(null===Object.getPrototypeOf(t))return!0;if("[object Object]"!==Object.prototype.toString.call(t)){let e=t[Symbol.toStringTag];return!!(null!=e&&Object.getOwnPropertyDescriptor(t,Symbol.toStringTag)?.writable)&&t.toString()===`[object ${e}]`}let e=t;for(;null!==Object.getPrototypeOf(e);)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e}},74675:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isSymbol=function(t){return"symbol"==typeof t||t instanceof Symbol}},76536:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(16643),i=r(60631);e.matches=function(t){return t=i.cloneDeep(t),e=>n.isMatch(e,t)}},14308:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(16643),i=r(49670),o=r(5076),a=r(84073),u=r(18141);e.matchesProperty=function(t,e){switch(typeof t){case"object":Object.is(t?.valueOf(),-0)&&(t="-0");break;case"number":t=i.toKey(t)}return e=o.cloneDeep(e),function(r){let i=a.get(r,t);return void 0===i?u.has(r,t):void 0===e?void 0===i:n.isMatch(i,e)}}},95037:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.eq=function(t,e){return t===e||Number.isNaN(t)&&Number.isNaN(e)}},55586:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(50601),i=r(83752),o=r(76536),a=r(14308);e.iteratee=function(t){if(null==t)return n.identity;switch(typeof t){case"function":return t;case"object":if(Array.isArray(t)&&2===t.length)return a.matchesProperty(t[0],t[1]);return o.matches(t);case"string":case"symbol":case"number":return i.property(t)}}},95810:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(44201);e.toFinite=function(t){return t?(t=n.toNumber(t))===1/0||t===-1/0?(t<0?-1:1)*Number.MAX_VALUE:t==t?t:0:0===t?t:0}},44201:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(74675);e.toNumber=function(t){return n.isSymbol(t)?NaN:Number(t)}},49027:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.toPath=function(t){let e=[],r=t.length;if(0===r)return e;let n=0,i="",o="",a=!1;for(46===t.charCodeAt(0)&&(e.push(""),n++);n<r;){let u=t[n];o?"\\"===u&&n+1<r?i+=t[++n]:u===o?o="":i+=u:a?'"'===u||"'"===u?o=u:"]"===u?(a=!1,e.push(i),i=""):i+=u:"["===u?(a=!0,i&&(e.push(i),i="")):"."===u?i&&(e.push(i),i=""):i+=u,n++}return i&&e.push(i),e}},50601:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.identity=function(t){return t}},60196:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.noop=function(){}},60631:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(16737);e.cloneDeep=function(t){return n.cloneDeepWithImpl(t,void 0,t,new Map,void 0)}},16737:function(t,e,r){"use strict";var n=r(82957).lW;Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let i=r(59924),o=r(83984),a=r(91116),u=r(34526),c=r(23497);function l(t,e,r,i=new Map,f){let h=f?.(t,e,r,i);if(null!=h)return h;if(u.isPrimitive(t))return t;if(i.has(t))return i.get(t);if(Array.isArray(t)){let e=Array(t.length);i.set(t,e);for(let n=0;n<t.length;n++)e[n]=l(t[n],n,r,i,f);return Object.hasOwn(t,"index")&&(e.index=t.index),Object.hasOwn(t,"input")&&(e.input=t.input),e}if(t instanceof Date)return new Date(t.getTime());if(t instanceof RegExp){let e=new RegExp(t.source,t.flags);return e.lastIndex=t.lastIndex,e}if(t instanceof Map){let e=new Map;for(let[n,o]of(i.set(t,e),t))e.set(n,l(o,n,r,i,f));return e}if(t instanceof Set){let e=new Set;for(let n of(i.set(t,e),t))e.add(l(n,void 0,r,i,f));return e}if(void 0!==n&&n.isBuffer(t))return t.subarray();if(c.isTypedArray(t)){let e=new(Object.getPrototypeOf(t)).constructor(t.length);i.set(t,e);for(let n=0;n<t.length;n++)e[n]=l(t[n],n,r,i,f);return e}if(t instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&t instanceof SharedArrayBuffer)return t.slice(0);if(t instanceof DataView){let e=new DataView(t.buffer.slice(0),t.byteOffset,t.byteLength);return i.set(t,e),s(e,t,r,i,f),e}if("undefined"!=typeof File&&t instanceof File){let e=new File([t],t.name,{type:t.type});return i.set(t,e),s(e,t,r,i,f),e}if(t instanceof Blob){let e=new Blob([t],{type:t.type});return i.set(t,e),s(e,t,r,i,f),e}if(t instanceof Error){let e=new t.constructor;return i.set(t,e),e.message=t.message,e.name=t.name,e.stack=t.stack,e.cause=t.cause,s(e,t,r,i,f),e}if("object"==typeof t&&function(t){switch(o.getTag(t)){case a.argumentsTag:case a.arrayTag:case a.arrayBufferTag:case a.dataViewTag:case a.booleanTag:case a.dateTag:case a.float32ArrayTag:case a.float64ArrayTag:case a.int8ArrayTag:case a.int16ArrayTag:case a.int32ArrayTag:case a.mapTag:case a.numberTag:case a.objectTag:case a.regexpTag:case a.setTag:case a.stringTag:case a.symbolTag:case a.uint8ArrayTag:case a.uint8ClampedArrayTag:case a.uint16ArrayTag:case a.uint32ArrayTag:return!0;default:return!1}}(t)){let e=Object.create(Object.getPrototypeOf(t));return i.set(t,e),s(e,t,r,i,f),e}return t}function s(t,e,r=t,n,o){let a=[...Object.keys(e),...i.getSymbols(e)];for(let i=0;i<a.length;i++){let u=a[i],c=Object.getOwnPropertyDescriptor(t,u);(null==c||c.writable)&&(t[u]=l(e[u],u,r,n,o))}}e.cloneDeepWith=function(t,e){return l(t,void 0,t,new Map,e)},e.cloneDeepWithImpl=l,e.copyProperties=s},7972:function(t,e,r){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(56993),i=r(60196);e.isEqual=function(t,e){return n.isEqualWith(t,e,i.noop)}},56993:function(t,e,r){"use strict";var n=r(82957).lW;Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let i=r(7252),o=r(59924),a=r(83984),u=r(91116),c=r(95037);e.isEqualWith=function(t,e,r){return function t(e,r,l,s,f,h,d){let p=d(e,r,l,s,f,h);if(void 0!==p)return p;if(typeof e==typeof r)switch(typeof e){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return e===r;case"number":return e===r||Object.is(e,r)}return function e(r,l,s,f){if(Object.is(r,l))return!0;let h=a.getTag(r),d=a.getTag(l);if(h===u.argumentsTag&&(h=u.objectTag),d===u.argumentsTag&&(d=u.objectTag),h!==d)return!1;switch(h){case u.stringTag:return r.toString()===l.toString();case u.numberTag:{let t=r.valueOf(),e=l.valueOf();return c.eq(t,e)}case u.booleanTag:case u.dateTag:case u.symbolTag:return Object.is(r.valueOf(),l.valueOf());case u.regexpTag:return r.source===l.source&&r.flags===l.flags;case u.functionTag:return r===l}let p=(s=s??new Map).get(r),y=s.get(l);if(null!=p&&null!=y)return p===l;s.set(r,l),s.set(l,r);try{switch(h){case u.mapTag:if(r.size!==l.size)return!1;for(let[e,n]of r.entries())if(!l.has(e)||!t(n,l.get(e),e,r,l,s,f))return!1;return!0;case u.setTag:{if(r.size!==l.size)return!1;let e=Array.from(r.values()),n=Array.from(l.values());for(let i=0;i<e.length;i++){let o=e[i],a=n.findIndex(e=>t(o,e,void 0,r,l,s,f));if(-1===a)return!1;n.splice(a,1)}return!0}case u.arrayTag:case u.uint8ArrayTag:case u.uint8ClampedArrayTag:case u.uint16ArrayTag:case u.uint32ArrayTag:case u.bigUint64ArrayTag:case u.int8ArrayTag:case u.int16ArrayTag:case u.int32ArrayTag:case u.bigInt64ArrayTag:case u.float32ArrayTag:case u.float64ArrayTag:if(void 0!==n&&n.isBuffer(r)!==n.isBuffer(l)||r.length!==l.length)return!1;for(let e=0;e<r.length;e++)if(!t(r[e],l[e],e,r,l,s,f))return!1;return!0;case u.arrayBufferTag:if(r.byteLength!==l.byteLength)return!1;return e(new Uint8Array(r),new Uint8Array(l),s,f);case u.dataViewTag:if(r.byteLength!==l.byteLength||r.byteOffset!==l.byteOffset)return!1;return e(new Uint8Array(r),new Uint8Array(l),s,f);case u.errorTag:return r.name===l.name&&r.message===l.message;case u.objectTag:{if(!(e(r.constructor,l.constructor,s,f)||i.isPlainObject(r)&&i.isPlainObject(l)))return!1;let n=[...Object.keys(r),...o.getSymbols(r)],a=[...Object.keys(l),...o.getSymbols(l)];if(n.length!==a.length)return!1;for(let e=0;e<n.length;e++){let i=n[e],o=r[i];if(!Object.hasOwn(l,i))return!1;let a=l[i];if(!t(o,a,i,r,l,s,f))return!1}return!0}default:return!1}}finally{s.delete(r),s.delete(l)}}(e,r,h,d)}(t,e,void 0,void 0,void 0,void 0,r)}},48973:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isLength=function(t){return Number.isSafeInteger(t)&&t>=0}},7252:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isPlainObject=function(t){if(!t||"object"!=typeof t)return!1;let e=Object.getPrototypeOf(t);return(null===e||e===Object.prototype||null===Object.getPrototypeOf(e))&&"[object Object]"===Object.prototype.toString.call(t)}},34526:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isPrimitive=function(t){return null==t||"object"!=typeof t&&"function"!=typeof t}},23497:function(t,e){"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isTypedArray=function(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}},77625:function(t){"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function i(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function o(t,e,n,o,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var u=new i(n,o||t,a),c=r?r+e:e;return t._events[c]?t._events[c].fn?t._events[c]=[t._events[c],u]:t._events[c].push(u):(t._events[c]=u,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function u(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),u.prototype.eventNames=function(){var t,n,i=[];if(0===this._eventsCount)return i;for(n in t=this._events)e.call(t,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(t)):i},u.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,a=Array(o);i<o;i++)a[i]=n[i].fn;return a},u.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},u.prototype.emit=function(t,e,n,i,o,a){var u=r?r+t:t;if(!this._events[u])return!1;var c,l,s=this._events[u],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,i),!0;case 5:return s.fn.call(s.context,e,n,i,o),!0;case 6:return s.fn.call(s.context,e,n,i,o,a),!0}for(l=1,c=Array(f-1);l<f;l++)c[l-1]=arguments[l];s.fn.apply(s.context,c)}else{var h,d=s.length;for(l=0;l<d;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,i);break;default:if(!c)for(h=1,c=Array(f-1);h<f;h++)c[h-1]=arguments[h];s[l].fn.apply(s[l].context,c)}}return!0},u.prototype.on=function(t,e,r){return o(this,t,e,r,!1)},u.prototype.once=function(t,e,r){return o(this,t,e,r,!0)},u.prototype.removeListener=function(t,e,n,i){var o=r?r+t:t;if(!this._events[o])return this;if(!e)return a(this,o),this;var u=this._events[o];if(u.fn)u.fn!==e||i&&!u.once||n&&u.context!==n||a(this,o);else{for(var c=0,l=[],s=u.length;c<s;c++)(u[c].fn!==e||i&&!u[c].once||n&&u[c].context!==n)&&l.push(u[c]);l.length?this._events[o]=1===l.length?l[0]:l:a(this,o)}return this},u.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,t.exports=u},68848:function(t,e){e.read=function(t,e,r,n,i){var o,a,u=8*i-n-1,c=(1<<u)-1,l=c>>1,s=-7,f=r?i-1:0,h=r?-1:1,d=t[e+f];for(f+=h,o=d&(1<<-s)-1,d>>=-s,s+=u;s>0;o=256*o+t[e+f],f+=h,s-=8);for(a=o&(1<<-s)-1,o>>=-s,s+=n;s>0;a=256*a+t[e+f],f+=h,s-=8);if(0===o)o=1-l;else{if(o===c)return a?NaN:1/0*(d?-1:1);a+=Math.pow(2,n),o-=l}return(d?-1:1)*a*Math.pow(2,o-n)},e.write=function(t,e,r,n,i,o){var a,u,c,l=8*o-i-1,s=(1<<l)-1,f=s>>1,h=23===i?5960464477539062e-23:0,d=n?0:o-1,p=n?1:-1,y=e<0||0===e&&1/e<0?1:0;for(isNaN(e=Math.abs(e))||e===1/0?(u=isNaN(e)?1:0,a=s):(a=Math.floor(Math.log(e)/Math.LN2),e*(c=Math.pow(2,-a))<1&&(a--,c*=2),a+f>=1?e+=h/c:e+=h*Math.pow(2,1-f),e*c>=2&&(a++,c/=2),a+f>=s?(u=0,a=s):a+f>=1?(u=(e*c-1)*Math.pow(2,i),a+=f):(u=e*Math.pow(2,f-1)*Math.pow(2,i),a=0));i>=8;t[r+d]=255&u,d+=p,u/=256,i-=8);for(a=a<<i|u,l+=i;l>0;t[r+d]=255&a,d+=p,a/=256,l-=8);t[r+d-p]|=128*y}},42488:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(39763).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},63639:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(39763).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},91723:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(39763).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},50091:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(39763).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},95252:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(39763).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},92735:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(39763).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},84428:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(39763).Z)("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]])},70525:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(39763).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},11239:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(39763).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},13126:function(t,e){"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,i=r?Symbol.for("react.portal"):60106,o=r?Symbol.for("react.fragment"):60107,a=r?Symbol.for("react.strict_mode"):60108,u=r?Symbol.for("react.profiler"):60114,c=r?Symbol.for("react.provider"):60109,l=r?Symbol.for("react.context"):60110,s=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,h=r?Symbol.for("react.forward_ref"):60112,d=r?Symbol.for("react.suspense"):60113,p=(r&&Symbol.for("react.suspense_list"),r?Symbol.for("react.memo"):60115),y=r?Symbol.for("react.lazy"):60116;r&&Symbol.for("react.block"),r&&Symbol.for("react.fundamental"),r&&Symbol.for("react.responder"),r&&Symbol.for("react.scope"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case s:case f:case o:case u:case a:case d:return t;default:switch(t=t&&t.$$typeof){case l:case h:case y:case p:case c:return t;default:return e}}case i:return e}}}(t)===o}},84851:function(t,e,r){"use strict";t.exports=r(13126)},46595:function(t,e,r){"use strict";r.d(e,{r:function(){return D}});var n=r(2265),i=r(38450),o=r.n(i),a=(t,e)=>[0,3*t,3*e-6*t,3*t-3*e+1],u=(t,e)=>t.map((t,r)=>t*e**r).reduce((t,e)=>t+e),c=(t,e)=>r=>u(a(t,e),r),l=(t,e)=>r=>u([...a(t,e).map((t,e)=>t*e).slice(1),0],r),s=function(){for(var t,e,r,n,i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];if(1===o.length)switch(o[0]){case"linear":[t,r,e,n]=[0,0,1,1];break;case"ease":[t,r,e,n]=[.25,.1,.25,1];break;case"ease-in":[t,r,e,n]=[.42,0,1,1];break;case"ease-out":[t,r,e,n]=[.42,0,.58,1];break;case"ease-in-out":[t,r,e,n]=[0,0,.58,1];break;default:var u=o[0].split("(");"cubic-bezier"===u[0]&&4===u[1].split(")")[0].split(",").length&&([t,r,e,n]=u[1].split(")")[0].split(",").map(t=>parseFloat(t)))}else 4===o.length&&([t,r,e,n]=o);var s=c(t,e),f=c(r,n),h=l(t,e),d=t=>t>1?1:t<0?0:t,p=t=>{for(var e=t>1?1:t,r=e,n=0;n<8;++n){var i=s(r)-e,o=h(r);if(1e-4>Math.abs(i-e)||o<1e-4)break;r=d(r-i/o)}return f(r)};return p.isStepper=!1,p},f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:e=100,damping:r=8,dt:n=17}=t,i=(t,i,o)=>{var a=o+(-(t-i)*e-o*r)*n/1e3,u=o*n/1e3+t;return 1e-4>Math.abs(u-i)&&1e-4>Math.abs(a)?[i,0]:[u,a]};return i.isStepper=!0,i.dt=n,i},h=t=>{if("string"==typeof t)switch(t){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return s(t);case"spring":return f();default:if("cubic-bezier"===t.split("(")[0])return s(t)}return"function"==typeof t?t:null};function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var y=t=>t.replace(/([A-Z])/g,t=>"-".concat(t.toLowerCase())),v=(t,e,r)=>t.map(t=>"".concat(y(t)," ").concat(e,"ms ").concat(r)).join(","),g=(t,e)=>[Object.keys(t),Object.keys(e)].reduce((t,e)=>t.filter(t=>e.includes(t))),m=(t,e)=>Object.keys(e).reduce((r,n)=>p(p({},r),{},{[n]:t(n,e[n])}),{});function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var x=(t,e,r)=>t+(e-t)*r,O=t=>{var{from:e,to:r}=t;return e!==r},P=(t,e,r)=>{var n=m((e,r)=>{if(O(r)){var[n,i]=t(r.from,r.to,r.velocity);return w(w({},r),{},{from:n,velocity:i})}return r},e);return r<1?m((t,e)=>O(e)?w(w({},e),{},{velocity:x(e.velocity,n[t].velocity,r),from:x(e.from,n[t].from,r)}):e,e):P(t,n,r-1)},j=(t,e,r,n,i,o)=>{var a,u,c,l,s,f,h,d,p,y,v=g(t,e);return!0===r.isStepper?(u=v.reduce((r,n)=>w(w({},r),{},{[n]:{from:t[n],velocity:0,to:e[n]}}),{}),c=()=>m((t,e)=>e.from,u),l=()=>!Object.values(u).filter(O).length,s=null,f=n=>{a||(a=n);var h=(n-a)/r.dt;u=P(r,u,h),i(w(w(w({},t),e),c())),a=n,l()||(s=o.setTimeout(f))},()=>(s=o.setTimeout(f),()=>{s()})):(d=null,p=v.reduce((r,n)=>w(w({},r),{},{[n]:[t[n],e[n]]}),{}),y=a=>{h||(h=a);var u=(a-h)/n,c=m((t,e)=>x(...e,r(u)),p);if(i(w(w(w({},t),e),c)),u<1)d=o.setTimeout(y);else{var l=m((t,e)=>x(...e,r(1)),p);i(w(w(w({},t),e),l))}},()=>(d=o.setTimeout(y),()=>{d()}))};class A{setTimeout(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=o=>{o-r>=e?t(o):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var E=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function S(){return(S=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function M(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function k(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?M(Object(r),!0).forEach(function(e){T(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function T(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}class C extends n.PureComponent{componentDidMount(){var{isActive:t,canBegin:e}=this.props;this.mounted=!0,t&&e&&this.runAnimation(this.props)}componentDidUpdate(t){var{isActive:e,canBegin:r,attributeName:n,shouldReAnimate:i,to:a,from:u}=this.props,{style:c}=this.state;if(r){if(!e){this.state&&c&&(n&&c[n]!==a||!n&&c!==a)&&this.setState({style:n?{[n]:a}:a});return}if(!o()(t.to,a)||!t.canBegin||!t.isActive){var l=!t.canBegin||!t.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=l||i?u:t.to;this.state&&c&&(n&&c[n]!==s||!n&&c!==s)&&this.setState({style:n?{[n]:s}:s}),this.runAnimation(k(k({},this.props),{},{from:s,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:t}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}handleStyleChange(t){this.changeStyle(t)}changeStyle(t){this.mounted&&this.setState({style:t})}runJSAnimation(t){var{from:e,to:r,duration:n,easing:i,begin:o,onAnimationEnd:a,onAnimationStart:u}=t,c=j(e,r,h(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([u,o,()=>{this.stopJSAnimation=c()},n,a])}runAnimation(t){var{begin:e,duration:r,attributeName:n,to:i,easing:o,onAnimationStart:a,onAnimationEnd:u,children:c}=t;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof o||"function"==typeof c||"spring"===o){this.runJSAnimation(t);return}var l=n?{[n]:i}:i,s=v(Object.keys(l),r,o);this.manager.start([a,e,k(k({},l),{},{transition:s}),r,u])}render(){var t=this.props,{children:e,begin:r,duration:i,attributeName:o,easing:a,isActive:u,from:c,to:l,canBegin:s,onAnimationEnd:f,shouldReAnimate:h,onAnimationReStart:d,animationManager:p}=t,y=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,E),v=n.Children.count(e),g=this.state.style;if("function"==typeof e)return e(g);if(!u||0===v||i<=0)return e;var m=t=>{var{style:e={},className:r}=t.props;return(0,n.cloneElement)(t,k(k({},y),{},{style:k(k({},e),g),className:r}))};return 1===v?m(n.Children.only(e)):n.createElement("div",null,n.Children.map(e,t=>m(t)))}constructor(t,e){super(t,e),T(this,"mounted",!1),T(this,"manager",null),T(this,"stopJSAnimation",null),T(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:o,children:a,duration:u,animationManager:c}=this.props;if(this.manager=c,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||u<=0){this.state={style:{}},"function"==typeof a&&(this.state={style:o});return}if(i){if("function"==typeof a){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}}T(C,"displayName","Animate"),T(C,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var _=(0,n.createContext)(null);function D(t){var e,r,i,o,a,u,c,l=(0,n.useContext)(_);return n.createElement(C,S({},t,{animationManager:null!==(u=null!==(c=t.animationManager)&&void 0!==c?c:l)&&void 0!==u?u:(e=new A,r=()=>null,i=!1,o=null,a=t=>{if(!i){if(Array.isArray(t)){if(!t.length)return;var[n,...u]=t;if("number"==typeof n){o=e.setTimeout(a.bind(null,u),n);return}a(n),o=e.setTimeout(a.bind(null,u));return}"object"==typeof t&&r(t),"function"==typeof t&&t()}},{stop:()=>{i=!0},start:t=>{i=!1,o&&(o(),o=null),a(t)},subscribe:t=>(r=t,()=>{r=()=>null}),getTimeoutController:()=>e})}))}},87774:function(t,e,r){"use strict";r.d(e,{$:function(){return tq},u:function(){return tZ}});var n=r(2265),i=r(61994),o=r(9841),a=r(82944),u=r(13790),c=r(58735),l=["children"],s=()=>{},f=(0,n.createContext)({addErrorBar:s,removeErrorBar:s}),h=(0,n.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function d(t){var{children:e}=t,r=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,l);return n.createElement(h.Provider,{value:r},e)}var p=()=>(0,n.useContext)(h),y=t=>{var{children:e,xAxisId:r,yAxisId:i,zAxisId:o,dataKey:a,data:l,stackId:s,hide:h,type:d,barSize:p}=t,[y,v]=n.useState([]),g=(0,n.useCallback)(t=>{v(e=>[...e,t])},[v]),m=(0,n.useCallback)(t=>{v(e=>e.filter(e=>e!==t))},[v]),b=(0,c.W)();return n.createElement(f.Provider,{value:{addErrorBar:g,removeErrorBar:m}},n.createElement(u.V,{type:d,data:l,xAxisId:r,yAxisId:i,zAxisId:o,dataKey:a,errorBars:y,stackId:s,hide:h,barSize:p,isPanorama:b}),e)};function v(t){var{addErrorBar:e,removeErrorBar:r}=(0,n.useContext)(f);return(0,n.useEffect)(()=>(e(t),()=>{r(t)}),[e,r,t]),null}var g=r(7986),m=r(40130),b=r(46595),w=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function x(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function O(){return(O=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function P(t){var{direction:e,width:r,dataKey:i,isAnimationActive:u,animationBegin:c,animationDuration:l,animationEasing:s}=t,f=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,w),h=(0,a.L6)(f,!1),{data:d,dataPointFormatter:y,xAxisId:v,yAxisId:m,errorBarOffset:x}=p(),P=(0,g.X_)(v),j=(0,g.nV)(m);if((null==P?void 0:P.scale)==null||(null==j?void 0:j.scale)==null||null==d||"x"===e&&"number"!==P.type)return null;var A=d.map(t=>{var a,f,{x:d,y:p,value:v,errorVal:g}=y(t,i,e);if(!g)return null;var m=[];if(Array.isArray(g)?[a,f]=g:a=f=g,"x"===e){var{scale:w}=P,A=p+x,E=A+r,S=A-r,M=w(v-a),k=w(v+f);m.push({x1:k,y1:E,x2:k,y2:S}),m.push({x1:M,y1:A,x2:k,y2:A}),m.push({x1:M,y1:E,x2:M,y2:S})}else if("y"===e){var{scale:T}=j,C=d+x,_=C-r,D=C+r,N=T(v-a),I=T(v+f);m.push({x1:_,y1:I,x2:D,y2:I}),m.push({x1:C,y1:N,x2:C,y2:I}),m.push({x1:_,y1:N,x2:D,y2:N})}var L="".concat(d+x,"px ").concat(p+x,"px");return n.createElement(o.m,O({className:"recharts-errorBar",key:"bar-".concat(m.map(t=>"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)))},h),m.map(t=>{var e=u?{transformOrigin:"".concat(t.x1-5,"px")}:void 0;return n.createElement(b.r,{from:{transform:"scaleY(0)",transformOrigin:L},to:{transform:"scaleY(1)",transformOrigin:L},begin:c,easing:s,isActive:u,duration:l,key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2),style:{transformOrigin:L}},n.createElement("line",O({},t,{style:e})))}))});return n.createElement(o.m,{className:"recharts-errorBars"},A)}var j=(0,n.createContext)(void 0);function A(t){var{direction:e,children:r}=t;return n.createElement(j.Provider,{value:e},r)}var E={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function S(t){var e,r,i=(e=t.direction,r=(0,n.useContext)(j),null!=e?e:null!=r?r:"x"),{width:o,isAnimationActive:a,animationBegin:u,animationDuration:c,animationEasing:l}=(0,m.j)(t,E);return n.createElement(n.Fragment,null,n.createElement(v,{dataKey:t.dataKey,direction:i}),n.createElement(P,O({},t,{direction:i,width:o,isAnimationActive:a,animationBegin:u,animationDuration:c,animationEasing:l})))}class M extends n.Component{render(){return n.createElement(S,this.props)}}x(M,"defaultProps",E),x(M,"displayName","ErrorBar");var k=r(20407),T=r(37618),C=r.n(T),_=r(26680),D=r(49037),N=r(16630),I=["valueAccessor"],L=["data","dataKey","clockWise","id","textBreakAll"];function R(){return(R=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function B(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function z(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?B(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):B(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function U(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var $=t=>Array.isArray(t.value)?C()(t.value):t.value;function K(t){var{valueAccessor:e=$}=t,r=U(t,I),{data:i,dataKey:u,clockWise:c,id:l,textBreakAll:s}=r,f=U(r,L);return i&&i.length?n.createElement(o.m,{className:"recharts-label-list"},i.map((t,r)=>{var i=(0,N.Rw)(u)?e(t,r):(0,D.F$)(t&&t.payload,u),o=(0,N.Rw)(l)?{}:{id:"".concat(l,"-").concat(r)};return n.createElement(_._,R({},(0,a.L6)(t,!0),f,o,{parentViewBox:t.parentViewBox,value:i,textBreakAll:s,viewBox:_._.parseViewBox((0,N.Rw)(c)?t:z(z({},t),{},{clockWise:c})),key:"label-".concat(r),index:r}))})):null}K.displayName="LabelList",K.renderCallByParent=function(t,e){var r,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&i&&!t.label)return null;var{children:o}=t,u=(0,a.NN)(o,K).map((t,r)=>(0,n.cloneElement)(t,{data:e,key:"labelList-".concat(r)}));return i?[(r=t.label)?!0===r?n.createElement(K,{key:"labelList-implicit",data:e}):n.isValidElement(r)||(0,_.d)(r)?n.createElement(K,{key:"labelList-implicit",data:e,content:r}):"object"==typeof r?n.createElement(K,R({data:e},r,{key:"labelList-implicit"})):null:null,...u]:u};var F=r(34067),W=r(41637),Y=r(80503),V=["x","y"];function Z(){return(Z=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function q(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function H(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?q(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):q(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function X(t,e){var{x:r,y:n}=t,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,V),o=parseInt("".concat(r),10),a=parseInt("".concat(n),10),u=parseInt("".concat(e.height||i.height),10),c=parseInt("".concat(e.width||i.width),10);return H(H(H(H(H({},e),i),o?{x:o}:{}),a?{y:a}:{}),{},{height:u,width:c,name:e.name,radius:e.radius})}function G(t){return n.createElement(Y.b,Z({shapeType:"rectangle",propTransformer:X,activeClassName:"recharts-active-bar"},t))}var Q=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if((0,N.hj)(t))return t;var i=(0,N.hj)(r)||(0,N.Rw)(r);return i?t(r,n):(i||function(t,e){if(!t)throw Error("Invariant failed")}(!1),e)}},J=r(44296),tt=r(35623),te=r(39040),tr=r(19579),tn=()=>{var t=(0,te.T)();return(0,n.useEffect)(()=>(t((0,tr.a1)()),()=>{t((0,tr.nF)())})),null},ti=r(98628);function to(t,e){var r,n,i=(0,te.C)(e=>(0,ti.i9)(e,t)),o=(0,te.C)(t=>(0,ti.t)(t,e)),a=null!==(r=null==i?void 0:i.allowDataOverflow)&&void 0!==r?r:ti.dW.allowDataOverflow,u=null!==(n=null==o?void 0:o.allowDataOverflow)&&void 0!==n?n:ti.RN.allowDataOverflow;return{needClip:a||u,needClipX:a,needClipY:u}}function ta(t){var{xAxisId:e,yAxisId:r,clipPathId:i}=t,o=(0,g.$$)(),{needClipX:a,needClipY:u,needClip:c}=to(e,r);if(!c)return null;var{x:l,y:s,width:f,height:h}=o;return n.createElement("clipPath",{id:"clipPath-".concat(i)},n.createElement("rect",{x:a?l:l-f/2,y:u?s:s-h/2,width:a?f:2*f,height:u?h:2*h}))}var tu=r(35953),tc=r(92713),tl=r(22932),ts=r(69729),tf=r(33968),th=r(66395);function td(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tp(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?td(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):td(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var ty=(t,e,r,n,i)=>i,tv=(t,e,r)=>{var n=null!=r?r:t;if(!(0,N.Rw)(n))return(0,N.h1)(n,e,0)},tg=(0,tc.P1)([tu.rE,ti.bm,(t,e)=>e,(t,e,r)=>r,(t,e,r,n)=>n],(t,e,r,n,i)=>e.filter(e=>"horizontal"===t?e.xAxisId===r:e.yAxisId===n).filter(t=>t.isPanorama===i).filter(t=>!1===t.hide).filter(t=>"bar"===t.type));function tm(t){return null!=t.stackId&&null!=t.dataKey}var tb=(0,tc.P1)([tg,tf.X8,(t,e,r)=>"horizontal"===(0,tu.rE)(t)?(0,ti.Lu)(t,"xAxis",e):(0,ti.Lu)(t,"yAxis",r)],(t,e,r)=>{var n=t.filter(tm),i=t.filter(t=>null==t.stackId);return[...Object.entries(n.reduce((t,e)=>(t[e.stackId]||(t[e.stackId]=[]),t[e.stackId].push(e),t),{})).map(t=>{var[n,i]=t;return{stackId:n,dataKeys:i.map(t=>t.dataKey),barSize:tv(e,r,i[0].barSize)}}),...i.map(t=>({stackId:void 0,dataKeys:[t.dataKey].filter(t=>null!=t),barSize:tv(e,r,t.barSize)}))]}),tw=(t,e,r,n)=>{var i,o;return"horizontal"===(0,tu.rE)(t)?(i=(0,ti.AS)(t,"xAxis",e,n),o=(0,ti.bY)(t,"xAxis",e,n)):(i=(0,ti.AS)(t,"yAxis",r,n),o=(0,ti.bY)(t,"yAxis",r,n)),(0,D.zT)(i,o)},tx=(0,tc.P1)([tb,tf.qy,tf.wK,tf.sd,(t,e,r,n,i)=>{var o,a,u,c,l=(0,tu.rE)(t),s=(0,tf.qy)(t),{maxBarSize:f}=i,h=(0,N.Rw)(f)?s:f;return"horizontal"===l?(u=(0,ti.AS)(t,"xAxis",e,n),c=(0,ti.bY)(t,"xAxis",e,n)):(u=(0,ti.AS)(t,"yAxis",r,n),c=(0,ti.bY)(t,"yAxis",r,n)),null!==(o=null!==(a=(0,D.zT)(u,c,!0))&&void 0!==a?a:h)&&void 0!==o?o:0},tw,(t,e,r,n,i)=>i.maxBarSize],(t,e,r,n,i,o,a)=>{var u=function(t,e,r,n,i){var o,a=n.length;if(!(a<1)){var u=(0,N.h1)(t,r,0,!0),c=[];if((0,th.n)(n[0].barSize)){var l=!1,s=r/a,f=n.reduce((t,e)=>t+(e.barSize||0),0);(f+=(a-1)*u)>=r&&(f-=(a-1)*u,u=0),f>=r&&s>0&&(l=!0,s*=.9,f=a*s);var h={offset:((r-f)/2>>0)-u,size:0};o=n.reduce((t,e)=>{var r,n=[...t,{stackId:e.stackId,dataKeys:e.dataKeys,position:{offset:h.offset+h.size+u,size:l?s:null!==(r=e.barSize)&&void 0!==r?r:0}}];return h=n[n.length-1].position,n},c)}else{var d=(0,N.h1)(e,r,0,!0);r-2*d-(a-1)*u<=0&&(u=0);var p=(r-2*d-(a-1)*u)/a;p>1&&(p>>=0);var y=(0,th.n)(i)?Math.min(p,i):p;o=n.reduce((t,e,r)=>[...t,{stackId:e.stackId,dataKeys:e.dataKeys,position:{offset:d+(p+u)*r+(p-y)/2,size:y}}],c)}return o}}(r,n,i!==o?i:o,t,(0,N.Rw)(a)?e:a);return i!==o&&null!=u&&(u=u.map(t=>tp(tp({},t),{},{position:tp(tp({},t.position),{},{offset:t.position.offset-i/2})}))),u}),tO=(0,tc.P1)([tx,ty],(t,e)=>{if(null!=t){var r=t.find(t=>t.stackId===e.stackId&&t.dataKeys.includes(e.dataKey));if(null!=r)return r.position}}),tP=(0,tc.P1)([ti.bm,ty],(t,e)=>{if(t.some(t=>"bar"===t.type&&e.dataKey===t.dataKey&&e.stackId===t.stackId&&e.stackId===t.stackId))return e}),tj=(0,tc.P1)([(t,e,r,n)=>"horizontal"===(0,tu.rE)(t)?(0,ti.g6)(t,"yAxis",r,n):(0,ti.g6)(t,"xAxis",e,n),ty],(t,e)=>{if(t&&(null==e?void 0:e.dataKey)!=null){var{stackId:r}=e;if(null!=r){var n=t[r];if(n){var{stackedData:i}=n;if(i)return i.find(t=>t.key===e.dataKey)}}}}),tA=(0,tc.P1)([ts.DX,(t,e,r,n)=>(0,ti.AS)(t,"xAxis",e,n),(t,e,r,n)=>(0,ti.AS)(t,"yAxis",r,n),(t,e,r,n)=>(0,ti.bY)(t,"xAxis",e,n),(t,e,r,n)=>(0,ti.bY)(t,"yAxis",r,n),tO,tu.rE,tl.hA,tw,tj,tP,(t,e,r,n,i,o)=>o],(t,e,r,n,i,o,a,u,c,l,s,f)=>{var h,{chartData:d,dataStartIndex:p,dataEndIndex:y}=u;if(null!=s&&null!=o&&("horizontal"===a||"vertical"===a)&&null!=e&&null!=r&&null!=n&&null!=i&&null!=c){var{data:v}=s;if(null!=(h=null!=v&&v.length>0?v:null==d?void 0:d.slice(p,y+1)))return tZ({layout:a,barSettings:s,pos:o,bandSize:c,xAxis:e,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:l,displayedData:h,offset:t,cells:f})}}),tE=r(31944),tS=r(62658),tM=r(59087),tk=["onMouseEnter","onMouseLeave","onClick"],tT=["value","background","tooltipPosition"],tC=["onMouseEnter","onClick","onMouseLeave"];function t_(){return(t_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function tD(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tN(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tD(Object(r),!0).forEach(function(e){tI(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tD(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tI(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tL(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var tR=t=>{var{dataKey:e,name:r,fill:n,legendType:i,hide:o}=t;return[{inactive:o,dataKey:e,type:i,color:n,value:(0,D.hn)(r,e),payload:t}]};function tB(t){var{dataKey:e,stroke:r,strokeWidth:n,fill:i,name:o,hide:a,unit:u}=t;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:e,nameKey:void 0,name:(0,D.hn)(o,e),hide:a,type:t.tooltipType,color:t.fill,unit:u}}}function tz(t){var e=(0,te.C)(tE.Ve),{data:r,dataKey:i,background:o,allOtherBarProps:u}=t,{onMouseEnter:c,onMouseLeave:l,onClick:s}=u,f=tL(u,tk),h=(0,J.Df)(c,i),d=(0,J.oQ)(l),p=(0,J.nC)(s,i);if(!o||null==r)return null;var y=(0,a.L6)(o,!1);return n.createElement(n.Fragment,null,r.map((t,r)=>{var{value:a,background:u,tooltipPosition:c}=t,l=tL(t,tT);if(!u)return null;var s=h(t,r),v=d(t,r),g=p(t,r),m=tN(tN(tN(tN(tN({option:o,isActive:String(r)===e},l),{},{fill:"#eee"},u),y),(0,W.bw)(f,t,r)),{},{onMouseEnter:s,onMouseLeave:v,onClick:g,dataKey:i,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(G,t_({key:"background-bar-".concat(r)},m))}))}function tU(t){var{data:e,props:r,showLabels:i}=t,u=(0,a.L6)(r,!1),{shape:c,dataKey:l,activeBar:s}=r,f=(0,te.C)(tE.Ve),h=(0,te.C)(tE.du),{onMouseEnter:d,onClick:p,onMouseLeave:y}=r,v=tL(r,tC),g=(0,J.Df)(d,l),m=(0,J.oQ)(y),b=(0,J.nC)(p,l);return e?n.createElement(n.Fragment,null,e.map((t,e)=>{var r=s&&String(e)===f&&(null==h||l===h),i=tN(tN(tN({},u),t),{},{isActive:r,option:r?s:c,index:e,dataKey:l});return n.createElement(o.m,t_({className:"recharts-bar-rectangle"},(0,W.bw)(v,t,e),{onMouseEnter:g(t,e),onMouseLeave:m(t,e),onClick:b(t,e),key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(e)}),n.createElement(G,i))}),i&&K.renderCallByParent(r,e)):null}function t$(t){var{props:e,previousRectanglesRef:r}=t,{data:i,layout:a,isAnimationActive:u,animationBegin:c,animationDuration:l,animationEasing:s,onAnimationEnd:f,onAnimationStart:h}=e,d=r.current,p=(0,tM.i)(e,"recharts-bar-"),[y,v]=(0,n.useState)(!1),g=(0,n.useCallback)(()=>{"function"==typeof f&&f(),v(!1)},[f]),m=(0,n.useCallback)(()=>{"function"==typeof h&&h(),v(!0)},[h]);return n.createElement(b.r,{begin:c,duration:l,isActive:u,easing:s,from:{t:0},to:{t:1},onAnimationEnd:g,onAnimationStart:m,key:p},t=>{var{t:u}=t,c=1===u?i:i.map((t,e)=>{var r=d&&d[e];if(r){var n=(0,N.k4)(r.x,t.x),i=(0,N.k4)(r.y,t.y),o=(0,N.k4)(r.width,t.width),c=(0,N.k4)(r.height,t.height);return tN(tN({},t),{},{x:n(u),y:i(u),width:o(u),height:c(u)})}if("horizontal"===a){var l=(0,N.k4)(0,t.height)(u);return tN(tN({},t),{},{y:t.y+t.height-l,height:l})}var s=(0,N.k4)(0,t.width)(u);return tN(tN({},t),{},{width:s})});return u>0&&(r.current=c),n.createElement(o.m,null,n.createElement(tU,{props:e,data:c,showLabels:!y}))})}function tK(t){var{data:e,isAnimationActive:r}=t,i=(0,n.useRef)(null);return r&&e&&e.length&&(null==i.current||i.current!==e)?n.createElement(t$,{previousRectanglesRef:i,props:t}):n.createElement(tU,{props:t,data:e,showLabels:!0})}var tF=(t,e)=>{var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:(0,D.F$)(t,e)}};class tW extends n.PureComponent{render(){var{hide:t,data:e,dataKey:r,className:a,xAxisId:u,yAxisId:c,needClip:l,background:s,id:f,layout:h}=this.props;if(t)return null;var d=(0,i.W)("recharts-bar",a),p=(0,N.Rw)(f)?this.id:f;return n.createElement(o.m,{className:d},l&&n.createElement("defs",null,n.createElement(ta,{clipPathId:p,xAxisId:u,yAxisId:c})),n.createElement(o.m,{className:"recharts-bar-rectangles",clipPath:l?"url(#clipPath-".concat(p,")"):null},n.createElement(tz,{data:e,dataKey:r,background:s,allOtherBarProps:this.props}),n.createElement(tK,this.props)),n.createElement(A,{direction:"horizontal"===h?"y":"x"},this.props.children))}constructor(){super(...arguments),tI(this,"id",(0,N.EL)("recharts-bar-"))}}var tY={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!F.x.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function tV(t){var e,{xAxisId:r,yAxisId:i,hide:o,legendType:u,minPointSize:l,activeBar:s,animationBegin:f,animationDuration:h,animationEasing:p,isAnimationActive:y}=(0,m.j)(t,tY),{needClip:v}=to(r,i),g=(0,tu.vn)(),b=(0,c.W)(),w=(0,n.useMemo)(()=>({barSize:t.barSize,data:void 0,dataKey:t.dataKey,maxBarSize:t.maxBarSize,minPointSize:l,stackId:(0,D.GA)(t.stackId)}),[t.barSize,t.dataKey,t.maxBarSize,l,t.stackId]),x=(0,a.NN)(t.children,k.b),O=(0,te.C)(t=>tA(t,r,i,b,w,x));if("vertical"!==g&&"horizontal"!==g)return null;var P=null==O?void 0:O[0];return e=null==P||null==P.height||null==P.width?0:"vertical"===g?P.height/2:P.width/2,n.createElement(d,{xAxisId:r,yAxisId:i,data:O,dataPointFormatter:tF,errorBarOffset:e},n.createElement(tW,t_({},t,{layout:g,needClip:v,data:O,xAxisId:r,yAxisId:i,hide:o,legendType:u,minPointSize:l,activeBar:s,animationBegin:f,animationDuration:h,animationEasing:p,isAnimationActive:y})))}function tZ(t){var{layout:e,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:o,xAxis:a,yAxis:u,xAxisTicks:c,yAxisTicks:l,stackedData:s,displayedData:f,offset:h,cells:d}=t,p="horizontal"===e?u:a,y=s?p.scale.domain():null,v=(0,D.Yj)({numericAxis:p});return f.map((t,f)=>{s?g=(0,D.Vv)(s[f],y):Array.isArray(g=(0,D.F$)(t,r))||(g=[v,g]);var p=Q(n,0)(g[1],f);if("horizontal"===e){var g,m,b,w,x,O,P,[j,A]=[u.scale(g[0]),u.scale(g[1])];m=(0,D.Fy)({axis:a,ticks:c,bandSize:o,offset:i.offset,entry:t,index:f}),b=null!==(P=null!=A?A:j)&&void 0!==P?P:void 0,w=i.size;var E=j-A;if(x=(0,N.In)(E)?0:E,O={x:m,y:h.top,width:w,height:h.height},Math.abs(p)>0&&Math.abs(x)<Math.abs(p)){var S=(0,N.uY)(x||p)*(Math.abs(p)-Math.abs(x));b-=S,x+=S}}else{var[M,k]=[a.scale(g[0]),a.scale(g[1])];if(m=M,b=(0,D.Fy)({axis:u,ticks:l,bandSize:o,offset:i.offset,entry:t,index:f}),w=k-M,x=i.size,O={x:h.left,y:b,width:h.width,height:x},Math.abs(p)>0&&Math.abs(w)<Math.abs(p)){var T=(0,N.uY)(w||p)*(Math.abs(p)-Math.abs(w));w+=T}}return tN(tN({},t),{},{x:m,y:b,width:w,height:x,value:s?g:g[1],payload:t,background:O,tooltipPosition:{x:m+w/2,y:b+x/2}},d&&d[f]&&d[f].props)})}class tq extends n.PureComponent{render(){return n.createElement(y,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},n.createElement(tn,null),n.createElement(tS.L,{legendPayload:tR(this.props)}),n.createElement(tt.k,{fn:tB,args:this.props}),n.createElement(tV,this.props))}}tI(tq,"displayName","Bar"),tI(tq,"defaultProps",tY)},70949:function(t,e,r){"use strict";r.d(e,{O:function(){return O}});var n=r(2265),i=r(15870),o=r.n(i),a=r(61994);function u(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}var c=r(9841),l=r(58811),s=r(26680),f=r(16630),h=r(41637),d=r(82944),p=r(12983),y=["viewBox"],v=["viewBox"];function g(){return(g=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function m(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function b(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?m(Object(r),!0).forEach(function(e){x(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function w(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function x(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}class O extends n.Component{shouldComponentUpdate(t,e){var{viewBox:r}=t,n=w(t,y),i=this.props,{viewBox:o}=i,a=w(i,v);return!u(r,o)||!u(n,a)||!u(e,this.state)}getTickLineCoord(t){var e,r,n,i,o,a,{x:u,y:c,width:l,height:s,orientation:h,tickSize:d,mirror:p,tickMargin:y}=this.props,v=p?-1:1,g=t.tickSize||d,m=(0,f.hj)(t.tickCoord)?t.tickCoord:t.coordinate;switch(h){case"top":e=r=t.coordinate,a=(n=(i=c+ +!p*s)-v*g)-v*y,o=m;break;case"left":n=i=t.coordinate,o=(e=(r=u+ +!p*l)-v*g)-v*y,a=m;break;case"right":n=i=t.coordinate,o=(e=(r=u+ +p*l)+v*g)+v*y,a=m;break;default:e=r=t.coordinate,a=(n=(i=c+ +p*s)+v*g)+v*y,o=m}return{line:{x1:e,y1:n,x2:r,y2:i},tick:{x:o,y:a}}}getTickTextAnchor(){var t,{orientation:e,mirror:r}=this.props;switch(e){case"left":t=r?"start":"end";break;case"right":t=r?"end":"start";break;default:t="middle"}return t}getTickVerticalAnchor(){var{orientation:t,mirror:e}=this.props;switch(t){case"left":case"right":return"middle";case"top":return e?"start":"end";default:return e?"end":"start"}}renderAxisLine(){var{x:t,y:e,width:r,height:i,orientation:u,mirror:c,axisLine:l}=this.props,s=b(b(b({},(0,d.L6)(this.props,!1)),(0,d.L6)(l,!1)),{},{fill:"none"});if("top"===u||"bottom"===u){var f=+("top"===u&&!c||"bottom"===u&&c);s=b(b({},s),{},{x1:t,y1:e+f*i,x2:t+r,y2:e+f*i})}else{var h=+("left"===u&&!c||"right"===u&&c);s=b(b({},s),{},{x1:t+h*r,y1:e,x2:t+h*r,y2:e+i})}return n.createElement("line",g({},s,{className:(0,a.W)("recharts-cartesian-axis-line",o()(l,"className"))}))}static renderTickItem(t,e,r){var i,o=(0,a.W)(e.className,"recharts-cartesian-axis-tick-value");if(n.isValidElement(t))i=n.cloneElement(t,b(b({},e),{},{className:o}));else if("function"==typeof t)i=t(b(b({},e),{},{className:o}));else{var u="recharts-cartesian-axis-tick-value";"boolean"!=typeof t&&(u=(0,a.W)(u,t.className)),i=n.createElement(l.x,g({},e,{className:u}),r)}return i}renderTicks(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:i,stroke:u,tick:l,tickFormatter:s,unit:f}=this.props,y=(0,p.f)(b(b({},this.props),{},{ticks:r}),t,e),v=this.getTickTextAnchor(),m=this.getTickVerticalAnchor(),w=(0,d.L6)(this.props,!1),x=(0,d.L6)(l,!1),P=b(b({},w),{},{fill:"none"},(0,d.L6)(i,!1)),j=y.map((t,e)=>{var{line:r,tick:d}=this.getTickLineCoord(t),p=b(b(b(b({textAnchor:v,verticalAnchor:m},w),{},{stroke:"none",fill:u},x),d),{},{index:e,payload:t,visibleTicksCount:y.length,tickFormatter:s});return n.createElement(c.m,g({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},(0,h.bw)(this.props,t,e)),i&&n.createElement("line",g({},P,r,{className:(0,a.W)("recharts-cartesian-axis-tick-line",o()(i,"className"))})),l&&O.renderTickItem(l,p,"".concat("function"==typeof s?s(t.value,e):t.value).concat(f||"")))});return j.length>0?n.createElement("g",{className:"recharts-cartesian-axis-ticks"},j):null}render(){var{axisLine:t,width:e,height:r,className:i,hide:o}=this.props;if(o)return null;var{ticks:u}=this.props;return null!=e&&e<=0||null!=r&&r<=0?null:n.createElement(c.m,{className:(0,a.W)("recharts-cartesian-axis",i),ref:t=>{if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(e);var r=e[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},t&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,u),s._.renderCallByParent(this.props))}constructor(t){super(t),this.tickRefs=n.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}}x(O,"displayName","CartesianAxis"),x(O,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},56940:function(t,e,r){"use strict";r.d(e,{q:function(){return _}});var n=r(2265),i=r(1175),o=r(16630),a=r(82944),u=r(49037),c=r(12983),l=r(70949),s=r(35953),f=r(98628),h=r(39040),d=r(58735),p=r(40130),y=["x1","y1","x2","y2","key"],v=["offset"],g=["xAxisId","yAxisId"],m=["xAxisId","yAxisId"];function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function x(){return(x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function O(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var P=t=>{var{fill:e}=t;if(!e||"none"===e)return null;var{fillOpacity:r,x:i,y:o,width:a,height:u,ry:c}=t;return n.createElement("rect",{x:i,y:o,ry:c,width:a,height:u,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function j(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if("function"==typeof t)r=t(e);else{var{x1:i,y1:o,x2:u,y2:c,key:l}=e,s=O(e,y),f=(0,a.L6)(s,!1),{offset:h}=f,d=O(f,v);r=n.createElement("line",x({},d,{x1:i,y1:o,x2:u,y2:c,fill:"none",key:l}))}return r}function A(t){var{x:e,width:r,horizontal:i=!0,horizontalPoints:o}=t;if(!i||!o||!o.length)return null;var{xAxisId:a,yAxisId:u}=t,c=O(t,g),l=o.map((t,n)=>j(i,w(w({},c),{},{x1:e,y1:t,x2:e+r,y2:t,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},l)}function E(t){var{y:e,height:r,vertical:i=!0,verticalPoints:o}=t;if(!i||!o||!o.length)return null;var{xAxisId:a,yAxisId:u}=t,c=O(t,m),l=o.map((t,n)=>j(i,w(w({},c),{},{x1:t,y1:e,x2:t,y2:e+r,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},l)}function S(t){var{horizontalFill:e,fillOpacity:r,x:i,y:o,width:a,height:u,horizontalPoints:c,horizontal:l=!0}=t;if(!l||!e||!e.length)return null;var s=c.map(t=>Math.round(t+o-o)).sort((t,e)=>t-e);o!==s[0]&&s.unshift(0);var f=s.map((t,c)=>{var l=s[c+1]?s[c+1]-t:o+u-t;if(l<=0)return null;var f=c%e.length;return n.createElement("rect",{key:"react-".concat(c),y:t,x:i,height:l,width:a,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function M(t){var{vertical:e=!0,verticalFill:r,fillOpacity:i,x:o,y:a,width:u,height:c,verticalPoints:l}=t;if(!e||!r||!r.length)return null;var s=l.map(t=>Math.round(t+o-o)).sort((t,e)=>t-e);o!==s[0]&&s.unshift(0);var f=s.map((t,e)=>{var l=s[e+1]?s[e+1]-t:o+u-t;if(l<=0)return null;var f=e%r.length;return n.createElement("rect",{key:"react-".concat(e),x:t,y:a,width:l,height:c,stroke:"none",fill:r[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var k=(t,e)=>{var{xAxis:r,width:n,height:i,offset:o}=t;return(0,u.Rf)((0,c.f)(w(w(w({},l.O.defaultProps),r),{},{ticks:(0,u.uY)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.left,o.left+o.width,e)},T=(t,e)=>{var{yAxis:r,width:n,height:i,offset:o}=t;return(0,u.Rf)((0,c.f)(w(w(w({},l.O.defaultProps),r),{},{ticks:(0,u.uY)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.top,o.top+o.height,e)},C={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function _(t){var e=(0,s.zn)(),r=(0,s.Mw)(),a=(0,s.rh)(),u=w(w({},(0,p.j)(t,C)),{},{x:(0,o.hj)(t.x)?t.x:a.left,y:(0,o.hj)(t.y)?t.y:a.top,width:(0,o.hj)(t.width)?t.width:a.width,height:(0,o.hj)(t.height)?t.height:a.height}),{xAxisId:c,yAxisId:l,x:y,y:v,width:g,height:m,syncWithTicks:b,horizontalValues:O,verticalValues:j}=u,_=(0,d.W)(),D=(0,h.C)(t=>(0,f.Lg)(t,"xAxis",c,_)),N=(0,h.C)(t=>(0,f.Lg)(t,"yAxis",l,_));if(!(0,o.hj)(g)||g<=0||!(0,o.hj)(m)||m<=0||!(0,o.hj)(y)||y!==+y||!(0,o.hj)(v)||v!==+v)return null;var I=u.verticalCoordinatesGenerator||k,L=u.horizontalCoordinatesGenerator||T,{horizontalPoints:R,verticalPoints:B}=u;if((!R||!R.length)&&"function"==typeof L){var z=O&&O.length,U=L({yAxis:N?w(w({},N),{},{ticks:z?O:N.ticks}):void 0,width:e,height:r,offset:a},!!z||b);(0,i.Z)(Array.isArray(U),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof U,"]")),Array.isArray(U)&&(R=U)}if((!B||!B.length)&&"function"==typeof I){var $=j&&j.length,K=I({xAxis:D?w(w({},D),{},{ticks:$?j:D.ticks}):void 0,width:e,height:r,offset:a},!!$||b);(0,i.Z)(Array.isArray(K),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof K,"]")),Array.isArray(K)&&(B=K)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(P,{fill:u.fill,fillOpacity:u.fillOpacity,x:u.x,y:u.y,width:u.width,height:u.height,ry:u.ry}),n.createElement(S,x({},u,{horizontalPoints:R})),n.createElement(M,x({},u,{verticalPoints:B})),n.createElement(A,x({},u,{offset:a,horizontalPoints:R,xAxis:D,yAxis:N})),n.createElement(E,x({},u,{offset:a,verticalPoints:B,xAxis:D,yAxis:N})))}_.displayName="CartesianGrid"},97059:function(t,e,r){"use strict";r.d(e,{K:function(){return b}});var n=r(2265),i=r(61994),o=r(70949),a=r(39040),u=r(17644),c=r(98628),l=r(69729),s=r(58735),f=["children"],h=["dangerouslySetInnerHTML","ticks"];function d(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function p(){return(p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function y(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function v(t){var e=(0,a.T)(),r=(0,n.useMemo)(()=>{var{children:e}=t;return y(t,f)},[t]),i=(0,a.C)(t=>(0,c.i9)(t,r.id)),o=r===i;return((0,n.useEffect)(()=>(e((0,u.m2)(r)),()=>{e((0,u.Jj)(r))}),[r,e]),o)?t.children:null}var g=t=>{var{xAxisId:e,className:r}=t,u=(0,a.C)(l.zM),f=(0,s.W)(),d="xAxis",v=(0,a.C)(t=>(0,c.Vm)(t,d,e,f)),g=(0,a.C)(t=>(0,c.ox)(t,d,e,f)),m=(0,a.C)(t=>(0,c.Oy)(t,e)),b=(0,a.C)(t=>(0,c.rs)(t,e));if(null==m||null==b)return null;var{dangerouslySetInnerHTML:w,ticks:x}=t,O=y(t,h);return n.createElement(o.O,p({},O,{scale:v,x:b.x,y:b.y,width:m.width,height:m.height,className:(0,i.W)("recharts-".concat(d," ").concat(d),r),viewBox:u,ticks:g}))},m=t=>{var e,r,i,o,a;return n.createElement(v,{interval:null!==(e=t.interval)&&void 0!==e?e:"preserveEnd",id:t.xAxisId,scale:t.scale,type:t.type,padding:t.padding,allowDataOverflow:t.allowDataOverflow,domain:t.domain,dataKey:t.dataKey,allowDuplicatedCategory:t.allowDuplicatedCategory,allowDecimals:t.allowDecimals,tickCount:t.tickCount,includeHidden:null!==(r=t.includeHidden)&&void 0!==r&&r,reversed:t.reversed,ticks:t.ticks,height:t.height,orientation:t.orientation,mirror:t.mirror,hide:t.hide,unit:t.unit,name:t.name,angle:null!==(i=t.angle)&&void 0!==i?i:0,minTickGap:null!==(o=t.minTickGap)&&void 0!==o?o:5,tick:null===(a=t.tick)||void 0===a||a,tickFormatter:t.tickFormatter},n.createElement(g,t))};class b extends n.Component{render(){return n.createElement(m,this.props)}}d(b,"displayName","XAxis"),d(b,"defaultProps",{allowDataOverflow:c.dW.allowDataOverflow,allowDecimals:c.dW.allowDecimals,allowDuplicatedCategory:c.dW.allowDuplicatedCategory,height:c.dW.height,hide:!1,mirror:c.dW.mirror,orientation:c.dW.orientation,padding:c.dW.padding,reversed:c.dW.reversed,scale:c.dW.scale,tickCount:c.dW.tickCount,type:c.dW.type,xAxisId:0})},2027:function(t,e,r){"use strict";r.d(e,{Q:function(){return w}});var n=r(2265),i=r(61994),o=r(70949),a=r(17644),u=r(39040),c=r(98628),l=r(69729),s=r(58735),f=t=>{var{ticks:e,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:o=0}=t,a=0;if(e){e.forEach(t=>{if(t){var e=t.getBoundingClientRect();e.width>a&&(a=e.width)}});var u=r?r.getBoundingClientRect().width:0;return Math.round(a+(i+o)+u+(r?n:0))}return 0},h=r(26680),d=["dangerouslySetInnerHTML","ticks"];function p(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(){return(y=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function v(t){var e=(0,u.T)();return(0,n.useEffect)(()=>(e((0,a.TC)(t)),()=>{e((0,a.cB)(t))}),[t,e]),null}var g=t=>{var e,{yAxisId:r,className:p,width:v,label:g}=t,m=(0,n.useRef)(null),b=(0,n.useRef)(null),w=(0,u.C)(l.zM),x=(0,s.W)(),O=(0,u.T)(),P="yAxis",j=(0,u.C)(t=>(0,c.Vm)(t,P,r,x)),A=(0,u.C)(t=>(0,c.ON)(t,r)),E=(0,u.C)(t=>(0,c.lU)(t,r)),S=(0,u.C)(t=>(0,c.ox)(t,P,r,x));if((0,n.useLayoutEffect)(()=>{if(!("auto"!==v||!A||(0,h.d)(g)||(0,n.isValidElement)(g))){var t,e=m.current,i=null==e||null===(t=e.tickRefs)||void 0===t?void 0:t.current,{tickSize:o,tickMargin:u}=e.props,c=f({ticks:i,label:b.current,labelGapWithTick:5,tickSize:o,tickMargin:u});Math.round(A.width)!==Math.round(c)&&O((0,a.kB)({id:r,width:c}))}},[m,null==m||null===(e=m.current)||void 0===e||null===(e=e.tickRefs)||void 0===e?void 0:e.current,null==A?void 0:A.width,A,O,g,r,v]),null==A||null==E)return null;var{dangerouslySetInnerHTML:M,ticks:k}=t,T=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,d);return n.createElement(o.O,y({},T,{ref:m,labelRef:b,scale:j,x:E.x,y:E.y,width:A.width,height:A.height,className:(0,i.W)("recharts-".concat(P," ").concat(P),p),viewBox:w,ticks:S}))},m=t=>{var e,r,i,o,a;return n.createElement(n.Fragment,null,n.createElement(v,{interval:null!==(e=t.interval)&&void 0!==e?e:"preserveEnd",id:t.yAxisId,scale:t.scale,type:t.type,domain:t.domain,allowDataOverflow:t.allowDataOverflow,dataKey:t.dataKey,allowDuplicatedCategory:t.allowDuplicatedCategory,allowDecimals:t.allowDecimals,tickCount:t.tickCount,padding:t.padding,includeHidden:null!==(r=t.includeHidden)&&void 0!==r&&r,reversed:t.reversed,ticks:t.ticks,width:t.width,orientation:t.orientation,mirror:t.mirror,hide:t.hide,unit:t.unit,name:t.name,angle:null!==(i=t.angle)&&void 0!==i?i:0,minTickGap:null!==(o=t.minTickGap)&&void 0!==o?o:5,tick:null===(a=t.tick)||void 0===a||a,tickFormatter:t.tickFormatter}),n.createElement(g,t))},b={allowDataOverflow:c.RN.allowDataOverflow,allowDecimals:c.RN.allowDecimals,allowDuplicatedCategory:c.RN.allowDuplicatedCategory,hide:!1,mirror:c.RN.mirror,orientation:c.RN.orientation,padding:c.RN.padding,reversed:c.RN.reversed,scale:c.RN.scale,tickCount:c.RN.tickCount,type:c.RN.type,width:c.RN.width,yAxisId:0};class w extends n.Component{render(){return n.createElement(m,this.props)}}p(w,"displayName","YAxis"),p(w,"defaultProps",b)},12983:function(t,e,r){"use strict";r.d(e,{f:function(){return p}});var n,i,o=r(16630),a=r(4094),u=r(34067);class c{static create(t){return new c(t)}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(t){var{bandAware:e,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==t){if(r)switch(r){case"start":default:return this.scale(t);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(e){var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o}return this.scale(t)}}isInRange(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}constructor(t){this.scale=t}}(i="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i="EPS","string"))?n:n+"")in c?Object.defineProperty(c,i,{value:1e-4,enumerable:!0,configurable:!0,writable:!0}):c[i]=1e-4;var l=function(t){var{width:e,height:r}=t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,o=Math.atan(r/e);return Math.abs(i>o&&i<Math.PI-o?r/Math.sin(i):e/Math.cos(i))};function s(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],i=0;i<t.length;i+=e){if(void 0!==r&&!0!==r(t[i]))return;n.push(t[i])}return n}function f(t,e,r,n,i){if(t*e<t*n||t*e>t*i)return!1;var o=r();return t*(e-t*o/2-n)>=0&&t*(e+t*o/2-i)<=0}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function p(t,e,r){var n,{tick:i,ticks:c,viewBox:h,minTickGap:p,orientation:y,interval:v,tickFormatter:g,unit:m,angle:b}=t;if(!c||!c.length||!i)return[];if((0,o.hj)(v)||u.x.isSsr)return null!==(n=s(c,((0,o.hj)(v)?v:0)+1))&&void 0!==n?n:[];var w="top"===y||"bottom"===y?"width":"height",x=m&&"width"===w?(0,a.x)(m,{fontSize:e,letterSpacing:r}):{width:0,height:0},O=(t,n)=>{var i,o="function"==typeof g?g(t.value,n):t.value;return"width"===w?l({width:(i=(0,a.x)(o,{fontSize:e,letterSpacing:r})).width+x.width,height:i.height+x.height},b):(0,a.x)(o,{fontSize:e,letterSpacing:r})[w]},P=c.length>=2?(0,o.uY)(c[1].coordinate-c[0].coordinate):1,j=function(t,e,r){var n="width"===r,{x:i,y:o,width:a,height:u}=t;return 1===e?{start:n?i:o,end:n?i+a:o+u}:{start:n?i+a:o+u,end:n?i:o}}(h,P,w);return"equidistantPreserveStart"===v?function(t,e,r,n,i){for(var o,a=(n||[]).slice(),{start:u,end:c}=e,l=0,h=1,d=u;h<=a.length;)if(o=function(){var e,o=null==n?void 0:n[l];if(void 0===o)return{v:s(n,h)};var a=l,p=()=>(void 0===e&&(e=r(o,a)),e),y=o.coordinate,v=0===l||f(t,y,p,d,c);v||(l=0,d=u,h+=1),v&&(d=y+t*(p()/2+i),l+=h)}())return o.v;return[]}(P,j,O,c,p):("preserveStart"===v||"preserveStartEnd"===v?function(t,e,r,n,i,o){var a=(n||[]).slice(),u=a.length,{start:c,end:l}=e;if(o){var s=n[u-1],h=r(s,u-1),p=t*(s.coordinate+t*h/2-l);a[u-1]=s=d(d({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),f(t,s.tickCoord,()=>h,c,l)&&(l=s.tickCoord-t*(h/2+i),a[u-1]=d(d({},s),{},{isShow:!0}))}for(var y=o?u-1:u,v=function(e){var n,o=a[e],u=()=>(void 0===n&&(n=r(o,e)),n);if(0===e){var s=t*(o.coordinate-t*u()/2-c);a[e]=o=d(d({},o),{},{tickCoord:s<0?o.coordinate-s*t:o.coordinate})}else a[e]=o=d(d({},o),{},{tickCoord:o.coordinate});f(t,o.tickCoord,u,c,l)&&(c=o.tickCoord+t*(u()/2+i),a[e]=d(d({},o),{},{isShow:!0}))},g=0;g<y;g++)v(g);return a}(P,j,O,c,p,"preserveStartEnd"===v):function(t,e,r,n,i){for(var o=(n||[]).slice(),a=o.length,{start:u}=e,{end:c}=e,l=function(e){var n,l=o[e],s=()=>(void 0===n&&(n=r(l,e)),n);if(e===a-1){var h=t*(l.coordinate+t*s()/2-c);o[e]=l=d(d({},l),{},{tickCoord:h>0?l.coordinate-h*t:l.coordinate})}else o[e]=l=d(d({},l),{},{tickCoord:l.coordinate});f(t,l.tickCoord,s,u,c)&&(c=l.tickCoord-t*(s()/2+i),o[e]=d(d({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return o}(P,j,O,c,p)).filter(t=>t.isShow)}},42736:function(t,e,r){"use strict";r.d(e,{v:function(){return g}});var n=r(2265),i=r(31057),o=r(12528),a=r(1196),u=r(87235),c=r(15317),l=r(39151),s=r(40130),f=r(66395),h=["width","height"];function d(){return(d=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var p={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},y=(0,n.forwardRef)(function(t,e){var r,i=(0,s.j)(t.categoricalChartProps,p),{width:y,height:v}=i,g=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(i,h);if(!(0,f.r)(y)||!(0,f.r)(v))return null;var{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:w,tooltipPayloadSearcher:x,categoricalChartProps:O}=t;return n.createElement(o.M,{preloadedState:{options:{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:w,tooltipPayloadSearcher:x,eventEmitter:void 0}},reduxStoreName:null!==(r=O.id)&&void 0!==r?r:m},n.createElement(a.gt,{chartData:O.data}),n.createElement(u.v,{width:y,height:v,layout:i.layout,margin:i.margin}),n.createElement(c.b,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(l.r,d({},g,{width:y,height:v,ref:e})))}),v=["axis","item"],g=(0,n.forwardRef)((t,e)=>n.createElement(y,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:v,tooltipPayloadSearcher:i.NL,categoricalChartProps:t,ref:e}))},39151:function(t,e,r){"use strict";r.d(e,{r:function(){return B}});var n=r(2265),i=r(82944),o=r(35953),a=r(14847),u=r(58735),c=r(61994),l=["children","width","height","viewBox","className","style","title","desc"];function s(){return(s=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var f=(0,n.forwardRef)((t,e)=>{var{children:r,width:o,height:a,viewBox:u,className:f,style:h,title:d,desc:p}=t,y=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,l),v=u||{width:o,height:a,x:0,y:0},g=(0,c.W)("recharts-surface",f);return n.createElement("svg",s({},(0,i.L6)(y,!0,"svg"),{className:g,width:o,height:a,style:h,viewBox:"".concat(v.x," ").concat(v.y," ").concat(v.width," ").concat(v.height),ref:e}),n.createElement("title",null,d),n.createElement("desc",null,p),r)}),h=r(39040),d=r(2431),p=r(66395),y=["children"];function v(){return(v=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var g={width:"100%",height:"100%"},m=(0,n.forwardRef)((t,e)=>{var r,i,u=(0,o.zn)(),c=(0,o.Mw)(),l=(0,a.F)();if(!(0,p.r)(u)||!(0,p.r)(c))return null;var{children:s,otherAttributes:h,title:d,desc:y}=t;return r="number"==typeof h.tabIndex?h.tabIndex:l?0:void 0,i="string"==typeof h.role?h.role:l?"application":void 0,n.createElement(f,v({},h,{title:d,desc:y,role:i,tabIndex:r,width:u,height:c,style:g,ref:e}),s)}),b=t=>{var{children:e}=t,r=(0,h.C)(d.V);if(!r)return null;var{width:i,height:o,y:a,x:u}=r;return n.createElement(f,{width:i,height:o,x:u,y:a},e)},w=(0,n.forwardRef)((t,e)=>{var{children:r}=t,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,y);return(0,u.W)()?n.createElement(b,null,r):n.createElement(m,v({ref:e},i),r)}),x=r(64725),O=r(48323),P=r(70858),j=r(7883),A=r(60152),E=r(65293),S=r(69366),M=r(83061),k=r(72321),T=(0,n.createContext)(null);function C(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var _=(0,n.forwardRef)((t,e)=>{var{children:r,className:i,height:o,onClick:a,onContextMenu:u,onDoubleClick:l,onMouseDown:s,onMouseEnter:f,onMouseLeave:d,onMouseMove:y,onMouseUp:v,onTouchEnd:g,onTouchMove:m,onTouchStart:b,style:w,width:_}=t,D=(0,h.T)(),[N,I]=(0,n.useState)(null),[L,R]=(0,n.useState)(null);(0,P.W9)();var B=function(){var t=(0,h.T)(),[e,r]=(0,n.useState)(null),i=(0,h.C)(A.K$);return(0,n.useEffect)(()=>{if(null!=e){var r=e.getBoundingClientRect().width/e.offsetWidth;(0,p.n)(r)&&r!==i&&t((0,E.ZP)(r))}},[e,t,i]),r}(),z=(0,n.useCallback)(t=>{B(t),"function"==typeof e&&e(t),I(t),R(t)},[B,e,I,R]),U=(0,n.useCallback)(t=>{D((0,O.AE)(t)),D((0,S.r)({handler:a,reactEvent:t}))},[D,a]),$=(0,n.useCallback)(t=>{D((0,O.TK)(t)),D((0,S.r)({handler:f,reactEvent:t}))},[D,f]),K=(0,n.useCallback)(t=>{D((0,x.ne)()),D((0,S.r)({handler:d,reactEvent:t}))},[D,d]),F=(0,n.useCallback)(t=>{D((0,O.TK)(t)),D((0,S.r)({handler:y,reactEvent:t}))},[D,y]),W=(0,n.useCallback)(()=>{D((0,j.eb)())},[D]),Y=(0,n.useCallback)(t=>{D((0,j.sj)(t.key))},[D]),V=(0,n.useCallback)(t=>{D((0,S.r)({handler:u,reactEvent:t}))},[D,u]),Z=(0,n.useCallback)(t=>{D((0,S.r)({handler:l,reactEvent:t}))},[D,l]),q=(0,n.useCallback)(t=>{D((0,S.r)({handler:s,reactEvent:t}))},[D,s]),H=(0,n.useCallback)(t=>{D((0,S.r)({handler:v,reactEvent:t}))},[D,v]),X=(0,n.useCallback)(t=>{D((0,S.r)({handler:b,reactEvent:t}))},[D,b]),G=(0,n.useCallback)(t=>{D((0,M.$)(t)),D((0,S.r)({handler:m,reactEvent:t}))},[D,m]),Q=(0,n.useCallback)(t=>{D((0,S.r)({handler:g,reactEvent:t}))},[D,g]);return n.createElement(k.E.Provider,{value:N},n.createElement(T.Provider,{value:L},n.createElement("div",{className:(0,c.W)("recharts-wrapper",i),style:function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?C(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({position:"relative",cursor:"default",width:_,height:o},w),onClick:U,onContextMenu:V,onDoubleClick:Z,onFocus:W,onKeyDown:Y,onMouseDown:q,onMouseEnter:$,onMouseLeave:K,onMouseMove:F,onMouseUp:H,onTouchEnd:Q,onTouchMove:G,onTouchStart:X,ref:z},r)))}),D=r(16630),N=r(7986),I=(0,n.createContext)(void 0),L=t=>{var{children:e}=t,[r]=(0,n.useState)("".concat((0,D.EL)("recharts"),"-clip")),i=(0,N.$$)();if(null==i)return null;var{x:o,y:a,width:u,height:c}=i;return n.createElement(I.Provider,{value:r},n.createElement("defs",null,n.createElement("clipPath",{id:r},n.createElement("rect",{x:o,y:a,height:c,width:u}))),e)},R=["children","className","width","height","style","compact","title","desc"],B=(0,n.forwardRef)((t,e)=>{var{children:r,className:o,width:a,height:u,style:c,compact:l,title:s,desc:f}=t,h=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,R),d=(0,i.L6)(h,!1);return l?n.createElement(w,{otherAttributes:d,title:s,desc:f},r):n.createElement(_,{className:o,style:c,width:a,height:u,onClick:t.onClick,onMouseLeave:t.onMouseLeave,onMouseEnter:t.onMouseEnter,onMouseMove:t.onMouseMove,onMouseDown:t.onMouseDown,onMouseUp:t.onMouseUp,onContextMenu:t.onContextMenu,onDoubleClick:t.onDoubleClick,onTouchStart:t.onTouchStart,onTouchMove:t.onTouchMove,onTouchEnd:t.onTouchEnd},n.createElement(w,{otherAttributes:d,title:s,desc:f,ref:e},n.createElement(L,null,r)))})},86810:function(t,e,r){"use strict";r.d(e,{u:function(){return x}});var n=r(2265),i=r(31057),o=r(12528),a=r(1196),u=r(87235),c=r(15317),l=r(39040),s=r(27410);function f(t){var e=(0,l.T)();return(0,n.useEffect)(()=>{e((0,s.a)(t))},[e,t]),null}var h=r(39151),d=r(40130),p=r(66395),y=["width","height","layout"];function v(){return(v=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var g={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},m=(0,n.forwardRef)(function(t,e){var r,i=(0,d.j)(t.categoricalChartProps,g),{width:l,height:s,layout:m}=i,b=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(i,y);if(!(0,p.r)(l)||!(0,p.r)(s))return null;var{chartName:w,defaultTooltipEventType:x,validateTooltipEventTypes:O,tooltipPayloadSearcher:P}=t;return n.createElement(o.M,{preloadedState:{options:{chartName:w,defaultTooltipEventType:x,validateTooltipEventTypes:O,tooltipPayloadSearcher:P,eventEmitter:void 0}},reduxStoreName:null!==(r=i.id)&&void 0!==r?r:w},n.createElement(a.gt,{chartData:i.data}),n.createElement(u.v,{width:l,height:s,layout:m,margin:i.margin}),n.createElement(c.b,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(f,{cx:i.cx,cy:i.cy,startAngle:i.startAngle,endAngle:i.endAngle,innerRadius:i.innerRadius,outerRadius:i.outerRadius}),n.createElement(h.r,v({width:l,height:s},b,{ref:e})))}),b=["item"],w={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},x=(0,n.forwardRef)((t,e)=>{var r=(0,d.j)(t,w);return n.createElement(m,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:b,tooltipPayloadSearcher:i.NL,categoricalChartProps:r,ref:e})})},20407:function(t,e,r){"use strict";r.d(e,{b:function(){return n}});var n=t=>null;n.displayName="Cell"},26680:function(t,e,r){"use strict";r.d(e,{_:function(){return P},d:function(){return g}});var n=r(2265),i=r(61994),o=r(58811),a=r(82944),u=r(16630),c=r(39206),l=r(35953),s=["offset"],f=["labelRef"];function h(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function y(){return(y=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var v=t=>{var{value:e,formatter:r}=t,n=(0,u.Rw)(t.children)?e:t.children;return"function"==typeof r?r(n):n},g=t=>null!=t&&"function"==typeof t,m=(t,e)=>(0,u.uY)(e-t)*Math.min(Math.abs(e-t),360),b=(t,e,r)=>{var o,a,{position:l,viewBox:s,offset:f,className:h}=t,{cx:d,cy:p,innerRadius:v,outerRadius:g,startAngle:b,endAngle:w,clockWise:x}=s,O=(v+g)/2,P=m(b,w),j=P>=0?1:-1;"insideStart"===l?(o=b+j*f,a=x):"insideEnd"===l?(o=w-j*f,a=!x):"end"===l&&(o=w+j*f,a=x),a=P<=0?a:!a;var A=(0,c.op)(d,p,O,o),E=(0,c.op)(d,p,O,o+(a?1:-1)*359),S="M".concat(A.x,",").concat(A.y,"\n    A").concat(O,",").concat(O,",0,1,").concat(a?0:1,",\n    ").concat(E.x,",").concat(E.y),M=(0,u.Rw)(t.id)?(0,u.EL)("recharts-radial-line-"):t.id;return n.createElement("text",y({},r,{dominantBaseline:"central",className:(0,i.W)("recharts-radial-bar-label",h)}),n.createElement("defs",null,n.createElement("path",{id:M,d:S})),n.createElement("textPath",{xlinkHref:"#".concat(M)},e))},w=t=>{var{viewBox:e,offset:r,position:n}=t,{cx:i,cy:o,innerRadius:a,outerRadius:u,startAngle:l,endAngle:s}=e,f=(l+s)/2;if("outside"===n){var{x:h,y:d}=(0,c.op)(i,o,u+r,f);return{x:h,y:d,textAnchor:h>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"end"};var{x:p,y}=(0,c.op)(i,o,(a+u)/2,f);return{x:p,y,textAnchor:"middle",verticalAnchor:"middle"}},x=(t,e)=>{var{parentViewBox:r,offset:n,position:i}=t,{x:o,y:a,width:c,height:l}=e,s=l>=0?1:-1,f=s*n,h=s>0?"end":"start",d=s>0?"start":"end",y=c>=0?1:-1,v=y*n,g=y>0?"end":"start",m=y>0?"start":"end";if("top"===i)return p(p({},{x:o+c/2,y:a-s*n,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===i)return p(p({},{x:o+c/2,y:a+l+f,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(r.y+r.height-(a+l),0),width:c}:{});if("left"===i){var b={x:o-v,y:a+l/2,textAnchor:g,verticalAnchor:"middle"};return p(p({},b),r?{width:Math.max(b.x-r.x,0),height:l}:{})}if("right"===i){var w={x:o+c+v,y:a+l/2,textAnchor:m,verticalAnchor:"middle"};return p(p({},w),r?{width:Math.max(r.x+r.width-w.x,0),height:l}:{})}var x=r?{width:c,height:l}:{};return"insideLeft"===i?p({x:o+v,y:a+l/2,textAnchor:m,verticalAnchor:"middle"},x):"insideRight"===i?p({x:o+c-v,y:a+l/2,textAnchor:g,verticalAnchor:"middle"},x):"insideTop"===i?p({x:o+c/2,y:a+f,textAnchor:"middle",verticalAnchor:d},x):"insideBottom"===i?p({x:o+c/2,y:a+l-f,textAnchor:"middle",verticalAnchor:h},x):"insideTopLeft"===i?p({x:o+v,y:a+f,textAnchor:m,verticalAnchor:d},x):"insideTopRight"===i?p({x:o+c-v,y:a+f,textAnchor:g,verticalAnchor:d},x):"insideBottomLeft"===i?p({x:o+v,y:a+l-f,textAnchor:m,verticalAnchor:h},x):"insideBottomRight"===i?p({x:o+c-v,y:a+l-f,textAnchor:g,verticalAnchor:h},x):i&&"object"==typeof i&&((0,u.hj)(i.x)||(0,u.hU)(i.x))&&((0,u.hj)(i.y)||(0,u.hU)(i.y))?p({x:o+(0,u.h1)(i.x,c),y:a+(0,u.h1)(i.y,l),textAnchor:"end",verticalAnchor:"end"},x):p({x:o+c/2,y:a+l/2,textAnchor:"middle",verticalAnchor:"middle"},x)},O=t=>"cx"in t&&(0,u.hj)(t.cx);function P(t){var e,{offset:r=5}=t,c=p({offset:r},h(t,s)),{viewBox:d,position:g,value:m,children:P,content:j,className:A="",textBreakAll:E,labelRef:S}=c,M=(0,l.d2)(),k=d||M;if(!k||(0,u.Rw)(m)&&(0,u.Rw)(P)&&!(0,n.isValidElement)(j)&&"function"!=typeof j)return null;if((0,n.isValidElement)(j)){var{labelRef:T}=c,C=h(c,f);return(0,n.cloneElement)(j,C)}if("function"==typeof j){if(e=(0,n.createElement)(j,c),(0,n.isValidElement)(e))return e}else e=v(c);var _=O(k),D=(0,a.L6)(c,!0);if(_&&("insideStart"===g||"insideEnd"===g||"end"===g))return b(c,e,D);var N=_?w(c):x(c,k);return n.createElement(o.x,y({ref:S,className:(0,i.W)("recharts-label",A)},D,N,{breakAll:E}),e)}P.displayName="Label";var j=t=>{var{cx:e,cy:r,angle:n,startAngle:i,endAngle:o,r:a,radius:c,innerRadius:l,outerRadius:s,x:f,y:h,top:d,left:p,width:y,height:v,clockWise:g,labelViewBox:m}=t;if(m)return m;if((0,u.hj)(y)&&(0,u.hj)(v)){if((0,u.hj)(f)&&(0,u.hj)(h))return{x:f,y:h,width:y,height:v};if((0,u.hj)(d)&&(0,u.hj)(p))return{x:d,y:p,width:y,height:v}}return(0,u.hj)(f)&&(0,u.hj)(h)?{x:f,y:h,width:0,height:0}:(0,u.hj)(e)&&(0,u.hj)(r)?{cx:e,cy:r,startAngle:i||n||0,endAngle:o||n||0,innerRadius:l||0,outerRadius:s||c||a||0,clockWise:g}:t.viewBox?t.viewBox:void 0},A=(t,e,r)=>{if(!t)return null;var i={viewBox:e,labelRef:r};return!0===t?n.createElement(P,y({key:"label-implicit"},i)):(0,u.P2)(t)?n.createElement(P,y({key:"label-implicit",value:t},i)):(0,n.isValidElement)(t)?t.type===P?(0,n.cloneElement)(t,p({key:"label-implicit"},i)):n.createElement(P,y({key:"label-implicit",content:t},i)):g(t)?n.createElement(P,y({key:"label-implicit",content:t},i)):t&&"object"==typeof t?n.createElement(P,y({},t,{key:"label-implicit"},i)):null};P.parseViewBox=j,P.renderCallByParent=function(t,e){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&r&&!t.label)return null;var{children:i,labelRef:o}=t,u=j(t),c=(0,a.NN)(i,P).map((t,r)=>(0,n.cloneElement)(t,{viewBox:e||u,key:"label-".concat(r)}));return r?[A(t.label,e||u,o),...c]:c}},47625:function(t,e,r){"use strict";r.d(e,{h:function(){return f}});var n=r(61994),i=r(2265),o=r(34926),a=r.n(o),u=r(16630),c=r(1175);function l(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var f=(0,i.forwardRef)((t,e)=>{var{aspect:r,initialDimension:o={width:-1,height:-1},width:l="100%",height:f="100%",minWidth:h=0,minHeight:d,maxHeight:p,children:y,debounce:v=0,id:g,className:m,onResize:b,style:w={}}=t,x=(0,i.useRef)(null),O=(0,i.useRef)();O.current=b,(0,i.useImperativeHandle)(e,()=>x.current);var[P,j]=(0,i.useState)({containerWidth:o.width,containerHeight:o.height}),A=(0,i.useCallback)((t,e)=>{j(r=>{var n=Math.round(t),i=Math.round(e);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,i.useEffect)(()=>{var t=t=>{var e,{width:r,height:n}=t[0].contentRect;A(r,n),null===(e=O.current)||void 0===e||e.call(O,r,n)};v>0&&(t=a()(t,v,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),{width:r,height:n}=x.current.getBoundingClientRect();return A(r,n),e.observe(x.current),()=>{e.disconnect()}},[A,v]);var E=(0,i.useMemo)(()=>{var{containerWidth:t,containerHeight:e}=P;if(t<0||e<0)return null;(0,c.Z)((0,u.hU)(l)||(0,u.hU)(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",l,f),(0,c.Z)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,u.hU)(l)?t:l,o=(0,u.hU)(f)?e:f;return r&&r>0&&(n?o=n/r:o&&(n=o*r),p&&o>p&&(o=p)),(0,c.Z)(n>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,o,l,f,h,d,r),i.Children.map(y,t=>(0,i.cloneElement)(t,{width:n,height:o,style:s({width:n,height:o},t.props.style)}))},[r,y,f,p,d,h,P,l]);return i.createElement("div",{id:g?"".concat(g):void 0,className:(0,n.W)("recharts-responsive-container",m),style:s(s({},w),{},{width:l,height:f,minWidth:h,minHeight:d,maxHeight:p}),ref:x},i.createElement("div",{style:{width:0,height:0,overflow:"visible"}},E))})},58811:function(t,e,r){"use strict";r.d(e,{x:function(){return k}});var n=r(2265),i=r(61994),o=r(16630),a=r(34067),u=r(82944),c=r(4094),l=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,s=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,f=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,h=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,d={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},p=Object.keys(d);class y{static parse(t){var e,[,r,n]=null!==(e=h.exec(t))&&void 0!==e?e:[];return new y(parseFloat(r),null!=n?n:"")}add(t){return this.unit!==t.unit?new y(NaN,""):new y(this.num+t.num,this.unit)}subtract(t){return this.unit!==t.unit?new y(NaN,""):new y(this.num-t.num,this.unit)}multiply(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new y(NaN,""):new y(this.num*t.num,this.unit||t.unit)}divide(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new y(NaN,""):new y(this.num/t.num,this.unit||t.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,o.In)(this.num)}constructor(t,e){this.num=t,this.unit=e,this.num=t,this.unit=e,(0,o.In)(t)&&(this.unit=""),""===e||f.test(e)||(this.num=NaN,this.unit=""),p.includes(e)&&(this.num=t*d[e],this.unit="px")}}function v(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,[,n,i,o]=null!==(r=l.exec(e))&&void 0!==r?r:[],a=y.parse(null!=n?n:""),u=y.parse(null!=o?o:""),c="*"===i?a.multiply(u):a.divide(u);if(c.isNaN())return"NaN";e=e.replace(l,c.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var f,[,h,d,p]=null!==(f=s.exec(e))&&void 0!==f?f:[],v=y.parse(null!=h?h:""),g=y.parse(null!=p?p:""),m="+"===d?v.add(g):v.subtract(g);if(m.isNaN())return"NaN";e=e.replace(s,m.toString())}return e}var g=/\(([^()]*)\)/;function m(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e,r=t;null!=(e=g.exec(r));){var[,n]=e;r=r.replace(g,v(n))}return r}(e),e=v(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var b=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],w=["dx","dy","angle","className","breakAll"];function x(){return(x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function O(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var P=/[ \f\n\r\t\v\u2028\u2029]+/,j=t=>{var{children:e,breakAll:r,style:n}=t;try{var i=[];(0,o.Rw)(e)||(i=r?e.toString().split(""):e.toString().split(P));var a=i.map(t=>({word:t,width:(0,c.x)(t,n).width})),u=r?0:(0,c.x)("\xa0",n).width;return{wordsWithComputedWidth:a,spaceWidth:u}}catch(t){return null}},A=(t,e,r,n,i)=>{var a,{maxLines:u,children:c,style:l,breakAll:s}=t,f=(0,o.hj)(u),h=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce((t,e)=>{var{word:o,width:a}=e,u=t[t.length-1];return u&&(null==n||i||u.width+a+r<Number(n))?(u.words.push(o),u.width+=a+r):t.push({words:[o],width:a}),t},[])},d=h(e),p=t=>t.reduce((t,e)=>t.width>e.width?t:e);if(!f||i||!(d.length>u||p(d).width>Number(n)))return d;for(var y=t=>{var e=h(j({breakAll:s,style:l,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>u||p(e).width>Number(n),e]},v=0,g=c.length-1,m=0;v<=g&&m<=c.length-1;){var b=Math.floor((v+g)/2),[w,x]=y(b-1),[O]=y(b);if(w||O||(v=b+1),w&&O&&(g=b-1),!w&&O){a=x;break}m++}return a||d},E=t=>[{words:(0,o.Rw)(t)?[]:t.toString().split(P)}],S=t=>{var{width:e,scaleToFit:r,children:n,style:i,breakAll:o,maxLines:u}=t;if((e||r)&&!a.x.isSsr){var c=j({breakAll:o,children:n,style:i});if(!c)return E(n);var{wordsWithComputedWidth:l,spaceWidth:s}=c;return A({breakAll:o,children:n,maxLines:u,style:i},l,s,e,r)}return E(n)},M="#808080",k=(0,n.forwardRef)((t,e)=>{var r,{x:a=0,y:c=0,lineHeight:l="1em",capHeight:s="0.71em",scaleToFit:f=!1,textAnchor:h="start",verticalAnchor:d="end",fill:p=M}=t,y=O(t,b),v=(0,n.useMemo)(()=>S({breakAll:y.breakAll,children:y.children,maxLines:y.maxLines,scaleToFit:f,style:y.style,width:y.width}),[y.breakAll,y.children,y.maxLines,f,y.style,y.width]),{dx:g,dy:P,angle:j,className:A,breakAll:E}=y,k=O(y,w);if(!(0,o.P2)(a)||!(0,o.P2)(c))return null;var T=a+((0,o.hj)(g)?g:0),C=c+((0,o.hj)(P)?P:0);switch(d){case"start":r=m("calc(".concat(s,")"));break;case"middle":r=m("calc(".concat((v.length-1)/2," * -").concat(l," + (").concat(s," / 2))"));break;default:r=m("calc(".concat(v.length-1," * -").concat(l,")"))}var _=[];if(f){var D=v[0].width,{width:N}=y;_.push("scale(".concat((0,o.hj)(N)?N/D:1,")"))}return j&&_.push("rotate(".concat(j,", ").concat(T,", ").concat(C,")")),_.length&&(k.transform=_.join(" ")),n.createElement("text",x({},(0,u.L6)(k,!0),{ref:e,x:T,y:C,className:(0,i.W)("recharts-text",A),textAnchor:h,fill:p.includes("url")?M:p}),v.map((t,e)=>{var i=t.words.join(E?"":" ");return n.createElement("tspan",{x:T,dy:0===e?r:l,key:"".concat(i,"-").concat(e)},i)}))});k.displayName="Text"},77719:function(t,e,r){"use strict";r.d(e,{u:function(){return ta}});var n=r(2265),i=r(54887),o=r(31104),a=r.n(o),u=r(61994),c=r(16630);function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t){return Array.isArray(t)&&(0,c.P2)(t[0])&&(0,c.P2)(t[1])?t.join(" ~ "):t}var d=t=>{var{separator:e=" : ",contentStyle:r={},itemStyle:i={},labelStyle:o={},payload:s,formatter:d,itemSorter:p,wrapperClassName:y,labelClassName:v,label:g,labelFormatter:m,accessibilityLayer:b=!1}=t,w=f({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),x=f({margin:0},o),O=!(0,c.Rw)(g),P=O?g:"",j=(0,u.W)("recharts-default-tooltip",y),A=(0,u.W)("recharts-tooltip-label",v);return O&&m&&null!=s&&(P=m(g,s)),n.createElement("div",l({className:j,style:w},b?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:A,style:x},n.isValidElement(P)?P:"".concat(P)),(()=>{if(s&&s.length){var t=(p?a()(s,p):s).map((t,r)=>{if("none"===t.type)return null;var o=t.formatter||d||h,{value:a,name:u}=t,l=a,p=u;if(o){var y=o(a,u,t,r,s);if(Array.isArray(y))[l,p]=y;else{if(null==y)return null;l=y}}var v=f({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},i);return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:v},(0,c.P2)(p)?n.createElement("span",{className:"recharts-tooltip-item-name"},p):null,(0,c.P2)(p)?n.createElement("span",{className:"recharts-tooltip-item-separator"},e):null,n.createElement("span",{className:"recharts-tooltip-item-value"},l),n.createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null})())},p="recharts-tooltip-wrapper",y={visibility:"hidden"};function v(t){var{allowEscapeViewBox:e,coordinate:r,key:n,offsetTopLeft:i,position:o,reverseDirection:a,tooltipDimension:u,viewBox:l,viewBoxDimension:s}=t;if(o&&(0,c.hj)(o[n]))return o[n];var f=r[n]-u-(i>0?i:0),h=r[n]+i;if(e[n])return a[n]?f:h;var d=l[n];return null==d?0:a[n]?f<d?Math.max(h,d):Math.max(f,d):null==s?0:h+u>d+s?Math.max(f,d):Math.max(h,d)}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function m(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach(function(e){b(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function b(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}class w extends n.PureComponent{componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var t,e;this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:t,allowEscapeViewBox:e,animationDuration:r,animationEasing:i,children:o,coordinate:a,hasPayload:l,isAnimationActive:s,offset:f,position:h,reverseDirection:d,useTranslate3d:g,viewBox:b,wrapperStyle:w,lastBoundingBox:x,innerRef:O,hasPortalFromProps:P}=this.props,{cssClasses:j,cssProperties:A}=function(t){var e,r,n,{allowEscapeViewBox:i,coordinate:o,offsetTopLeft:a,position:l,reverseDirection:s,tooltipBox:f,useTranslate3d:h,viewBox:d}=t;return{cssProperties:f.height>0&&f.width>0&&o?function(t){var{translateX:e,translateY:r,useTranslate3d:n}=t;return{transform:n?"translate3d(".concat(e,"px, ").concat(r,"px, 0)"):"translate(".concat(e,"px, ").concat(r,"px)")}}({translateX:r=v({allowEscapeViewBox:i,coordinate:o,key:"x",offsetTopLeft:a,position:l,reverseDirection:s,tooltipDimension:f.width,viewBox:d,viewBoxDimension:d.width}),translateY:n=v({allowEscapeViewBox:i,coordinate:o,key:"y",offsetTopLeft:a,position:l,reverseDirection:s,tooltipDimension:f.height,viewBox:d,viewBoxDimension:d.height}),useTranslate3d:h}):y,cssClasses:function(t){var{coordinate:e,translateX:r,translateY:n}=t;return(0,u.W)(p,{["".concat(p,"-right")]:(0,c.hj)(r)&&e&&(0,c.hj)(e.x)&&r>=e.x,["".concat(p,"-left")]:(0,c.hj)(r)&&e&&(0,c.hj)(e.x)&&r<e.x,["".concat(p,"-bottom")]:(0,c.hj)(n)&&e&&(0,c.hj)(e.y)&&n>=e.y,["".concat(p,"-top")]:(0,c.hj)(n)&&e&&(0,c.hj)(e.y)&&n<e.y})}({translateX:r,translateY:n,coordinate:o})}}({allowEscapeViewBox:e,coordinate:a,offsetTopLeft:f,position:h,reverseDirection:d,tooltipBox:{height:x.height,width:x.width},useTranslate3d:g,viewBox:b}),E=P?{}:m(m({transition:s&&t?"transform ".concat(r,"ms ").concat(i):void 0},A),{},{pointerEvents:"none",visibility:!this.state.dismissed&&t&&l?"visible":"hidden",position:"absolute",top:0,left:0}),S=m(m({},E),{},{visibility:!this.state.dismissed&&t&&l?"visible":"hidden"},w);return n.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:j,style:S,ref:O},o)}constructor(){super(...arguments),b(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),b(this,"handleKeyDown",t=>{if("Escape"===t.key){var e,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(e=null===(r=this.props.coordinate)||void 0===r?void 0:r.x)&&void 0!==e?e:0,y:null!==(n=null===(i=this.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==n?n:0}})}})}}var x=r(34067),O=r(36841),P=r.n(O),j=r(35953),A=r(14847),E=r(57165),S=r(82944),M=["x","y","top","left","width","height","className"];function k(){return(k=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function T(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var C=(t,e,r,n,i,o)=>"M".concat(t,",").concat(i,"v").concat(n,"M").concat(o,",").concat(e,"h").concat(r),_=t=>{var{x:e=0,y:r=0,top:i=0,left:o=0,width:a=0,height:l=0,className:s}=t,f=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?T(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:e,y:r,top:i,left:o,width:a,height:l},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,M));return(0,c.hj)(e)&&(0,c.hj)(r)&&(0,c.hj)(a)&&(0,c.hj)(l)&&(0,c.hj)(i)&&(0,c.hj)(o)?n.createElement("path",k({},(0,S.L6)(f,!0),{className:(0,u.W)("recharts-cross",s),d:C(e,r,a,l,i,o)})):null},D=r(73649),N=r(39206);function I(t){var{cx:e,cy:r,radius:n,startAngle:i,endAngle:o}=t;return{points:[(0,N.op)(e,r,n,i),(0,N.op)(e,r,n,o)],cx:e,cy:r,radius:n,startAngle:i,endAngle:o}}var L=r(60474),R=r(39040),B=r(49037),z=r(31944);function U(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function $(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?U(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var K=()=>(0,R.C)(z.zv),F=()=>{var t=K(),e=(0,R.C)(z.WQ),r=(0,R.C)(z.ri);return(0,B.zT)($($({},t),{},{scale:r}),e)},W=r(84461);function Y(){return(Y=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function V(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Z(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?V(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):V(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function q(t){var e,r,i,{coordinate:o,payload:a,index:c,offset:l,tooltipAxisBandSize:s,layout:f,cursor:h,tooltipEventType:d,chartName:p}=t;if(!h||!o||"ScatterChart"!==p&&"axis"!==d)return null;if("ScatterChart"===p)r=o,i=_;else if("BarChart"===p)e=s/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===f?o.x-e:l.left+.5,y:"horizontal"===f?l.top+.5:o.y-e,width:"horizontal"===f?s:l.width-1,height:"horizontal"===f?l.height-1:s},i=D.A;else if("radial"===f){var{cx:y,cy:v,radius:g,startAngle:m,endAngle:b}=I(o);r={cx:y,cy:v,startAngle:m,endAngle:b,innerRadius:g,outerRadius:g},i=L.L}else r={points:function(t,e,r){var n,i,o,a;if("horizontal"===t)o=n=e.x,i=r.top,a=r.top+r.height;else if("vertical"===t)a=i=e.y,n=r.left,o=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return I(e);var{cx:u,cy:c,innerRadius:l,outerRadius:s,angle:f}=e,h=(0,N.op)(u,c,l,f),d=(0,N.op)(u,c,s,f);n=h.x,i=h.y,o=d.x,a=d.y}return[{x:n,y:i},{x:o,y:a}]}(f,o,l)},i=E.H;var w="object"==typeof h&&"className"in h?h.className:void 0,x=Z(Z(Z(Z({stroke:"#ccc",pointerEvents:"none"},l),r),(0,S.L6)(h,!1)),{},{payload:a,payloadIndex:c,className:(0,u.W)("recharts-tooltip-cursor",w)});return(0,n.isValidElement)(h)?(0,n.cloneElement)(h,x):(0,n.createElement)(i,x)}function H(t){var e=F(),r=(0,j.rh)(),i=(0,j.vn)(),o=(0,W.AC)();return n.createElement(q,Y({},t,{coordinate:t.coordinate,index:t.index,payload:t.payload,offset:r,layout:i,tooltipAxisBandSize:e,chartName:o}))}var X=r(72321),G=r(64725),Q=r(70858),J=r(46302),tt=r(40130);function te(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?te(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):te(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tn(t){return t.dataKey}var ti=[],to={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!x.x.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function ta(t){var e,r,o=(0,tt.j)(t,to),{active:a,allowEscapeViewBox:u,animationDuration:c,animationEasing:l,content:s,filterNull:f,isAnimationActive:h,offset:p,payloadUniqBy:y,position:v,reverseDirection:g,useTranslate3d:m,wrapperStyle:b,cursor:x,shared:O,trigger:E,defaultIndex:S,portal:M,axisId:k}=o,T=(0,R.T)(),C="number"==typeof S?String(S):S;(0,n.useEffect)(()=>{T((0,G.PD)({shared:O,trigger:E,axisId:k,active:a,defaultIndex:C}))},[T,O,E,k,a,C]);var _=(0,j.d2)(),D=(0,A.F)(),N=(0,J.Y4)(O),{activeIndex:I,isActive:L}=(0,R.C)(t=>(0,W.oo)(t,N,E,C)),B=(0,R.C)(t=>(0,W.TT)(t,N,E,C)),z=(0,R.C)(t=>(0,W.i)(t,N,E,C)),U=(0,R.C)(t=>(0,W.ck)(t,N,E,C)),$=(0,X.C)(),K=null!=a?a:L,[F,Y]=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[e,r]=(0,n.useState)({height:0,left:0,top:0,width:0}),i=(0,n.useCallback)(t=>{if(null!=t){var n=t.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-e.height)>1||Math.abs(i.left-e.left)>1||Math.abs(i.top-e.top)>1||Math.abs(i.width-e.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[e.width,e.height,e.top,e.left,...t]);return[e,i]}([B,K]),V="axis"===N?z:void 0;(0,Q.Fg)(N,E,U,V,I,K);var Z=null!=M?M:$;if(null==Z)return null;var q=null!=B?B:ti;K||(q=ti),f&&q.length&&(e=B.filter(t=>null!=t.value&&(!0!==t.hide||o.includeHidden)),q=!0===y?P()(e,tn):"function"==typeof y?P()(e,y):e);var te=q.length>0,ta=n.createElement(w,{allowEscapeViewBox:u,animationDuration:c,animationEasing:l,isAnimationActive:h,active:K,coordinate:U,hasPayload:te,offset:p,position:v,reverseDirection:g,useTranslate3d:m,viewBox:_,wrapperStyle:b,lastBoundingBox:F,innerRef:Y,hasPortalFromProps:!!M},(r=tr(tr({},o),{},{payload:q,label:V,active:K,coordinate:U,accessibilityLayer:D}),n.isValidElement(s)?n.cloneElement(s,r):"function"==typeof s?n.createElement(s,r):n.createElement(d,r)));return n.createElement(n.Fragment,null,(0,i.createPortal)(ta,Z),K&&n.createElement(H,{cursor:x,tooltipEventType:N,coordinate:U,payload:B,index:I}))}},9841:function(t,e,r){"use strict";r.d(e,{m:function(){return c}});var n=r(2265),i=r(61994),o=r(82944),a=["children","className"];function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var c=n.forwardRef((t,e)=>{var{children:r,className:c}=t,l=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,a),s=(0,i.W)("recharts-layer",c);return n.createElement("g",u({className:s},(0,o.L6)(l,!0),{ref:e}),r)})},58735:function(t,e,r){"use strict";r.d(e,{W:function(){return o}});var n=r(2265),i=(0,n.createContext)(null),o=()=>null!=(0,n.useContext)(i)},14847:function(t,e,r){"use strict";r.d(e,{F:function(){return i}});var n=r(39040),i=()=>(0,n.C)(t=>t.rootProps.accessibilityLayer)},1196:function(t,e,r){"use strict";r.d(e,{gt:function(){return u}});var n=r(2265),i=r(39173),o=r(39040),a=r(58735),u=t=>{var{chartData:e}=t,r=(0,o.T)(),u=(0,a.W)();return(0,n.useEffect)(()=>u?()=>{}:(r((0,i.zR)(e)),()=>{r((0,i.zR)(void 0))}),[e,r,u]),null}},35953:function(t,e,r){"use strict";r.d(e,{Mw:function(){return h},d2:function(){return c},rE:function(){return d},rh:function(){return s},vn:function(){return p},zn:function(){return f}}),r(2265);var n=r(39040),i=r(69729),o=r(60152),a=r(58735),u=r(2431),c=()=>{var t,e=(0,a.W)(),r=(0,n.C)(i.nd),o=(0,n.C)(u.V),c=null===(t=(0,n.C)(u.F))||void 0===t?void 0:t.padding;return e&&o&&c?{width:o.width-c.left-c.right,height:o.height-c.top-c.bottom,x:c.left,y:c.top}:r},l={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},s=()=>{var t;return null!==(t=(0,n.C)(i.DX))&&void 0!==t?t:l},f=()=>(0,n.C)(o.RD),h=()=>(0,n.C)(o.d_),d=t=>t.layout.layoutType,p=()=>(0,n.C)(d)},44296:function(t,e,r){"use strict";r.d(e,{Df:function(){return o},nC:function(){return u},oQ:function(){return a}});var n=r(39040),i=r(64725),o=(t,e)=>{var r=(0,n.T)();return(n,o)=>a=>{null==t||t(n,o,a),r((0,i.M1)({activeIndex:String(o),activeDataKey:e,activeCoordinate:n.tooltipPosition}))}},a=t=>{var e=(0,n.T)();return(r,n)=>o=>{null==t||t(r,n,o),e((0,i.Vg)())}},u=(t,e)=>{var r=(0,n.T)();return(n,o)=>a=>{null==t||t(n,o,a),r((0,i.O_)({activeIndex:String(o),activeDataKey:e,activeCoordinate:n.tooltipPosition}))}}},72321:function(t,e,r){"use strict";r.d(e,{C:function(){return o},E:function(){return i}});var n=r(2265),i=(0,n.createContext)(null),o=()=>(0,n.useContext)(i)},7986:function(t,e,r){"use strict";r.d(e,{$$:function(){return d},X_:function(){return f},nV:function(){return h}});var n=r(98628),i=r(39040),o=r(58735),a=r(92713),u=r(69729),c=(0,a.P1)([u.DX],t=>{if(t)return{top:t.top,bottom:t.bottom,left:t.left,right:t.right}}),l=r(60152),s=(0,a.P1)([c,l.RD,l.d_],(t,e,r)=>{if(t&&null!=e&&null!=r)return{x:t.left,y:t.top,width:Math.max(0,e-t.left-t.right),height:Math.max(0,r-t.top-t.bottom)}}),f=t=>{var e=(0,o.W)();return(0,i.C)(r=>(0,n.AS)(r,"xAxis",t,e))},h=t=>{var e=(0,o.W)();return(0,i.C)(r=>(0,n.AS)(r,"yAxis",t,e))},d=()=>(0,i.C)(s)},10062:function(t,e,r){"use strict";r.d(e,{b:function(){return tP},w:function(){return tg}});var n=r(2265),i=r(15870),o=r.n(i),a=r(61994),u=r(92713),c=r(22932),l=r(69729),s=r(49037),f=r(98628),h=r(35953),d=r(40304),p=r(56462),y=r(33968),v=t=>t.graphicalItems.polarItems,g=(0,u.P1)([d.z,p.l],f.YZ),m=(0,u.P1)([v,f.fW,g],f.$B),b=(0,u.P1)([m],f.bU),w=(0,u.P1)([b,c.RV],f.tZ),x=(0,u.P1)([w,f.fW,m],f.UA),O=(0,u.P1)([w,f.fW,m],(t,e,r)=>r.length>0?t.flatMap(t=>r.flatMap(r=>{var n;return{value:(0,s.F$)(t,null!==(n=e.dataKey)&&void 0!==n?n:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==e?void 0:e.dataKey)!=null?t.map(t=>({value:(0,s.F$)(t,e.dataKey),errorDomain:[]})):t.map(t=>({value:t,errorDomain:[]}))),P=()=>void 0,j=(0,u.P1)([f.fW,f.KB,P,O,P],f.E8),A=(0,u.P1)([f.fW,h.rE,w,x,y.Qw,d.z,j],f.l_),E=(0,u.P1)([A,f.fW,f.cV],f.vb);function S(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function M(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?S(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}(0,u.P1)([f.fW,A,E,d.z],f.kO);var k=(t,e)=>e,T=[],C=(t,e,r)=>(null==r?void 0:r.length)===0?T:r,_=(0,u.P1)([c.RV,k,C],(t,e,r)=>{var n,{chartData:i}=t;if((n=(null==e?void 0:e.data)!=null&&e.data.length>0?e.data:i)&&n.length||null==r||(n=r.map(t=>M(M({},e.presentationProps),t.props))),null!=n)return n}),D=(0,u.P1)([_,k,C],(t,e,r)=>{if(null!=t)return t.map((t,n)=>{var i,o,a=(0,s.F$)(t,e.nameKey,e.name);return o=null!=r&&null!==(i=r[n])&&void 0!==i&&null!==(i=i.props)&&void 0!==i&&i.fill?r[n].props.fill:"object"==typeof t&&null!=t&&"fill"in t?t.fill:e.fill,{value:(0,s.hn)(a,e.dataKey),color:o,payload:t,type:e.legendType}})}),N=(0,u.P1)([v,k],(t,e)=>{if(t.some(t=>"pie"===t.type&&e.dataKey===t.dataKey&&e.data===t.data))return e}),I=(0,u.P1)([_,N,C,l.DX],(t,e,r,n)=>{if(null!=e&&null!=t)return tg({offset:n,pieSettings:e,displayedData:t,cells:r})}),L=r(39040),R=r(13790),B=r(9841),z=r(57165),U=r(58811),$=r(20407),K=r(82944),F=r(34067),W=r(39206),Y=r(16630),V=r(41637),Z=r(80503),q=r(44296),H=r(35623),X=r(31944),G=r(62658),Q=r(78487),J=r(59087),tt=r(40130),te=r(46595),tr=["onMouseEnter","onClick","onMouseLeave"];function tn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ti(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tn(Object(r),!0).forEach(function(e){to(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tn(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function to(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ta(){return(ta=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function tu(t){var e=(0,n.useMemo)(()=>(0,K.L6)(t,!1),[t]),r=(0,n.useMemo)(()=>(0,K.NN)(t.children,$.b),[t.children]),i=(0,n.useMemo)(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:e}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,e]),o=(0,L.C)(t=>D(t,i,r));return n.createElement(G.t,{legendPayload:o})}function tc(t){var{dataKey:e,nameKey:r,sectors:n,stroke:i,strokeWidth:o,fill:a,name:u,hide:c,tooltipType:l}=t;return{dataDefinedOnItem:null==n?void 0:n.map(t=>t.tooltipPayload),positions:null==n?void 0:n.map(t=>t.tooltipPosition),settings:{stroke:i,strokeWidth:o,fill:a,dataKey:e,nameKey:r,name:(0,s.hn)(u,e),hide:c,type:l,color:a,unit:""}}}var tl=(t,e)=>t>e?"start":t<e?"end":"middle",ts=(t,e,r)=>"function"==typeof e?e(t):(0,Y.h1)(e,r,.8*r),tf=(t,e,r)=>{var{top:n,left:i,width:o,height:a}=e,u=(0,W.$4)(o,a),c=i+(0,Y.h1)(t.cx,o,o/2),l=n+(0,Y.h1)(t.cy,a,a/2);return{cx:c,cy:l,innerRadius:(0,Y.h1)(t.innerRadius,u,0),outerRadius:ts(r,t.outerRadius,u),maxRadius:t.maxRadius||Math.sqrt(o*o+a*a)/2}},th=(t,e)=>(0,Y.uY)(e-t)*Math.min(Math.abs(e-t),360),td=(t,e)=>{if(n.isValidElement(t))return n.cloneElement(t,e);if("function"==typeof t)return t(e);var r=(0,a.W)("recharts-pie-label-line","boolean"!=typeof t?t.className:"");return n.createElement(z.H,ta({},e,{type:"linear",className:r}))},tp=(t,e,r)=>{if(n.isValidElement(t))return n.cloneElement(t,e);var i=r;if("function"==typeof t&&(i=t(e),n.isValidElement(i)))return i;var o=(0,a.W)("recharts-pie-label-text","boolean"!=typeof t&&"function"!=typeof t?t.className:"");return n.createElement(U.x,ta({},e,{alignmentBaseline:"middle",className:o}),i)};function ty(t){var{sectors:e,props:r,showLabels:i}=t,{label:o,labelLine:a,dataKey:u}=r;if(!i||!o||!e)return null;var c=(0,K.L6)(r,!1),l=(0,K.L6)(o,!1),f=(0,K.L6)(a,!1),h="object"==typeof o&&"offsetRadius"in o&&o.offsetRadius||20,d=e.map((t,e)=>{var r=(t.startAngle+t.endAngle)/2,i=(0,W.op)(t.cx,t.cy,t.outerRadius+h,r),d=ti(ti(ti(ti({},c),t),{},{stroke:"none"},l),{},{index:e,textAnchor:tl(i.x,t.cx)},i),p=ti(ti(ti(ti({},c),t),{},{fill:"none",stroke:t.fill},f),{},{index:e,points:[(0,W.op)(t.cx,t.cy,t.outerRadius,r),i],key:"line"});return n.createElement(B.m,{key:"label-".concat(t.startAngle,"-").concat(t.endAngle,"-").concat(t.midAngle,"-").concat(e)},a&&td(a,p),tp(o,d,(0,s.F$)(t,u)))});return n.createElement(B.m,{className:"recharts-pie-labels"},d)}function tv(t){var{sectors:e,activeShape:r,inactiveShape:i,allOtherPieProps:o,showLabels:a}=t,u=(0,L.C)(X.Ve),{onMouseEnter:c,onClick:l,onMouseLeave:s}=o,f=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(o,tr),h=(0,q.Df)(c,o.dataKey),d=(0,q.oQ)(s),p=(0,q.nC)(l,o.dataKey);return null==e?null:n.createElement(n.Fragment,null,e.map((t,a)=>{if((null==t?void 0:t.startAngle)===0&&(null==t?void 0:t.endAngle)===0&&1!==e.length)return null;var c=r&&String(a)===u,l=c?r:u?i:null,s=ti(ti({},t),{},{stroke:t.stroke,tabIndex:-1,[Q.Gh]:a,[Q.aN]:o.dataKey});return n.createElement(B.m,ta({tabIndex:-1,className:"recharts-pie-sector"},(0,V.bw)(f,t,a),{onMouseEnter:h(t,a),onMouseLeave:d(t,a),onClick:p(t,a),key:"sector-".concat(null==t?void 0:t.startAngle,"-").concat(null==t?void 0:t.endAngle,"-").concat(t.midAngle,"-").concat(a)}),n.createElement(Z.b,ta({option:l,isActive:c,shapeType:"sector"},s)))}),n.createElement(ty,{sectors:e,props:o,showLabels:a}))}function tg(t){var e,r,n,{pieSettings:i,displayedData:o,cells:a,offset:u}=t,{cornerRadius:c,startAngle:l,endAngle:f,dataKey:h,nameKey:d,tooltipType:p}=i,y=Math.abs(i.minAngle),v=th(l,f),g=Math.abs(v),m=o.length<=1?0:null!==(e=i.paddingAngle)&&void 0!==e?e:0,b=o.filter(t=>0!==(0,s.F$)(t,h,0)).length,w=g-b*y-(g>=360?b:b-1)*m,x=o.reduce((t,e)=>{var r=(0,s.F$)(e,h,0);return t+((0,Y.hj)(r)?r:0)},0);return x>0&&(r=o.map((t,e)=>{var r,o=(0,s.F$)(t,h,0),f=(0,s.F$)(t,d,e),g=tf(i,u,t),b=((0,Y.hj)(o)?o:0)/x,O=ti(ti({},t),a&&a[e]&&a[e].props),P=(r=e?n.endAngle+(0,Y.uY)(v)*m*(0!==o?1:0):l)+(0,Y.uY)(v)*((0!==o?y:0)+b*w),j=(r+P)/2,A=(g.innerRadius+g.outerRadius)/2,E=[{name:f,value:o,payload:O,dataKey:h,type:p}],S=(0,W.op)(g.cx,g.cy,A,j);return n=ti(ti(ti(ti({},i.presentationProps),{},{percent:b,cornerRadius:c,name:f,tooltipPayload:E,midAngle:j,middleRadius:A,tooltipPosition:S},O),g),{},{value:(0,s.F$)(t,h),startAngle:r,endAngle:P,payload:O,paddingAngle:(0,Y.uY)(v)*m})})),r}function tm(t){var{props:e,previousSectorsRef:r}=t,{sectors:i,isAnimationActive:a,animationBegin:u,animationDuration:c,animationEasing:l,activeShape:s,inactiveShape:f,onAnimationStart:h,onAnimationEnd:d}=e,p=(0,J.i)(e,"recharts-pie-"),y=r.current,[v,g]=(0,n.useState)(!0),m=(0,n.useCallback)(()=>{"function"==typeof d&&d(),g(!1)},[d]),b=(0,n.useCallback)(()=>{"function"==typeof h&&h(),g(!0)},[h]);return n.createElement(te.r,{begin:u,duration:c,isActive:a,easing:l,from:{t:0},to:{t:1},onAnimationStart:b,onAnimationEnd:m,key:p},t=>{var{t:a}=t,u=[],c=(i&&i[0]).startAngle;return i.forEach((t,e)=>{var r=y&&y[e],n=e>0?o()(t,"paddingAngle",0):0;if(r){var i=(0,Y.k4)(r.endAngle-r.startAngle,t.endAngle-t.startAngle),l=ti(ti({},t),{},{startAngle:c+n,endAngle:c+i(a)+n});u.push(l),c=l.endAngle}else{var{endAngle:s,startAngle:f}=t,h=(0,Y.k4)(0,s-f)(a),d=ti(ti({},t),{},{startAngle:c+n,endAngle:c+h+n});u.push(d),c=d.endAngle}}),r.current=u,n.createElement(B.m,null,n.createElement(tv,{sectors:u,activeShape:s,inactiveShape:f,allOtherPieProps:e,showLabels:!v}))})}function tb(t){var{sectors:e,isAnimationActive:r,activeShape:i,inactiveShape:o}=t,a=(0,n.useRef)(null),u=a.current;return r&&e&&e.length&&(!u||u!==e)?n.createElement(tm,{props:t,previousSectorsRef:a}):n.createElement(tv,{sectors:e,activeShape:i,inactiveShape:o,allOtherPieProps:t,showLabels:!0})}function tw(t){var{hide:e,className:r,rootTabIndex:i}=t,o=(0,a.W)("recharts-pie",r);return e?null:n.createElement(B.m,{tabIndex:i,className:o},n.createElement(tb,t))}var tx={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!F.x.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function tO(t){var e=(0,tt.j)(t,tx),r=(0,n.useMemo)(()=>(0,K.NN)(t.children,$.b),[t.children]),i=(0,K.L6)(e,!1),o=(0,n.useMemo)(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:i}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,i]),a=(0,L.C)(t=>I(t,o,r));return n.createElement(n.Fragment,null,n.createElement(H.k,{fn:tc,args:ti(ti({},e),{},{sectors:a})}),n.createElement(tw,ta({},e,{sectors:a})))}class tP extends n.PureComponent{render(){return n.createElement(n.Fragment,null,n.createElement(R.E,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),n.createElement(tu,this.props),n.createElement(tO,this.props),this.props.children)}constructor(){super(...arguments),to(this,"id",(0,Y.EL)("recharts-pie-"))}}to(tP,"displayName","Pie"),to(tP,"defaultProps",tx)},57165:function(t,e,r){"use strict";r.d(e,{H:function(){return F}});var n=r(2265);function i(){}function o(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function a(t){this._context=t}function u(t){this._context=t}function c(t){this._context=t}a.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:o(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:o(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},u.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:o(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},c.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:o(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class l{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function s(t){this._context=t}function f(t){this._context=t}function h(t){return new f(t)}function d(t,e,r){var n=t._x1-t._x0,i=e-t._x1,o=(t._y1-t._y0)/(n||i<0&&-0),a=(r-t._y1)/(i||n<0&&-0);return((o<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(o),Math.abs(a),.5*Math.abs((o*i+a*n)/(n+i)))||0}function p(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function y(t,e,r){var n=t._x0,i=t._y0,o=t._x1,a=t._y1,u=(o-n)/3;t._context.bezierCurveTo(n+u,i+u*e,o-u,a-u*r,o,a)}function v(t){this._context=t}function g(t){this._context=new m(t)}function m(t){this._context=t}function b(t){this._context=t}function w(t){var e,r,n=t.length-1,i=Array(n),o=Array(n),a=Array(n);for(i[0]=0,o[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)i[e]=1,o[e]=4,a[e]=4*t[e]+2*t[e+1];for(i[n-1]=2,o[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=i[e]/o[e-1],o[e]-=r,a[e]-=r*a[e-1];for(i[n-1]=a[n-1]/o[n-1],e=n-2;e>=0;--e)i[e]=(a[e]-i[e+1])/o[e];for(e=0,o[n-1]=(t[n]+i[n-1])/2;e<n-1;++e)o[e]=2*t[e+1]-i[e+1];return[i,o]}function x(t,e){this._context=t,this._t=e}s.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,p(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,y(this,p(this,r=d(this,t,e)),r);break;default:y(this,this._t0,r=d(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(g.prototype=Object.create(v.prototype)).point=function(t,e){v.prototype.point.call(this,e,t)},m.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,i,o){this._context.bezierCurveTo(e,t,n,r,o,i)}},b.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r){if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=w(t),i=w(e),o=0,a=1;a<r;++o,++a)this._context.bezierCurveTo(n[0][o],i[0][o],n[1][o],i[1][o],t[a],e[a])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},x.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var O=r(22516),P=r(76115),j=r(67790);function A(t){return t[0]}function E(t){return t[1]}function S(t,e){var r=(0,P.Z)(!0),n=null,i=h,o=null,a=(0,j.d)(u);function u(u){var c,l,s,f=(u=(0,O.Z)(u)).length,h=!1;for(null==n&&(o=i(s=a())),c=0;c<=f;++c)!(c<f&&r(l=u[c],c,u))===h&&((h=!h)?o.lineStart():o.lineEnd()),h&&o.point(+t(l,c,u),+e(l,c,u));if(s)return o=null,s+""||null}return t="function"==typeof t?t:void 0===t?A:(0,P.Z)(t),e="function"==typeof e?e:void 0===e?E:(0,P.Z)(e),u.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,P.Z)(+e),u):t},u.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,P.Z)(+t),u):e},u.defined=function(t){return arguments.length?(r="function"==typeof t?t:(0,P.Z)(!!t),u):r},u.curve=function(t){return arguments.length?(i=t,null!=n&&(o=i(n)),u):i},u.context=function(t){return arguments.length?(null==t?n=o=null:o=i(n=t),u):n},u}function M(t,e,r){var n=null,i=(0,P.Z)(!0),o=null,a=h,u=null,c=(0,j.d)(l);function l(l){var s,f,h,d,p,y=(l=(0,O.Z)(l)).length,v=!1,g=Array(y),m=Array(y);for(null==o&&(u=a(p=c())),s=0;s<=y;++s){if(!(s<y&&i(d=l[s],s,l))===v){if(v=!v)f=s,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),h=s-1;h>=f;--h)u.point(g[h],m[h]);u.lineEnd(),u.areaEnd()}}v&&(g[s]=+t(d,s,l),m[s]=+e(d,s,l),u.point(n?+n(d,s,l):g[s],r?+r(d,s,l):m[s]))}if(p)return u=null,p+""||null}function s(){return S().defined(i).curve(a).context(o)}return t="function"==typeof t?t:void 0===t?A:(0,P.Z)(+t),e="function"==typeof e?e:void 0===e?(0,P.Z)(0):(0,P.Z)(+e),r="function"==typeof r?r:void 0===r?E:(0,P.Z)(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,P.Z)(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:(0,P.Z)(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:(0,P.Z)(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,P.Z)(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:(0,P.Z)(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:(0,P.Z)(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(i="function"==typeof t?t:(0,P.Z)(!!t),l):i},l.curve=function(t){return arguments.length?(a=t,null!=o&&(u=a(o)),l):a},l.context=function(t){return arguments.length?(null==t?o=u=null:u=a(o=t),l):o},l}var k=r(61994),T=r(41637),C=r(82944),_=r(16630),D=r(66395);function N(){return(N=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function I(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function L(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?I(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var R={curveBasisClosed:function(t){return new u(t)},curveBasisOpen:function(t){return new c(t)},curveBasis:function(t){return new a(t)},curveBumpX:function(t){return new l(t,!0)},curveBumpY:function(t){return new l(t,!1)},curveLinearClosed:function(t){return new s(t)},curveLinear:h,curveMonotoneX:function(t){return new v(t)},curveMonotoneY:function(t){return new g(t)},curveNatural:function(t){return new b(t)},curveStep:function(t){return new x(t,.5)},curveStepAfter:function(t){return new x(t,1)},curveStepBefore:function(t){return new x(t,0)}},B=t=>(0,D.n)(t.x)&&(0,D.n)(t.y),z=t=>t.x,U=t=>t.y,$=(t,e)=>{if("function"==typeof t)return t;var r="curve".concat((0,_.jC)(t));return("curveMonotone"===r||"curveBump"===r)&&e?R["".concat(r).concat("vertical"===e?"Y":"X")]:R[r]||h},K=t=>{var e,{type:r="linear",points:n=[],baseLine:i,layout:o,connectNulls:a=!1}=t,u=$(r,o),c=a?n.filter(B):n;if(Array.isArray(i)){var l=a?i.filter(t=>B(t)):i,s=c.map((t,e)=>L(L({},t),{},{base:l[e]}));return(e="vertical"===o?M().y(U).x1(z).x0(t=>t.base.x):M().x(z).y1(U).y0(t=>t.base.y)).defined(B).curve(u),e(s)}return(e="vertical"===o&&(0,_.hj)(i)?M().y(U).x1(z).x0(i):(0,_.hj)(i)?M().x(z).y1(U).y0(i):S().x(z).y(U)).defined(B).curve(u),e(c)},F=t=>{var{className:e,points:r,path:i,pathRef:o}=t;if((!r||!r.length)&&!i)return null;var a=r&&r.length?K(t):i;return n.createElement("path",N({},(0,C.L6)(t,!1),(0,T.Ym)(t),{className:(0,k.W)("recharts-curve",e),d:null===a?void 0:a,ref:o}))}},73649:function(t,e,r){"use strict";r.d(e,{A:function(){return f}});var n=r(2265),i=r(61994),o=r(82944),a=r(40130),u=r(46595);function c(){return(c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var l=(t,e,r,n,i)=>{var o,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),u=n>=0?1:-1,c=r>=0?1:-1,l=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>a?a:i[f];o="M".concat(t,",").concat(e+u*s[0]),s[0]>0&&(o+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+c*s[0],",").concat(e)),o+="L ".concat(t+r-c*s[1],",").concat(e),s[1]>0&&(o+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+u*s[1])),o+="L ".concat(t+r,",").concat(e+n-u*s[2]),s[2]>0&&(o+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-c*s[2],",").concat(e+n)),o+="L ".concat(t+c*s[3],",").concat(e+n),s[3]>0&&(o+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-u*s[3])),o+="Z"}else if(a>0&&i===+i&&i>0){var h=Math.min(a,i);o="M ".concat(t,",").concat(e+u*h,"\n            A ").concat(h,",").concat(h,",0,0,").concat(l,",").concat(t+c*h,",").concat(e,"\n            L ").concat(t+r-c*h,",").concat(e,"\n            A ").concat(h,",").concat(h,",0,0,").concat(l,",").concat(t+r,",").concat(e+u*h,"\n            L ").concat(t+r,",").concat(e+n-u*h,"\n            A ").concat(h,",").concat(h,",0,0,").concat(l,",").concat(t+r-c*h,",").concat(e+n,"\n            L ").concat(t+c*h,",").concat(e+n,"\n            A ").concat(h,",").concat(h,",0,0,").concat(l,",").concat(t,",").concat(e+n-u*h," Z")}else o="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return o},s={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},f=t=>{var e=(0,a.j)(t,s),r=(0,n.useRef)(null),[f,h]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var t=r.current.getTotalLength();t&&h(t)}catch(t){}},[]);var{x:d,y:p,width:y,height:v,radius:g,className:m}=e,{animationEasing:b,animationDuration:w,animationBegin:x,isAnimationActive:O,isUpdateAnimationActive:P}=e;if(d!==+d||p!==+p||y!==+y||v!==+v||0===y||0===v)return null;var j=(0,i.W)("recharts-rectangle",m);return P?n.createElement(u.r,{canBegin:f>0,from:{width:y,height:v,x:d,y:p},to:{width:y,height:v,x:d,y:p},duration:w,animationEasing:b,isActive:P},t=>{var{width:i,height:a,x:s,y:h}=t;return n.createElement(u.r,{canBegin:f>0,from:"0px ".concat(-1===f?1:f,"px"),to:"".concat(f,"px 0px"),attributeName:"strokeDasharray",begin:x,duration:w,isActive:O,easing:b},n.createElement("path",c({},(0,o.L6)(e,!0),{className:j,d:l(s,h,i,a,g),ref:r})))}):n.createElement("path",c({},(0,o.L6)(e,!0),{className:j,d:l(d,p,y,v,g)}))}},60474:function(t,e,r){"use strict";r.d(e,{L:function(){return y}});var n=r(2265),i=r(61994),o=r(82944),a=r(39206),u=r(16630),c=r(40130);function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var s=(t,e)=>(0,u.uY)(e-t)*Math.min(Math.abs(e-t),359.999),f=t=>{var{cx:e,cy:r,radius:n,angle:i,sign:o,isExternal:u,cornerRadius:c,cornerIsExternal:l}=t,s=c*(u?1:-1)+n,f=Math.asin(c/s)/a.Wk,h=l?i:i+o*f;return{center:(0,a.op)(e,r,s,h),circleTangency:(0,a.op)(e,r,n,h),lineTangency:(0,a.op)(e,r,s*Math.cos(f*a.Wk),l?i-o*f:i),theta:f}},h=t=>{var{cx:e,cy:r,innerRadius:n,outerRadius:i,startAngle:o,endAngle:u}=t,c=s(o,u),l=o+c,f=(0,a.op)(e,r,i,o),h=(0,a.op)(e,r,i,l),d="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(o>l),",\n    ").concat(h.x,",").concat(h.y,"\n  ");if(n>0){var p=(0,a.op)(e,r,n,o),y=(0,a.op)(e,r,n,l);d+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(o<=l),",\n            ").concat(p.x,",").concat(p.y," Z")}else d+="L ".concat(e,",").concat(r," Z");return d},d=t=>{var{cx:e,cy:r,innerRadius:n,outerRadius:i,cornerRadius:o,forceCornerRadius:a,cornerIsExternal:c,startAngle:l,endAngle:s}=t,d=(0,u.uY)(s-l),{circleTangency:p,lineTangency:y,theta:v}=f({cx:e,cy:r,radius:i,angle:l,sign:d,cornerRadius:o,cornerIsExternal:c}),{circleTangency:g,lineTangency:m,theta:b}=f({cx:e,cy:r,radius:i,angle:s,sign:-d,cornerRadius:o,cornerIsExternal:c}),w=c?Math.abs(l-s):Math.abs(l-s)-v-b;if(w<0)return a?"M ".concat(y.x,",").concat(y.y,"\n        a").concat(o,",").concat(o,",0,0,1,").concat(2*o,",0\n        a").concat(o,",").concat(o,",0,0,1,").concat(-(2*o),",0\n      "):h({cx:e,cy:r,innerRadius:n,outerRadius:i,startAngle:l,endAngle:s});var x="M ".concat(y.x,",").concat(y.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(d<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(w>180),",").concat(+(d<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(d<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var{circleTangency:O,lineTangency:P,theta:j}=f({cx:e,cy:r,radius:n,angle:l,sign:d,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),{circleTangency:A,lineTangency:E,theta:S}=f({cx:e,cy:r,radius:n,angle:s,sign:-d,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),M=c?Math.abs(l-s):Math.abs(l-s)-j-S;if(M<0&&0===o)return"".concat(x,"L").concat(e,",").concat(r,"Z");x+="L".concat(E.x,",").concat(E.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(d<0),",").concat(A.x,",").concat(A.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(M>180),",").concat(+(d>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(d<0),",").concat(P.x,",").concat(P.y,"Z")}else x+="L".concat(e,",").concat(r,"Z");return x},p={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},y=t=>{var e,r=(0,c.j)(t,p),{cx:a,cy:s,innerRadius:f,outerRadius:y,cornerRadius:v,forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:w,className:x}=r;if(y<f||b===w)return null;var O=(0,i.W)("recharts-sector",x),P=y-f,j=(0,u.h1)(v,P,0,!0);return e=j>0&&360>Math.abs(b-w)?d({cx:a,cy:s,innerRadius:f,outerRadius:y,cornerRadius:Math.min(j,P/2),forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:w}):h({cx:a,cy:s,innerRadius:f,outerRadius:y,startAngle:b,endAngle:w}),n.createElement("path",l({},(0,o.L6)(r,!0),{className:O,d:e}))}},50209:function(t,e,r){"use strict";r.d(e,{l:function(){return n}});var n=(0,r(2265).createContext)(null)},12528:function(t,e,r){"use strict";r.d(e,{M:function(){return G}});var n=r(2265);r(76548);var i={notify(){},get:()=>[]},o=!!("undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement),a="undefined"!=typeof navigator&&"ReactNative"===navigator.product,u=o||a?n.useLayoutEffect:n.useEffect,c=Symbol.for("react-redux-context"),l="undefined"!=typeof globalThis?globalThis:{},s=function(){if(!n.createContext)return{};let t=l[c]??=new Map,e=t.get(n.createContext);return e||(e=n.createContext(null),t.set(n.createContext,e)),e}(),f=function(t){let{children:e,context:r,serverState:o,store:a}=t,c=n.useMemo(()=>{let t=function(t,e){let r;let n=i,o=0,a=!1;function u(){s.onStateChange&&s.onStateChange()}function c(){if(o++,!r){let e,i;r=t.subscribe(u),e=null,i=null,n={clear(){e=null,i=null},notify(){(()=>{let t=e;for(;t;)t.callback(),t=t.next})()},get(){let t=[],r=e;for(;r;)t.push(r),r=r.next;return t},subscribe(t){let r=!0,n=i={callback:t,next:null,prev:i};return n.prev?n.prev.next=n:e=n,function(){r&&null!==e&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:e=n.next)}}}}}function l(){o--,r&&0===o&&(r(),r=void 0,n.clear(),n=i)}let s={addNestedSub:function(t){c();let e=n.subscribe(t),r=!1;return()=>{r||(r=!0,e(),l())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:u,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,c())},tryUnsubscribe:function(){a&&(a=!1,l())},getListeners:()=>n};return s}(a);return{store:a,subscription:t,getServerState:o?()=>o:void 0}},[a,o]),l=n.useMemo(()=>a.getState(),[a]);return u(()=>{let{subscription:t}=c;return t.onStateChange=t.notifyNestedSubs,t.trySubscribe(),l!==a.getState()&&t.notifyNestedSubs(),()=>{t.tryUnsubscribe(),t.onStateChange=void 0}},[c,l]),n.createElement((r||s).Provider,{value:c},e)},h=r(59688),d=r(39129),p=r(31057),y=r(64725),v=r(39173),g=r(65293),m=r(48323);function b(t,e){return e instanceof HTMLElement?"HTMLElement <".concat(e.tagName,' class="').concat(e.className,'">'):e===window?"global.window":e}var w=r(17644),x=r(19579),O=r(10418),P=(0,d.oM)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(t,e)=>{t.dots.push(e.payload)},removeDot:(t,e)=>{var r=(0,O.Vk)(t).dots.findIndex(t=>t===e.payload);-1!==r&&t.dots.splice(r,1)},addArea:(t,e)=>{t.areas.push(e.payload)},removeArea:(t,e)=>{var r=(0,O.Vk)(t).areas.findIndex(t=>t===e.payload);-1!==r&&t.areas.splice(r,1)},addLine:(t,e)=>{t.lines.push(e.payload)},removeLine:(t,e)=>{var r=(0,O.Vk)(t).lines.findIndex(t=>t===e.payload);-1!==r&&t.lines.splice(r,1)}}}),{addDot:j,removeDot:A,addArea:E,removeArea:S,addLine:M,removeLine:k}=P.actions,T=P.reducer,C={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},_=(0,d.oM)({name:"brush",initialState:C,reducers:{setBrushSettings:(t,e)=>null==e.payload?C:e.payload}}),{setBrushSettings:D}=_.actions,N=_.reducer,I=r(32738),L=r(59156),R=(0,d.oM)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(t,e){t.radiusAxis[e.payload.id]=(0,O.cA)(e.payload)},removeRadiusAxis(t,e){delete t.radiusAxis[e.payload.id]},addAngleAxis(t,e){t.angleAxis[e.payload.id]=(0,O.cA)(e.payload)},removeAngleAxis(t,e){delete t.angleAxis[e.payload.id]}}}),{addRadiusAxis:B,removeRadiusAxis:z,addAngleAxis:U,removeAngleAxis:$}=R.actions,K=R.reducer,F=r(27410),W=r(7883),Y=r(69366),V=r(83061),Z=(0,h.UY)({brush:N,cartesianAxis:w.vk,chartData:v.Q2,graphicalItems:x.iX,layout:g.EW,legend:I.Ny,options:p.wB,polarAxis:K,polarOptions:F.i,referenceElements:T,rootProps:L.$f,tooltip:y.Kw}),q=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,d.xC)({reducer:Z,preloadedState:t,middleware:t=>t({serializableCheck:!1}).concat([m.RG.middleware,m.an.middleware,W._2.middleware,Y.Y.middleware,V.x.middleware]),devTools:{serialize:{replacer:b},name:"recharts-".concat(e)}})},H=r(58735),X=r(50209);function G(t){var{preloadedState:e,children:r,reduxStoreName:i}=t,o=(0,H.W)(),a=(0,n.useRef)(null);if(o)return r;null==a.current&&(a.current=q(e,i));var u=X.l;return n.createElement(f,{context:u,store:a.current},r)}},15317:function(t,e,r){"use strict";r.d(e,{b:function(){return a}});var n=r(2265),i=r(59156),o=r(39040);function a(t){var e=(0,o.T)();return(0,n.useEffect)(()=>{e((0,i.Rk)(t))},[e,t]),null}},87235:function(t,e,r){"use strict";r.d(e,{v:function(){return u}});var n=r(2265),i=r(58735),o=r(65293),a=r(39040);function u(t){var{layout:e,width:r,height:u,margin:c}=t,l=(0,a.T)(),s=(0,i.W)();return(0,n.useEffect)(()=>{s||(l((0,o.jx)(e)),l((0,o.Dx)({width:r,height:u})),l((0,o.Qb)(c)))},[l,s,e,r,u,c]),null}},13790:function(t,e,r){"use strict";r.d(e,{E:function(){return s},V:function(){return l}});var n=r(2265),i=r(39040),o=r(19579),a=r(49037);function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function c(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function l(t){var e=(0,i.T)(),r=(0,n.useRef)(null);return(0,n.useEffect)(()=>{var n=c(c({},t),{},{stackId:(0,a.GA)(t.stackId)});null===r.current?e((0,o.AC)(n)):r.current!==n&&e((0,o.Bi)({prev:r.current,next:n})),r.current=n},[e,t]),(0,n.useEffect)(()=>()=>{r.current&&(e((0,o._Q)(r.current)),r.current=null)},[e]),null}function s(t){var e=(0,i.T)();return(0,n.useEffect)(()=>(e((0,o.Wz)(t)),()=>{e((0,o.ot)(t))}),[e,t]),null}},62658:function(t,e,r){"use strict";r.d(e,{L:function(){return l},t:function(){return s}});var n=r(2265),i=r(58735),o=r(35953),a=r(39040),u=r(32738),c=()=>{};function l(t){var{legendPayload:e}=t,r=(0,a.T)(),o=(0,i.W)();return(0,n.useEffect)(()=>o?c:(r((0,u.t8)(e)),()=>{r((0,u.ZR)(e))}),[r,o,e]),null}function s(t){var{legendPayload:e}=t,r=(0,a.T)(),i=(0,a.C)(o.rE);return(0,n.useEffect)(()=>"centric"!==i&&"radial"!==i?c:(r((0,u.t8)(e)),()=>{r((0,u.ZR)(e))}),[r,i,e]),null}},35623:function(t,e,r){"use strict";r.d(e,{k:function(){return u}});var n=r(2265),i=r(39040),o=r(64725),a=r(58735);function u(t){var{fn:e,args:r}=t,u=(0,i.T)(),c=(0,a.W)();return(0,n.useEffect)(()=>{if(!c){var t=e(r);return u((0,o.KO)(t)),()=>{u((0,o.cK)(t))}}},[e,r,u,c]),null}},17644:function(t,e,r){"use strict";r.d(e,{Jj:function(){return l},TC:function(){return s},cB:function(){return f},kB:function(){return p},m2:function(){return c},vk:function(){return y}});var n=r(39129),i=r(10418);function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?o(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var u=(0,n.oM)({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(t,e){t.xAxis[e.payload.id]=(0,i.cA)(e.payload)},removeXAxis(t,e){delete t.xAxis[e.payload.id]},addYAxis(t,e){t.yAxis[e.payload.id]=(0,i.cA)(e.payload)},removeYAxis(t,e){delete t.yAxis[e.payload.id]},addZAxis(t,e){t.zAxis[e.payload.id]=(0,i.cA)(e.payload)},removeZAxis(t,e){delete t.zAxis[e.payload.id]},updateYAxisWidth(t,e){var{id:r,width:n}=e.payload;t.yAxis[r]&&(t.yAxis[r]=a(a({},t.yAxis[r]),{},{width:n}))}}}),{addXAxis:c,removeXAxis:l,addYAxis:s,removeYAxis:f,addZAxis:h,removeZAxis:d,updateYAxisWidth:p}=u.actions,y=u.reducer},39173:function(t,e,r){"use strict";r.d(e,{Q2:function(){return u},t0:function(){return o},zR:function(){return i}});var n=(0,r(39129).oM)({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(t,e){if(t.chartData=e.payload,null==e.payload){t.dataStartIndex=0,t.dataEndIndex=0;return}e.payload.length>0&&t.dataEndIndex!==e.payload.length-1&&(t.dataEndIndex=e.payload.length-1)},setComputedData(t,e){t.computedData=e.payload},setDataStartEndIndexes(t,e){var{startIndex:r,endIndex:n}=e.payload;null!=r&&(t.dataStartIndex=r),null!=n&&(t.dataEndIndex=n)}}}),{setChartData:i,setDataStartEndIndexes:o,setComputedData:a}=n.actions,u=n.reducer},69366:function(t,e,r){"use strict";r.d(e,{Y:function(){return a},r:function(){return o}});var n=r(39129),i=r(31944),o=(0,n.PH)("externalEvent"),a=(0,n.e)();a.startListening({actionCreator:o,effect:(t,e)=>{if(null!=t.payload.handler){var r=e.getState(),n={activeCoordinate:(0,i.pI)(r),activeDataKey:(0,i.du)(r),activeIndex:(0,i.Ve)(r),activeLabel:(0,i.i)(r),activeTooltipIndex:(0,i.Ve)(r),isTooltipActive:(0,i.oo)(r)};t.payload.handler(n,t.payload.reactEvent)}}})},19579:function(t,e,r){"use strict";r.d(e,{AC:function(){return c},Bi:function(){return l},Wz:function(){return f},_Q:function(){return s},a1:function(){return a},iX:function(){return d},nF:function(){return u},ot:function(){return h}});var n=r(39129),i=r(10418),o=(0,n.oM)({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(t){t.countOfBars+=1},removeBar(t){t.countOfBars-=1},addCartesianGraphicalItem(t,e){t.cartesianItems.push((0,i.cA)(e.payload))},replaceCartesianGraphicalItem(t,e){var{prev:r,next:n}=e.payload,o=(0,i.Vk)(t).cartesianItems.indexOf((0,i.cA)(r));o>-1&&(t.cartesianItems[o]=(0,i.cA)(n))},removeCartesianGraphicalItem(t,e){var r=(0,i.Vk)(t).cartesianItems.indexOf((0,i.cA)(e.payload));r>-1&&t.cartesianItems.splice(r,1)},addPolarGraphicalItem(t,e){t.polarItems.push((0,i.cA)(e.payload))},removePolarGraphicalItem(t,e){var r=(0,i.Vk)(t).polarItems.indexOf((0,i.cA)(e.payload));r>-1&&t.polarItems.splice(r,1)}}}),{addBar:a,removeBar:u,addCartesianGraphicalItem:c,replaceCartesianGraphicalItem:l,removeCartesianGraphicalItem:s,addPolarGraphicalItem:f,removePolarGraphicalItem:h}=o.actions,d=o.reducer},39040:function(t,e,r){"use strict";r.d(e,{C:function(){return f},T:function(){return u}});var n=r(35195),i=r(2265),o=r(50209),a=t=>t,u=()=>{var t=(0,i.useContext)(o.l);return t?t.store.dispatch:a},c=()=>{},l=()=>c,s=(t,e)=>t===e;function f(t){var e=(0,i.useContext)(o.l);return(0,n.useSyncExternalStoreWithSelector)(e?e.subscription.addNestedSub:l,e?e.store.getState:c,e?e.store.getState:c,e?t:c,s)}},7883:function(t,e,r){"use strict";r.d(e,{_2:function(){return f},eb:function(){return s},sj:function(){return l}});var n=r(39129),i=r(64725),o=r(31944),a=r(84461),u=r(98628),c=r(6064),l=(0,n.PH)("keyDown"),s=(0,n.PH)("focus"),f=(0,n.e)();f.startListening({actionCreator:l,effect:(t,e)=>{var r=e.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,l=t.payload;if("ArrowRight"===l||"ArrowLeft"===l||"Enter"===l){var s=Number((0,c.p)(n,(0,o.wQ)(r))),f=(0,o.WQ)(r);if("Enter"===l){var h=(0,a.hA)(r,"axis","hover",String(n.index));e.dispatch((0,i.KN)({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:h}));return}var d=s+("ArrowRight"===l?1:-1)*("left-to-right"===(0,u.Xv)(r)?1:-1);if(null!=f&&!(d>=f.length)&&!(d<0)){var p=(0,a.hA)(r,"axis","hover",String(d));e.dispatch((0,i.KN)({active:!0,activeIndex:d.toString(),activeDataKey:void 0,activeCoordinate:p}))}}}}}),f.startListening({actionCreator:s,effect:(t,e)=>{var r=e.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var o=(0,a.hA)(r,"axis","hover",String("0"));e.dispatch((0,i.KN)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:o}))}}}})},65293:function(t,e,r){"use strict";r.d(e,{Dx:function(){return a},EW:function(){return c},Qb:function(){return i},ZP:function(){return u},jx:function(){return o}});var n=(0,r(39129).oM)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(t,e){t.layoutType=e.payload},setChartSize(t,e){t.width=e.payload.width,t.height=e.payload.height},setMargin(t,e){t.margin.top=e.payload.top,t.margin.right=e.payload.right,t.margin.bottom=e.payload.bottom,t.margin.left=e.payload.left},setScale(t,e){t.scale=e.payload}}}),{setMargin:i,setLayout:o,setChartSize:a,setScale:u}=n.actions,c=n.reducer},32738:function(t,e,r){"use strict";r.d(e,{Ny:function(){return s},ZR:function(){return l},t8:function(){return c}});var n=r(39129),i=r(10418),o=(0,n.oM)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(t,e){t.size.width=e.payload.width,t.size.height=e.payload.height},setLegendSettings(t,e){t.settings.align=e.payload.align,t.settings.layout=e.payload.layout,t.settings.verticalAlign=e.payload.verticalAlign,t.settings.itemSorter=e.payload.itemSorter},addLegendPayload(t,e){t.payload.push((0,i.cA)(e.payload))},removeLegendPayload(t,e){var r=(0,i.Vk)(t).payload.indexOf((0,i.cA)(e.payload));r>-1&&t.payload.splice(r,1)}}}),{setLegendSize:a,setLegendSettings:u,addLegendPayload:c,removeLegendPayload:l}=o.actions,s=o.reducer},48323:function(t,e,r){"use strict";r.d(e,{AE:function(){return c},RG:function(){return l},TK:function(){return s},an:function(){return f}});var n=r(39129),i=r(64725),o=r(63330),a=r(46302),u=r(13169),c=(0,n.PH)("mouseClick"),l=(0,n.e)();l.startListening({actionCreator:c,effect:(t,e)=>{var r=t.payload,n=(0,o.h)(e.getState(),(0,u.a)(r));(null==n?void 0:n.activeIndex)!=null&&e.dispatch((0,i.eB)({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var s=(0,n.PH)("mouseMove"),f=(0,n.e)();f.startListening({actionCreator:s,effect:(t,e)=>{var r=t.payload,n=e.getState(),c=(0,a.SB)(n,n.tooltip.settings.shared),l=(0,o.h)(n,(0,u.a)(r));"axis"===c&&((null==l?void 0:l.activeIndex)!=null?e.dispatch((0,i.Rr)({activeIndex:l.activeIndex,activeDataKey:void 0,activeCoordinate:l.activeCoordinate})):e.dispatch((0,i.ne)()))}})},31057:function(t,e,r){"use strict";r.d(e,{IC:function(){return c},NL:function(){return o},wB:function(){return u}});var n=r(39129),i=r(16630);function o(t,e){if(e){var r=Number.parseInt(e,10);if(!(0,i.In)(r))return null==t?void 0:t[r]}}var a=(0,n.oM)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:t=>{null==t.eventEmitter&&(t.eventEmitter=Symbol("rechartsEventEmitter"))}}}),u=a.reducer,{createEventEmitter:c}=a.actions},27410:function(t,e,r){"use strict";r.d(e,{a:function(){return i},i:function(){return o}});var n=(0,r(39129).oM)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(t,e)=>e.payload}}),{updatePolarOptions:i}=n.actions,o=n.reducer},59156:function(t,e,r){"use strict";r.d(e,{$f:function(){return a},Rk:function(){return u}});var n=r(39129),i={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},o=(0,n.oM)({name:"rootProps",initialState:i,reducers:{updateOptions:(t,e)=>{var r;t.accessibilityLayer=e.payload.accessibilityLayer,t.barCategoryGap=e.payload.barCategoryGap,t.barGap=null!==(r=e.payload.barGap)&&void 0!==r?r:i.barGap,t.barSize=e.payload.barSize,t.maxBarSize=e.payload.maxBarSize,t.stackOffset=e.payload.stackOffset,t.syncId=e.payload.syncId,t.syncMethod=e.payload.syncMethod,t.className=e.payload.className}}}),a=o.reducer,{updateOptions:u}=o.actions},98628:function(t,e,r){"use strict";r.d(e,{VQ:function(){return io},UA:function(){return n4},yT:function(){return iO},l_:function(){return iC},kO:function(){return iB},UC:function(){return on},tZ:function(){return n3},dz:function(){return ir},zX:function(){return iw},rC:function(){return oe},bU:function(){return n2},$B:function(){return nQ},fY:function(){return ij},vb:function(){return iL},E8:function(){return iM},Yf:function(){return iD},Jw:function(){return iI},CK:function(){return it},Tk:function(){return n0},zW:function(){return ip},c4:function(){return is},dW:function(){return nU},RN:function(){return nK},YZ:function(){return nH},Eu:function(){return ih},Lg:function(){return oo},Fn:function(){return iV},Vm:function(){return iq},ub:function(){return nZ},AS:function(){return oc},fW:function(){return nV},Lu:function(){return ot},Xv:function(){return os},KB:function(){return iS},Q4:function(){return nq},cV:function(){return iN},ww:function(){return iv},RA:function(){return id},xz:function(){return im},g6:function(){return ie},ox:function(){return oa},bY:function(){return ou},bm:function(){return nX},rs:function(){return i8},i9:function(){return n$},Oy:function(){return i2},lU:function(){return i9},t:function(){return nF},ON:function(){return i7}});var n,i,o,a,u,c,l,s={};r.r(s),r.d(s,{scaleBand:function(){return x},scaleDiverging:function(){return function t(){var e=tY(r4()(tS));return e.copy=function(){return r6(e,t())},y.apply(e,arguments)}},scaleDivergingLog:function(){return function t(){var e=tJ(r4()).domain([.1,1,10]);return e.copy=function(){return r6(e,t()).base(e.base())},y.apply(e,arguments)}},scaleDivergingPow:function(){return r8},scaleDivergingSqrt:function(){return r9},scaleDivergingSymlog:function(){return function t(){var e=t2(r4());return e.copy=function(){return r6(e,t()).constant(e.constant())},y.apply(e,arguments)}},scaleIdentity:function(){return function t(e){var r;function n(t){return null==t||isNaN(t=+t)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,tA),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,tA):[0,1],tY(n)}},scaleImplicit:function(){return b},scaleLinear:function(){return function t(){var e=tD();return e.copy=function(){return tC(e,t())},p.apply(e,arguments),tY(e)}},scaleLog:function(){return function t(){let e=tJ(t_()).domain([1,10]);return e.copy=()=>tC(e,t()).base(e.base()),p.apply(e,arguments),e}},scaleOrdinal:function(){return w},scalePoint:function(){return O},scalePow:function(){return t8},scaleQuantile:function(){return function t(){var e,r=[],n=[],i=[];function o(){var t=0,e=Math.max(1,n.length);for(i=Array(e-1);++t<e;)i[t-1]=function(t,e,r=N){if(!(!(n=t.length)||isNaN(e=+e))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,i=(n-1)*e,o=Math.floor(i),a=+r(t[o],o,t);return a+(+r(t[o+1],o+1,t)-a)*(i-o)}}(r,t/e);return a}function a(t){return null==t||isNaN(t=+t)?e:n[L(i,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?i[e-1]:r[0],e<i.length?i[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e=+e)||r.push(e);return r.sort(T),o()},a.range=function(t){return arguments.length?(n=Array.from(t),o()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return i.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},p.apply(a,arguments)}},scaleQuantize:function(){return function t(){var e,r=0,n=1,i=1,o=[.5],a=[0,1];function u(t){return null!=t&&t<=t?a[L(o,t,0,i)]:e}function c(){var t=-1;for(o=Array(i);++t<i;)o[t]=((t+1)*n-(t-i)*r)/(i+1);return u}return u.domain=function(t){return arguments.length?([r,n]=t,r=+r,n=+n,c()):[r,n]},u.range=function(t){return arguments.length?(i=(a=Array.from(t)).length-1,c()):a.slice()},u.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,o[0]]:e>=i?[o[i-1],n]:[o[e-1],o[e]]},u.unknown=function(t){return arguments.length&&(e=t),u},u.thresholds=function(){return o.slice()},u.copy=function(){return t().domain([r,n]).range(a).unknown(e)},p.apply(tY(u),arguments)}},scaleRadial:function(){return function t(){var e,r=tD(),n=[0,1],i=!1;function o(t){var n,o=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(o)?e:i?Math.round(o):o}return o.invert=function(t){return r.invert(t7(t))},o.domain=function(t){return arguments.length?(r.domain(t),o):r.domain()},o.range=function(t){return arguments.length?(r.range((n=Array.from(t,tA)).map(t7)),o):n.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(i=!!t,o):i},o.clamp=function(t){return arguments.length?(r.clamp(t),o):r.clamp()},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t(r.domain(),n).round(i).clamp(r.clamp()).unknown(e)},p.apply(o,arguments),tY(o)}},scaleSequential:function(){return function t(){var e=tY(r2()(tS));return e.copy=function(){return r6(e,t())},y.apply(e,arguments)}},scaleSequentialLog:function(){return function t(){var e=tJ(r2()).domain([1,10]);return e.copy=function(){return r6(e,t()).base(e.base())},y.apply(e,arguments)}},scaleSequentialPow:function(){return r3},scaleSequentialQuantile:function(){return function t(){var e=[],r=tS;function n(t){if(null!=t&&!isNaN(t=+t))return r((L(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r=+r)||e.push(r);return e.sort(T),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e=+e)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n=+n)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e=+e))){if(e<=0||n<2)return ee(t);if(e>=1)return et(t);var n,i=(n-1)*e,o=Math.floor(i),a=et((function t(e,r,n=0,i=1/0,o){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(e.length-1,i)),!(n<=r&&r<=i))return e;for(o=void 0===o?er:function(t=T){if(t===T)return er;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(o);i>n;){if(i-n>600){let a=i-n+1,u=r-n+1,c=Math.log(a),l=.5*Math.exp(2*c/3),s=.5*Math.sqrt(c*l*(a-l)/a)*(u-a/2<0?-1:1),f=Math.max(n,Math.floor(r-u*l/a+s)),h=Math.min(i,Math.floor(r+(a-u)*l/a+s));t(e,r,f,h,o)}let a=e[r],u=n,c=i;for(en(e,n,r),o(e[i],a)>0&&en(e,n,i);u<c;){for(en(e,u,c),++u,--c;0>o(e[u],a);)++u;for(;o(e[c],a)>0;)--c}0===o(e[n],a)?en(e,n,c):en(e,++c,i),c<=r&&(n=c+1),r<=c&&(i=c-1)}return e})(t,o).subarray(0,o+1));return a+(ee(t.subarray(o+1))-a)*(i-o)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},y.apply(n,arguments)}},scaleSequentialSqrt:function(){return r5},scaleSequentialSymlog:function(){return function t(){var e=t2(r2());return e.copy=function(){return r6(e,t()).constant(e.constant())},y.apply(e,arguments)}},scaleSqrt:function(){return t9},scaleSymlog:function(){return function t(){var e=t2(t_());return e.copy=function(){return tC(e,t()).constant(e.constant())},p.apply(e,arguments)}},scaleThreshold:function(){return function t(){var e,r=[.5],n=[0,1],i=1;function o(t){return null!=t&&t<=t?n[L(r,t,0,i)]:e}return o.domain=function(t){return arguments.length?(i=Math.min((r=Array.from(t)).length,n.length-1),o):r.slice()},o.range=function(t){return arguments.length?(n=Array.from(t),i=Math.min(r.length,n.length-1),o):n.slice()},o.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t().domain(r).range(n).unknown(e)},p.apply(o,arguments)}},scaleTime:function(){return r0},scaleUtc:function(){return r1},tickFormat:function(){return tW}});var f=r(92713),h=r(41664),d=r.n(h);function p(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function y(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class v extends Map{constructor(t,e=m){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(g(this,t))}has(t){return super.has(g(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function g({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function m(t){return null!==t&&"object"==typeof t?t.valueOf():t}let b=Symbol("implicit");function w(){var t=new v,e=[],r=[],n=b;function i(i){let o=t.get(i);if(void 0===o){if(n!==b)return n;t.set(i,o=e.push(i)-1)}return r[o%r.length]}return i.domain=function(r){if(!arguments.length)return e.slice();for(let n of(e=[],t=new v,r))t.has(n)||t.set(n,e.push(n)-1);return i},i.range=function(t){return arguments.length?(r=Array.from(t),i):r.slice()},i.unknown=function(t){return arguments.length?(n=t,i):n},i.copy=function(){return w(e,r).unknown(n)},p.apply(i,arguments),i}function x(){var t,e,r=w().unknown(void 0),n=r.domain,i=r.range,o=0,a=1,u=!1,c=0,l=0,s=.5;function f(){var r=n().length,f=a<o,h=f?a:o,d=f?o:a;t=(d-h)/Math.max(1,r-c+2*l),u&&(t=Math.floor(t)),h+=(d-h-t*(r-c))*s,e=t*(1-c),u&&(h=Math.round(h),e=Math.round(e));var p=(function(t,e,r){t=+t,e=+e,r=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((e-t)/r)),o=Array(i);++n<i;)o[n]=t+n*r;return o})(r).map(function(e){return h+t*e});return i(f?p.reverse():p)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([o,a]=t,o=+o,a=+a,f()):[o,a]},r.rangeRound=function(t){return[o,a]=t,o=+o,a=+a,u=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(u=!!t,f()):u},r.padding=function(t){return arguments.length?(c=Math.min(1,l=+t),f()):c},r.paddingInner=function(t){return arguments.length?(c=Math.min(1,t),f()):c},r.paddingOuter=function(t){return arguments.length?(l=+t,f()):l},r.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},r.copy=function(){return x(n(),[o,a]).round(u).paddingInner(c).paddingOuter(l).align(s)},p.apply(f(),arguments)}function O(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(x.apply(null,arguments).paddingInner(1))}let P=Math.sqrt(50),j=Math.sqrt(10),A=Math.sqrt(2);function E(t,e,r){let n,i,o;let a=(e-t)/Math.max(0,r),u=Math.floor(Math.log10(a)),c=a/Math.pow(10,u),l=c>=P?10:c>=j?5:c>=A?2:1;return(u<0?(n=Math.round(t*(o=Math.pow(10,-u)/l)),i=Math.round(e*o),n/o<t&&++n,i/o>e&&--i,o=-o):(n=Math.round(t/(o=Math.pow(10,u)*l)),i=Math.round(e/o),n*o<t&&++n,i*o>e&&--i),i<n&&.5<=r&&r<2)?E(t,e,2*r):[n,i,o]}function S(t,e,r){if(e=+e,t=+t,!((r=+r)>0))return[];if(t===e)return[t];let n=e<t,[i,o,a]=n?E(e,t,r):E(t,e,r);if(!(o>=i))return[];let u=o-i+1,c=Array(u);if(n){if(a<0)for(let t=0;t<u;++t)c[t]=-((o-t)/a);else for(let t=0;t<u;++t)c[t]=(o-t)*a}else if(a<0)for(let t=0;t<u;++t)c[t]=-((i+t)/a);else for(let t=0;t<u;++t)c[t]=(i+t)*a;return c}function M(t,e,r){return E(t=+t,e=+e,r=+r)[2]}function k(t,e,r){e=+e,t=+t,r=+r;let n=e<t,i=n?M(e,t,r):M(t,e,r);return(n?-1:1)*(i<0?-(1/i):i)}function T(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function C(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function _(t){let e,r,n;function i(t,n,i=0,o=t.length){if(i<o){if(0!==e(n,n))return o;do{let e=i+o>>>1;0>r(t[e],n)?i=e+1:o=e}while(i<o)}return i}return 2!==t.length?(e=T,r=(e,r)=>T(t(e),r),n=(e,r)=>t(e)-r):(e=t===T||t===C?t:D,r=t,n=t),{left:i,center:function(t,e,r=0,o=t.length){let a=i(t,e,r,o-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,i=0,o=t.length){if(i<o){if(0!==e(n,n))return o;do{let e=i+o>>>1;0>=r(t[e],n)?i=e+1:o=e}while(i<o)}return i}}}function D(){return 0}function N(t){return null===t?NaN:+t}let I=_(T),L=I.right;function R(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function B(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function z(){}I.left,_(N).center;var U="\\s*([+-]?\\d+)\\s*",$="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",K="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",F=/^#([0-9a-f]{3,8})$/,W=RegExp(`^rgb\\(${U},${U},${U}\\)$`),Y=RegExp(`^rgb\\(${K},${K},${K}\\)$`),V=RegExp(`^rgba\\(${U},${U},${U},${$}\\)$`),Z=RegExp(`^rgba\\(${K},${K},${K},${$}\\)$`),q=RegExp(`^hsl\\(${$},${K},${K}\\)$`),H=RegExp(`^hsla\\(${$},${K},${K},${$}\\)$`),X={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function G(){return this.rgb().formatHex()}function Q(){return this.rgb().formatRgb()}function J(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=F.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?tt(e):3===r?new tn(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?te(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?te(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=W.exec(t))?new tn(e[1],e[2],e[3],1):(e=Y.exec(t))?new tn(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=V.exec(t))?te(e[1],e[2],e[3],e[4]):(e=Z.exec(t))?te(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=q.exec(t))?tl(e[1],e[2]/100,e[3]/100,1):(e=H.exec(t))?tl(e[1],e[2]/100,e[3]/100,e[4]):X.hasOwnProperty(t)?tt(X[t]):"transparent"===t?new tn(NaN,NaN,NaN,0):null}function tt(t){return new tn(t>>16&255,t>>8&255,255&t,1)}function te(t,e,r,n){return n<=0&&(t=e=r=NaN),new tn(t,e,r,n)}function tr(t,e,r,n){var i;return 1==arguments.length?((i=t)instanceof z||(i=J(i)),i)?new tn((i=i.rgb()).r,i.g,i.b,i.opacity):new tn:new tn(t,e,r,null==n?1:n)}function tn(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function ti(){return`#${tc(this.r)}${tc(this.g)}${tc(this.b)}`}function to(){let t=ta(this.opacity);return`${1===t?"rgb(":"rgba("}${tu(this.r)}, ${tu(this.g)}, ${tu(this.b)}${1===t?")":`, ${t})`}`}function ta(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function tu(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function tc(t){return((t=tu(t))<16?"0":"")+t.toString(16)}function tl(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new tf(t,e,r,n)}function ts(t){if(t instanceof tf)return new tf(t.h,t.s,t.l,t.opacity);if(t instanceof z||(t=J(t)),!t)return new tf;if(t instanceof tf)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,i=Math.min(e,r,n),o=Math.max(e,r,n),a=NaN,u=o-i,c=(o+i)/2;return u?(a=e===o?(r-n)/u+(r<n)*6:r===o?(n-e)/u+2:(e-r)/u+4,u/=c<.5?o+i:2-o-i,a*=60):u=c>0&&c<1?0:a,new tf(a,u,c,t.opacity)}function tf(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function th(t){return(t=(t||0)%360)<0?t+360:t}function td(t){return Math.max(0,Math.min(1,t||0))}function tp(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function ty(t,e,r,n,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*r+(1+3*t+3*o-3*a)*n+a*i)/6}R(z,J,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:G,formatHex:G,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return ts(this).formatHsl()},formatRgb:Q,toString:Q}),R(tn,tr,B(z,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new tn(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tn(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new tn(tu(this.r),tu(this.g),tu(this.b),ta(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ti,formatHex:ti,formatHex8:function(){return`#${tc(this.r)}${tc(this.g)}${tc(this.b)}${tc((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:to,toString:to})),R(tf,function(t,e,r,n){return 1==arguments.length?ts(t):new tf(t,e,r,null==n?1:n)},B(z,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new tf(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tf(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,i=2*r-n;return new tn(tp(t>=240?t-240:t+120,i,n),tp(t,i,n),tp(t<120?t+240:t-120,i,n),this.opacity)},clamp(){return new tf(th(this.h),td(this.s),td(this.l),ta(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=ta(this.opacity);return`${1===t?"hsl(":"hsla("}${th(this.h)}, ${100*td(this.s)}%, ${100*td(this.l)}%${1===t?")":`, ${t})`}`}}));var tv=t=>()=>t;function tg(t,e){var r=e-t;return r?function(e){return t+e*r}:tv(isNaN(t)?e:t)}var tm=function t(e){var r,n=1==(r=+(r=e))?tg:function(t,e){var n,i,o;return e-t?(n=t,i=e,n=Math.pow(n,o=r),i=Math.pow(i,o)-n,o=1/o,function(t){return Math.pow(n+t*i,o)}):tv(isNaN(t)?e:t)};function i(t,e){var r=n((t=tr(t)).r,(e=tr(e)).r),i=n(t.g,e.g),o=n(t.b,e.b),a=tg(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=i(e),t.b=o(e),t.opacity=a(e),t+""}}return i.gamma=t,i}(1);function tb(t){return function(e){var r,n,i=e.length,o=Array(i),a=Array(i),u=Array(i);for(r=0;r<i;++r)n=tr(e[r]),o[r]=n.r||0,a[r]=n.g||0,u[r]=n.b||0;return o=t(o),a=t(a),u=t(u),n.opacity=1,function(t){return n.r=o(t),n.g=a(t),n.b=u(t),n+""}}}function tw(t,e){return t=+t,e=+e,function(r){return t*(1-r)+e*r}}tb(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),i=t[n],o=t[n+1],a=n>0?t[n-1]:2*i-o,u=n<e-1?t[n+2]:2*o-i;return ty((r-n/e)*e,a,i,o,u)}}),tb(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),i=t[(n+e-1)%e],o=t[n%e],a=t[(n+1)%e],u=t[(n+2)%e];return ty((r-n/e)*e,i,o,a,u)}});var tx=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,tO=RegExp(tx.source,"g");function tP(t,e){var r,n,i=typeof e;return null==e||"boolean"===i?tv(e):("number"===i?tw:"string"===i?(n=J(e))?(e=n,tm):function(t,e){var r,n,i,o,a,u=tx.lastIndex=tO.lastIndex=0,c=-1,l=[],s=[];for(t+="",e+="";(i=tx.exec(t))&&(o=tO.exec(e));)(a=o.index)>u&&(a=e.slice(u,a),l[c]?l[c]+=a:l[++c]=a),(i=i[0])===(o=o[0])?l[c]?l[c]+=o:l[++c]=o:(l[++c]=null,s.push({i:c,x:tw(i,o)})),u=tO.lastIndex;return u<e.length&&(a=e.slice(u),l[c]?l[c]+=a:l[++c]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof J?tm:e instanceof Date?function(t,e){var r=new Date;return t=+t,e=+e,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,i=t?Math.min(n,t.length):0,o=Array(i),a=Array(n);for(r=0;r<i;++r)o[r]=tP(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<i;++r)a[r]=o[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},i={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=tP(t[r],e[r]):i[r]=e[r];return function(t){for(r in n)i[r]=n[r](t);return i}}:tw:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,i=e.slice();return function(o){for(r=0;r<n;++r)i[r]=t[r]*(1-o)+e[r]*o;return i}})(t,e)}function tj(t,e){return t=+t,e=+e,function(r){return Math.round(t*(1-r)+e*r)}}function tA(t){return+t}var tE=[0,1];function tS(t){return t}function tM(t,e){var r;return(e-=t=+t)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function tk(t,e,r){var n=t[0],i=t[1],o=e[0],a=e[1];return i<n?(n=tM(i,n),o=r(a,o)):(n=tM(n,i),o=r(o,a)),function(t){return o(n(t))}}function tT(t,e,r){var n=Math.min(t.length,e.length)-1,i=Array(n),o=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)i[a]=tM(t[a],t[a+1]),o[a]=r(e[a],e[a+1]);return function(e){var r=L(t,e,1,n)-1;return o[r](i[r](e))}}function tC(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function t_(){var t,e,r,n,i,o,a=tE,u=tE,c=tP,l=tS;function s(){var t,e,r,c=Math.min(a.length,u.length);return l!==tS&&(t=a[0],e=a[c-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=c>2?tT:tk,i=o=null,f}function f(e){return null==e||isNaN(e=+e)?r:(i||(i=n(a.map(t),u,c)))(t(l(e)))}return f.invert=function(r){return l(e((o||(o=n(u,a.map(t),tw)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,tA),s()):a.slice()},f.range=function(t){return arguments.length?(u=Array.from(t),s()):u.slice()},f.rangeRound=function(t){return u=Array.from(t),c=tj,s()},f.clamp=function(t){return arguments.length?(l=!!t||tS,s()):l!==tS},f.interpolate=function(t){return arguments.length?(c=t,s()):c},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function tD(){return t_()(tS,tS)}var tN=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function tI(t){var e;if(!(e=tN.exec(t)))throw Error("invalid format: "+t);return new tL({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function tL(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function tR(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function tB(t){return(t=tR(Math.abs(t)))?t[1]:NaN}function tz(t,e){var r=tR(t,e);if(!r)return t+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}tI.prototype=tL.prototype,tL.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var tU={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>tz(100*t,e),r:tz,s:function(t,e){var r=tR(t,e);if(!r)return t+"";var i=r[0],o=r[1],a=o-(n=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,u=i.length;return a===u?i:a>u?i+Array(a-u+1).join("0"):a>0?i.slice(0,a)+"."+i.slice(a):"0."+Array(1-a).join("0")+tR(t,Math.max(0,e+a-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function t$(t){return t}var tK=Array.prototype.map,tF=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function tW(t,e,r,n){var i,u,c=k(t,e,r);switch((n=tI(null==n?",f":n)).type){case"s":var l=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(u=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(tB(l)/3)))-tB(Math.abs(c))))||(n.precision=u),a(n,l);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(u=Math.max(0,tB(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(i=Math.abs(i=c)))-tB(i))+1)||(n.precision=u-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(u=Math.max(0,-tB(Math.abs(c))))||(n.precision=u-("%"===n.type)*2)}return o(n)}function tY(t){var e=t.domain;return t.ticks=function(t){var r=e();return S(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return tW(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,i,o=e(),a=0,u=o.length-1,c=o[a],l=o[u],s=10;for(l<c&&(i=c,c=l,l=i,i=a,a=u,u=i);s-- >0;){if((i=M(c,l,r))===n)return o[a]=c,o[u]=l,e(o);if(i>0)c=Math.floor(c/i)*i,l=Math.ceil(l/i)*i;else if(i<0)c=Math.ceil(c*i)/i,l=Math.floor(l*i)/i;else break;n=i}return t},t}function tV(t,e){t=t.slice();var r,n=0,i=t.length-1,o=t[n],a=t[i];return a<o&&(r=n,n=i,i=r,r=o,o=a,a=r),t[n]=e.floor(o),t[i]=e.ceil(a),t}function tZ(t){return Math.log(t)}function tq(t){return Math.exp(t)}function tH(t){return-Math.log(-t)}function tX(t){return-Math.exp(-t)}function tG(t){return isFinite(t)?+("1e"+t):t<0?0:t}function tQ(t){return(e,r)=>-t(-e,r)}function tJ(t){let e,r;let n=t(tZ,tq),i=n.domain,a=10;function u(){var o,u;return e=(o=a)===Math.E?Math.log:10===o&&Math.log10||2===o&&Math.log2||(o=Math.log(o),t=>Math.log(t)/o),r=10===(u=a)?tG:u===Math.E?Math.exp:t=>Math.pow(u,t),i()[0]<0?(e=tQ(e),r=tQ(r),t(tH,tX)):t(tZ,tq),n}return n.base=function(t){return arguments.length?(a=+t,u()):a},n.domain=function(t){return arguments.length?(i(t),u()):i()},n.ticks=t=>{let n,o;let u=i(),c=u[0],l=u[u.length-1],s=l<c;s&&([c,l]=[l,c]);let f=e(c),h=e(l),d=null==t?10:+t,p=[];if(!(a%1)&&h-f<d){if(f=Math.floor(f),h=Math.ceil(h),c>0){for(;f<=h;++f)for(n=1;n<a;++n)if(!((o=f<0?n/r(-f):n*r(f))<c)){if(o>l)break;p.push(o)}}else for(;f<=h;++f)for(n=a-1;n>=1;--n)if(!((o=f>0?n/r(-f):n*r(f))<c)){if(o>l)break;p.push(o)}2*p.length<d&&(p=S(c,l,d))}else p=S(f,h,Math.min(h-f,d)).map(r);return s?p.reverse():p},n.tickFormat=(t,i)=>{if(null==t&&(t=10),null==i&&(i=10===a?"s":","),"function"!=typeof i&&(a%1||null!=(i=tI(i)).precision||(i.trim=!0),i=o(i)),t===1/0)return i;let u=Math.max(1,a*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*a<a-.5&&(n*=a),n<=u?i(t):""}},n.nice=()=>i(tV(i(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function t0(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function t1(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function t2(t){var e=1,r=t(t0(1),t1(e));return r.constant=function(r){return arguments.length?t(t0(e=+r),t1(e)):e},tY(r)}function t6(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function t3(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function t5(t){return t<0?-t*t:t*t}function t4(t){var e=t(tS,tS),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(tS,tS):.5===r?t(t3,t5):t(t6(r),t6(1/r)):r},tY(e)}function t8(){var t=t4(t_());return t.copy=function(){return tC(t,t8()).exponent(t.exponent())},p.apply(t,arguments),t}function t9(){return t8.apply(null,arguments).exponent(.5)}function t7(t){return Math.sign(t)*t*t}function et(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function ee(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function er(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}function en(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}o=(i=function(t){var e,r,i,o=void 0===t.grouping||void 0===t.thousands?t$:(e=tK.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var i=t.length,o=[],a=0,u=e[0],c=0;i>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),o.push(t.substring(i-=u,i+u)),!((c+=u+1)>n));)u=e[a=(a+1)%e.length];return o.reverse().join(r)}),a=void 0===t.currency?"":t.currency[0]+"",u=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",l=void 0===t.numerals?t$:(i=tK.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return i[+t]})}),s=void 0===t.percent?"%":t.percent+"",f=void 0===t.minus?"−":t.minus+"",h=void 0===t.nan?"NaN":t.nan+"";function d(t){var e=(t=tI(t)).fill,r=t.align,i=t.sign,d=t.symbol,p=t.zero,y=t.width,v=t.comma,g=t.precision,m=t.trim,b=t.type;"n"===b?(v=!0,b="g"):tU[b]||(void 0===g&&(g=12),m=!0,b="g"),(p||"0"===e&&"="===r)&&(p=!0,e="0",r="=");var w="$"===d?a:"#"===d&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===d?u:/[%p]/.test(b)?s:"",O=tU[b],P=/[defgprs%]/.test(b);function j(t){var a,u,s,d=w,j=x;if("c"===b)j=O(t)+j,t="";else{var A=(t=+t)<0||1/t<0;if(t=isNaN(t)?h:O(Math.abs(t),g),m&&(t=function(t){t:for(var e,r=t.length,n=1,i=-1;n<r;++n)switch(t[n]){case".":i=e=n;break;case"0":0===i&&(i=n),e=n;break;default:if(!+t[n])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(e+1):t}(t)),A&&0==+t&&"+"!==i&&(A=!1),d=(A?"("===i?i:f:"-"===i||"("===i?"":i)+d,j=("s"===b?tF[8+n/3]:"")+j+(A&&"("===i?")":""),P){for(a=-1,u=t.length;++a<u;)if(48>(s=t.charCodeAt(a))||s>57){j=(46===s?c+t.slice(a+1):t.slice(a))+j,t=t.slice(0,a);break}}}v&&!p&&(t=o(t,1/0));var E=d.length+t.length+j.length,S=E<y?Array(y-E+1).join(e):"";switch(v&&p&&(t=o(S+t,S.length?y-j.length:1/0),S=""),r){case"<":t=d+t+j+S;break;case"=":t=d+S+t+j;break;case"^":t=S.slice(0,E=S.length>>1)+d+t+j+S.slice(E);break;default:t=S+d+t+j}return l(t)}return g=void 0===g?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,g)):Math.max(0,Math.min(20,g)),j.toString=function(){return t+""},j}return{format:d,formatPrefix:function(t,e){var r=d(((t=tI(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(tB(e)/3))),i=Math.pow(10,-n),o=tF[8+n/3];return function(t){return r(i*t)+o}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,a=i.formatPrefix;let ei=new Date,eo=new Date;function ea(t,e,r,n){function i(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return i.floor=e=>(t(e=new Date(+e)),e),i.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),i.round=t=>{let e=i(t),r=i.ceil(t);return t-e<r-t?e:r},i.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),i.range=(r,n,o)=>{let a;let u=[];if(r=i.ceil(r),o=null==o?1:Math.floor(o),!(r<n)||!(o>0))return u;do u.push(a=new Date(+r)),e(r,o),t(r);while(a<r&&r<n);return u},i.filter=r=>ea(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t){if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}}),r&&(i.count=(e,n)=>(ei.setTime(+e),eo.setTime(+n),t(ei),t(eo),Math.floor(r(ei,eo))),i.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?i.filter(n?e=>n(e)%t==0:e=>i.count(0,e)%t==0):i:null),i}let eu=ea(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);eu.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?ea(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):eu:null,eu.range;let ec=ea(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());ec.range;let el=ea(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());el.range;let es=ea(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());es.range;let ef=ea(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());ef.range;let eh=ea(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());eh.range;let ed=ea(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);ed.range;let ep=ea(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);ep.range;let ey=ea(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function ev(t){return ea(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}ey.range;let eg=ev(0),em=ev(1),eb=ev(2),ew=ev(3),ex=ev(4),eO=ev(5),eP=ev(6);function ej(t){return ea(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}eg.range,em.range,eb.range,ew.range,ex.range,eO.range,eP.range;let eA=ej(0),eE=ej(1),eS=ej(2),eM=ej(3),ek=ej(4),eT=ej(5),eC=ej(6);eA.range,eE.range,eS.range,eM.range,ek.range,eT.range,eC.range;let e_=ea(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());e_.range;let eD=ea(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());eD.range;let eN=ea(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());eN.every=t=>isFinite(t=Math.floor(t))&&t>0?ea(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,eN.range;let eI=ea(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function eL(t,e,r,n,i,o){let a=[[ec,1,1e3],[ec,5,5e3],[ec,15,15e3],[ec,30,3e4],[o,1,6e4],[o,5,3e5],[o,15,9e5],[o,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function u(e,r,n){let i=Math.abs(r-e)/n,o=_(([,,t])=>t).right(a,i);if(o===a.length)return t.every(k(e/31536e6,r/31536e6,n));if(0===o)return eu.every(Math.max(k(e,r,n),1));let[u,c]=a[i/a[o-1][2]<a[o][2]/i?o-1:o];return u.every(c)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let i=r&&"function"==typeof r.range?r:u(t,e,r),o=i?i.range(t,+e+1):[];return n?o.reverse():o},u]}eI.every=t=>isFinite(t=Math.floor(t))&&t>0?ea(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,eI.range;let[eR,eB]=eL(eI,eD,eA,ey,eh,es),[ez,eU]=eL(eN,e_,eg,ed,ef,el);function e$(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function eK(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function eF(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var eW={"-":"",_:" ",0:"0"},eY=/^\s*\d+/,eV=/^%/,eZ=/[\\^$*+?|[\]().{}]/g;function eq(t,e,r){var n=t<0?"-":"",i=(n?-t:t)+"",o=i.length;return n+(o<r?Array(r-o+1).join(e)+i:i)}function eH(t){return t.replace(eZ,"\\$&")}function eX(t){return RegExp("^(?:"+t.map(eH).join("|")+")","i")}function eG(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function eQ(t,e,r){var n=eY.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function eJ(t,e,r){var n=eY.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function e0(t,e,r){var n=eY.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function e1(t,e,r){var n=eY.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function e2(t,e,r){var n=eY.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function e6(t,e,r){var n=eY.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function e3(t,e,r){var n=eY.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function e5(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function e4(t,e,r){var n=eY.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function e8(t,e,r){var n=eY.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function e9(t,e,r){var n=eY.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function e7(t,e,r){var n=eY.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function rt(t,e,r){var n=eY.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function re(t,e,r){var n=eY.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function rr(t,e,r){var n=eY.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function rn(t,e,r){var n=eY.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function ri(t,e,r){var n=eY.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function ro(t,e,r){var n=eV.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function ra(t,e,r){var n=eY.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function ru(t,e,r){var n=eY.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function rc(t,e){return eq(t.getDate(),e,2)}function rl(t,e){return eq(t.getHours(),e,2)}function rs(t,e){return eq(t.getHours()%12||12,e,2)}function rf(t,e){return eq(1+ed.count(eN(t),t),e,3)}function rh(t,e){return eq(t.getMilliseconds(),e,3)}function rd(t,e){return rh(t,e)+"000"}function rp(t,e){return eq(t.getMonth()+1,e,2)}function ry(t,e){return eq(t.getMinutes(),e,2)}function rv(t,e){return eq(t.getSeconds(),e,2)}function rg(t){var e=t.getDay();return 0===e?7:e}function rm(t,e){return eq(eg.count(eN(t)-1,t),e,2)}function rb(t){var e=t.getDay();return e>=4||0===e?ex(t):ex.ceil(t)}function rw(t,e){return t=rb(t),eq(ex.count(eN(t),t)+(4===eN(t).getDay()),e,2)}function rx(t){return t.getDay()}function rO(t,e){return eq(em.count(eN(t)-1,t),e,2)}function rP(t,e){return eq(t.getFullYear()%100,e,2)}function rj(t,e){return eq((t=rb(t)).getFullYear()%100,e,2)}function rA(t,e){return eq(t.getFullYear()%1e4,e,4)}function rE(t,e){var r=t.getDay();return eq((t=r>=4||0===r?ex(t):ex.ceil(t)).getFullYear()%1e4,e,4)}function rS(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+eq(e/60|0,"0",2)+eq(e%60,"0",2)}function rM(t,e){return eq(t.getUTCDate(),e,2)}function rk(t,e){return eq(t.getUTCHours(),e,2)}function rT(t,e){return eq(t.getUTCHours()%12||12,e,2)}function rC(t,e){return eq(1+ep.count(eI(t),t),e,3)}function r_(t,e){return eq(t.getUTCMilliseconds(),e,3)}function rD(t,e){return r_(t,e)+"000"}function rN(t,e){return eq(t.getUTCMonth()+1,e,2)}function rI(t,e){return eq(t.getUTCMinutes(),e,2)}function rL(t,e){return eq(t.getUTCSeconds(),e,2)}function rR(t){var e=t.getUTCDay();return 0===e?7:e}function rB(t,e){return eq(eA.count(eI(t)-1,t),e,2)}function rz(t){var e=t.getUTCDay();return e>=4||0===e?ek(t):ek.ceil(t)}function rU(t,e){return t=rz(t),eq(ek.count(eI(t),t)+(4===eI(t).getUTCDay()),e,2)}function r$(t){return t.getUTCDay()}function rK(t,e){return eq(eE.count(eI(t)-1,t),e,2)}function rF(t,e){return eq(t.getUTCFullYear()%100,e,2)}function rW(t,e){return eq((t=rz(t)).getUTCFullYear()%100,e,2)}function rY(t,e){return eq(t.getUTCFullYear()%1e4,e,4)}function rV(t,e){var r=t.getUTCDay();return eq((t=r>=4||0===r?ek(t):ek.ceil(t)).getUTCFullYear()%1e4,e,4)}function rZ(){return"+0000"}function rq(){return"%"}function rH(t){return+t}function rX(t){return Math.floor(+t/1e3)}function rG(t){return new Date(t)}function rQ(t){return t instanceof Date?+t:+new Date(+t)}function rJ(t,e,r,n,i,o,a,u,c,l){var s=tD(),f=s.invert,h=s.domain,d=l(".%L"),p=l(":%S"),y=l("%I:%M"),v=l("%I %p"),g=l("%a %d"),m=l("%b %d"),b=l("%B"),w=l("%Y");function x(t){return(c(t)<t?d:u(t)<t?p:a(t)<t?y:o(t)<t?v:n(t)<t?i(t)<t?g:m:r(t)<t?b:w)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?h(Array.from(t,rQ)):h().map(rG)},s.ticks=function(e){var r=h();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?x:l(e)},s.nice=function(t){var r=h();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?h(tV(r,t)):s},s.copy=function(){return tC(s,rJ(t,e,r,n,i,o,a,u,c,l))},s}function r0(){return p.apply(rJ(ez,eU,eN,e_,eg,ed,ef,el,ec,c).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function r1(){return p.apply(rJ(eR,eB,eI,eD,eA,ep,eh,es,ec,l).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function r2(){var t,e,r,n,i,o=0,a=1,u=tS,c=!1;function l(e){return null==e||isNaN(e=+e)?i:u(0===r?.5:(e=(n(e)-t)*r,c?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,u=t(r,n),l):[u(0),u(1)]}}return l.domain=function(i){return arguments.length?([o,a]=i,t=n(o=+o),e=n(a=+a),r=t===e?0:1/(e-t),l):[o,a]},l.clamp=function(t){return arguments.length?(c=!!t,l):c},l.interpolator=function(t){return arguments.length?(u=t,l):u},l.range=s(tP),l.rangeRound=s(tj),l.unknown=function(t){return arguments.length?(i=t,l):i},function(i){return n=i,t=i(o),e=i(a),r=t===e?0:1/(e-t),l}}function r6(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function r3(){var t=t4(r2());return t.copy=function(){return r6(t,r3()).exponent(t.exponent())},y.apply(t,arguments)}function r5(){return r3.apply(null,arguments).exponent(.5)}function r4(){var t,e,r,n,i,o,a,u=0,c=.5,l=1,s=1,f=tS,h=!1;function d(t){return isNaN(t=+t)?a:(t=.5+((t=+o(t))-e)*(s*t<s*e?n:i),f(h?Math.max(0,Math.min(1,t)):t))}function p(t){return function(e){var r,n,i;return arguments.length?([r,n,i]=e,f=function(t,e){void 0===e&&(e=t,t=tP);for(var r=0,n=e.length-1,i=e[0],o=Array(n<0?0:n);r<n;)o[r]=t(i,i=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return o[e](t-e)}}(t,[r,n,i]),d):[f(0),f(.5),f(1)]}}return d.domain=function(a){return arguments.length?([u,c,l]=a,t=o(u=+u),e=o(c=+c),r=o(l=+l),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,d):[u,c,l]},d.clamp=function(t){return arguments.length?(h=!!t,d):h},d.interpolator=function(t){return arguments.length?(f=t,d):f},d.range=p(tP),d.rangeRound=p(tj),d.unknown=function(t){return arguments.length?(a=t,d):a},function(a){return o=a,t=a(u),e=a(c),r=a(l),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,d}}function r8(){var t=t4(r4());return t.copy=function(){return r6(t,r8()).exponent(t.exponent())},y.apply(t,arguments)}function r9(){return r8.apply(null,arguments).exponent(.5)}c=(u=function(t){var e=t.dateTime,r=t.date,n=t.time,i=t.periods,o=t.days,a=t.shortDays,u=t.months,c=t.shortMonths,l=eX(i),s=eG(i),f=eX(o),h=eG(o),d=eX(a),p=eG(a),y=eX(u),v=eG(u),g=eX(c),m=eG(c),b={a:function(t){return a[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return c[t.getMonth()]},B:function(t){return u[t.getMonth()]},c:null,d:rc,e:rc,f:rd,g:rj,G:rE,H:rl,I:rs,j:rf,L:rh,m:rp,M:ry,p:function(t){return i[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:rH,s:rX,S:rv,u:rg,U:rm,V:rw,w:rx,W:rO,x:null,X:null,y:rP,Y:rA,Z:rS,"%":rq},w={a:function(t){return a[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return c[t.getUTCMonth()]},B:function(t){return u[t.getUTCMonth()]},c:null,d:rM,e:rM,f:rD,g:rW,G:rV,H:rk,I:rT,j:rC,L:r_,m:rN,M:rI,p:function(t){return i[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:rH,s:rX,S:rL,u:rR,U:rB,V:rU,w:r$,W:rK,x:null,X:null,y:rF,Y:rY,Z:rZ,"%":rq},x={a:function(t,e,r){var n=d.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=g.exec(e.slice(r));return n?(t.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return j(t,e,r,n)},d:e9,e:e9,f:ri,g:e3,G:e6,H:rt,I:rt,j:e7,L:rn,m:e8,M:re,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:e4,Q:ra,s:ru,S:rr,u:eJ,U:e0,V:e1,w:eQ,W:e2,x:function(t,e,n){return j(t,r,e,n)},X:function(t,e,r){return j(t,n,e,r)},y:e3,Y:e6,Z:e5,"%":ro};function O(t,e){return function(r){var n,i,o,a=[],u=-1,c=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++u<l;)37===t.charCodeAt(u)&&(a.push(t.slice(c,u)),null!=(i=eW[n=t.charAt(++u)])?n=t.charAt(++u):i="e"===n?" ":"0",(o=e[n])&&(n=o(r,i)),a.push(n),c=u+1);return a.push(t.slice(c,u)),a.join("")}}function P(t,e){return function(r){var n,i,o=eF(1900,void 0,1);if(j(o,t,r+="",0)!=r.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(!e||"Z"in o||(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(n=(i=(n=eK(eF(o.y,0,1))).getUTCDay())>4||0===i?eE.ceil(n):eE(n),n=ep.offset(n,(o.V-1)*7),o.y=n.getUTCFullYear(),o.m=n.getUTCMonth(),o.d=n.getUTCDate()+(o.w+6)%7):(n=(i=(n=e$(eF(o.y,0,1))).getDay())>4||0===i?em.ceil(n):em(n),n=ed.offset(n,(o.V-1)*7),o.y=n.getFullYear(),o.m=n.getMonth(),o.d=n.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:"W"in o?1:0),i="Z"in o?eK(eF(o.y,0,1)).getUTCDay():e$(eF(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,eK(o)):e$(o)}}function j(t,e,r,n){for(var i,o,a=0,u=e.length,c=r.length;a<u;){if(n>=c)return -1;if(37===(i=e.charCodeAt(a++))){if(!(o=x[(i=e.charAt(a++))in eW?e.charAt(a++):i])||(n=o(t,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(e,b),w.x=O(r,w),w.X=O(n,w),w.c=O(e,w),{format:function(t){var e=O(t+="",b);return e.toString=function(){return t},e},parse:function(t){var e=P(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",w);return e.toString=function(){return t},e},utcParse:function(t){var e=P(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,u.parse,l=u.utcFormat,u.utcParse;var r7=r(35953),nt=r(49037),ne=r(22932),nr=r(16630),nn=r(66395);function ni(t){if(Array.isArray(t)&&2===t.length){var[e,r]=t;if((0,nn.n)(e)&&(0,nn.n)(r))return!0}return!1}function no(t,e,r){return r?t:[Math.min(t[0],e[0]),Math.max(t[1],e[1])]}var na=r(61134),nu=r.n(na),nc=t=>t,nl={},ns=t=>t===nl,nf=t=>function e(){return 0==arguments.length||1==arguments.length&&ns(arguments.length<=0?void 0:arguments[0])?e:t(...arguments)},nh=(t,e)=>1===t?e:nf(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var o=n.filter(t=>t!==nl).length;return o>=t?e(...n):nh(t-o,nf(function(){for(var t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];return e(...n.map(t=>ns(t)?r.shift():t),...r)}))}),nd=t=>nh(t.length,t),np=(t,e)=>{for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},ny=nd((t,e)=>Array.isArray(e)?e.map(t):Object.keys(e).map(t=>e[t]).map(t)),nv=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return nc;var n=e.reverse(),i=n[0],o=n.slice(1);return function(){return o.reduce((t,e)=>e(t),i(...arguments))}},ng=t=>Array.isArray(t)?t.reverse():t.split("").reverse().join(""),nm=t=>{var e=null,r=null;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return e&&i.every((t,r)=>{var n;return t===(null===(n=e)||void 0===n?void 0:n[r])})?r:(e=i,r=t(...i))}};function nb(t){return 0===t?1:Math.floor(new(nu())(t).abs().log(10).toNumber())+1}function nw(t,e,r){for(var n=new(nu())(t),i=0,o=[];n.lt(e)&&i<1e5;)o.push(n.toNumber()),n=n.add(r),i++;return o}nd((t,e,r)=>{var n=+t;return n+r*(+e-n)}),nd((t,e,r)=>{var n=e-+t;return(r-t)/(n=n||1/0)}),nd((t,e,r)=>{var n=e-+t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});var nx=t=>{var[e,r]=t,[n,i]=[e,r];return e>r&&([n,i]=[r,e]),[n,i]},nO=(t,e,r)=>{if(t.lte(0))return new(nu())(0);var n=nb(t.toNumber()),i=new(nu())(10).pow(n),o=t.div(i),a=1!==n?.05:.1,u=new(nu())(Math.ceil(o.div(a).toNumber())).add(r).mul(a).mul(i);return new(nu())(e?u.toNumber():Math.ceil(u.toNumber()))},nP=(t,e,r)=>{var n=new(nu())(1),i=new(nu())(t);if(!i.isint()&&r){var o=Math.abs(t);o<1?(n=new(nu())(10).pow(nb(t)-1),i=new(nu())(Math.floor(i.div(n).toNumber())).mul(n)):o>1&&(i=new(nu())(Math.floor(t)))}else 0===t?i=new(nu())(Math.floor((e-1)/2)):r||(i=new(nu())(Math.floor(t)));var a=Math.floor((e-1)/2);return nv(ny(t=>i.add(new(nu())(t-a).mul(n)).toNumber()),np)(0,e)},nj=function(t,e,r,n){var i,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((e-t)/(r-1)))return{step:new(nu())(0),tickMin:new(nu())(0),tickMax:new(nu())(0)};var a=nO(new(nu())(e).sub(t).div(r-1),n,o),u=Math.ceil((i=t<=0&&e>=0?new(nu())(0):(i=new(nu())(t).add(e).div(2)).sub(new(nu())(i).mod(a))).sub(t).div(a).toNumber()),c=Math.ceil(new(nu())(e).sub(i).div(a).toNumber()),l=u+c+1;return l>r?nj(t,e,r,n,o+1):(l<r&&(c=e>0?c+(r-l):c,u=e>0?u:u+(r-l)),{step:a,tickMin:i.sub(new(nu())(u).mul(a)),tickMax:i.add(new(nu())(c).mul(a))})},nA=nm(function(t){var[e,r]=t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],o=Math.max(n,2),[a,u]=nx([e,r]);if(a===-1/0||u===1/0){var c=u===1/0?[a,...np(0,n-1).map(()=>1/0)]:[...np(0,n-1).map(()=>-1/0),u];return e>r?ng(c):c}if(a===u)return nP(a,n,i);var{step:l,tickMin:s,tickMax:f}=nj(a,u,o,i,0),h=nw(s,f.add(new(nu())(.1).mul(l)),l);return e>r?ng(h):h}),nE=nm(function(t,e){var[r,n]=t,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[o,a]=nx([r,n]);if(o===-1/0||a===1/0)return[r,n];if(o===a)return[o];var u=nO(new(nu())(a).sub(o).div(Math.max(e,2)-1),i,0),c=[...nw(new(nu())(o),new(nu())(a),u),a];return!1===i&&(c=c.map(t=>Math.round(t))),r>n?ng(c):c}),nS=r(60152),nM=r(32498),nk=r(69729),nT=r(2431),nC=r(33968),n_=r(80796),nD=r(40304),nN=r(56462),nI=r(87367),nL=r(78487);function nR(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nB(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nR(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nR(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var nz=[0,"auto"],nU={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},n$=(t,e)=>{var r=t.cartesianAxis.xAxis[e];return null==r?nU:r},nK={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:nz,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:nL.n9},nF=(t,e)=>{var r=t.cartesianAxis.yAxis[e];return null==r?nK:r},nW={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},nY=(t,e)=>{var r=t.cartesianAxis.zAxis[e];return null==r?nW:r},nV=(t,e,r)=>{switch(e){case"xAxis":return n$(t,r);case"yAxis":return nF(t,r);case"zAxis":return nY(t,r);case"angleAxis":return(0,n_.dc)(t,r);case"radiusAxis":return(0,n_.Au)(t,r);default:throw Error("Unexpected axis type: ".concat(e))}},nZ=(t,e,r)=>{switch(e){case"xAxis":return n$(t,r);case"yAxis":return nF(t,r);case"angleAxis":return(0,n_.dc)(t,r);case"radiusAxis":return(0,n_.Au)(t,r);default:throw Error("Unexpected axis type: ".concat(e))}},nq=t=>t.graphicalItems.countOfBars>0;function nH(t,e){return r=>{switch(t){case"xAxis":return"xAxisId"in r&&r.xAxisId===e;case"yAxis":return"yAxisId"in r&&r.yAxisId===e;case"zAxis":return"zAxisId"in r&&r.zAxisId===e;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===e;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===e;default:return!1}}}var nX=t=>t.graphicalItems.cartesianItems,nG=(0,f.P1)([nD.z,nN.l],nH),nQ=(t,e,r)=>t.filter(r).filter(t=>(null==e?void 0:e.includeHidden)===!0||!t.hide),nJ=(0,f.P1)([nX,nV,nG],nQ),n0=t=>t.filter(t=>void 0===t.stackId),n1=(0,f.P1)([nJ],n0),n2=t=>t.map(t=>t.data).filter(Boolean).flat(1),n6=(0,f.P1)([nJ],n2),n3=(t,e)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=e;return t.length>0?t:r.slice(n,i+1)},n5=(0,f.P1)([n6,ne.hA],n3),n4=(t,e,r)=>(null==e?void 0:e.dataKey)!=null?t.map(t=>({value:(0,nt.F$)(t,e.dataKey)})):r.length>0?r.map(t=>t.dataKey).flatMap(e=>t.map(t=>({value:(0,nt.F$)(t,e)}))):t.map(t=>({value:t})),n8=(0,f.P1)([n5,nV,nJ],n4);function n9(t,e){switch(t){case"xAxis":return"x"===e.direction;case"yAxis":return"y"===e.direction;default:return!1}}function n7(t){return t.filter(t=>(0,nr.P2)(t)||t instanceof Date).map(Number).filter(t=>!1===(0,nr.In)(t))}var it=(t,e,r)=>Object.fromEntries(Object.entries(e.reduce((t,e)=>(null==e.stackId||(null==t[e.stackId]&&(t[e.stackId]=[]),t[e.stackId].push(e)),t),{})).map(e=>{var[n,i]=e,o=i.map(t=>t.dataKey);return[n,{stackedData:(0,nt.uX)(t,o,r),graphicalItems:i}]})),ie=(0,f.P1)([n5,nJ,nC.Qw],it),ir=(t,e,r)=>{var{dataStartIndex:n,dataEndIndex:i}=e;if("zAxis"!==r){var o=(0,nt.EB)(t,n,i);if(null==o||0!==o[0]||0!==o[1])return o}},ii=(0,f.P1)([ie,ne.iP,nD.z],ir),io=(t,e,r,n)=>r.length>0?t.flatMap(t=>r.flatMap(r=>{var i,o,a=null===(i=r.errorBars)||void 0===i?void 0:i.filter(t=>n9(n,t)),u=(0,nt.F$)(t,null!==(o=e.dataKey)&&void 0!==o?o:r.dataKey);return{value:u,errorDomain:!(!a||"number"!=typeof u||(0,nr.In)(u))&&a.length?n7(a.flatMap(e=>{var r,n,i=(0,nt.F$)(t,e.dataKey);if(Array.isArray(i)?[r,n]=i:r=n=i,(0,nn.n)(r)&&(0,nn.n)(n))return[u-r,u+n]})):[]}})).filter(Boolean):(null==e?void 0:e.dataKey)!=null?t.map(t=>({value:(0,nt.F$)(t,e.dataKey),errorDomain:[]})):t.map(t=>({value:t,errorDomain:[]})),ia=(0,f.P1)(n5,nV,n1,nD.z,io);function iu(t){var{value:e}=t;if((0,nr.P2)(e)||e instanceof Date)return e}var ic=t=>{var e=n7(t.flatMap(t=>[t.value,t.errorDomain]).flat(1));if(0!==e.length)return[Math.min(...e),Math.max(...e)]},il=(t,e,r)=>{var n=t.map(iu).filter(t=>null!=t);return r&&(null==e.dataKey||e.allowDuplicatedCategory&&(0,nr.bv)(n))?d()(0,t.length):e.allowDuplicatedCategory?n:Array.from(new Set(n))},is=t=>{var e;if(null==t||!("domain"in t))return nz;if(null!=t.domain)return t.domain;if(null!=t.ticks){if("number"===t.type){var r=n7(t.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===t.type)return t.ticks.map(String)}return null!==(e=null==t?void 0:t.domain)&&void 0!==e?e:nz},ih=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},id=t=>t.referenceElements.dots,ip=(t,e,r)=>t.filter(t=>"extendDomain"===t.ifOverflow).filter(t=>"xAxis"===e?t.xAxisId===r:t.yAxisId===r),iy=(0,f.P1)([id,nD.z,nN.l],ip),iv=t=>t.referenceElements.areas,ig=(0,f.P1)([iv,nD.z,nN.l],ip),im=t=>t.referenceElements.lines,ib=(0,f.P1)([im,nD.z,nN.l],ip),iw=(t,e)=>{var r=n7(t.map(t=>"xAxis"===e?t.x:t.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ix=(0,f.P1)(iy,nD.z,iw),iO=(t,e)=>{var r=n7(t.flatMap(t=>["xAxis"===e?t.x1:t.y1,"xAxis"===e?t.x2:t.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iP=(0,f.P1)([ig,nD.z],iO),ij=(t,e)=>{var r=n7(t.map(t=>"xAxis"===e?t.x:t.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iA=(0,f.P1)(ib,nD.z,ij),iE=(0,f.P1)(ix,iA,iP,(t,e,r)=>ih(t,r,e)),iS=(0,f.P1)([nV],is),iM=(t,e,r,n,i)=>{var o=function(t,e){if(e&&"function"!=typeof t&&Array.isArray(t)&&2===t.length){var r,n,[i,o]=t;if((0,nn.n)(i))r=i;else if("function"==typeof i)return;if((0,nn.n)(o))n=o;else if("function"==typeof o)return;var a=[r,n];if(ni(a))return a}}(e,t.allowDataOverflow);return null!=o?o:function(t,e,r){if(r||null!=e){if("function"==typeof t&&null!=e)try{var n=t(e,r);if(ni(n))return no(n,e,r)}catch(t){}if(Array.isArray(t)&&2===t.length){var i,o,[a,u]=t;if("auto"===a)null!=e&&(i=Math.min(...e));else if((0,nr.hj)(a))i=a;else if("function"==typeof a)try{null!=e&&(i=a(null==e?void 0:e[0]))}catch(t){}else if("string"==typeof a&&nt.rI.test(a)){var c=nt.rI.exec(a);if(null==c||null==e)i=void 0;else{var l=+c[1];i=e[0]-l}}else i=null==e?void 0:e[0];if("auto"===u)null!=e&&(o=Math.max(...e));else if((0,nr.hj)(u))o=u;else if("function"==typeof u)try{null!=e&&(o=u(null==e?void 0:e[1]))}catch(t){}else if("string"==typeof u&&nt.Ji.test(u)){var s=nt.Ji.exec(u);if(null==s||null==e)o=void 0;else{var f=+s[1];o=e[1]+f}}else o=null==e?void 0:e[1];var h=[i,o];if(ni(h))return null==e?h:no(h,e,r)}}}(e,ih(r,i,ic(n)),t.allowDataOverflow)},ik=(0,f.P1)([nV,iS,ii,ia,iE],iM),iT=[0,1],iC=(t,e,r,n,i,o,a)=>{if(null!=t&&null!=r&&0!==r.length){var{dataKey:u,type:c}=t,l=(0,nt.NA)(e,o);return l&&null==u?d()(0,r.length):"category"===c?il(n,t,l):"expand"===i?iT:a}},i_=(0,f.P1)([nV,r7.rE,n5,n8,nC.Qw,nD.z,ik],iC),iD=(t,e,r,n,i)=>{if(null!=t){var{scale:o,type:a}=t;if("auto"===o)return"radial"===e&&"radiusAxis"===i?"band":"radial"===e&&"angleAxis"===i?"linear":"category"===a&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":"category"===a?"band":"linear";if("string"==typeof o){var u="scale".concat((0,nr.jC)(o));return u in s?u:"point"}}},iN=(0,f.P1)([nV,r7.rE,nq,nC.E2,nD.z],iD);function iI(t,e,r,n){if(null!=r&&null!=n){if("function"==typeof t.scale)return t.scale.copy().domain(r).range(n);var i=function(t){if(null!=t){if(t in s)return s[t]();var e="scale".concat((0,nr.jC)(t));if(e in s)return s[e]()}}(e);if(null!=i){var o=i.domain(r).range(n);return(0,nt.zF)(o),o}}}var iL=(t,e,r)=>{var n=is(e);return"auto"!==r&&"linear"!==r?void 0:null!=e&&e.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&ni(t)?nA(t,e.tickCount,e.allowDecimals):null!=e&&e.tickCount&&"number"===e.type&&ni(t)?nE(t,e.tickCount,e.allowDecimals):void 0},iR=(0,f.P1)([i_,nZ,iN],iL),iB=(t,e,r,n)=>"angleAxis"!==n&&(null==t?void 0:t.type)==="number"&&ni(e)&&Array.isArray(r)&&r.length>0?[Math.min(e[0],r[0]),Math.max(e[1],r[r.length-1])]:e,iz=(0,f.P1)([nV,i_,iR,nD.z],iB),iU=(0,f.P1)(n8,nV,(t,e)=>{if(e&&"number"===e.type){var r=1/0,n=Array.from(n7(t.map(t=>t.value))).sort((t,e)=>t-e);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var o=0;o<n.length-1;o++)r=Math.min(r,n[o+1]-n[o]);return r/i}}),i$=(0,f.P1)(iU,r7.rE,nC.sd,nk.DX,(t,e,r,n)=>n,(t,e,r,n,i)=>{if(!(0,nn.n)(t))return 0;var o="vertical"===e?n.height:n.width;if("gap"===i)return t*o/2;if("no-gap"===i){var a=(0,nr.h1)(r,t*o),u=t*o/2;return u-a-(u-a)/o*a}return 0}),iK=(0,f.P1)(n$,(t,e)=>{var r=n$(t,e);return null==r||"string"!=typeof r.padding?0:i$(t,"xAxis",e,r.padding)},(t,e)=>{if(null==t)return{left:0,right:0};var r,n,{padding:i}=t;return"string"==typeof i?{left:e,right:e}:{left:(null!==(r=i.left)&&void 0!==r?r:0)+e,right:(null!==(n=i.right)&&void 0!==n?n:0)+e}}),iF=(0,f.P1)(nF,(t,e)=>{var r=nF(t,e);return null==r||"string"!=typeof r.padding?0:i$(t,"yAxis",e,r.padding)},(t,e)=>{if(null==t)return{top:0,bottom:0};var r,n,{padding:i}=t;return"string"==typeof i?{top:e,bottom:e}:{top:(null!==(r=i.top)&&void 0!==r?r:0)+e,bottom:(null!==(n=i.bottom)&&void 0!==n?n:0)+e}}),iW=(0,f.P1)([nk.DX,iK,nT.V,nT.F,(t,e,r)=>r],(t,e,r,n,i)=>{var{padding:o}=n;return i?[o.left,r.width-o.right]:[t.left+e.left,t.left+t.width-e.right]}),iY=(0,f.P1)([nk.DX,r7.rE,iF,nT.V,nT.F,(t,e,r)=>r],(t,e,r,n,i,o)=>{var{padding:a}=i;return o?[n.height-a.bottom,a.top]:"horizontal"===e?[t.top+t.height-r.bottom,t.top+r.top]:[t.top+r.top,t.top+t.height-r.bottom]}),iV=(t,e,r,n)=>{var i;switch(e){case"xAxis":return iW(t,r,n);case"yAxis":return iY(t,r,n);case"zAxis":return null===(i=nY(t,r))||void 0===i?void 0:i.range;case"angleAxis":return(0,n_.bH)(t);case"radiusAxis":return(0,n_.s9)(t,r);default:return}},iZ=(0,f.P1)([nV,iV],nI.$),iq=(0,f.P1)([nV,iN,iz,iZ],iI);function iH(t,e){return t.id<e.id?-1:t.id>e.id?1:0}(0,f.P1)(nJ,nD.z,(t,e)=>t.flatMap(t=>{var e;return null!==(e=t.errorBars)&&void 0!==e?e:[]}).filter(t=>n9(e,t)));var iX=(t,e)=>e,iG=(t,e,r)=>r,iQ=(0,f.P1)(nM.X,iX,iG,(t,e,r)=>t.filter(t=>t.orientation===e).filter(t=>t.mirror===r).sort(iH)),iJ=(0,f.P1)(nM.Z,iX,iG,(t,e,r)=>t.filter(t=>t.orientation===e).filter(t=>t.mirror===r).sort(iH)),i0=(t,e)=>({width:t.width,height:e.height}),i1=(t,e)=>({width:"number"==typeof e.width?e.width:nL.n9,height:t.height}),i2=(0,f.P1)(nk.DX,n$,i0),i6=(t,e,r)=>{switch(e){case"top":return t.top;case"bottom":return r-t.bottom;default:return 0}},i3=(t,e,r)=>{switch(e){case"left":return t.left;case"right":return r-t.right;default:return 0}},i5=(0,f.P1)(nS.d_,nk.DX,iQ,iX,iG,(t,e,r,n,i)=>{var o,a={};return r.forEach(r=>{var u=i0(e,r);null==o&&(o=i6(e,n,t));var c="top"===n&&!i||"bottom"===n&&i;a[r.id]=o-Number(c)*u.height,o+=(c?-1:1)*u.height}),a}),i4=(0,f.P1)(nS.RD,nk.DX,iJ,iX,iG,(t,e,r,n,i)=>{var o,a={};return r.forEach(r=>{var u=i1(e,r);null==o&&(o=i3(e,n,t));var c="left"===n&&!i||"right"===n&&i;a[r.id]=o-Number(c)*u.width,o+=(c?-1:1)*u.width}),a}),i8=(t,e)=>{var r=(0,nk.DX)(t),n=n$(t,e);if(null!=n){var i=i5(t,n.orientation,n.mirror)[e];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},i9=(t,e)=>{var r=(0,nk.DX)(t),n=nF(t,e);if(null!=n){var i=i4(t,n.orientation,n.mirror)[e];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},i7=(0,f.P1)(nk.DX,nF,(t,e)=>({width:"number"==typeof e.width?e.width:nL.n9,height:t.height})),ot=(t,e,r)=>{switch(e){case"xAxis":return i2(t,r).width;case"yAxis":return i7(t,r).height;default:return}},oe=(t,e,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:o,dataKey:a}=r,u=(0,nt.NA)(t,n),c=e.map(t=>t.value);if(a&&u&&"category"===o&&i&&(0,nr.bv)(c))return c}},or=(0,f.P1)([r7.rE,n8,nV,nD.z],oe),on=(t,e,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:o}=r;if((0,nt.NA)(t,n)&&("number"===i||"auto"!==o))return e.map(t=>t.value)}},oi=(0,f.P1)([r7.rE,n8,nZ,nD.z],on),oo=(0,f.P1)([r7.rE,(t,e,r)=>{switch(e){case"xAxis":return n$(t,r);case"yAxis":return nF(t,r);default:throw Error("Unexpected axis type: ".concat(e))}},iN,iq,or,oi,iV,iR,nD.z],(t,e,r,n,i,o,a,u,c)=>{if(null==e)return null;var l=(0,nt.NA)(t,c);return{angle:e.angle,interval:e.interval,minTickGap:e.minTickGap,orientation:e.orientation,tick:e.tick,tickCount:e.tickCount,tickFormatter:e.tickFormatter,ticks:e.ticks,type:e.type,unit:e.unit,axisType:c,categoricalDomain:o,duplicateDomain:i,isCategorical:l,niceTicks:u,range:a,realScaleType:r,scale:n}}),oa=(0,f.P1)([r7.rE,nZ,iN,iq,iR,iV,or,oi,nD.z],(t,e,r,n,i,o,a,u,c)=>{if(null!=e&&null!=n){var l=(0,nt.NA)(t,c),{type:s,ticks:f,tickCount:h}=e,d="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,p="category"===s&&n.bandwidth?n.bandwidth()/d:0;p="angleAxis"===c&&null!=o&&o.length>=2?2*(0,nr.uY)(o[0]-o[1])*p:p;var y=f||i;return y?y.map((t,e)=>({index:e,coordinate:n(a?a.indexOf(t):t)+p,value:t,offset:p})).filter(t=>!(0,nr.In)(t.coordinate)):l&&u?u.map((t,e)=>({coordinate:n(t)+p,value:t,index:e,offset:p})):n.ticks?n.ticks(h).map(t=>({coordinate:n(t)+p,value:t,offset:p})):n.domain().map((t,e)=>({coordinate:n(t)+p,value:a?a[t]:t,index:e,offset:p}))}}),ou=(0,f.P1)([r7.rE,nZ,iq,iV,or,oi,nD.z],(t,e,r,n,i,o,a)=>{if(null!=e&&null!=r&&null!=n&&n[0]!==n[1]){var u=(0,nt.NA)(t,a),{tickCount:c}=e,l=0;return(l="angleAxis"===a&&(null==n?void 0:n.length)>=2?2*(0,nr.uY)(n[0]-n[1])*l:l,u&&o)?o.map((t,e)=>({coordinate:r(t)+l,value:t,index:e,offset:l})):r.ticks?r.ticks(c).map(t=>({coordinate:r(t)+l,value:t,offset:l})):r.domain().map((t,e)=>({coordinate:r(t)+l,value:i?i[t]:t,index:e,offset:l}))}}),oc=(0,f.P1)(nV,iq,(t,e)=>{if(null!=t&&null!=e)return nB(nB({},t),{},{scale:e})}),ol=(0,f.P1)([nV,iN,i_,iZ],iI);(0,f.P1)((t,e,r)=>nY(t,r),ol,(t,e)=>{if(null!=t&&null!=e)return nB(nB({},t),{},{scale:e})});var os=(0,f.P1)([r7.rE,nM.X,nM.Z],(t,e,r)=>{switch(t){case"horizontal":return e.some(t=>t.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(t=>t.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},2431:function(t,e,r){"use strict";r.d(e,{F:function(){return u},V:function(){return c}});var n=r(92713),i=r(69729),o=r(60152),a=r(16630),u=t=>t.brush,c=(0,n.P1)([u,i.DX,o.lr],(t,e,r)=>({height:t.height,x:(0,a.hj)(t.x)?t.x:e.left,y:(0,a.hj)(t.y)?t.y:e.top+e.height+e.brushBottom-((null==r?void 0:r.bottom)||0),width:(0,a.hj)(t.width)?t.width:e.width}))},3838:function(t,e,r){"use strict";r.d(e,{b:function(){return i}});var n=r(16630),i=(t,e)=>{var r,i=Number(e);if(!(0,n.In)(i)&&null!=e)return i>=0?null==t||null===(r=t[i])||void 0===r?void 0:r.value:void 0}},6064:function(t,e,r){"use strict";r.d(e,{p:function(){return i}});var n=r(66395),i=(t,e)=>{var r=null==t?void 0:t.index;if(null==r)return null;var i=Number(r);if(!(0,n.n)(i))return r;var o=Infinity;return e.length>0&&(o=e.length-1),String(Math.max(0,Math.min(i,o)))}},87367:function(t,e,r){"use strict";r.d(e,{$:function(){return n}});var n=(t,e)=>t&&e?null!=t&&t.reversed?[e[1],e[0]]:e:void 0},8656:function(t,e,r){"use strict";r.d(e,{$:function(){return n}});var n=(t,e,r,n,i,o,a,u)=>{if(null!=o&&null!=u){var c=a[0],l=null==c?void 0:u(c.positions,o);if(null!=l)return l;var s=null==i?void 0:i[Number(o)];if(s)return"horizontal"===r?{x:s.coordinate,y:(n.top+e)/2}:{x:(n.left+t)/2,y:s.coordinate}}}},24597:function(t,e,r){"use strict";r.d(e,{k:function(){return a}});var n=r(64725);function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var a=(t,e,r,i)=>{if(null==e)return n.UW;var a="axis"===e?"click"===r?t.axisInteraction.click:t.axisInteraction.hover:"click"===r?t.itemInteraction.click:t.itemInteraction.hover;if(null==a)return n.UW;if(a.active)return a;if(t.keyboardInteraction.active)return t.keyboardInteraction;if(t.syncInteraction.active&&null!=t.syncInteraction.index)return t.syncInteraction;var u=!0===t.settings.active;if(null!=a.index){if(u)return o(o({},a),{},{active:!0})}else if(null!=i)return{active:!0,coordinate:void 0,dataKey:void 0,index:i};return o(o({},n.UW),{},{coordinate:a.coordinate})}},60240:function(t,e,r){"use strict";r.d(e,{J:function(){return u}});var n=r(16630),i=r(49037);function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?o(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var u=(t,e,r,o,u,c,l)=>{if(null!=e&&null!=c){var{chartData:s,computedData:f,dataStartIndex:h,dataEndIndex:d}=r;return t.reduce((t,r)=>{var p,y,v,g,{dataDefinedOnItem:m,settings:b}=r,w=Array.isArray(p=null!=m?m:s)&&p&&h+d!==0?p.slice(h,d+1):p,x=null!==(y=null==b?void 0:b.dataKey)&&void 0!==y?y:null==o?void 0:o.dataKey,O=null==b?void 0:b.nameKey;return Array.isArray(v=null!=o&&o.dataKey&&Array.isArray(w)&&!Array.isArray(w[0])&&"axis"===l?(0,n.Ap)(w,o.dataKey,u):c(w,e,f,O))?v.forEach(e=>{var r=a(a({},b),{},{name:e.name,unit:e.unit,color:void 0,fill:void 0});t.push((0,i.eV)({tooltipEntrySettings:r,dataKey:e.dataKey,payload:e.payload,value:(0,i.F$)(e.payload,e.dataKey),name:e.name}))}):t.push((0,i.eV)({tooltipEntrySettings:b,dataKey:x,payload:v,value:(0,i.F$)(v,x),name:null!==(g=(0,i.F$)(v,O))&&void 0!==g?g:null==b?void 0:b.name})),t},[])}}},89667:function(t,e,r){"use strict";r.d(e,{k:function(){return n}});var n=(t,e,r,n)=>{var i;return"axis"===e?t.tooltipItemPayloads:0===t.tooltipItemPayloads.length?[]:null==(i="hover"===r?t.itemInteraction.hover.dataKey:t.itemInteraction.click.dataKey)&&null!=n?[t.tooltipItemPayloads[0]]:t.tooltipItemPayloads.filter(t=>{var e;return(null===(e=t.settings)||void 0===e?void 0:e.dataKey)===i})}},60152:function(t,e,r){"use strict";r.d(e,{K$:function(){return o},RD:function(){return n},d_:function(){return i},lr:function(){return a}});var n=t=>t.layout.width,i=t=>t.layout.height,o=t=>t.layout.scale,a=t=>t.layout.margin},22932:function(t,e,r){"use strict";r.d(e,{RV:function(){return o},hA:function(){return a},iP:function(){return i}});var n=r(92713),i=t=>t.chartData,o=(0,n.P1)([i],t=>{var e=null!=t.chartData?t.chartData.length-1:0;return{chartData:t.chartData,computedData:t.computedData,dataEndIndex:e,dataStartIndex:0}}),a=(t,e,r,n)=>n?o(t):i(t)},56462:function(t,e,r){"use strict";r.d(e,{l:function(){return n}});var n=(t,e,r)=>r},40304:function(t,e,r){"use strict";r.d(e,{z:function(){return n}});var n=(t,e)=>e},80796:function(t,e,r){"use strict";r.d(e,{dc:function(){return v},bH:function(){return O},HW:function(){return j},Au:function(){return g},s9:function(){return P}});var n=r(92713),i=r(60152),o=r(69729),a=r(39206),u=r(16630),c="auto",l="auto",s=r(87367),f=r(35953),h={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:0,includeHidden:!1,name:void 0,reversed:!1,scale:c,tick:!0,tickCount:void 0,ticks:void 0,type:"category",unit:void 0},d={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!0,dataKey:void 0,domain:void 0,id:0,includeHidden:!1,name:void 0,reversed:!1,scale:l,tick:!0,tickCount:5,ticks:void 0,type:"number",unit:void 0},p={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!0,dataKey:void 0,domain:void 0,id:0,includeHidden:!1,name:void 0,reversed:!1,scale:c,tick:!0,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},y={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!0,dataKey:void 0,domain:void 0,id:0,includeHidden:!1,name:void 0,reversed:!1,scale:l,tick:!0,tickCount:5,ticks:void 0,type:"category",unit:void 0},v=(t,e)=>null!=t.polarAxis.angleAxis[e]?t.polarAxis.angleAxis[e]:"radial"===t.layout.layoutType?p:h,g=(t,e)=>null!=t.polarAxis.radiusAxis[e]?t.polarAxis.radiusAxis[e]:"radial"===t.layout.layoutType?y:d,m=t=>t.polarOptions,b=(0,n.P1)([i.RD,i.d_,o.DX],a.$4),w=(0,n.P1)([m,b],(t,e)=>{if(null!=t)return(0,u.h1)(t.innerRadius,e,0)}),x=(0,n.P1)([m,b],(t,e)=>{if(null!=t)return(0,u.h1)(t.outerRadius,e,.8*e)}),O=(0,n.P1)([m],t=>{if(null==t)return[0,0];var{startAngle:e,endAngle:r}=t;return[e,r]});(0,n.P1)([v,O],s.$);var P=(0,n.P1)([b,w,x],(t,e,r)=>{if(null!=t&&null!=e&&null!=r)return[e,r]});(0,n.P1)([g,P],s.$);var j=(0,n.P1)([f.rE,m,w,x,i.RD,i.d_],(t,e,r,n,i,o)=>{if(("centric"===t||"radial"===t)&&null!=e&&null!=r&&null!=n){var{cx:a,cy:c,startAngle:l,endAngle:s}=e;return{cx:(0,u.h1)(a,i,i/2),cy:(0,u.h1)(c,o,o/2),innerRadius:r,outerRadius:n,startAngle:l,endAngle:s,clockWise:!1}}})},33968:function(t,e,r){"use strict";r.d(e,{E2:function(){return c},Qw:function(){return u},Sg:function(){return f},X8:function(){return a},Yd:function(){return l},b2:function(){return s},qy:function(){return n},sd:function(){return o},wK:function(){return i}});var n=t=>t.rootProps.maxBarSize,i=t=>t.rootProps.barGap,o=t=>t.rootProps.barCategoryGap,a=t=>t.rootProps.barSize,u=t=>t.rootProps.stackOffset,c=t=>t.options.chartName,l=t=>t.rootProps.syncId,s=t=>t.rootProps.syncMethod,f=t=>t.options.eventEmitter},63330:function(t,e,r){"use strict";r.d(e,{h:function(){return l}});var n=r(92713),i=r(35953),o=r(31944),a=r(69729),u=r(84461),c=r(80796),l=(0,n.P1)([(t,e)=>e,i.rE,c.HW,o.cS,o.PG,o.WQ,u.EM,a.DX],u.Nb)},32498:function(t,e,r){"use strict";r.d(e,{X:function(){return i},Z:function(){return o}});var n=r(92713),i=(0,n.P1)(t=>t.cartesianAxis.xAxis,t=>Object.values(t)),o=(0,n.P1)(t=>t.cartesianAxis.yAxis,t=>Object.values(t))},69729:function(t,e,r){"use strict";r.d(e,{zM:function(){return g},DX:function(){return y},nd:function(){return v}});var n=r(92713),i=r(15870),o=r.n(i),a=r(31104),u=r.n(a),c=t=>t.legend.settings;(0,n.P1)([t=>t.legend.payload,c],(t,e)=>{var{itemSorter:r}=e,n=t.flat(1);return r?u()(n,r):n});var l=r(49037),s=r(60152),f=r(32498),h=r(78487);function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var y=(0,n.P1)([s.RD,s.d_,s.lr,t=>t.brush.height,f.X,f.Z,c,t=>t.legend.size],(t,e,r,n,i,a,u,c)=>{var s=a.reduce((t,e)=>{var{orientation:r}=e;if(!e.mirror&&!e.hide){var n="number"==typeof e.width?e.width:h.n9;return p(p({},t),{},{[r]:t[r]+n})}return t},{left:r.left||0,right:r.right||0}),f=i.reduce((t,e)=>{var{orientation:r}=e;return e.mirror||e.hide?t:p(p({},t),{},{[r]:o()(t,"".concat(r))+e.height})},{top:r.top||0,bottom:r.bottom||0}),d=p(p({},f),s),y=d.bottom;d.bottom+=n;var v=t-(d=(0,l.By)(d,u,c)).left-d.right,g=e-d.top-d.bottom;return p(p({brushBottom:y},d),{},{width:Math.max(v,0),height:Math.max(g,0)})}),v=(0,n.P1)(y,t=>({x:t.left,y:t.top,width:t.width,height:t.height})),g=(0,n.P1)(s.RD,s.d_,(t,e)=>({x:0,y:0,width:t,height:e}))},46302:function(t,e,r){"use strict";r.d(e,{Nb:function(){return o},SB:function(){return u},Y4:function(){return c},_j:function(){return a},iS:function(){return i}});var n=r(39040),i=t=>t.options.defaultTooltipEventType,o=t=>t.options.validateTooltipEventTypes;function a(t,e,r){if(null==t)return e;var n=t?"axis":"item";return null==r?e:r.includes(n)?n:e}function u(t,e){return a(e,i(t),o(t))}function c(t){return(0,n.C)(e=>u(e,t))}},73535:function(t,e,r){"use strict";r.d(e,{h:function(){return n}});var n=t=>t.options.tooltipPayloadSearcher},54803:function(t,e,r){"use strict";r.d(e,{M:function(){return n}});var n=t=>t.tooltip},84461:function(t,e,r){"use strict";r.d(e,{AC:function(){return O},EM:function(){return E},Nb:function(){return L},TT:function(){return N},ck:function(){return _},hA:function(){return C},i:function(){return D},oo:function(){return I},wi:function(){return k}});var n=r(92713),i=r(31104),o=r.n(i),a=r(39040),u=r(49037),c=r(22932),l=r(31944),s=r(33968),f=r(35953),h=r(69729),d=r(60152),p=r(3838),y=r(24597),v=r(6064),g=r(8656),m=r(89667),b=r(73535),w=r(54803),x=r(60240),O=()=>(0,a.C)(s.E2),P=(t,e)=>e,j=(t,e,r)=>r,A=(t,e,r,n)=>n,E=(0,n.P1)(l.WQ,t=>o()(t,t=>t.coordinate)),S=(0,n.P1)([w.M,P,j,A],y.k),M=(0,n.P1)([S,l.wQ],v.p),k=(t,e,r)=>{if(null!=e){var n=(0,w.M)(t);return"axis"===e?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},T=(0,n.P1)([w.M,P,j,A],m.k),C=(0,n.P1)([d.RD,d.d_,f.rE,h.DX,l.WQ,A,T,b.h],g.$),_=(0,n.P1)([S,C],(t,e)=>{var r;return null!==(r=t.coordinate)&&void 0!==r?r:e}),D=(0,n.P1)(l.WQ,M,p.b),N=(0,n.P1)([T,M,c.iP,l.zv,D,b.h,P],x.J),I=(0,n.P1)([S],t=>({isActive:t.active,activeIndex:t.index})),L=(t,e,r,n,i,o,a,c)=>{if(t&&e&&n&&i&&o){var l=(0,u.Z2)(t.chartX,t.chartY,e,r,c);if(l){var s=(0,u.HZ)(l,e),f=(0,u.VO)(s,a,o,n,i),h=(0,u.ep)(e,o,f,l);return{activeIndex:String(f),activeCoordinate:h}}}}},31944:function(t,e,r){"use strict";r.d(e,{i:function(){return to},pI:function(){return tl},du:function(){return ta},Ve:function(){return ti},oo:function(){return ts},zv:function(){return j},PG:function(){return H},ri:function(){return X},WQ:function(){return J},cS:function(){return O},wQ:function(){return T}});var n=r(92713),i=r(98628),o=r(35953),a=r(49037),u=r(22932),c=r(33968),l=r(16630),s=r(87367),f=r(46302),h=r(3838),d=r(24597),p=r(6064),y=r(8656),v=r(60152),g=r(69729),m=r(89667),b=r(73535),w=r(54803),x=r(60240),O=t=>{var e=(0,o.rE)(t);return"horizontal"===e?"xAxis":"vertical"===e?"yAxis":"centric"===e?"angleAxis":"radiusAxis"},P=t=>t.tooltip.settings.axisId,j=t=>{var e=O(t),r=P(t);return(0,i.ub)(t,e,r)},A=(0,n.P1)([j,o.rE,i.Q4,c.E2,O],i.Yf),E=(0,n.P1)([t=>t.graphicalItems.cartesianItems,t=>t.graphicalItems.polarItems],(t,e)=>[...t,...e]),S=(0,n.P1)([O,P],i.YZ),M=(0,n.P1)([E,j,S],i.$B),k=(0,n.P1)([M],i.bU),T=(0,n.P1)([k,u.iP],i.tZ),C=(0,n.P1)([T,j,M],i.UA),_=(0,n.P1)([j],i.c4),D=(0,n.P1)([T,M,c.Qw],i.CK),N=(0,n.P1)([D,u.iP,O],i.dz),I=(0,n.P1)([M],i.Tk),L=(0,n.P1)([T,j,I,O],i.VQ),R=(0,n.P1)([i.RA,O,P],i.zW),B=(0,n.P1)([R,O],i.zX),z=(0,n.P1)([i.ww,O,P],i.zW),U=(0,n.P1)([z,O],i.yT),$=(0,n.P1)([i.xz,O,P],i.zW),K=(0,n.P1)([$,O],i.fY),F=(0,n.P1)([B,K,U],i.Eu),W=(0,n.P1)([j,_,N,L,F],i.E8),Y=(0,n.P1)([j,o.rE,T,C,c.Qw,O,W],i.l_),V=(0,n.P1)([Y,j,A],i.vb),Z=(0,n.P1)([j,Y,V,O],i.kO),q=t=>{var e=O(t),r=P(t);return(0,i.Fn)(t,e,r,!1)},H=(0,n.P1)([j,q],s.$),X=(0,n.P1)([j,A,Z,H],i.Jw),G=(0,n.P1)([o.rE,C,j,O],i.rC),Q=(0,n.P1)([o.rE,C,j,O],i.UC),J=(0,n.P1)([o.rE,j,A,X,q,G,Q,O],(t,e,r,n,i,o,u,c)=>{if(e){var{type:s}=e,f=(0,a.NA)(t,c);if(n){var h="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,d="category"===s&&n.bandwidth?n.bandwidth()/h:0;return(d="angleAxis"===c&&null!=i&&(null==i?void 0:i.length)>=2?2*(0,l.uY)(i[0]-i[1])*d:d,f&&u)?u.map((t,e)=>({coordinate:n(t)+d,value:t,index:e,offset:d})):n.domain().map((t,e)=>({coordinate:n(t)+d,value:o?o[t]:t,index:e,offset:d}))}}}),tt=(0,n.P1)([f.iS,f.Nb,t=>t.tooltip.settings],(t,e,r)=>(0,f._j)(r.shared,t,e)),te=t=>t.tooltip.settings.trigger,tr=t=>t.tooltip.settings.defaultIndex,tn=(0,n.P1)([w.M,tt,te,tr],d.k),ti=(0,n.P1)([tn,T],p.p),to=(0,n.P1)([J,ti],h.b),ta=(0,n.P1)([tn],t=>{if(t)return t.dataKey}),tu=(0,n.P1)([w.M,tt,te,tr],m.k),tc=(0,n.P1)([v.RD,v.d_,o.rE,g.DX,J,tr,tu,b.h],y.$),tl=(0,n.P1)([tn,tc],(t,e)=>null!=t&&t.coordinate?t.coordinate:e),ts=(0,n.P1)([tn],t=>t.active),tf=(0,n.P1)([tu,ti,u.iP,j,to,b.h,tt],x.J);(0,n.P1)([tf],t=>{if(null!=t)return Array.from(new Set(t.map(t=>t.payload).filter(t=>null!=t)))})},64725:function(t,e,r){"use strict";r.d(e,{$A:function(){return v},KN:function(){return g},KO:function(){return u},Kw:function(){return m},M1:function(){return s},O_:function(){return d},PD:function(){return l},Rr:function(){return p},UW:function(){return o},Vg:function(){return f},cK:function(){return c},eB:function(){return y},ne:function(){return h}});var n=r(39129),i=r(10418),o={active:!1,index:null,dataKey:void 0,coordinate:void 0},a=(0,n.oM)({name:"tooltip",initialState:{itemInteraction:{click:o,hover:o},axisInteraction:{click:o,hover:o},keyboardInteraction:o,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(t,e){t.tooltipItemPayloads.push((0,i.cA)(e.payload))},removeTooltipEntrySettings(t,e){var r=(0,i.Vk)(t).tooltipItemPayloads.indexOf((0,i.cA)(e.payload));r>-1&&t.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(t,e){t.settings=e.payload},setActiveMouseOverItemIndex(t,e){t.syncInteraction.active=!1,t.keyboardInteraction.active=!1,t.itemInteraction.hover.active=!0,t.itemInteraction.hover.index=e.payload.activeIndex,t.itemInteraction.hover.dataKey=e.payload.activeDataKey,t.itemInteraction.hover.coordinate=e.payload.activeCoordinate},mouseLeaveChart(t){t.itemInteraction.hover.active=!1,t.axisInteraction.hover.active=!1},mouseLeaveItem(t){t.itemInteraction.hover.active=!1},setActiveClickItemIndex(t,e){t.syncInteraction.active=!1,t.itemInteraction.click.active=!0,t.keyboardInteraction.active=!1,t.itemInteraction.click.index=e.payload.activeIndex,t.itemInteraction.click.dataKey=e.payload.activeDataKey,t.itemInteraction.click.coordinate=e.payload.activeCoordinate},setMouseOverAxisIndex(t,e){t.syncInteraction.active=!1,t.axisInteraction.hover.active=!0,t.keyboardInteraction.active=!1,t.axisInteraction.hover.index=e.payload.activeIndex,t.axisInteraction.hover.dataKey=e.payload.activeDataKey,t.axisInteraction.hover.coordinate=e.payload.activeCoordinate},setMouseClickAxisIndex(t,e){t.syncInteraction.active=!1,t.keyboardInteraction.active=!1,t.axisInteraction.click.active=!0,t.axisInteraction.click.index=e.payload.activeIndex,t.axisInteraction.click.dataKey=e.payload.activeDataKey,t.axisInteraction.click.coordinate=e.payload.activeCoordinate},setSyncInteraction(t,e){t.syncInteraction=e.payload},setKeyboardInteraction(t,e){t.keyboardInteraction.active=e.payload.active,t.keyboardInteraction.index=e.payload.activeIndex,t.keyboardInteraction.coordinate=e.payload.activeCoordinate,t.keyboardInteraction.dataKey=e.payload.activeDataKey}}}),{addTooltipEntrySettings:u,removeTooltipEntrySettings:c,setTooltipSettingsState:l,setActiveMouseOverItemIndex:s,mouseLeaveItem:f,mouseLeaveChart:h,setActiveClickItemIndex:d,setMouseOverAxisIndex:p,setMouseClickAxisIndex:y,setSyncInteraction:v,setKeyboardInteraction:g}=a.actions,m=a.reducer},83061:function(t,e,r){"use strict";r.d(e,{$:function(){return p},x:function(){return y}});var n=r(39129),i=r(64725),o=r(63330),a=r(13169),u=r(46302),c=r(78487),l=r(92713),s=r(73535),f=r(54803),h=(0,l.P1)([f.M],t=>t.tooltipItemPayloads),d=(0,l.P1)([h,s.h,(t,e,r)=>e,(t,e,r)=>r],(t,e,r,n)=>{var i=t.find(t=>t.settings.dataKey===n);if(null!=i){var{positions:o}=i;if(null!=o)return e(o,r)}}),p=(0,n.PH)("touchMove"),y=(0,n.e)();y.startListening({actionCreator:p,effect:(t,e)=>{var r=t.payload,n=e.getState(),l=(0,u.SB)(n,n.tooltip.settings.shared);if("axis"===l){var s=(0,o.h)(n,(0,a.a)({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==s?void 0:s.activeIndex)!=null&&e.dispatch((0,i.Rr)({activeIndex:s.activeIndex,activeDataKey:void 0,activeCoordinate:s.activeCoordinate}))}else if("item"===l){var f,h=r.touches[0],p=document.elementFromPoint(h.clientX,h.clientY);if(!p||!p.getAttribute)return;var y=p.getAttribute(c.Gh),v=null!==(f=p.getAttribute(c.aN))&&void 0!==f?f:void 0,g=d(e.getState(),y,v);e.dispatch((0,i.M1)({activeDataKey:v,activeIndex:y,activeCoordinate:g}))}}})},70858:function(t,e,r){"use strict";r.d(e,{W9:function(){return g},Fg:function(){return m}});var n=r(2265),i=r(39040),o=r(33968),a=new(r(77625)),u="recharts.syncEvent.tooltip",c="recharts.syncEvent.brush",l=r(31057),s=r(64725),f=r(84461),h=r(31944);function d(t){return t.tooltip.syncInteraction}var p=r(35953),y=r(39173),v=()=>{};function g(){var t,e,r,f,d,g,m,b,w,x,O,P=(0,i.T)();(0,n.useEffect)(()=>{P((0,l.IC)())},[P]),t=(0,i.C)(o.Yd),e=(0,i.C)(o.Sg),r=(0,i.T)(),f=(0,i.C)(o.b2),d=(0,i.C)(h.WQ),g=(0,p.vn)(),m=(0,p.d2)(),b=(0,i.C)(t=>t.rootProps.className),(0,n.useEffect)(()=>{if(null==t)return v;var n=(n,i,o)=>{if(e!==o&&t===n){if("index"===f){r(i);return}if(null!=d){if("function"==typeof f){var a,u=f(d,{activeTooltipIndex:null==i.payload.index?void 0:Number(i.payload.index),isTooltipActive:i.payload.active,activeIndex:null==i.payload.index?void 0:Number(i.payload.index),activeLabel:i.payload.label,activeDataKey:i.payload.dataKey,activeCoordinate:i.payload.coordinate});a=d[u]}else"value"===f&&(a=d.find(t=>String(t.value)===i.payload.label));var{coordinate:c}=i.payload;if(null==a||!1===i.payload.active||null==c||null==m){r((0,s.$A)({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));return}var{x:l,y:h}=c,p=Math.min(l,m.x+m.width),y=Math.min(h,m.y+m.height),v={x:"horizontal"===g?a.coordinate:p,y:"horizontal"===g?y:a.coordinate};r((0,s.$A)({active:i.payload.active,coordinate:v,dataKey:i.payload.dataKey,index:String(a.index),label:i.payload.label}))}}};return a.on(u,n),()=>{a.off(u,n)}},[b,r,e,t,f,d,g,m]),w=(0,i.C)(o.Yd),x=(0,i.C)(o.Sg),O=(0,i.T)(),(0,n.useEffect)(()=>{if(null==w)return v;var t=(t,e,r)=>{x!==r&&w===t&&O((0,y.t0)(e))};return a.on(c,t),()=>{a.off(c,t)}},[O,x,w])}function m(t,e,r,c,l,h){var p=(0,i.C)(r=>(0,f.wi)(r,t,e)),y=(0,i.C)(o.Sg),v=(0,i.C)(o.Yd),g=(0,i.C)(o.b2),m=(0,i.C)(d),b=null==m?void 0:m.active;(0,n.useEffect)(()=>{if(!b&&null!=v&&null!=y){var t=(0,s.$A)({active:h,coordinate:r,dataKey:p,index:l,label:"number"==typeof c?String(c):c});a.emit(u,v,t,y)}},[b,r,p,l,c,y,v,g,h])}},80503:function(t,e,r){"use strict";r.d(e,{b:function(){return H}});var n=r(2265),i=r(46802),o=r.n(i),a=r(73649),u=r(61994),c=r(82944),l=r(40130),s=r(46595);function f(){return(f=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var h=(t,e,r,n,i)=>{var o=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-o/2,",").concat(e+i)+"L ".concat(t+r-o/2-n,",").concat(e+i)+"L ".concat(t,",").concat(e," Z")},d={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},p=t=>{var e=(0,l.j)(t,d),r=(0,n.useRef)(),[i,o]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var t=r.current.getTotalLength();t&&o(t)}catch(t){}},[]);var{x:a,y:p,upperWidth:y,lowerWidth:v,height:g,className:m}=e,{animationEasing:b,animationDuration:w,animationBegin:x,isUpdateAnimationActive:O}=e;if(a!==+a||p!==+p||y!==+y||v!==+v||g!==+g||0===y&&0===v||0===g)return null;var P=(0,u.W)("recharts-trapezoid",m);return O?n.createElement(s.r,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:g,x:a,y:p},to:{upperWidth:y,lowerWidth:v,height:g,x:a,y:p},duration:w,animationEasing:b,isActive:O},t=>{var{upperWidth:o,lowerWidth:a,height:u,x:l,y:d}=t;return n.createElement(s.r,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:x,duration:w,easing:b},n.createElement("path",f({},(0,c.L6)(e,!0),{className:P,d:h(l,d,o,a,u),ref:r})))}):n.createElement("g",null,n.createElement("path",f({},(0,c.L6)(e,!0),{className:P,d:h(a,p,y,v,g)})))},y=r(60474),v=r(9841);let g=Math.cos,m=Math.sin,b=Math.sqrt,w=Math.PI,x=2*w;var O={draw(t,e){let r=b(e/w);t.moveTo(r,0),t.arc(0,0,r,0,x)}};let P=b(1/3),j=2*P,A=m(w/10)/m(7*w/10),E=m(x/10)*A,S=-g(x/10)*A,M=b(3),k=b(3)/2,T=1/b(12),C=(T/2+1)*3;var _=r(76115),D=r(67790);b(3),b(3);var N=r(16630),I=["type","size","sizeType"];function L(){return(L=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function R(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function B(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?R(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var z={symbolCircle:O,symbolCross:{draw(t,e){let r=b(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=b(e/j),n=r*P;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=b(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=b(.8908130915292852*e),n=E*r,i=S*r;t.moveTo(0,-r),t.lineTo(n,i);for(let e=1;e<5;++e){let o=x*e/5,a=g(o),u=m(o);t.lineTo(u*r,-a*r),t.lineTo(a*n-u*i,u*n+a*i)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-b(e/(3*M));t.moveTo(0,2*r),t.lineTo(-M*r,-r),t.lineTo(M*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=b(e/C),n=r/2,i=r*T,o=r*T+r,a=-n;t.moveTo(n,i),t.lineTo(n,o),t.lineTo(a,o),t.lineTo(-.5*n-k*i,k*n+-.5*i),t.lineTo(-.5*n-k*o,k*n+-.5*o),t.lineTo(-.5*a-k*o,k*a+-.5*o),t.lineTo(-.5*n+k*i,-.5*i-k*n),t.lineTo(-.5*n+k*o,-.5*o-k*n),t.lineTo(-.5*a+k*o,-.5*o-k*a),t.closePath()}}},U=Math.PI/180,$=t=>z["symbol".concat((0,N.jC)(t))]||O,K=(t,e,r)=>{if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*U;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},F=t=>{var e,{type:r="circle",size:i=64,sizeType:o="area"}=t,a=B(B({},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,I)),{},{type:r,size:i,sizeType:o}),{className:l,cx:s,cy:f}=a,h=(0,c.L6)(a,!0);return s===+s&&f===+f&&i===+i?n.createElement("path",L({},h,{className:(0,u.W)("recharts-symbols",l),transform:"translate(".concat(s,", ").concat(f,")"),d:(e=$(r),(function(t,e){let r=null,n=(0,D.d)(i);function i(){let i;if(r||(r=i=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),i)return r=null,i+""||null}return t="function"==typeof t?t:(0,_.Z)(t||O),e="function"==typeof e?e:(0,_.Z)(void 0===e?64:+e),i.type=function(e){return arguments.length?(t="function"==typeof e?e:(0,_.Z)(e),i):t},i.size=function(t){return arguments.length?(e="function"==typeof t?t:(0,_.Z)(+t),i):e},i.context=function(t){return arguments.length?(r=null==t?null:t,i):r},i})().type(e).size(K(i,o,r))())})):null};F.registerSymbol=(t,e)=>{z["symbol".concat((0,N.jC)(t))]=e};var W=["option","shapeType","propTransformer","activeClassName","isActive"];function Y(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function V(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Y(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Y(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Z(t,e){return V(V({},e),t)}function q(t){var{shapeType:e,elementProps:r}=t;switch(e){case"rectangle":return n.createElement(a.A,r);case"trapezoid":return n.createElement(p,r);case"sector":return n.createElement(y.L,r);case"symbols":if("symbols"===e)return n.createElement(F,r);break;default:return null}}function H(t){var e,{option:r,shapeType:i,propTransformer:a=Z,activeClassName:u="recharts-active-shape",isActive:c}=t,l=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,W);if((0,n.isValidElement)(r))e=(0,n.cloneElement)(r,V(V({},l),(0,n.isValidElement)(r)?r.props:r));else if("function"==typeof r)e=r(l);else if(o()(r)&&"boolean"!=typeof r){var s=a(r,l);e=n.createElement(q,{shapeType:i,elementProps:s})}else e=n.createElement(q,{shapeType:i,elementProps:l});return c?n.createElement(v.m,{className:u},e):e}},49037:function(t,e,r){"use strict";r.d(e,{Ji:function(){return N},rI:function(){return D},By:function(){return b},VO:function(){return m},HZ:function(){return U},zF:function(){return P},ep:function(){return z},zT:function(){return I},Yj:function(){return k},Fy:function(){return M},Rf:function(){return x},EB:function(){return _},GA:function(){return S},uX:function(){return E},uY:function(){return O},eV:function(){return L},hn:function(){return R},F$:function(){return g},Z2:function(){return B},NA:function(){return w},Vv:function(){return j}});var n=r(31104),i=r.n(n),o=r(15870),a=r.n(o);function u(t,e){if((i=t.length)>1)for(var r,n,i,o=1,a=t[e[0]],u=a.length;o<i;++o)for(n=a,a=t[e[o]],r=0;r<u;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}var c=r(22516),l=r(76115);function s(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function f(t,e){return t[e]}function h(t){let e=[];return e.key=t,e}var d=r(16630),p=r(39206);function y(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function v(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?y(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function g(t,e,r){return(0,d.Rw)(t)||(0,d.Rw)(e)?r:(0,d.P2)(e)?a()(t,e,r):"function"==typeof e?e(t):r}var m=(t,e,r,n,i)=>{var o,a=-1,u=null!==(o=null==e?void 0:e.length)&&void 0!==o?o:0;if(u<=1||null==t)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var c=0;c<u;c++){var l=c>0?r[c-1].coordinate:r[u-1].coordinate,s=r[c].coordinate,f=c>=u-1?r[0].coordinate:r[c+1].coordinate,h=void 0;if((0,d.uY)(s-l)!==(0,d.uY)(f-s)){var p=[];if((0,d.uY)(f-s)===(0,d.uY)(i[1]-i[0])){h=f;var y=s+i[1]-i[0];p[0]=Math.min(y,(y+l)/2),p[1]=Math.max(y,(y+l)/2)}else{h=l;var v=f+i[1]-i[0];p[0]=Math.min(s,(v+s)/2),p[1]=Math.max(s,(v+s)/2)}var g=[Math.min(s,(h+s)/2),Math.max(s,(h+s)/2)];if(t>g[0]&&t<=g[1]||t>=p[0]&&t<=p[1]){({index:a}=r[c]);break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){({index:a}=r[c]);break}}}else if(e){for(var w=0;w<u;w++)if(0===w&&t<=(e[w].coordinate+e[w+1].coordinate)/2||w>0&&w<u-1&&t>(e[w].coordinate+e[w-1].coordinate)/2&&t<=(e[w].coordinate+e[w+1].coordinate)/2||w===u-1&&t>(e[w].coordinate+e[w-1].coordinate)/2){({index:a}=e[w]);break}}return a},b=(t,e,r)=>{if(e&&r){var{width:n,height:i}=r,{align:o,verticalAlign:a,layout:u}=e;if(("vertical"===u||"horizontal"===u&&"middle"===a)&&"center"!==o&&(0,d.hj)(t[o]))return v(v({},t),{},{[o]:t[o]+(n||0)});if(("horizontal"===u||"vertical"===u&&"center"===o)&&"middle"!==a&&(0,d.hj)(t[a]))return v(v({},t),{},{[a]:t[a]+(i||0)})}return t},w=(t,e)=>"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e,x=(t,e,r,n)=>{if(n)return t.map(t=>t.coordinate);var i,o,a=t.map(t=>(t.coordinate===e&&(i=!0),t.coordinate===r&&(o=!0),t.coordinate));return i||a.push(e),o||a.push(r),a},O=(t,e,r)=>{if(!t)return null;var{duplicateDomain:n,type:i,range:o,scale:a,realScaleType:u,isCategorical:c,categoricalDomain:l,tickCount:s,ticks:f,niceTicks:h,axisType:p}=t;if(!a)return null;var y="scaleBand"===u&&a.bandwidth?a.bandwidth()/2:2,v=(e||r)&&"category"===i&&a.bandwidth?a.bandwidth()/y:0;return(v="angleAxis"===p&&o&&o.length>=2?2*(0,d.uY)(o[0]-o[1])*v:v,e&&(f||h))?(f||h||[]).map((t,e)=>({coordinate:a(n?n.indexOf(t):t)+v,value:t,offset:v,index:e})).filter(t=>!(0,d.In)(t.coordinate)):c&&l?l.map((t,e)=>({coordinate:a(t)+v,value:t,index:e,offset:v})):a.ticks&&!r&&null!=s?a.ticks(s).map((t,e)=>({coordinate:a(t)+v,value:t,offset:v,index:e})):a.domain().map((t,e)=>({coordinate:a(t)+v,value:n?n[t]:t,index:e,offset:v}))},P=t=>{var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),i=Math.min(n[0],n[1])-1e-4,o=Math.max(n[0],n[1])+1e-4,a=t(e[0]),u=t(e[r-1]);(a<i||a>o||u<i||u>o)&&t.domain([e[0],e[r-1]])}},j=(t,e)=>{if(!e||2!==e.length||!(0,d.hj)(e[0])||!(0,d.hj)(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),i=[t[0],t[1]];return(!(0,d.hj)(t[0])||t[0]<r)&&(i[0]=r),(!(0,d.hj)(t[1])||t[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},A={sign:t=>{var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0,a=0;a<e;++a){var u=(0,d.In)(t[a][r][1])?t[a][r][0]:t[a][r][1];u>=0?(t[a][r][0]=i,t[a][r][1]=i+u,i=t[a][r][1]):(t[a][r][0]=o,t[a][r][1]=o+u,o=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,i,o=0,a=t[0].length;o<a;++o){for(i=r=0;r<n;++r)i+=t[r][o][1]||0;if(i)for(r=0;r<n;++r)t[r][o][1]/=i}u(t,e)}},none:u,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,i=t[e[0]],o=i.length;n<o;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;i[n][1]+=i[n][0]=-c/2}u(t,e)}},wiggle:function(t,e){if((i=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,i,o=0,a=1;a<n;++a){for(var c=0,l=0,s=0;c<i;++c){for(var f=t[e[c]],h=f[a][1]||0,d=(h-(f[a-1][1]||0))/2,p=0;p<c;++p){var y=t[e[p]];d+=(y[a][1]||0)-(y[a-1][1]||0)}l+=h,s+=d*h}r[a-1][1]+=r[a-1][0]=o,l&&(o-=s/l)}r[a-1][1]+=r[a-1][0]=o,u(t,e)}},positive:t=>{var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0;o<e;++o){var a=(0,d.In)(t[o][r][1])?t[o][r][0]:t[o][r][1];a>=0?(t[o][r][0]=i,t[o][r][1]=i+a,i=t[o][r][1]):(t[o][r][0]=0,t[o][r][1]=0)}}},E=(t,e,r)=>{var n=A[r];return(function(){var t=(0,l.Z)([]),e=s,r=u,n=f;function i(i){var o,a,u=Array.from(t.apply(this,arguments),h),l=u.length,s=-1;for(let t of i)for(o=0,++s;o<l;++o)(u[o][s]=[0,+n(t,u[o].key,s,i)]).data=t;for(o=0,a=(0,c.Z)(e(u));o<l;++o)u[a[o]].index=o;return r(u,a),u}return i.keys=function(e){return arguments.length?(t="function"==typeof e?e:(0,l.Z)(Array.from(e)),i):t},i.value=function(t){return arguments.length?(n="function"==typeof t?t:(0,l.Z)(+t),i):n},i.order=function(t){return arguments.length?(e=null==t?s:"function"==typeof t?t:(0,l.Z)(Array.from(t)),i):e},i.offset=function(t){return arguments.length?(r=null==t?u:t,i):r},i})().keys(e).value((t,e)=>+g(t,e,0)).order(s).offset(n)(t)};function S(t){return null==t?void 0:String(t)}var M=t=>{var{axis:e,ticks:r,offset:n,bandSize:i,entry:o,index:a}=t;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var u=g(o,e.dataKey,e.scale.domain()[a]);return(0,d.Rw)(u)?null:e.scale(u)-i/2+n},k=t=>{var{numericAxis:e}=t,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},T=t=>{var e=t.flat(2).filter(d.hj);return[Math.min(...e),Math.max(...e)]},C=t=>[t[0]===1/0?0:t[0],t[1]===-1/0?0:t[1]],_=(t,e,r)=>{if(null!=t)return C(Object.keys(t).reduce((n,i)=>{var{stackedData:o}=t[i],a=o.reduce((t,n)=>{var i=T(n.slice(e,r+1));return[Math.min(t[0],i[0]),Math.max(t[1],i[1])]},[1/0,-1/0]);return[Math.min(a[0],n[0]),Math.max(a[1],n[1])]},[1/0,-1/0]))},D=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,N=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,I=(t,e,r)=>{if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=i()(e,t=>t.coordinate),a=1/0,u=1,c=o.length;u<c;u++){var l=o[u],s=o[u-1];a=Math.min((l.coordinate||0)-(s.coordinate||0),a)}return a===1/0?0:a}return r?void 0:0};function L(t){var{tooltipEntrySettings:e,dataKey:r,payload:n,value:i,name:o}=t;return v(v({},e),{},{dataKey:r,payload:n,value:i,name:o})}function R(t,e){return t?String(t):"string"==typeof e?e:void 0}function B(t,e,r,n,i){return"horizontal"===r||"vertical"===r?t>=i.left&&t<=i.left+i.width&&e>=i.top&&e<=i.top+i.height?{x:t,y:e}:null:n?(0,p.z3)({x:t,y:e},n):null}var z=(t,e,r,n)=>{var i=e.find(t=>t&&t.index===r);if(i){if("horizontal"===t)return{x:i.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:i.coordinate};if("centric"===t){var o=i.coordinate,{radius:a}=n;return v(v(v({},n),(0,p.op)(n.cx,n.cy,a,o)),{},{angle:o,radius:a})}var u=i.coordinate,{angle:c}=n;return v(v(v({},n),(0,p.op)(n.cx,n.cy,u,c)),{},{angle:c,radius:u})}return{x:0,y:0}},U=(t,e)=>"horizontal"===e?t.x:"vertical"===e?t.y:"centric"===e?t.angle:t.radius},78487:function(t,e,r){"use strict";r.d(e,{Gh:function(){return n},aN:function(){return i},n9:function(){return o}});var n="data-recharts-item-index",i="data-recharts-item-data-key",o=60},4094:function(t,e,r){"use strict";r.d(e,{x:function(){return l}});var n=r(34067);function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var a={widthCache:{},cacheCount:0},u={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},c="recharts_measurement_span",l=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||n.x.isSsr)return{width:0,height:0};var i=(Object.keys(e=o({},r)).forEach(t=>{e[t]||delete e[t]}),e),l=JSON.stringify({text:t,copyStyle:i});if(a.widthCache[l])return a.widthCache[l];try{var s=document.getElementById(c);s||((s=document.createElement("span")).setAttribute("id",c),s.setAttribute("aria-hidden","true"),document.body.appendChild(s));var f=o(o({},u),i);Object.assign(s.style,f),s.textContent="".concat(t);var h=s.getBoundingClientRect(),d={width:h.width,height:h.height};return a.widthCache[l]=d,++a.cacheCount>2e3&&(a.cacheCount=0,a.widthCache={}),d}catch(t){return{width:0,height:0}}}},16630:function(t,e,r){"use strict";r.d(e,{Ap:function(){return y},EL:function(){return f},In:function(){return a},P2:function(){return l},Rw:function(){return v},bv:function(){return d},h1:function(){return h},hU:function(){return u},hj:function(){return c},jC:function(){return g},k4:function(){return p},uY:function(){return o}});var n=r(15870),i=r.n(n),o=t=>0===t?0:t>0?1:-1,a=t=>"number"==typeof t&&t!=+t,u=t=>"string"==typeof t&&t.indexOf("%")===t.length-1,c=t=>("number"==typeof t||t instanceof Number)&&!a(t),l=t=>c(t)||"string"==typeof t,s=0,f=t=>{var e=++s;return"".concat(t||"").concat(e)},h=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!c(t)&&"string"!=typeof t)return n;if(u(t)){if(null==e)return n;var o=t.indexOf("%");r=e*parseFloat(t.slice(0,o))/100}else r=+t;return a(r)&&(r=n),i&&null!=e&&r>e&&(r=e),r},d=t=>{if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1},p=(t,e)=>c(t)&&c(e)?r=>t+r*(e-t):()=>e;function y(t,e,r){if(t&&t.length)return t.find(t=>t&&("function"==typeof e?e(t):i()(t,e))===r)}var v=t=>null==t,g=t=>v(t)?t:"".concat(t.charAt(0).toUpperCase()).concat(t.slice(1))},34067:function(t,e,r){"use strict";r.d(e,{x:function(){return n}});var n={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout)}},1175:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});var n=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]}},39206:function(t,e,r){"use strict";function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function i(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}r.d(e,{$4:function(){return c},Wk:function(){return o},op:function(){return u},z3:function(){return d}}),r(2265);var o=Math.PI/180,a=t=>180*t/Math.PI,u=(t,e,r,n)=>({x:t+Math.cos(-o*n)*r,y:e+Math.sin(-o*n)*r}),c=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},l=(t,e)=>{var{x:r,y:n}=t,{x:i,y:o}=e;return Math.sqrt((r-i)**2+(n-o)**2)},s=(t,e)=>{var{x:r,y:n}=t,{cx:i,cy:o}=e,u=l({x:r,y:n},{x:i,y:o});if(u<=0)return{radius:u,angle:0};var c=Math.acos((r-i)/u);return n>o&&(c=2*Math.PI-c),{radius:u,angle:a(c),angleInRadian:c}},f=t=>{var{startAngle:e,endAngle:r}=t,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},h=(t,e)=>{var{startAngle:r,endAngle:n}=e;return t+360*Math.min(Math.floor(r/360),Math.floor(n/360))},d=(t,e)=>{var r,{x:n,y:o}=t,{radius:a,angle:u}=s({x:n,y:o},e),{innerRadius:c,outerRadius:l}=e;if(a<c||a>l||0===a)return null;var{startAngle:d,endAngle:p}=f(e),y=u;if(d<=p){for(;y>p;)y-=360;for(;y<d;)y+=360;r=y>=d&&y<=p}else{for(;y>d;)y-=360;for(;y<p;)y+=360;r=y>=p&&y<=d}return r?i(i({},e),{},{radius:a,angle:h(y,e)}):null}},82944:function(t,e,r){"use strict";r.d(e,{L6:function(){return y},NN:function(){return d}});var n=r(15870),i=r.n(n),o=r(2265),a=r(84851),u=r(16630),c=r(41637),l=t=>"string"==typeof t?t:t?t.displayName||t.name||"Component":"",s=null,f=null,h=t=>{if(t===s&&Array.isArray(f))return f;var e=[];return o.Children.forEach(t,t=>{(0,u.Rw)(t)||((0,a.isFragment)(t)?e=e.concat(h(t.props.children)):e.push(t))}),f=e,s=t,e};function d(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(t=>l(t)):[l(e)],h(t).forEach(t=>{var e=i()(t,"type.displayName")||i()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}var p=(t,e,r,n)=>{var i,o=null!==(i=n&&(null===c.ry||void 0===c.ry?void 0:c.ry[n]))&&void 0!==i?i:[];return e.startsWith("data-")||"function"!=typeof t&&(n&&o.includes(e)||c.Yh.includes(e))||r&&c.nv.includes(e)},y=(t,e,r)=>{if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,o.isValidElement)(t)&&(n=t.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(t=>{var o;p(null===(o=n)||void 0===o?void 0:o[t],t,e,r)&&(i[t]=n[t])}),i}},13169:function(t,e,r){"use strict";r.d(e,{a:function(){return n}});var n=t=>{var e=t.currentTarget.getBoundingClientRect(),r=e.width/t.currentTarget.offsetWidth,n=e.height/t.currentTarget.offsetHeight;return{chartX:Math.round((t.clientX-e.left)/r),chartY:Math.round((t.clientY-e.top)/n)}}},66395:function(t,e,r){"use strict";function n(t){return Number.isFinite(t)}function i(t){return"number"==typeof t&&t>0&&Number.isFinite(t)}r.d(e,{n:function(){return n},r:function(){return i}})},40130:function(t,e,r){"use strict";function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function i(t,e){var r=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return Object.keys(e).reduce((t,r)=>(void 0===t[r]&&void 0!==e[r]&&(t[r]=e[r]),t),r)}r.d(e,{j:function(){return i}})},41637:function(t,e,r){"use strict";r.d(e,{Yh:function(){return i},Ym:function(){return c},bw:function(){return s},nv:function(){return u},ry:function(){return a}});var n=r(2265),i=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],o=["points","pathLength"],a={svg:["viewBox","children"],polygon:o,polyline:o},u=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],c=(t,e)=>{if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,n.isValidElement)(t)&&(r=t.props),"object"!=typeof r&&"function"!=typeof r)return null;var i={};return Object.keys(r).forEach(t=>{u.includes(t)&&(i[t]=e||(e=>r[t](r,e)))}),i},l=(t,e,r)=>n=>(t(e,r,n),null),s=(t,e,r)=>{if(null===t||"object"!=typeof t&&"function"!=typeof t)return null;var n=null;return Object.keys(t).forEach(i=>{var o=t[i];u.includes(i)&&"function"==typeof o&&(n||(n={}),n[i]=l(o,e,r))}),n}},59087:function(t,e,r){"use strict";r.d(e,{i:function(){return o}});var n=r(2265),i=r(16630);function o(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,n.useRef)((0,i.EL)(e)),o=(0,n.useRef)(t);return o.current!==t&&(r.current=(0,i.EL)(e),o.current=t),r.current}},24369:function(t,e,r){"use strict";var n=r(2265),i="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},o=n.useState,a=n.useEffect,u=n.useLayoutEffect,c=n.useDebugValue;function l(t){var e=t.getSnapshot;t=t.value;try{var r=e();return!i(t,r)}catch(t){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,e){var r=e(),n=o({inst:{value:r,getSnapshot:e}}),i=n[0].inst,s=n[1];return u(function(){i.value=r,i.getSnapshot=e,l(i)&&s({inst:i})},[t,r,e]),a(function(){return l(i)&&s({inst:i}),t(function(){l(i)&&s({inst:i})})},[t]),c(r),r};e.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},92860:function(t,e,r){"use strict";var n=r(2265),i=r(82558),o="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},a=i.useSyncExternalStore,u=n.useRef,c=n.useEffect,l=n.useMemo,s=n.useDebugValue;e.useSyncExternalStoreWithSelector=function(t,e,r,n,i){var f=u(null);if(null===f.current){var h={hasValue:!1,value:null};f.current=h}else h=f.current;var d=a(t,(f=l(function(){function t(t){if(!c){if(c=!0,a=t,t=n(t),void 0!==i&&h.hasValue){var e=h.value;if(i(e,t))return u=e}return u=t}if(e=u,o(a,t))return e;var r=n(t);return void 0!==i&&i(e,r)?(a=t,e):(a=t,u=r)}var a,u,c=!1,l=void 0===r?null:r;return[function(){return t(e())},null===l?void 0:function(){return t(l())}]},[e,r,n,i]))[0],f[1]);return c(function(){h.hasValue=!0,h.value=d},[d]),s(d),d}},95274:function(t,e,r){"use strict";var n=r(2265);n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},82558:function(t,e,r){"use strict";t.exports=r(24369)},35195:function(t,e,r){"use strict";t.exports=r(92860)},76548:function(t,e,r){"use strict";r(95274)},25566:function(t){var e,r,n,i=t.exports={};function o(){throw Error("setTimeout has not been defined")}function a(){throw Error("clearTimeout has not been defined")}function u(t){if(e===setTimeout)return setTimeout(t,0);if((e===o||!e)&&setTimeout)return e=setTimeout,setTimeout(t,0);try{return e(t,0)}catch(r){try{return e.call(null,t,0)}catch(r){return e.call(this,t,0)}}}!function(){try{e="function"==typeof setTimeout?setTimeout:o}catch(t){e=o}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(t){r=a}}();var c=[],l=!1,s=-1;function f(){l&&n&&(l=!1,n.length?c=n.concat(c):s=-1,c.length&&h())}function h(){if(!l){var t=u(f);l=!0;for(var e=c.length;e;){for(n=c,c=[];++s<e;)n&&n[s].run();s=-1,e=c.length}n=null,l=!1,function(t){if(r===clearTimeout)return clearTimeout(t);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(t);try{r(t)}catch(e){try{return r.call(null,t)}catch(e){return r.call(this,t)}}}(t)}}function d(t,e){this.fun=t,this.array=e}function p(){}i.nextTick=function(t){var e=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];c.push(new d(t,e)),1!==c.length||l||u(h)},d.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=p,i.addListener=p,i.once=p,i.off=p,i.removeListener=p,i.removeAllListeners=p,i.emit=p,i.prependListener=p,i.prependOnceListener=p,i.listeners=function(t){return[]},i.binding=function(t){throw Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw Error("process.chdir is not supported")},i.umask=function(){return 0}},39129:function(t,e,r){"use strict";r.d(e,{xC:function(){return g},PH:function(){return l},e:function(){return tn},oM:function(){return O}});var n,i=r(59688);function o(t){return({dispatch:e,getState:r})=>n=>i=>"function"==typeof i?i(e,r,t):n(i)}var a=o(),u=r(10418);r(25566);var c="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?i.qC:i.qC.apply(null,arguments)};"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;function l(t,e){function r(...n){if(e){let r=e(...n);if(!r)throw Error(ti(0));return{type:t,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:t,payload:n[0]}}return r.toString=()=>`${t}`,r.type=t,r.match=e=>(0,i.LG)(e)&&e.type===t,r}var s=class t extends Array{constructor(...e){super(...e),Object.setPrototypeOf(this,t.prototype)}static get[Symbol.species](){return t}concat(...t){return super.concat.apply(this,t)}prepend(...e){return 1===e.length&&Array.isArray(e[0])?new t(...e[0].concat(this)):new t(...e.concat(this))}};function f(t){return(0,u.o$)(t)?(0,u.Uy)(t,()=>{}):t}function h(t,e,r){return t.has(e)?t.get(e):t.set(e,r(e)).get(e)}var d=()=>function(t){let{thunk:e=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:i=!0}=t??{},u=new s;return e&&("boolean"==typeof e?u.push(a):u.push(o(e.extraArgument))),u},p=t=>e=>{setTimeout(e,t)},y=(t={type:"raf"})=>e=>(...r)=>{let n=e(...r),i=!0,o=!1,a=!1,u=new Set,c="tick"===t.type?queueMicrotask:"raf"===t.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:p(10):"callback"===t.type?t.queueNotification:p(t.timeout),l=()=>{a=!1,o&&(o=!1,u.forEach(t=>t()))};return Object.assign({},n,{subscribe(t){let e=n.subscribe(()=>i&&t());return u.add(t),()=>{e(),u.delete(t)}},dispatch(t){try{return(o=!(i=!t?.meta?.RTK_autoBatch))&&!a&&(a=!0,c(l)),n.dispatch(t)}finally{i=!0}}})},v=t=>function(e){let{autoBatch:r=!0}=e??{},n=new s(t);return r&&n.push(y("object"==typeof r?r:void 0)),n};function g(t){let e,r;let n=d(),{reducer:o,middleware:a,devTools:u=!0,duplicateMiddlewareCheck:l=!0,preloadedState:s,enhancers:f}=t||{};if("function"==typeof o)e=o;else if((0,i.PO)(o))e=(0,i.UY)(o);else throw Error(ti(1));r="function"==typeof a?a(n):n();let h=i.qC;u&&(h=c({trace:!1,..."object"==typeof u&&u}));let p=v((0,i.md)(...r)),y=h(..."function"==typeof f?f(p):p());return(0,i.MT)(e,s,y)}function m(t){let e;let r={},n=[],i={addCase(t,e){let n="string"==typeof t?t:t.type;if(!n)throw Error(ti(28));if(n in r)throw Error(ti(29));return r[n]=e,i},addMatcher:(t,e)=>(n.push({matcher:t,reducer:e}),i),addDefaultCase:t=>(e=t,i)};return t(i),[r,n,e]}var b=(t=21)=>{let e="",r=t;for(;r--;)e+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return e},w=Symbol.for("rtk-slice-createasyncthunk"),x=((n=x||{}).reducer="reducer",n.reducerWithPrepare="reducerWithPrepare",n.asyncThunk="asyncThunk",n),O=function({creators:t}={}){let e=t?.asyncThunk?.[w];return function(t){let r;let{name:n,reducerPath:i=n}=t;if(!n)throw Error(ti(11));let o=("function"==typeof t.reducers?t.reducers(function(){function t(t,e){return{_reducerDefinitionType:"asyncThunk",payloadCreator:t,...e}}return t.withTypes=()=>t,{reducer:t=>Object.assign({[t.name]:(...e)=>t(...e)}[t.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(t,e)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:t,reducer:e}),asyncThunk:t}}()):t.reducers)||{},a=Object.keys(o),c={},s={},d={},p=[],y={addCase(t,e){let r="string"==typeof t?t:t.type;if(!r)throw Error(ti(12));if(r in s)throw Error(ti(13));return s[r]=e,y},addMatcher:(t,e)=>(p.push({matcher:t,reducer:e}),y),exposeAction:(t,e)=>(d[t]=e,y),exposeCaseReducer:(t,e)=>(c[t]=e,y)};function v(){let[e={},r=[],n]="function"==typeof t.extraReducers?m(t.extraReducers):[t.extraReducers],i={...e,...s};return function(t,e){let r;let[n,i,o]=m(e);if("function"==typeof t)r=()=>f(t());else{let e=f(t);r=()=>e}function a(t=r(),e){let a=[n[e.type],...i.filter(({matcher:t})=>t(e)).map(({reducer:t})=>t)];return 0===a.filter(t=>!!t).length&&(a=[o]),a.reduce((t,r)=>{if(r){if((0,u.mv)(t)){let n=r(t,e);return void 0===n?t:n}if((0,u.o$)(t))return(0,u.Uy)(t,t=>r(t,e));{let n=r(t,e);if(void 0===n){if(null===t)return t;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}}return t},t)}return a.getInitialState=r,a}(t.initialState,t=>{for(let e in i)t.addCase(e,i[e]);for(let e of p)t.addMatcher(e.matcher,e.reducer);for(let e of r)t.addMatcher(e.matcher,e.reducer);n&&t.addDefaultCase(n)})}a.forEach(r=>{let i=o[r],a={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof t.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:t,reducerName:e},r,n,i){if(!i)throw Error(ti(18));let{payloadCreator:o,fulfilled:a,pending:u,rejected:c,settled:l,options:s}=r,f=i(t,o,s);n.exposeAction(e,f),a&&n.addCase(f.fulfilled,a),u&&n.addCase(f.pending,u),c&&n.addCase(f.rejected,c),l&&n.addMatcher(f.settled,l),n.exposeCaseReducer(e,{fulfilled:a||P,pending:u||P,rejected:c||P,settled:l||P})}(a,i,y,e):function({type:t,reducerName:e,createNotation:r},n,i){let o,a;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(ti(17));o=n.reducer,a=n.prepare}else o=n;i.addCase(t,o).exposeCaseReducer(e,o).exposeAction(e,a?l(t,a):l(t))}(a,i,y)});let g=t=>t,b=new Map,w=new WeakMap;function x(t,e){return r||(r=v()),r(t,e)}function O(){return r||(r=v()),r.getInitialState()}function j(e,r=!1){function n(t){let i=t[e];return void 0===i&&r&&(i=h(w,n,O)),i}function i(e=g){let n=h(b,r,()=>new WeakMap);return h(n,e,()=>{let n={};for(let[i,o]of Object.entries(t.selectors??{}))n[i]=function(t,e,r,n){function i(o,...a){let u=e(o);return void 0===u&&n&&(u=r()),t(u,...a)}return i.unwrapped=t,i}(o,e,()=>h(w,e,O),r);return n})}return{reducerPath:e,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let A={name:n,reducer:x,actions:d,caseReducers:c,getInitialState:O,...j(i),injectInto(t,{reducerPath:e,...r}={}){let n=e??i;return t.inject({reducerPath:n,reducer:x},r),{...A,...j(n,!0)}}};return A}}();function P(){}var j="listener",A="completed",E="cancelled",S=`task-${E}`,M=`task-${A}`,k=`${j}-${E}`,T=`${j}-${A}`,C=class{constructor(t){this.code=t,this.message=`task ${E} (reason: ${t})`}name="TaskAbortError";message},_=(t,e)=>{if("function"!=typeof t)throw TypeError(ti(32))},D=()=>{},N=(t,e=D)=>(t.catch(e),t),I=(t,e)=>(t.addEventListener("abort",e,{once:!0}),()=>t.removeEventListener("abort",e)),L=(t,e)=>{let r=t.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:e,configurable:!0,writable:!0}),t.abort(e))},R=t=>{if(t.aborted){let{reason:e}=t;throw new C(e)}};function B(t,e){let r=D;return new Promise((n,i)=>{let o=()=>i(new C(t.reason));if(t.aborted){o();return}r=I(t,o),e.finally(()=>r()).then(n,i)}).finally(()=>{r=D})}var z=async(t,e)=>{try{await Promise.resolve();let e=await t();return{status:"ok",value:e}}catch(t){return{status:t instanceof C?"cancelled":"rejected",error:t}}finally{e?.()}},U=t=>e=>N(B(t,e).then(e=>(R(t),e))),$=t=>{let e=U(t);return t=>e(new Promise(e=>setTimeout(e,t)))},{assign:K}=Object,F={},W="listenerMiddleware",Y=(t,e)=>{let r=e=>I(t,()=>L(e,t.reason));return(n,i)=>{_(n,"taskExecutor");let o=new AbortController;r(o);let a=z(async()=>{R(t),R(o.signal);let e=await n({pause:U(o.signal),delay:$(o.signal),signal:o.signal});return R(o.signal),e},()=>L(o,M));return i?.autoJoin&&e.push(a.catch(D)),{result:U(t)(a),cancel(){L(o,S)}}}},V=(t,e)=>{let r=async(r,n)=>{R(e);let i=()=>{},o=[new Promise((e,n)=>{let o=t({predicate:r,effect:(t,r)=>{r.unsubscribe(),e([t,r.getState(),r.getOriginalState()])}});i=()=>{o(),n()}})];null!=n&&o.push(new Promise(t=>setTimeout(t,n,null)));try{let t=await B(e,Promise.race(o));return R(e),t}finally{i()}};return(t,e)=>N(r(t,e))},Z=t=>{let{type:e,actionCreator:r,matcher:n,predicate:i,effect:o}=t;if(e)i=l(e).match;else if(r)e=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(ti(21));return _(o,"options.listener"),{predicate:i,type:e,effect:o}},q=K(t=>{let{type:e,predicate:r,effect:n}=Z(t);return{id:b(),effect:n,type:e,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(ti(22))}}},{withTypes:()=>q}),H=(t,e)=>{let{type:r,effect:n,predicate:i}=Z(e);return Array.from(t.values()).find(t=>("string"==typeof r?t.type===r:t.predicate===i)&&t.effect===n)},X=t=>{t.pending.forEach(t=>{L(t,k)})},G=t=>()=>{t.forEach(X),t.clear()},Q=(t,e,r)=>{try{t(e,r)}catch(t){setTimeout(()=>{throw t},0)}},J=K(l(`${W}/add`),{withTypes:()=>J}),tt=l(`${W}/removeAll`),te=K(l(`${W}/remove`),{withTypes:()=>te}),tr=(...t)=>{console.error(`${W}/error`,...t)},tn=(t={})=>{let e=new Map,{extra:r,onError:n=tr}=t;_(n,"onError");let o=t=>(t.unsubscribe=()=>e.delete(t.id),e.set(t.id,t),e=>{t.unsubscribe(),e?.cancelActive&&X(t)}),a=t=>o(H(e,t)??q(t));K(a,{withTypes:()=>a});let u=t=>{let r=H(e,t);return r&&(r.unsubscribe(),t.cancelActive&&X(r)),!!r};K(u,{withTypes:()=>u});let c=async(t,i,o,u)=>{let c=new AbortController,l=V(a,c.signal),s=[];try{t.pending.add(c),await Promise.resolve(t.effect(i,K({},o,{getOriginalState:u,condition:(t,e)=>l(t,e).then(Boolean),take:l,delay:$(c.signal),pause:U(c.signal),extra:r,signal:c.signal,fork:Y(c.signal,s),unsubscribe:t.unsubscribe,subscribe:()=>{e.set(t.id,t)},cancelActiveListeners:()=>{t.pending.forEach((t,e,r)=>{t!==c&&(L(t,k),r.delete(t))})},cancel:()=>{L(c,k),t.pending.delete(c)},throwIfCancelled:()=>{R(c.signal)}})))}catch(t){t instanceof C||Q(n,t,{raisedBy:"effect"})}finally{await Promise.all(s),L(c,T),t.pending.delete(c)}},l=G(e);return{middleware:t=>r=>o=>{let s;if(!(0,i.LG)(o))return r(o);if(J.match(o))return a(o.payload);if(tt.match(o)){l();return}if(te.match(o))return u(o.payload);let f=t.getState(),h=()=>{if(f===F)throw Error(ti(23));return f};try{if(s=r(o),e.size>0){let r=t.getState();for(let i of Array.from(e.values())){let e=!1;try{e=i.predicate(o,r,f)}catch(t){e=!1,Q(n,t,{raisedBy:"predicate"})}e&&c(i,o,t,h)}}}finally{f=F}return s},startListening:a,stopListening:u,clearListeners:l}};function ti(t){return`Minified Redux Toolkit error #${t}; visit https://redux-toolkit.js.org/Errors?code=${t} for the full message or use the non-minified dev environment for full errors. `}Symbol.for("rtk-state-proxy-original")},22516:function(t,e,r){"use strict";function n(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}r.d(e,{Z:function(){return n}}),Array.prototype.slice},76115:function(t,e,r){"use strict";function n(t){return function(){return t}}r.d(e,{Z:function(){return n}})},67790:function(t,e,r){"use strict";r.d(e,{d:function(){return c}});let n=Math.PI,i=2*n,o=i-1e-6;function a(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class u{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?a:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return a;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,i,o){this._append`C${+t},${+e},${+r},${+n},${this._x1=+i},${this._y1=+o}`}arcTo(t,e,r,i,o){if(t=+t,e=+e,r=+r,i=+i,(o=+o)<0)throw Error(`negative radius: ${o}`);let a=this._x1,u=this._y1,c=r-t,l=i-e,s=a-t,f=u-e,h=s*s+f*f;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(h>1e-6){if(Math.abs(f*c-l*s)>1e-6&&o){let d=r-a,p=i-u,y=c*c+l*l,v=Math.sqrt(y),g=Math.sqrt(h),m=o*Math.tan((n-Math.acos((y+h-(d*d+p*p))/(2*v*g)))/2),b=m/g,w=m/v;Math.abs(b-1)>1e-6&&this._append`L${t+b*s},${e+b*f}`,this._append`A${o},${o},0,0,${+(f*d>s*p)},${this._x1=t+w*c},${this._y1=e+w*l}`}else this._append`L${this._x1=t},${this._y1=e}`}}arc(t,e,r,a,u,c){if(t=+t,e=+e,c=!!c,(r=+r)<0)throw Error(`negative radius: ${r}`);let l=r*Math.cos(a),s=r*Math.sin(a),f=t+l,h=e+s,d=1^c,p=c?a-u:u-a;null===this._x1?this._append`M${f},${h}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-h)>1e-6)&&this._append`L${f},${h}`,r&&(p<0&&(p=p%i+i),p>o?this._append`A${r},${r},0,1,${d},${t-l},${e-s}A${r},${r},0,1,${d},${this._x1=f},${this._y1=h}`:p>1e-6&&this._append`A${r},${r},0,${+(p>=n)},${d},${this._x1=t+r*Math.cos(u)},${this._y1=e+r*Math.sin(u)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function c(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new u(e)}u.prototype},10418:function(t,e,r){"use strict";r.d(e,{Uy:function(){return W},Vk:function(){return K},cA:function(){return Y},mv:function(){return l},o$:function(){return s}});var n,i=Symbol.for("immer-nothing"),o=Symbol.for("immer-draftable"),a=Symbol.for("immer-state");function u(t,...e){throw Error(`[Immer] minified error nr: ${t}. Full error at: https://bit.ly/3cXEKWf`)}var c=Object.getPrototypeOf;function l(t){return!!t&&!!t[a]}function s(t){return!!t&&(h(t)||Array.isArray(t)||!!t[o]||!!t.constructor?.[o]||g(t)||m(t))}var f=Object.prototype.constructor.toString();function h(t){if(!t||"object"!=typeof t)return!1;let e=c(t);if(null===e)return!0;let r=Object.hasOwnProperty.call(e,"constructor")&&e.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===f}function d(t,e){0===p(t)?Reflect.ownKeys(t).forEach(r=>{e(r,t[r],t)}):t.forEach((r,n)=>e(n,r,t))}function p(t){let e=t[a];return e?e.type_:Array.isArray(t)?1:g(t)?2:m(t)?3:0}function y(t,e){return 2===p(t)?t.has(e):Object.prototype.hasOwnProperty.call(t,e)}function v(t,e,r){let n=p(t);2===n?t.set(e,r):3===n?t.add(r):t[e]=r}function g(t){return t instanceof Map}function m(t){return t instanceof Set}function b(t){return t.copy_||t.base_}function w(t,e){if(g(t))return new Map(t);if(m(t))return new Set(t);if(Array.isArray(t))return Array.prototype.slice.call(t);let r=h(t);if(!0!==e&&("class_only"!==e||r)){let e=c(t);return null!==e&&r?{...t}:Object.assign(Object.create(e),t)}{let e=Object.getOwnPropertyDescriptors(t);delete e[a];let r=Reflect.ownKeys(e);for(let n=0;n<r.length;n++){let i=r[n],o=e[i];!1===o.writable&&(o.writable=!0,o.configurable=!0),(o.get||o.set)&&(e[i]={configurable:!0,writable:!0,enumerable:o.enumerable,value:t[i]})}return Object.create(c(t),e)}}function x(t,e=!1){return P(t)||l(t)||!s(t)||(p(t)>1&&(t.set=t.add=t.clear=t.delete=O),Object.freeze(t),e&&Object.entries(t).forEach(([t,e])=>x(e,!0))),t}function O(){u(2)}function P(t){return Object.isFrozen(t)}var j={};function A(t){let e=j[t];return e||u(0,t),e}function E(t,e){e&&(A("Patches"),t.patches_=[],t.inversePatches_=[],t.patchListener_=e)}function S(t){M(t),t.drafts_.forEach(T),t.drafts_=null}function M(t){t===n&&(n=t.parent_)}function k(t){return n={drafts_:[],parent_:n,immer_:t,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function T(t){let e=t[a];0===e.type_||1===e.type_?e.revoke_():e.revoked_=!0}function C(t,e){e.unfinalizedDrafts_=e.drafts_.length;let r=e.drafts_[0];return void 0!==t&&t!==r?(r[a].modified_&&(S(e),u(4)),s(t)&&(t=_(e,t),e.parent_||N(e,t)),e.patches_&&A("Patches").generateReplacementPatches_(r[a].base_,t,e.patches_,e.inversePatches_)):t=_(e,r,[]),S(e),e.patches_&&e.patchListener_(e.patches_,e.inversePatches_),t!==i?t:void 0}function _(t,e,r){if(P(e))return e;let n=e[a];if(!n)return d(e,(i,o)=>D(t,n,e,i,o,r)),e;if(n.scope_!==t)return e;if(!n.modified_)return N(t,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let e=n.copy_,i=e,o=!1;3===n.type_&&(i=new Set(e),e.clear(),o=!0),d(i,(i,a)=>D(t,n,e,i,a,r,o)),N(t,e,!1),r&&t.patches_&&A("Patches").generatePatches_(n,r,t.patches_,t.inversePatches_)}return n.copy_}function D(t,e,r,n,i,o,a){if(l(i)){let a=_(t,i,o&&e&&3!==e.type_&&!y(e.assigned_,n)?o.concat(n):void 0);if(v(r,n,a),!l(a))return;t.canAutoFreeze_=!1}else a&&r.add(i);if(s(i)&&!P(i)){if(!t.immer_.autoFreeze_&&t.unfinalizedDrafts_<1)return;_(t,i),(!e||!e.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&N(t,i)}}function N(t,e,r=!1){!t.parent_&&t.immer_.autoFreeze_&&t.canAutoFreeze_&&x(e,r)}var I={get(t,e){if(e===a)return t;let r=b(t);if(!y(r,e))return function(t,e,r){let n=B(e,r);return n?"value"in n?n.value:n.get?.call(t.draft_):void 0}(t,r,e);let n=r[e];return t.finalized_||!s(n)?n:n===R(t.base_,e)?(U(t),t.copy_[e]=$(n,t)):n},has:(t,e)=>e in b(t),ownKeys:t=>Reflect.ownKeys(b(t)),set(t,e,r){let n=B(b(t),e);if(n?.set)return n.set.call(t.draft_,r),!0;if(!t.modified_){let n=R(b(t),e),i=n?.[a];if(i&&i.base_===r)return t.copy_[e]=r,t.assigned_[e]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||y(t.base_,e)))return!0;U(t),z(t)}return!!(t.copy_[e]===r&&(void 0!==r||e in t.copy_)||Number.isNaN(r)&&Number.isNaN(t.copy_[e]))||(t.copy_[e]=r,t.assigned_[e]=!0,!0)},deleteProperty:(t,e)=>(void 0!==R(t.base_,e)||e in t.base_?(t.assigned_[e]=!1,U(t),z(t)):delete t.assigned_[e],t.copy_&&delete t.copy_[e],!0),getOwnPropertyDescriptor(t,e){let r=b(t),n=Reflect.getOwnPropertyDescriptor(r,e);return n?{writable:!0,configurable:1!==t.type_||"length"!==e,enumerable:n.enumerable,value:r[e]}:n},defineProperty(){u(11)},getPrototypeOf:t=>c(t.base_),setPrototypeOf(){u(12)}},L={};function R(t,e){let r=t[a];return(r?b(r):t)[e]}function B(t,e){if(!(e in t))return;let r=c(t);for(;r;){let t=Object.getOwnPropertyDescriptor(r,e);if(t)return t;r=c(r)}}function z(t){!t.modified_&&(t.modified_=!0,t.parent_&&z(t.parent_))}function U(t){t.copy_||(t.copy_=w(t.base_,t.scope_.immer_.useStrictShallowCopy_))}function $(t,e){let r=g(t)?A("MapSet").proxyMap_(t,e):m(t)?A("MapSet").proxySet_(t,e):function(t,e){let r=Array.isArray(t),i={type_:r?1:0,scope_:e?e.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:e,base_:t,draft_:null,copy_:null,revoke_:null,isManual_:!1},o=i,a=I;r&&(o=[i],a=L);let{revoke:u,proxy:c}=Proxy.revocable(o,a);return i.draft_=c,i.revoke_=u,c}(t,e);return(e?e.scope_:n).drafts_.push(r),r}function K(t){return l(t)||u(10,t),function t(e){let r;if(!s(e)||P(e))return e;let n=e[a];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=w(e,n.scope_.immer_.useStrictShallowCopy_)}else r=w(e,!0);return d(r,(e,n)=>{v(r,e,t(n))}),n&&(n.finalized_=!1),r}(t)}d(I,(t,e)=>{L[t]=function(){return arguments[0]=arguments[0][0],e.apply(this,arguments)}}),L.deleteProperty=function(t,e){return L.set.call(this,t,e,void 0)},L.set=function(t,e,r){return I.set.call(this,t[0],e,r,t[0])};var F=new class{constructor(t){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(t,e,r)=>{let n;if("function"==typeof t&&"function"!=typeof e){let r=e;e=t;let n=this;return function(t=r,...i){return n.produce(t,t=>e.call(this,t,...i))}}if("function"!=typeof e&&u(6),void 0!==r&&"function"!=typeof r&&u(7),s(t)){let i=k(this),o=$(t,void 0),a=!0;try{n=e(o),a=!1}finally{a?S(i):M(i)}return E(i,r),C(n,i)}if(t&&"object"==typeof t)u(1,t);else{if(void 0===(n=e(t))&&(n=t),n===i&&(n=void 0),this.autoFreeze_&&x(n,!0),r){let e=[],i=[];A("Patches").generateReplacementPatches_(t,n,e,i),r(e,i)}return n}},this.produceWithPatches=(t,e)=>{let r,n;return"function"==typeof t?(e,...r)=>this.produceWithPatches(e,e=>t(e,...r)):[this.produce(t,e,(t,e)=>{r=t,n=e}),r,n]},"boolean"==typeof t?.autoFreeze&&this.setAutoFreeze(t.autoFreeze),"boolean"==typeof t?.useStrictShallowCopy&&this.setUseStrictShallowCopy(t.useStrictShallowCopy)}createDraft(t){s(t)||u(8),l(t)&&(t=K(t));let e=k(this),r=$(t,void 0);return r[a].isManual_=!0,M(e),r}finishDraft(t,e){let r=t&&t[a];r&&r.isManual_||u(9);let{scope_:n}=r;return E(n,e),C(void 0,n)}setAutoFreeze(t){this.autoFreeze_=t}setUseStrictShallowCopy(t){this.useStrictShallowCopy_=t}applyPatches(t,e){let r;for(r=e.length-1;r>=0;r--){let n=e[r];if(0===n.path.length&&"replace"===n.op){t=n.value;break}}r>-1&&(e=e.slice(r+1));let n=A("Patches").applyPatches_;return l(t)?n(t,e):this.produce(t,t=>n(t,e))}},W=F.produce;function Y(t){return t}F.produceWithPatches.bind(F),F.setAutoFreeze.bind(F),F.setUseStrictShallowCopy.bind(F),F.applyPatches.bind(F),F.createDraft.bind(F),F.finishDraft.bind(F)},59688:function(t,e,r){"use strict";function n(t){return`Minified Redux error #${t}; visit https://redux.js.org/Errors?code=${t} for the full message or use the non-minified dev environment for full errors. `}r.d(e,{LG:function(){return h},MT:function(){return c},PO:function(){return u},UY:function(){return l},md:function(){return f},qC:function(){return s}});var i="function"==typeof Symbol&&Symbol.observable||"@@observable",o=()=>Math.random().toString(36).substring(7).split("").join("."),a={INIT:`@@redux/INIT${o()}`,REPLACE:`@@redux/REPLACE${o()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${o()}`};function u(t){if("object"!=typeof t||null===t)return!1;let e=t;for(;null!==Object.getPrototypeOf(e);)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e||null===Object.getPrototypeOf(t)}function c(t,e,r){if("function"!=typeof t)throw Error(n(2));if("function"==typeof e&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw Error(n(0));if("function"==typeof e&&void 0===r&&(r=e,e=void 0),void 0!==r){if("function"!=typeof r)throw Error(n(1));return r(c)(t,e)}let o=t,l=e,s=new Map,f=s,h=0,d=!1;function p(){f===s&&(f=new Map,s.forEach((t,e)=>{f.set(e,t)}))}function y(){if(d)throw Error(n(3));return l}function v(t){if("function"!=typeof t)throw Error(n(4));if(d)throw Error(n(5));let e=!0;p();let r=h++;return f.set(r,t),function(){if(e){if(d)throw Error(n(6));e=!1,p(),f.delete(r),s=null}}}function g(t){if(!u(t))throw Error(n(7));if(void 0===t.type)throw Error(n(8));if("string"!=typeof t.type)throw Error(n(17));if(d)throw Error(n(9));try{d=!0,l=o(l,t)}finally{d=!1}return(s=f).forEach(t=>{t()}),t}return g({type:a.INIT}),{dispatch:g,subscribe:v,getState:y,replaceReducer:function(t){if("function"!=typeof t)throw Error(n(10));o=t,g({type:a.REPLACE})},[i]:function(){return{subscribe(t){if("object"!=typeof t||null===t)throw Error(n(11));function e(){t.next&&t.next(y())}return e(),{unsubscribe:v(e)}},[i](){return this}}}}}function l(t){let e;let r=Object.keys(t),i={};for(let e=0;e<r.length;e++){let n=r[e];"function"==typeof t[n]&&(i[n]=t[n])}let o=Object.keys(i);try{!function(t){Object.keys(t).forEach(e=>{let r=t[e];if(void 0===r(void 0,{type:a.INIT}))throw Error(n(12));if(void 0===r(void 0,{type:a.PROBE_UNKNOWN_ACTION()}))throw Error(n(13))})}(i)}catch(t){e=t}return function(t={},r){if(e)throw e;let a=!1,u={};for(let e=0;e<o.length;e++){let c=o[e],l=i[c],s=t[c],f=l(s,r);if(void 0===f)throw r&&r.type,Error(n(14));u[c]=f,a=a||f!==s}return(a=a||o.length!==Object.keys(t).length)?u:t}}function s(...t){return 0===t.length?t=>t:1===t.length?t[0]:t.reduce((t,e)=>(...r)=>t(e(...r)))}function f(...t){return e=>(r,i)=>{let o=e(r,i),a=()=>{throw Error(n(15))},u={getState:o.getState,dispatch:(t,...e)=>a(t,...e)};return a=s(...t.map(t=>t(u)))(o.dispatch),{...o,dispatch:a}}}function h(t){return u(t)&&"type"in t&&"string"==typeof t.type}},92713:function(t,e,r){"use strict";r.d(e,{P1:function(){return w}});var n=t=>Array.isArray(t)?t:[t],i=0,o=class{revision=i;_value;_lastValue;_isEqual=a;constructor(t,e=a){this._value=this._lastValue=t,this._isEqual=e}get value(){return this._value}set value(t){this.value!==t&&(this._value=t,this.revision=++i)}};function a(t,e){return t===e}function u(t){return t instanceof o||console.warn("Not a valid cell! ",t),t.value}var c=(t,e)=>!1;function l(){return function(t,e=a){return new o(null,e)}(0,c)}var s=t=>{let e=t.collectionTag;null===e&&(e=t.collectionTag=l()),u(e)};Symbol();var f=0,h=Object.getPrototypeOf({}),d=class{constructor(t){this.value=t,this.value=t,this.tag.value=t}proxy=new Proxy(this,p);tag=l();tags={};children={};collectionTag=null;id=f++},p={get:(t,e)=>(function(){let{value:r}=t,n=Reflect.get(r,e);if("symbol"==typeof e||e in h)return n;if("object"==typeof n&&null!==n){let r=t.children[e];return void 0===r&&(r=t.children[e]=Array.isArray(n)?new y(n):new d(n)),r.tag&&u(r.tag),r.proxy}{let r=t.tags[e];return void 0===r&&((r=t.tags[e]=l()).value=n),u(r),n}})(),ownKeys:t=>(s(t),Reflect.ownKeys(t.value)),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t.value,e),has:(t,e)=>Reflect.has(t.value,e)},y=class{constructor(t){this.value=t,this.value=t,this.tag.value=t}proxy=new Proxy([this],v);tag=l();tags={};children={};collectionTag=null;id=f++},v={get:([t],e)=>("length"===e&&s(t),p.get(t,e)),ownKeys:([t])=>p.ownKeys(t),getOwnPropertyDescriptor:([t],e)=>p.getOwnPropertyDescriptor(t,e),has:([t],e)=>p.has(t,e)},g="undefined"!=typeof WeakRef?WeakRef:class{constructor(t){this.value=t}deref(){return this.value}};function m(){return{s:0,v:void 0,o:null,p:null}}function b(t,e={}){let r,n=m(),{resultEqualityCheck:i}=e,o=0;function a(){let e,a=n,{length:u}=arguments;for(let t=0;t<u;t++){let e=arguments[t];if("function"==typeof e||"object"==typeof e&&null!==e){let t=a.o;null===t&&(a.o=t=new WeakMap);let r=t.get(e);void 0===r?(a=m(),t.set(e,a)):a=r}else{let t=a.p;null===t&&(a.p=t=new Map);let r=t.get(e);void 0===r?(a=m(),t.set(e,a)):a=r}}let c=a;if(1===a.s)e=a.v;else if(e=t.apply(null,arguments),o++,i){let t=r?.deref?.()??r;null!=t&&i(t,e)&&(e=t,0!==o&&o--),r="object"==typeof e&&null!==e||"function"==typeof e?new g(e):e}return c.s=1,c.v=e,e}return a.clearCache=()=>{n=m(),a.resetResultsCount()},a.resultsCount=()=>o,a.resetResultsCount=()=>{o=0},a}var w=function(t,...e){let r="function"==typeof t?{memoize:t,memoizeOptions:e}:t,i=(...t)=>{let e,i=0,o=0,a={},u=t.pop();"object"==typeof u&&(a=u,u=t.pop()),function(t,e=`expected a function, instead received ${typeof t}`){if("function"!=typeof t)throw TypeError(e)}(u,`createSelector expects an output function after the inputs, but received: [${typeof u}]`);let{memoize:c,memoizeOptions:l=[],argsMemoize:s=b,argsMemoizeOptions:f=[],devModeChecks:h={}}={...r,...a},d=n(l),p=n(f),y=function(t){let e=Array.isArray(t[0])?t[0]:t;return!function(t,e="expected all items to be functions, instead received the following types: "){if(!t.every(t=>"function"==typeof t)){let r=t.map(t=>"function"==typeof t?`function ${t.name||"unnamed"}()`:typeof t).join(", ");throw TypeError(`${e}[${r}]`)}}(e,"createSelector expects all input-selectors to be functions, but received the following types: "),e}(t),v=c(function(){return i++,u.apply(null,arguments)},...d);return Object.assign(s(function(){o++;let t=function(t,e){let r=[],{length:n}=t;for(let i=0;i<n;i++)r.push(t[i].apply(null,e));return r}(y,arguments);return e=v.apply(null,t)},...p),{resultFunc:u,memoizedResultFunc:v,dependencies:y,dependencyRecomputations:()=>o,resetDependencyRecomputations:()=>{o=0},lastResult:()=>e,recomputations:()=>i,resetRecomputations:()=>{i=0},memoize:c,argsMemoize:s})};return Object.assign(i,{withTypes:()=>i}),i}(b),x=Object.assign((t,e=w)=>{!function(t,e=`expected an object, instead received ${typeof t}`){if("object"!=typeof t)throw TypeError(e)}(t,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof t}`);let r=Object.keys(t);return e(r.map(e=>t[e]),(...t)=>t.reduce((t,e,n)=>(t[r[n]]=e,t),{}))},{withTypes:()=>x})}}]);