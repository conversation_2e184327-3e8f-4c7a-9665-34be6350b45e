/**
 * 地址处理工具函数
 * 用于智能截取和显示地址信息
 */

/**
 * 智能截取地址，只显示市和县/区级别的信息
 * @param fullAddress 完整地址
 * @returns 截取后的地址
 */
export function formatAddressForDisplay(fullAddress: string): string {
  if (!fullAddress || typeof fullAddress !== 'string') {
    return '';
  }

  // 移除开头的#号（如果存在）
  let address = fullAddress.replace(/^#/, '').trim();
  
  if (!address) {
    return '';
  }

  // 定义地址级别的正则表达式
  const patterns = {
    // 省级：省、自治区、直辖市、特别行政区
    province: /(.*?(?:省|自治区|市|特别行政区))/,
    // 市级：市、地区、州、盟
    city: /(.*?(?:市|地区|州|盟|区))/,
    // 县级：县、区、市、旗、自治县
    county: /(.*?(?:县|区|市|旗|自治县|林区|特区))/,
    // 镇级：镇、乡、街道、办事处
    town: /(.*?(?:镇|乡|街道|办事处|社区))/
  };

  try {
    // 使用更精确的分割方法
    let province = '';
    let city = '';
    let county = '';

    // 先提取省份
    const provinceMatch = address.match(/(.*?(?:省|自治区|特别行政区))/);
    if (provinceMatch) {
      province = provinceMatch[1];
      address = address.replace(province, '');
    }

    // 再提取市（包括直辖市的情况）
    const cityMatch = address.match(/(.*?市)/);
    if (cityMatch) {
      city = cityMatch[1];
      address = address.replace(city, '');
    }

    // 最后提取县/区
    const countyMatch = address.match(/(.*?(?:县|区|市|旗|自治县|林区|特区))/);
    if (countyMatch) {
      county = countyMatch[1];
    }

    // 智能组合显示逻辑
    let result = '';

    // 特殊处理直辖市的情况
    const isDirectMunicipality = ['北京', '上海', '天津', '重庆'].some(dm =>
      (province && province.includes(dm)) || (city && city.includes(dm))
    );

    // 特殊的新区，需要保留市名
    const specialNewAreas = ['浦东新区', '滨海新区', '两江新区'];
    const hasSpecialNewArea = county && specialNewAreas.some(area => county.includes(area));

    if (isDirectMunicipality) {
      // 直辖市：特殊新区保留市名，其他区只显示区名
      if (hasSpecialNewArea && city) {
        result = city + county;
      } else if (county) {
        result = county;
      } else if (city) {
        result = city;
      } else if (province) {
        result = province;
      }
    } else {
      // 非直辖市：优先显示市+县的组合
      if (city && county) {
        result = city + county;
      } else if (city) {
        result = city;
      } else if (county) {
        result = county;
      } else if (province) {
        result = province;
      }
    }

    // 如果都没有省市县信息，尝试提取镇村信息
    if (!result) {
      const originalAddress = fullAddress.replace(/^#/, '').trim();

      // 尝试提取镇级信息
      const townMatch = originalAddress.match(/(.*?(?:镇|乡|街道|办事处))/);
      if (townMatch) {
        result = townMatch[1];
      } else {
        // 如果没有镇级信息，检查是否有村级信息
        const villageMatch = originalAddress.match(/(.*?(?:村|社区|小区|路|街|巷|弄|号))/);
        if (villageMatch) {
          // 如果地址太长，只取前面的部分
          const village = villageMatch[1];
          result = village.length > 8 ? village.substring(0, 8) + '...' : village;
        } else {
          // 都没有，返回原地址的前10个字符
          result = originalAddress.length > 10 ? originalAddress.substring(0, 10) + '...' : originalAddress;
        }
      }
    }

    return result;
  } catch (error) {
    console.error('地址格式化出错:', error);
    // 出错时返回原地址的前10个字符
    return address.length > 10 ? address.substring(0, 10) + '...' : address;
  }
}

/**
 * 获取完整地址用于详情页显示
 * @param fullAddress 完整地址
 * @returns 清理后的完整地址
 */
export function getFullAddress(fullAddress: string): string {
  if (!fullAddress || typeof fullAddress !== 'string') {
    return '';
  }

  // 移除开头的#号（如果存在）
  return fullAddress.replace(/^#/, '').trim();
}

/**
 * 验证地址格式
 * @param address 地址字符串
 * @returns 是否为有效地址
 */
export function isValidAddress(address: string): boolean {
  if (!address || typeof address !== 'string') {
    return false;
  }

  const cleanAddress = address.replace(/^#/, '').trim();
  return cleanAddress.length > 0 && cleanAddress.length <= 200;
}

/**
 * 地址示例和测试用例
 */
export const addressExamples = {
  // 测试用例
  testCases: [
    {
      input: '湖南省郴州市临武县南强镇渣塘村',
      expected: '郴州市临武县'
    },
    {
      input: '北京市朝阳区',
      expected: '朝阳区'
    },
    {
      input: '上海市浦东新区',
      expected: '上海市浦东新区'
    },
    {
      input: '广东省深圳市南山区',
      expected: '深圳市南山区'
    },
    {
      input: '江苏省南京市',
      expected: '南京市'
    },
    {
      input: '浙江省杭州市西湖区文三路',
      expected: '杭州市西湖区'
    },
    {
      input: '#湖南省长沙市',
      expected: '长沙市'
    },
    // 边缘情况测试
    {
      input: '南强镇渣塘村',
      expected: '南强镇'
    },
    {
      input: '渣塘村',
      expected: '渣塘村'
    },
    {
      input: '南强镇',
      expected: '南强镇'
    },
    {
      input: '某某街道办事处',
      expected: '某某街道'
    },
    {
      input: '某某社区',
      expected: '某某社区'
    }
  ]
};
