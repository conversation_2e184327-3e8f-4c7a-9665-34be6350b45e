import React, { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import PetCard from './PetCard';
import { PetCardSkeleton, Loading } from '@/components/ui/Loading';
import AdContainer from '@/components/AdContainer';
import { usePostsData } from '@/hooks/usePostsData';
import { FilterState } from '@/hooks/usePostFilters';

interface PetGridProps {
  filters: FilterState;
}

const PetGrid: React.FC<PetGridProps> = ({ filters }) => {
  // 使用优化的数据获取Hook
  const {
    posts,
    loading,
    loadingMore,
    error,
    hasMore,
    loadMore,
    refresh,
    totalCount
  } = usePostsData({ filters });

  // 无限滚动检测
  const { ref, inView } = useInView({
    threshold: 0,
    rootMargin: '100px',
  });

  // 监听无限滚动
  useEffect(() => {
    if (inView && hasMore && !loading && !loadingMore) {
      loadMore();
    }
  }, [inView, hasMore, loading, loadingMore, loadMore]);

  // 重试函数
  const handleRetry = () => {
    refresh();
  };

  // 错误状态
  if (error && posts.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 mb-4">
          <p className="text-lg font-medium">加载失败</p>
          <p className="text-sm">{error}</p>
        </div>
        <button
          onClick={handleRetry}
          className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
        >
          重试
        </button>
      </div>
    );
  }

  // 加载状态
  if (loading && posts.length === 0) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {Array.from({ length: 20 }).map((_, index) => (
          <PetCardSkeleton key={index} />
        ))}
      </div>
    );
  }

  // 空状态
  if (!loading && posts.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500">
          <div className="text-6xl mb-4">🐾</div>
          <p className="text-lg font-medium mb-2">暂无宠物信息</p>
          <p className="text-sm">
            {filters.category ? '该分类下暂无宠物，试试其他分类吧' : '还没有人发布宠物，快来成为第一个吧！'}
          </p>
        </div>
      </div>
    );
  }

  // 渲染宠物卡片和广告的混合内容
  const renderContent = () => {
    const content = [];
    const adInterval = 6; // 每6个宠物卡片插入一个广告

    for (let i = 0; i < posts.length; i++) {
      // 添加宠物卡片
      content.push(
        <PetCard
          key={posts[i]._id}
          post={posts[i]}
        />
      );

      // 每隔adInterval个卡片插入一个广告
      if ((i + 1) % adInterval === 0 && i < posts.length - 1) {
        content.push(
          <AdContainer
            key={`ad-${i}`}
            positionId="home_feed"
            className="col-span-2 md:col-span-1"
          />
        );
      }
    }

    return content;
  };

  return (
    <div className="space-y-6">
      {/* 宠物网格 */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {renderContent()}
      </div>

      {/* 加载更多指示器 */}
      {hasMore && (
        <div ref={ref} className="flex justify-center py-8">
          {loadingMore ? (
            <Loading size="sm" text="加载更多..." />
          ) : (
            <div className="text-gray-400 text-sm">滑动到底部加载更多</div>
          )}
        </div>
      )}

      {/* 没有更多数据提示 */}
      {!hasMore && posts.length > 0 && (
        <div className="text-center py-8 text-gray-400 text-sm">
          已显示全部内容
        </div>
      )}
    </div>
  );
};

export default PetGrid;
