'use client';

import { useState, useEffect } from 'react';
import { petAPI } from '@/lib/cloudbase';
import { 
  EyeIcon, 
  PencilIcon, 
  TrashIcon,
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EyeSlashIcon,
  StarIcon,
  FlagIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface Post {
  _id: string;
  title: string;
  description: string;
  images: string[];
  user_id: string;
  username: string;
  priority_level: 'high' | 'normal' | 'low' | 'hidden';
  quality_score: number;
  priority_reason?: string;
  created_at: string;
  likes: number;
  comments: number;
  views: number;
  hasAds?: boolean;
  isPromotional?: boolean;
  reportCount?: number;
  violationLevel?: 'minor' | 'moderate' | 'severe';
  current_quality_score?: number;
  score_breakdown?: {
    content: number;
    user: number;
    engagement: number;
    time: number;
    penalty: number;
  };
}

export default function PostsManagement() {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPosts, setSelectedPosts] = useState<string[]>([]);
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [showScores, setShowScores] = useState(false);
  const [batchAction, setBatchAction] = useState<string>('');

  useEffect(() => {
    loadPosts();
  }, [filterPriority, showScores]);

  const loadPosts = async () => {
    setLoading(true);
    try {
      const result = await petAPI.getPostsByPriority({
        priority_level: filterPriority as any,
        limit: 50,
        include_scores: showScores
      });
      
      if (result.success) {
        setPosts(result.data || []);
      }
    } catch (error) {
      console.error('加载帖子失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const updatePostPriority = async (postId: string, priorityLevel: string, reason?: string) => {
    try {
      const result = await petAPI.updatePostPriority({
        post_id: postId,
        priority_level: priorityLevel as any,
        reason: reason
      });
      
      if (result.success) {
        await loadPosts(); // 重新加载列表
      }
    } catch (error: any) {
      console.error('更新帖子优先级失败:', error);
      alert('更新失败：' + (error?.message || '未知错误'));
    }
  };

  const handleBatchAction = async () => {
    if (selectedPosts.length === 0 || !batchAction) return;
    
    const reason = prompt('请输入操作原因（可选）：');
    
    try {
      const result = await petAPI.batchUpdatePostPriority({
        post_ids: selectedPosts,
        priority_level: batchAction as any,
        reason: reason || undefined
      });
      
      if (result.success) {
        alert(`批量操作完成：成功 ${result.data.successful} 个，失败 ${result.data.failed} 个`);
        setSelectedPosts([]);
        setBatchAction('');
        await loadPosts();
      }
    } catch (error: any) {
      console.error('批量操作失败:', error);
      alert('批量操作失败：' + (error?.message || '未知错误'));
    }
  };

  const handleBatchUpdateQuality = async () => {
    if (!confirm('确定要批量更新所有帖子的质量评分吗？这可能需要一些时间。')) {
      return;
    }

    try {
      setLoading(true);
      const result = await petAPI.batchUpdateAllPostsQuality();

      if (result.success) {
        alert(result.data.message);
        await loadPosts(); // 重新加载列表
      }
    } catch (error: any) {
      console.error('批量更新质量评分失败:', error);
      alert('批量更新失败：' + (error?.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  const getPriorityBadge = (priority: string) => {
    const config = {
      high: { label: '高优先级', color: 'bg-red-100 text-red-800', icon: ArrowUpIcon },
      normal: { label: '普通', color: 'bg-green-100 text-green-800', icon: StarIcon },
      low: { label: '低优先级', color: 'bg-yellow-100 text-yellow-800', icon: ArrowDownIcon },
      hidden: { label: '已隐藏', color: 'bg-gray-100 text-gray-800', icon: EyeSlashIcon }
    };
    
    const cfg = config[priority as keyof typeof config] || config.normal;
    const IconComponent = cfg.icon;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${cfg.color}`}>
        <IconComponent className="h-3 w-3 mr-1" />
        {cfg.label}
      </span>
    );
  };

  const getQualityScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">帖子优先级管理</h1>
          <p className="text-gray-600">管理帖子展示优先级和内容质量</p>
        </div>
        <div className="flex items-center space-x-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={showScores}
              onChange={(e) => setShowScores(e.target.checked)}
              className="mr-2"
            />
            显示详细评分
          </label>
        </div>
      </div>

      {/* 筛选和批量操作 */}
      <div className="bg-white p-4 rounded-lg shadow flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <select
            value={filterPriority}
            onChange={(e) => setFilterPriority(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="all">全部优先级</option>
            <option value="high">高优先级</option>
            <option value="normal">普通</option>
            <option value="low">低优先级</option>
            <option value="hidden">已隐藏</option>
          </select>
          
          {selectedPosts.length > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">
                已选择 {selectedPosts.length} 个帖子
              </span>
              <select
                value={batchAction}
                onChange={(e) => setBatchAction(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="">选择批量操作</option>
                <option value="high">设为高优先级</option>
                <option value="normal">设为普通</option>
                <option value="low">设为低优先级</option>
                <option value="hidden">隐藏帖子</option>
              </select>
              <button
                onClick={handleBatchAction}
                disabled={!batchAction}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                执行
              </button>
            </div>
          )}

          <button
            onClick={handleBatchUpdateQuality}
            disabled={loading}
            className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 disabled:opacity-50 flex items-center space-x-2"
          >
            <StarIcon className="w-4 h-4" />
            <span>{loading ? '更新中...' : '批量更新质量评分'}</span>
          </button>
        </div>
      </div>

      {/* 帖子列表 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">帖子列表</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    checked={selectedPosts.length === posts.length && posts.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedPosts(posts.map(p => p._id));
                      } else {
                        setSelectedPosts([]);
                      }
                    }}
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  帖子信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  优先级
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  质量评分
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  数据统计
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态标识
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {posts.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-12 text-center text-gray-500">
                    暂无帖子数据
                  </td>
                </tr>
              ) : (
                posts.map((post) => (
                  <tr key={post._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedPosts.includes(post._id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedPosts([...selectedPosts, post._id]);
                          } else {
                            setSelectedPosts(selectedPosts.filter(id => id !== post._id));
                          }
                        }}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {post.images && post.images.length > 0 && (
                          <img
                            src={post.images[0]}
                            alt={post.title}
                            className="h-10 w-10 rounded object-cover mr-3"
                          />
                        )}
                        <div>
                          <div className="text-sm font-medium text-gray-900 max-w-xs truncate">
                            {post.title || post.description?.substring(0, 50) + '...'}
                          </div>
                          <div className="text-sm text-gray-500">@{post.username}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getPriorityBadge(post.priority_level || 'normal')}
                      {post.priority_reason && (
                        <div className="text-xs text-gray-500 mt-1">
                          {post.priority_reason}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-lg font-bold ${getQualityScoreColor(post.current_quality_score || post.quality_score || 50)}`}>
                        {post.current_quality_score || post.quality_score || 50}
                      </div>
                      {showScores && post.score_breakdown && (
                        <div className="text-xs text-gray-500 space-y-1">
                          <div>内容: {post.score_breakdown.content}</div>
                          <div>用户: {post.score_breakdown.user}</div>
                          <div>互动: {post.score_breakdown.engagement}</div>
                          <div>时效: {post.score_breakdown.time}</div>
                          <div>扣分: {post.score_breakdown.penalty}</div>
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>👍 {post.likes || 0}</div>
                      <div>💬 {post.comments || 0}</div>
                      <div>👁️ {post.views || 0}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col space-y-1">
                        {post.hasAds && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800">
                            📢 含广告
                          </span>
                        )}
                        {post.reportCount && post.reportCount > 0 && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
                            🚨 {post.reportCount} 举报
                          </span>
                        )}
                        {post.violationLevel && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800">
                            ⚠️ {post.violationLevel}
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <select
                          value={post.priority_level || 'normal'}
                          onChange={(e) => {
                            const reason = prompt('请输入调整原因（可选）：');
                            updatePostPriority(post._id, e.target.value, reason || undefined);
                          }}
                          className="text-xs border border-gray-300 rounded px-2 py-1"
                        >
                          <option value="high">高优先级</option>
                          <option value="normal">普通</option>
                          <option value="low">低优先级</option>
                          <option value="hidden">隐藏</option>
                        </select>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
