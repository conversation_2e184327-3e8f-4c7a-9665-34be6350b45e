'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { petAPI } from '@/lib/cloudbase';
import { showToast } from '@/components/ui/Toast';
import { ArrowLeft, MessageCircle, AlertTriangle, Copy, ExternalLink, CheckCircle, Clock, XCircle } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import Button from '@/components/ui/Button';
import AppealButton from '@/components/ui/AppealButton';

interface Notification {
  _id: string;
  type: 'contact' | 'system';
  recipient_id: string;
  sender_id: string;
  post_id?: string;
  message: string;
  data: any;
  is_read: boolean;
  created_at: string;
}

const NotificationsPage = () => {
  const { isLoggedIn, user } = useAuth();
  const router = useRouter();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'contact' | 'system'>('contact');

  // 加载通知数据
  const loadNotifications = async () => {
    if (!isLoggedIn) return;

    try {
      setLoading(true);
      const result = await petAPI.getUserNotifications({
        limit: 50,
        type: activeTab
      });

      if (result.success) {
        setNotifications(result.data || []);
      } else {
        showToast.error(result.message || '加载通知失败');
      }
    } catch (error: any) {
      console.error('加载通知失败:', error);
      showToast.error('加载通知失败');
    } finally {
      setLoading(false);
    }
  };

  // 复制联系方式到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      showToast.success('已复制到剪贴板');
    } catch (error) {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      showToast.success('已复制到剪贴板');
    }
  };

  // 标记通知为已读
  const markAsRead = async (notificationId: string) => {
    try {
      await petAPI.markNotificationRead({ notificationId });
      setNotifications(prev =>
        prev.map(n => n._id === notificationId ? { ...n, is_read: true } : n)
      );
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  };
  useEffect(() => {
    loadNotifications();
  }, [isLoggedIn, activeTab]);

  // 获取通知图标
  const getNotificationIcon = (notification: Notification) => {
    if (notification.type === 'contact') {
      return <MessageCircle className="w-5 h-5 text-blue-500" />;
    } else if (notification.type === 'system') {
      if (notification.data?.report_type) {
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      }
      return <CheckCircle className="w-5 h-5 text-green-500" />;
    }
    return <MessageCircle className="w-5 h-5 text-gray-500" />;
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  if (!isLoggedIn) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">请先登录</h2>
          <Link href="/login">
            <Button>去登录</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-md mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-lg font-semibold">消息通知</h1>
            <div className="w-9" />
          </div>
        </div>
      </div>

      {/* 标签页 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-md mx-auto px-4">
          <div className="flex">
            <button
              onClick={() => setActiveTab('contact')}
              className={`flex-1 py-3 text-center border-b-2 transition-colors ${
                activeTab === 'contact'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500'
              }`}
            >
              联系通知
            </button>
            <button
              onClick={() => setActiveTab('system')}
              className={`flex-1 py-3 text-center border-b-2 transition-colors ${
                activeTab === 'system'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500'
              }`}
            >
              系统通知
            </button>
          </div>
        </div>
      </div>

      {/* 通知列表 */}
      <div className="max-w-md mx-auto">
        {loading ? (
          <div className="text-center py-12">
            <div className="text-gray-400">加载中...</div>
          </div>
        ) : notifications.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <MessageCircle className="w-16 h-16 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              暂无通知
            </h3>
            <p className="text-gray-600">
              有新消息时会显示在这里
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {notifications.map((notification) => (
              <div
                key={notification._id}
                className={`p-4 bg-white hover:bg-gray-50 transition-colors ${
                  !notification.is_read ? 'bg-blue-50' : ''
                }`}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">
                    {getNotificationIcon(notification.type)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900 mb-1">
                      {notification.message}
                    </p>
                    
                    {notification.data?.post_title && (
                      <p className="text-xs text-gray-500 mb-2">
                        帖子：{notification.data.post_title}
                      </p>
                    )}
                    
                    {notification.type === 'contact' && (
                      <div className="text-xs bg-blue-50 rounded p-2 mb-2">
                        {/* 显示发送者联系方式 */}
                        {notification.data?.sender_contact && (
                          <div className="text-blue-700 mb-1">
                            <span className="font-medium">对方联系方式：</span>
                            {notification.data.sender_contact.type === 'phone' && (
                              <span>📱 {notification.data.sender_contact.value}</span>
                            )}
                            {notification.data.sender_contact.type === 'wechat' && (
                              <span>💬 {notification.data.sender_contact.value}</span>
                            )}
                            {notification.data.sender_contact.type === 'qq' && (
                              <span>🐧 {notification.data.sender_contact.value}</span>
                            )}
                          </div>
                        )}

                        {/* 显示作者联系方式 */}
                        {notification.data?.author_contact && (
                          <div className="text-blue-700">
                            <span className="font-medium">对方联系方式：</span>
                            {notification.data.author_contact.type === 'phone' && (
                              <span>📱 {notification.data.author_contact.value}</span>
                            )}
                            {notification.data.author_contact.type === 'wechat' && (
                              <span>💬 {notification.data.author_contact.value}</span>
                            )}
                            {notification.data.author_contact.type === 'qq' && (
                              <span>🐧 {notification.data.author_contact.value}</span>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                    
                    <p className="text-xs text-gray-400">
                      {formatTime(notification.created_at)}
                    </p>
                  </div>
                  
                  <div className="flex-shrink-0 flex items-center space-x-2">
                    {!notification.is_read && (
                      <button
                        onClick={() => markAsRead(notification._id)}
                        className="p-1 text-blue-500 hover:text-blue-600"
                        title="标记为已读"
                      >
                        <Check className="w-4 h-4" />
                      </button>
                    )}
                    
                    <button
                      onClick={() => deleteNotification(notification._id)}
                      className="p-1 text-red-500 hover:text-red-600"
                      title="删除"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationsPage;
