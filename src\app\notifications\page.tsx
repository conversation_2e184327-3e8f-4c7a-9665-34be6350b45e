'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { petAPI } from '@/lib/cloudbase';
import { showToast } from '@/components/ui/Toast';
import { ArrowLeft, MessageCircle, AlertTriangle, Copy, ExternalLink, CheckCircle, Clock, XCircle } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import Button from '@/components/ui/Button';
import AppealButton from '@/components/ui/AppealButton';

interface Notification {
  _id: string;
  type: 'contact' | 'system';
  recipient_id: string;
  sender_id: string;
  post_id?: string;
  message: string;
  data: any;
  is_read: boolean;
  created_at: string;
}

const NotificationsPage = () => {
  const { isLoggedIn, user } = useAuth();
  const router = useRouter();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'all' | 'contact' | 'breeding' | 'lost'>('contact');

  // 加载通知数据
  const loadNotifications = async () => {
    if (!isLoggedIn) return;

    try {
      setLoading(true);
      const result = await petAPI.getUserNotifications({
        limit: 50,
        type: activeTab === 'all' ? undefined : undefined // 获取所有通知，然后前端过滤
      });

      if (result.success) {
        let filteredNotifications = result.data || [];

        // 根据选项卡过滤通知
        if (activeTab === 'contact') {
          // 买卖通知：出售和求购类型的帖子
          filteredNotifications = filteredNotifications.filter(
            (n: Notification) => n.type === 'contact' &&
            (n.data?.post_type === 'selling' || n.data?.post_type === 'wanted')
          );
        } else if (activeTab === 'breeding') {
          // 配种通知：配种类型的帖子
          filteredNotifications = filteredNotifications.filter(
            (n: Notification) => n.type === 'contact' && n.data?.post_type === 'breeding'
          );
        } else if (activeTab === 'lost') {
          // 寻回通知：寻回类型的帖子
          filteredNotifications = filteredNotifications.filter(
            (n: Notification) => n.type === 'contact' && n.data?.post_type === 'lost'
          );
        }
        // 'all' 标签页显示所有通知（包括系统通知）

        setNotifications(filteredNotifications);
      } else {
        showToast.error(result.message || '加载通知失败');
      }
    } catch (error: any) {
      console.error('加载通知失败:', error);
      showToast.error('加载通知失败');
    } finally {
      setLoading(false);
    }
  };

  // 复制联系方式到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      showToast.success('已复制到剪贴板');
    } catch (error) {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      showToast.success('已复制到剪贴板');
    }
  };

  // 标记通知为已读
  const markAsRead = async (notificationId: string) => {
    try {
      await petAPI.markNotificationRead({ notificationId });
      setNotifications(prev =>
        prev.map(n => n._id === notificationId ? { ...n, is_read: true } : n)
      );
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  };
  useEffect(() => {
    loadNotifications();
  }, [isLoggedIn, activeTab]);

  // 获取通知图标
  const getNotificationIcon = (notification: Notification) => {
    if (notification.type === 'contact') {
      return <MessageCircle className="w-5 h-5 text-blue-500" />;
    } else if (notification.type === 'system') {
      if (notification.data?.report_type) {
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      }
      return <CheckCircle className="w-5 h-5 text-green-500" />;
    }
    return <MessageCircle className="w-5 h-5 text-gray-500" />;
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  if (!isLoggedIn) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">请先登录</h2>
          <Link href="/login">
            <Button>去登录</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-md mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-lg font-semibold">消息通知</h1>
            <div className="w-9" />
          </div>
        </div>
      </div>

      {/* 标签页 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-md mx-auto px-4">
          <div className="flex">
            <button
              onClick={() => setActiveTab('contact')}
              className={`flex-1 py-3 text-center border-b-2 transition-colors text-xs ${
                activeTab === 'contact'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500'
              }`}
            >
              买卖通知
            </button>
            <button
              onClick={() => setActiveTab('breeding')}
              className={`flex-1 py-3 text-center border-b-2 transition-colors text-xs ${
                activeTab === 'breeding'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500'
              }`}
            >
              配种通知
            </button>
            <button
              onClick={() => setActiveTab('lost')}
              className={`flex-1 py-3 text-center border-b-2 transition-colors text-xs ${
                activeTab === 'lost'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500'
              }`}
            >
              寻宠通知
            </button>
            <button
              onClick={() => setActiveTab('all')}
              className={`flex-1 py-3 text-center border-b-2 transition-colors text-xs ${
                activeTab === 'all'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500'
              }`}
            >
              全部通知
            </button>
          </div>
        </div>
      </div>

      {/* 通知列表 */}
      <div className="max-w-md mx-auto">
        {loading ? (
          <div className="text-center py-12">
            <div className="text-gray-400">加载中...</div>
          </div>
        ) : notifications.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <MessageCircle className="w-16 h-16 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              暂无通知
            </h3>
            <p className="text-gray-600">
              有新消息时会显示在这里
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {notifications.map((notification) => (
              <div
                key={notification._id}
                className={`p-4 bg-white hover:bg-gray-50 transition-colors ${
                  !notification.is_read ? 'bg-blue-50' : ''
                }`}
                onClick={() => !notification.is_read && markAsRead(notification._id)}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">
                    {getNotificationIcon(notification)}
                  </div>

                  <div className="flex-1 min-w-0">
                    {/* 联系方式交换通知 */}
                    {notification.type === 'contact' && (
                      <div className="space-y-3">
                        <div className="text-sm text-gray-900">
                          {notification.message}
                        </div>

                        {/* 帖子信息 */}
                        {notification.data?.post_title && (
                          <div className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                            <span className="text-sm text-gray-700">
                              {notification.data.post_title}
                            </span>
                            {notification.post_id && (
                              <Link href={`/post/detail?id=${notification.post_id}`}>
                                <button className="flex items-center space-x-1 text-blue-600 hover:text-blue-700 text-sm">
                                  <ExternalLink className="w-4 h-4" />
                                  <span>查看帖子</span>
                                </button>
                              </Link>
                            )}
                          </div>
                        )}

                        {/* 联系方式信息 */}
                        {notification.data?.contact_info && (
                          <div className="bg-blue-50 rounded-lg p-3">
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-blue-900 font-medium">
                                {notification.data.contact_info}
                              </span>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  copyToClipboard(notification.data.contact_info);
                                }}
                                className="flex items-center space-x-1 text-blue-600 hover:text-blue-700 text-sm"
                              >
                                <Copy className="w-4 h-4" />
                                <span>复制</span>
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* 系统通知 */}
                    {notification.type === 'system' && (
                      <div className="space-y-3">
                        <div className="text-sm text-gray-900">
                          {notification.message}
                        </div>

                        {/* 申诉按钮 */}
                        {notification.data?.can_appeal && (
                          <div className="flex justify-end">
                            <AppealButton />
                          </div>
                        )}
                      </div>
                    )}

                    <div className="flex items-center justify-between mt-3">
                      <p className="text-xs text-gray-400">
                        {formatTime(notification.created_at)}
                      </p>

                      {!notification.is_read && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationsPage;
