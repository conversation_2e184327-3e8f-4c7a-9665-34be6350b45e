{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "/pet-trading-platform", "redirects": [{"source": "/pet-trading-platform", "destination": "/pet-trading-platform/", "basePath": false, "internal": true, "statusCode": 308, "regex": "^/pet-trading-platform$"}, {"source": "/:file((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+)/", "destination": "/:file", "internal": true, "missing": [{"type": "header", "key": "x-nextjs-data"}], "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+))/$"}, {"source": "/:notfile((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+)", "destination": "/:notfile/", "internal": true, "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+))$"}], "headers": [], "dynamicRoutes": [{"page": "/profile/[id]", "regex": "^/profile/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/profile/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/activities", "regex": "^/activities(?:/)?$", "routeKeys": {}, "namedRegex": "^/activities(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/admin/activities", "regex": "^/admin/activities(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/activities(?:/)?$"}, {"page": "/admin/ads", "regex": "^/admin/ads(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/ads(?:/)?$"}, {"page": "/admin/dashboard", "regex": "^/admin/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/dashboard(?:/)?$"}, {"page": "/admin/performance", "regex": "^/admin/performance(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/performance(?:/)?$"}, {"page": "/admin/posts", "regex": "^/admin/posts(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/posts(?:/)?$"}, {"page": "/admin/settings", "regex": "^/admin/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/settings(?:/)?$"}, {"page": "/debug", "regex": "^/debug(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug(?:/)?$"}, {"page": "/favorites", "regex": "^/favorites(?:/)?$", "routeKeys": {}, "namedRegex": "^/favorites(?:/)?$"}, {"page": "/messages", "regex": "^/messages(?:/)?$", "routeKeys": {}, "namedRegex": "^/messages(?:/)?$"}, {"page": "/notifications", "regex": "^/notifications(?:/)?$", "routeKeys": {}, "namedRegex": "^/notifications(?:/)?$"}, {"page": "/post/detail", "regex": "^/post/detail(?:/)?$", "routeKeys": {}, "namedRegex": "^/post/detail(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/test-email", "regex": "^/test\\-email(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-email(?:/)?$"}, {"page": "/upload", "regex": "^/upload(?:/)?$", "routeKeys": {}, "namedRegex": "^/upload(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}