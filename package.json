{"name": "pet-trading-platform", "version": "1.0.0", "description": "🐾 宠物交易平台 - TikTok风格的现代化宠物交易平台", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "deploy": "npm run build && npx @cloudbase/cli hosting deploy out -e yichongyuzhou-3g9112qwf5f3487b"}, "dependencies": {"@cloudbase/cli": "^2.7.8", "@cloudbase/js-sdk": "^2.17.6", "@cloudbase/mcp": "^1.0.0-beta.26", "@cloudbase/node-sdk": "^3.10.1", "@heroicons/react": "^2.2.0", "@modelcontextprotocol/sdk": "^1.16.0", "bcryptjs": "^3.0.2", "clsx": "^2.0.0", "framer-motion": "^10.16.0", "lucide-react": "^0.294.0", "next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.5.0", "recharts": "^3.1.0", "swiper": "^11.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.0", "@tailwindcss/forms": "^0.5.0", "@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "eslint": "^8.51.0", "eslint-config-next": "^14.0.0", "postcss": "^8.4.0", "tailwindcss": "^3.3.0", "typescript": "^5.2.0"}, "keywords": ["pet", "trading", "platform", "tiktok", "nextjs", "cloudbase", "typescript"], "author": "Pet Trading Platform Team", "license": "MIT"}