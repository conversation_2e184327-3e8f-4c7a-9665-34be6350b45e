'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { petAPI, initCloudBase } from '@/lib/cloudbase';
import { showToast } from '@/components/ui/Toast';
import Button from '@/components/ui/Button';
import {
  Shield,
  Flag,
  MessageSquare,
  Users,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  ArrowLeft,
  Eye,
  Plus,
  Edit,
  Trash2,
  Settings,
  BarChart3,
  UserPlus,
  Key,
  Activity,
  Database,
  Globe,
  Image,
  Save,
  RefreshCw
} from 'lucide-react';

interface Appeal {
  _id: string;
  report_id: string;
  appellant_id: string;
  reason: string;
  type: 'post' | 'user';
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  admin_id?: string;
  admin_reason?: string;
  handled_at?: string;
}

interface Admin {
  _id: string;
  username: string;
  role: string;
  level: number;
  permissions: string[];
  status: 'active' | 'suspended' | 'disabled';
  created_at: string;
  created_by: string;
  last_login?: string;
  is_system_account: boolean;
  can_create_admin: boolean;
  can_delete_admin: boolean;
  max_admin_level: number;
}

interface DashboardStats {
  totalUsers: number;
  totalPosts: number;
  totalReports: number;
  totalAppeals: number;
  yesterdayNewUsers: number;
  yesterdayNewPosts: number;
  activeUsers: number;
  onlineUsers: number;
}

export default function AdminDashboard() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'dashboard' | 'admins' | 'appeals' | 'reports' | 'users' | 'ads' | 'posts' | 'activities' | 'settings'>('dashboard');
  const [currentAdmin, setCurrentAdmin] = useState<Admin | null>(null);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [appeals, setAppeals] = useState<Appeal[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [selectedAppeal, setSelectedAppeal] = useState<Appeal | null>(null);
  const [adminReason, setAdminReason] = useState('');
  const [showProcessModal, setShowProcessModal] = useState(false);
  const [processAction, setProcessAction] = useState<'approved' | 'rejected'>('approved');
  const [showCreateAdminModal, setShowCreateAdminModal] = useState(false);
  const [newAdminData, setNewAdminData] = useState({
    username: '',
    password: '',
    role: 'admin',
    level: 1,
    permissions: [] as string[]
  });

  // 帖子管理相关状态
  const [posts, setPosts] = useState<any[]>([]);
  const [postsLoading, setPostsLoading] = useState(false);
  const [showDeletePostModal, setShowDeletePostModal] = useState(false);
  const [selectedPost, setSelectedPost] = useState<any>(null);
  const [deleteReason, setDeleteReason] = useState('');

  // 广告管理相关状态
  const [ads, setAds] = useState<any[]>([]);
  const [adPositions, setAdPositions] = useState<any[]>([]);
  const [adsLoading, setAdsLoading] = useState(false);
  const [activeAdTab, setActiveAdTab] = useState<'ads' | 'positions' | 'statistics'>('ads');
  const [showCreateAdModal, setShowCreateAdModal] = useState(false);

  // 活动管理相关状态
  const [activities, setActivities] = useState<any[]>([]);
  const [activitiesLoading, setActivitiesLoading] = useState(false);
  const [systemConfig, setSystemConfig] = useState<any>(null);
  const [showConfigModal, setShowConfigModal] = useState(false);
  const [showCreateActivityModal, setShowCreateActivityModal] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');

  // 系统设置相关状态
  const [settings, setSettings] = useState<any>({
    maxImageSize: 5,
    maxImagesPerPost: 9,
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp']
  });
  const [settingsLoading, setSettingsLoading] = useState(false);

  // 确认模态框状态
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmConfig, setConfirmConfig] = useState<{
    title: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
    onConfirm: () => void;
    onCancel?: () => void;
    type?: 'danger' | 'warning' | 'info';
  } | null>(null);
  const [settingsSaving, setSettingsSaving] = useState(false);

  // 通用确认函数
  const showConfirm = (config: {
    title: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
    onConfirm: () => void;
    onCancel?: () => void;
    type?: 'danger' | 'warning' | 'info';
  }) => {
    setConfirmConfig(config);
    setShowConfirmModal(true);
  };

  const handleConfirm = () => {
    if (confirmConfig?.onConfirm) {
      confirmConfig.onConfirm();
    }
    setShowConfirmModal(false);
    setConfirmConfig(null);
  };

  const handleCancel = () => {
    if (confirmConfig?.onCancel) {
      confirmConfig.onCancel();
    }
    setShowConfirmModal(false);
    setConfirmConfig(null);
  };

  // 检查管理员权限并加载数据
  useEffect(() => {
    const adminToken = localStorage.getItem('adminToken');
    const adminUser = localStorage.getItem('adminUser');

    if (!adminToken || !adminUser) {
      router.push('/admin');
      return;
    }

    try {
      const admin = JSON.parse(adminUser);
      setCurrentAdmin(admin);
      loadDashboardData();
    } catch (error) {
      console.error('解析管理员信息失败:', error);
      router.push('/admin');
    }
  }, [router]);

  // 加载仪表板数据
  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // 获取真实统计数据
      try {
        // 直接调用云函数
        const cloudbaseApp = await initCloudBase();
        if (!cloudbaseApp) {
          throw new Error('CloudBase未初始化');
        }

        const result = await cloudbaseApp.callFunction({
          name: 'pet-api',
          data: {
            action: 'getDashboardStats',
            data: {}
          }
        });

        if (result.result?.success) {
          setStats(result.result.data);
        } else {
          console.error('获取统计数据失败:', result.result?.message);
          // 使用默认数据
          setStats({
            totalUsers: 0,
            totalPosts: 0,
            totalReports: 0,
            totalAppeals: 0,
            yesterdayNewUsers: 0,
            yesterdayNewPosts: 0,
            activeUsers: 0,
            onlineUsers: 0
          });
        }
      } catch (error) {
        console.error('获取统计数据异常:', error);
        // 使用默认数据
        setStats({
          totalUsers: 0,
          totalPosts: 0,
          totalReports: 0,
          totalAppeals: 0,
          yesterdayNewUsers: 0,
          yesterdayNewPosts: 0,
          activeUsers: 0,
          onlineUsers: 0
        });
      }

      // 尝试加载管理员列表（如果有权限的话）
      try {
        await loadAdmins();
      } catch (error) {
        console.log('无权限加载管理员列表或不是超级管理员');
      }

      // 加载申诉列表
      await loadAppeals();

      // 加载帖子列表
      await loadPosts();

      // 加载广告数据
      await loadAds();

      // 加载活动数据
      await loadActivities();

      // 加载系统设置
      await loadSettings();
    } catch (error: any) {
      console.error('加载仪表板数据失败:', error);
      showToast.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载管理员列表
  const loadAdmins = async () => {
    try {
      const result = await petAPI.getAdmins({
        limit: 50,
        offset: 0
      });

      if (result.success) {
        setAdmins(result.data || []);
      }
    } catch (error: any) {
      console.error('加载管理员列表失败:', error);
    }
  };

  // 加载帖子列表
  const loadPosts = async () => {
    setPostsLoading(true);
    try {
      const result = await petAPI.getPostsForAdmin({ limit: 50 });
      if (result.success) {
        setPosts(result.data);
      }
    } catch (error) {
      console.error('加载帖子列表失败:', error);
      showToast.error('加载帖子列表失败');
    } finally {
      setPostsLoading(false);
    }
  };

  // 加载申诉列表
  const loadAppeals = async () => {
    try {
      const result = await petAPI.getAppeals({
        status: 'all',
        limit: 50,
        offset: 0
      });

      if (result.success) {
        setAppeals(result.data || []);
      }
    } catch (error: any) {
      console.error('加载申诉列表失败:', error);
    }
  };

  // 创建管理员
  const createAdmin = async () => {
    if (!newAdminData.username || !newAdminData.password) {
      showToast.error('请填写用户名和密码');
      return;
    }

    try {
      const result = await petAPI.createAdmin({
        ...newAdminData,
        permissions: ['*'] // 普通管理员也拥有所有权限
      });

      if (result.success) {
        showToast.success('管理员创建成功');
        setShowCreateAdminModal(false);
        setNewAdminData({
          username: '',
          password: '',
          role: 'admin',
          level: 1,
          permissions: []
        });
        loadAdmins();
      } else {
        showToast.error(result.message || '创建失败');
      }
    } catch (error: any) {
      console.error('创建管理员失败:', error);
      showToast.error(error.message || '创建管理员失败');
    }
  };

  // 删除管理员
  const deleteAdmin = async (adminId: string) => {
    showConfirm({
      title: '删除管理员',
      message: '确定要删除这个管理员吗？此操作不可恢复。',
      confirmText: '删除',
      cancelText: '取消',
      type: 'danger',
      onConfirm: async () => {
        try {
          const result = await petAPI.deleteAdmin({ adminId });
          if (result.success) {
            showToast.success('管理员删除成功');
            loadAdmins();
          } else {
            showToast.error(result.message || '删除失败');
          }
        } catch (error: any) {
          console.error('删除管理员失败:', error);
          showToast.error(error.message || '删除管理员失败');
        }
      }
    });
  };

  // 处理申诉
  const handleAppeal = async () => {
    if (!selectedAppeal) return;

    try {
      setProcessingId(selectedAppeal._id);
      const result = await petAPI.handleAppeal({
        appealId: selectedAppeal._id,
        action: processAction,
        adminReason: adminReason
      });

      if (result.success) {
        showToast.success('申诉处理成功');
        setShowProcessModal(false);
        setSelectedAppeal(null);
        setAdminReason('');
        loadAppeals();
      } else {
        showToast.error(result.message || '处理失败');
      }
    } catch (error: any) {
      console.error('处理申诉失败:', error);
      showToast.error(error.message || '处理申诉失败');
    } finally {
      setProcessingId(null);
    }
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 获取状态样式
  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '待处理';
      case 'approved':
        return '已通过';
      case 'rejected':
        return '已驳回';
      default:
        return '未知';
    }
  };

  // 删除帖子
  const deletePost = async () => {
    if (!selectedPost || !deleteReason.trim()) {
      showToast.error('请填写删除原因');
      return;
    }

    try {
      const result = await petAPI.adminDeletePost({
        postId: selectedPost._id,
        reason: deleteReason
      });

      if (result.success) {
        showToast.success('帖子删除成功');
        setShowDeletePostModal(false);
        setSelectedPost(null);
        setDeleteReason('');
        loadPosts(); // 重新加载帖子列表
      } else {
        showToast.error(result.message || '删除失败');
      }
    } catch (error: any) {
      console.error('删除帖子失败:', error);
      showToast.error(error.message || '删除帖子失败');
    }
  };

  // 加载广告数据
  const loadAds = async () => {
    setAdsLoading(true);
    try {
      // 模拟广告数据
      const mockAds = [
        {
          _id: 'ad_001',
          title: '优质宠物用品推荐',
          advertiser_id: 'advertiser_001',
          advertiser_name: '宠物之家商城',
          position_id: 'home_banner',
          position_name: '首页横幅广告',
          ad_type: 'banner',
          content: '为您的爱宠提供最好的生活用品，健康快乐每一天！全场8折优惠中。',
          target_url: 'https://example.com/pet-products',
          start_date: '2025-01-01T00:00:00.000Z',
          end_date: '2025-03-31T23:59:59.000Z',
          status: 'active',
          priority: 1,
          budget: 5000,
          spent: 1250.50,
          impressions: 15420,
          clicks: 342,
          ctr: 0.0222,
          created_at: '2025-01-01T00:00:00.000Z'
        },
        {
          _id: 'ad_002',
          title: '专业宠物医院',
          advertiser_id: 'advertiser_002',
          advertiser_name: '爱宠医疗中心',
          position_id: 'home_feed',
          position_name: '首页信息流广告',
          ad_type: 'feed',
          content: '24小时宠物医疗服务，专业医师团队，让您的爱宠健康无忧。',
          target_url: 'https://example.com/pet-hospital',
          start_date: '2025-01-10T00:00:00.000Z',
          end_date: '2025-02-28T23:59:59.000Z',
          status: 'active',
          priority: 2,
          budget: 3000,
          spent: 890.25,
          impressions: 8750,
          clicks: 156,
          ctr: 0.0178,
          created_at: '2025-01-10T00:00:00.000Z'
        },
        {
          _id: 'ad_003',
          title: '宠物美容服务',
          advertiser_id: 'advertiser_003',
          advertiser_name: '美宠工坊',
          position_id: 'home_feed',
          position_name: '首页信息流广告',
          ad_type: 'feed',
          content: '专业宠物美容，让您的爱宠更加美丽动人。新客户首次服务7折。',
          target_url: 'https://example.com/pet-grooming',
          start_date: '2025-01-15T00:00:00.000Z',
          end_date: '2025-04-15T23:59:59.000Z',
          status: 'paused',
          priority: 3,
          budget: 2000,
          spent: 450.75,
          impressions: 4200,
          clicks: 89,
          ctr: 0.0212,
          created_at: '2025-01-15T00:00:00.000Z'
        }
      ];

      const mockPositions = [
        {
          position_id: 'home_banner',
          name: '首页横幅广告',
          page: 'home',
          location: 'top',
          width: 728,
          height: 90,
          ad_type: 'banner',
          max_ads: 3,
          rotation_interval: 5000,
          status: 'active'
        },
        {
          position_id: 'home_feed',
          name: '首页信息流广告',
          page: 'home',
          location: 'feed',
          width: 300,
          height: 200,
          ad_type: 'feed',
          max_ads: 5,
          rotation_interval: 0,
          status: 'active'
        },
        {
          position_id: 'sidebar_banner',
          name: '侧边栏广告',
          page: 'all',
          location: 'sidebar',
          width: 300,
          height: 250,
          ad_type: 'banner',
          max_ads: 2,
          rotation_interval: 8000,
          status: 'inactive'
        }
      ];

      setAds(mockAds);
      setAdPositions(mockPositions);
    } catch (error) {
      console.error('加载广告数据失败:', error);
      showToast.error('加载广告数据失败');
    } finally {
      setAdsLoading(false);
    }
  };

  // 广告管理辅助函数
  const getAdStatusBadge = (status: string) => {
    const statusConfig = {
      active: { label: '投放中', color: 'bg-green-100 text-green-800' },
      paused: { label: '已暂停', color: 'bg-yellow-100 text-yellow-800' },
      expired: { label: '已过期', color: 'bg-red-100 text-red-800' },
      pending: { label: '待审核', color: 'bg-blue-100 text-blue-800' },
      inactive: { label: '未启用', color: 'bg-gray-100 text-gray-800' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  const formatCurrency = (amount: number) => {
    return `¥${amount.toFixed(2)}`;
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(2)}%`;
  };

  // 加载活动数据
  const loadActivities = async () => {
    setActivitiesLoading(true);
    try {
      // 模拟活动数据
      const mockActivities = [
        {
          _id: 'activity_001',
          title: '最萌宠物评选大赛',
          description: '展示你的宠物，参与最萌宠物评选活动，赢取丰厚奖品！',
          type: 'CONTEST',
          status: 'ACTIVE',
          start_time: '2025-01-01T00:00:00.000Z',
          end_time: '2025-01-31T23:59:59.000Z',
          result_display_end_time: '2025-02-03T23:59:59.000Z',
          duration_days: 31,
          result_display_days: 3,
          config: { comments_enabled: true },
          statistics_summary: {
            total_votes: 1250,
            total_comments: 340
          },
          created_at: '2024-12-25T00:00:00.000Z'
        },
        {
          _id: 'activity_002',
          title: '猫咪 VS 狗狗投票',
          description: '你更喜欢猫咪还是狗狗？快来投票表达你的观点！',
          type: 'VOTING',
          status: 'ACTIVE',
          start_time: '2025-01-15T00:00:00.000Z',
          end_time: '2025-02-15T23:59:59.000Z',
          result_display_end_time: '2025-02-18T23:59:59.000Z',
          duration_days: 31,
          result_display_days: 3,
          config: { comments_enabled: true },
          statistics_summary: {
            total_votes: 890,
            total_comments: 156
          },
          created_at: '2025-01-10T00:00:00.000Z'
        },
        {
          _id: 'activity_003',
          title: '宠物护理经验分享',
          description: '分享你的宠物护理经验，帮助更多宠物主人',
          type: 'DISCUSSION',
          status: 'ENDED',
          start_time: '2024-12-01T00:00:00.000Z',
          end_time: '2024-12-31T23:59:59.000Z',
          result_display_end_time: '2025-01-03T23:59:59.000Z',
          duration_days: 31,
          result_display_days: 3,
          config: { comments_enabled: true },
          statistics_summary: {
            total_votes: 0,
            total_comments: 245
          },
          created_at: '2024-11-25T00:00:00.000Z'
        }
      ];

      setActivities(mockActivities);

      // 模拟系统配置
      const mockSystemConfig = {
        enabled: true,
        comments_enabled: true,
        rate_limit_interval: 10,
        max_comment_length: 100,
        default_result_display_days: 3
      };

      setSystemConfig(mockSystemConfig);
    } catch (error) {
      console.error('加载活动数据失败:', error);
      showToast.error('加载活动数据失败');
    } finally {
      setActivitiesLoading(false);
    }
  };

  // 加载系统设置
  const loadSettings = async () => {
    setSettingsLoading(true);
    try {
      // 从localStorage读取设置，如果没有则使用默认值
      const savedSettings = localStorage.getItem('systemSettings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('加载设置失败:', error);
      showToast.error('加载设置失败');
    } finally {
      setSettingsLoading(false);
    }
  };

  // 活动管理辅助函数
  const getActivityTypeIcon = (type: string) => {
    switch (type) {
      case 'CONTEST':
        return '🏆';
      case 'VOTING':
        return '🗳️';
      case 'DISCUSSION':
        return '💬';
      default:
        return '🏆';
    }
  };

  const getActivityTypeLabel = (type: string) => {
    switch (type) {
      case 'CONTEST':
        return '评选竞赛';
      case 'VOTING':
        return '投票话题';
      case 'DISCUSSION':
        return '讨论活动';
      default:
        return '未知类型';
    }
  };

  const getActivityStatusBadge = (status: string) => {
    const config = {
      DRAFT: { label: '草稿', color: 'bg-yellow-100 text-yellow-800' },
      ACTIVE: { label: '进行中', color: 'bg-green-100 text-green-800' },
      ENDED: { label: '已结束', color: 'bg-blue-100 text-blue-800' },
      ARCHIVED: { label: '已归档', color: 'bg-gray-100 text-gray-800' }
    };

    const cfg = config[status as keyof typeof config] || config.DRAFT;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${cfg.color}`}>
        {cfg.label}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  // 系统设置保存函数
  const saveSettings = async () => {
    try {
      setSettingsSaving(true);

      // 验证设置
      if (settings.maxImageSize <= 0 || settings.maxImageSize > 100) {
        showToast.error('图片大小限制必须在1-100MB之间');
        return;
      }

      if (settings.maxImagesPerPost <= 0 || settings.maxImagesPerPost > 20) {
        showToast.error('每帖图片数量必须在1-20张之间');
        return;
      }

      // 保存到localStorage
      localStorage.setItem('systemSettings', JSON.stringify(settings));

      showToast.success('设置保存成功');
    } catch (error) {
      console.error('保存设置失败:', error);
      showToast.error('保存设置失败');
    } finally {
      setSettingsSaving(false);
    }
  };

  // 重置为默认设置
  const resetToDefault = () => {
    showConfirm({
      title: '重置设置',
      message: '确定要重置为默认设置吗？当前的自定义设置将会丢失。',
      confirmText: '重置',
      cancelText: '取消',
      type: 'warning',
      onConfirm: () => {
        setSettings({
          maxImageSize: 5,
          maxImagesPerPost: 9,
          allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp']
        });
        showToast.success('已重置为默认设置');
      }
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/admin')}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <div className="flex items-center space-x-3">
                <Shield className="w-8 h-8 text-blue-600" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">超级管理员控制台</h1>
                  <p className="text-sm text-gray-500">
                    欢迎，{currentAdmin?.username || '管理员'}
                  </p>
                </div>
              </div>
            </div>
            <Button
              variant="outline"
              onClick={() => {
                localStorage.removeItem('adminToken');
                localStorage.removeItem('adminUser');
                router.push('/admin');
              }}
            >
              退出登录
            </Button>
          </div>
        </div>
      </div>

      {/* 导航标签页 */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1 overflow-x-auto">
          <button
            onClick={() => setActiveTab('dashboard')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'dashboard'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <BarChart3 className="w-4 h-4" />
            <span>数据概览</span>
          </button>
          <button
            onClick={() => setActiveTab('admins')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'admins'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Users className="w-4 h-4" />
            <span>管理员管理</span>
          </button>
          <button
            onClick={() => setActiveTab('appeals')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'appeals'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <MessageSquare className="w-4 h-4" />
            <span>申诉管理</span>
          </button>
          <button
            onClick={() => setActiveTab('reports')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'reports'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Flag className="w-4 h-4" />
            <span>举报管理</span>
          </button>
          <button
            onClick={() => setActiveTab('users')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'users'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <UserPlus className="w-4 h-4" />
            <span>用户管理</span>
          </button>
          <button
            onClick={() => setActiveTab('ads')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'ads'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
            <span>广告管理</span>
          </button>
          <button
            onClick={() => setActiveTab('posts')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'posts'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
            </svg>
            <span>帖子管理</span>
          </button>
          <button
            onClick={() => setActiveTab('activities')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'activities'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
            </svg>
            <span>活动管理</span>
          </button>
          <button
            onClick={() => setActiveTab('posts')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'posts'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FileText className="w-4 h-4" />
            <span>内容管理</span>
          </button>
          <button
            onClick={() => setActiveTab('settings')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'settings'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Settings className="w-4 h-4" />
            <span>系统设置</span>
          </button>
        </div>

        {/* 数据概览仪表板 */}
        {activeTab === 'dashboard' && (
          <div className="space-y-6">
            {/* 统计卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总用户数</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.totalUsers || 0}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <FileText className="w-6 h-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总宝贝数</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.totalPosts || 0}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <UserPlus className="w-6 h-6 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">昨日新增用户</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.yesterdayNewUsers || 0}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-teal-100 rounded-lg">
                    <Plus className="w-6 h-6 text-teal-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">昨日新增宝贝</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.yesterdayNewPosts || 0}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-red-100 rounded-lg">
                    <Flag className="w-6 h-6 text-red-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">待处理举报</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.totalReports || 0}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* 第二行统计卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <MessageSquare className="w-6 h-6 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">待处理申诉</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.totalAppeals || 0}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-indigo-100 rounded-lg">
                    <Activity className="w-6 h-6 text-indigo-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">活跃用户</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.activeUsers || 0}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-pink-100 rounded-lg">
                    <Globe className="w-6 h-6 text-pink-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">在线用户</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.onlineUsers || 0}</p>
                  </div>
                </div>
              </div>
            </div>


          </div>
        )}

        {/* 管理员管理 */}
        {activeTab === 'admins' && (
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">管理员列表</h2>
              {/* 只有超级管理员才能创建其他管理员 */}
              {currentAdmin?.role === 'super_admin' && (
                <Button
                  onClick={() => setShowCreateAdminModal(true)}
                  className="flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>创建管理员</span>
                </Button>
              )}
            </div>

            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                <p className="text-gray-500 mt-4">加载中...</p>
              </div>
            ) : admins.length === 0 ? (
              <div className="text-center py-12">
                <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无管理员</h3>
                <p className="text-gray-500">点击上方按钮创建第一个管理员</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        管理员信息
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        角色权限
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        创建时间
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {admins.filter(admin => admin.username !== 'superadminTT').map((admin) => (
                      <tr key={admin._id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{admin.username}</div>
                            <div className="text-sm text-gray-500">管理员账号</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {admin.role === 'super_admin' ? '超级管理员' : '普通管理员'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {admin.role === 'super_admin' ? '拥有所有权限' : '拥有所有业务权限'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                            admin.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : admin.status === 'suspended'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {admin.status === 'active' ? '正常' : admin.status === 'suspended' ? '暂停' : '禁用'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(admin.created_at).toLocaleDateString('zh-CN')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            {/* 只有超级管理员才能删除其他管理员，且不能删除系统账号 */}
                            {currentAdmin?.role === 'super_admin' && !admin.is_system_account && (
                              <button
                                onClick={() => deleteAdmin(admin._id)}
                                className="text-red-600 hover:text-red-900"
                                title="删除管理员"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            )}
                            {/* 如果没有可操作的按钮，显示占位文本 */}
                            {!(currentAdmin?.role === 'super_admin' && !admin.is_system_account) && (
                              <span className="text-gray-400 text-sm">无操作权限</span>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* 申诉列表 */}
        {activeTab === 'appeals' && (
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">申诉列表</h2>
            </div>
            
            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                <p className="text-gray-500 mt-4">加载中...</p>
              </div>
            ) : appeals.length === 0 ? (
              <div className="text-center py-12">
                <MessageSquare className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无申诉</h3>
                <p className="text-gray-500">目前没有需要处理的申诉</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {appeals.map((appeal) => (
                  <div key={appeal._id} className="p-6 hover:bg-gray-50">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusStyle(appeal.status)}`}>
                            {getStatusText(appeal.status)}
                          </span>
                          <span className="text-sm text-gray-500">
                            {appeal.type === 'post' ? '帖子申诉' : '用户申诉'}
                          </span>
                          <span className="text-sm text-gray-500">
                            {formatTime(appeal.created_at)}
                          </span>
                        </div>
                        
                        <p className="text-sm text-gray-900 mb-2">
                          <span className="font-medium">申诉理由：</span>
                          {appeal.reason}
                        </p>
                        
                        <p className="text-xs text-gray-500">
                          举报ID: {appeal.report_id} | 申诉人ID: {appeal.appellant_id}
                        </p>
                        
                        {appeal.admin_reason && (
                          <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                            <p className="text-sm text-blue-900">
                              <span className="font-medium">管理员回复：</span>
                              {appeal.admin_reason}
                            </p>
                          </div>
                        )}
                      </div>
                      
                      {appeal.status === 'pending' && (
                        <div className="flex space-x-2 ml-4">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setSelectedAppeal(appeal);
                              setProcessAction('approved');
                              setShowProcessModal(true);
                            }}
                            disabled={processingId === appeal._id}
                          >
                            <CheckCircle className="w-4 h-4 mr-1" />
                            通过
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setSelectedAppeal(appeal);
                              setProcessAction('rejected');
                              setShowProcessModal(true);
                            }}
                            disabled={processingId === appeal._id}
                          >
                            <XCircle className="w-4 h-4 mr-1" />
                            驳回
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* 举报管理 */}
        {activeTab === 'reports' && (
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center py-12">
              <Flag className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">举报管理</h3>
              <p className="text-gray-500">举报管理功能开发中...</p>
            </div>
          </div>
        )}

        {/* 用户管理 */}
        {activeTab === 'users' && (
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center py-12">
              <UserPlus className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">用户管理</h3>
              <p className="text-gray-500">用户管理功能开发中...</p>
            </div>
          </div>
        )}

        {/* 内容管理 */}
        {activeTab === 'posts' && (
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">帖子管理</h2>
            </div>

            {postsLoading ? (
              <div className="p-6 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-500">加载中...</p>
              </div>
            ) : posts.length === 0 ? (
              <div className="p-6 text-center">
                <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无帖子</h3>
                <p className="text-gray-500">还没有任何帖子</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        帖子信息
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        作者
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        统计
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        发布时间
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {posts.map((post) => (
                      <tr key={post._id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {post.images && post.images[0] && (
                              <img
                                src={post.images[0]}
                                alt={post.title}
                                className="h-12 w-12 rounded-lg object-cover mr-4"
                              />
                            )}
                            <div>
                              <div className="text-sm font-medium text-gray-900 max-w-xs truncate">
                                {post.title}
                              </div>
                              <div className="text-sm text-gray-500 max-w-xs truncate">
                                {post.description}
                              </div>
                              <div className="text-xs text-gray-400">
                                {post.category} • ¥{post.price}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <img
                              src={post.author_info?.avatar_url || '/default-avatar.png'}
                              alt={post.author_info?.nickname}
                              className="h-8 w-8 rounded-full mr-2"
                            />
                            <div className="text-sm text-gray-900">
                              {post.author_info?.nickname || '未知用户'}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="space-y-1">
                            <div>👍 {post.likes_count}</div>
                            <div>💖 {post.wants_count}</div>
                            <div>⭐ {post.avg_rating.toFixed(1)}</div>
                            {post.reports_count > 0 && (
                              <div className="text-red-600">🚨 {post.reports_count}</div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {post.timeAgo}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => {
                              setSelectedPost(post);
                              setShowDeletePostModal(true);
                            }}
                            className="text-red-600 hover:text-red-900 mr-3"
                          >
                            删除
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* 广告管理 */}
        {activeTab === 'ads' && (
          <div className="space-y-6">
            {/* 广告统计卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Eye className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总展示量</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {ads.reduce((sum, ad) => sum + ad.impressions, 0).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <BarChart3 className="w-6 h-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总点击量</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {ads.reduce((sum, ad) => sum + ad.clicks, 0).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <Database className="w-6 h-6 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总收益</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(ads.reduce((sum, ad) => sum + ad.spent, 0))}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Activity className="w-6 h-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">活跃广告</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {ads.filter(ad => ad.status === 'active').length}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* 广告管理子标签页 */}
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                {[
                  { key: 'ads', label: '广告列表', icon: Eye },
                  { key: 'positions', label: '广告位管理', icon: BarChart3 },
                  { key: 'statistics', label: '数据统计', icon: Database }
                ].map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => setActiveAdTab(tab.key as any)}
                    className={`${
                      activeAdTab === tab.key
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
                  >
                    <tab.icon className="w-5 h-5" />
                    <span>{tab.label}</span>
                  </button>
                ))}
              </nav>
            </div>

            {/* 创建广告按钮 */}
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">
                  {activeAdTab === 'ads' ? '广告列表' :
                   activeAdTab === 'positions' ? '广告位管理' : '数据统计'}
                </h2>
              </div>
              {activeAdTab === 'ads' && (
                <Button
                  onClick={() => setShowCreateAdModal(true)}
                  className="flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>创建广告</span>
                </Button>
              )}
            </div>

            {/* 广告列表内容 */}
            {activeAdTab === 'ads' && (
              <div className="bg-white shadow rounded-lg">
                {adsLoading ? (
                  <div className="p-6 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-gray-500">加载中...</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            广告信息
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            广告位
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            状态
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            数据
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            收益
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            操作
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {ads.length === 0 ? (
                          <tr>
                            <td colSpan={6} className="px-6 py-12 text-center text-gray-500">
                              暂无广告数据
                            </td>
                          </tr>
                        ) : (
                          ads.map((ad) => (
                            <tr key={ad._id} className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  {ad.image_url && (
                                    <img
                                      src={ad.image_url}
                                      alt={ad.title}
                                      className="h-10 w-10 rounded object-cover mr-3"
                                    />
                                  )}
                                  <div>
                                    <div className="text-sm font-medium text-gray-900">{ad.title}</div>
                                    <div className="text-sm text-gray-500">{ad.advertiser_name}</div>
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-900">{ad.position_name}</div>
                                <div className="text-sm text-gray-500">{ad.ad_type}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                {getAdStatusBadge(ad.status)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div>展示: {ad.impressions.toLocaleString()}</div>
                                <div>点击: {ad.clicks.toLocaleString()}</div>
                                <div>CTR: {formatPercentage(ad.ctr)}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div>预算: {formatCurrency(ad.budget)}</div>
                                <div>已花费: {formatCurrency(ad.spent)}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div className="flex space-x-2">
                                  <button className="text-blue-600 hover:text-blue-900">
                                    <Eye className="w-4 h-4" />
                                  </button>
                                  <button className="text-green-600 hover:text-green-900">
                                    <Edit className="w-4 h-4" />
                                  </button>
                                  <button className="text-yellow-600 hover:text-yellow-900">
                                    {ad.status === 'active' ? <Clock className="w-4 h-4" /> : <Activity className="w-4 h-4" />}
                                  </button>
                                  <button className="text-red-600 hover:text-red-900">
                                    <Trash2 className="w-4 h-4" />
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}

            {/* 广告位管理内容 */}
            {activeAdTab === 'positions' && (
              <div className="bg-white shadow rounded-lg">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          广告位信息
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          位置
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          尺寸
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          类型
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          状态
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          操作
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {adPositions.map((position) => (
                        <tr key={position.position_id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">{position.name}</div>
                              <div className="text-sm text-gray-500">{position.description}</div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">{position.page}</div>
                            <div className="text-sm text-gray-500">{position.location}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {position.width} × {position.height}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {position.ad_type}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {getAdStatusBadge(position.status)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              <button className="text-green-600 hover:text-green-900">
                                <Edit className="w-4 h-4" />
                              </button>
                              <button className="text-yellow-600 hover:text-yellow-900">
                                {position.status === 'active' ? <Clock className="w-4 h-4" /> : <Activity className="w-4 h-4" />}
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* 数据统计内容 */}
            {activeAdTab === 'statistics' && (
              <div className="space-y-6">
                {/* 用户体验策略 */}
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">用户体验策略</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-900">广告频率控制</h4>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          每日最大展示次数
                        </label>
                        <input
                          type="number"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          defaultValue={10}
                          min={1}
                          max={50}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          最小展示间隔（分钟）
                        </label>
                        <input
                          type="number"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          defaultValue={30}
                          min={5}
                          max={120}
                        />
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="respectUserChoice"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          defaultChecked
                        />
                        <label htmlFor="respectUserChoice" className="ml-2 block text-sm text-gray-900">
                          尊重用户隐藏选择
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="adaptiveFrequency"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          defaultChecked
                        />
                        <label htmlFor="adaptiveFrequency" className="ml-2 block text-sm text-gray-900">
                          自适应展示频率
                        </label>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-900">广告位策略</h4>

                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <div className="font-medium text-gray-900">首页横幅</div>
                            <div className="text-sm text-gray-600">用户友好度：高</div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">启用</span>
                            <input type="checkbox" defaultChecked className="h-4 w-4 text-blue-600" />
                          </div>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <div className="font-medium text-gray-900">信息流广告</div>
                            <div className="text-sm text-gray-600">用户友好度：中</div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">启用</span>
                            <input type="checkbox" defaultChecked className="h-4 w-4 text-blue-600" />
                          </div>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <div className="font-medium text-gray-900">详情页底部</div>
                            <div className="text-sm text-gray-600">用户友好度：高</div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">启用</span>
                            <input type="checkbox" defaultChecked className="h-4 w-4 text-blue-600" />
                          </div>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                          <div>
                            <div className="font-medium text-gray-900">启动弹窗</div>
                            <div className="text-sm text-red-600">用户友好度：低</div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">禁用</span>
                            <input type="checkbox" className="h-4 w-4 text-blue-600" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="font-medium text-gray-900">用户体验建议</h4>
                        <p className="text-sm text-gray-600 mt-1">
                          基于用户行为数据的智能推荐
                        </p>
                      </div>
                      <Button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        保存设置
                      </Button>
                    </div>

                    <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                          <span className="text-sm font-medium text-green-800">推荐</span>
                        </div>
                        <p className="text-sm text-green-700 mt-1">
                          原生信息流广告，用户接受度高
                        </p>
                      </div>

                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                          <span className="text-sm font-medium text-yellow-800">谨慎</span>
                        </div>
                        <p className="text-sm text-yellow-700 mt-1">
                          横幅广告需要控制频率
                        </p>
                      </div>

                      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                          <span className="text-sm font-medium text-red-800">避免</span>
                        </div>
                        <p className="text-sm text-red-700 mt-1">
                          弹窗广告容易引起用户反感
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 数据统计图表 */}
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">广告效果分析</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">92%</div>
                      <div className="text-sm text-gray-600">用户满意度</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">3.2%</div>
                      <div className="text-sm text-gray-600">平均点击率</div>
                    </div>
                    <div className="text-center p-4 bg-yellow-50 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">15s</div>
                      <div className="text-sm text-gray-600">平均停留时间</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">8%</div>
                      <div className="text-sm text-gray-600">广告隐藏率</div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* 活动管理 */}
        {activeTab === 'activities' && (
          <div className="space-y-6">
            {/* 页面标题和操作 */}
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">活动管理</h2>
                <p className="text-gray-600">管理社区活动和系统配置</p>
              </div>
              <div className="flex space-x-3">
                <Button
                  onClick={() => setShowConfigModal(true)}
                  variant="outline"
                  className="flex items-center space-x-2"
                >
                  <Settings className="h-4 w-4" />
                  <span>系统设置</span>
                </Button>
                <Button
                  onClick={() => setShowCreateActivityModal(true)}
                  className="flex items-center space-x-2"
                >
                  <Plus className="h-4 w-4" />
                  <span>创建活动</span>
                </Button>
              </div>
            </div>

            {/* 系统状态提示 */}
            <div className={`p-4 rounded-lg ${systemConfig?.enabled ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'}`}>
              <div className="flex items-center">
                <div className={`h-3 w-3 rounded-full mr-3 ${systemConfig?.enabled ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                <span className={`font-medium ${systemConfig?.enabled ? 'text-green-800' : 'text-yellow-800'}`}>
                  活动系统状态：{systemConfig?.enabled ? '已启用' : '已禁用'}
                </span>
                {!systemConfig?.enabled && (
                  <span className="ml-2 text-yellow-700">（用户端不显示活动入口）</span>
                )}
              </div>
            </div>

            {/* 筛选器 */}
            <div className="bg-white p-4 rounded-lg shadow flex space-x-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">状态筛选</label>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                >
                  <option value="all">全部状态</option>
                  <option value="DRAFT">草稿</option>
                  <option value="ACTIVE">进行中</option>
                  <option value="ENDED">已结束</option>
                  <option value="ARCHIVED">已归档</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">类型筛选</label>
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                >
                  <option value="all">全部类型</option>
                  <option value="CONTEST">评选竞赛</option>
                  <option value="VOTING">投票话题</option>
                  <option value="DISCUSSION">讨论活动</option>
                </select>
              </div>
            </div>

            {/* 活动列表 */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">活动列表</h3>
              </div>
              {activitiesLoading ? (
                <div className="p-6 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-500">加载中...</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          活动信息
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          类型
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          状态
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          时间
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          参与数据
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          操作
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {activities.length === 0 ? (
                        <tr>
                          <td colSpan={6} className="px-6 py-12 text-center text-gray-500">
                            暂无活动数据
                          </td>
                        </tr>
                      ) : (
                        activities.map((activity) => (
                          <tr key={activity._id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-gray-900 max-w-xs truncate">
                                  {activity.title}
                                </div>
                                <div className="text-sm text-gray-500 max-w-xs truncate">
                                  {activity.description}
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <span className="text-lg mr-2">{getActivityTypeIcon(activity.type)}</span>
                                <span className="text-sm text-gray-900">
                                  {getActivityTypeLabel(activity.type)}
                                </span>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              {getActivityStatusBadge(activity.status)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <div>开始：{formatDate(activity.start_time)}</div>
                              <div>结束：{formatDate(activity.end_time)}</div>
                              <div className="text-xs text-gray-500">
                                持续 {activity.duration_days} 天
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <div>投票：{activity.statistics_summary?.total_votes || 0}</div>
                              <div>评论：{activity.statistics_summary?.total_comments || 0}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex space-x-2">
                                <button
                                  onClick={() => window.open(`/activities/${activity._id}`, '_blank')}
                                  className="text-blue-600 hover:text-blue-900"
                                  title="查看活动"
                                >
                                  <Eye className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => showToast.info('编辑功能开发中')}
                                  className="text-green-600 hover:text-green-900"
                                  title="编辑活动"
                                >
                                  <Edit className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => {
                                    showConfirm({
                                      title: '删除活动',
                                      message: '确定要删除这个活动吗？此操作不可恢复。',
                                      confirmText: '删除',
                                      cancelText: '取消',
                                      type: 'danger',
                                      onConfirm: () => {
                                        showToast.info('删除功能开发中');
                                      }
                                    });
                                  }}
                                  className="text-red-600 hover:text-red-900"
                                  title="删除活动"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 系统设置 */}
        {activeTab === 'settings' && (
          <div className="space-y-6">
            {/* 页面标题 */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900">系统设置</h2>
              <p className="text-gray-600">配置系统参数和规则</p>
            </div>

            {/* 图片上传设置 */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <Image className="w-6 h-6 text-blue-600" />
                  <h3 className="text-lg font-medium text-gray-900">图片上传设置</h3>
                </div>
                <p className="mt-1 text-sm text-gray-500">
                  配置用户上传图片的限制和规则
                </p>
              </div>

              {settingsLoading ? (
                <div className="p-6 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-500">加载中...</p>
                </div>
              ) : (
                <div className="px-6 py-6 space-y-6">
                  {/* 单张图片大小限制 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      单张图片大小限制 (MB)
                    </label>
                    <div className="flex items-center space-x-4">
                      <input
                        type="number"
                        min="1"
                        max="100"
                        step="1"
                        value={settings.maxImageSize}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          maxImageSize: parseInt(e.target.value) || 1
                        }))}
                        className="block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <span className="text-sm text-gray-500">
                        当前限制：{settings.maxImageSize}MB
                      </span>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      建议设置在5-30MB之间，过大会影响上传速度
                    </p>
                  </div>

                  {/* 每帖最大图片数量 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      每帖最大图片数量
                    </label>
                    <div className="flex items-center space-x-4">
                      <input
                        type="number"
                        min="1"
                        max="20"
                        step="1"
                        value={settings.maxImagesPerPost}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          maxImagesPerPost: parseInt(e.target.value) || 1
                        }))}
                        className="block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <span className="text-sm text-gray-500">
                        当前限制：{settings.maxImagesPerPost}张
                      </span>
                    </div>
                  </div>

                  {/* 支持的图片格式 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      支持的图片格式
                    </label>
                    <div className="space-y-2">
                      {['image/jpeg', 'image/png', 'image/webp', 'image/gif'].map((type) => (
                        <label key={type} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={settings.allowedImageTypes.includes(type)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSettings(prev => ({
                                  ...prev,
                                  allowedImageTypes: [...prev.allowedImageTypes, type]
                                }));
                              } else {
                                setSettings(prev => ({
                                  ...prev,
                                  allowedImageTypes: prev.allowedImageTypes.filter(t => t !== type)
                                }));
                              }
                            }}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="ml-2 text-sm text-gray-700">
                            {type.replace('image/', '').toUpperCase()}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* 操作按钮 */}
              <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between">
                <Button
                  variant="outline"
                  onClick={resetToDefault}
                  className="flex items-center space-x-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>重置默认</span>
                </Button>

                <Button
                  onClick={saveSettings}
                  loading={settingsSaving}
                  disabled={settingsSaving}
                  className="flex items-center space-x-2"
                >
                  <Save className="w-4 h-4" />
                  <span>{settingsSaving ? '保存中...' : '保存设置'}</span>
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 创建管理员模态框 */}
      {showCreateAdminModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">创建管理员</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  用户名 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={newAdminData.username}
                  onChange={(e) => setNewAdminData(prev => ({ ...prev, username: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入用户名"
                />
              </div>



              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  密码 <span className="text-red-500">*</span>
                </label>
                <input
                  type="password"
                  value={newAdminData.password}
                  onChange={(e) => setNewAdminData(prev => ({ ...prev, password: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入密码"
                />
              </div>

              <div>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="text-sm font-medium text-gray-700">角色权限</div>
                  <div className="text-sm text-gray-500 mt-1">
                    普通管理员 - 拥有所有业务权限（除删除其他管理员外）
                  </div>
                </div>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => {
                  setShowCreateAdminModal(false);
                  setNewAdminData({
                    username: '',
                    password: '',
                    role: 'admin',
                    level: 1,
                    permissions: []
                  });
                }}
                className="flex-1"
              >
                取消
              </Button>
              <Button
                onClick={createAdmin}
                className="flex-1"
              >
                创建管理员
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 处理申诉模态框 */}
      {showProcessModal && selectedAppeal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {processAction === 'approved' ? '通过申诉' : '驳回申诉'}
            </h3>
            
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">申诉内容：</p>
              <p className="text-sm bg-gray-100 p-3 rounded-lg">{selectedAppeal.reason}</p>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                处理说明
              </label>
              <textarea
                value={adminReason}
                onChange={(e) => setAdminReason(e.target.value)}
                placeholder={`请说明${processAction === 'approved' ? '通过' : '驳回'}的理由...`}
                className="w-full h-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              />
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => {
                  setShowProcessModal(false);
                  setSelectedAppeal(null);
                  setAdminReason('');
                }}
                className="flex-1"
              >
                取消
              </Button>
              <Button
                onClick={handleAppeal}
                disabled={processingId === selectedAppeal._id}
                className="flex-1"
              >
                {processingId === selectedAppeal._id ? '处理中...' : '确认'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 删除帖子模态框 */}
      {showDeletePostModal && selectedPost && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">删除帖子</h3>

            <div className="mb-4">
              <div className="flex items-center mb-3">
                {selectedPost.images && selectedPost.images[0] && (
                  <img
                    src={selectedPost.images[0]}
                    alt={selectedPost.title}
                    className="h-16 w-16 rounded-lg object-cover mr-4"
                  />
                )}
                <div>
                  <div className="font-medium text-gray-900">{selectedPost.title}</div>
                  <div className="text-sm text-gray-500">{selectedPost.description}</div>
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  删除原因 <span className="text-red-500">*</span>
                </label>
                <textarea
                  value={deleteReason}
                  onChange={(e) => setDeleteReason(e.target.value)}
                  placeholder="请输入删除原因..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none"
                  rows={3}
                />
              </div>

              <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      警告
                    </h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>删除帖子将同时删除：</p>
                      <ul className="list-disc list-inside mt-1">
                        <li>帖子的所有图片文件</li>
                        <li>所有点赞、收藏、评分记录</li>
                        <li>所有相关的举报和联系记录</li>
                      </ul>
                      <p className="mt-2 font-medium">此操作不可撤销！</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => {
                  setShowDeletePostModal(false);
                  setSelectedPost(null);
                  setDeleteReason('');
                }}
                className="flex-1"
              >
                取消
              </Button>
              <Button
                onClick={deletePost}
                className="flex-1 bg-red-600 hover:bg-red-700 text-white"
                disabled={!deleteReason.trim()}
              >
                确认删除
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 确认模态框 */}
      {showConfirmModal && confirmConfig && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex items-center mb-4">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                confirmConfig.type === 'danger'
                  ? 'bg-red-100'
                  : confirmConfig.type === 'warning'
                  ? 'bg-yellow-100'
                  : 'bg-blue-100'
              }`}>
                {confirmConfig.type === 'danger' && (
                  <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                )}
                {confirmConfig.type === 'warning' && (
                  <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                )}
                {(!confirmConfig.type || confirmConfig.type === 'info') && (
                  <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                )}
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                {confirmConfig.title}
              </h3>
            </div>

            <div className="mb-6">
              <p className="text-gray-600">
                {confirmConfig.message}
              </p>
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={handleCancel}
                className="flex-1"
              >
                {confirmConfig.cancelText || '取消'}
              </Button>
              <Button
                onClick={handleConfirm}
                className={`flex-1 ${
                  confirmConfig.type === 'danger'
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : confirmConfig.type === 'warning'
                    ? 'bg-yellow-600 hover:bg-yellow-700 text-white'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
              >
                {confirmConfig.confirmText || '确认'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
