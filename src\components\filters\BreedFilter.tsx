'use client';

import React, { useState, useEffect } from 'react';
import { useClickOutside } from '@/hooks/useClickOutside';

interface BreedFilterProps {
  selectedBreed: string;
  onBreedChange: (breed: string) => void;
}

// 本地存储键名
const BREED_FILTER_HISTORY_KEY = 'breed_filter_history';
const MAX_HISTORY_ITEMS = 10;

const BreedFilter: React.FC<BreedFilterProps> = ({
  selectedBreed,
  onBreedChange,
}) => {
  const [inputValue, setInputValue] = useState(selectedBreed);
  const [inputHistory, setInputHistory] = useState<string[]>([]);
  const [showHistory, setShowHistory] = useState(false);

  // 从本地存储加载历史记录
  const loadHistory = (): string[] => {
    try {
      const stored = localStorage.getItem(BREED_FILTER_HISTORY_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  };

  // 保存历史记录到本地存储
  const saveHistory = (history: string[]) => {
    try {
      localStorage.setItem(BREED_FILTER_HISTORY_KEY, JSON.stringify(history));
    } catch {
      // 忽略存储错误
    }
  };

  // 添加到历史记录
  const addToHistory = (breed: string) => {
    if (!breed.trim()) return;
    
    const currentHistory = loadHistory();
    const newHistory = [breed, ...currentHistory.filter(item => item !== breed)]
      .slice(0, MAX_HISTORY_ITEMS);
    
    setInputHistory(newHistory);
    saveHistory(newHistory);
  };

  // 清除历史记录
  const clearHistory = () => {
    setInputHistory([]);
    saveHistory([]);
    setShowHistory(false);
  };

  // 初始化历史记录
  useEffect(() => {
    setInputHistory(loadHistory());
  }, []);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    
    // 实时过滤
    onBreedChange(newValue);
  };

  // 处理历史记录选择
  const handleHistoryClick = (historyItem: string) => {
    setInputValue(historyItem);
    onBreedChange(historyItem);
    setShowHistory(false);
  };

  // 处理输入确认
  const handleInputConfirm = () => {
    if (inputValue && inputValue.trim()) {
      addToHistory(inputValue.trim());
    }
  };

  // 切换历史记录显示
  const toggleHistory = () => {
    setShowHistory(!showHistory);
  };

  // 点击外部关闭历史记录
  const historyRef = useClickOutside<HTMLDivElement>(() => {
    setShowHistory(false);
  });

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleInputConfirm();
    }
  };

  // 清空输入
  const handleClear = () => {
    setInputValue('');
    onBreedChange('');
    setShowHistory(false);
  };

  // 同步外部变化
  useEffect(() => {
    setInputValue(selectedBreed);
  }, [selectedBreed]);

  return (
    <div className="space-y-3">
      {/* 输入框和历史记录按钮 */}
      <div className="flex gap-2">
        <div className="flex-1">
          <input
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onBlur={handleInputConfirm}
            placeholder="输入品种筛选，如：柴犬、英短等"
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"
          />
        </div>
        
        {/* 历史记录按钮 */}
        <div className="relative" ref={historyRef}>
          <button
            type="button"
            onClick={toggleHistory}
            className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            title="查看品种历史"
          >
            <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </button>

          {/* 历史记录下拉列表 */}
          {showHistory && inputHistory.length > 0 && (
            <div className="absolute right-0 top-full mt-1 w-64 bg-white border border-gray-300 rounded-lg shadow-lg z-10">
              {/* 历史记录标题 */}
              <div className="px-4 py-2 bg-gray-50 border-b border-gray-200 flex items-center justify-between">
                <span className="text-sm text-gray-600 flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  品种历史
                </span>
                <button
                  onClick={clearHistory}
                  className="text-xs text-red-500 hover:text-red-700 transition-colors"
                >
                  清除
                </button>
              </div>
              
              {/* 历史记录列表 */}
              <div className="max-h-48 overflow-y-auto">
                {inputHistory.map((historyItem, index) => (
                  <div
                    key={`${historyItem}-${index}`}
                    onClick={() => handleHistoryClick(historyItem)}
                    className="px-4 py-2 cursor-pointer hover:bg-gray-50 transition-colors"
                  >
                    <span className="text-sm">{historyItem}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 清空按钮 */}
      {inputValue && (
        <div className="flex justify-end">
          <button
            onClick={handleClear}
            className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
          >
            清空筛选
          </button>
        </div>
      )}
    </div>
  );
};

export default BreedFilter;
