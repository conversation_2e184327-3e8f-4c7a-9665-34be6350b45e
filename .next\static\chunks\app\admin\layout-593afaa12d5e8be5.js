(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[91],{38002:function(e,s,n){Promise.resolve().then(n.bind(n,96631))},96631:function(e,s,n){"use strict";n.r(s),n.d(s,{default:function(){return o}});var t=n(57437);n(2265);var r=n(9356);function o(e){let{children:s}=e;return(0,t.jsxs)("div",{className:"admin-layout",children:[(0,t.jsx)(r.<PERSON>,{}),s]})}n(2778)},9356:function(e,s,n){"use strict";n.d(s,{C:function(){return x},ToastProvider:function(){return p}});var t=n(57437);n(2265);var r=n(69064),o=n(65302),i=n(45131),a=n(22252),c=n(33245),u=n(32489),d=n(68661);let l={duration:4e3,position:"top-center",style:{background:"#fff",color:"#374151",border:"1px solid #e5e7eb",borderRadius:"8px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",padding:"12px 16px",fontSize:"14px",maxWidth:"400px"}},m=e=>{let{message:s,type:n,onDismiss:r}=e,l={success:(0,t.jsx)(o.Z,{className:"h-5 w-5 text-green-500"}),error:(0,t.jsx)(i.Z,{className:"h-5 w-5 text-red-500"}),warning:(0,t.jsx)(a.Z,{className:"h-5 w-5 text-yellow-500"}),info:(0,t.jsx)(c.Z,{className:"h-5 w-5 text-blue-500"})};return(0,t.jsxs)("div",{className:(0,d.cn)("flex items-center space-x-3 p-3 rounded-lg border shadow-lg",{success:"bg-green-50 border-green-200",error:"bg-red-50 border-red-200",warning:"bg-yellow-50 border-yellow-200",info:"bg-blue-50 border-blue-200"}[n]),children:[l[n],(0,t.jsx)("span",{className:"flex-1 text-sm font-medium",children:s}),(0,t.jsx)("button",{onClick:r,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,t.jsx)(u.Z,{className:"h-4 w-4"})})]})},x={success:e=>{r.Am.custom(s=>(0,t.jsx)(m,{message:e,type:"success",onDismiss:()=>r.Am.dismiss(s.id)}),l)},error:e=>{r.Am.custom(s=>(0,t.jsx)(m,{message:e,type:"error",onDismiss:()=>r.Am.dismiss(s.id)}),{...l,duration:6e3})},warning:e=>{r.Am.custom(s=>(0,t.jsx)(m,{message:e,type:"warning",onDismiss:()=>r.Am.dismiss(s.id)}),l)},info:e=>{r.Am.custom(s=>(0,t.jsx)(m,{message:e,type:"info",onDismiss:()=>r.Am.dismiss(s.id)}),l)},loading:e=>r.Am.loading(e,{style:l.style,position:l.position}),dismiss:e=>{r.Am.dismiss(e)},promise:(e,s)=>r.Am.promise(e,s,{style:l.style,position:l.position})},p=()=>(0,t.jsx)(r.x7,{position:"top-center",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"transparent",boxShadow:"none",padding:0}}})},68661:function(e,s,n){"use strict";n.d(s,{cn:function(){return o},uf:function(){return i},vV:function(){return a}});var t=n(61994),r=n(53335);function o(){for(var e=arguments.length,s=Array(e),n=0;n<e;n++)s[n]=arguments[n];return(0,r.m6)((0,t.W)(s))}function i(e){return e<1e3?e.toString():e<1e4?"".concat((e/1e3).toFixed(1),"k"):e<1e5?"".concat((e/1e4).toFixed(1),"w"):"".concat(Math.floor(e/1e4),"w")}function a(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}},2778:function(){}},function(e){e.O(0,[461,554,721,971,117,744],function(){return e(e.s=38002)}),_N_E=e.O()}]);