'use client';

import React, { useState, useEffect } from 'react';
import { User, LogIn, LogOut, Upload, ShoppingCart } from 'lucide-react';

interface TestAccount {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
}

const testAccounts: TestAccount[] = [
  {
    id: 'test-uploader',
    name: '测试上传者',
    description: '用于测试上传宝贝功能',
    icon: <Upload className="h-4 w-4" />,
    color: 'bg-blue-500'
  },
  {
    id: 'test-buyer',
    name: '测试买家',
    description: '用于测试购买和联系功能',
    icon: <ShoppingCart className="h-4 w-4" />,
    color: 'bg-green-500'
  }
];

const AccountSwitcher: React.FC = () => {
  const [currentAccount, setCurrentAccount] = useState<string | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  useEffect(() => {
    // 检查当前登录状态
    const checkLoginStatus = async () => {
      try {
        const response = await fetch('/api/cloudbase', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: 'user-auth',
            data: { action: 'getLoginState' }
          })
        });
        
        const result = await response.json();
        if (result.result?.isLoggedIn) {
          setIsLoggedIn(true);
          // 获取当前用户信息
          const userResponse = await fetch('/api/cloudbase', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              name: 'user-auth',
              data: { action: 'getCurrentUser' }
            })
          });
          
          const userResult = await userResponse.json();
          if (userResult.result?.user?.uid) {
            setCurrentAccount(userResult.result.user.uid);
          }
        }
      } catch (error) {
        console.error('检查登录状态失败:', error);
      }
    };

    checkLoginStatus();
  }, []);

  const handleLogin = async (accountId: string) => {
    try {
      const account = testAccounts.find(acc => acc.id === accountId);
      if (!account) return;

      const response = await fetch('/api/cloudbase', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: 'user-auth',
          data: {
            action: 'login',
            data: {
              email: `${accountId}@test.com`,
              nickname: account.name
            }
          }
        })
      });

      const result = await response.json();
      if (result.result?.success) {
        setCurrentAccount(accountId);
        setIsLoggedIn(true);
        
        // 刷新页面以更新所有组件的状态
        window.location.reload();
      }
    } catch (error) {
      console.error('登录失败:', error);
    }
  };

  const handleLogout = async () => {
    try {
      const response = await fetch('/api/cloudbase', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: 'user-auth',
          data: { action: 'logout' }
        })
      });

      const result = await response.json();
      if (result.result?.success) {
        setCurrentAccount(null);
        setIsLoggedIn(false);
        
        // 刷新页面以更新所有组件的状态
        window.location.reload();
      }
    } catch (error) {
      console.error('登出失败:', error);
    }
  };

  return (
    <div className="fixed top-4 right-4 z-50 bg-white rounded-lg shadow-lg border p-4 min-w-[280px]">
      <div className="flex items-center gap-2 mb-3">
        <User className="h-5 w-5 text-gray-600" />
        <h3 className="font-semibold text-gray-900">测试账号切换</h3>
      </div>

      {isLoggedIn && currentAccount && (
        <div className="mb-3 p-2 bg-gray-50 rounded-md">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${testAccounts.find(acc => acc.id === currentAccount)?.color || 'bg-gray-400'}`}></div>
              <span className="text-sm font-medium text-gray-900">
                {testAccounts.find(acc => acc.id === currentAccount)?.name || '当前用户'}
              </span>
            </div>
            <button
              onClick={handleLogout}
              className="flex items-center gap-1 px-2 py-1 text-xs text-red-600 hover:bg-red-50 rounded"
            >
              <LogOut className="h-3 w-3" />
              登出
            </button>
          </div>
        </div>
      )}

      <div className="space-y-2">
        {testAccounts.map((account) => (
          <button
            key={account.id}
            onClick={() => handleLogin(account.id)}
            disabled={currentAccount === account.id}
            className={`w-full p-3 rounded-md border text-left transition-colors ${
              currentAccount === account.id
                ? 'bg-blue-50 border-blue-200 cursor-not-allowed'
                : 'hover:bg-gray-50 border-gray-200'
            }`}
          >
            <div className="flex items-center gap-3">
              <div className={`w-8 h-8 rounded-full ${account.color} flex items-center justify-center text-white`}>
                {account.icon}
              </div>
              <div className="flex-1">
                <div className="font-medium text-gray-900">{account.name}</div>
                <div className="text-xs text-gray-500">{account.description}</div>
              </div>
              {currentAccount === account.id && (
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              )}
            </div>
          </button>
        ))}
      </div>

      <div className="mt-3 pt-3 border-t border-gray-200">
        <p className="text-xs text-gray-500">
          💡 切换账号后页面会自动刷新
        </p>
      </div>
    </div>
  );
};

export default AccountSwitcher;
