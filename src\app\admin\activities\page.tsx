'use client';

import { useState, useEffect } from 'react';
import { petAPI } from '@/lib/cloudbase';
import { 
  PlusIcon,
  TrophyIcon,
  ChatBubbleLeftRightIcon,
  UserGroupIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  ClockIcon,
  FireIcon,
  CogIcon
} from '@heroicons/react/24/outline';

interface Activity {
  _id: string;
  title: string;
  description: string;
  type: 'CONTEST' | 'VOTING' | 'DISCUSSION';
  status: 'DRAFT' | 'ACTIVE' | 'ENDED' | 'ARCHIVED';
  start_time: string;
  end_time: string;
  result_display_end_time: string;
  duration_days: number;
  result_display_days: number;
  config: any;
  statistics_summary?: {
    total_votes: number;
    total_comments: number;
  };
  created_at: string;
}

export default function ActivitiesManagement() {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [systemConfig, setSystemConfig] = useState<any>(null);
  const [showConfigModal, setShowConfigModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');

  useEffect(() => {
    loadSystemConfig();
    loadActivities();
  }, [filterStatus, filterType]);

  const loadSystemConfig = async () => {
    try {
      const result = await petAPI.getSystemConfig();
      if (result.success) {
        setSystemConfig(result.data);
      }
    } catch (error) {
      console.error('加载系统配置失败:', error);
    }
  };

  const loadActivities = async () => {
    setLoading(true);
    try {
      const result = await petAPI.getActivities({
        status: filterStatus === 'all' ? undefined : filterStatus as any,
        type: filterType === 'all' ? undefined : filterType as any,
        limit: 100,
        includeArchived: true
      });
      
      if (result.success) {
        setActivities(result.data || []);
      }
    } catch (error) {
      console.error('加载活动失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateSystemConfig = async (newConfig: any) => {
    try {
      const result = await petAPI.updateSystemConfig({ config: newConfig });
      if (result.success) {
        setSystemConfig(newConfig);
        alert('系统配置更新成功！');
        setShowConfigModal(false);
      }
    } catch (error: any) {
      console.error('更新系统配置失败:', error);
      alert('更新失败：' + (error?.message || '未知错误'));
    }
  };

  const deleteActivity = async (activityId: string) => {
    if (!confirm('确定要删除这个活动吗？此操作不可恢复。')) {
      return;
    }

    try {
      const result = await petAPI.deleteActivity({ activity_id: activityId });
      if (result.success) {
        alert('活动删除成功！');
        await loadActivities();
      }
    } catch (error: any) {
      console.error('删除活动失败:', error);
      alert('删除失败：' + (error?.message || '未知错误'));
    }
  };

  const getActivityTypeIcon = (type: string) => {
    switch (type) {
      case 'CONTEST':
        return TrophyIcon;
      case 'VOTING':
        return ChatBubbleLeftRightIcon;
      case 'DISCUSSION':
        return UserGroupIcon;
      default:
        return TrophyIcon;
    }
  };

  const getActivityTypeLabel = (type: string) => {
    switch (type) {
      case 'CONTEST':
        return '评选竞赛';
      case 'VOTING':
        return '投票话题';
      case 'DISCUSSION':
        return '讨论活动';
      default:
        return '未知类型';
    }
  };

  const getStatusBadge = (status: string) => {
    const config = {
      DRAFT: { label: '草稿', color: 'bg-yellow-100 text-yellow-800' },
      ACTIVE: { label: '进行中', color: 'bg-green-100 text-green-800' },
      ENDED: { label: '已结束', color: 'bg-blue-100 text-blue-800' },
      ARCHIVED: { label: '已归档', color: 'bg-gray-100 text-gray-800' }
    };
    
    const cfg = config[status as keyof typeof config] || config.DRAFT;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${cfg.color}`}>
        {cfg.label}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">活动管理</h1>
          <p className="text-gray-600">管理社区活动和系统配置</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowConfigModal(true)}
            className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <CogIcon className="h-4 w-4 mr-2" />
            系统设置
          </button>
          <button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            创建活动
          </button>
        </div>
      </div>

      {/* 系统状态提示 */}
      <div className={`p-4 rounded-lg ${systemConfig?.enabled ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'}`}>
        <div className="flex items-center">
          <div className={`h-3 w-3 rounded-full mr-3 ${systemConfig?.enabled ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
          <span className={`font-medium ${systemConfig?.enabled ? 'text-green-800' : 'text-yellow-800'}`}>
            活动系统状态：{systemConfig?.enabled ? '已启用' : '已禁用'}
          </span>
          {!systemConfig?.enabled && (
            <span className="ml-2 text-yellow-700">（用户端不显示活动入口）</span>
          )}
        </div>
      </div>

      {/* 筛选器 */}
      <div className="bg-white p-4 rounded-lg shadow flex space-x-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">状态筛选</label>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="all">全部状态</option>
            <option value="DRAFT">草稿</option>
            <option value="ACTIVE">进行中</option>
            <option value="ENDED">已结束</option>
            <option value="ARCHIVED">已归档</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">类型筛选</label>
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="all">全部类型</option>
            <option value="CONTEST">评选竞赛</option>
            <option value="VOTING">投票话题</option>
            <option value="DISCUSSION">讨论活动</option>
          </select>
        </div>
      </div>

      {/* 活动列表 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">活动列表</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  活动信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  类型
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  时间
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  参与数据
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {activities.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-12 text-center text-gray-500">
                    暂无活动数据
                  </td>
                </tr>
              ) : (
                activities.map((activity) => {
                  const IconComponent = getActivityTypeIcon(activity.type);
                  
                  return (
                    <tr key={activity._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900 max-w-xs truncate">
                            {activity.title}
                          </div>
                          <div className="text-sm text-gray-500 max-w-xs truncate">
                            {activity.description}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <IconComponent className="h-5 w-5 text-gray-400 mr-2" />
                          <span className="text-sm text-gray-900">
                            {getActivityTypeLabel(activity.type)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(activity.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>开始：{formatDate(activity.start_time)}</div>
                        <div>结束：{formatDate(activity.end_time)}</div>
                        <div className="text-xs text-gray-500">
                          持续 {activity.duration_days} 天
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>投票：{activity.statistics_summary?.total_votes || 0}</div>
                        <div>评论：{activity.statistics_summary?.total_comments || 0}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => window.open(`/activities/${activity._id}`, '_blank')}
                            className="text-blue-600 hover:text-blue-900"
                            title="查看活动"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => alert('编辑功能开发中')}
                            className="text-green-600 hover:text-green-900"
                            title="编辑活动"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => deleteActivity(activity._id)}
                            className="text-red-600 hover:text-red-900"
                            title="删除活动"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* 系统配置模态框 */}
      {showConfigModal && (
        <SystemConfigModal
          config={systemConfig}
          onSave={updateSystemConfig}
          onClose={() => setShowConfigModal(false)}
        />
      )}

      {/* 创建活动模态框 */}
      {showCreateModal && (
        <CreateActivityModal
          onSuccess={() => {
            setShowCreateModal(false);
            loadActivities();
          }}
          onClose={() => setShowCreateModal(false)}
        />
      )}

    </div>
  );
}

// 系统配置模态框组件
const SystemConfigModal = ({ config, onSave, onClose }: any) => {
  const [formData, setFormData] = useState({
    enabled: config?.enabled || false,
    comments_enabled: config?.comments_enabled || true,
    rate_limit_interval: config?.rate_limit_interval || 10,
    max_comment_length: config?.max_comment_length || 100,
    default_result_display_days: config?.default_result_display_days || 3
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-medium text-gray-900 mb-4">系统配置</h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.enabled}
                onChange={(e) => setFormData({ ...formData, enabled: e.target.checked })}
                className="mr-2"
              />
              <span className="text-sm font-medium text-gray-700">启用活动系统</span>
            </label>
            <p className="text-xs text-gray-500 mt-1">关闭后用户端将不显示活动入口</p>
          </div>
          
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.comments_enabled}
                onChange={(e) => setFormData({ ...formData, comments_enabled: e.target.checked })}
                className="mr-2"
              />
              <span className="text-sm font-medium text-gray-700">启用评论功能</span>
            </label>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              评论间隔限制（秒）
            </label>
            <select
              value={formData.rate_limit_interval}
              onChange={(e) => setFormData({ ...formData, rate_limit_interval: parseInt(e.target.value) })}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            >
              <option value={5}>5秒</option>
              <option value={10}>10秒（推荐）</option>
              <option value={15}>15秒</option>
              <option value={20}>20秒</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              评论最大长度
            </label>
            <input
              type="number"
              value={formData.max_comment_length}
              onChange={(e) => setFormData({ ...formData, max_comment_length: parseInt(e.target.value) })}
              min={10}
              max={500}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              默认结果展示天数
            </label>
            <input
              type="number"
              value={formData.default_result_display_days}
              onChange={(e) => setFormData({ ...formData, default_result_display_days: parseInt(e.target.value) })}
              min={1}
              max={30}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            />
          </div>
          
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
            >
              保存
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// 创建活动模态框组件
const CreateActivityModal = ({ onSuccess, onClose }: any) => {
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'VOTING',
    startTime: '',
    durationDays: 7,
    resultDisplayDays: 3,
    options: [
      { id: 'option1', title: '', description: '' },
      { id: 'option2', title: '', description: '' }
    ]
  });

  const activityTemplates = [
    {
      id: 'pet_vs_pet',
      title: '宠物对决',
      description: '猫咪 VS 狗狗，你更喜欢哪一种？',
      type: 'VOTING',
      options: [
        { id: 'cats', title: '猫咪更好', description: '独立、优雅、适合忙碌生活' },
        { id: 'dogs', title: '狗狗更好', description: '忠诚、活泼、是最好的伙伴' }
      ]
    },
    {
      id: 'cute_contest',
      title: '最萌宠物评选',
      description: '展示你的宠物，参与最萌宠物评选活动',
      type: 'CONTEST'
    },
    {
      id: 'pet_care_tips',
      title: '宠物护理讨论',
      description: '分享宠物护理经验和技巧',
      type: 'DISCUSSION'
    }
  ];

  const handleTemplateSelect = (template: any) => {
    setFormData({
      ...formData,
      title: template.title,
      description: template.description,
      type: template.type,
      options: template.options || formData.options
    });
    setStep(2);
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      // 计算结束时间
      const startDate = new Date(formData.startTime);
      const endDate = new Date(startDate.getTime() + formData.durationDays * 24 * 60 * 60 * 1000);
      const resultEndDate = new Date(endDate.getTime() + formData.resultDisplayDays * 24 * 60 * 60 * 1000);

      const activityData = {
        title: formData.title,
        description: formData.description,
        type: formData.type as 'CONTEST' | 'VOTING' | 'DISCUSSION',
        startTime: formData.startTime,
        endTime: endDate.toISOString(),
        durationDays: formData.durationDays,
        resultDisplayDays: formData.resultDisplayDays,
        config: {
          comments_enabled: true,
          comments_after_vote: true,
          rate_limit: { interval: 10 },
          ...(formData.type === 'VOTING' && {
            options: formData.options.filter(opt => opt.title.trim())
          })
        }
      };

      const result = await petAPI.createActivity(activityData);

      if (result.success) {
        alert('活动创建成功！');
        onSuccess();
      }
    } catch (error: any) {
      console.error('创建活动失败:', error);
      alert('创建失败：' + (error?.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">
            {step === 1 ? '选择活动模板' : '完善活动信息'}
          </h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-6">
          {step === 1 ? (
            // 步骤1：选择模板
            <div className="space-y-4">
              <p className="text-gray-600 mb-6">选择一个活动模板快速开始，或者创建自定义活动</p>

              {activityTemplates.map((template) => (
                <div
                  key={template.id}
                  onClick={() => handleTemplateSelect(template)}
                  className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900">{template.title}</h3>
                      <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                      <span className="inline-block mt-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                        {template.type === 'VOTING' ? '投票话题' :
                         template.type === 'CONTEST' ? '评选竞赛' : '讨论活动'}
                      </span>
                    </div>
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              ))}

              <div
                onClick={() => setStep(2)}
                className="border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-gray-400 cursor-pointer transition-colors text-center"
              >
                <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <h3 className="font-medium text-gray-900">自定义活动</h3>
                <p className="text-sm text-gray-600 mt-1">从头开始创建活动</p>
              </div>
            </div>
          ) : (
            // 步骤2：完善信息
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">活动标题 *</label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入活动标题"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">活动描述</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入活动描述"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">开始时间 *</label>
                  <input
                    type="datetime-local"
                    value={formData.startTime}
                    onChange={(e) => setFormData({ ...formData, startTime: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">持续天数</label>
                  <input
                    type="number"
                    value={formData.durationDays}
                    onChange={(e) => setFormData({ ...formData, durationDays: parseInt(e.target.value) })}
                    min={1}
                    max={365}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {formData.type === 'VOTING' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">投票选项</label>
                  <div className="space-y-3">
                    {formData.options.map((option, index) => (
                      <div key={option.id} className="border border-gray-200 rounded-lg p-3">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <input
                            type="text"
                            value={option.title}
                            onChange={(e) => {
                              const newOptions = [...formData.options];
                              newOptions[index].title = e.target.value;
                              setFormData({ ...formData, options: newOptions });
                            }}
                            placeholder={`选项 ${index + 1} 标题`}
                            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                          />
                          <input
                            type="text"
                            value={option.description}
                            onChange={(e) => {
                              const newOptions = [...formData.options];
                              newOptions[index].description = e.target.value;
                              setFormData({ ...formData, options: newOptions });
                            }}
                            placeholder={`选项 ${index + 1} 描述`}
                            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                          />
                        </div>
                      </div>
                    ))}

                    {formData.options.length < 5 && (
                      <button
                        type="button"
                        onClick={() => {
                          const newOptions = [...formData.options, {
                            id: `option${formData.options.length + 1}`,
                            title: '',
                            description: ''
                          }];
                          setFormData({ ...formData, options: newOptions });
                        }}
                        className="w-full border-2 border-dashed border-gray-300 rounded-lg p-3 text-gray-600 hover:border-gray-400"
                      >
                        + 添加选项
                      </button>
                    )}
                  </div>
                </div>
              )}

              <div className="flex justify-between pt-4">
                <button
                  type="button"
                  onClick={() => setStep(1)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  上一步
                </button>

                <div className="space-x-3">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    取消
                  </button>
                  <button
                    onClick={handleSubmit}
                    disabled={loading || !formData.title || !formData.startTime}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50"
                  >
                    {loading ? '创建中...' : '创建活动'}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};


