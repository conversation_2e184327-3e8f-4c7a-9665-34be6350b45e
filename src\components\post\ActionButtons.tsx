import React, { useState } from 'react';
import { Heart, Bookmark, Star, MoreVertical, Share, Flag, MessageCircle } from 'lucide-react';
import { Post } from '@/types';
import { petAPI } from '@/lib/cloudbase';
import { useAuthContext } from '@/components/auth/AuthProvider';
import { showToast } from '@/components/ui/Toast';
import { formatNumber } from '@/utils';
import { cn } from '@/utils';
import RatingModal from './RatingModal';
import ReportModal from './ReportModal';

interface ActionButtonsProps {
  post: Post;
  onUpdate: () => void;
  onForceOpenContactSettings?: () => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({ post, onUpdate, onForceOpenContactSettings }) => {
  const { isLoggedIn, user } = useAuthContext();
  const [showRatingModal, setShowRatingModal] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [showMoreMenu, setShowMoreMenu] = useState(false);
  const [showContactConfirm, setShowContactConfirm] = useState(false);
  const [loading, setLoading] = useState<string | null>(null);

  // 点赞/取消点赞
  const handleLike = async () => {
    if (!isLoggedIn) {
      showToast.warning('请先登录');
      return;
    }

    try {
      setLoading('like');
      const result = await petAPI.toggleLike({ postId: post._id });
      
      if (result.success) {
        showToast.success(result.action === 'liked' ? '点赞成功' : '取消点赞');
        onUpdate();
      } else {
        showToast.error(result.message || '操作失败');
      }
    } catch (error: any) {
      showToast.error(error.message || '操作失败');
    } finally {
      setLoading(null);
    }
  };

  // 收藏/取消收藏
  const handleBookmark = async () => {
    if (!isLoggedIn) {
      showToast.warning('请先登录');
      return;
    }

    try {
      setLoading('bookmark');
      const result = await petAPI.toggleBookmark({ postId: post._id });

      if (result.success) {
        showToast.success(result.action === 'bookmarked' ? '收藏成功' : '取消收藏');
        onUpdate();
      } else {
        showToast.error(result.message || '操作失败');
      }
    } catch (error: any) {
      showToast.error(error.message || '操作失败');
    } finally {
      setLoading(null);
    }
  };

  // 分享
  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: post.title,
          text: post.description,
          url: window.location.href,
        });
      } else {
        // 复制链接到剪贴板
        await navigator.clipboard.writeText(window.location.href);
        showToast.success('链接已复制到剪贴板');
      }
    } catch (error) {
      console.error('分享失败:', error);
    }
    setShowMoreMenu(false);
  };

  // 举报
  const handleReport = () => {
    if (!isLoggedIn) {
      showToast.warning('请先登录');
      return;
    }
    setShowReportModal(true);
    setShowMoreMenu(false);
  };

  // 联系功能
  const handleContact = async () => {
    if (!isLoggedIn) {
      showToast.warning('请先登录');
      return;
    }

    // 检查用户是否已经联系过
    if (post.user_interactions?.hasContacted) {
      showToast.info('您已经联系过对方了');
      return;
    }

    // 检查用户是否填写了联系方式
    const savedContact = localStorage.getItem(`contact_${user?._id}`);
    if (!savedContact) {
      showToast.warning('请先完善联系方式才能使用联系功能');
      // 触发强制打开个人设置的联系方式标签页
      if (onForceOpenContactSettings) {
        onForceOpenContactSettings();
      }
      return;
    }

    try {
      const contactInfo = JSON.parse(savedContact);
      if (!contactInfo.value || !contactInfo.value.trim()) {
        showToast.warning('请先完善联系方式才能使用联系功能');
        if (onForceOpenContactSettings) {
          onForceOpenContactSettings();
        }
        return;
      }
    } catch (error) {
      showToast.warning('联系方式格式错误，请重新设置');
      if (onForceOpenContactSettings) {
        onForceOpenContactSettings();
      }
      return;
    }

    setShowContactConfirm(true);
  };

  // 确认联系
  const confirmContact = async () => {
    try {
      setLoading('contact');

      // 获取用户的联系方式
      const savedContact = localStorage.getItem(`contact_${user?._id}`);
      if (!savedContact) {
        showToast.warning('请先在个人信息中填写联系方式');
        setShowContactConfirm(false);
        return;
      }

      const contactInfo = JSON.parse(savedContact);

      // 调用API交换联系方式
      const result = await petAPI.exchangeContact({
        postId: post._id,
        contactInfo
      });

      if (result.success) {
        showToast.success(result.message || '联系方式已交换');
        setShowContactConfirm(false);
        onUpdate(); // 更新帖子状态
      } else {
        showToast.error(result.message || '交换失败');
      }
    } catch (error: any) {
      showToast.error(error.message || '发送失败');
    } finally {
      setLoading(null);
    }
  };

  // 基础按钮（所有帖子都有）
  const baseButtons = [
    {
      icon: Heart,
      count: post.likes_count || 0,
      active: post.user_interactions?.hasLiked || false,
      onClick: handleLike,
      loading: loading === 'like',
      activeColor: 'text-red-500',
      label: '点赞',
    },
    {
      icon: Bookmark,
      count: post.bookmarks_count || 0,
      active: post.user_interactions?.hasBookmarked || false,
      onClick: handleBookmark,
      loading: loading === 'bookmark',
      activeColor: 'text-blue-500',
      label: '收藏',
    },
    {
      icon: Star,
      count: post.ratings_count || 0,
      active: post.user_interactions?.hasRated || false,
      onClick: () => setShowRatingModal(true),
      loading: false,
      activeColor: 'text-yellow-500',
      label: '评分',
    },
  ];

  // 联系按钮（只在需要联系的帖子类型中显示）
  const contactButton = {
    icon: MessageCircle,
    count: 0, // 联系按钮不显示数量
    active: post.user_interactions?.hasContacted || false,
    onClick: handleContact,
    loading: loading === 'contact',
    activeColor: 'text-green-500',
    label: '联系',
  };

  // 根据帖子类型决定是否显示联系按钮
  // 展示分享类型（type为空或undefined）不显示联系按钮
  const actionButtons = (post.type === 'breeding' || post.type === 'selling' || post.type === 'lost' || post.type === 'wanted')
    ? [...baseButtons, contactButton]
    : baseButtons;

  return (
    <>
      <div className="fixed right-4 top-1/2 transform -translate-y-1/2 z-20 space-y-4">
        {/* 主要操作按钮 */}
        {actionButtons.map((button, index) => {
          const Icon = button.icon;
          return (
            <div key={index} className="flex flex-col items-center space-y-1">
              <button
                onClick={button.onClick}
                disabled={button.loading}
                className={cn(
                  'w-12 h-12 rounded-full backdrop-blur-sm border border-white/20 flex items-center justify-center transition-all duration-200 hover:scale-110',
                  button.active
                    ? `bg-white/90 ${button.activeColor}`
                    : 'bg-black/20 text-white hover:bg-white/30',

                )}
              >
                {button.loading ? (
                  <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Icon
                    className={cn(
                      'h-6 w-6',
                      button.active && (button.icon === Heart || button.icon === Bookmark) && 'fill-current'
                    )}
                  />
                )}
              </button>
              
              {/* 数量显示 - 联系按钮不显示数量 */}
              {button.count > 0 && (
                <span className="text-white text-xs font-medium bg-black/20 backdrop-blur-sm px-2 py-1 rounded-full">
                  {formatNumber(button.count)}
                </span>
              )}
            </div>
          );
        })}

        {/* 更多菜单 */}
        <div className="relative">
          <button
            onClick={() => setShowMoreMenu(!showMoreMenu)}
            className="w-12 h-12 rounded-full bg-black/20 backdrop-blur-sm border border-white/20 text-white flex items-center justify-center transition-all duration-200 hover:scale-110 hover:bg-white/30"
          >
            <MoreVertical className="h-6 w-6" />
          </button>

          {/* 更多菜单下拉 */}
          {showMoreMenu && (
            <div className="absolute right-0 bottom-full mb-2 w-32 bg-white rounded-lg shadow-lg border border-gray-200 py-1">
              <button
                onClick={handleShare}
                className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                <Share className="h-4 w-4" />
                <span>分享</span>
              </button>
              <button
                onClick={handleReport}
                className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-gray-100"
              >
                <Flag className="h-4 w-4" />
                <span>举报</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 评分模态框 */}
      <RatingModal
        isOpen={showRatingModal}
        onClose={() => setShowRatingModal(false)}
        post={post}
        onSuccess={onUpdate}
      />

      {/* 举报模态框 */}
      <ReportModal
        isOpen={showReportModal}
        onClose={() => setShowReportModal(false)}
        postId={post._id}
        onSuccess={() => {
          showToast.success('举报提交成功');
          setShowReportModal(false);
        }}
      />

      {/* 联系确认对话框 */}
      {showContactConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">确认联系</h3>
            <p className="text-gray-600 mb-6">
              将与对方互换联系方式，您确定吗？
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowContactConfirm(false)}
                className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                取消
              </button>
              <button
                onClick={confirmContact}
                disabled={loading === 'contact'}
                className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50"
              >
                {loading === 'contact' ? '发送中...' : '确定'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 点击外部关闭菜单 */}
      {showMoreMenu && (
        <div
          className="fixed inset-0 z-10"
          onClick={() => setShowMoreMenu(false)}
        />
      )}
    </>
  );
};

export default ActionButtons;
