<!--宠物页面模板-->
<view class="container">
  <!-- 分类选择 -->
  <scroll-view scroll-x="true" class="category-scroll">
    <view class="category-item {{selectedCategory === 'all' ? 'active' : ''}}" 
          bindtap="selectCategory" data-category="all">
      全部
    </view>
    <view wx:for="{{categories}}" wx:key="id" 
          class="category-item {{selectedCategory === item.id ? 'active' : ''}}"
          bindtap="selectCategory" data-category="{{item.id}}">
      {{item.name}}
    </view>
  </scroll-view>

  <!-- 宠物列表 -->
  <view class="pets-grid">
    <view wx:for="{{pets}}" wx:key="id" class="pet-card">
      <image src="{{item.image}}" class="pet-image" mode="aspectFill"></image>
      <view class="pet-info">
        <view class="pet-name">{{item.name}}</view>
        <view class="pet-breed">{{item.breed}}</view>
        <view class="pet-price">¥{{item.price}}</view>
      </view>
    </view>
  </view>
</view>
