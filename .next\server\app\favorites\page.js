(()=>{var e={};e.id=57,e.ids=[57],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},43525:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>o}),t(9908),t(16953),t(35866);var r=t(23191),a=t(88716),i=t(37922),l=t.n(i),n=t(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let o=["",{children:["favorites",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9908)),"D:\\web-cloudbase-project\\src\\app\\favorites\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\web-cloudbase-project\\src\\app\\favorites\\page.tsx"],x="/favorites/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/favorites/page",pathname:"/favorites",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},63060:(e,s,t)=>{Promise.resolve().then(t.bind(t,89755))},924:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},89755:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var r=t(10326),a=t(17577),i=t(67427),l=t(924);let n=(0,t(76557).Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]);var c=t(46152),o=t(22502),d=t(99837),x=t(35659),p=t(41828),m=t(16545),g=t(20603),u=t(28295);let h=()=>{let{user:e,isLoggedIn:s}=(0,x.E)(),[t,h]=(0,a.useState)([]),[y,v]=(0,a.useState)(!0),[f,j]=(0,a.useState)("grid"),b=async()=>{try{v(!0);let e=await p.petAPI.getUserBookmarks({limit:50});e.success&&h(e.data||[])}catch(e){console.error("获取收藏列表失败:",e),g.C.error("获取收藏列表失败")}finally{v(!1)}};return(0,a.useEffect)(()=>{b()},[]),(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[r.jsx(c.Z,{}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[r.jsx(i.Z,{className:"h-6 w-6 text-red-500 mr-2"}),"我的收藏"]}),(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:[t.length," 个收藏"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("button",{onClick:()=>j("grid"),className:(0,u.cn)("p-2 rounded-lg transition-colors","grid"===f?"bg-primary-600 text-white":"bg-white text-gray-600 hover:bg-gray-100"),children:r.jsx(l.Z,{className:"h-4 w-4"})}),r.jsx("button",{onClick:()=>j("list"),className:(0,u.cn)("p-2 rounded-lg transition-colors","list"===f?"bg-primary-600 text-white":"bg-white text-gray-600 hover:bg-gray-100"),children:r.jsx(n,{className:"h-4 w-4"})})]})]}),y?r.jsx("div",{className:(0,u.cn)("grid"===f?"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4":"space-y-4"),children:Array.from({length:8}).map((e,s)=>r.jsx(m.gG,{},s))}):t.length>0?r.jsx("div",{className:(0,u.cn)("grid"===f?"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4":"space-y-4"),children:t.map(e=>r.jsx(o.Z,{post:e,showRemoveFromFavorites:!0,onRemoveFromFavorites:()=>{h(s=>s.filter(s=>s._id!==e._id)),g.C.success("已从收藏中移除")}},e._id))}):r.jsx("div",{className:"text-center py-12",children:(0,r.jsxs)("div",{className:"text-gray-500",children:[r.jsx("div",{className:"text-6xl mb-4",children:"\uD83D\uDC9D"}),r.jsx("p",{className:"text-lg font-medium mb-2",children:"还没有收藏任何宠物"}),r.jsx("p",{className:"text-sm mb-6",children:"去首页看看有什么喜欢的宠物吧"}),r.jsx(d.Z,{onClick:()=>window.location.href="/",children:"去首页逛逛"})]})})]})]})}},9908:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\favorites\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[276,201,240,535,152],()=>t(43525));module.exports=r})();