'use client';

import React, { useState } from 'react';
import { authAPI } from '@/lib/cloudbase';

export default function TestEmailPage() {
  const [email, setEmail] = useState('');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const testSendCode = async () => {
    if (!email) {
      setError('请输入邮箱地址');
      return;
    }

    setLoading(true);
    setError('');
    setResult(null);

    try {
      console.log('开始测试发送验证码...');
      const response = await authAPI.sendVerificationCode(email, 'register');
      console.log('发送验证码响应:', response);
      setResult(response);
    } catch (err: any) {
      console.error('发送验证码错误:', err);
      setError(err.message || '发送失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6">邮箱验证码测试</h1>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              邮箱地址
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入邮箱地址"
            />
          </div>

          <button
            onClick={testSendCode}
            disabled={loading}
            className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? '发送中...' : '发送验证码'}
          </button>

          {error && (
            <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              <strong>错误:</strong> {error}
            </div>
          )}

          {result && (
            <div className="p-3 bg-green-100 border border-green-400 text-green-700 rounded">
              <strong>成功:</strong>
              <pre className="mt-2 text-sm overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>

        <div className="mt-6 text-sm text-gray-600">
          <p><strong>测试说明:</strong></p>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>输入真实邮箱地址</li>
            <li>点击发送验证码</li>
            <li>查看控制台日志</li>
            <li>检查邮箱是否收到验证码</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
