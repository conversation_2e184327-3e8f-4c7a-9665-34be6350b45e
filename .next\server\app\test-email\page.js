(()=>{var e={};e.id=207,e.ids=[207],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},43864:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d}),r(33075),r(16953),r(35866);var s=r(23191),a=r(88716),l=r(37922),n=r.n(l),i=r(95231),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d=["",{children:["test-email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,33075)),"D:\\web-cloudbase-project\\src\\app\\test-email\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],c=["D:\\web-cloudbase-project\\src\\app\\test-email\\page.tsx"],u="/test-email/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/test-email/page",pathname:"/test-email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},83272:(e,t,r)=>{Promise.resolve().then(r.bind(r,21538))},21538:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(10326),a=r(17577),l=r(41828);function n(){let[e,t]=(0,a.useState)(""),[r,n]=(0,a.useState)(null),[i,o]=(0,a.useState)(!1),[d,c]=(0,a.useState)(""),u=async()=>{if(!e){c("请输入邮箱地址");return}o(!0),c(""),n(null);try{console.log("开始测试发送验证码...");let t=await l.authAPI.sendVerificationCode(e,"register");console.log("发送验证码响应:",t),n(t)}catch(e){console.error("发送验证码错误:",e),c(e.message||"发送失败")}finally{o(!1)}};return s.jsx("div",{className:"min-h-screen bg-gray-50 py-12 px-4",children:(0,s.jsxs)("div",{className:"max-w-md mx-auto bg-white rounded-lg shadow-md p-6",children:[s.jsx("h1",{className:"text-2xl font-bold text-center mb-6",children:"邮箱验证码测试"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),s.jsx("input",{type:"email",value:e,onChange:e=>t(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"请输入邮箱地址"})]}),s.jsx("button",{onClick:u,disabled:i,className:"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed",children:i?"发送中...":"发送验证码"}),d&&(0,s.jsxs)("div",{className:"p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:[s.jsx("strong",{children:"错误:"})," ",d]}),r&&(0,s.jsxs)("div",{className:"p-3 bg-green-100 border border-green-400 text-green-700 rounded",children:[s.jsx("strong",{children:"成功:"}),s.jsx("pre",{className:"mt-2 text-sm overflow-auto",children:JSON.stringify(r,null,2)})]})]}),(0,s.jsxs)("div",{className:"mt-6 text-sm text-gray-600",children:[s.jsx("p",{children:s.jsx("strong",{children:"测试说明:"})}),(0,s.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[s.jsx("li",{children:"输入真实邮箱地址"}),s.jsx("li",{children:"点击发送验证码"}),s.jsx("li",{children:"查看控制台日志"}),s.jsx("li",{children:"检查邮箱是否收到验证码"})]})]})]})})}},33075:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\test-email\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[276,201,240],()=>r(43864));module.exports=s})();