'use client';

import { useState, useEffect } from 'react';
import { X, Eye, EyeOff, User, Lock, MapPin, MessageCircle } from 'lucide-react';
import Button from '@/components/ui/Button';
import { showToast } from '@/components/ui/Toast';
import { petAPI } from '@/lib/cloudbase';

interface PersonalInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentUser: any;
  onUpdate: (data: any) => void;
  forceContactTab?: boolean; // 强制打开联系方式标签页
}

interface ContactInfo {
  type: 'wechat' | 'phone';
  value: string;
}

type TabType = 'nickname' | 'password' | 'address' | 'contact';



export default function PersonalInfoModal({ isOpen, onClose, currentUser, onUpdate, forceContactTab = false }: PersonalInfoModalProps) {
  // 标签页状态
  const [activeTab, setActiveTab] = useState<TabType>('nickname');

  // 各个标签页的状态
  const [nickname, setNickname] = useState('');
  const [oldPassword, setOldPassword] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [contactInfo, setContactInfo] = useState<ContactInfo>({
    type: 'wechat',
    value: ''
  });
  const [address, setAddress] = useState('');

  // 验证错误状态
  const [contactError, setContactError] = useState('');

  const [showNicknameConfirm, setShowNicknameConfirm] = useState(false);
  const [nicknameWithNumber, setNicknameWithNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [lastNicknameChange] = useState<Date | null>(new Date(Date.now() - 25 * 24 * 60 * 60 * 1000)); // 25天前

  // 联系方式验证函数
  const validateContactInfo = (type: 'wechat' | 'phone', value: string): string => {
    if (!value.trim()) {
      return '联系方式不能为空';
    }

    if (type === 'wechat') {
      const wechatPattern = /^[a-zA-Z][a-zA-Z0-9_-]{5,19}$/;
      if (!wechatPattern.test(value)) {
        return '微信号格式：6-20位，字母开头，可包含字母、数字、下划线、减号';
      }
    } else if (type === 'phone') {
      const phonePattern = /^1[3-9]\d{9}$/;
      if (!phonePattern.test(value)) {
        return '请输入正确的11位手机号';
      }
    }

    return '';
  };

  // 自动填充登录信息
  const autoFillContactInfo = () => {
    if (currentUser?.loginType === 'wechat' && currentUser?.wechat_id) {
      return { type: 'wechat' as const, value: currentUser.wechat_id };
    } else if (currentUser?.loginType === 'phone' && currentUser?.phone) {
      return { type: 'phone' as const, value: currentUser.phone };
    }
    return { type: 'wechat' as const, value: '' };
  };

  useEffect(() => {
    if (isOpen && currentUser) {
      // 设置默认标签页
      if (forceContactTab) {
        setActiveTab('contact');
      } else {
        setActiveTab('nickname');
      }

      setNickname(currentUser.nickname || '');

      // 从数据库或本地存储加载联系方式
      const savedContact = localStorage.getItem(`contact_${currentUser._id}`);
      if (savedContact) {
        setContactInfo(JSON.parse(savedContact));
      } else {
        // 自动填充登录信息
        setContactInfo(autoFillContactInfo());
      }

      // 从本地存储加载地址
      const savedAddress = localStorage.getItem(`address_${currentUser._id}`);
      if (savedAddress) {
        setAddress(savedAddress);
      } else {
        setAddress('');
      }
    }
  }, [isOpen, currentUser, forceContactTab]);

  const canChangeNickname = () => {
    if (!lastNicknameChange) return true;
    const daysSinceLastChange = Math.floor((Date.now() - lastNicknameChange.getTime()) / (1000 * 60 * 60 * 24));
    return daysSinceLastChange >= 30;
  };

  const getDaysUntilNextChange = () => {
    if (!lastNicknameChange) return 0;
    const daysSinceLastChange = Math.floor((Date.now() - lastNicknameChange.getTime()) / (1000 * 60 * 60 * 24));
    return Math.max(0, 30 - daysSinceLastChange);
  };

  const checkNicknameAvailability = async (name: string) => {
    // 模拟检查昵称可用性
    const existingCount = Math.floor(Math.random() * 3); // 0-2 随机数模拟已存在的数量
    return existingCount;
  };

  const handleNicknameSubmit = async () => {
    if (!nickname.trim()) {
      showToast.error('昵称不能为空');
      return;
    }

    if (!canChangeNickname()) {
      showToast.error(`昵称30天只能修改一次，还需等待${getDaysUntilNextChange()}天`);
      return;
    }

    setLoading(true);
    try {
      const existingCount = await checkNicknameAvailability(nickname);
      if (existingCount === 0) {
        setNicknameWithNumber(nickname);
      } else {
        setNicknameWithNumber(`${nickname}#${existingCount + 1}`);
      }
      setShowNicknameConfirm(true);
    } catch (error) {
      showToast.error('检查昵称失败');
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmNickname = () => {
    setNickname(nicknameWithNumber);
    setShowNicknameConfirm(false);
  };

  // 保存昵称
  const handleSaveNickname = async () => {
    if (!nickname.trim()) {
      showToast.error('昵称不能为空');
      return;
    }

    if (!canChangeNickname()) {
      showToast.error(`昵称30天只能修改一次，还需等待${getDaysUntilNextChange()}天`);
      return;
    }

    setLoading(true);
    try {
      await onUpdate({ nickname });
      showToast.success('昵称更新成功');
    } catch (error) {
      showToast.error('昵称更新失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存密码
  const handleSavePassword = async () => {
    if (!oldPassword) {
      showToast.error('请输入旧密码');
      return;
    }
    if (!password) {
      showToast.error('请输入新密码');
      return;
    }
    if (password !== confirmPassword) {
      showToast.error('两次输入的密码不一致');
      return;
    }
    if (password.length < 6) {
      showToast.error('密码长度至少6位');
      return;
    }

    setLoading(true);
    try {
      await onUpdate({ oldPassword, password });
      showToast.success('密码修改成功');
      setOldPassword('');
      setPassword('');
      setConfirmPassword('');
    } catch (error) {
      showToast.error('密码修改失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存地址
  const handleSaveAddress = async () => {
    setLoading(true);
    try {
      localStorage.setItem(`address_${currentUser._id}`, address);
      await onUpdate({ address });
      showToast.success('地址更新成功');
    } catch (error) {
      showToast.error('地址更新失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存联系方式
  const handleSaveContact = async () => {
    const error = validateContactInfo(contactInfo.type, contactInfo.value);
    if (error) {
      setContactError(error);
      showToast.error(error);
      return;
    }

    setLoading(true);
    try {
      // 保存到本地存储
      localStorage.setItem(`contact_${currentUser._id}`, JSON.stringify(contactInfo));

      // 保存到数据库
      await onUpdate({ contactInfo });

      setContactError('');
      showToast.success('联系方式更新成功');

      // 如果是强制打开的联系方式标签页，保存成功后关闭模态框
      if (forceContactTab) {
        onClose();
      }
    } catch (error) {
      showToast.error('联系方式更新失败');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const tabs = [
    { id: 'nickname' as TabType, label: '昵称设置', icon: User },
    { id: 'password' as TabType, label: '密码修改', icon: Lock },
    { id: 'address' as TabType, label: '地址信息', icon: MapPin },
    { id: 'contact' as TabType, label: '联系方式', icon: MessageCircle },
  ];

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={(e) => {
        // 点击背景遮罩关闭模态框
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div
        className="bg-white rounded-lg w-[480px] h-[500px] flex flex-col"
        onClick={(e) => e.stopPropagation()} // 防止点击内容区域时关闭模态框
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b flex-shrink-0">
          <h2 className="text-xl font-bold text-gray-900">个人设置</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 标签页导航 */}
        <div className="border-b border-gray-200 flex-shrink-0">
          <div className="flex">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span className="hidden sm:inline">{tab.label}</span>
                </button>
              );
            })}
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-y-auto p-6">{renderTabContent()}</div>
      </div>

      {/* 昵称确认弹窗 */}
      {showNicknameConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <h3 className="text-lg font-bold text-gray-900 mb-4">确认昵称</h3>
            <p className="text-gray-600 mb-6">
              您的昵称将显示为：<span className="font-medium text-gray-900">{nicknameWithNumber}</span>
            </p>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowNicknameConfirm(false)}
                className="flex-1"
              >
                再想想
              </Button>
              <Button
                onClick={handleConfirmNickname}
                className="flex-1"
              >
                确认
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // 渲染标签页内容
  function renderTabContent() {
    switch (activeTab) {
      case 'nickname':
        return renderNicknameTab();
      case 'password':
        return renderPasswordTab();
      case 'address':
        return renderAddressTab();
      case 'contact':
        return renderContactTab();
      default:
        return null;
    }
  }

  // 昵称设置标签页
  function renderNicknameTab() {
    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            昵称
          </label>
          <div className="flex space-x-2">
            <input
              type="text"
              value={nickname}
              onChange={(e) => setNickname(e.target.value)}
              placeholder={currentUser?.loginType === 'wechat' ? '微信用户' : '邮箱用户'}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={!canChangeNickname()}
            />
            <Button
              onClick={handleNicknameSubmit}
              loading={loading}
              size="sm"
              disabled={!canChangeNickname()}
            >
              检查
            </Button>
          </div>
          {!canChangeNickname() && (
            <p className="text-sm text-orange-600 mt-1">
              昵称30天只能修改一次，还需等待{getDaysUntilNextChange()}天
            </p>
          )}
        </div>

        <div className="flex justify-end">
          <Button
            onClick={handleSaveNickname}
            loading={loading}
            disabled={!canChangeNickname() || !nickname.trim()}
          >
            保存昵称
          </Button>
        </div>
      </div>
    );
  }

  // 密码修改标签页
  function renderPasswordTab() {
    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            旧密码
          </label>
          <div className="relative">
            <input
              type={showOldPassword ? 'text' : 'password'}
              value={oldPassword}
              onChange={(e) => setOldPassword(e.target.value)}
              placeholder="请输入当前密码"
              className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              type="button"
              onClick={() => setShowOldPassword(!showOldPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showOldPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            新密码
          </label>
          <div className="relative">
            <input
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="至少6位字符"
              className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            确认密码
          </label>
          <div className="relative">
            <input
              type={showConfirmPassword ? 'text' : 'password'}
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="再次输入新密码"
              className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
        </div>

        <div className="flex justify-end">
          <Button
            onClick={handleSavePassword}
            loading={loading}
            disabled={!oldPassword || !password || !confirmPassword}
          >
            修改密码
          </Button>
        </div>
      </div>
    );
  }

  // 地址信息标签页
  function renderAddressTab() {
    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            常用地址
          </label>
          <input
            type="text"
            value={address}
            onChange={(e) => setAddress(e.target.value)}
            placeholder="请输入您的常用地址"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <p className="text-xs text-gray-500 mt-1">
            • 建议填写到县/区一级，如：北京市朝阳区、上海市浦东新区
            <br />
            • 此地址将作为发布宠物时的默认位置，提升交易效率
          </p>
        </div>

        <div className="flex justify-end">
          <Button
            onClick={handleSaveAddress}
            loading={loading}
          >
            保存地址
          </Button>
        </div>
      </div>
    );
  }

  // 联系方式标签页
  function renderContactTab() {
    return (
      <div className="space-y-4">
        {forceContactTab && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <p className="text-sm text-blue-800">
              💡 检测到您还没有设置联系方式，请先完善联系信息才能使用联系功能
            </p>
          </div>
        )}

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            联系方式类型
          </label>
          <div className="flex space-x-3 mb-3">
            <select
              value={contactInfo.type}
              onChange={(e) => {
                const newType = e.target.value as 'wechat' | 'phone';
                setContactInfo({ ...contactInfo, type: newType });
                setContactError('');
              }}
              className="min-w-[120px] px-3 py-2 pr-8 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none bg-white"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                backgroundPosition: 'right 0.5rem center',
                backgroundRepeat: 'no-repeat',
                backgroundSize: '1.5em 1.5em'
              }}
            >
              <option value="wechat">微信号</option>
              <option value="phone">手机号</option>
            </select>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {contactInfo.type === 'wechat' ? '微信号' : '手机号'}
          </label>
          <input
            type="text"
            value={contactInfo.value}
            onChange={(e) => {
              setContactInfo({ ...contactInfo, value: e.target.value });
              setContactError('');
            }}
            placeholder={contactInfo.type === 'wechat' ? '请输入微信号' : '请输入手机号'}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              contactError ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          {contactError && (
            <p className="text-sm text-red-600 mt-1">{contactError}</p>
          )}

          <div className="text-xs text-gray-500 mt-2">
            {contactInfo.type === 'wechat' ? (
              <div>
                <p>• 微信号格式：6-20位字符</p>
                <p>• 必须以字母开头</p>
                <p>• 可包含字母、数字、下划线、减号</p>
                <p>• 示例：wechat123、user_name、my-id</p>
              </div>
            ) : (
              <div>
                <p>• 请输入11位中国大陆手机号</p>
                <p>• 示例：13812345678</p>
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-end">
          <Button
            onClick={handleSaveContact}
            loading={loading}
            disabled={!contactInfo.value.trim()}
          >
            保存联系方式
          </Button>
        </div>
      </div>
    );
  }
}
