(()=>{var e={};e.id=750,e.ids=[750],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},98276:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c}),s(8501),s(16953),s(35866);var a=s(23191),r=s(88716),n=s(37922),i=s.n(n),o=s(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["messages",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8501)),"D:\\web-cloudbase-project\\src\\app\\messages\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\web-cloudbase-project\\src\\app\\messages\\page.tsx"],m="/messages/page",p={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/messages/page",pathname:"/messages",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},80141:(e,t,s)=>{Promise.resolve().then(s.bind(s,94879))},86333:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},67427:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},88378:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},94879:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var a=s(10326),r=s(17577),n=s(37202),i=s(54659),o=s(86333),l=s(34565),c=s(67427),d=s(88307),m=s(88378),p=s(39730),x=s(94019),u=s(35047),h=s(46152),g=s(35659),f=s(41828);let b=()=>{let e=(0,u.useRouter)(),{user:t,isLoggedIn:s}=(0,g.E)(),[b,y]=(0,r.useState)([]),[v,j]=(0,r.useState)(!0),[w,N]=(0,r.useState)(null),[k,_]=(0,r.useState)("trade"),[I,Z]=(0,r.useState)(!1),[T,P]=(0,r.useState)(null),[C,U]=(0,r.useState)(""),S=async()=>{if(s)try{j(!0);let e=await f.petAPI.getUserNotifications({limit:50,type:"contact"});if(e.success){let s=(e.data||[]).map(e=>"contact"===e.type?{id:e._id,type:"contact",fromUser:{id:e.sender_id,nickname:e.data?.sender_nickname||e.data?.author_nickname||"用户",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"},toUser:{id:e.recipient_id,nickname:t?.nickname||"",avatar:t?.avatar_url||""},postInfo:{id:e.post_id,title:e.data?.post_title||"宠物帖子",image:"https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=300&h=300&fit=crop",postType:e.data?.post_type||"selling"},contactInfo:{method:e.data?.sender_contact?.type||e.data?.author_contact?.type||"wechat",value:e.data?.sender_contact?.value||e.data?.author_contact?.value||"未提供"},timestamp:e.created_at,read:e.read||!1}:{id:e._id,type:"system",title:e.message,content:e.message,timestamp:e.created_at,read:e.read||!1,severity:"info"});y(s)}else A()}catch(e){console.error("加载通知失败:",e),A()}finally{j(!1)}},A=()=>{y([{id:"1",type:"system",title:"发布限制通知",content:"由于您发布的内容违反了社区规定，您的账号被限制发布功能7天。限制期间：2024年1月1日 - 2024年1月8日。有意见可以申诉。",timestamp:"2024-01-01T10:00:00Z",read:!1,severity:"warning",canAppeal:!0,reportId:"report-123",appealStatus:"none"},{id:"2",type:"contact",fromUser:{id:"user-2",nickname:"爱猫人士",avatar:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"},toUser:{id:t?._id||"",nickname:t?.nickname||"",avatar:t?.avatar_url||""},postInfo:{id:"post-1",title:"可爱的金毛宝宝找新家",image:"https://images.unsplash.com/photo-1552053831-71594a27632d?w=300&h=300&fit=crop",postType:"selling"},contactInfo:{method:"wechat",value:"wechat_user123"},timestamp:"2024-01-02T14:30:00Z",read:!1},{id:"3",type:"contact",fromUser:{id:"user-3",nickname:"繁育专家",avatar:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"},toUser:{id:t?._id||"",nickname:t?.nickname||"",avatar:t?.avatar_url||""},postInfo:{id:"post-2",title:"优质拉布拉多配种服务",image:"https://images.unsplash.com/photo-1518717758536-85ae29035b6d?w=300&h=300&fit=crop",postType:"breeding"},contactInfo:{method:"phone",value:"138****8888"},timestamp:"2024-01-02T16:20:00Z",read:!1},{id:"4",type:"contact",fromUser:{id:"user-4",nickname:"寻宠志愿者",avatar:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face"},toUser:{id:t?._id||"",nickname:t?.nickname||"",avatar:t?.avatar_url||""},postInfo:{id:"post-3",title:"寻找走失的橘猫咪咪",image:"https://images.unsplash.com/photo-1574158622682-e40e69881006?w=300&h=300&fit=crop",postType:"lost"},contactInfo:{method:"wechat",value:"helper_volunteer"},timestamp:"2024-01-02T18:45:00Z",read:!0},{id:"5",type:"contact",fromUser:{id:"user-5",nickname:"宠物爱好者",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"},toUser:{id:t?._id||"",nickname:t?.nickname||"",avatar:t?.avatar_url||""},postInfo:{id:"post-4",title:"求购健康的边牧幼犬",image:"https://images.unsplash.com/photo-1551717743-49959800b1f6?w=300&h=300&fit=crop",postType:"wanted"},contactInfo:{method:"wechat",value:"wechat123456"},timestamp:"2024-01-01T20:15:00Z",read:!1},{id:"6",type:"system",title:"平台欢迎通知",content:"欢迎您加入我们的宠物交易平台！请遵守社区规定，文明交易，共同维护良好的交易环境。如需帮助，请查看使用指南。",timestamp:"2023-12-25T09:00:00Z",read:!0,severity:"info"}])};(0,r.useEffect)(()=>{if(!s){e.push("/");return}S()},[s,e,k]);let $=b.filter(e=>{if("system"===k)return"system"===e.type;if("contact"===e.type)switch(k){case"trade":return"selling"===e.postInfo.postType||"wanted"===e.postInfo.postType;case"breeding":return"breeding"===e.postInfo.postType;case"lost":return"lost"===e.postInfo.postType}return!1}),q=e=>{y(t=>t.map(t=>t.id===e?{...t,read:!0}:t))},z=async e=>{if(!w){N(e);try{await new Promise(e=>setTimeout(e,500)),y(t=>t.filter(t=>t.id!==e)),console.log("消息已删除")}catch(e){console.error("删除失败:",e)}finally{N(null)}}},M=e=>{P(e),Z(!0)},D=async()=>{if(!T||!C.trim()){alert("请填写申诉理由");return}if(!T.reportId){alert("申诉信息不完整");return}try{let e=await f.petAPI.submitAppeal({reportId:T.reportId,reason:C,type:"post"});e.success?(y(e=>e.map(e=>e.id===T.id?{...e,appealStatus:"pending"}:e)),Z(!1),P(null),U(""),alert("申诉已提交，我们会在3个工作日内处理")):alert(e.message||"申诉提交失败")}catch(e){console.error("申诉提交失败:",e),alert(e.message||"申诉提交失败，请稍后重试")}},E=async()=>{if(confirm({trade:"确定清空所有买卖通知吗？",breeding:"确定清空所有配种通知吗？",lost:"确定清空所有寻宠通知吗？",system:"确定清空所有系统通知吗？"}[k]))try{let e=$.map(e=>e.id);await new Promise(e=>setTimeout(e,500)),y(t=>t.filter(t=>!e.includes(t.id))),console.log(`已清空${$.length}条通知`)}catch(e){console.error("清空失败:",e)}},G=e=>{let t=new Date(e),s=(new Date().getTime()-t.getTime())/36e5;return s<24?t.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}):s<168?`${Math.floor(s/24)}天前`:t.toLocaleDateString("zh-CN")},H=e=>{switch(e){case"warning":return a.jsx(n.Z,{className:"w-5 h-5 text-orange-500"});case"error":return a.jsx(n.Z,{className:"w-5 h-5 text-red-500"});default:return a.jsx(i.Z,{className:"w-5 h-5 text-blue-500"})}};return s?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx(h.Z,{}),(0,a.jsxs)("main",{className:"max-w-4xl mx-auto px-4 py-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[a.jsx("button",{onClick:()=>e.back(),className:"mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors",children:a.jsx(o.Z,{className:"w-5 h-5"})}),a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"通知中心"})]}),(0,a.jsxs)("div",{className:"flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1",children:[(0,a.jsxs)("button",{onClick:()=>_("trade"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"trade"===k?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(l.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"买卖通知"})]}),(0,a.jsxs)("button",{onClick:()=>_("breeding"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"breeding"===k?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(c.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"配种通知"})]}),(0,a.jsxs)("button",{onClick:()=>_("lost"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"lost"===k?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(d.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"寻宠通知"})]}),(0,a.jsxs)("button",{onClick:()=>_("system"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"system"===k?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(m.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"系统通知"})]})]}),$.length>0&&a.jsx("div",{className:"flex justify-end mb-4",children:a.jsx("button",{onClick:E,className:"px-4 py-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors",children:"清空当前分类"})}),a.jsx("div",{className:"space-y-4",children:v?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),a.jsx("p",{className:"text-gray-500 mt-4",children:"加载中..."})]}):0===$.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx(p.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无通知"}),a.jsx("p",{className:"text-gray-500",children:"您还没有收到任何通知"})]}):$.map(e=>a.jsx("div",{className:`bg-white rounded-lg border p-4 hover:shadow-md transition-shadow cursor-pointer ${e.read?"border-gray-200":"border-blue-200 bg-blue-50"}`,onClick:()=>q(e.id),children:"system"===e.type?(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("div",{className:"flex-shrink-0 mt-1",children:H(e.severity)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[a.jsx("h3",{className:"text-sm font-medium text-gray-900 truncate",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-xs text-gray-500",children:G(e.timestamp)}),!e.read&&a.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),a.jsx("button",{onClick:t=>{t.stopPropagation(),z(e.id)},disabled:w===e.id,className:"p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50",children:w===e.id?a.jsx("div",{className:"w-3 h-3 border border-gray-300 border-t-transparent rounded-full animate-spin"}):a.jsx(x.Z,{className:"w-3 h-3"})})]})]}),a.jsx("p",{className:"text-sm text-gray-600 line-clamp-2",children:e.content}),e.canAppeal&&(0,a.jsxs)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:["none"===e.appealStatus&&a.jsx("button",{onClick:t=>{t.stopPropagation(),M(e)},className:"text-sm bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors",children:"申诉"}),"pending"===e.appealStatus&&a.jsx("span",{className:"text-sm text-orange-600 bg-orange-100 px-3 py-1 rounded-full",children:"申诉处理中..."}),"approved"===e.appealStatus&&a.jsx("span",{className:"text-sm text-green-600 bg-green-100 px-3 py-1 rounded-full",children:"申诉已通过，处罚已撤销"}),"rejected"===e.appealStatus&&a.jsx("span",{className:"text-sm text-red-600 bg-red-100 px-3 py-1 rounded-full",children:"申诉已驳回，处罚维持"})]})]})]}):(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("img",{src:e.fromUser.avatar,alt:e.fromUser.nickname,className:"w-10 h-10 rounded-full object-cover"}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[a.jsx("h3",{className:"text-sm font-medium text-gray-900",children:(()=>{switch(e.postInfo.postType){case"selling":return"有用户想购买您的宠物";case"wanted":return"有卖家回应了您的求购";case"breeding":return"有用户需要您的配种服务";case"lost":return"有用户提供了走失线索";default:return"有用户联系了您"}})()}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-xs text-gray-500",children:G(e.timestamp)}),!e.read&&a.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),a.jsx("button",{onClick:t=>{t.stopPropagation(),z(e.id)},disabled:w===e.id,className:"p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50",children:w===e.id?a.jsx("div",{className:"w-3 h-3 border border-gray-300 border-t-transparent rounded-full animate-spin"}):a.jsx(x.Z,{className:"w-3 h-3"})})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[a.jsx("img",{src:e.postInfo.image,alt:e.postInfo.title,className:"w-12 h-12 rounded-lg object-cover"}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx("p",{className:"text-sm text-gray-900 truncate",children:e.postInfo.title}),a.jsx("p",{className:"text-xs text-gray-500",children:(()=>{switch(e.postInfo.postType){case"selling":return`买家：${e.fromUser.nickname}`;case"wanted":return`卖家：${e.fromUser.nickname}`;case"breeding":return`需求方：${e.fromUser.nickname}`;case"lost":return`提供者：${e.fromUser.nickname}`;default:return`联系人：${e.fromUser.nickname}`}})()})]}),a.jsx("button",{className:"text-xs text-blue-600 hover:text-blue-800 transition-colors",children:"查看帖子"})]}),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-3 border border-blue-200",children:[a.jsx("p",{className:"text-xs text-blue-600 mb-2 font-medium",children:"买家联系方式"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:["wechat"===e.contactInfo.method&&"微信：","phone"===e.contactInfo.method&&"电话：",e.contactInfo.value]}),a.jsx("button",{className:"text-xs bg-blue-500 text-white px-3 py-1 rounded-full hover:bg-blue-600 transition-colors",onClick:t=>{t.stopPropagation(),navigator.clipboard.writeText(e.contactInfo.value)},children:"复制"})]})]})]})]})},e.id))})]}),I&&T&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"申诉处罚"}),(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"处罚内容："}),a.jsx("p",{className:"text-sm bg-gray-100 p-3 rounded-lg",children:T.content})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["申诉理由 ",a.jsx("span",{className:"text-red-500",children:"*"})]}),a.jsx("textarea",{value:C,onChange:e=>U(e.target.value),placeholder:"请详细说明您认为处罚不当的理由...",className:"w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx("button",{onClick:()=>{Z(!1),P(null),U("")},className:"flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"取消"}),a.jsx("button",{onClick:D,disabled:!C.trim(),className:"flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"提交申诉"})]})]})})]}):null}},99837:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l});var a=s(10326),r=s(17577),n=s.n(r),i=s(28295);let o=n().forwardRef(({className:e,variant:t="primary",size:s="md",loading:r=!1,icon:n,children:o,disabled:l,...c},d)=>(0,a.jsxs)("button",{className:(0,i.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[t],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[s],e),ref:d,disabled:l||r,...c,children:[r&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[a.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),a.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!r&&n&&a.jsx("span",{className:"mr-2",children:n}),o]}));o.displayName="Button";let l=o},8501:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\messages\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[276,201,240,152],()=>s(98276));module.exports=a})();