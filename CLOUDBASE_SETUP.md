# 腾讯云开发环境与微信开发者工具打通指南

## 🎯 项目概述

本项目已经配置好了腾讯云开发环境与微信开发者工具的连接。

### 当前配置信息
- **环境ID**: `yichongyuzhou-3g9112qwf5f3487b`
- **小程序AppID**: `wx453409e8a70193cb`
- **项目类型**: 云开发小程序 + Web应用

## 📁 项目结构

```
├── miniprogram/          # 小程序源码
│   ├── app.js           # 小程序入口文件（已配置云开发）
│   ├── app.json         # 小程序配置
│   ├── pages/           # 页面目录
│   └── images/          # 图标资源
├── cloudfunctions/      # 云函数目录
├── src/                 # Web应用源码
├── cloudbaserc.json     # 云开发配置
└── project.config.json  # 微信开发者工具配置
```

## 🔧 在微信开发者工具中打开项目

### 步骤1：打开微信开发者工具
1. 启动微信开发者工具
2. 选择"小程序"
3. 点击"导入项目"

### 步骤2：导入项目
1. **项目目录**: 选择当前项目根目录
2. **AppID**: `wx453409e8a70193cb`
3. **项目名称**: 宠物交易平台
4. 点击"导入"

### 步骤3：云开发设置
1. 点击工具栏中的"云开发"按钮
2. 如果提示开通云开发，选择"使用已有腾讯云环境"
3. 输入环境ID: `yichongyuzhou-3g9112qwf5f3487b`
4. 完成授权

## 🚀 部署云函数

在微信开发者工具中：
1. 右键点击 `cloudfunctions` 目录下的云函数
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

需要部署的云函数：
- `pet-api` - 宠物相关API
- `user-auth` - 用户认证

## 📱 小程序功能

已创建的页面：
- **首页** (`pages/index/index`) - 展示宠物帖子列表
- **宠物分类** (`pages/pets/pets`) - 按分类浏览宠物
- **个人中心** (`pages/profile/profile`) - 用户信息和设置

## 🔗 云开发功能集成

### 数据库
- 已配置数据库集合（见 `cloudbaserc.json`）
- 小程序中通过 `wx.cloud.database()` 访问

### 云函数
- 已配置云函数调用
- 小程序中通过 `wx.cloud.callFunction()` 调用

### 云存储
- 支持图片上传和管理
- 小程序中通过 `wx.cloud.uploadFile()` 上传

## ⚠️ 注意事项

1. **图标文件**: 请在 `miniprogram/images/` 目录下添加所需的图标文件
2. **环境权限**: 确保您的腾讯云账号有该环境的管理权限
3. **小程序权限**: 确保您有该小程序的开发权限

## 🛠️ 故障排除

### 云开发初始化失败
- 检查环境ID是否正确
- 确认腾讯云账号与小程序已关联
- 重新授权云开发权限

### 云函数调用失败
- 确认云函数已正确部署
- 检查云函数代码是否有错误
- 查看云开发控制台的日志

### 数据库访问失败
- 检查数据库权限设置
- 确认集合是否已创建
- 验证数据库规则配置

## 📞 技术支持

如遇到问题，可以：
1. 查看微信开发者工具的调试控制台
2. 登录腾讯云开发控制台查看日志
3. 参考官方文档：https://developers.weixin.qq.com/miniprogram/dev/wxcloud/

---

✅ **项目已配置完成，可以在微信开发者工具中直接打开使用！**
