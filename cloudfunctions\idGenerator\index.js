const cloudbase = require('@cloudbase/node-sdk');

const app = cloudbase.init({
  env: cloudbase.SYMBOL_CURRENT_ENV
});

const db = app.database();

// ID生成器配置
const ID_CONFIGS = {
  user: {
    prefix: 'USER',
    collection: 'users',
    counterCollection: 'id_counters',
    counterKey: 'user_counter',
    format: (index, date) => {
      const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
      const indexStr = index.toString().padStart(6, '0');
      return `USER_${indexStr}_${dateStr}`;
    }
  },
  post: {
    prefix: 'POST',
    collection: 'posts',
    counterCollection: 'id_counters',
    counterKey: 'post_daily_counter',
    format: (index, date) => {
      const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
      const indexStr = index.toString().padStart(3, '0');
      return `POST_${dateStr}_${indexStr}`;
    }
  }
};

// 获取下一个用户序号（全局递增）
async function getNextUserIndex() {
  try {
    const counterDoc = await db.collection('id_counters').doc('user_counter').get();
    
    if (!counterDoc.data) {
      // 初始化计数器
      await db.collection('id_counters').doc('user_counter').set({
        current_index: 1,
        updated_at: new Date()
      });
      return 1;
    }
    
    const nextIndex = counterDoc.data.current_index + 1;
    
    // 原子性更新计数器
    await db.collection('id_counters').doc('user_counter').update({
      current_index: nextIndex,
      updated_at: new Date()
    });
    
    return nextIndex;
  } catch (error) {
    console.error('获取用户序号失败:', error);
    throw new Error('生成用户ID失败');
  }
}

// 获取当日帖子序号（每日重置）
async function getDailyPostIndex(date) {
  try {
    const dateKey = date.toISOString().slice(0, 10);
    const counterId = `post_daily_${dateKey}`;
    
    const counterDoc = await db.collection('id_counters').doc(counterId).get();
    
    if (!counterDoc.data) {
      // 初始化当日计数器
      await db.collection('id_counters').doc(counterId).set({
        date: dateKey,
        current_index: 1,
        updated_at: new Date()
      });
      return 1;
    }
    
    const nextIndex = counterDoc.data.current_index + 1;
    
    // 原子性更新计数器
    await db.collection('id_counters').doc(counterId).update({
      current_index: nextIndex,
      updated_at: new Date()
    });
    
    return nextIndex;
  } catch (error) {
    console.error('获取帖子序号失败:', error);
    throw new Error('生成帖子ID失败');
  }
}

// 生成用户可读性ID
async function generateUserCode(registrationDate = new Date()) {
  try {
    const userIndex = await getNextUserIndex();
    const config = ID_CONFIGS.user;
    return config.format(userIndex, registrationDate);
  } catch (error) {
    console.error('生成用户代码失败:', error);
    throw error;
  }
}

// 生成帖子可读性ID
async function generatePostCode(postDate = new Date()) {
  try {
    const dailyIndex = await getDailyPostIndex(postDate);
    const config = ID_CONFIGS.post;
    return config.format(dailyIndex, postDate);
  } catch (error) {
    console.error('生成帖子代码失败:', error);
    throw error;
  }
}

// 验证ID唯一性
async function validateIdUniqueness(collection, field, value) {
  try {
    const existing = await db.collection(collection)
      .where({ [field]: value })
      .get();
    
    return existing.data.length === 0;
  } catch (error) {
    console.error('验证ID唯一性失败:', error);
    return false;
  }
}

// 批量生成ID（用于数据迁移）
async function batchGenerateIds(type, count = 100) {
  try {
    const config = ID_CONFIGS[type];
    if (!config) {
      throw new Error(`不支持的ID类型: ${type}`);
    }

    const results = [];
    const currentDate = new Date();

    for (let i = 0; i < count; i++) {
      let generatedId;
      
      if (type === 'user') {
        generatedId = await generateUserCode(currentDate);
      } else if (type === 'post') {
        generatedId = await generatePostCode(currentDate);
      }
      
      results.push({
        index: i + 1,
        id: generatedId,
        timestamp: new Date().toISOString()
      });
    }

    return {
      success: true,
      type,
      count,
      results
    };
  } catch (error) {
    console.error('批量生成ID失败:', error);
    throw error;
  }
}

// 获取ID统计信息
async function getIdStatistics() {
  try {
    // 获取用户计数器
    const userCounter = await db.collection('id_counters').doc('user_counter').get();
    
    // 获取今日帖子计数器
    const today = new Date().toISOString().slice(0, 10);
    const todayPostCounter = await db.collection('id_counters').doc(`post_daily_${today}`).get();
    
    // 获取所有帖子计数器（最近7天）
    const recentCounters = await db.collection('id_counters')
      .where({
        _id: db.command.regex({
          regexp: '^post_daily_',
          options: 'i'
        })
      })
      .orderBy('_id', 'desc')
      .limit(7)
      .get();

    return {
      success: true,
      statistics: {
        totalUsers: userCounter.data?.current_index || 0,
        todayPosts: todayPostCounter.data?.current_index || 0,
        recentDailyPosts: recentCounters.data.map(counter => ({
          date: counter.date,
          count: counter.current_index
        }))
      }
    };
  } catch (error) {
    console.error('获取统计信息失败:', error);
    throw error;
  }
}

// 重置计数器（谨慎使用）
async function resetCounters(type) {
  try {
    if (type === 'user') {
      await db.collection('id_counters').doc('user_counter').set({
        current_index: 0,
        updated_at: new Date(),
        reset_at: new Date()
      });
    } else if (type === 'post') {
      // 删除所有帖子计数器
      const postCounters = await db.collection('id_counters')
        .where({
          _id: db.command.regex({
            regexp: '^post_daily_',
            options: 'i'
          })
        })
        .get();
      
      const batch = db.batch();
      postCounters.data.forEach(counter => {
        batch.remove(db.collection('id_counters').doc(counter._id));
      });
      
      await batch.commit();
    }

    return {
      success: true,
      message: `${type} 计数器已重置`
    };
  } catch (error) {
    console.error('重置计数器失败:', error);
    throw error;
  }
}

// 云函数入口
exports.main = async (event, context) => {
  const { action, type, count, date } = event;

  try {
    switch (action) {
      case 'generateUserCode':
        const userCode = await generateUserCode(date ? new Date(date) : undefined);
        return {
          success: true,
          userCode,
          timestamp: new Date().toISOString()
        };
      
      case 'generatePostCode':
        const postCode = await generatePostCode(date ? new Date(date) : undefined);
        return {
          success: true,
          postCode,
          timestamp: new Date().toISOString()
        };
      
      case 'batchGenerate':
        return await batchGenerateIds(type, count);
      
      case 'getStatistics':
        return await getIdStatistics();
      
      case 'resetCounters':
        return await resetCounters(type);
      
      case 'validate':
        const { collection, field, value } = event;
        const isUnique = await validateIdUniqueness(collection, field, value);
        return {
          success: true,
          isUnique,
          value
        };
      
      default:
        return {
          success: false,
          message: '不支持的操作'
        };
    }
  } catch (error) {
    console.error('ID生成器操作失败:', error);
    return {
      success: false,
      message: error.message,
      error: error
    };
  }
};
