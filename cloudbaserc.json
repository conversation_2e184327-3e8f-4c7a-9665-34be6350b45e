{"envId": "yichongyuzhou-3g9112qwf5f3487b", "version": "2.0", "framework": {"name": "pet-trading-platform", "plugins": {"client": {"use": "@cloudbase/framework-plugin-website", "inputs": {"buildCommand": "npm run build", "outputPath": "dist", "cloudPath": "/pet-platform"}}, "server": {"use": "@cloudbase/framework-plugin-function", "inputs": {"functionRootPath": "cloudfunctions", "functions": [{"name": "pet-api", "timeout": 60, "envVariables": {}, "runtime": "Nodejs18.15", "memorySize": 256}, {"name": "user-auth", "timeout": 60, "envVariables": {}, "runtime": "Nodejs18.15", "memorySize": 256}]}}}}, "functions": [{"name": "pet-api", "timeout": 60, "envVariables": {}, "runtime": "Nodejs18.15", "memorySize": 256, "handler": "index.main"}, {"name": "user-auth", "timeout": 60, "envVariables": {}, "runtime": "Nodejs18.15", "memorySize": 256, "handler": "index.main"}], "database": {"collections": [{"collectionName": "users", "description": "用户信息表", "aclTag": "READONLY"}, {"collectionName": "posts", "description": "宠物帖子表", "aclTag": "READONLY"}, {"collectionName": "categories", "description": "分类表", "aclTag": "READONLY"}, {"collectionName": "likes", "description": "点赞表", "aclTag": "READONLY"}, {"collectionName": "wants", "description": "想买表", "aclTag": "READONLY"}, {"collectionName": "ratings", "description": "评分表", "aclTag": "READONLY"}, {"collectionName": "follows", "description": "关注表", "aclTag": "READONLY"}, {"collectionName": "post_reports", "description": "帖子举报表", "aclTag": "ADMINONLY"}, {"collectionName": "user_reports", "description": "用户举报表", "aclTag": "ADMINONLY"}]}}