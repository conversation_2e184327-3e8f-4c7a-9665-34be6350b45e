'use client';

import React, { useState } from 'react';
import { Phone, MessageCircle } from 'lucide-react';
import { Input } from '@/components/ui/Input';
import { cn } from '@/utils';

interface ContactInputProps {
  value: {
    phone?: string;
    wechat?: string;
  };
  onChange: (contactInfo: { phone?: string; wechat?: string }) => void;
  petType?: 'breeding' | 'selling' | 'lost' | 'wanted' | '';
  required?: boolean;
  error?: string;
  className?: string;
}

type ContactType = 'phone' | 'wechat';

const ContactInput: React.FC<ContactInputProps> = ({
  value,
  onChange,
  petType = '',
  required = false,
  error,
  className
}) => {
  const [selectedType, setSelectedType] = useState<ContactType>(() => {
    // 根据已有值确定默认选中的联系方式类型
    if (value.wechat) return 'wechat';
    if (value.phone) return 'phone';
    return 'wechat'; // 默认选择微信号
  });

  // 判断是否需要联系方式（出售、配种、寻回、求购需要）
  const needsContact = ['breeding', 'selling', 'lost', 'wanted'].includes(petType);
  const isRequired = required || needsContact;

  // 联系方式选项
  const contactOptions = [
    {
      type: 'wechat' as ContactType,
      label: '微信号',
      icon: MessageCircle,
      placeholder: '请输入微信号',
      pattern: /^[a-zA-Z][\w-]{5,19}$/,
      errorMessage: '微信号格式：6-20位，字母开头，可包含字母、数字、下划线、减号'
    },
    {
      type: 'phone' as ContactType,
      label: '手机号',
      icon: Phone,
      placeholder: '请输入手机号',
      pattern: /^1[3-9]\d{9}$/,
      errorMessage: '请输入正确的手机号格式'
    }
  ];

  // 获取当前选中的联系方式配置
  const currentOption = contactOptions.find(option => option.type === selectedType)!;

  // 获取当前输入值
  const currentValue = value[selectedType] || '';

  // 处理联系方式类型切换
  const handleTypeChange = (type: ContactType) => {
    setSelectedType(type);
    // 如果切换到的类型已有值，保持原值；否则清空
    if (!value[type]) {
      const newValue = { ...value };
      // 清空其他类型的值，只保留当前选中类型
      contactOptions.forEach(option => {
        if (option.type !== type) {
          delete newValue[option.type];
        }
      });
      onChange(newValue);
    }
  };

  // 处理输入值变化
  const handleValueChange = (inputValue: string) => {
    const newValue = { ...value };
    
    // 清空其他类型的值
    contactOptions.forEach(option => {
      if (option.type !== selectedType) {
        delete newValue[option.type];
      }
    });
    
    // 设置当前类型的值
    if (inputValue.trim()) {
      newValue[selectedType] = inputValue.trim();
    } else {
      delete newValue[selectedType];
    }
    
    onChange(newValue);
  };

  // 验证当前输入值
  const validateCurrentValue = (inputValue: string): string | null => {
    if (!inputValue.trim()) {
      return isRequired ? '请填写联系方式' : null;
    }
    
    if (!currentOption.pattern.test(inputValue.trim())) {
      return currentOption.errorMessage;
    }
    
    return null;
  };

  const validationError = validateCurrentValue(currentValue);

  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">
          联系方式 {isRequired && <span className="text-red-500">*</span>}
        </label>
        {needsContact && (
          <span className="text-xs text-gray-500">
            {petType === 'breeding' && '配种需要联系方式'}
            {petType === 'selling' && '出售需要联系方式'}
            {petType === 'lost' && '寻回需要联系方式'}
            {petType === 'wanted' && '求购需要联系方式'}
          </span>
        )}
      </div>

      {/* 联系方式类型选择 */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        {contactOptions.map((option) => {
          const Icon = option.icon;
          const isSelected = selectedType === option.type;
          
          return (
            <button
              key={option.type}
              type="button"
              onClick={() => handleTypeChange(option.type)}
              className={cn(
                'flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-colors',
                isSelected
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              )}
            >
              <Icon className="h-4 w-4" />
              <span>{option.label}</span>
            </button>
          );
        })}
      </div>

      {/* 联系方式输入框 */}
      <Input
        placeholder={currentOption.placeholder}
        value={currentValue}
        onChange={(e) => handleValueChange(e.target.value)}
        leftIcon={<currentOption.icon className="h-4 w-4" />}
        error={validationError || error}
        type={selectedType === 'phone' ? 'tel' : 'text'}
      />

      {/* 使用说明 */}
      <div className="text-xs text-gray-500">
        {!needsContact && (
          <p>• 如需要其他用户联系您，请填写联系方式</p>
        )}
        {needsContact && (
          <p>• 请确保联系方式准确，以便买家/配种方/失主/卖家联系您</p>
        )}
        <p>• 平台不会公开您的联系方式，仅在用户点击联系时显示</p>
      </div>
    </div>
  );
};

export default ContactInput;
