import { useState, useEffect, useCallback, useRef } from 'react';
import { Post, PostsQueryParams } from '@/types';
import { petAPI } from '@/lib/cloudbase';
import { showToast } from '@/components/ui/Toast';
import { FilterState } from './usePostFilters';
import { dataCache } from '@/utils/cacheManager';
import { performanceMonitor } from '@/utils/performanceMonitor';
import { queryOptimizer } from '@/utils/queryOptimizer';

interface UsePostsDataOptions {
  filters: FilterState;
  enabled?: boolean;
  debounceMs?: number;
}

interface UsePostsDataReturn {
  posts: Post[];
  loading: boolean;
  loadingMore: boolean;
  error: string | null;
  hasMore: boolean;
  loadMore: () => void;
  refresh: () => void;
  totalCount: number;
}

// 简单的内存缓存
class PostsCache {
  private cache = new Map<string, { data: Post[]; timestamp: number; hasMore: boolean; totalCount: number }>();
  private readonly TTL = 30 * 1000; // 30秒缓存（减少缓存时间）

  private generateKey(filters: FilterState, page: number): string {
    const { limit, ...filterParams } = filters;
    return JSON.stringify({ ...filterParams, page, limit });
  }

  get(filters: FilterState, page: number) {
    const key = this.generateKey(filters, page);
    const cached = this.cache.get(key);
    
    if (cached && Date.now() - cached.timestamp < this.TTL) {
      return cached;
    }
    
    return null;
  }

  set(filters: FilterState, page: number, data: Post[], hasMore: boolean, totalCount: number) {
    const key = this.generateKey(filters, page);
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      hasMore,
      totalCount
    });
  }

  // 清理所有缓存
  clear() {
    this.cache.clear();
    console.log('帖子缓存已清理');
  }

  // 清理特定筛选条件的缓存
  clearByFilters(filters: Partial<FilterState>) {
    const keysToDelete: string[] = [];

    this.cache.forEach((_, key) => {
      try {
        const parsedKey = JSON.parse(key);
        let shouldDelete = false;

        // 如果没有指定筛选条件，清理所有
        if (Object.keys(filters).length === 0) {
          shouldDelete = true;
        } else {
          // 检查是否匹配筛选条件
          for (const [filterKey, filterValue] of Object.entries(filters)) {
            if (parsedKey[filterKey] === filterValue) {
              shouldDelete = true;
              break;
            }
          }
        }

        if (shouldDelete) {
          keysToDelete.push(key);
        }
      } catch (error) {
        // 忽略解析错误
      }
    });

    keysToDelete.forEach(key => this.cache.delete(key));
    console.log(`清理了 ${keysToDelete.length} 个缓存项`);
  }

  // 预加载下一页
  async preloadNextPage(filters: FilterState, currentPage: number) {
    const nextPage = currentPage + 1;
    const key = this.generateKey(filters, nextPage);

    // 检查是否已经缓存
    if (this.cache.get(key)) {
      return;
    }

    try {
      // 预加载下一页数据
      const result = await queryOptimizer.optimizedPostQuery({
        page: nextPage,
        limit: filters.limit,
        sortBy: filters.sortBy,
        category: filters.category || undefined,
        includeUserInfo: true,
        includeCategoryInfo: true,
        useCache: true
      });

      if (result.success && result.data) {
        const posts = Array.isArray(result.data) ? result.data : result.data.posts || [];
        const hasMore = result.pagination?.hasMore || false;
        const total = result.pagination?.total || posts.length;

        this.set(filters, nextPage, posts, hasMore, total);
        console.log(`预加载第${nextPage}页成功，共${posts.length}条数据`);
      }
    } catch (error) {
      console.warn('预加载下一页失败:', error);
    }
  }
}

const postsCache = new PostsCache();

export const usePostsData = ({ 
  filters, 
  enabled = true, 
  debounceMs = 300 
}: UsePostsDataOptions): UsePostsDataReturn => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  
  const abortControllerRef = useRef<AbortController | null>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const currentFiltersRef = useRef<FilterState>(filters);

  // 品种过滤函数（客户端过滤）
  const filterPostsByBreed = useCallback((postsToFilter: Post[], breedFilter: string) => {
    if (!breedFilter.trim()) {
      return postsToFilter;
    }

    const lowerBreedFilter = breedFilter.toLowerCase().trim();
    return postsToFilter.filter(post => {
      if (post.breed && post.breed.toLowerCase().includes(lowerBreedFilter)) {
        return true;
      }
      if (post.title && post.title.toLowerCase().includes(lowerBreedFilter)) {
        return true;
      }
      if (post.tags && post.tags.some(tag => tag.toLowerCase().includes(lowerBreedFilter))) {
        return true;
      }
      return false;
    });
  }, []);

  // 获取帖子数据
  const fetchPosts = useCallback(async (
    targetFilters: FilterState, 
    isLoadMore = false,
    signal?: AbortSignal
  ) => {
    try {
      const currentPage = isLoadMore ? targetFilters.page : 1;
      
      // 检查缓存
      if (!isLoadMore) {
        const cached = postsCache.get(targetFilters, currentPage);
        if (cached) {
          setPosts(cached.data);
          setHasMore(cached.hasMore);
          setTotalCount(cached.totalCount);
          setError(null);
          return;
        }
      }

      if (!isLoadMore) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      let result;
      const startTime = Date.now();

      // 使用优化的查询器
      result = await queryOptimizer.optimizedPostQuery({
        page: currentPage,
        limit: targetFilters.limit,
        sortBy: targetFilters.sortBy,
        category: targetFilters.category || undefined,
        type: targetFilters.petType !== 'all' ? targetFilters.petType : undefined,
        location: targetFilters.location || undefined,
        includeUserInfo: true,
        includeCategoryInfo: true,
        useCache: true
      });

      // 记录性能
      const duration = Date.now() - startTime;
      if (typeof performanceMonitor !== 'undefined') {
        performanceMonitor.recordApiResponse('optimizedPostQuery', duration);
      }

      if (signal?.aborted) {
        return;
      }

      if (result.success) {
        let newPosts = result.data || [];
        
        // 应用品种过滤（客户端过滤）
        if (targetFilters.breed) {
          newPosts = filterPostsByBreed(newPosts, targetFilters.breed);
        }

        if (isLoadMore) {
          setPosts(prev => [...prev, ...newPosts]);
        } else {
          setPosts(newPosts);
        }

        const hasMoreData = result.pagination?.hasMore || false;
        const total = result.pagination?.total || newPosts.length;
        
        setHasMore(hasMoreData);
        setTotalCount(total);
        setError(null);

        // 缓存结果（只缓存第一页的数据）
        if (!isLoadMore) {
          postsCache.set(targetFilters, currentPage, newPosts, hasMoreData, total);

          // 预加载下一页（如果有更多数据）
          if (hasMoreData && currentPage === 1) {
            setTimeout(() => {
              postsCache.preloadNextPage(targetFilters, currentPage);
            }, 1000); // 1秒后预加载
          }
        }
      } else {
        throw new Error(result.message || '获取数据失败');
      }
    } catch (error: any) {
      if (signal?.aborted) {
        return;
      }
      
      console.error('获取宠物列表失败:', error);
      setError(error.message || '获取数据失败');
      
      if (!isLoadMore) {
        showToast.error('获取宠物列表失败，请重试');
      }
    } finally {
      if (!signal?.aborted) {
        setLoading(false);
        setLoadingMore(false);
      }
    }
  }, [filterPostsByBreed]);

  // 防抖的数据获取
  const debouncedFetchPosts = useCallback((targetFilters: FilterState, isLoadMore = false) => {
    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    // 清除之前的防抖定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // 如果是加载更多，不需要防抖
    if (isLoadMore) {
      const controller = new AbortController();
      abortControllerRef.current = controller;
      fetchPosts(targetFilters, true, controller.signal);
      return;
    }

    // 设置防抖定时器
    debounceTimerRef.current = setTimeout(() => {
      const controller = new AbortController();
      abortControllerRef.current = controller;
      fetchPosts(targetFilters, false, controller.signal);
    }, debounceMs);
  }, [fetchPosts, debounceMs]);

  // 加载更多
  const loadMore = useCallback(() => {
    if (!loadingMore && hasMore && enabled) {
      const nextPageFilters = { ...currentFiltersRef.current, page: currentFiltersRef.current.page + 1 };
      currentFiltersRef.current = nextPageFilters;
      debouncedFetchPosts(nextPageFilters, true);
    }
  }, [loadingMore, hasMore, enabled, debouncedFetchPosts]);

  // 刷新数据
  const refresh = useCallback(() => {
    postsCache.clear();
    setPosts([]);
    setError(null);
    setHasMore(true);
    setTotalCount(0);
    
    if (enabled) {
      const refreshFilters = { ...filters, page: 1 };
      currentFiltersRef.current = refreshFilters;
      debouncedFetchPosts(refreshFilters);
    }
  }, [filters, enabled, debouncedFetchPosts]);

  // 监听筛选条件变化
  useEffect(() => {
    if (!enabled) return;

    // 检查是否是重要筛选条件的变化（需要重置数据）
    const isImportantChange = 
      currentFiltersRef.current.category !== filters.category ||
      currentFiltersRef.current.petType !== filters.petType ||
      currentFiltersRef.current.location !== filters.location ||
      currentFiltersRef.current.breed !== filters.breed ||
      currentFiltersRef.current.sortBy !== filters.sortBy;

    if (isImportantChange || filters.page === 1) {
      setPosts([]);
      setError(null);
      setHasMore(true);
      setTotalCount(0);
    }

    currentFiltersRef.current = filters;
    debouncedFetchPosts(filters);

    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [filters, enabled, debouncedFetchPosts]);

  // 清理函数
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  return {
    posts,
    loading,
    loadingMore,
    error,
    hasMore,
    loadMore,
    refresh,
    totalCount
  };
};
