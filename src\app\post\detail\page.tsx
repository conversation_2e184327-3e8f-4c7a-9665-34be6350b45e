'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { ArrowLeft, Eye, EyeOff, Heart, Share2, MessageCircle, User, Clock, MapPin, Star, MoreHorizontal, Flag, ThumbsDown } from 'lucide-react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Navigation, Zoom } from 'swiper/modules';
import { cn } from '@/utils';
import { showToast } from '@/components/ui/Toast';
import { petAPI } from '@/lib/cloudbase';
import RatingModal from '@/components/post/RatingModal';
import ReportModal from '@/components/post/ReportModal';
import PersonalInfoModal from '@/components/profile/PersonalInfoModal';
import ConfirmDialog from '@/components/ui/ConfirmDialog';
import { browseHistoryManager } from '@/utils/historyManager';
import { useAuth } from '@/hooks/useAuth';
import { getFullAddress } from '@/utils/addressUtils';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import 'swiper/css/zoom';

interface Post {
  _id: string;
  breed?: string;
  description?: string;
  images?: string[];
  location?: string;
  created_at: string;
  type?: 'breeding' | 'selling' | 'lost' | 'wanted' | '' | undefined; // 添加 type 字段
  postType?: string; // 保留兼容性
  gender?: 'male' | 'female';
  author?: {
    _id: string;
    nickname: string;
    avatar_url?: string;
  };
  likes_count?: number;
  dislikes_count?: number;
  wants_count?: number;
  user_liked?: boolean;
  user_disliked?: boolean;
  userRating?: number;
  contact_info?: {
    phone?: string;
    wechat?: string;
  };
}

// 格式化日期为 YYYY/M/D 格式
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = date.getMonth() + 1; // getMonth() 返回 0-11，需要加1
  const day = date.getDate();

  return `${year}/${month}/${day}`;
};

export default function PostDetailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const postId = searchParams.get('id');
  const { isLoggedIn, user } = useAuth();
  
  const [post, setPost] = useState<Post | null>(null);
  const [loading, setLoading] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [liked, setLiked] = useState(false);
  const [disliked, setDisliked] = useState(false);
  const [showMoreMenu, setShowMoreMenu] = useState(false);
  const [userRating, setUserRating] = useState(0);
  const [showRatingModal, setShowRatingModal] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [showPersonalInfoModal, setShowPersonalInfoModal] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmAction, setConfirmAction] = useState<'like' | 'dislike' | null>(null);
  const [confirmDialogConfig, setConfirmDialogConfig] = useState<{
    title: string;
    message: string;
    onConfirm: () => void;
  } | null>(null);

  useEffect(() => {
    if (!postId) {
      router.push('/');
      return;
    }

    loadPostDetail();
  }, [postId]);

  const loadPostDetail = async () => {
    try {
      setLoading(true);
      const response = await petAPI.getPostDetail({ postId: postId! });

      if (response.success) {
        setPost(response.data);
        setLiked(response.data.user_interactions?.hasLiked || false);
        setDisliked(response.data.user_interactions?.hasDisliked || false);
        setUserRating(response.data.user_interactions?.userRating || 0);

        // 如果用户已经评分过这个帖子，同步到本地存储
        if (response.data.user_interactions?.hasRated && response.data.user_interactions?.userRating > 0) {
          try {
            const existingRatedPosts = JSON.parse(localStorage.getItem('userRatedPosts') || '[]');
            if (!existingRatedPosts.includes(response.data._id)) {
              existingRatedPosts.unshift(response.data._id);
              // 限制最多保存100个评分记录
              if (existingRatedPosts.length > 100) {
                existingRatedPosts.splice(100);
              }
              localStorage.setItem('userRatedPosts', JSON.stringify(existingRatedPosts));
            }
          } catch (error) {
            console.warn('同步评分记录到本地存储失败:', error);
          }
        }

        // 添加浏览历史记录
        if (response.data.author) {
          browseHistoryManager.addBrowseRecord(
            response.data._id,
            response.data.breed || '宠物发布',
            response.data.author.nickname || '匿名用户',
            response.data.author._id,
            response.data.images?.[0]
          );
        }
      } else {
        showToast.error('加载失败：' + response.message);
        router.push('/');
      }
    } catch (error: any) {
      console.error('加载帖子详情失败:', error);
      showToast.error('加载失败，请重试');
      router.push('/');
    } finally {
      setLoading(false);
    }
  };

  // 重新获取帖子数据（用于评分后刷新）
  const fetchPost = async () => {
    try {
      const response = await petAPI.getPostDetail({ postId: postId! });
      if (response.success) {
        setPost(response.data);
        setLiked(response.data.user_interactions?.hasLiked || false);
        setDisliked(response.data.user_interactions?.hasDisliked || false);
        setUserRating(response.data.user_interactions?.userRating || 0);

        // 如果用户已经评分过这个帖子，同步到本地存储
        if (response.data.user_interactions?.hasRated && response.data.user_interactions?.userRating > 0) {
          try {
            const existingRatedPosts = JSON.parse(localStorage.getItem('userRatedPosts') || '[]');
            if (!existingRatedPosts.includes(response.data._id)) {
              existingRatedPosts.unshift(response.data._id);
              // 限制最多保存100个评分记录
              if (existingRatedPosts.length > 100) {
                existingRatedPosts.splice(100);
              }
              localStorage.setItem('userRatedPosts', JSON.stringify(existingRatedPosts));
            }
          } catch (error) {
            console.warn('同步评分记录到本地存储失败:', error);
          }
        }
      }
    } catch (error: any) {
      console.error('刷新帖子数据失败:', error);
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleLike = async () => {
    if (!post) return;

    // 检查是否登录
    if (!isLoggedIn || !user) {
      showToast.error('请先登录');
      return;
    }

    // 显示确认对话框
    setConfirmDialogConfig({
      title: liked ? '取消点赞' : '点赞确认',
      message: liked ? '确定要取消点赞吗？' : '确定要点赞这个宝贝吗？',
      onConfirm: () => performLikeAction()
    });
  };

  const handleDislike = async () => {
    if (!post) return;

    // 检查是否登录
    if (!isLoggedIn || !user) {
      showToast.error('请先登录');
      return;
    }

    // 显示确认对话框
    setConfirmDialogConfig({
      title: disliked ? '取消不喜欢' : '不喜欢确认',
      message: disliked ? '确定要取消不喜欢吗？' : '确定不喜欢这个宝贝吗？',
      onConfirm: () => performDislikeAction()
    });
  };

  const performLikeAction = async () => {
    if (!post) return;

    try {
      const response = await petAPI.toggleLike({ postId: post._id });
      if (response.success) {
        const newLiked = !liked;
        setLiked(newLiked);
        // 如果点赞，取消不喜欢
        if (newLiked && disliked) {
          setDisliked(false);
        }
        setPost(prev => prev ? {
          ...prev,
          likes_count: (prev.likes_count || 0) + (liked ? -1 : 1),
          dislikes_count: (disliked && newLiked) ? (prev.dislikes_count || 0) - 1 : prev.dislikes_count
        } : null);
        showToast.success(liked ? '取消点赞' : '点赞成功');
      } else {
        showToast.error(response.message || '操作失败');
      }
    } catch (error) {
      console.error('点赞操作失败:', error);
      showToast.error('操作失败，请重试');
    } finally {
      setConfirmDialogConfig(null);
    }
  };

  const performDislikeAction = async () => {
    if (!post) return;

    try {
      const response = await petAPI.toggleDislike({ postId: post._id });
      if (response.success) {
        const newDisliked = !disliked;
        setDisliked(newDisliked);
        // 如果不喜欢，取消点赞
        if (newDisliked && liked) {
          setLiked(false);
        }
        setPost(prev => prev ? {
          ...prev,
          dislikes_count: (prev.dislikes_count || 0) + (disliked ? -1 : 1),
          likes_count: (liked && newDisliked) ? (prev.likes_count || 0) - 1 : prev.likes_count
        } : null);
        showToast.success(disliked ? '取消不喜欢' : '不喜欢');
      } else {
        showToast.error(response.message || '操作失败');
      }
    } catch (error) {
      console.error('不喜欢操作失败:', error);
      showToast.error('操作失败，请重试');
    } finally {
      setConfirmDialogConfig(null);
    }
  };



  const handleShare = () => {
    const url = window.location.href;
    const title = post?.breed || '宠物交易平台';
    const text = `${title} - 来看看这个可爱的宠物吧！`;

    // 尝试使用原生分享API
    if (navigator.share) {
      navigator.share({
        title: title,
        text: text,
        url: url,
      }).then(() => {
        showToast.success('分享成功');
      }).catch((error) => {
        // 如果用户取消分享，不显示错误
        if (error.name !== 'AbortError') {
          // 降级到复制链接
          fallbackShare(url, text);
        }
      });
    } else {
      // 降级到复制链接
      fallbackShare(url, text);
    }
  };

  const fallbackShare = (url: string, text: string) => {
    const shareText = `${text}\n${url}`;
    navigator.clipboard.writeText(shareText).then(() => {
      showToast.success('分享内容已复制到剪贴板');
    }).catch(() => {
      showToast.error('复制失败，请手动复制链接');
    });
  };

  // 强制打开联系方式设置
  const handleForceOpenContactSettings = () => {
    setShowPersonalInfoModal(true);
  };

  // 更新用户资料
  const handleUpdateProfile = async (data: any) => {
    try {
      const result = await petAPI.updateProfile(data);
      if (result.success) {
        showToast.success('资料更新成功');
        // 如果更新了联系方式，同时保存到本地存储
        if (data.contactInfo && user) {
          localStorage.setItem(`contact_${user._id}`, JSON.stringify(data.contactInfo));
        }
        // 如果更新了地址，同时保存到本地存储
        if (data.address !== undefined && user) {
          localStorage.setItem(`address_${user._id}`, data.address);
        }
      } else {
        throw new Error(result.message || '更新失败');
      }
    } catch (error: any) {
      showToast.error(error.message || '更新失败，请重试');
      throw error;
    }
  };

  const handleContact = async () => {
    // 检查是否登录
    if (!isLoggedIn || !user) {
      showToast.error('请先登录');
      return;
    }

    // 检查当前用户是否填写了联系方式
    let userContactInfo = null;
    try {
      const savedContact = localStorage.getItem(`contact_${user._id}`);
      if (savedContact) {
        userContactInfo = JSON.parse(savedContact);
      }
    } catch (error) {
      console.log('获取联系方式失败');
    }

    if (!userContactInfo || !userContactInfo.value) {
      showToast.warning('请先完善联系方式才能使用联系功能');
      // 强制打开个人设置的联系方式标签页
      setShowPersonalInfoModal(true);
      return;
    }

    // 如果都有联系方式，则互换联系方式
    if (post && post.contact_info) {
      try {
        // 调用API发送联系通知
        const result = await petAPI.sendContactNotification({
          postId: post._id,
          authorId: post.author?._id || '',
          userContact: userContactInfo,
          authorContact: post.contact_info
        });

        if (result.success) {
          showToast.success('联系方式已发送至通知');
        } else {
          showToast.error(result.message || '发送失败');
        }
      } catch (error) {
        console.error('发送联系通知失败:', error);
        showToast.error('发送失败，请重试');
      }
    } else {
      showToast.error('对方未提供联系方式');
    }
  };



  const handleReport = () => {
    setShowMoreMenu(false);
    setShowReportModal(true);
  };

  const handleShareFromMenu = () => {
    handleShare();
    setShowMoreMenu(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-gray-500">加载中...</div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-gray-500">宝贝不存在</div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen bg-white relative"
      onClick={() => setShowMoreMenu(false)}
    >
      {/* 顶部按钮组 */}
      <div className={cn(
        "fixed top-4 left-4 z-30 flex space-x-2 transition-opacity duration-300",
        isFullscreen ? "opacity-0 pointer-events-none" : "opacity-100"
      )}>
        {/* 返回按钮 */}
        <button
          onClick={() => router.back()}
          className="w-10 h-10 rounded-full bg-white/90 backdrop-blur-sm border border-gray-200 text-gray-700 flex items-center justify-center hover:bg-gray-100 transition-all duration-200 shadow-sm"
        >
          <ArrowLeft className="h-5 w-5" />
        </button>

        {/* 清屏按钮 */}
        <button
          onClick={toggleFullscreen}
          className="w-10 h-10 rounded-full bg-white/90 backdrop-blur-sm border border-gray-200 text-gray-700 flex items-center justify-center hover:bg-gray-100 transition-all duration-200 shadow-sm"
          title={isFullscreen ? "退出清屏" : "清屏模式"}
        >
          <EyeOff className="h-5 w-5" />
        </button>
      </div>

      {/* 清屏模式下的退出按钮 - 与进入全屏按钮位置一致 */}
      {isFullscreen && (
        <div className="fixed top-4 left-16 z-30">
          <button
            onClick={toggleFullscreen}
            className="w-10 h-10 rounded-full bg-black/50 backdrop-blur-sm border border-white/20 text-white flex items-center justify-center hover:bg-black/70 transition-all duration-200 shadow-sm"
            title="退出清屏"
          >
            <Eye className="h-5 w-5" />
          </button>
        </div>
      )}

      {/* 图片轮播 */}
      <div className="relative h-screen">
        {post.images && post.images.length > 0 ? (
          <Swiper
            modules={[Pagination, Navigation, Zoom]}
            pagination={{ clickable: true }}
            navigation
            zoom={{
              maxRatio: 3,
              minRatio: 1,
              toggle: true,
            }}
            className="h-full"
          >
            {post.images.map((image, index) => (
              <SwiperSlide key={index}>
                <div className="swiper-zoom-container relative h-full">
                  <img
                    src={image}
                    alt={`${post.breed || '宠物'} - ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                  {/* 渐变遮罩 */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent pointer-events-none" />
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        ) : (
          <div className="h-full bg-gray-100 flex items-center justify-center">
            <span className="text-gray-500 text-lg">暂无图片</span>
          </div>
        )}
      </div>

      {/* 右侧操作按钮 */}
      <div className={cn(
        "fixed right-4 bottom-20 z-20 flex flex-col space-y-6 transition-opacity duration-300",
        isFullscreen ? "opacity-0 pointer-events-none" : "opacity-100"
      )}>
        {/* 点赞按钮 */}
        <button
          onClick={handleLike}
          className="flex flex-col items-center justify-center transition-all duration-200 group"
        >
          <Heart className={cn(
            "h-8 w-8 transition-all duration-200 drop-shadow-lg",
            liked
              ? "text-red-500 fill-current scale-110"
              : "text-white hover:scale-110"
          )} />
          <span className="text-xs mt-1 text-white font-bold drop-shadow-lg">
            {post.likes_count || 0}
          </span>
        </button>

        {/* 不喜欢按钮 */}
        <button
          onClick={handleDislike}
          className="flex flex-col items-center justify-center transition-all duration-200 group"
        >
          <ThumbsDown className={cn(
            "h-8 w-8 transition-all duration-200 drop-shadow-lg",
            disliked
              ? "text-gray-600 fill-current scale-110"
              : "text-white hover:scale-110"
          )} />
          <span className="text-xs mt-1 text-white font-bold drop-shadow-lg">
            {post.dislikes_count || 0}
          </span>
        </button>



        {/* 评分按钮 */}
        <button
          onClick={() => {
            // 每次点击评分按钮都刷新帖子数据
            fetchPost();
            setShowRatingModal(true);
          }}
          className="flex flex-col items-center justify-center transition-all duration-200 group"
        >
          <Star className={cn(
            "h-8 w-8 transition-all duration-200 drop-shadow-lg",
            userRating > 0
              ? "text-orange-400 fill-current scale-110"
              : "text-white hover:scale-110"
          )} />
          {userRating > 0 && (
            <span className="text-xs mt-1 text-white font-bold drop-shadow-lg">
              {userRating}
            </span>
          )}
        </button>

        {/* 联系按钮 - 只在需要联系的帖子类型中显示 */}
        {post && (post.type === 'breeding' || post.type === 'selling' || post.type === 'lost' || post.type === 'wanted') && (
          <button
            onClick={handleContact}
            className="flex flex-col items-center justify-center transition-all duration-200 group"
          >
            <MessageCircle className="h-8 w-8 text-blue-400 transition-all duration-200 drop-shadow-lg hover:scale-110" />
          </button>
        )}

        {/* 更多菜单按钮 */}
        <div className="relative">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowMoreMenu(!showMoreMenu);
            }}
            className="flex flex-col items-center justify-center transition-all duration-200 group"
          >
            <MoreHorizontal className="h-8 w-8 text-white transition-all duration-200 drop-shadow-lg hover:scale-110" />
          </button>

          {/* 更多菜单 */}
          {showMoreMenu && (
            <div
              className="absolute right-14 bottom-0 w-32 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-30"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={handleShareFromMenu}
                className="w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
              >
                <Share2 className="h-4 w-4" />
                <span>分享</span>
              </button>
              <button
                onClick={handleReport}
                className="w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
              >
                <Flag className="h-4 w-4" />
                <span>举报</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 左下角信息区域 */}
      <div className={cn(
        "absolute bottom-4 left-4 z-20 bg-black/50 backdrop-blur-md rounded-xl p-4 text-white max-w-sm transition-opacity duration-300 border border-white/10",
        isFullscreen ? "opacity-0 pointer-events-none" : "opacity-100"
      )}>
        {/* 用户信息 */}
        <div className="flex items-center space-x-3 mb-3">
          {/* 用户头像 */}
          {post.author?.avatar_url ? (
            <img
              src={post.author.avatar_url}
              alt={post.author.nickname}
              className="w-12 h-12 rounded-full object-cover border-2 border-white/30 shadow-lg"
            />
          ) : (
            <div className="w-12 h-12 rounded-full bg-gray-600/80 border-2 border-white/30 flex items-center justify-center shadow-lg">
              <User className="h-6 w-6 text-white" />
            </div>
          )}

          {/* 用户昵称 */}
          <div className="flex-1 min-w-0">
            {post.author?._id && post.author._id !== 'anonymous' ? (
              <button
                onClick={() => {
                  // 获取当前用户信息
                  let currentUserId = null;
                  try {
                    const savedUser = localStorage.getItem('pet_platform_user');
                    if (savedUser) {
                      const userData = JSON.parse(savedUser);
                      currentUserId = userData._id;
                    }
                  } catch (error) {
                    console.log('获取当前用户信息失败');
                  }

                  // 如果是自己的帖子，跳转到自己的个人主页
                  if (currentUserId && post.author && currentUserId === post.author._id) {
                    router.push('/profile');
                  } else if (post.author) {
                    // 如果是别人的帖子，跳转到作者的个人主页
                    router.push(`/profile/${post.author._id}`);
                  }
                }}
                className="font-semibold text-white hover:text-white/80 transition-colors block truncate text-base"
              >
                {post.author?.nickname || '匿名用户'}
              </button>
            ) : (
              <span className="font-semibold text-white block truncate text-base">
                {post.author?.nickname || '匿名用户'}
              </span>
            )}
          </div>
        </div>

        {/* 时间和地点信息 */}
        <div className="space-y-1 mb-3">
          {/* 发布时间 */}
          <div className="flex items-center space-x-2 text-sm text-white/90">
            <Clock className="h-4 w-4 flex-shrink-0" />
            <span>{formatDate(post.created_at)}</span>
          </div>

          {/* 发布地点 */}
          {post.location && (
            <div className="flex items-center space-x-2 text-sm text-white/90">
              <MapPin className="h-4 w-4 flex-shrink-0" />
              <span className="truncate">{getFullAddress(post.location)}</span>
            </div>
          )}

          {/* 性别信息（仅配种类型显示） */}
          {post.postType === 'breeding' && post.gender && (
            <div className="flex items-center space-x-2 text-sm text-white/90">
              <span className={cn(
                "px-2 py-1 rounded-full text-xs font-medium",
                post.gender === 'male'
                  ? "bg-blue-500/80 text-white"
                  : "bg-pink-500/80 text-white"
              )}>
                {post.gender === 'male' ? '雄性' : '雌性'}
              </span>
            </div>
          )}
        </div>

        {/* 宝贝描述 */}
        {post.description && (
          <div className="border-t border-white/20 pt-3">
            <div className="text-sm text-white leading-relaxed">
              <p className="line-clamp-4">{post.description}</p>
            </div>
          </div>
        )}
      </div>

      {/* 评分模态框 */}
      {post && (
        <RatingModal
          isOpen={showRatingModal}
          onClose={() => setShowRatingModal(false)}
          post={post as any}
          onSuccess={() => {
            // 评分成功后刷新帖子数据
            fetchPost();
          }}
        />
      )}

      {/* 举报模态框 */}
      {post && (
        <ReportModal
          isOpen={showReportModal}
          onClose={() => setShowReportModal(false)}
          postId={post._id}
          onSuccess={() => {
            setShowReportModal(false);
            showToast.success('举报已提交，我们会尽快处理');
          }}
        />
      )}

      {/* 个人设置模态框 */}
      {user && (
        <PersonalInfoModal
          isOpen={showPersonalInfoModal}
          onClose={() => setShowPersonalInfoModal(false)}
          currentUser={user}
          onUpdate={handleUpdateProfile}
          forceContactTab={true} // 强制打开联系方式标签页
        />
      )}

      {/* 确认对话框 */}
      {confirmDialogConfig && (
        <ConfirmDialog
          isOpen={true}
          onClose={() => setConfirmDialogConfig(null)}
          onConfirm={confirmDialogConfig.onConfirm}
          title={confirmDialogConfig.title}
          message={confirmDialogConfig.message}
          type="info"
        />
      )}
    </div>
  );
}
