exports.id=240,exports.ids=[240],exports.modules={93113:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,12994,23)),Promise.resolve().then(s.t.bind(s,96114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,79671,23)),Promise.resolve().then(s.t.bind(s,41868,23)),Promise.resolve().then(s.t.bind(s,84759,23))},88462:(e,t,s)=>{Promise.resolve().then(s.bind(s,35659)),Promise.resolve().then(s.bind(s,69596)),Promise.resolve().then(s.bind(s,20603))},35659:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>n,E:()=>c,Y:()=>d});var a=s(10326),r=s(17577),o=s(74131),i=s(16545);let l=(0,r.createContext)(void 0),n=({children:e})=>{let t=(0,o.a)();return t.isLoading&&!t.user?a.jsx(i.SX,{text:"正在初始化..."}):a.jsx(l.Provider,{value:t,children:e})},c=()=>{let e=(0,r.useContext)(l);if(void 0===e)throw Error("useAuthContext must be used within an AuthProvider");return e},d=({children:e,fallback:t})=>{let{isLoggedIn:s,isLoading:r}=c();return r?a.jsx(i.SX,{text:"验证登录状态..."}):s?a.jsx(a.Fragment,{children:e}):t||a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"需要登录"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录后再访问此页面"})]})})}},69596:(e,t,s)=>{"use strict";s.d(t,{default:()=>g});var a=s(10326);s(17577);var r=s(90434),o=s(35047),i=s(95920),l=s(88307),n=s(83855),c=s(6507),d=s(79635),p=s(35659),u=s(28295);let g=()=>{let e=(0,o.usePathname)(),{user:t,isLoggedIn:s}=(0,p.E)(),g=[{href:"/",icon:i.Z,label:"首页",active:"/"===e},{href:"/search",icon:l.Z,label:"搜索",active:"/search"===e},{href:"/upload",icon:n.Z,label:"发布",active:"/upload"===e,requireAuth:!0},{href:"/notifications",icon:c.Z,label:"通知",active:"/notifications"===e,requireAuth:!0},{href:s?"/profile":"/login",icon:d.Z,label:"我",active:"/profile"===e}];return a.jsx("nav",{className:"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 md:hidden",children:a.jsx("div",{className:"flex items-center justify-around h-16",children:g.map(e=>{let t=e.icon,o=e.requireAuth&&!s?"/login":e.href,i=e.active;return(0,a.jsxs)(r.default,{href:o,className:(0,u.cn)("flex flex-col items-center justify-center flex-1 h-full transition-colors",i?"text-primary-600":"text-gray-500 hover:text-gray-700"),children:[a.jsx(t,{className:(0,u.cn)("h-5 w-5 mb-1",i&&"text-primary-600")}),a.jsx("span",{className:(0,u.cn)("text-xs font-medium",i&&"text-primary-600"),children:e.label})]},e.label)})})})}},16545:(e,t,s)=>{"use strict";s.d(t,{LL:()=>n,SX:()=>i,gG:()=>l,gb:()=>o});var a=s(10326);s(17577);var r=s(28295);let o=({size:e="md",variant:t="spinner",className:s,text:o})=>{let i={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"},l={sm:"text-sm",md:"text-base",lg:"text-lg"};if("spinner"===t)return a.jsx("div",{className:(0,r.cn)("flex items-center justify-center",s),children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsxs)("svg",{className:(0,r.cn)("animate-spin text-primary-600",i[e]),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[a.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),a.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),o&&a.jsx("p",{className:(0,r.cn)("text-gray-500",l[e]),children:o})]})});if("dots"===t){let t="sm"===e?"w-2 h-2":"md"===e?"w-3 h-3":"w-4 h-4";return a.jsx("div",{className:(0,r.cn)("flex items-center justify-center",s),children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsxs)("div",{className:"flex space-x-1",children:[a.jsx("div",{className:(0,r.cn)("bg-primary-600 rounded-full animate-bounce",t),style:{animationDelay:"0ms"}}),a.jsx("div",{className:(0,r.cn)("bg-primary-600 rounded-full animate-bounce",t),style:{animationDelay:"150ms"}}),a.jsx("div",{className:(0,r.cn)("bg-primary-600 rounded-full animate-bounce",t),style:{animationDelay:"300ms"}})]}),o&&a.jsx("p",{className:(0,r.cn)("text-gray-500",l[e]),children:o})]})})}return"pulse"===t?a.jsx("div",{className:(0,r.cn)("flex items-center justify-center",s),children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[a.jsx("div",{className:(0,r.cn)("bg-primary-600 rounded-full animate-pulse",i[e])}),o&&a.jsx("p",{className:(0,r.cn)("text-gray-500",l[e]),children:o})]})}):null},i=({text:e="加载中..."})=>a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:a.jsx(o,{size:"lg",text:e})}),l=()=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden animate-pulse",children:[a.jsx("div",{className:"aspect-square bg-gray-200"}),(0,a.jsxs)("div",{className:"p-3 space-y-2",children:[a.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),a.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/2"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/4"}),a.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/4"})]})]})]}),n=()=>a.jsx("div",{className:"animate-pulse",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4 p-6",children:[a.jsx("div",{className:"w-24 h-24 bg-gray-200 rounded-full"}),a.jsx("div",{className:"h-6 bg-gray-200 rounded w-32"}),a.jsx("div",{className:"h-4 bg-gray-200 rounded w-48"}),(0,a.jsxs)("div",{className:"flex space-x-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),a.jsx("div",{className:"h-4 bg-gray-200 rounded w-8"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),a.jsx("div",{className:"h-4 bg-gray-200 rounded w-8"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),a.jsx("div",{className:"h-4 bg-gray-200 rounded w-8"})]})]}),a.jsx("div",{className:"h-10 bg-gray-200 rounded w-24"})]})})},20603:(e,t,s)=>{"use strict";s.d(t,{C:()=>g,ToastProvider:()=>m});var a=s(10326);s(17577);var r=s(40381),o=s(54659),i=s(91470),l=s(87888),n=s(18019),c=s(94019),d=s(28295);let p={duration:4e3,position:"top-center",style:{background:"#fff",color:"#374151",border:"1px solid #e5e7eb",borderRadius:"8px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",padding:"12px 16px",fontSize:"14px",maxWidth:"400px"}},u=({message:e,type:t,onDismiss:s})=>{let r={success:a.jsx(o.Z,{className:"h-5 w-5 text-green-500"}),error:a.jsx(i.Z,{className:"h-5 w-5 text-red-500"}),warning:a.jsx(l.Z,{className:"h-5 w-5 text-yellow-500"}),info:a.jsx(n.Z,{className:"h-5 w-5 text-blue-500"})};return(0,a.jsxs)("div",{className:(0,d.cn)("flex items-center space-x-3 p-3 rounded-lg border shadow-lg",{success:"bg-green-50 border-green-200",error:"bg-red-50 border-red-200",warning:"bg-yellow-50 border-yellow-200",info:"bg-blue-50 border-blue-200"}[t]),children:[r[t],a.jsx("span",{className:"flex-1 text-sm font-medium",children:e}),a.jsx("button",{onClick:s,className:"text-gray-400 hover:text-gray-600 transition-colors",children:a.jsx(c.Z,{className:"h-4 w-4"})})]})},g={success:e=>{r.Am.custom(t=>a.jsx(u,{message:e,type:"success",onDismiss:()=>r.Am.dismiss(t.id)}),p)},error:e=>{r.Am.custom(t=>a.jsx(u,{message:e,type:"error",onDismiss:()=>r.Am.dismiss(t.id)}),{...p,duration:6e3})},warning:e=>{r.Am.custom(t=>a.jsx(u,{message:e,type:"warning",onDismiss:()=>r.Am.dismiss(t.id)}),p)},info:e=>{r.Am.custom(t=>a.jsx(u,{message:e,type:"info",onDismiss:()=>r.Am.dismiss(t.id)}),p)},loading:e=>r.Am.loading(e,{style:p.style,position:p.position}),dismiss:e=>{r.Am.dismiss(e)},promise:(e,t)=>r.Am.promise(e,t,{style:p.style,position:p.position})},m=()=>a.jsx(r.x7,{position:"top-center",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"transparent",boxShadow:"none",padding:0}}})},74131:(e,t,s)=>{"use strict";s.d(t,{a:()=>i});var a=s(17577),r=s(41828),o=s(20603);let i=()=>{let[e,t]=(0,a.useState)(null),[s,i]=(0,a.useState)(!0),[l,n]=(0,a.useState)(!1),c=(0,a.useCallback)(e=>{try{e?(localStorage.setItem("pet_platform_user",JSON.stringify(e)),localStorage.setItem("pet_platform_logged_in","true")):(localStorage.removeItem("pet_platform_user"),localStorage.removeItem("pet_platform_logged_in"))}catch(e){console.error("保存用户状态失败:",e)}},[]),d=(0,a.useCallback)(()=>{try{let e=localStorage.getItem("pet_platform_user"),s=localStorage.getItem("pet_platform_logged_in");if(e&&"true"===s){let s=JSON.parse(e);return t(s),n(!0),s}}catch(e){console.error("恢复用户状态失败:",e)}return null},[]),p=(0,a.useCallback)(async()=>{try{i(!0);let e=d();if(e)try{if(e.email){let s=await r.authAPI.getCurrentUser(e.email);s.success&&s.data?(t(s.data),n(!0),c(s.data)):(t(null),n(!1),c(null))}else console.log("使用本地保存的用户信息（匿名用户）"),t(e),n(!0)}catch(s){console.error("验证用户状态失败:",s),e?(console.log("验证失败，使用本地用户信息"),t(e),n(!0)):(t(null),n(!1),c(null))}else{console.log("没有保存的用户信息，尝试匿名登录");try{let e=await r.authAPI.getCurrentUser();e.success&&e.data?(t(e.data),n(!0),c(e.data)):(t(null),n(!1))}catch(e){console.error("匿名登录失败:",e),t(null),n(!1)}}}catch(e){console.error("检查登录状态失败:",e),t(null),n(!1)}finally{i(!1)}},[d,c]),u=(0,a.useCallback)(async(e,s)=>{try{if(i(!0),!e||!s)return o.C.error("请输入邮箱和密码"),!1;let a=await r.authAPI.loginWithEmail(e,s);if(a.success)return t(a.data),n(!0),c(a.data),a.data.isNewUser?o.C.success("欢迎加入宠物交易平台！"):o.C.success("登录成功！"),!0;return o.C.error(a.message||"登录失败"),!1}catch(e){return console.error("登录失败:",e),o.C.error(e.message||"登录失败，请重试"),!1}finally{i(!1)}},[c]),g=(0,a.useCallback)(async()=>{try{return i(!0),await r.authAPI.logout(),t(null),n(!1),c(null),o.C.success("已退出登录"),!0}catch(e){return console.error("退出登录失败:",e),o.C.error("退出登录失败"),!1}finally{i(!1)}},[c]),m=(0,a.useCallback)(async s=>{if(!e)return!1;try{i(!0),console.log("更新用户资料:",s);let a={...e,...s};return t(a),c(a),o.C.success("资料更新成功"),!0}catch(e){return console.error("更新资料失败:",e),o.C.error("更新失败"),!1}finally{i(!1)}},[e,c]),y=(0,a.useCallback)(async()=>{if(l)try{let e=await r.authAPI.getCurrentUser();e.success&&e.data&&(t(e.data),c(e.data))}catch(e){console.error("刷新用户信息失败:",e)}},[l,c]),h=(0,a.useCallback)(async()=>{try{let e=d(),s=await r.authAPI.getCurrentUser(e?.email);s.success&&s.data?(t(s.data),n(!0),c(s.data)):(t(null),n(!1),c(null))}catch(e){console.error("刷新登录状态失败:",e),t(null),n(!1),c(null)}},[c,d]);return(0,a.useEffect)(()=>{(async()=>{let e=d();if(e){t(e),n(!0),i(!1);try{console.log("后台验证用户状态...");let s=await r.authAPI.getCurrentUser(e.email);s.success&&s.data?(t(s.data),c(s.data),console.log("用户状态验证成功")):console.warn("云端验证失败，但保持本地状态")}catch(e){console.error("后台验证失败:",e)}}else await p()})()},[p,d,c]),(0,a.useEffect)(()=>{},[]),{user:e,isLoading:s,isLoggedIn:l,login:u,logout:g,updateProfile:m,refreshUser:y,refreshLoginState:h}}},41828:(e,t,s)=>{"use strict";s.r(t),s.d(t,{activityAPI:()=>p,app:()=>a,auth:()=>r,authAPI:()=>u,db:()=>o,default:()=>f,getImageUrl:()=>m,initCloudBase:()=>i,petAPI:()=>d,uploadFile:()=>x,uploadFileToStatic:()=>y}),s(93386);let a=null,r=null,o=null,i=async()=>(console.log("非浏览器环境，跳过CloudBase初始化"),null),l=()=>{try{let e=localStorage.getItem("pet_platform_user"),t="true"===localStorage.getItem("pet_platform_logged_in");return e&&t}catch(e){return!1}},n=["toggleLike","toggleDislike","toggleBookmark","ratePet","exchangeContact","toggleFollow","reportPost","reportUser","wantPet","submitAppeal","getUserBookmarks","getUserPosts","getUserFollowing","getUserPermissions"],c=async(e,t,s)=>{try{if(console.log(`🚀 开始调用云函数: ${e}.${t}`,s),n.includes(t)){if(!l())throw console.log("❌ 操作需要登录，但用户未登录"),Error("请先登录后再进行此操作");console.log("✅ 用户已登录，可以执行操作")}console.log("\uD83D\uDD0D 检查CloudBase初始化状态...");let a=await i();if(!a)throw console.error("❌ CloudBase未初始化"),Error("CloudBase未初始化");console.log("\uD83D\uDD10 检查用户登录状态..."),r||(r=a.auth()),console.log("✅ CloudBase已初始化，准备调用云函数");let o=null,c=null;try{let e=localStorage.getItem("pet_platform_user");if(e){let t=JSON.parse(e);o=t._id,c=t._id,console.log("✅ 使用注册用户ID:",o)}else console.log("❌ 未找到注册用户信息")}catch(e){console.error("获取注册用户ID失败:",e)}let d=!["getUserInfo","getUserPosts","getUserBookmarks","getUserFollowing"].includes(t),p={name:e,data:{action:t,data:d?{...s,userId:o,openId:c}:{...s,currentUserId:o,openId:c}}};console.log("\uD83D\uDCCB 云函数调用参数:",p),console.log("\uD83D\uDCE1 正在调用云函数...");let u=await a.callFunction(p);if(console.log(`✅ ${e}.${t} 调用成功:`,u),u.result)return console.log("\uD83D\uDCE6 返回result字段:",u.result),u.result;return console.log("\uD83D\uDCE6 返回完整结果:",u),u}catch(s){console.error(`❌ ${e}.${t} 调用失败:`,s),console.error("❌ 错误类型:",typeof s),console.error("❌ 错误构造函数:",s?.constructor?.name),s?.message&&console.error(`❌ 错误消息: ${s.message}`),s?.code&&console.error(`❌ 错误代码: ${s.code}`),s?.name&&console.error(`❌ 错误名称: ${s.name}`),s?.stack&&console.error(`❌ 错误堆栈:`,s.stack);try{console.error("❌ 错误对象JSON:",JSON.stringify(s,null,2))}catch(e){console.error("❌ 无法序列化错误对象:",e),console.error("❌ 错误对象属性:",Object.keys(s)),console.error("❌ 错误对象值:",Object.values(s))}throw s?.response&&console.error("❌ 响应错误:",s.response),s?.request&&console.error("❌ 请求错误:",s.request),s}},d={getPosts:async(e={})=>c("pet-api","getPosts",e),getOptimizedPosts:async(e={})=>c("optimizedPostQuery","query",e),getPostDetail:async e=>c("pet-api","getPostDetail",e),createPost:async e=>c("pet-api","createPost",e),likePost:async e=>c("pet-api","likePost",e),toggleLike:async e=>c("pet-api","toggleLike",e),toggleDislike:async e=>c("pet-api","toggleDislike",e),toggleBookmark:async e=>c("pet-api","toggleBookmark",e),bookmarkPost:async e=>c("pet-api","bookmarkPost",e),exchangeContact:async e=>c("pet-api","exchangeContact",e),ratePost:async e=>c("pet-api","ratePost",e),toggleFollow:async e=>c("pet-api","toggleFollow",e),getCategories:async()=>c("pet-api","getCategories"),ratePet:async e=>c("pet-api","ratePet",e),reportPost:async e=>c("pet-api","reportPost",e),getUserBookmarks:async(e={})=>c("pet-api","getUserBookmarks",e),getUserPosts:async(e={})=>c("pet-api","getUserPosts",e),getUserFollowing:async(e={})=>c("pet-api","getUserFollowing",e),getUserFollowers:async(e={})=>c("pet-api","getUserFollowers",e),getUserNotifications:async(e={})=>c("pet-api","getUserNotifications",e),markNotificationRead:async e=>c("pet-api","markNotificationRead",e),deleteNotification:async e=>c("pet-api","deleteNotification",e),bulkDeleteNotifications:async e=>c("pet-api","bulkDeleteNotifications",e),updateProfile:async e=>c("pet-api","updateProfile",e),sendContactNotification:async e=>c("pet-api","sendContactNotification",e),getReports:async(e={})=>c("pet-api","getReports",e),handleReport:async e=>c("pet-api","handleReport",e),banUser:async e=>c("pet-api","banUser",e),getUserInfo:async e=>c("pet-api","getUserInfo",e),uploadToStatic:async e=>c("pet-api","uploadToStatic",e),updateAvatar:async e=>c("pet-api","updateAvatar",e),getImage:async e=>c("pet-api","getImage",e),blockUser:async e=>c("pet-api","blockUser",e),unblockUser:async e=>c("pet-api","unblockUser",e),getBlockedUsers:async(e={})=>c("pet-api","getBlockedUsers",e),reportUser:async e=>c("pet-api","reportUser",e),submitAppeal:async e=>c("pet-api","submitAppeal",e),getUserPermissions:async e=>c("pet-api","getUserPermissions",e),getAppeals:async(e={})=>c("pet-api","getAppeals",e),handleAppeal:async e=>c("pet-api","handleAppeal",e),adminLogin:async e=>c("pet-api","adminLogin",e),getAdmins:async(e={})=>c("pet-api","getAdmins",e),createAdmin:async e=>c("pet-api","createAdmin",e),updateAdmin:async e=>c("pet-api","updateAdmin",e),deleteAdmin:async e=>c("pet-api","deleteAdmin",e),getAds:async e=>c("pet-api","getAds",e||{}),getAdPositions:async e=>c("pet-api","getAdPositions",e||{}),createAd:async e=>c("pet-api","createAd",e),updateAd:async e=>c("pet-api","updateAd",e),deleteAd:async e=>c("pet-api","deleteAd",e),getAdStatistics:async e=>c("pet-api","getAdStatistics",e||{}),updatePostPriority:async e=>c("pet-api","updatePostPriority",e),getPostQualityScore:async e=>c("pet-api","getPostQualityScore",e),batchUpdatePostPriority:async e=>c("pet-api","batchUpdatePostPriority",e),getPostsByPriority:async e=>c("pet-api","getPostsByPriority",e||{}),batchUpdateAllPostsQuality:async()=>c("pet-api","batchUpdateAllPostsQuality",{}),createActivity:async e=>c("pet-api","createActivity",e),getActivities:async e=>c("pet-api","getActivities",e||{}),getActivityDetail:async e=>c("pet-api","getActivityDetail",e),updateActivity:async e=>c("pet-api","updateActivity",e),deleteActivity:async e=>c("pet-api","deleteActivity",e),voteInActivity:async e=>c("pet-api","voteInActivity",e),addActivityComment:async e=>c("pet-api","addActivityComment",e),getActivityComments:async e=>c("pet-api","getActivityComments",e),getSystemConfig:async()=>c("pet-api","getSystemConfig",{}),updateSystemConfig:async e=>c("pet-api","updateSystemConfig",e),getPermissions:async()=>c("pet-api","getPermissions",{}),getUserRatedPosts:async e=>c("pet-api","getUserRatedPosts",e),updatePostStatus:async e=>c("pet-api","updatePostStatus",e),deletePost:async e=>c("pet-api","deleteMyPost",e),adminDeletePost:async e=>c("pet-api","adminDeletePost",e),deleteCloudFile:async e=>c("pet-api","deleteCloudFile",e),getPostsForAdmin:async(e={})=>c("pet-api","getPostsForAdmin",e)},p={getActiveActivities:async()=>c("pet-api","getActiveActivities"),getActivities:async(e={})=>c("pet-api","getActivities",e),getSystemConfig:async()=>c("pet-api","getSystemConfig"),getDashboardStats:async()=>c("pet-api","getDashboardStats")},u={sendVerificationCode:async(e,t="register")=>c("email-auth","sendVerificationCode",{email:e,type:t}),verifyCode:async(e,t,s="register")=>c("email-auth","verifyCode",{email:e,code:t,type:s}),registerWithEmail:async(e,t,s,a)=>c("email-auth","registerWithEmail",{email:e,password:t,nickname:s,verificationCode:a}),loginWithEmail:async(e,t)=>c("email-auth","loginWithEmail",{email:e,password:t}),resetPassword:async(e,t,s)=>c("email-auth","resetPassword",{email:e,verificationCode:t,newPassword:s}),changePassword:async(e,t,s,a)=>c("email-auth","changePassword",{email:e,verificationCode:t,oldPassword:s,newPassword:a}),getCurrentUser:async e=>c("user-auth","getCurrentUser",e?{email:e}:{}),logout:async()=>c("user-auth","logout")},g=async(e,t=800,s=.8)=>new Promise(a=>{let r=document.createElement("canvas"),o=r.getContext("2d"),i=new Image;i.onload=()=>{let{width:l,height:n}=i,c=l,d=n,p=s;l>2e3||n>2e3?(d=n*(c=Math.min(l,t))/l,p=.8):l>t?(d=n*t/l,c=t,p=s):p=Math.max(.9,s),r.width=c,r.height=d,o?.drawImage(i,0,0,c,d),r.toBlob(t=>{if(t){let s=new File([t],e.name,{type:"image/jpeg",lastModified:Date.now()}),r=Math.round((1-s.size/e.size)*100);console.log(`图片压缩完成: ${e.size} -> ${s.size} (${r}% 减少)`),console.log(`压缩参数: ${l}x${n} -> ${c}x${d}, 质量: ${Math.round(100*p)}%`),a(s)}else a(e)},"image/jpeg",p)},i.src=URL.createObjectURL(e)}),m=async e=>{try{let t=await i();if(!t)throw Error("CloudBase未初始化");let s=await t.callFunction({name:"pet-api",data:{action:"getImage",data:{fileId:e}}});if(s.result?.success&&s.result?.data?.url)return s.result.data.url;return console.error("获取图片URL失败:",s.result?.message),"/placeholder-image.png"}catch(e){return console.error("获取图片URL失败:",e),"/placeholder-image.png"}},y=async e=>{try{console.log("开始上传文件到静态托管:",e.name,"原始大小:",e.size);let t=e;e.type.startsWith("image/")&&(t=await g(e));let s=Date.now(),a=Math.random().toString(36).substring(2),r=e.name.split(".").pop(),o=`${s}_${a}.${r}`;console.log("压缩后大小:",t.size);let l=await h(t),n=await i();if(!n)throw Error("CloudBase未初始化");let c=await n.callFunction({name:"pet-api",data:{action:"uploadToStatic",data:{fileName:o,fileData:l,contentType:t.type}}});if(c.result?.success&&c.result?.data?.url)return console.log("文件上传成功，URL:",c.result.data.url),c.result.data.url;throw Error(c.result?.message||"上传失败")}catch(e){throw console.error("文件上传失败:",e),e}},h=e=>new Promise((t,s)=>{let a=new FileReader;a.readAsDataURL(e),a.onload=()=>{t(a.result.split(",")[1])},a.onerror=e=>s(e)}),x=async e=>await y(e),f={petAPI:d,authAPI:u,activityAPI:p,uploadFile:x}},28295:(e,t,s)=>{"use strict";s.d(t,{cn:()=>o,uf:()=>i,vV:()=>l});var a=s(41135),r=s(31009);function o(...e){return(0,r.m6)((0,a.W)(e))}function i(e){return e<1e3?e.toString():e<1e4?`${(e/1e3).toFixed(1)}k`:e<1e5?`${(e/1e4).toFixed(1)}w`:`${Math.floor(e/1e4)}w`}function l(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}},16953:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,generateViewport:()=>c,metadata:()=>n});var a=s(19510),r=s(68570);let o=(0,r.createProxy)(String.raw`D:\web-cloudbase-project\src\components\auth\AuthProvider.tsx#AuthProvider`);(0,r.createProxy)(String.raw`D:\web-cloudbase-project\src\components\auth\AuthProvider.tsx#useAuthContext`),(0,r.createProxy)(String.raw`D:\web-cloudbase-project\src\components\auth\AuthProvider.tsx#RequireAuth`),(0,r.createProxy)(String.raw`D:\web-cloudbase-project\src\components\ui\Toast.tsx#showToast`);let i=(0,r.createProxy)(String.raw`D:\web-cloudbase-project\src\components\ui\Toast.tsx#ToastProvider`),l=(0,r.createProxy)(String.raw`D:\web-cloudbase-project\src\components\layout\BottomNav.tsx#default`);s(5023);let n={title:"宠物交易平台 - TikTok风格的现代化宠物交易平台",description:"一个基于腾讯云开发的现代化宠物交易平台，采用TikTok风格的UI设计和完整的社交功能",keywords:["宠物","交易","平台","TikTok","社交","云开发"],authors:[{name:"Pet Trading Platform Team"}],manifest:"/pet-platform-v2/manifest.json",icons:{icon:"/pet-platform-v2/favicon.ico",apple:"/pet-platform-v2/apple-touch-icon.png"},openGraph:{title:"宠物交易平台",description:"TikTok风格的现代化宠物交易平台",type:"website",locale:"zh_CN",siteName:"宠物交易平台"},twitter:{card:"summary_large_image",title:"宠物交易平台",description:"TikTok风格的现代化宠物交易平台"}};function c(){return{width:"device-width",initialScale:1,themeColor:"#3b82f6"}}function d({children:e}){return(0,a.jsxs)("html",{lang:"zh-CN",children:[a.jsx("head",{}),a.jsx("body",{className:"font-sans",children:(0,a.jsxs)(o,{children:[a.jsx("div",{className:"pb-16 md:pb-0",children:e}),a.jsx(l,{}),a.jsx(i,{})]})})]})}},5023:()=>{}};