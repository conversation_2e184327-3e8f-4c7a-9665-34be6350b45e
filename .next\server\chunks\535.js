"use strict";exports.id=535,exports.ids=[535],exports.modules={67427:(e,t,r)=>{r.d(t,{Z:()=>l});let l=(0,r(76557).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},77636:(e,t,r)=>{r.d(t,{Z:()=>l});let l=(0,r(76557).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},88378:(e,t,r)=>{r.d(t,{Z:()=>l});let l=(0,r(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},22502:(e,t,r)=>{r.d(t,{Z:()=>m});var l=r(10326),s=r(17577),a=r(90434),i=r(94019),n=r(79635),o=r(77636),c=r(67427),d=r(28295),u=r(46424),g=r(42443);let h=({src:e,alt:t,className:r,placeholder:a,fallback:i="/images/placeholder.jpg",onLoad:n,onError:o,priority:c=!1,quality:u=75,sizes:h})=>{let[m,x]=(0,s.useState)("loading"),[f,p]=(0,s.useState)(a||""),y=(0,s.useRef)(null),{ref:v,inView:b}=(0,g.YD)({threshold:0,rootMargin:"200px",triggerOnce:!0,skip:c}),w=(e,t=75)=>{if(!e)return e;if(e.includes(".myqcloud.com")||e.includes(".cos.")){let r=e.includes("?")?"&":"?";return`${e}${r}imageMogr2/quality/${t}/format/webp`}return e.includes(".tcloudbaseapp.com"),e},j=e=>new Promise((t,r)=>{let l=new Image;l.onload=()=>{x("loaded"),p(e),n?.(),t()},l.onerror=()=>{x("error"),p(i),o?.(),r(Error("Image load failed"))},l.src=e});(0,s.useEffect)(()=>{if(c||b){let t=w(e,u),r=performance.now();j(t).then(()=>{let t=performance.now()-r;console.log(`图片加载完成: ${e}, 耗时: ${t.toFixed(2)}ms`)}).catch(()=>{t!==e?(console.warn("优化图片加载失败，尝试原图:",e),j(e).catch(()=>{console.error("原图加载也失败:",e),x("error"),p(i)})):(x("error"),p(i))})}},[b,c,e,u,i,n,o]);let N=(0,s.useCallback)(()=>{if(!c&&!b&&"loading"===m){let t=w(e,u);console.log("鼠标悬停预加载:",e),j(t).catch(()=>{})}},[c,b,m,e,u]);return(0,l.jsxs)("div",{ref:v,className:"relative overflow-hidden",onMouseEnter:N,children:["loading"===m&&!f&&l.jsx("div",{className:(0,d.cn)("bg-gray-200 animate-pulse flex items-center justify-center",r),children:l.jsx("svg",{className:"w-8 h-8 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:l.jsx("path",{fillRule:"evenodd",d:"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z",clipRule:"evenodd"})})}),"error"===m&&l.jsx("div",{className:(0,d.cn)("bg-gray-100 flex items-center justify-center text-gray-400",r),children:l.jsx("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 20 20",children:l.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),("loaded"===m||f)&&l.jsx("img",{ref:y,src:f,alt:t,className:(0,d.cn)("transition-opacity duration-300","loaded"===m?"opacity-100":"opacity-0",r),sizes:h,loading:c?"eager":"lazy",decoding:"async"}),"loading"===m&&f&&l.jsx("div",{className:"absolute inset-0 bg-gray-200 animate-pulse"})]})},m=({post:e,className:t,showRemoveFromFavorites:r=!1,onRemoveFromFavorites:s,isDraft:g=!1})=>{let m=e.images?.[0]||"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center",x=(e=>{switch(e){case"breeding":return{label:"配种",color:"bg-pink-500"};case"selling":return{label:"出售",color:"bg-green-500"};case"lost":return{label:"寻回",color:"bg-orange-500"};default:return null}})(e.type);return l.jsx(a.default,{href:`/post/detail?id=${e._id}`,children:(0,l.jsxs)("div",{className:(0,d.cn)("bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200 cursor-pointer h-full flex flex-col",t),children:[(0,l.jsxs)("div",{className:"relative w-full overflow-hidden",style:{height:"304px"},children:[l.jsx(h,{src:m,alt:e.breed||"宠物图片",className:"w-full h-full object-cover rounded-t-lg",fallback:"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center",quality:80,sizes:"(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 20vw"}),g&&l.jsx("div",{className:"absolute top-2 left-2 bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full font-medium",children:"待发布"}),!g&&x&&l.jsx("div",{className:(0,d.cn)("absolute top-2 left-2 text-white text-xs px-2 py-1 rounded-full font-medium",x.color),children:x.label}),e.images&&e.images.length>1&&(0,l.jsxs)("div",{className:"absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded-full",children:["1/",e.images.length]}),r&&l.jsx("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),s?.()},className:"absolute bottom-2 right-2 bg-red-500 hover:bg-red-600 text-white p-1.5 rounded-full transition-colors",title:"从收藏中移除",children:l.jsx(i.Z,{className:"h-3 w-3"})})]}),l.jsx("div",{className:"p-3 flex-1 flex flex-col justify-center",style:{minHeight:"64px"},children:(0,l.jsxs)("div",{className:"flex items-center justify-between h-full",children:[(0,l.jsxs)("div",{className:"flex flex-col justify-center flex-1 min-w-0 pr-2",children:[l.jsx("h3",{className:"font-medium text-gray-900 text-sm leading-tight truncate mb-1",children:e.breed||"未知品种"}),(0,l.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-gray-600",children:[l.jsx(n.Z,{className:"h-3 w-3 flex-shrink-0"}),l.jsx("span",{className:"truncate",children:e.user?.nickname||e.author?.nickname||"匿名用户"})]})]}),(0,l.jsxs)("div",{className:"flex flex-col justify-center items-end flex-shrink-0",children:[e.location&&(0,l.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-gray-500 mb-1",children:[l.jsx(o.Z,{className:"h-3 w-3"}),l.jsx("span",{className:"truncate max-w-20",title:e.location,children:(0,u.lx)(e.location)})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-gray-500",children:[l.jsx(c.Z,{className:"h-3 w-3"}),l.jsx("span",{children:(0,d.uf)(e.likes_count||0)})]})]})]})})]})})}},99837:(e,t,r)=>{r.d(t,{Z:()=>o});var l=r(10326),s=r(17577),a=r.n(s),i=r(28295);let n=a().forwardRef(({className:e,variant:t="primary",size:r="md",loading:s=!1,icon:a,children:n,disabled:o,...c},d)=>(0,l.jsxs)("button",{className:(0,i.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[t],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[r],e),ref:d,disabled:o||s,...c,children:[s&&(0,l.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[l.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),l.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!s&&a&&l.jsx("span",{className:"mr-2",children:a}),n]}));n.displayName="Button";let o=n},46424:(e,t,r)=>{function l(e){if(!e||"string"!=typeof e)return"";let t=e.replace(/^#/,"").trim();if(!t)return"";try{let r="",l="",s="",a=t.match(/(.*?(?:省|自治区|特别行政区))/);a&&(r=a[1],t=t.replace(r,""));let i=t.match(/(.*?市)/);i&&(l=i[1],t=t.replace(l,""));let n=t.match(/(.*?(?:县|区|市|旗|自治县|林区|特区))/);n&&(s=n[1]);let o="",c=["北京","上海","天津","重庆"].some(e=>r&&r.includes(e)||l&&l.includes(e)),d=s&&["浦东新区","滨海新区","两江新区"].some(e=>s.includes(e));if(c?d&&l?o=l+s:s?o=s:l?o=l:r&&(o=r):l&&s?o=l+s:l?o=l:s?o=s:r&&(o=r),!o){let t=e.replace(/^#/,"").trim(),r=t.match(/(.*?(?:镇|乡|街道|办事处))/);if(r)o=r[1];else{let e=t.match(/(.*?(?:村|社区|小区|路|街|巷|弄|号))/);if(e){let t=e[1];o=t.length>8?t.substring(0,8)+"...":t}else o=t.length>10?t.substring(0,10)+"...":t}}return o}catch(e){return console.error("地址格式化出错:",e),t.length>10?t.substring(0,10)+"...":t}}function s(e){return e&&"string"==typeof e?e.replace(/^#/,"").trim():""}r.d(t,{Ed:()=>s,lx:()=>l})},42443:(e,t,r)=>{r.d(t,{YD:()=>c});var l=r(17577),s=Object.defineProperty,a=new Map,i=new WeakMap,n=0,o=void 0;function c({threshold:e,delay:t,trackVisibility:r,rootMargin:s,root:c,triggerOnce:d,skip:u,initialInView:g,fallbackInView:h,onChange:m}={}){var x;let[f,p]=l.useState(null),y=l.useRef(m),[v,b]=l.useState({inView:!!g,entry:void 0});y.current=m,l.useEffect(()=>{let l;if(!u&&f)return l=function(e,t,r={},l=o){if(void 0===window.IntersectionObserver&&void 0!==l){let s=e.getBoundingClientRect();return t(l,{isIntersecting:l,target:e,intersectionRatio:"number"==typeof r.threshold?r.threshold:0,time:0,boundingClientRect:s,intersectionRect:s,rootBounds:s}),()=>{}}let{id:s,observer:c,elements:d}=function(e){let t=Object.keys(e).sort().filter(t=>void 0!==e[t]).map(t=>{var r;return`${t}_${"root"===t?(r=e.root)?(i.has(r)||(n+=1,i.set(r,n.toString())),i.get(r)):"0":e[t]}`}).toString(),r=a.get(t);if(!r){let l;let s=new Map,i=new IntersectionObserver(t=>{t.forEach(t=>{var r;let a=t.isIntersecting&&l.some(e=>t.intersectionRatio>=e);e.trackVisibility&&void 0===t.isVisible&&(t.isVisible=a),null==(r=s.get(t.target))||r.forEach(e=>{e(a,t)})})},e);l=i.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),r={id:t,observer:i,elements:s},a.set(t,r)}return r}(r),u=d.get(e)||[];return d.has(e)||d.set(e,u),u.push(t),c.observe(e),function(){u.splice(u.indexOf(t),1),0===u.length&&(d.delete(e),c.unobserve(e)),0===d.size&&(c.disconnect(),a.delete(s))}}(f,(e,t)=>{b({inView:e,entry:t}),y.current&&y.current(e,t),t.isIntersecting&&d&&l&&(l(),l=void 0)},{root:c,rootMargin:s,threshold:e,trackVisibility:r,delay:t},h),()=>{l&&l()}},[Array.isArray(e)?e.toString():e,f,c,s,d,u,r,h,t]);let w=null==(x=v.entry)?void 0:x.target,j=l.useRef(void 0);f||!w||d||u||j.current===w||(j.current=w,b({inView:!!g,entry:void 0}));let N=[p,v.inView,v.entry];return N.ref=N[0],N.inView=N[1],N.entry=N[2],N}l.Component}};