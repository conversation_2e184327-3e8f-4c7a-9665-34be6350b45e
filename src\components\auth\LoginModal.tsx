import React, { useState } from 'react';
import { Modal, ModalBody } from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Mail, Lock, X } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { authAPI } from '@/lib/cloudbase';
import { isValidEmail } from '@/utils';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

type LoginMode = 'main' | 'email' | 'register';

const LoginModal: React.FC<LoginModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const { login, isLoading } = useAuth();
  const [mode, setMode] = useState<LoginMode>('main');
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    nickname: '',
    confirmPassword: '',
    verificationCode: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isCodeSending, setIsCodeSending] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // 重置表单
  const resetForm = () => {
    setFormData({ email: '', password: '', nickname: '', confirmPassword: '', verificationCode: '' });
    setErrors({});
    setMode('main');
    setIsCodeSending(false);
    setCountdown(0);
  };

  // 处理关闭
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // 验证表单
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (mode === 'email' || mode === 'register') {
      if (!formData.email.trim()) {
        newErrors.email = '请输入邮箱地址';
      } else if (!isValidEmail(formData.email)) {
        newErrors.email = '请输入有效的邮箱地址';
      }

      if (!formData.password.trim()) {
        newErrors.password = '请输入密码';
      } else if (formData.password.length < 6) {
        newErrors.password = '密码至少6位';
      }
    }

    if (mode === 'register') {
      if (!formData.nickname.trim()) {
        newErrors.nickname = '请输入昵称';
      } else if (formData.nickname.length < 2) {
        newErrors.nickname = '昵称至少需要2个字符';
      } else if (formData.nickname.length > 20) {
        newErrors.nickname = '昵称不能超过20个字符';
      }

      if (!formData.confirmPassword.trim()) {
        newErrors.confirmPassword = '请确认密码';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = '两次密码输入不一致';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理微信登录
  const handleWeChatLogin = async () => {
    // TODO: 实现微信登录逻辑
    console.log('微信登录');
  };

  // 发送验证码
  const handleSendCode = async () => {
    if (!formData.email.trim()) {
      setErrors({ email: '请输入邮箱地址' });
      return;
    }
    if (!isValidEmail(formData.email)) {
      setErrors({ email: '请输入有效的邮箱地址' });
      return;
    }

    try {
      setIsCodeSending(true);
      const result = await authAPI.sendVerificationCode(formData.email, 'register');

      if (result.success) {
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        setErrors({ verificationCode: result.message });
      }
    } catch (error: any) {
      setErrors({ verificationCode: error.message || '发送验证码失败' });
    } finally {
      setIsCodeSending(false);
    }
  };

  // 处理邮箱登录
  const handleEmailLogin = async () => {
    if (!validateForm()) return;

    const success = await login(formData.email, formData.password);
    if (success) {
      onSuccess?.();
      handleClose();
    }
  };

  // 处理邮箱注册
  const handleEmailRegister = async () => {
    if (!validateForm()) return;

    try {
      const result = await authAPI.registerWithEmail(
        formData.email,
        formData.password,
        formData.nickname,
        formData.verificationCode
      );

      if (result.success) {
        // 注册成功后自动登录
        const loginSuccess = await login(formData.email, formData.password);
        if (loginSuccess) {
          onSuccess?.();
          handleClose();
        }
      } else {
        setErrors({ verificationCode: result.message });
      }
    } catch (error: any) {
      setErrors({ verificationCode: error.message || '注册失败' });
    }
  };

  // 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // 渲染邮箱登录界面
  const renderEmailMode = () => (
    <div className="space-y-6">
      {/* 返回按钮和标题 */}
      <div className="text-center">
        <button
          onClick={() => setMode('main')}
          className="absolute left-4 top-4 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">邮箱登录</h2>
        <p className="text-gray-600">使用邮箱和密码登录</p>
      </div>

      {/* 登录表单 */}
      <div className="space-y-4">
        <Input
          label="邮箱地址"
          type="email"
          placeholder="请输入邮箱地址"
          value={formData.email}
          onChange={(e) => handleInputChange('email', e.target.value)}
          error={errors.email}
          leftIcon={<Mail className="h-4 w-4" />}
          disabled={isLoading}
        />

        <Input
          label="密码"
          type="password"
          placeholder="请输入密码"
          value={formData.password}
          onChange={(e) => handleInputChange('password', e.target.value)}
          error={errors.password}
          leftIcon={<Lock className="h-4 w-4" />}
          disabled={isLoading}
        />
      </div>

      {/* 登录按钮 */}
      <Button
        onClick={handleEmailLogin}
        className="w-full py-3 rounded-lg"
        disabled={isLoading}
      >
        {isLoading ? '登录中...' : '登录'}
      </Button>

      {/* 注册链接 */}
      <div className="text-center">
        <span className="text-gray-600">还没有账号？</span>
        <button
          onClick={() => setMode('register')}
          className="text-blue-600 hover:text-blue-700 ml-1"
        >
          立即注册
        </button>
      </div>
    </div>
  );

  // 渲染注册界面
  const renderRegisterMode = () => (
    <div className="space-y-6">
      {/* 返回按钮和标题 */}
      <div className="text-center">
        <button
          onClick={() => setMode('email')}
          className="absolute left-4 top-4 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">注册账号</h2>
        <p className="text-gray-600">创建您的宠物交易账号</p>
      </div>

      {/* 注册表单 */}
      <div className="space-y-4">
        <Input
          label="邮箱地址"
          type="email"
          placeholder="请输入邮箱地址"
          value={formData.email}
          onChange={(e) => handleInputChange('email', e.target.value)}
          error={errors.email}
          leftIcon={<Mail className="h-4 w-4" />}
          disabled={isLoading}
        />

        <Input
          label="昵称"
          type="text"
          placeholder="请输入昵称"
          value={formData.nickname}
          onChange={(e) => handleInputChange('nickname', e.target.value)}
          error={errors.nickname}
          disabled={isLoading}
          maxLength={20}
        />

        <Input
          label="密码"
          type="password"
          placeholder="请输入密码（至少6位）"
          value={formData.password}
          onChange={(e) => handleInputChange('password', e.target.value)}
          error={errors.password}
          leftIcon={<Lock className="h-4 w-4" />}
          disabled={isLoading}
        />

        <Input
          label="确认密码"
          type="password"
          placeholder="请再次输入密码"
          value={formData.confirmPassword}
          onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
          error={errors.confirmPassword}
          leftIcon={<Lock className="h-4 w-4" />}
          disabled={isLoading}
        />

        {/* 验证码输入 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            邮箱验证码
          </label>
          <div className="flex space-x-2">
            <Input
              type="text"
              placeholder="请输入6位验证码"
              value={formData.verificationCode}
              onChange={(e) => handleInputChange('verificationCode', e.target.value)}
              error={errors.verificationCode}
              disabled={isLoading}
              maxLength={6}
              className="flex-1"
            />
            <Button
              onClick={handleSendCode}
              disabled={isCodeSending || countdown > 0 || !formData.email || !isValidEmail(formData.email)}
              variant="outline"
              className="whitespace-nowrap"
            >
              {isCodeSending ? '发送中...' : countdown > 0 ? `${countdown}s` : '发送验证码'}
            </Button>
          </div>
        </div>
      </div>

      {/* 注册按钮 */}
      <Button
        onClick={handleEmailRegister}
        className="w-full py-3 rounded-lg"
        disabled={isLoading}
      >
        {isLoading ? '注册中...' : '注册'}
      </Button>

      {/* 登录链接 */}
      <div className="text-center">
        <span className="text-gray-600">已有账号？</span>
        <button
          onClick={() => setMode('email')}
          className="text-blue-600 hover:text-blue-700 ml-1"
        >
          立即登录
        </button>
      </div>
    </div>
  );

  // 渲染主界面
  const renderMainMode = () => (
    <div className="space-y-6">
      {/* 标题 */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">欢迎回来</h2>
        <p className="text-gray-600">选择登录方式继续使用</p>
      </div>

      {/* 微信登录按钮 */}
      <Button
        onClick={handleWeChatLogin}
        className="w-full bg-green-500 hover:bg-green-600 text-white py-3 rounded-lg flex items-center justify-center space-x-2"
        disabled={isLoading}
      >
        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
          <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.932 7.621-.55-.302-2.676-2.91-4.624-6.364-4.624zm-2.44 5.738a.868.868 0 0 1-.869-.855c0-.472.39-.856.869-.856s.868.384.868.856a.868.868 0 0 1-.868.855zm4.928 0a.868.868 0 0 1-.868-.855c0-.472.39-.856.868-.856s.869.384.869.856a.868.868 0 0 1-.869.855z"/>
          <path d="M24 14.388c0-3.14-2.956-5.69-6.594-5.69-3.638 0-6.594 2.55-6.594 5.69 0 3.14 2.956 5.69 6.594 5.69a7.842 7.842 0 0 0 2.208-.32.671.671 0 0 1 .556.075l1.462.855a.25.25 0 0 0 .128.042.226.226 0 0 0 .223-.227.166.166 0 0 0-.037-.164l-.3-1.14a.454.454 0 0 1 .164-.512C22.84 17.64 24 16.125 24 14.388zm-8.738-1.14a.67.67 0 0 1-.669-.66c0-.365.3-.66.67-.66a.67.67 0 0 1 .668.66c0 .365-.3.66-.669.66zm3.348 0a.67.67 0 0 1-.668-.66c0-.365.3-.66.668-.66a.67.67 0 0 1 .669.66c0 .365-.3.66-.669.66z"/>
        </svg>
        <span>微信登录</span>
      </Button>

      {/* 分割线 */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-300" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-white text-gray-500">或</span>
        </div>
      </div>

      {/* 邮箱登录按钮 */}
      <Button
        onClick={() => setMode('email')}
        variant="outline"
        className="w-full py-3 rounded-lg flex items-center justify-center space-x-2"
      >
        <Mail className="w-5 h-5" />
        <span>邮箱登录</span>
      </Button>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      size="sm"
    >
      <ModalBody>
        {/* 关闭按钮 */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X className="w-5 h-5" />
        </button>

        {mode === 'main' && renderMainMode()}
        {mode === 'email' && renderEmailMode()}
        {mode === 'register' && renderRegisterMode()}
      </ModalBody>
    </Modal>
  );
};

export default LoginModal;
