(()=>{var e={};e.id=193,e.ids=[193],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},65245:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>o}),s(93468),s(16953),s(35866);var a=s(23191),r=s(88716),l=s(37922),i=s.n(l),n=s(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let o=["",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,93468)),"D:\\web-cloudbase-project\\src\\app\\notifications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\web-cloudbase-project\\src\\app\\notifications\\page.tsx"],x="/notifications/page",m={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/notifications/page",pathname:"/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},81825:(e,t,s)=>{Promise.resolve().then(s.bind(s,41268))},37202:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},86333:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},39730:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},40617:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},41268:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var a=s(10326),r=s(17577),l=s(74131),i=s(41828),n=s(20603),c=s(39730),o=s(37202),d=s(54659),x=s(86333),m=s(76557);let u=(0,m.Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),h=(0,m.Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]);var p=s(90434),y=s(35047),g=s(99837),b=s(28676),f=s(40617);let j=({className:e="",disabled:t=!1})=>{let[s,l]=(0,r.useState)(!1),[c,d]=(0,r.useState)(""),[x,m]=(0,r.useState)(!1),u=async()=>{if(!c.trim()){n.C.error("请填写申诉理由");return}try{m(!0);let e=await i.petAPI.submitAppeal({reason:c.trim()});e.success?(n.C.success("申诉已提交，请等待管理员处理"),l(!1),d("")):n.C.error(e.message||"申诉提交失败")}catch(e){console.error("申诉提交失败:",e),n.C.error(e.message||"申诉提交失败，请重试")}finally{m(!1)}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(g.Z,{variant:"outline",size:"sm",onClick:()=>l(!0),disabled:t,className:`flex items-center space-x-1 ${e}`,children:[a.jsx(f.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"申诉"})]}),a.jsx(b.u_,{isOpen:s,onClose:()=>l(!1),title:"提交申诉",size:"md",children:a.jsx(b.fe,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx(o.Z,{className:"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-yellow-800",children:[a.jsx("p",{className:"font-medium mb-1",children:"申诉说明"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[a.jsx("li",{children:"请详细说明您认为处罚不当的理由"}),a.jsx("li",{children:"提供相关证据或说明情况"}),a.jsx("li",{children:"申诉将由管理员审核，请耐心等待"}),a.jsx("li",{children:"恶意申诉可能影响您的账户信誉"})]})]})]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["申诉理由 ",a.jsx("span",{className:"text-red-500",children:"*"})]}),a.jsx("textarea",{value:c,onChange:e=>d(e.target.value),placeholder:"请详细说明您认为处罚不当的理由...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none",rows:6,maxLength:500}),(0,a.jsxs)("div",{className:"text-right text-xs text-gray-500 mt-1",children:[c.length,"/500"]})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx(g.Z,{variant:"outline",onClick:()=>l(!1),className:"flex-1",disabled:x,children:"取消"}),a.jsx(g.Z,{variant:"primary",onClick:u,loading:x,className:"flex-1",disabled:!c.trim()||x,children:"提交申诉"})]})]})})})]})},v=()=>{let{isLoggedIn:e,user:t}=(0,l.a)(),s=(0,y.useRouter)(),[m,b]=(0,r.useState)([]),[f,v]=(0,r.useState)(!0),[N,w]=(0,r.useState)("contact"),k=async()=>{if(e)try{v(!0);let e=await i.petAPI.getUserNotifications({limit:50,type:void 0});if(e.success){let t=e.data||[];"contact"===N?t=t.filter(e=>"contact"===e.type&&(e.data?.post_type==="selling"||e.data?.post_type==="wanted")):"breeding"===N?t=t.filter(e=>"contact"===e.type&&e.data?.post_type==="breeding"):"lost"===N&&(t=t.filter(e=>"contact"===e.type&&e.data?.post_type==="lost")),b(t)}else n.C.error(e.message||"加载通知失败")}catch(e){console.error("加载通知失败:",e),n.C.error("加载通知失败")}finally{v(!1)}},_=async e=>{try{await navigator.clipboard.writeText(e),n.C.success("已复制到剪贴板")}catch(s){let t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),n.C.success("已复制到剪贴板")}},C=async e=>{try{await i.petAPI.markNotificationRead({notificationId:e}),b(t=>t.map(t=>t._id===e?{...t,is_read:!0}:t))}catch(e){console.error("标记已读失败:",e)}};(0,r.useEffect)(()=>{k()},[e,N]);let Z=e=>"contact"===e.type?a.jsx(c.Z,{className:"w-5 h-5 text-blue-500"}):"system"===e.type?e.data?.report_type?a.jsx(o.Z,{className:"w-5 h-5 text-red-500"}):a.jsx(d.Z,{className:"w-5 h-5 text-green-500"}):a.jsx(c.Z,{className:"w-5 h-5 text-gray-500"}),P=e=>{let t=new Date(e),s=new Date().getTime()-t.getTime(),a=Math.floor(s/6e4),r=Math.floor(s/36e5),l=Math.floor(s/864e5);return a<60?`${a}分钟前`:r<24?`${r}小时前`:l<7?`${l}天前`:t.toLocaleDateString("zh-CN")};return e?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("div",{className:"bg-white border-b border-gray-200 sticky top-0 z-10",children:a.jsx("div",{className:"max-w-md mx-auto px-4 py-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("button",{onClick:()=>s.back(),className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:a.jsx(x.Z,{className:"w-5 h-5"})}),a.jsx("h1",{className:"text-lg font-semibold",children:"消息通知"}),a.jsx("div",{className:"w-9"})]})})}),a.jsx("div",{className:"bg-white border-b border-gray-200",children:a.jsx("div",{className:"max-w-md mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex",children:[a.jsx("button",{onClick:()=>w("contact"),className:`flex-1 py-3 text-center border-b-2 transition-colors text-xs ${"contact"===N?"border-blue-500 text-blue-600":"border-transparent text-gray-500"}`,children:"买卖通知"}),a.jsx("button",{onClick:()=>w("breeding"),className:`flex-1 py-3 text-center border-b-2 transition-colors text-xs ${"breeding"===N?"border-blue-500 text-blue-600":"border-transparent text-gray-500"}`,children:"配种通知"}),a.jsx("button",{onClick:()=>w("lost"),className:`flex-1 py-3 text-center border-b-2 transition-colors text-xs ${"lost"===N?"border-blue-500 text-blue-600":"border-transparent text-gray-500"}`,children:"寻宠通知"}),a.jsx("button",{onClick:()=>w("all"),className:`flex-1 py-3 text-center border-b-2 transition-colors text-xs ${"all"===N?"border-blue-500 text-blue-600":"border-transparent text-gray-500"}`,children:"全部通知"})]})})}),a.jsx("div",{className:"max-w-md mx-auto",children:f?a.jsx("div",{className:"text-center py-12",children:a.jsx("div",{className:"text-gray-400",children:"加载中..."})}):0===m.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"text-gray-400 mb-4",children:a.jsx(c.Z,{className:"w-16 h-16 mx-auto"})}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无通知"}),a.jsx("p",{className:"text-gray-600",children:"有新消息时会显示在这里"})]}):a.jsx("div",{className:"divide-y divide-gray-200",children:m.map(e=>a.jsx("div",{className:`p-4 bg-white hover:bg-gray-50 transition-colors ${e.is_read?"":"bg-blue-50"}`,onClick:()=>!e.is_read&&C(e._id),children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("div",{className:"flex-shrink-0 mt-1",children:Z(e)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:["contact"===e.type&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:[e.data?.action_type==="received"?(0,a.jsxs)("span",{children:["用户",a.jsx("span",{className:"font-medium text-blue-600",children:e.data.sender_nickname}),"想与您取得联系：",a.jsx("span",{className:"font-medium text-green-600 ml-1",children:e.data.contact_info})]}):(0,a.jsxs)("span",{children:["已与",a.jsx("span",{className:"font-medium text-blue-600",children:e.data.sender_nickname}),"取得联系：",a.jsx("span",{className:"font-medium text-green-600 ml-1",children:e.data.contact_info})]}),(0,a.jsxs)("button",{onClick:t=>{t.stopPropagation(),_(e.data.contact_info)},className:"ml-2 inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors",children:[a.jsx(u,{className:"w-3 h-3 mr-1"}),"复制"]})]}),e.data?.post_title&&(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 rounded-lg p-3",children:[a.jsx("span",{className:"text-sm text-gray-700 font-medium",children:e.data.post_title}),e.post_id&&a.jsx(p.default,{href:`/post/detail?id=${e.post_id}`,children:(0,a.jsxs)("button",{className:"flex items-center space-x-1 text-blue-600 hover:text-blue-700 text-sm font-medium",children:[a.jsx(h,{className:"w-4 h-4"}),a.jsx("span",{children:"查看帖子"})]})})]})]}),"system"===e.type&&(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx("div",{className:"text-sm text-gray-900",children:e.message}),e.data?.can_appeal&&a.jsx("div",{className:"flex justify-end",children:a.jsx(j,{})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-3",children:[a.jsx("p",{className:"text-xs text-gray-400",children:P(e.created_at)}),!e.is_read&&a.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})]})]})]})},e._id))})})]}):a.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"请先登录"}),a.jsx(p.default,{href:"/login",children:a.jsx(g.Z,{children:"去登录"})})]})})}},99837:(e,t,s)=>{"use strict";s.d(t,{Z:()=>c});var a=s(10326),r=s(17577),l=s.n(r),i=s(28295);let n=l().forwardRef(({className:e,variant:t="primary",size:s="md",loading:r=!1,icon:l,children:n,disabled:c,...o},d)=>(0,a.jsxs)("button",{className:(0,i.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[t],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[s],e),ref:d,disabled:c||r,...o,children:[r&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[a.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),a.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!r&&l&&a.jsx("span",{className:"mr-2",children:l}),n]}));n.displayName="Button";let c=n},28676:(e,t,s)=>{"use strict";s.d(t,{fe:()=>d,u_:()=>o});var a=s(10326),r=s(17577),l=s(60962),i=s(94019),n=s(28295),c=s(99837);let o=({isOpen:e,onClose:t,title:s,children:o,size:d="md",showCloseButton:x=!0,closeOnOverlayClick:m=!0,className:u})=>{if((0,r.useEffect)(()=>{let s=e=>{"Escape"===e.key&&t()};return e&&(document.addEventListener("keydown",s),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",s),document.body.style.overflow="unset"}},[e,t]),!e)return null;let h=(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:m?t:void 0}),(0,a.jsxs)("div",{className:(0,n.cn)("relative bg-white rounded-lg shadow-xl w-full mx-4 max-h-[90vh] overflow-hidden",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[d],u),onClick:e=>e.stopPropagation(),children:[(s||x)&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[s&&a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:s}),x&&a.jsx(c.Z,{variant:"ghost",size:"sm",onClick:t,className:"p-1 hover:bg-gray-100 rounded-full",children:a.jsx(i.Z,{className:"h-5 w-5"})})]}),a.jsx("div",{className:"overflow-y-auto max-h-[calc(90vh-80px)]",children:o})]})]});return(0,l.createPortal)(h,document.body)},d=({children:e,className:t})=>a.jsx("div",{className:(0,n.cn)("p-4",t),children:e})},93468:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\notifications\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[276,201,240],()=>s(65245));module.exports=a})();