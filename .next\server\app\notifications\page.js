(()=>{var e={};e.id=193,e.ids=[193],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},95051:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>o}),a(93468),a(16953),a(35866);var s=a(23191),r=a(88716),c=a(37922),i=a.n(c),n=a(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);a.d(t,l);let o=["",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,93468)),"D:\\web-cloudbase-project\\src\\app\\notifications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\web-cloudbase-project\\src\\app\\notifications\\page.tsx"],x="/notifications/page",u={require:a,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/notifications/page",pathname:"/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},81825:(e,t,a)=>{Promise.resolve().then(a.bind(a,16175))},86333:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},67427:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},39730:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},33734:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},98091:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},16175:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var s=a(10326),r=a(17577),c=a(74131),i=a(41828),n=a(20603),l=a(67427),o=a(39730),d=a(33734),x=a(79635),u=a(86333);let p=(0,a(76557).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var m=a(98091),h=a(90434),y=a(35047),g=a(99837);let b=()=>{let{isLoggedIn:e,user:t}=(0,c.a)(),a=(0,y.useRouter)(),[b,f]=(0,r.useState)([]),[j,v]=(0,r.useState)(!0),[N,w]=(0,r.useState)("contact"),_=async()=>{if(e)try{v(!0);let e=await i.petAPI.getUserNotifications({limit:50,type:"all"===N?void 0:"contact"});if(e.success){let t=e.data||[];"breeding"===N?t=t.filter(e=>"contact"===e.type&&e.data?.post_type==="breeding"):"lost"===N?t=t.filter(e=>"contact"===e.type&&e.data?.post_type==="lost"):"contact"===N&&(t=t.filter(e=>"contact"===e.type&&(e.data?.post_type==="selling"||e.data?.post_type==="wanted"||!e.data?.post_type))),f(t)}}catch(e){console.error("加载通知失败:",e),n.C.error("加载通知失败")}finally{v(!1)}};(0,r.useEffect)(()=>{_()},[e,N]);let k=async e=>{try{await i.petAPI.markNotificationRead({notificationId:e}),f(t=>t.map(t=>t._id===e?{...t,is_read:!0}:t))}catch(e){console.error("标记已读失败:",e)}},C=async e=>{try{await i.petAPI.deleteNotification({notificationId:e}),f(t=>t.filter(t=>t._id!==e)),n.C.success("删除成功")}catch(e){console.error("删除通知失败:",e),n.C.error("删除失败")}},D=e=>{switch(e){case"like":return s.jsx(l.Z,{className:"w-5 h-5 text-red-500"});case"contact":return s.jsx(o.Z,{className:"w-5 h-5 text-blue-500"});case"rating":return s.jsx(d.Z,{className:"w-5 h-5 text-yellow-500"});case"follow":return s.jsx(x.Z,{className:"w-5 h-5 text-green-500"});default:return s.jsx(o.Z,{className:"w-5 h-5 text-gray-500"})}},Z=e=>{let t=new Date(e),a=new Date().getTime()-t.getTime(),s=Math.floor(a/6e4),r=Math.floor(a/36e5),c=Math.floor(a/864e5);return s<60?`${s}分钟前`:r<24?`${r}小时前`:c<7?`${c}天前`:t.toLocaleDateString("zh-CN")};return e?(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[s.jsx("div",{className:"bg-white border-b border-gray-200 sticky top-0 z-10",children:s.jsx("div",{className:"max-w-md mx-auto px-4 py-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("button",{onClick:()=>a.back(),className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:s.jsx(u.Z,{className:"w-5 h-5"})}),s.jsx("h1",{className:"text-lg font-semibold",children:"消息通知"}),s.jsx("div",{className:"w-9"})]})})}),s.jsx("div",{className:"bg-white border-b border-gray-200",children:s.jsx("div",{className:"max-w-md mx-auto px-4",children:(0,s.jsxs)("div",{className:"flex",children:[s.jsx("button",{onClick:()=>w("contact"),className:`flex-1 py-3 text-center border-b-2 transition-colors text-xs ${"contact"===N?"border-blue-500 text-blue-600":"border-transparent text-gray-500"}`,children:"买卖通知"}),s.jsx("button",{onClick:()=>w("breeding"),className:`flex-1 py-3 text-center border-b-2 transition-colors text-xs ${"breeding"===N?"border-blue-500 text-blue-600":"border-transparent text-gray-500"}`,children:"配种通知"}),s.jsx("button",{onClick:()=>w("lost"),className:`flex-1 py-3 text-center border-b-2 transition-colors text-xs ${"lost"===N?"border-blue-500 text-blue-600":"border-transparent text-gray-500"}`,children:"寻宠通知"}),s.jsx("button",{onClick:()=>w("all"),className:`flex-1 py-3 text-center border-b-2 transition-colors text-xs ${"all"===N?"border-blue-500 text-blue-600":"border-transparent text-gray-500"}`,children:"全部通知"})]})})}),s.jsx("div",{className:"max-w-md mx-auto",children:j?s.jsx("div",{className:"text-center py-12",children:s.jsx("div",{className:"text-gray-400",children:"加载中..."})}):0===b.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[s.jsx("div",{className:"text-gray-400 mb-4",children:s.jsx(o.Z,{className:"w-16 h-16 mx-auto"})}),s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无通知"}),s.jsx("p",{className:"text-gray-600",children:"有新消息时会显示在这里"})]}):s.jsx("div",{className:"divide-y divide-gray-200",children:b.map(e=>s.jsx("div",{className:`p-4 bg-white hover:bg-gray-50 transition-colors ${e.is_read?"":"bg-blue-50"}`,children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:"flex-shrink-0 mt-1",children:D(e.type)}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"text-sm text-gray-900 mb-1",children:e.message}),e.data?.post_title&&(0,s.jsxs)("p",{className:"text-xs text-gray-500 mb-2",children:["帖子：",e.data.post_title]}),"contact"===e.type&&(0,s.jsxs)("div",{className:"text-xs bg-blue-50 rounded p-2 mb-2",children:[e.data?.sender_contact&&(0,s.jsxs)("div",{className:"text-blue-700 mb-1",children:[s.jsx("span",{className:"font-medium",children:"对方联系方式："}),"phone"===e.data.sender_contact.type&&(0,s.jsxs)("span",{children:["\uD83D\uDCF1 ",e.data.sender_contact.value]}),"wechat"===e.data.sender_contact.type&&(0,s.jsxs)("span",{children:["\uD83D\uDCAC ",e.data.sender_contact.value]}),"qq"===e.data.sender_contact.type&&(0,s.jsxs)("span",{children:["\uD83D\uDC27 ",e.data.sender_contact.value]})]}),e.data?.author_contact&&(0,s.jsxs)("div",{className:"text-blue-700",children:[s.jsx("span",{className:"font-medium",children:"对方联系方式："}),"phone"===e.data.author_contact.type&&(0,s.jsxs)("span",{children:["\uD83D\uDCF1 ",e.data.author_contact.value]}),"wechat"===e.data.author_contact.type&&(0,s.jsxs)("span",{children:["\uD83D\uDCAC ",e.data.author_contact.value]}),"qq"===e.data.author_contact.type&&(0,s.jsxs)("span",{children:["\uD83D\uDC27 ",e.data.author_contact.value]})]})]}),s.jsx("p",{className:"text-xs text-gray-400",children:Z(e.created_at)})]}),(0,s.jsxs)("div",{className:"flex-shrink-0 flex items-center space-x-2",children:[!e.is_read&&s.jsx("button",{onClick:()=>k(e._id),className:"p-1 text-blue-500 hover:text-blue-600",title:"标记为已读",children:s.jsx(p,{className:"w-4 h-4"})}),s.jsx("button",{onClick:()=>C(e._id),className:"p-1 text-red-500 hover:text-red-600",title:"删除",children:s.jsx(m.Z,{className:"w-4 h-4"})})]})]})},e._id))})})]}):s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"请先登录"}),s.jsx(h.default,{href:"/login",children:s.jsx(g.Z,{children:"去登录"})})]})})}},99837:(e,t,a)=>{"use strict";a.d(t,{Z:()=>l});var s=a(10326),r=a(17577),c=a.n(r),i=a(28295);let n=c().forwardRef(({className:e,variant:t="primary",size:a="md",loading:r=!1,icon:c,children:n,disabled:l,...o},d)=>(0,s.jsxs)("button",{className:(0,i.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[t],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[a],e),ref:d,disabled:l||r,...o,children:[r&&(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!r&&c&&s.jsx("span",{className:"mr-2",children:c}),n]}));n.displayName="Button";let l=n},93468:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\notifications\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[276,201,240],()=>a(95051));module.exports=s})();