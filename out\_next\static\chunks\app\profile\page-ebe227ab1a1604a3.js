(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[178],{49289:function(e,t,s){Promise.resolve().then(s.bind(s,38732))},38732:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return W}});var a=s(57437),r=s(99376),l=s(2265),n=s(98734),i=s(32489),c=s(94630),o=s(32660),d=s(92369),m=s(53581),x=s(83774),u=s(31047),h=s(34423),g=s(98728),f=s(82718),p=s(88997),y=s(10407),j=s(41473),b=s(91723),v=s(86595),w=s(42208),N=s(56334),_=s(9356),C=s(98011);let k=e=>e.startsWith("cloud://")||!e.startsWith("http")&&!e.startsWith("/");var S=e=>{let{fileId:t,alt:s="",className:r="",width:n,height:i,style:c,onClick:o}=e,[d,m]=(0,l.useState)(""),[x,u]=(0,l.useState)(!0),[h,g]=(0,l.useState)(!1),[f,p]=(0,l.useState)(0);return((0,l.useEffect)(()=>{(async()=>{if(!t){g(!0),u(!1);return}if(t.startsWith("http")){m(t),u(!1);return}try{u(!0),g(!1),p(0);let e=await (0,C.getImageUrl)(t);m(e)}catch(e){console.error("加载图片失败:",e),g(!0)}finally{u(!1)}})()},[t]),x)?(0,a.jsx)("div",{className:"bg-gray-200 animate-pulse flex items-center justify-center ".concat(r),style:{width:n,height:i,...c},children:(0,a.jsx)("div",{className:"text-gray-400 text-sm",children:"加载中..."})}):h||!d?(0,a.jsx)("div",{className:"bg-gray-200 flex items-center justify-center ".concat(r),style:{width:n,height:i,...c},children:(0,a.jsx)("div",{className:"text-gray-400 text-sm",children:"图片加载失败"})}):(0,a.jsx)("img",{src:d,alt:s,className:r,width:n,height:i,style:c,onClick:o,onError:async()=>{if(console.log("图片加载失败，尝试刷新URL，重试次数:",f),f<2){if(p(e=>e+1),k(t))try{var e;let s=await C.petAPI.getImage({fileId:t});s.success&&(null===(e=s.data)||void 0===e?void 0:e.url)?(console.log("获取新的图片URL:",s.data.url),m(s.data.url)):(console.error("刷新图片URL失败:",s.message),g(!0))}catch(e){console.error("刷新图片URL异常:",e),g(!0)}else console.log("普通URL加载失败，无法刷新"),g(!0)}else g(!0)}})},I=s(27648),Z=s(95578);function O(e){let{isOpen:t,onClose:s,currentBio:r,onSave:n}=e,[c,o]=(0,l.useState)(""),[d,m]=(0,l.useState)(!1);(0,l.useEffect)(()=>{t&&o(r||"")},[t,r]);let x=async()=>{if(c.length>200){_.C.error("个人简介不能超过200字");return}m(!0);try{await n(c),_.C.success("个人简介更新成功"),s()}catch(e){_.C.error("更新失败，请重试")}finally{m(!1)}};return t?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"编辑个人简介"}),(0,a.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(i.Z,{className:"w-6 h-6"})})]}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"个人简介"}),(0,a.jsx)("textarea",{value:c,onChange:e=>o(e.target.value),placeholder:"介绍一下自己吧...",rows:4,maxLength:200,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"}),(0,a.jsx)("div",{className:"flex justify-between items-center mt-2",children:(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[c.length,"/200"]})})]})}),(0,a.jsxs)("div",{className:"flex space-x-3 p-6 border-t",children:[(0,a.jsx)(N.Z,{variant:"outline",onClick:s,className:"flex-1",children:"取消"}),(0,a.jsx)(N.Z,{onClick:x,loading:d,className:"flex-1",children:"保存"})]})]})}):null}var D=s(88906),E=s(43745);function P(e){let{isOpen:t,onClose:s,userId:r}=e,[n,c]=(0,l.useState)("following"),[o,m]=(0,l.useState)([]),[x,u]=(0,l.useState)([]),[h,g]=(0,l.useState)(!1);(0,l.useEffect)(()=>{t&&("following"===n?f():b())},[t,r,n]);let f=async()=>{g(!0);try{let e=await C.petAPI.getUserFollowing({targetUserId:r,limit:100});if(e.success){let t=e.data.map(e=>({_id:e.following_id,nickname:e.following_nickname||"用户已删除",avatar_url:e.following_avatar_url||"",bio:e.following_bio||"",isFollowing:!0}));m(t)}else _.C.error(e.message||"加载关注列表失败")}catch(e){_.C.error("加载关注列表失败")}finally{g(!1)}},y=async e=>{try{let t=await C.petAPI.toggleFollow({targetUserId:e});t.success?(m(t=>t.map(t=>t._id===e?{...t,isFollowing:!1}:t)),_.C.success("已取消关注")):_.C.error(t.message||"取消关注失败")}catch(e){_.C.error("取消关注失败")}},j=async e=>{try{let t=await C.petAPI.toggleFollow({targetUserId:e});t.success?(m(t=>t.map(t=>t._id===e?{...t,isFollowing:!0}:t)),_.C.success("关注成功")):_.C.error(t.message||"关注失败")}catch(e){_.C.error("关注失败")}},b=async()=>{g(!0);try{let e=await C.petAPI.getBlockedUsers();e.success?u(e.data||[]):_.C.error(e.message||"加载黑名单失败")}catch(e){_.C.error("加载黑名单失败")}finally{g(!1)}},v=async e=>{try{let t=await C.petAPI.unblockUser({targetUserId:e});t.success?(u(t=>t.filter(t=>t._id!==e)),_.C.success("已取消拉黑")):_.C.error(t.message||"取消拉黑失败")}catch(e){_.C.error("取消拉黑失败")}};return t?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",onClick:e=>{e.target===e.currentTarget&&s()},children:(0,a.jsxs)("div",{className:"bg-white rounded-lg w-[480px] h-[500px] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b flex-shrink-0",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"following"===n?"我的关注":"黑名单"}),(0,a.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(i.Z,{className:"w-6 h-6"})})]}),(0,a.jsx)("div",{className:"border-b border-gray-200 flex-shrink-0",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsxs)("button",{onClick:()=>c("following"),className:"flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors ".concat("following"===n?"text-blue-600 border-b-2 border-blue-600 bg-blue-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[(0,a.jsx)(p.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"我的关注"})]}),(0,a.jsxs)("button",{onClick:()=>c("blocked"),className:"flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors ".concat("blocked"===n?"text-blue-600 border-b-2 border-blue-600 bg-blue-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[(0,a.jsx)(D.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"黑名单"})]})]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto",children:h?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):"following"===n?0===o.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(p.Z,{className:"w-16 h-16 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有关注任何人"}),(0,a.jsx)("p",{className:"text-gray-600",children:"去发现更多有趣的用户吧！"})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:o.map(e=>(0,a.jsx)("div",{className:"p-4 hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-200 overflow-hidden flex-shrink-0",children:e.avatar_url?(0,a.jsx)("img",{src:e.avatar_url,alt:e.nickname,className:"w-full h-full object-cover"}):(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,a.jsx)(d.Z,{className:"w-6 h-6 text-gray-400"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900 truncate",children:e.nickname}),e.bio&&(0,a.jsx)("p",{className:"text-sm text-gray-500 truncate",children:e.bio})]}),(0,a.jsx)("div",{className:"flex-shrink-0",children:e.isFollowing?(0,a.jsx)(N.Z,{variant:"outline",size:"sm",onClick:()=>y(e._id),children:"取消关注"}):(0,a.jsx)(N.Z,{size:"sm",onClick:()=>j(e._id),children:"关注"})})]})},e._id))}):0===x.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(D.Z,{className:"w-16 h-16 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"黑名单为空"}),(0,a.jsx)("p",{className:"text-gray-600",children:"您还没有拉黑任何用户"})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:x.map(e=>(0,a.jsx)("div",{className:"p-4 hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-200 overflow-hidden flex-shrink-0",children:e.avatar_url?(0,a.jsx)("img",{src:e.avatar_url,alt:e.nickname,className:"w-full h-full object-cover"}):(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,a.jsx)(E.Z,{className:"w-6 h-6 text-gray-400"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900 truncate",children:e.nickname}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 truncate",children:["拉黑时间：",new Date(e.blockedAt).toLocaleDateString()]})]}),(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(N.Z,{variant:"outline",size:"sm",onClick:()=>v(e._id),className:"text-red-600 border-red-200 hover:bg-red-50",children:"取消拉黑"})})]})},e._id))})})]})}):null}var L=s(83565),A=s(59604),U=s(89841),R=s(18930),F=s(63639),z=e=>{let{isOpen:t,onClose:s,onConfirm:r,title:n,message:c,confirmText:o="确定",cancelText:d="取消",type:m="danger",loading:x=!1}=e;return((0,l.useEffect)(()=>{let e=e=>{t&&("Escape"===e.key?s():"Enter"!==e.key||x||r())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[t,s,r,x]),(0,l.useEffect)(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]),t)?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",role:"dialog","aria-modal":"true","aria-labelledby":"confirm-modal-title","aria-describedby":"confirm-modal-description",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 transition-opacity",onClick:s,"aria-hidden":"true"}),(0,a.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all",children:[(0,a.jsx)("button",{onClick:s,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors","aria-label":"关闭对话框",children:(0,a.jsx)(i.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(()=>{switch(m){case"danger":return(0,a.jsx)(R.Z,{className:"w-6 h-6 text-red-600"});case"warning":return(0,a.jsx)(F.Z,{className:"w-6 h-6 text-yellow-600"});default:return(0,a.jsx)(F.Z,{className:"w-6 h-6 text-blue-600"})}})(),(0,a.jsx)("h3",{id:"confirm-modal-title",className:"text-lg font-medium text-gray-900",children:n||"确认操作"})]}),(0,a.jsx)("p",{id:"confirm-modal-description",className:"text-gray-600 mb-6 leading-relaxed",children:c}),(0,a.jsxs)("div",{className:"flex space-x-3 justify-end",children:[(0,a.jsx)(N.Z,{variant:"outline",onClick:s,disabled:x,className:"px-4 py-2",children:d}),(0,a.jsx)(N.Z,{variant:(()=>{switch(m){case"danger":return"danger";case"warning":return"warning";default:return"primary"}})(),onClick:r,loading:x,disabled:x,className:"px-4 py-2",children:o})]})]})]})]}):null},T=e=>{let{isOpen:t,onClose:s,items:r,position:n,className:i=""}=e,c=(0,l.useRef)(null);if((0,l.useEffect)(()=>{let e=e=>{c.current&&!c.current.contains(e.target)&&s()};return t&&(document.addEventListener("mousedown",e),document.addEventListener("touchstart",e)),()=>{document.removeEventListener("mousedown",e),document.removeEventListener("touchstart",e)}},[t,s]),(0,l.useEffect)(()=>{let e=e=>{t&&"Escape"===e.key&&s()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[t,s]),(0,l.useEffect)(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]),!t)return null;let o=e=>{e.onClick(),s()};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-30",onClick:s,"aria-hidden":"true"}),(0,a.jsx)("div",{ref:c,className:"\n          fixed z-50 bg-white rounded-xl shadow-2xl border border-gray-200\n          min-w-48 py-3 mx-4 transform transition-all duration-200 ease-out\n          ".concat(n?"":"bottom-6 left-0 right-0 md:bottom-auto md:left-auto md:right-auto md:mx-0","\n          ").concat(t?"scale-100 opacity-100":"scale-95 opacity-0","\n          ").concat(i,"\n        "),style:n?{left:n.x,top:n.y,transform:"translate(-50%, -100%)"}:void 0,role:"menu","aria-orientation":"vertical",children:r.map((e,t)=>(0,a.jsxs)("button",{onClick:()=>o(e),className:"\n              w-full px-5 py-4 text-left flex items-center space-x-3\n              transition-all duration-150 font-medium text-base\n              ".concat("danger"===e.variant?"text-red-600 hover:bg-red-50 active:bg-red-100 hover:scale-[1.02]":"text-gray-700 hover:bg-gray-50 active:bg-gray-100 hover:scale-[1.02]","\n              ").concat(0===t?"rounded-t-xl":"","\n              ").concat(t===r.length-1?"rounded-b-xl":"","\n              ").concat(t>0?"border-t border-gray-100":"","\n            "),role:"menuitem",tabIndex:0,children:[e.icon&&(0,a.jsx)("span",{className:"flex-shrink-0 w-5 h-5 flex items-center justify-center",children:e.icon}),(0,a.jsx)("span",{className:"flex-1",children:e.label})]},e.id))})]})};let J=e=>{let{onLongPress:t,onPress:s,delay:a=500,shouldPreventDefault:r=!0}=e,[n,i]=(0,l.useState)(!1),c=(0,l.useRef)(),o=(0,l.useRef)(),d=(0,l.useCallback)(e=>{r&&e.target&&(e.target.addEventListener("touchend",x,{passive:!1}),o.current=e.target),i(!1),c.current=setTimeout(()=>{i(!0),navigator.vibrate&&navigator.vibrate([50,30,50]),t()},a)},[t,a,r]),m=(0,l.useCallback)(function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];c.current&&clearTimeout(c.current),t&&!n&&s&&s(),i(!1),r&&o.current&&o.current.removeEventListener("touchend",x)},[r,n,s]),x=e=>{e.touches&&e.touches.length<2&&e.preventDefault&&e.preventDefault()};return{onMouseDown:e=>d(e),onMouseUp:e=>m(e),onMouseLeave:e=>m(e,!1),onTouchStart:e=>d(e),onTouchEnd:e=>m(e),onTouchMove:e=>{e.touches[0]&&m(e,!1)},isLongPressing:n}};var M=e=>{let{post:t,onLongPress:s,onDelete:r,onEdit:l,type:n,isDraft:c=!1,onClick:o}=e,d=J({onLongPress:s,onPress:o,delay:500});return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{...d,className:"relative transition-all duration-200 ".concat(d.isLongPressing?"scale-95 opacity-80 ring-2 ring-blue-500 ring-opacity-50":""," ").concat(o?"cursor-pointer":""),children:[(0,a.jsx)(U.Z,{post:t,isDraft:c}),d.isLongPressing&&(0,a.jsx)("div",{className:"absolute inset-0 bg-blue-500 bg-opacity-10 rounded-lg flex items-center justify-center md:hidden",children:(0,a.jsx)("div",{className:"bg-white bg-opacity-90 px-3 py-1 rounded-full text-sm font-medium text-blue-600",children:"松开显示选项"})})]}),(0,a.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),r()},className:"absolute top-2 right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-colors shadow-lg z-10 md:flex hidden",title:(()=>{switch(n){case"published":return"下架宝贝并移至待发布";case"draft":return"删除草稿";case"history":return"从浏览历史中移除";default:return"删除"}})(),children:(0,a.jsx)(i.Z,{className:"w-3 h-3"})})]})};function W(){let e=(0,r.useRouter)(),t=(0,r.useSearchParams)().get("id"),{user:k,isLoggedIn:D,isLoading:E}=(0,n.a)(),[R,F]=(0,l.useState)(null),[J,W]=(0,l.useState)(!0),[B,X]=(0,l.useState)(!1),[K,H]=(0,l.useState)("posts"),[q,Y]=(0,l.useState)(!1),[G,Q]=(0,l.useState)(!1),[V,$]=(0,l.useState)(!1),[ee,et]=(0,l.useState)([]),[es,ea]=(0,l.useState)([]),[er,el]=(0,l.useState)([]),[en,ei]=(0,l.useState)(!1),[ec,eo]=(0,l.useState)(!1),[ed,em]=(0,l.useState)(!1),[ex,eu]=(0,l.useState)(""),[eh,eg]=(0,l.useState)([]),[ef,ep]=(0,l.useState)({isOpen:!1,message:"",onConfirm:()=>{}}),[ey,ej]=(0,l.useState)({isOpen:!1,postId:"",postType:"published"});(0,l.useEffect)(()=>{"drafts"===K&&et((0,L.Oe)())},[K]),(0,l.useEffect)(()=>{"history"===K&&B&&eg(A.KX.getHistory())},[K,B]);let eb=t=>{try{if(!t){_.C.error("草稿ID无效");return}console.log("准备跳转到发布页面，草稿ID:",t),e.push("/upload?draftId=".concat(encodeURIComponent(t)))}catch(e){console.error("跳转到发布页面失败:",e),_.C.error("跳转失败，请重试")}},ev=async()=>{try{ei(!0);try{let e=await C.petAPI.getUserRatedPosts({limit:20});if(e&&e.success&&e.data){ea(e.data);return}}catch(e){console.warn("getUserRatedPosts API不可用，使用替代方案")}let e=JSON.parse(localStorage.getItem("userRatedPosts")||"[]");if(0===e.length){ea([]);return}let t=e.filter(e=>!e.startsWith("archived_"));if(0===t.length){ea([]);return}let s=[];for(let e of t.slice(0,20))try{let t=await C.petAPI.getPostDetail({postId:e});t.success&&t.data&&s.push(t.data)}catch(t){console.warn("获取帖子 ".concat(e," 详情失败:"),t)}ea(s)}catch(e){console.error("加载评分记录失败:",e),_.C.error("加载评分记录失败")}finally{ei(!1)}},ew=async()=>{if(!D)return;let e=t||(null==k?void 0:k._id);if(e)try{eo(!0);let t=await C.petAPI.getUserPosts({limit:20,targetUserId:e});t.success&&el(t.data||[])}catch(e){console.error("加载发布失败:",e),_.C.error("加载发布失败")}finally{eo(!1)}};(0,l.useEffect)(()=>{"wants"===K&&B?ev():"posts"===K&&ew()},[K,B,D]);let eN=async e=>{ep({isOpen:!0,title:"确认下架",message:"下架这个宝贝并移动到待发布吗？",onConfirm:()=>e_(e)})},e_=async e=>{ep(e=>({...e,loading:!0}));try{_.C.loading("正在下架宝贝...");let t=await C.petAPI.getPostDetail({postId:e});if(!t.success){_.C.dismiss(),_.C.error("获取帖子信息失败");return}let s=t.data,a=await C.petAPI.deletePost({postId:e});if(!a||!a.success){_.C.dismiss(),_.C.error("下架失败："+((null==a?void 0:a.message)||"服务器删除失败"));return}console.log("服务器删除成功，开始保存草稿");let r=[];if(s.images&&Array.isArray(s.images))for(let e of s.images)try{if("string"==typeof e&&e.startsWith("http")){let t=await fetch(e),s=await t.blob(),a=await new Promise((e,t)=>{let a=new FileReader;a.onload=()=>e(a.result),a.onerror=t,a.readAsDataURL(s)});r.push(a),console.log("成功转换图片URL为base64:",e.substring(0,50)+"...")}else r.push(e)}catch(t){console.error("转换图片失败:",e,t),r.push(e)}let l={id:"archived_".concat(e,"_").concat(Date.now()),title:s.breed||"",breed:s.breed||"",description:s.description||"",images:r,category:s.category||"",location:s.location||"",contact_info:s.contact_info||{},type:s.type||"selling",price:s.price||"",age:s.age||"",gender:s.gender||"",vaccination:s.vaccination||!1,health_certificate:s.health_certificate||!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),isArchived:!0,originalPostId:e},n=(0,L.Oe)(),i=[l,...n];localStorage.setItem("petDrafts",JSON.stringify(i)),console.log("草稿保存成功，包含",r.length,"张图片"),el(t=>t.filter(t=>t._id!==e)),"drafts"===K&&et(i),_.C.dismiss(),_.C.success("宝贝已下架并移至待发布")}catch(e){console.error("下架宝贝失败:",e),_.C.dismiss(),_.C.error(e.message||"下架失败，请重试")}finally{ep(e=>({...e,isOpen:!1,loading:!1}))}},eC=async e=>{try{let t=(0,L.Oe)().find(t=>t.id===e);if(!t){_.C.error("草稿不存在");return}let s=t.isArchived?"确定要删除这个归档的宝贝吗？删除后无法恢复。":"确定要删除这个草稿吗？";ep({isOpen:!0,title:"确认删除",message:s,onConfirm:()=>ek(e)})}catch(e){console.error("删除草稿失败:",e),_.C.error("删除失败，请重试")}},ek=async e=>{try{let t=(0,L.Oe)(),s=t.find(t=>t.id===e),a=t.filter(t=>t.id!==e);localStorage.setItem("petDrafts",JSON.stringify(a)),et(a);let r=(null==s?void 0:s.isArchived)?"归档宝贝已删除":"草稿已删除";_.C.success(r),ep(e=>({...e,isOpen:!1}))}catch(e){console.error("删除草稿失败:",e),_.C.error("删除失败，请重试"),ep(e=>({...e,isOpen:!1}))}},eS=e=>{try{A.KX.removeRecord(e),eg(t=>t.filter(t=>t.postId!==e)),_.C.success("已从浏览历史中移除")}catch(e){console.error("删除历史记录失败:",e),_.C.error("操作失败，请重试")}},eI=(e,t)=>{ej({isOpen:!0,postId:e,postType:t})};(0,l.useEffect)(()=>{let e=t||(null==k?void 0:k._id);if(!e){E||D||_.C.error("请先登录"),W(!1);return}if(k&&k._id===e){var s;X(!0),F({_id:k._id,nickname:k.nickname,email:k.email,avatar_url:k.avatar_url,location:"未设置",bio:k.bio||"这个人很懒，什么都没有留下...",created_at:(null===(s=k.created_at)||void 0===s?void 0:s.toString())||new Date().toISOString(),posts_count:k.posts_count||0,likes_count:k.total_likes||0,followers_count:k.followers_count||0,following_count:k.following_count||0,rating:5,rating_count:0}),W(!1)}else eZ(e)},[t,k,E,D]),(0,l.useEffect)(()=>{R&&D&&!ec&&ew()},[R,D]);let eZ=async e=>{try{W(!0);let t=await C.petAPI.getUserInfo({userId:e});if(t.success&&t.data)F({...t.data,posts_count:t.data.posts_count||0,likes_count:t.data.likes_count||0,followers_count:t.data.followers_count||0,following_count:t.data.following_count||0,rating:0,rating_count:0});else{let t={_id:e,nickname:"用户".concat(e.slice(-6)),avatar_url:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",location:"北京市朝阳区",bio:"热爱宠物，专业繁殖者",created_at:"2024-01-01T00:00:00Z",posts_count:12,likes_count:89,followers_count:156,following_count:89,rating:4.8,rating_count:23};F(t)}}catch(e){console.error("加载用户资料失败:",e),_.C.error("加载用户资料失败")}finally{W(!1)}},eO=async e=>{try{let t=await C.petAPI.updateProfile(e);if(t.success)_.C.success("资料更新成功"),R&&F({...R,nickname:e.nickname||R.nickname,bio:e.bio||R.bio,contact_info:e.contactInfo||R.contact_info,address:e.address||R.address}),e.contactInfo&&k&&localStorage.setItem("contact_".concat(k._id),JSON.stringify(e.contactInfo)),void 0!==e.address&&k&&localStorage.setItem("address_".concat(k._id),e.address);else throw Error(t.message||"更新失败")}catch(e){throw _.C.error(e.message||"更新失败，请重试"),e}},eD=async e=>{var t;let a=null===(t=e.target.files)||void 0===t?void 0:t[0];if(a){if(!a.type.startsWith("image/")){eu("请选择图片文件");return}if(a.size>5242880){eu("图片大小不能超过5MB");return}em(!0),eu("");try{let{uploadFile:e}=await Promise.resolve().then(s.bind(s,98011)),t=document.createElement("canvas"),r=t.getContext("2d"),l=new Image,n=await new Promise((e,s)=>{l.onload=()=>{let{width:n,height:i}=l;n>i?n>300&&(i=300*i/n,n=300):i>300&&(n=300*n/i,i=300),t.width=n,t.height=i,null==r||r.drawImage(l,0,0,n,i),t.toBlob(t=>{if(t){let s=new File([t],a.name,{type:"image/jpeg",lastModified:Date.now()});e(s)}else s(Error("压缩失败"))},"image/jpeg",.9)},l.onerror=()=>s(Error("图片加载失败")),l.src=URL.createObjectURL(a)});console.log("头像压缩: ".concat(a.size," -> ").concat(n.size," (").concat(Math.round((1-n.size/a.size)*100),"% 减少)"));let{petAPI:i}=await Promise.resolve().then(s.bind(s,98011)),c=await new Promise((e,t)=>{let s=new FileReader;s.onload=()=>{let t=s.result.split(",")[1];e(t)},s.onerror=t,s.readAsDataURL(n)}),o=Date.now(),d=Math.random().toString(36).substring(2,8),m="avatar_".concat(o,"_").concat(d,".jpg"),x=await i.uploadToStatic({fileName:m,fileData:c,contentType:"image/jpeg"});if(!x.success)throw Error(x.message||"头像上传失败");let u=x.data.url,h=await i.updateAvatar({avatarUrl:u});if(h.success){if(F(e=>e?{...e,avatar_url:u}:null),k)try{let e={...k,avatar_url:u};localStorage.setItem("pet_platform_user",JSON.stringify(e))}catch(e){console.error("保存头像到本地存储失败:",e)}_.C.success("您的头像已经更改完成，三十天内只能修改一次头像")}else console.error("头像更新失败:",h),eu(h.message||"头像更新失败")}catch(e){console.error("头像上传失败:",e),eu("头像上传失败，请重试: "+(e instanceof Error?e.message:String(e)))}finally{em(!1)}}},eE=async e=>{try{R&&F({...R,bio:e||"这个人很懒，什么都没有留下..."})}catch(e){throw e}},eP=t=>{e.push("/upload?draftId=".concat(t))};return J?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"加载中..."})]})}):R?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white shadow-sm",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,a.jsx)("button",{onClick:()=>e.push("/"),className:"mb-6 p-2 hover:bg-gray-100 rounded-full transition-colors",children:(0,a.jsx)(o.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"w-24 h-24 md:w-32 md:h-32 rounded-full bg-gray-200 overflow-hidden",children:[R.avatar_url?(0,a.jsx)(S,{fileId:R.avatar_url,alt:R.nickname,className:"w-full h-full object-cover"}):(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,a.jsx)(d.Z,{className:"w-12 h-12 md:w-16 md:h-16 text-gray-400"})}),ed&&(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-full",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-white"})})]}),B&&(0,a.jsxs)("div",{className:"absolute bottom-0 right-0",children:[(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:eD,className:"hidden",id:"avatar-upload",disabled:ed}),(0,a.jsx)("label",{htmlFor:"avatar-upload",className:"bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 transition-colors cursor-pointer block ".concat(ed?"opacity-50 cursor-not-allowed":""),children:(0,a.jsx)(m.Z,{className:"w-4 h-4"})})]}),ex&&(0,a.jsx)("div",{className:"absolute top-full left-0 mt-2 text-red-500 text-sm",children:ex})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl md:text-3xl font-bold text-gray-900",children:R.nickname}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-gray-600",children:[R.location&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(x.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:R.location})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(u.Z,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:["加入于 ",new Date(R.created_at).getFullYear(),"年"]})]})]}),(0,a.jsxs)("div",{className:"mt-3 flex items-center space-x-2",children:[(0,a.jsx)("p",{className:"text-gray-700",children:R.bio||"这个人很懒，什么都没有留下..."}),B&&(0,a.jsx)("button",{onClick:()=>Q(!0),className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(h.Z,{className:"w-4 h-4"})})]})]}),(0,a.jsx)("div",{className:"flex space-x-3 mt-4 md:mt-0",children:B?(0,a.jsx)(N.Z,{variant:"outline",icon:(0,a.jsx)(g.Z,{className:"w-4 h-4"}),onClick:()=>Y(!0),children:"个人设置"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.Z,{icon:(0,a.jsx)(f.Z,{className:"w-4 h-4"}),children:"私信"}),(0,a.jsx)(N.Z,{variant:"outline",icon:(0,a.jsx)(p.Z,{className:"w-4 h-4"}),children:"关注"})]})})]}),(0,a.jsxs)("div",{className:"flex space-x-6 mt-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-gray-900",children:R.likes_count}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"获赞"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-gray-900",children:R.followers_count}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"粉丝"})]}),(0,a.jsxs)("div",{className:"text-center cursor-pointer",onClick:()=>B&&$(!0),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900",children:R.following_count}),B&&(0,a.jsx)(y.Z,{className:"w-4 h-4 text-gray-400"})]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"关注"})]})]})]})]})]})}),(0,a.jsx)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[B?(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1",children:[(0,a.jsxs)("button",{onClick:()=>H("posts"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("posts"===K?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(j.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"已发布宝贝"})]}),(0,a.jsxs)("button",{onClick:()=>H("drafts"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("drafts"===K?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(b.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"待发布宝贝"})]}),(0,a.jsxs)("button",{onClick:()=>H("wants"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("wants"===K?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(v.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"评分"})]}),(0,a.jsxs)("button",{onClick:()=>H("history"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("history"===K?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(w.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"浏览历史"})]})]})}):(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"".concat(R.nickname,"的发布")}),B&&(0,a.jsx)("div",{className:"mb-4 md:hidden",children:(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,a.jsx)("p",{className:"text-sm text-blue-700",children:"\uD83D\uDCA1 提示：长按卡片可显示操作选项"})})}),"posts"===K&&(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:[(0,a.jsx)(I.default,{href:"/upload",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow cursor-pointer",children:(0,a.jsx)("div",{className:"aspect-square bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.Z,{className:"w-12 h-12 text-blue-500 mx-auto mb-3"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-1",children:"发布宝贝"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"分享您的宠物"})]})})})}),ec?(0,a.jsx)("div",{className:"col-span-full text-center py-8 text-gray-500",children:(0,a.jsx)("p",{children:"加载中..."})}):0===er.length?(0,a.jsx)("div",{className:"col-span-full text-center py-8 text-gray-500",children:(0,a.jsx)("p",{children:"还没有发布任何内容"})}):er.map(e=>(0,a.jsx)(M,{post:e,onLongPress:()=>eI(e._id,"published"),onDelete:()=>eN(e._id),type:"published"},e._id))]}),"drafts"===K&&(0,a.jsxs)(a.Fragment,{children:[ee.length>0&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"待发布宝贝"}),(0,a.jsx)("button",{onClick:()=>{ep({isOpen:!0,title:"确认清空",message:"确定要清空所有待发布的宝贝吗？此操作不可恢复。",onConfirm:()=>{localStorage.removeItem("petDrafts"),et([]),_.C.success("所有待发布宝贝已清空"),ep(e=>({...e,isOpen:!1}))}})},className:"text-sm text-red-600 hover:text-red-800 transition-colors font-medium",children:"全部清空"})]}),0===ee.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(b.Z,{className:"w-16 h-16 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有待发布的内容"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"在发布页面保存草稿后会显示在这里"}),(0,a.jsx)(I.default,{href:"/upload",children:(0,a.jsx)(N.Z,{icon:(0,a.jsx)(m.Z,{className:"w-4 h-4"}),children:"创建内容"})})]}):(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:ee.map(e=>{var t;let s=(null===(t=e.images)||void 0===t?void 0:t.map(e=>"string"==typeof e?e:(console.warn("草稿中发现非字符串图片数据，使用默认图片:",e),"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center")))||[],r={_id:e.id,breed:e.breed||"未命名草稿",description:e.description||"",images:s,author:{_id:"draft",nickname:"草稿"},location:e.location||"",likes_count:0,created_at:e.updated_at||e.created_at,type:e.type||"selling",gender:e.gender};return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(M,{post:r,onLongPress:()=>eI(e.id,"draft"),onDelete:()=>eC(e.id),onClick:()=>eb(e.id),type:"draft",isDraft:!0}),e.isArchived&&(0,a.jsx)("div",{className:"absolute top-2 left-2 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium z-10",children:"已归档"})]},e.id)})})]}),"wants"===K&&(0,a.jsx)(a.Fragment,{children:en?(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("div",{className:"text-gray-400",children:"加载中..."})}):0===es.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(v.Z,{className:"w-16 h-16 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有评分任何内容"}),(0,a.jsx)("p",{className:"text-gray-600",children:"给喜欢的宠物评分后会显示在这里"})]}):(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:es.map(e=>(0,a.jsx)(U.Z,{post:e},e._id))})}),"history"===K&&(0,a.jsxs)("div",{children:[eh.length>0&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["浏览记录保留3天，共",eh.length,"条记录"]}),(0,a.jsx)("button",{onClick:()=>{ep({isOpen:!0,title:"确认清空",message:"确定要清空所有浏览历史吗？",onConfirm:()=>{A.KX.clearHistory(),eg([]),_.C.success("浏览历史已清空"),ep(e=>({...e,isOpen:!1}))}})},className:"text-sm text-red-600 hover:text-red-800 transition-colors font-medium",children:"清空历史"})]}),0===eh.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(w.Z,{className:"w-16 h-16 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有浏览历史"}),(0,a.jsx)("p",{className:"text-gray-600",children:"浏览过的内容会显示在这里，记录保留3天"})]}):(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:eh.map(e=>{let t={_id:e.postId,breed:e.title,description:"",images:e.image?[e.image]:[],author:{_id:"history",nickname:e.author||"匿名用户"},location:"",likes_count:0,created_at:new Date(e.timestamp).toISOString()};return(0,a.jsx)(M,{post:t,onLongPress:()=>eI(e.postId,"history"),onDelete:()=>eS(e.postId),type:"history"},e.postId)})})]})]})}),(0,a.jsx)(Z.Z,{isOpen:q,onClose:()=>Y(!1),currentUser:k,onUpdate:eO}),(0,a.jsx)(O,{isOpen:G,onClose:()=>Q(!1),currentBio:(null==R?void 0:R.bio)||"",onSave:eE}),(0,a.jsx)(P,{isOpen:V,onClose:()=>$(!1),userId:(null==k?void 0:k._id)||""}),(0,a.jsx)(z,{isOpen:ef.isOpen,onClose:()=>ep(e=>({...e,isOpen:!1})),onConfirm:ef.onConfirm,title:ef.title,message:ef.message,loading:ef.loading}),(0,a.jsx)(T,{isOpen:ey.isOpen,onClose:()=>ej(e=>({...e,isOpen:!1})),items:(()=>{let{postId:e,postType:t}=ey;switch(t){case"published":return[{id:"delete",label:"下架宝贝",icon:(0,a.jsx)(i.Z,{className:"w-4 h-4"}),onClick:()=>{ej(e=>({...e,isOpen:!1})),eN(e)},variant:"danger"}];case"draft":return[{id:"edit",label:"编辑草稿",icon:(0,a.jsx)(c.Z,{className:"w-4 h-4"}),onClick:()=>{ej(e=>({...e,isOpen:!1})),eP(e)}},{id:"delete",label:"删除草稿",icon:(0,a.jsx)(i.Z,{className:"w-4 h-4"}),onClick:()=>{ej(e=>({...e,isOpen:!1})),eC(e)},variant:"danger"}];case"history":return[{id:"delete",label:"从历史中移除",icon:(0,a.jsx)(i.Z,{className:"w-4 h-4"}),onClick:()=>{ej(e=>({...e,isOpen:!1})),eS(e)},variant:"danger"}];default:return[]}})(),position:ey.position})]}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"用户不存在"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"抱歉，找不到该用户的信息"}),(0,a.jsx)(I.default,{href:"/",children:(0,a.jsx)(N.Z,{children:"返回首页"})})]})})}},83565:function(e,t,s){"use strict";s.d(t,{DO:function(){return n},Oe:function(){return r},XD:function(){return i},eJ:function(){return c}});let a="petDrafts",r=()=>{try{let e=localStorage.getItem(a);return e?JSON.parse(e):[]}catch(e){return console.error("获取草稿失败:",e),[]}},l=async e=>{let t=[];for(let s of e)if(s instanceof File)try{let e=await new Promise((e,t)=>{let a=new FileReader;a.onload=()=>e(a.result),a.onerror=t,a.readAsDataURL(s)});t.push(e)}catch(e){console.error("转换图片失败:",e),t.push("https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center")}else"string"==typeof s&&t.push(s);return t},n=async e=>{try{let t=await l(e.images),s=r(),n={id:Date.now().toString(),...e,images:t,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},i=[n,...s];return localStorage.setItem(a,JSON.stringify(i)),n.id}catch(e){throw console.error("保存草稿失败:",e),Error("保存草稿失败")}},i=async(e,t)=>{try{let s=r(),n=s.findIndex(t=>t.id===e);if(-1!==n){let e=await l(t.images);s[n]={...s[n],...t,images:e,updated_at:new Date().toISOString()},localStorage.setItem(a,JSON.stringify(s))}}catch(e){throw console.error("更新草稿失败:",e),Error("更新草稿失败")}},c=e=>{try{let t=r().find(t=>t.id===e)||null;return console.log("获取草稿 ".concat(e,":"),t),t}catch(e){return console.error("获取草稿失败:",e),null}}}},function(e){e.O(0,[649,19,347,554,721,319,503,11,734,825,136,971,117,744],function(){return e(e.s=49289)}),_N_E=e.O()}]);