(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[495],{81545:function(e,s,t){Promise.resolve().then(t.bind(t,65730))},32660:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},46211:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]])},41473:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},88997:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},83774:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},98728:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},88906:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},70525:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},37806:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},43745:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]])},99376:function(e,s,t){"use strict";var r=t(35475);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},65730:function(e,s,t){"use strict";t.d(s,{default:function(){return S}});var r=t(57437),l=t(2265),a=t(99376),i=t(32660),n=t(41473),c=t(88997),d=t(70525),o=t(98728),x=t(39763);let u=(0,x.Z)("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);var m=t(46211),h=t(43745);let g=(0,x.Z)("UserMinus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var f=t(37806),y=t(98011),v=t(88941),j=t(56334),p=t(89841),b=t(31215),N=t(9356),w=t(68661),k=t(98702);let C=[{id:1,label:"疑似骗子",description:"该用户存在欺诈行为或疑似诈骗"}];var Z=e=>{let{isOpen:s,onClose:t,targetUserId:a,targetUserName:i,onSuccess:n}=e,[c,d]=(0,l.useState)(1),[o,x]=(0,l.useState)(!1),u=()=>{d(1),t()},h=async()=>{try{x(!0);let e=await y.petAPI.reportUser({targetUserId:a,reason:"疑似骗子"});e.success?(N.C.success("举报提交成功，我们会尽快处理"),null==n||n(),u()):N.C.error(e.message||"举报失败")}catch(e){console.error("举报用户失败:",e),N.C.error(e.message||"举报失败")}finally{x(!1)}};return(0,r.jsx)(k.u_,{isOpen:s,onClose:u,showCloseButton:!1,children:(0,r.jsx)(k.fe,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,r.jsx)(m.Z,{className:"h-5 w-5 text-red-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"举报用户"}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["举报 ",i]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"请选择举报原因："}),(0,r.jsx)("div",{className:"space-y-2",children:C.map(e=>(0,r.jsxs)("label",{className:"flex items-start space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ".concat(c===e.id?"border-red-500 bg-red-50":"border-gray-200 hover:bg-gray-50"),children:[(0,r.jsx)("input",{type:"radio",name:"reason",value:e.id,checked:c===e.id,onChange:()=>d(e.id),className:"mt-1 text-red-600 focus:ring-red-500"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.label}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})]},e.id))})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(j.Z,{variant:"outline",onClick:u,className:"flex-1",disabled:o,children:"取消"}),(0,r.jsx)(j.Z,{variant:"danger",onClick:h,loading:o,className:"flex-1",disabled:!1,children:"提交举报"})]})]})})})},_=e=>{let{isOpen:s,onClose:t,targetUserId:a,targetUserName:i,onSuccess:n}=e,[c,d]=(0,l.useState)(!1),o=()=>{t()},x=async()=>{try{d(!0);let e=await y.petAPI.blockUser({targetUserId:a});e.success?(N.C.success("已拉黑该用户"),null==n||n(),o()):N.C.error(e.message||"拉黑失败")}catch(e){console.error("拉黑用户失败:",e),N.C.error(e.message||"拉黑失败")}finally{d(!1)}};return(0,r.jsx)(k.u_,{isOpen:s,onClose:o,showCloseButton:!1,children:(0,r.jsx)(k.fe,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-gray-100 rounded-lg",children:(0,r.jsx)(h.Z,{className:"h-5 w-5 text-gray-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"拉黑用户"}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["拉黑 ",i]})]})]}),(0,r.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,r.jsx)("p",{className:"text-sm text-yellow-700",children:"您将不会再看到该用户发布的内容，该用户也不能再联系您"})}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(j.Z,{variant:"outline",onClick:o,className:"flex-1",disabled:c,children:"取消"}),(0,r.jsx)(j.Z,{variant:"danger",onClick:x,loading:c,className:"flex-1",children:"确认拉黑"})]})]})})})},P=t(26467),S=e=>{let{userId:s}=e,t=(0,a.useParams)(),x=(0,a.useRouter)(),{user:k,isLoggedIn:C}=(0,v.E)(),S=s||t.id,[A,I]=(0,l.useState)(null),[U,M]=(0,l.useState)([]),[z,E]=(0,l.useState)(!0),[V,F]=(0,l.useState)(!1),[O,R]=(0,l.useState)(!1),[D,L]=(0,l.useState)("created_at"),[q,B]=(0,l.useState)(!1),[H,T]=(0,l.useState)(!1),[X,G]=(0,l.useState)(!1),[W,Y]=(0,l.useState)(!1),J=(null==k?void 0:k._id)===S,K=async()=>{try{if(E(!0),!S){N.C.error("用户ID无效"),x.push("/");return}let e=await y.petAPI.getUserInfo({userId:S});if(e.success){if(I(e.data),C&&k&&k._id!==S)try{let e=await y.petAPI.getUserFollowing({targetUserId:k._id,limit:1e3});if(e.success){let s=e.data.some(e=>e.following_id===S);R(s)}}catch(e){console.warn("检查关注状态失败:",e)}}else N.C.error("用户不存在"),x.push("/")}catch(e){console.error("获取用户信息失败:",e),N.C.error("获取用户信息失败"),x.push("/")}finally{E(!1)}},Q=async()=>{try{F(!0);let e=await y.petAPI.getUserPosts({targetUserId:S,limit:50,offset:0});e.success&&M(e.data||[])}catch(e){console.error("获取用户帖子失败:",e)}finally{F(!1)}},$=async()=>{if(!C){N.C.warning("请先登录");return}try{let e=await y.petAPI.toggleFollow({targetUserId:S});e.success?(R("followed"===e.action),N.C.success(e.message),A&&I(s=>s?{...s,followers_count:(s.followers_count||0)+("followed"===e.action?1:-1)}:null)):N.C.error(e.message||"操作失败")}catch(e){N.C.error(e.message||"操作失败")}};if((0,l.useEffect)(()=>{S&&K()},[S]),(0,l.useEffect)(()=>{S&&Q()},[S,D]),(0,l.useEffect)(()=>{if(S&&C&&k&&A){let e=async()=>{try{let e=await y.petAPI.getUserFollowing({targetUserId:k._id,limit:1e3});if(e.success){let s=e.data.some(e=>e.following_id===S);R(s)}}catch(e){console.warn("检查关注状态失败:",e)}};k._id!==S&&e()}},[C,k,S,A]),z)return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"flex items-center h-16",children:(0,r.jsx)("button",{onClick:()=>x.push("/"),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(i.Z,{className:"h-5 w-5"})})})})}),(0,r.jsx)(b.LL,{})]});if(!A)return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"用户不存在"}),(0,r.jsx)(j.Z,{onClick:()=>x.push("/"),children:"返回首页"})]})});let ee=[{value:"created_at",label:"最新",icon:n.Z},{value:"likes_count",label:"最受欢迎",icon:c.Z},{value:"wants_count",label:"收藏最多",icon:d.Z}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"bg-white border-b border-gray-200 sticky top-0 z-10",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("button",{onClick:()=>x.push("/"),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(i.Z,{className:"h-5 w-5"})}),(0,r.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:A.nickname})]}),J?(0,r.jsx)(j.Z,{variant:"outline",icon:(0,r.jsx)(o.Z,{className:"h-4 w-4"}),onClick:()=>x.push("/profile"),children:"个人设置"}):C&&(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("button",{onClick:()=>B(!q),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(u,{className:"h-5 w-5 text-gray-600"})}),q&&(0,r.jsxs)("div",{className:"absolute right-0 top-full mt-2 w-40 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-20",children:[(0,r.jsxs)("button",{onClick:()=>{T(!0),B(!1)},className:"flex items-center space-x-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-gray-100",children:[(0,r.jsx)(m.Z,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"举报用户"})]}),(0,r.jsxs)("button",{onClick:()=>{G(!0),B(!1)},className:"flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,r.jsx)(h.Z,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"拉黑用户"})]})]})]})]})})}),(0,r.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-6",children:[(0,r.jsx)("div",{className:"relative",children:A.avatar_url?(0,r.jsx)("img",{src:A.avatar_url,alt:A.nickname,className:"w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg"}):(0,r.jsx)("div",{className:"w-24 h-24 rounded-full bg-gray-300 border-4 border-white shadow-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-2xl font-bold text-gray-600",children:A.nickname.charAt(0).toUpperCase()})})}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:A.nickname}),A.bio?(0,r.jsx)("p",{className:"text-gray-600 max-w-md",children:A.bio}):(0,r.jsx)("p",{className:"text-gray-400 italic",children:J?"点击添加简介，让大家了解你":"这个人很懒，什么都没留下"})]}),(0,r.jsxs)("div",{className:"flex space-x-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,w.uf)(A.total_likes||0)}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"获赞"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,w.uf)(A.followers_count||0)}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"粉丝"})]}),(0,r.jsxs)("div",{className:(0,w.cn)("text-center",J&&"cursor-pointer hover:bg-gray-50 rounded-lg p-2 transition-colors"),onClick:()=>J&&Y(!0),title:J?"点击查看关注列表":void 0,children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,w.uf)(A.following_count||0)}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"关注"})]})]}),!J&&C&&(0,r.jsx)(j.Z,{onClick:$,variant:O?"outline":"primary",icon:O?(0,r.jsx)(g,{className:"h-4 w-4"}):(0,r.jsx)(f.Z,{className:"h-4 w-4"}),children:O?"已关注":"关注"})]})})}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["发布的宠物 (",A.posts_count||0,")"]}),(0,r.jsx)("div",{className:"flex space-x-2",children:ee.map(e=>{let s=e.icon;return(0,r.jsxs)("button",{onClick:()=>L(e.value),className:(0,w.cn)("flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors",D===e.value?"bg-primary-600 text-white":"bg-white text-gray-700 hover:bg-gray-100 border border-gray-200"),children:[(0,r.jsx)(s,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:e.label})]},e.value)})})]}),V?(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:Array.from({length:8}).map((e,s)=>(0,r.jsx)(b.gG,{},s))}):U.length>0?(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:U.map(e=>(0,r.jsx)(p.Z,{post:e},e._id))}):(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsxs)("div",{className:"text-gray-500",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDC3E"}),(0,r.jsx)("p",{className:"text-lg font-medium mb-2",children:J?"还没有发布宠物":"TA还没有发布宠物"}),(0,r.jsx)("p",{className:"text-sm",children:J?"快去发布第一只宠物吧！":"期待TA的第一次分享"}),J&&(0,r.jsx)(j.Z,{className:"mt-4",onClick:()=>x.push("/upload"),children:"发布宠物"})]})})]}),(0,r.jsx)(Z,{isOpen:H,onClose:()=>T(!1),targetUserId:S,targetUserName:(null==A?void 0:A.nickname)||"",onSuccess:()=>{N.C.success("举报提交成功"),T(!1)}}),(0,r.jsx)(_,{isOpen:X,onClose:()=>G(!1),targetUserId:S,targetUserName:(null==A?void 0:A.nickname)||"",onSuccess:()=>{N.C.success("已拉黑该用户"),R(!1),G(!1),x.push("/")}}),(0,r.jsx)(P.Z,{isOpen:W,onClose:()=>Y(!1),userId:(null==k?void 0:k._id)||""})]})}},88941:function(e,s,t){"use strict";t.d(s,{AuthProvider:function(){return c},E:function(){return d},Y:function(){return o}});var r=t(57437),l=t(2265),a=t(98734),i=t(31215);let n=(0,l.createContext)(void 0),c=e=>{let{children:s}=e,t=(0,a.a)();return t.isLoading&&!t.user?(0,r.jsx)(i.SX,{text:"正在初始化..."}):(0,r.jsx)(n.Provider,{value:t,children:s})},d=()=>{let e=(0,l.useContext)(n);if(void 0===e)throw Error("useAuthContext must be used within an AuthProvider");return e},o=e=>{let{children:s,fallback:t}=e,{isLoggedIn:l,isLoading:a}=d();return a?(0,r.jsx)(i.SX,{text:"验证登录状态..."}):l?(0,r.jsx)(r.Fragment,{children:s}):t||(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"需要登录"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"请先登录后再访问此页面"})]})})}},26467:function(e,s,t){"use strict";t.d(s,{Z:function(){return h}});var r=t(57437),l=t(2265),a=t(99376),i=t(32489),n=t(88997),c=t(88906),d=t(92369),o=t(43745),x=t(56334),u=t(9356),m=t(98011);function h(e){let{isOpen:s,onClose:t,userId:h}=e,g=(0,a.useRouter)(),[f,y]=(0,l.useState)("following"),[v,j]=(0,l.useState)([]),[p,b]=(0,l.useState)([]),[N,w]=(0,l.useState)(!1);(0,l.useEffect)(()=>{s&&("following"===f?C():P())},[s,h,f]);let k=e=>{try{g.push("/profile/".concat(e)),t()}catch(e){console.error("导航到用户资料页面失败:",e),u.C.error("无法打开用户资料页面")}},C=async()=>{w(!0);try{let e=await m.petAPI.getUserFollowing({targetUserId:h,limit:100});if(e.success){let s=e.data.map(e=>({_id:e.following_id,nickname:e.following_nickname||"用户已删除",avatar_url:e.following_avatar_url||"/default-avatar.png",bio:e.following_bio||"",isFollowing:!0}));j(s)}else u.C.error(e.message||"加载关注列表失败")}catch(e){u.C.error("加载关注列表失败")}finally{w(!1)}},Z=async e=>{try{let s=await m.petAPI.toggleFollow({targetUserId:e});s.success?(j(s=>s.map(s=>s._id===e?{...s,isFollowing:!1}:s)),u.C.success("已取消关注")):u.C.error(s.message||"取消关注失败")}catch(e){u.C.error("取消关注失败")}},_=async e=>{try{let s=await m.petAPI.toggleFollow({targetUserId:e});s.success?(j(s=>s.map(s=>s._id===e?{...s,isFollowing:!0}:s)),u.C.success("关注成功")):u.C.error(s.message||"关注失败")}catch(e){u.C.error("关注失败")}},P=async()=>{w(!0);try{let e=await m.petAPI.getBlockedUsers();e.success?b(e.data||[]):u.C.error(e.message||"加载黑名单失败")}catch(e){u.C.error("加载黑名单失败")}finally{w(!1)}},S=async e=>{try{let s=await m.petAPI.unblockUser({targetUserId:e});s.success?(b(s=>s.filter(s=>s._id!==e)),u.C.success("已取消拉黑")):u.C.error(s.message||"取消拉黑失败")}catch(e){u.C.error("取消拉黑失败")}};return s?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",onClick:e=>{e.target===e.currentTarget&&t()},children:(0,r.jsxs)("div",{className:"bg-white rounded-lg w-[480px] h-[500px] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b flex-shrink-0",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"following"===f?"我的关注":"黑名单"}),(0,r.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(i.Z,{className:"w-6 h-6"})})]}),(0,r.jsx)("div",{className:"border-b border-gray-200 flex-shrink-0",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsxs)("button",{onClick:()=>y("following"),className:"flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors ".concat("following"===f?"text-blue-600 border-b-2 border-blue-600 bg-blue-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[(0,r.jsx)(n.Z,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"我的关注"})]}),(0,r.jsxs)("button",{onClick:()=>y("blocked"),className:"flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors ".concat("blocked"===f?"text-blue-600 border-b-2 border-blue-600 bg-blue-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[(0,r.jsx)(c.Z,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"黑名单"})]})]})}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto",children:N?(0,r.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):"following"===f?0===v.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(n.Z,{className:"w-16 h-16 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有关注任何人"}),(0,r.jsx)("p",{className:"text-gray-600",children:"去发现更多有趣的用户吧！"})]}):(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:v.map(e=>(0,r.jsx)("div",{className:"p-4 hover:bg-gray-50",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-200 overflow-hidden flex-shrink-0 cursor-pointer hover:ring-2 hover:ring-blue-300 transition-all",onClick:()=>k(e._id),title:"点击查看用户资料",children:e.avatar_url?(0,r.jsx)("img",{src:e.avatar_url,alt:e.nickname,className:"w-full h-full object-cover"}):(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,r.jsx)(d.Z,{className:"w-6 h-6 text-gray-400"})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-blue-600 hover:text-blue-800 cursor-pointer truncate transition-colors",onClick:()=>k(e._id),title:"点击查看用户资料",children:e.nickname}),e.bio&&(0,r.jsx)("p",{className:"text-sm text-gray-500 truncate",children:e.bio})]}),(0,r.jsx)("div",{className:"flex-shrink-0",children:e.isFollowing?(0,r.jsx)(x.Z,{variant:"outline",size:"sm",onClick:()=>Z(e._id),children:"取消关注"}):(0,r.jsx)(x.Z,{size:"sm",onClick:()=>_(e._id),children:"关注"})})]})},e._id))}):0===p.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(c.Z,{className:"w-16 h-16 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"黑名单为空"}),(0,r.jsx)("p",{className:"text-gray-600",children:"您还没有拉黑任何用户"})]}):(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:p.map(e=>(0,r.jsx)("div",{className:"p-4 hover:bg-gray-50",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-200 overflow-hidden flex-shrink-0 cursor-pointer hover:ring-2 hover:ring-blue-300 transition-all",onClick:()=>k(e._id),title:"点击查看用户资料",children:e.avatar_url?(0,r.jsx)("img",{src:e.avatar_url,alt:e.nickname,className:"w-full h-full object-cover"}):(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,r.jsx)(o.Z,{className:"w-6 h-6 text-gray-400"})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-blue-600 hover:text-blue-800 cursor-pointer truncate transition-colors",onClick:()=>k(e._id),title:"点击查看用户资料",children:e.nickname}),(0,r.jsxs)("p",{className:"text-sm text-gray-500 truncate",children:["拉黑时间：",new Date(e.blockedAt).toLocaleDateString()]})]}),(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(x.Z,{variant:"outline",size:"sm",onClick:()=>S(e._id),className:"text-red-600 border-red-200 hover:bg-red-50",children:"取消拉黑"})})]})},e._id))})})]})}):null}},31215:function(e,s,t){"use strict";t.d(s,{LL:function(){return c},SX:function(){return i},gG:function(){return n},gb:function(){return a}});var r=t(57437);t(2265);var l=t(68661);let a=e=>{let{size:s="md",variant:t="spinner",className:a,text:i}=e,n={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"},c={sm:"text-sm",md:"text-base",lg:"text-lg"};if("spinner"===t)return(0,r.jsx)("div",{className:(0,l.cn)("flex items-center justify-center",a),children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsxs)("svg",{className:(0,l.cn)("animate-spin text-primary-600",n[s]),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i&&(0,r.jsx)("p",{className:(0,l.cn)("text-gray-500",c[s]),children:i})]})});if("dots"===t){let e="sm"===s?"w-2 h-2":"md"===s?"w-3 h-3":"w-4 h-4";return(0,r.jsx)("div",{className:(0,l.cn)("flex items-center justify-center",a),children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("div",{className:(0,l.cn)("bg-primary-600 rounded-full animate-bounce",e),style:{animationDelay:"0ms"}}),(0,r.jsx)("div",{className:(0,l.cn)("bg-primary-600 rounded-full animate-bounce",e),style:{animationDelay:"150ms"}}),(0,r.jsx)("div",{className:(0,l.cn)("bg-primary-600 rounded-full animate-bounce",e),style:{animationDelay:"300ms"}})]}),i&&(0,r.jsx)("p",{className:(0,l.cn)("text-gray-500",c[s]),children:i})]})})}return"pulse"===t?(0,r.jsx)("div",{className:(0,l.cn)("flex items-center justify-center",a),children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsx)("div",{className:(0,l.cn)("bg-primary-600 rounded-full animate-pulse",n[s])}),i&&(0,r.jsx)("p",{className:(0,l.cn)("text-gray-500",c[s]),children:i})]})}):null},i=e=>{let{text:s="加载中..."}=e;return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsx)(a,{size:"lg",text:s})})},n=()=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden animate-pulse",children:[(0,r.jsx)("div",{className:"aspect-square bg-gray-200"}),(0,r.jsxs)("div",{className:"p-3 space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/4"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/4"})]})]})]}),c=()=>(0,r.jsx)("div",{className:"animate-pulse",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4 p-6",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-gray-200 rounded-full"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48"}),(0,r.jsxs)("div",{className:"flex space-x-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-8"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-8"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-8"})]})]}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-24"})]})})},98702:function(e,s,t){"use strict";t.d(s,{fe:function(){return o},u_:function(){return d}});var r=t(57437),l=t(2265),a=t(54887),i=t(32489),n=t(68661),c=t(56334);let d=e=>{let{isOpen:s,onClose:t,title:d,children:o,size:x="md",showCloseButton:u=!0,closeOnOverlayClick:m=!0,className:h}=e;if((0,l.useEffect)(()=>{let e=e=>{"Escape"===e.key&&t()};return s&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[s,t]),!s)return null;let g=(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:m?t:void 0}),(0,r.jsxs)("div",{className:(0,n.cn)("relative bg-white rounded-lg shadow-xl w-full mx-4 max-h-[90vh] overflow-hidden",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[x],h),onClick:e=>e.stopPropagation(),children:[(d||u)&&(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[d&&(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:d}),u&&(0,r.jsx)(c.Z,{variant:"ghost",size:"sm",onClick:t,className:"p-1 hover:bg-gray-100 rounded-full",children:(0,r.jsx)(i.Z,{className:"h-5 w-5"})})]}),(0,r.jsx)("div",{className:"overflow-y-auto max-h-[calc(90vh-80px)]",children:o})]})]});return(0,a.createPortal)(g,document.body)},o=e=>{let{children:s,className:t}=e;return(0,r.jsx)("div",{className:(0,n.cn)("p-4",t),children:s})}},28819:function(e,s,t){"use strict";t.d(s,{YD:function(){return d}});var r=t(2265),l=Object.defineProperty,a=new Map,i=new WeakMap,n=0,c=void 0;function d(){var e;let{threshold:s,delay:t,trackVisibility:l,rootMargin:d,root:o,triggerOnce:x,skip:u,initialInView:m,fallbackInView:h,onChange:g}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[f,y]=r.useState(null),v=r.useRef(g),[j,p]=r.useState({inView:!!m,entry:void 0});v.current=g,r.useEffect(()=>{let e;if(!u&&f)return e=function(e,s){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:c;if(void 0===window.IntersectionObserver&&void 0!==r){let l=e.getBoundingClientRect();return s(r,{isIntersecting:r,target:e,intersectionRatio:"number"==typeof t.threshold?t.threshold:0,time:0,boundingClientRect:l,intersectionRect:l,rootBounds:l}),()=>{}}let{id:l,observer:d,elements:o}=function(e){let s=Object.keys(e).sort().filter(s=>void 0!==e[s]).map(s=>{var t;return"".concat(s,"_").concat("root"===s?(t=e.root)?(i.has(t)||(n+=1,i.set(t,n.toString())),i.get(t)):"0":e[s])}).toString(),t=a.get(s);if(!t){let r;let l=new Map,i=new IntersectionObserver(s=>{s.forEach(s=>{var t;let a=s.isIntersecting&&r.some(e=>s.intersectionRatio>=e);e.trackVisibility&&void 0===s.isVisible&&(s.isVisible=a),null==(t=l.get(s.target))||t.forEach(e=>{e(a,s)})})},e);r=i.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),t={id:s,observer:i,elements:l},a.set(s,t)}return t}(t),x=o.get(e)||[];return o.has(e)||o.set(e,x),x.push(s),d.observe(e),function(){x.splice(x.indexOf(s),1),0===x.length&&(o.delete(e),d.unobserve(e)),0===o.size&&(d.disconnect(),a.delete(l))}}(f,(s,t)=>{p({inView:s,entry:t}),v.current&&v.current(s,t),t.isIntersecting&&x&&e&&(e(),e=void 0)},{root:o,rootMargin:d,threshold:s,trackVisibility:l,delay:t},h),()=>{e&&e()}},[Array.isArray(s)?s.toString():s,f,o,d,x,u,l,h,t]);let b=null==(e=j.entry)?void 0:e.target,N=r.useRef(void 0);f||!b||x||u||N.current===b||(N.current=b,p({inView:!!m,entry:void 0}));let w=[y,j.inView,j.entry];return w.ref=w[0],w.inView=w[1],w.entry=w[2],w}r.Component}},function(e){e.O(0,[649,19,347,554,721,319,11,734,825,971,117,744],function(){return e(e.s=81545)}),_N_E=e.O()}]);