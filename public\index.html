<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宠物交易平台 - TikTok风格的现代化宠物交易平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js"></script>
    <style>
        .loading-spinner {
            border: 4px solid #f3f4f6;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <div id="app">
        <!-- 加载状态 -->
        <div id="loading" class="min-h-screen flex items-center justify-center">
            <div class="text-center">
                <div class="loading-spinner mx-auto mb-4"></div>
                <p class="text-gray-600">正在初始化...</p>
            </div>
        </div>

        <!-- 主要内容 -->
        <div id="main-content" class="hidden">
            <!-- 头部导航 -->
            <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between items-center h-16">
                        <div class="flex items-center">
                            <h1 class="text-xl font-bold text-gray-900">🐾 宠物交易平台</h1>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button id="login-btn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                快速登录
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 主要内容区域 -->
            <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <!-- 分类筛选 -->
                <div class="mb-6">
                    <div class="flex flex-wrap gap-2">
                        <button class="category-btn bg-blue-600 text-white px-4 py-2 rounded-lg" data-category="all">
                            全部
                        </button>
                        <button class="category-btn bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300" data-category="mammals">
                            哺乳动物
                        </button>
                        <button class="category-btn bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300" data-category="birds">
                            鸟类
                        </button>
                        <button class="category-btn bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300" data-category="others">
                            其他
                        </button>
                    </div>
                </div>

                <!-- 宠物网格 -->
                <div id="pets-grid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                    <!-- 宠物卡片将通过 JavaScript 动态生成 -->
                </div>

                <!-- 加载更多 -->
                <div id="load-more" class="text-center mt-8">
                    <button class="bg-gray-200 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-300 transition-colors">
                        加载更多
                    </button>
                </div>
            </main>
        </div>

        <!-- 错误状态 -->
        <div id="error-content" class="hidden min-h-screen flex items-center justify-center">
            <div class="text-center">
                <div class="text-6xl mb-4">😿</div>
                <h2 class="text-2xl font-bold text-gray-900 mb-4">初始化失败</h2>
                <p class="text-gray-600 mb-6">请检查网络连接或稍后重试</p>
                <button onclick="location.reload()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    重新加载
                </button>
            </div>
        </div>
    </div>

    <script>
        // CloudBase 配置
        const app = cloudbase.init({
            env: 'yichongyuzhou-3g9112qwf5f3487b'
        });

        const auth = app.auth({
            persistence: 'local'
        });

        const db = app.database();

        // 页面元素
        const loadingEl = document.getElementById('loading');
        const mainContentEl = document.getElementById('main-content');
        const errorContentEl = document.getElementById('error-content');
        const petsGridEl = document.getElementById('pets-grid');
        const loginBtn = document.getElementById('login-btn');

        // 初始化应用
        async function initApp() {
            try {
                console.log('开始初始化 CloudBase...');
                
                // 尝试匿名登录
                const loginState = await auth.getLoginState();
                if (!loginState || !loginState.isLoggedIn) {
                    await auth.signInAnonymously();
                    console.log('匿名登录成功');
                }

                // 加载宠物数据
                await loadPets();

                // 显示主要内容
                loadingEl.classList.add('hidden');
                mainContentEl.classList.remove('hidden');

                console.log('应用初始化成功');
            } catch (error) {
                console.error('应用初始化失败:', error);
                showError();
            }
        }

        // 加载宠物数据
        async function loadPets() {
            try {
                // 调用云函数获取宠物列表
                const result = await app.callFunction({
                    name: 'pet-api',
                    data: {
                        action: 'getPosts',
                        data: {
                            page: 1,
                            limit: 20
                        }
                    }
                });

                if (result.result && result.result.success) {
                    renderPets(result.result.data || []);
                } else {
                    // 如果云函数调用失败，显示示例数据
                    renderSamplePets();
                }
            } catch (error) {
                console.error('加载宠物数据失败:', error);
                // 显示示例数据
                renderSamplePets();
            }
        }

        // 渲染宠物列表
        function renderPets(pets) {
            petsGridEl.innerHTML = pets.map(pet => `
                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow cursor-pointer">
                    <div class="aspect-square bg-gray-200 relative">
                        ${pet.images && pet.images[0] ? 
                            `<img src="${pet.images[0]}" alt="${pet.title}" class="w-full h-full object-cover">` :
                            `<div class="w-full h-full flex items-center justify-center text-gray-400">
                                <span class="text-4xl">🐾</span>
                            </div>`
                        }
                        <div class="absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                            ${pet.category_name || '宠物'}
                        </div>
                    </div>
                    <div class="p-3">
                        <h3 class="font-medium text-gray-900 truncate">${pet.title}</h3>
                        <p class="text-sm text-gray-500 mt-1">${pet.location || '未知位置'}</p>
                        <div class="flex items-center justify-between mt-2">
                            <div class="flex items-center space-x-2 text-xs text-gray-500">
                                <span>❤️ ${pet.likes_count || 0}</span>
                                <span>⭐ ${pet.avg_rating || 0}</span>
                            </div>
                            <span class="text-xs text-gray-400">${formatTime(pet.created_at)}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 渲染示例数据
        function renderSamplePets() {
            const samplePets = [
                { title: '可爱的金毛犬', category_name: '哺乳动物', location: '北京', likes_count: 15, avg_rating: 4.5 },
                { title: '活泼的边牧', category_name: '哺乳动物', location: '上海', likes_count: 23, avg_rating: 4.8 },
                { title: '温顺的布偶猫', category_name: '哺乳动物', location: '广州', likes_count: 31, avg_rating: 4.9 },
                { title: '聪明的鹦鹉', category_name: '鸟类', location: '深圳', likes_count: 12, avg_rating: 4.2 },
                { title: '可爱的仓鼠', category_name: '其他', location: '杭州', likes_count: 8, avg_rating: 4.0 }
            ];
            renderPets(samplePets);
        }

        // 显示错误状态
        function showError() {
            loadingEl.classList.add('hidden');
            errorContentEl.classList.remove('hidden');
        }

        // 格式化时间
        function formatTime(timestamp) {
            if (!timestamp) return '刚刚';
            const date = new Date(timestamp);
            const now = new Date();
            const diff = now - date;
            const minutes = Math.floor(diff / 60000);
            const hours = Math.floor(diff / 3600000);
            const days = Math.floor(diff / 86400000);

            if (days > 0) return `${days}天前`;
            if (hours > 0) return `${hours}小时前`;
            if (minutes > 0) return `${minutes}分钟前`;
            return '刚刚';
        }

        // 登录按钮事件
        loginBtn.addEventListener('click', async () => {
            try {
                // 这里可以实现更复杂的登录逻辑
                alert('登录功能开发中...');
            } catch (error) {
                console.error('登录失败:', error);
            }
        });

        // 分类筛选事件
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                // 更新按钮状态
                document.querySelectorAll('.category-btn').forEach(b => {
                    b.classList.remove('bg-blue-600', 'text-white');
                    b.classList.add('bg-gray-200', 'text-gray-700');
                });
                btn.classList.remove('bg-gray-200', 'text-gray-700');
                btn.classList.add('bg-blue-600', 'text-white');

                // 这里可以实现分类筛选逻辑
                console.log('筛选分类:', btn.dataset.category);
            });
        });

        // 启动应用
        document.addEventListener('DOMContentLoaded', initApp);
    </script>
</body>
</html>
