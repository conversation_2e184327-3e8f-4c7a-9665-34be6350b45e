(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[652,882],{23648:function(e,t,s){Promise.resolve().then(s.bind(s,53717))},53717:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return P}});var r=s(57437),a=s(2265),i=s(42488),l=s(70525),c=s(50091),n=s(11239),o=s(91723),d=s(80240),m=s(33882),h=s(56334),x=s(47625),g=s(42736),u=s(56940),f=s(97059),p=s(2027),b=s(77719),j=s(87774),v=s(86810),y=s(10062),N=s(20407),w=s(63639),S=s(95252),C=s(84428),T=s(92735),R=()=>{let[e,t]=(0,a.useState)(null),[s,i]=(0,a.useState)(!0);(0,a.useEffect)(()=>{c();let e=setInterval(c,6e4);return()=>clearInterval(e)},[]);let c=async()=>{try{t({storage:{used:3.2,limit:5,cost:0},traffic:{used:12.5,limit:5,cost:7.5*.18},requests:{used:85e4,limit:1e7,cost:0},totalCost:1.35,projectedCost:4.05}),i(!1)}catch(e){console.error("加载成本数据失败:",e),i(!1)}},n=e=>e>=1e6?"".concat((e/1e6).toFixed(1),"M"):e>=1e3?"".concat((e/1e3).toFixed(1),"K"):e.toString(),o=(e,t)=>{let s=e/t*100;return s>=90?"text-red-600":s>=70?"text-yellow-600":"text-green-600"},d=(e,t)=>{let s=e/t*100;return s>=90?"bg-red-500":s>=70?"bg-yellow-500":"bg-green-500"};if(s)return(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4 mb-4"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-5/6"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-4/6"})]})]})});if(!e)return(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,r.jsxs)("div",{className:"text-center text-gray-500",children:[(0,r.jsx)(w.Z,{className:"w-8 h-8 mx-auto mb-2"}),(0,r.jsx)("p",{children:"无法加载成本数据"})]})});let m=[{name:"存储",免费额度:e.storage.limit,已使用:e.storage.used},{name:"流量",免费额度:e.traffic.limit,已使用:e.traffic.used},{name:"请求",免费额度:e.requests.limit/1e6,已使用:e.requests.used/1e6}],h=[{name:"存储费用",value:e.storage.cost,color:"#8884d8"},{name:"流量费用",value:e.traffic.cost,color:"#82ca9d"},{name:"请求费用",value:e.requests.cost,color:"#ffc658"}];return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(S.Z,{className:"w-8 h-8 text-green-500 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"本月成本"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["\xa5",e.totalCost.toFixed(2)]})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(l.Z,{className:"w-8 h-8 text-blue-500 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"预测成本"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["\xa5",e.projectedCost.toFixed(2)]})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(C.Z,{className:"w-8 h-8 text-purple-500 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"存储使用"}),(0,r.jsxs)("p",{className:"text-2xl font-bold ".concat(o(e.storage.used,e.storage.limit)),children:[(e.storage.used/e.storage.limit*100).toFixed(1),"%"]})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(T.Z,{className:"w-8 h-8 text-orange-500 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"流量使用"}),(0,r.jsxs)("p",{className:"text-2xl font-bold ".concat(o(e.traffic.used,e.traffic.limit)),children:[(e.traffic.used/e.traffic.limit*100).toFixed(1),"%"]})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"资源使用情况"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"存储空间"}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[e.storage.used.toFixed(2),"GB / ",e.storage.limit,"GB"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"h-2 rounded-full ".concat(d(e.storage.used,e.storage.limit)),style:{width:"".concat(Math.min(e.storage.used/e.storage.limit*100,100),"%")}})}),e.storage.cost>0&&(0,r.jsxs)("p",{className:"text-xs text-red-600 mt-1",children:["超出免费额度，费用: \xa5",e.storage.cost.toFixed(2)]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"CDN流量"}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[e.traffic.used.toFixed(2),"GB / ",e.traffic.limit,"GB"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"h-2 rounded-full ".concat(d(e.traffic.used,e.traffic.limit)),style:{width:"".concat(Math.min(e.traffic.used/e.traffic.limit*100,100),"%")}})}),e.traffic.cost>0&&(0,r.jsxs)("p",{className:"text-xs text-red-600 mt-1",children:["超出免费额度，费用: \xa5",e.traffic.cost.toFixed(2)]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"API请求"}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[n(e.requests.used)," / ",n(e.requests.limit)]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"h-2 rounded-full ".concat(d(e.requests.used,e.requests.limit)),style:{width:"".concat(e.requests.used/e.requests.limit*100,"%")}})}),e.requests.cost>0&&(0,r.jsxs)("p",{className:"text-xs text-red-600 mt-1",children:["超出免费额度，费用: \xa5",e.requests.cost.toFixed(2)]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"使用量对比"}),(0,r.jsx)(x.h,{width:"100%",height:250,children:(0,r.jsxs)(g.v,{data:m,children:[(0,r.jsx)(u.q,{strokeDasharray:"3 3"}),(0,r.jsx)(f.K,{dataKey:"name"}),(0,r.jsx)(p.Q,{}),(0,r.jsx)(b.u,{}),(0,r.jsx)(j.$,{dataKey:"免费额度",fill:"#e5e7eb"}),(0,r.jsx)(j.$,{dataKey:"已使用",fill:"#3b82f6"})]})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"成本构成"}),(0,r.jsx)(x.h,{width:"100%",height:200,children:(0,r.jsxs)(v.u,{children:[(0,r.jsx)(y.b,{data:h.filter(e=>e.value>0),cx:"50%",cy:"50%",outerRadius:80,dataKey:"value",label:e=>{let{name:t,value:s}=e;return"".concat(t,": \xa5").concat((s||0).toFixed(2))},children:h.map((e,t)=>(0,r.jsx)(N.b,{fill:e.color},"cell-".concat(t)))}),(0,r.jsx)(b.u,{})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"优化建议"}),(0,r.jsxs)("div",{className:"space-y-3",children:[e.storage.used/e.storage.limit>.8&&(0,r.jsx)("div",{className:"p-3 bg-yellow-50 border border-yellow-200 rounded",children:(0,r.jsxs)("p",{className:"text-sm text-yellow-800",children:[(0,r.jsx)("strong",{children:"存储空间即将用完"}),(0,r.jsx)("br",{}),"建议启用图片压缩或迁移到云存储"]})}),e.traffic.used>e.traffic.limit&&(0,r.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 rounded",children:(0,r.jsxs)("p",{className:"text-sm text-red-800",children:[(0,r.jsx)("strong",{children:"流量已超出免费额度"}),(0,r.jsx)("br",{}),"建议启用CDN缓存或图片懒加载"]})}),0===e.totalCost&&(0,r.jsx)("div",{className:"p-3 bg-green-50 border border-green-200 rounded",children:(0,r.jsxs)("p",{className:"text-sm text-green-800",children:[(0,r.jsx)("strong",{children:"当前在免费额度内"}),(0,r.jsx)("br",{}),"继续保持当前的使用模式"]})})]})]})]})]})},P=()=>{var e,t;let[s,x]=(0,a.useState)(null),[g,u]=(0,a.useState)(null),[f,p]=(0,a.useState)(!0),[b,j]=(0,a.useState)("performance");(0,a.useEffect)(()=>{v();let e=setInterval(v,5e3);return()=>clearInterval(e)},[]);let v=()=>{try{let e=d.Bm.getMetrics(),t=d.Bm.getPerformanceReport(),s=d.Bm.getPerformanceScore();x({metrics:e,report:t,score:s}),u({dataCache:m.dataCache.getStats(),imageCache:m.U_.getStats()}),p(!1)}catch(e){console.error("加载性能数据失败:",e),p(!1)}};return f?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(i.Z,{className:"w-8 h-8 animate-spin mx-auto mb-4 text-blue-500"}),(0,r.jsx)("p",{className:"text-gray-600",children:"加载性能数据中..."})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"性能监控中心"}),(0,r.jsx)("p",{className:"text-gray-600",children:"实时监控应用性能指标、缓存状态和成本分析"}),(0,r.jsxs)("div",{className:"mt-4 flex gap-1 bg-gray-100 p-1 rounded-lg w-fit",children:[(0,r.jsx)("button",{onClick:()=>j("performance"),className:"px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat("performance"===b?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:"性能监控"}),(0,r.jsx)("button",{onClick:()=>j("cost"),className:"px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat("cost"===b?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:"成本分析"})]}),(0,r.jsxs)("div",{className:"mt-4 flex gap-4",children:[(0,r.jsxs)(h.Z,{onClick:v,variant:"outline",children:[(0,r.jsx)(i.Z,{className:"w-4 h-4 mr-2"}),"刷新数据"]}),(0,r.jsxs)(h.Z,{onClick:()=>{let e=new Blob([d.Bm.exportData()],{type:"application/json"}),t=URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="performance-report-".concat(new Date().toISOString().split("T")[0],".json"),s.click(),URL.revokeObjectURL(t)},variant:"outline",children:[(0,r.jsx)(l.Z,{className:"w-4 h-4 mr-2"}),"导出报告"]}),(0,r.jsxs)(h.Z,{onClick:()=>{m.dataCache.clear(),m.U_.clear(),u({dataCache:m.dataCache.getStats(),imageCache:m.U_.getStats()})},variant:"outline",children:[(0,r.jsx)(c.Z,{className:"w-4 h-4 mr-2"}),"清理缓存"]})]})]}),"performance"===b&&(0,r.jsxs)(r.Fragment,{children:[(null==s?void 0:s.score)&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[(0,r.jsx)(n.Z,{className:"w-5 h-5 mr-2 text-yellow-500"}),"性能评分"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold mb-2 ".concat(s.score.overall>=80?"text-green-500":s.score.overall>=60?"text-yellow-500":"text-red-500"),children:s.score.overall}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"总体评分"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold mb-2 ".concat(s.score.breakdown.loading>=80?"text-green-500":s.score.breakdown.loading>=60?"text-yellow-500":"text-red-500"),children:s.score.breakdown.loading}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"加载性能"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold mb-2 ".concat(s.score.breakdown.interactivity>=80?"text-green-500":s.score.breakdown.interactivity>=60?"text-yellow-500":"text-red-500"),children:s.score.breakdown.interactivity}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"交互性能"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold mb-2 ".concat(s.score.breakdown.visualStability>=80?"text-green-500":s.score.breakdown.visualStability>=60?"text-yellow-500":"text-red-500"),children:s.score.breakdown.visualStability}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"视觉稳定性"})]})]})]}),(null==s?void 0:null===(e=s.report)||void 0===e?void 0:e.coreWebVitals)&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[(0,r.jsx)(o.Z,{className:"w-5 h-5 mr-2 text-blue-500"}),"Core Web Vitals"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-blue-600 mb-2",children:[s.report.coreWebVitals.lcp,"ms"]}),(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"LCP (最大内容绘制)"}),(0,r.jsx)("div",{className:"text-xs ".concat(s.report.coreWebVitals.lcp<=2500?"text-green-500":s.report.coreWebVitals.lcp<=4e3?"text-yellow-500":"text-red-500"),children:s.report.coreWebVitals.lcp<=2500?"优秀":s.report.coreWebVitals.lcp<=4e3?"需要改进":"较差"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-green-600 mb-2",children:[s.report.coreWebVitals.fid,"ms"]}),(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"FID (首次输入延迟)"}),(0,r.jsx)("div",{className:"text-xs ".concat(s.report.coreWebVitals.fid<=100?"text-green-500":s.report.coreWebVitals.fid<=300?"text-yellow-500":"text-red-500"),children:s.report.coreWebVitals.fid<=100?"优秀":s.report.coreWebVitals.fid<=300?"需要改进":"较差"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-2",children:s.report.coreWebVitals.cls}),(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"CLS (累积布局偏移)"}),(0,r.jsx)("div",{className:"text-xs ".concat(s.report.coreWebVitals.cls<=.1?"text-green-500":s.report.coreWebVitals.cls<=.25?"text-yellow-500":"text-red-500"),children:s.report.coreWebVitals.cls<=.1?"优秀":s.report.coreWebVitals.cls<=.25?"需要改进":"较差"})]})]})]}),g&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[(0,r.jsx)(c.Z,{className:"w-5 h-5 mr-2 text-green-500"}),"缓存统计"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-semibold mb-3",children:"数据缓存"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"缓存大小:"}),(0,r.jsxs)("span",{className:"font-medium",children:[g.dataCache.size,"/",g.dataCache.maxSize]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"命中率:"}),(0,r.jsxs)("span",{className:"font-medium text-green-600",children:[g.dataCache.hitRate.toFixed(1),"%"]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"过期条目:"}),(0,r.jsx)("span",{className:"font-medium text-yellow-600",children:g.dataCache.expired})]})]})]}),(0,r.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-semibold mb-3",children:"图片缓存"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"缓存大小:"}),(0,r.jsxs)("span",{className:"font-medium",children:[g.imageCache.size,"/",g.imageCache.maxSize]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"命中率:"}),(0,r.jsxs)("span",{className:"font-medium text-green-600",children:[g.imageCache.hitRate.toFixed(1),"%"]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"过期条目:"}),(0,r.jsx)("span",{className:"font-medium text-yellow-600",children:g.imageCache.expired})]})]})]})]})]}),(null==s?void 0:null===(t=s.report)||void 0===t?void 0:t.apiPerformance)&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[(0,r.jsx)(i.Z,{className:"w-5 h-5 mr-2 text-orange-500"}),"API性能统计"]}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full text-sm",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b",children:[(0,r.jsx)("th",{className:"text-left py-2",children:"API名称"}),(0,r.jsx)("th",{className:"text-right py-2",children:"调用次数"}),(0,r.jsx)("th",{className:"text-right py-2",children:"平均响应时间"}),(0,r.jsx)("th",{className:"text-right py-2",children:"最大响应时间"}),(0,r.jsx)("th",{className:"text-right py-2",children:"最小响应时间"})]})}),(0,r.jsx)("tbody",{children:Object.entries(s.report.apiPerformance).map(e=>{let[t,s]=e;return(0,r.jsxs)("tr",{className:"border-b",children:[(0,r.jsx)("td",{className:"py-2 font-medium",children:t}),(0,r.jsx)("td",{className:"text-right py-2",children:s.callCount}),(0,r.jsx)("td",{className:"text-right py-2",children:(0,r.jsxs)("span",{className:"".concat(s.avgResponseTime<=500?"text-green-600":s.avgResponseTime<=1e3?"text-yellow-600":"text-red-600"),children:[s.avgResponseTime,"ms"]})}),(0,r.jsxs)("td",{className:"text-right py-2",children:[s.maxResponseTime,"ms"]}),(0,r.jsxs)("td",{className:"text-right py-2",children:[s.minResponseTime,"ms"]})]},t)})})]})})]})]}),"cost"===b&&(0,r.jsx)(R,{})]})})}},56334:function(e,t,s){"use strict";var r=s(57437),a=s(2265),i=s(68661);let l=a.forwardRef((e,t)=>{let{className:s,variant:a="primary",size:l="md",loading:c=!1,icon:n,children:o,disabled:d,...m}=e;return(0,r.jsxs)("button",{className:(0,i.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[a],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[l],s),ref:t,disabled:d||c,...m,children:[c&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!c&&n&&(0,r.jsx)("span",{className:"mr-2",children:n}),o]})});l.displayName="Button",t.Z=l},33882:function(e,t,s){"use strict";s.d(t,{U_:function(){return i},dataCache:function(){return l}});class r{set(e,t,s){let r=Date.now(),a=s||this.config.ttl;this.cache.size>=this.config.maxSize&&this.evict(),this.cache.set(e,{data:t,timestamp:r,ttl:a,accessCount:0,lastAccess:r})}get(e){let t=this.cache.get(e);if(!t)return null;let s=Date.now();return s-t.timestamp>t.ttl?(this.cache.delete(e),null):(t.accessCount++,t.lastAccess=s,t.data)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}evict(){let e;if(0!==this.cache.size){switch(this.config.strategy){case"LRU":e=this.findLRUKey();break;case"FIFO":e=this.findFIFOKey();break;case"TTL":e=this.findExpiredKey();break;default:e=this.cache.keys().next().value}e&&this.cache.delete(e)}}findLRUKey(){let e;let t=Date.now();return this.cache.forEach((s,r)=>{s.lastAccess<t&&(t=s.lastAccess,e=r)}),e}findFIFOKey(){let e;let t=Date.now();return this.cache.forEach((s,r)=>{s.timestamp<t&&(t=s.timestamp,e=r)}),e}findExpiredKey(){let e;let t=Date.now();return(this.cache.forEach((s,r)=>{if(t-s.timestamp>s.ttl){e=r;return}}),e)?e:this.findLRUKey()}getStats(){let e=Date.now(),t=0,s=0;return this.cache.forEach(r=>{e-r.timestamp>r.ttl&&t++,s+=JSON.stringify(r.data).length}),{size:this.cache.size,maxSize:this.config.maxSize,expired:t,totalSize:s,hitRate:this.calculateHitRate()}}calculateHitRate(){let e=0;return this.cache.forEach(t=>{e+=t.accessCount}),e>0?this.cache.size/e*100:0}constructor(e={}){this.cache=new Map,this.config={ttl:3e5,maxSize:100,strategy:"LRU",...e}}}class a{set(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:864e5;if(!this.storage)return;let r={data:t,timestamp:Date.now(),ttl:s};try{this.storage.setItem(this.prefix+e,JSON.stringify(r))}catch(t){console.warn("浏览器存储空间不足，清理过期缓存"),this.cleanup();try{this.storage.setItem(this.prefix+e,JSON.stringify(r))}catch(e){console.error("缓存设置失败:",e)}}}get(e){if(!this.storage)return null;try{let t=this.storage.getItem(this.prefix+e);if(!t)return null;let s=JSON.parse(t);if(Date.now()-s.timestamp>s.ttl)return this.storage.removeItem(this.prefix+e),null;return s.data}catch(e){return console.error("缓存读取失败:",e),null}}delete(e){this.storage&&this.storage.removeItem(this.prefix+e)}cleanup(){if(!this.storage)return;let e=Date.now(),t=[];for(let s=0;s<this.storage.length;s++){let r=this.storage.key(s);if(r&&r.startsWith(this.prefix))try{let s=this.storage.getItem(r);if(s){let a=JSON.parse(s);e-a.timestamp>a.ttl&&t.push(r)}}catch(e){t.push(r)}}t.forEach(e=>this.storage.removeItem(e))}clear(){if(!this.storage)return;let e=[];for(let t=0;t<this.storage.length;t++){let s=this.storage.key(t);s&&s.startsWith(this.prefix)&&e.push(s)}e.forEach(e=>this.storage.removeItem(e))}constructor(e="pet_cache_",t=!1){this.prefix=e,this.storage=t?sessionStorage:localStorage}}let i=new r({ttl:18e5,maxSize:50,strategy:"LRU"}),l=new r({ttl:3e5,maxSize:100,strategy:"LRU"});new a("pet_app_")},68661:function(e,t,s){"use strict";s.d(t,{cn:function(){return i},uf:function(){return l},vV:function(){return c}});var r=s(61994),a=s(53335);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.m6)((0,r.W)(t))}function l(e){return e<1e3?e.toString():e<1e4?"".concat((e/1e3).toFixed(1),"k"):e<1e5?"".concat((e/1e4).toFixed(1),"w"):"".concat(Math.floor(e/1e4),"w")}function c(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}},80240:function(e,t,s){"use strict";s.d(t,{Bm:function(){return a}});class r{static getInstance(){return r.instance||(r.instance=new r),r.instance}initializeObservers(){try{this.observeNavigation(),this.observePaint(),this.observeLayoutShift(),this.observeFirstInputDelay(),this.observeResources()}catch(e){console.warn("性能监控初始化失败:",e)}}observeNavigation(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{"navigation"===e.entryType&&(this.metrics.pageLoadTime=e.loadEventEnd-e.fetchStart)})});e.observe({entryTypes:["navigation"]}),this.observers.push(e)}}observePaint(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{"first-contentful-paint"===e.name&&(this.metrics.firstContentfulPaint=e.startTime)})});e.observe({entryTypes:["paint"]}),this.observers.push(e)}if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{let t=e.getEntries(),s=t[t.length-1];this.metrics.largestContentfulPaint=s.startTime});e.observe({entryTypes:["largest-contentful-paint"]}),this.observers.push(e)}}observeLayoutShift(){if("PerformanceObserver"in window){let e=0,t=new PerformanceObserver(t=>{t.getEntries().forEach(t=>{t.hadRecentInput||(e+=t.value,this.metrics.cumulativeLayoutShift=e)})});t.observe({entryTypes:["layout-shift"]}),this.observers.push(t)}}observeFirstInputDelay(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{this.metrics.firstInputDelay=e.processingStart-e.startTime})});e.observe({entryTypes:["first-input"]}),this.observers.push(e)}}observeResources(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{if("img"===e.initiatorType){let t=e.responseEnd-e.startTime;this.metrics.imageLoadTimes.push(t)}})});e.observe({entryTypes:["resource"]}),this.observers.push(e)}}recordApiResponse(e,t){this.metrics.apiResponseTimes.has(e)||this.metrics.apiResponseTimes.set(e,[]),this.metrics.apiResponseTimes.get(e).push(t)}recordImageLoad(e){this.metrics.imageLoadTimes.push(e)}getMemoryUsage(){return"memory"in performance?performance.memory:null}getMetrics(){return{...this.metrics,memoryUsage:this.getMemoryUsage()||void 0}}getPerformanceReport(){let e={};this.metrics.apiResponseTimes.forEach((t,s)=>{let r=t.reduce((e,t)=>e+t,0)/t.length;e[s]={avgResponseTime:Math.round(r),callCount:t.length,maxResponseTime:Math.round(Math.max(...t)),minResponseTime:Math.round(Math.min(...t))}});let t=this.metrics.imageLoadTimes.length>0?this.metrics.imageLoadTimes.reduce((e,t)=>e+t,0)/this.metrics.imageLoadTimes.length:0;return{coreWebVitals:{lcp:Math.round(this.metrics.largestContentfulPaint),fid:Math.round(this.metrics.firstInputDelay),cls:Math.round(1e3*this.metrics.cumulativeLayoutShift)/1e3},loadingPerformance:{pageLoadTime:Math.round(this.metrics.pageLoadTime),fcp:Math.round(this.metrics.firstContentfulPaint),avgImageLoadTime:Math.round(t)},apiPerformance:e,memoryUsage:this.getMemoryUsage()||void 0}}getPerformanceScore(){let e=this.metrics.largestContentfulPaint<=2500?100:this.metrics.largestContentfulPaint<=4e3?50:0,t=this.metrics.firstInputDelay<=100?100:this.metrics.firstInputDelay<=300?50:0,s=this.metrics.cumulativeLayoutShift<=.1?100:this.metrics.cumulativeLayoutShift<=.25?50:0;return{overall:Math.round((e+t+s)/3),breakdown:{loading:e,interactivity:t,visualStability:s}}}cleanup(){this.observers.forEach(e=>e.disconnect()),this.observers=[]}exportData(){return JSON.stringify({timestamp:new Date().toISOString(),url:window.location.href,userAgent:navigator.userAgent,metrics:this.getMetrics(),report:this.getPerformanceReport(),score:this.getPerformanceScore()},null,2)}constructor(){this.observers=[],this.metrics={pageLoadTime:0,firstContentfulPaint:0,largestContentfulPaint:0,firstInputDelay:0,cumulativeLayoutShift:0,imageLoadTimes:[],apiResponseTimes:new Map},this.initializeObservers()}}let a=r.getInstance()}},function(e){e.O(0,[554,170,971,117,744],function(){return e(e.s=23648)}),_N_E=e.O()}]);