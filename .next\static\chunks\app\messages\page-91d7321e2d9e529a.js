(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[750],{64733:function(e,t,a){Promise.resolve().then(a.bind(a,36099))},63639:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(39763).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},32660:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(39763).Z)("<PERSON>Left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},88997:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(39763).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},66337:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(39763).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},47692:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(39763).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},89345:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(39763).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},58293:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(39763).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},82718:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(39763).Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},13041:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(39763).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},73247:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(39763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},98728:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(39763).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},42449:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(39763).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},82023:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(39763).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},92369:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(39763).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},99376:function(e,t,a){"use strict";var s=a(35475);a.o(s,"useParams")&&a.d(t,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(t,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(t,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(t,{useSearchParams:function(){return s.useSearchParams}})},36099:function(e,t,a){"use strict";a.r(t);var s=a(57437),r=a(2265),n=a(63639),i=a(65302),l=a(32660),o=a(42449),c=a(88997),d=a(73247),u=a(98728),m=a(82718),p=a(32489),h=a(99376),x=a(47465),f=a(88941),y=a(98011);t.default=()=>{let e=(0,h.useRouter)(),{user:t,isLoggedIn:a}=(0,f.E)(),[g,b]=(0,r.useState)([]),[v,j]=(0,r.useState)(!0),[w,N]=(0,r.useState)(null),[k,Z]=(0,r.useState)("trade"),[_,I]=(0,r.useState)(!1),[T,C]=(0,r.useState)(null),[S,M]=(0,r.useState)(""),P=async()=>{if(a)try{j(!0);let e=await y.petAPI.getUserNotifications({limit:50,type:"contact"});if(e.success){let a=(e.data||[]).map(e=>{var a,s,r,n,i,l,o,c,d,u,m,p;return"contact"!==e.type?{id:e._id,type:"system",title:e.message,content:e.message,timestamp:e.created_at,read:e.read||!1,severity:"info"}:{id:e._id,type:"contact",fromUser:{id:e.sender_id,nickname:(null===(a=e.data)||void 0===a?void 0:a.sender_nickname)||(null===(s=e.data)||void 0===s?void 0:s.author_nickname)||"用户",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"},toUser:{id:e.recipient_id,nickname:(null==t?void 0:t.nickname)||"",avatar:(null==t?void 0:t.avatar_url)||""},postInfo:{id:e.post_id,title:(null===(r=e.data)||void 0===r?void 0:r.post_title)||"宠物帖子",image:"https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=300&h=300&fit=crop",postType:(null===(n=e.data)||void 0===n?void 0:n.post_type)||"selling"},contactInfo:{method:(null===(l=e.data)||void 0===l?void 0:null===(i=l.sender_contact)||void 0===i?void 0:i.type)||(null===(c=e.data)||void 0===c?void 0:null===(o=c.author_contact)||void 0===o?void 0:o.type)||"wechat",value:(null===(u=e.data)||void 0===u?void 0:null===(d=u.sender_contact)||void 0===d?void 0:d.value)||(null===(p=e.data)||void 0===p?void 0:null===(m=p.author_contact)||void 0===m?void 0:m.value)||"未提供"},timestamp:e.created_at,read:e.read||!1}});b(a)}else U()}catch(e){console.error("加载通知失败:",e),U()}finally{j(!1)}},U=()=>{b([{id:"1",type:"system",title:"发布限制通知",content:"由于您发布的内容违反了社区规定，您的账号被限制发布功能7天。限制期间：2024年1月1日 - 2024年1月8日。有意见可以申诉。",timestamp:"2024-01-01T10:00:00Z",read:!1,severity:"warning",canAppeal:!0,reportId:"report-123",appealStatus:"none"},{id:"2",type:"contact",fromUser:{id:"user-2",nickname:"爱猫人士",avatar:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"},toUser:{id:(null==t?void 0:t._id)||"",nickname:(null==t?void 0:t.nickname)||"",avatar:(null==t?void 0:t.avatar_url)||""},postInfo:{id:"post-1",title:"可爱的金毛宝宝找新家",image:"https://images.unsplash.com/photo-1552053831-71594a27632d?w=300&h=300&fit=crop",postType:"selling"},contactInfo:{method:"wechat",value:"wechat_user123"},timestamp:"2024-01-02T14:30:00Z",read:!1},{id:"3",type:"contact",fromUser:{id:"user-3",nickname:"繁育专家",avatar:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"},toUser:{id:(null==t?void 0:t._id)||"",nickname:(null==t?void 0:t.nickname)||"",avatar:(null==t?void 0:t.avatar_url)||""},postInfo:{id:"post-2",title:"优质拉布拉多配种服务",image:"https://images.unsplash.com/photo-1518717758536-85ae29035b6d?w=300&h=300&fit=crop",postType:"breeding"},contactInfo:{method:"phone",value:"138****8888"},timestamp:"2024-01-02T16:20:00Z",read:!1},{id:"4",type:"contact",fromUser:{id:"user-4",nickname:"寻宠志愿者",avatar:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face"},toUser:{id:(null==t?void 0:t._id)||"",nickname:(null==t?void 0:t.nickname)||"",avatar:(null==t?void 0:t.avatar_url)||""},postInfo:{id:"post-3",title:"寻找走失的橘猫咪咪",image:"https://images.unsplash.com/photo-1574158622682-e40e69881006?w=300&h=300&fit=crop",postType:"lost"},contactInfo:{method:"wechat",value:"helper_volunteer"},timestamp:"2024-01-02T18:45:00Z",read:!0},{id:"5",type:"contact",fromUser:{id:"user-5",nickname:"宠物爱好者",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"},toUser:{id:(null==t?void 0:t._id)||"",nickname:(null==t?void 0:t.nickname)||"",avatar:(null==t?void 0:t.avatar_url)||""},postInfo:{id:"post-4",title:"求购健康的边牧幼犬",image:"https://images.unsplash.com/photo-1551717743-49959800b1f6?w=300&h=300&fit=crop",postType:"wanted"},contactInfo:{method:"wechat",value:"wechat123456"},timestamp:"2024-01-01T20:15:00Z",read:!1},{id:"6",type:"system",title:"平台欢迎通知",content:"欢迎您加入我们的宠物交易平台！请遵守社区规定，文明交易，共同维护良好的交易环境。如需帮助，请查看使用指南。",timestamp:"2023-12-25T09:00:00Z",read:!0,severity:"info"}])};(0,r.useEffect)(()=>{if(!a){e.push("/");return}P()},[a,e,k]);let z=g.filter(e=>{if("system"===k)return"system"===e.type;if("contact"===e.type)switch(k){case"trade":return"selling"===e.postInfo.postType||"wanted"===e.postInfo.postType;case"breeding":return"breeding"===e.postInfo.postType;case"lost":return"lost"===e.postInfo.postType}return!1}),A=e=>{b(t=>t.map(t=>t.id===e?{...t,read:!0}:t))},L=async e=>{if(!w){N(e);try{await new Promise(e=>setTimeout(e,500)),b(t=>t.filter(t=>t.id!==e)),console.log("消息已删除")}catch(e){console.error("删除失败:",e)}finally{N(null)}}},V=e=>{C(e),I(!0)},E=async()=>{if(!T||!S.trim()){alert("请填写申诉理由");return}if(!T.reportId){alert("申诉信息不完整");return}try{let e=await y.petAPI.submitAppeal({reportId:T.reportId,reason:S,type:"post"});e.success?(b(e=>e.map(e=>e.id===T.id?{...e,appealStatus:"pending"}:e)),I(!1),C(null),M(""),alert("申诉已提交，我们会在3个工作日内处理")):alert(e.message||"申诉提交失败")}catch(e){console.error("申诉提交失败:",e),alert(e.message||"申诉提交失败，请稍后重试")}},H=async()=>{if(confirm({trade:"确定清空所有买卖通知吗？",breeding:"确定清空所有配种通知吗？",lost:"确定清空所有寻宠通知吗？",system:"确定清空所有系统通知吗？"}[k]))try{let e=z.map(e=>e.id);await new Promise(e=>setTimeout(e,500)),b(t=>t.filter(t=>!e.includes(t.id))),console.log("已清空".concat(z.length,"条通知"))}catch(e){console.error("清空失败:",e)}},R=e=>{let t=new Date(e),a=(new Date().getTime()-t.getTime())/36e5;return a<24?t.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}):a<168?"".concat(Math.floor(a/24),"天前"):t.toLocaleDateString("zh-CN")},q=e=>{switch(e){case"warning":return(0,s.jsx)(n.Z,{className:"w-5 h-5 text-orange-500"});case"error":return(0,s.jsx)(n.Z,{className:"w-5 h-5 text-red-500"});default:return(0,s.jsx)(i.Z,{className:"w-5 h-5 text-blue-500"})}};return a?(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(x.Z,{}),(0,s.jsxs)("main",{className:"max-w-4xl mx-auto px-4 py-6",children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)("button",{onClick:()=>e.back(),className:"mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors",children:(0,s.jsx)(l.Z,{className:"w-5 h-5"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"通知中心"})]}),(0,s.jsxs)("div",{className:"flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1",children:[(0,s.jsxs)("button",{onClick:()=>Z("trade"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("trade"===k?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,s.jsx)(o.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"买卖通知"})]}),(0,s.jsxs)("button",{onClick:()=>Z("breeding"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("breeding"===k?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,s.jsx)(c.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"配种通知"})]}),(0,s.jsxs)("button",{onClick:()=>Z("lost"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("lost"===k?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,s.jsx)(d.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"寻宠通知"})]}),(0,s.jsxs)("button",{onClick:()=>Z("system"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("system"===k?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,s.jsx)(u.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"系统通知"})]})]}),z.length>0&&(0,s.jsx)("div",{className:"flex justify-end mb-4",children:(0,s.jsx)("button",{onClick:H,className:"px-4 py-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors",children:"清空当前分类"})}),(0,s.jsx)("div",{className:"space-y-4",children:v?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),(0,s.jsx)("p",{className:"text-gray-500 mt-4",children:"加载中..."})]}):0===z.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(m.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无通知"}),(0,s.jsx)("p",{className:"text-gray-500",children:"您还没有收到任何通知"})]}):z.map(e=>(0,s.jsx)("div",{className:"bg-white rounded-lg border p-4 hover:shadow-md transition-shadow cursor-pointer ".concat(e.read?"border-gray-200":"border-blue-200 bg-blue-50"),onClick:()=>A(e.id),children:"system"===e.type?(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-1",children:q(e.severity)}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-900 truncate",children:e.title}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-xs text-gray-500",children:R(e.timestamp)}),!e.read&&(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,s.jsx)("button",{onClick:t=>{t.stopPropagation(),L(e.id)},disabled:w===e.id,className:"p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50",children:w===e.id?(0,s.jsx)("div",{className:"w-3 h-3 border border-gray-300 border-t-transparent rounded-full animate-spin"}):(0,s.jsx)(p.Z,{className:"w-3 h-3"})})]})]}),(0,s.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2",children:e.content}),e.canAppeal&&(0,s.jsxs)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:["none"===e.appealStatus&&(0,s.jsx)("button",{onClick:t=>{t.stopPropagation(),V(e)},className:"text-sm bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors",children:"申诉"}),"pending"===e.appealStatus&&(0,s.jsx)("span",{className:"text-sm text-orange-600 bg-orange-100 px-3 py-1 rounded-full",children:"申诉处理中..."}),"approved"===e.appealStatus&&(0,s.jsx)("span",{className:"text-sm text-green-600 bg-green-100 px-3 py-1 rounded-full",children:"申诉已通过，处罚已撤销"}),"rejected"===e.appealStatus&&(0,s.jsx)("span",{className:"text-sm text-red-600 bg-red-100 px-3 py-1 rounded-full",children:"申诉已驳回，处罚维持"})]})]})]}):(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("img",{src:e.fromUser.avatar,alt:e.fromUser.nickname,className:"w-10 h-10 rounded-full object-cover"}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:(()=>{switch(e.postInfo.postType){case"selling":return"有用户想购买您的宠物";case"wanted":return"有卖家回应了您的求购";case"breeding":return"有用户需要您的配种服务";case"lost":return"有用户提供了走失线索";default:return"有用户联系了您"}})()}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-xs text-gray-500",children:R(e.timestamp)}),!e.read&&(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,s.jsx)("button",{onClick:t=>{t.stopPropagation(),L(e.id)},disabled:w===e.id,className:"p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50",children:w===e.id?(0,s.jsx)("div",{className:"w-3 h-3 border border-gray-300 border-t-transparent rounded-full animate-spin"}):(0,s.jsx)(p.Z,{className:"w-3 h-3"})})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,s.jsx)("img",{src:e.postInfo.image,alt:e.postInfo.title,className:"w-12 h-12 rounded-lg object-cover"}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm text-gray-900 truncate",children:e.postInfo.title}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:(()=>{switch(e.postInfo.postType){case"selling":return"买家：".concat(e.fromUser.nickname);case"wanted":return"卖家：".concat(e.fromUser.nickname);case"breeding":return"需求方：".concat(e.fromUser.nickname);case"lost":return"提供者：".concat(e.fromUser.nickname);default:return"联系人：".concat(e.fromUser.nickname)}})()})]}),(0,s.jsx)("button",{className:"text-xs text-blue-600 hover:text-blue-800 transition-colors",children:"查看帖子"})]}),(0,s.jsxs)("div",{className:"bg-blue-50 rounded-lg p-3 border border-blue-200",children:[(0,s.jsx)("p",{className:"text-xs text-blue-600 mb-2 font-medium",children:"买家联系方式"}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:["wechat"===e.contactInfo.method&&"微信：","phone"===e.contactInfo.method&&"电话：",e.contactInfo.value]}),(0,s.jsx)("button",{className:"text-xs bg-blue-500 text-white px-3 py-1 rounded-full hover:bg-blue-600 transition-colors",onClick:t=>{t.stopPropagation(),navigator.clipboard.writeText(e.contactInfo.value)},children:"复制"})]})]})]})]})},e.id))})]}),_&&T&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"申诉处罚"}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"处罚内容："}),(0,s.jsx)("p",{className:"text-sm bg-gray-100 p-3 rounded-lg",children:T.content})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["申诉理由 ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("textarea",{value:S,onChange:e=>M(e.target.value),placeholder:"请详细说明您认为处罚不当的理由...",className:"w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsx)("button",{onClick:()=>{I(!1),C(null),M("")},className:"flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"取消"}),(0,s.jsx)("button",{onClick:E,disabled:!S.trim(),className:"flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"提交申诉"})]})]})})]}):null}},56334:function(e,t,a){"use strict";var s=a(57437),r=a(2265),n=a(68661);let i=r.forwardRef((e,t)=>{let{className:a,variant:r="primary",size:i="md",loading:l=!1,icon:o,children:c,disabled:d,...u}=e;return(0,s.jsxs)("button",{className:(0,n.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[r],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[i],a),ref:t,disabled:d||l,...u,children:[l&&(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!l&&o&&(0,s.jsx)("span",{className:"mr-2",children:o}),c]})});i.displayName="Button",t.Z=i}},function(e){e.O(0,[649,19,347,554,721,648,11,734,465,971,117,744],function(){return e(e.s=64733)}),_N_E=e.O()}]);