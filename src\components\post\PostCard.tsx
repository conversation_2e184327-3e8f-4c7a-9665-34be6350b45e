'use client';

import React from 'react';
import Link from 'next/link';
import { Heart, Star, MapPin, Clock } from 'lucide-react';
import { cn } from '@/utils';
import CloudImage from '@/components/ui/CloudImage';

interface Post {
  _id: string;
  title: string;
  description: string;
  images: string[];
  category: string;
  type: string;
  location: string;
  created_at: string;
  likes_count: number;
  bookmarks_count: number;
  ratings_count: number;
  avg_rating: number;
}

interface PostCardProps {
  post: Post;
  className?: string;
}

const PostCard: React.FC<PostCardProps> = ({ post, className }) => {
  // 格式化时间
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  // 获取类型标签颜色
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'selling':
        return 'bg-green-500';
      case 'breeding':
        return 'bg-pink-500';
      case 'lost':
        return 'bg-orange-500';
      case 'wanted':
        return 'bg-blue-500';
      default:
        return 'bg-gray-500';
    }
  };

  // 获取类型标签文本
  const getTypeText = (type: string) => {
    switch (type) {
      case 'selling':
        return '出售';
      case 'breeding':
        return '配种';
      case 'lost':
        return '寻回';
      case 'wanted':
        return '求购';
      default:
        return '其他';
    }
  };

  return (
    <Link
      href={`/post/detail?id=${post._id}`}
      className={cn(
        'block bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow',
        className
      )}
    >
      {/* 图片区域 */}
      <div className="relative aspect-square bg-gray-100">
        {post.images && post.images[0] ? (
          <CloudImage
            fileId={post.images[0]}
            alt={post.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-gray-400">
            <span className="text-sm">暂无图片</span>
          </div>
        )}
        
        {/* 类型标签 */}
        {post.type && (
          <div className={cn(
            'absolute top-2 left-2 px-2 py-1 rounded text-white text-xs font-medium',
            getTypeColor(post.type)
          )}>
            {getTypeText(post.type)}
          </div>
        )}
      </div>

      {/* 内容区域 */}
      <div className="p-3">
        {/* 标题 */}
        <h3 className="font-medium text-gray-900 text-sm line-clamp-2 mb-1">
          {post.title}
        </h3>

        {/* 描述 */}
        <p className="text-xs text-gray-600 line-clamp-2 mb-2">
          {post.description}
        </p>

        {/* 位置 */}
        {post.location && (
          <div className="flex items-center text-xs text-gray-500 mb-2">
            <MapPin className="w-3 h-3 mr-1" />
            <span className="truncate">{post.location.replace('#', '')}</span>
          </div>
        )}

        {/* 统计信息 */}
        <div className="flex items-center justify-between text-xs text-gray-400">
          <div className="flex items-center space-x-3">
            <span className="flex items-center space-x-1">
              <Heart className="w-3 h-3" />
              <span>{post.likes_count}</span>
            </span>
            
            {post.ratings_count > 0 && (
              <span className="flex items-center space-x-1">
                <Star className="w-3 h-3" />
                <span>{post.avg_rating.toFixed(1)}</span>
              </span>
            )}
          </div>

          <div className="flex items-center space-x-1">
            <Clock className="w-3 h-3" />
            <span>{formatTime(post.created_at)}</span>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default PostCard;
