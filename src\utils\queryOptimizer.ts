// 数据库查询优化工具
import { dataCache, cached } from './cacheManager';

export interface QueryOptions {
  useCache?: boolean;
  cacheTtl?: number;
  batchSize?: number;
  preload?: string[]; // 预加载字段
  fields?: string[]; // 只查询指定字段
}

export interface PaginationOptions {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 查询优化器类
export class QueryOptimizer {
  private static instance: QueryOptimizer;
  private queryStats = new Map<string, { count: number; avgTime: number }>();

  static getInstance(): QueryOptimizer {
    if (!QueryOptimizer.instance) {
      QueryOptimizer.instance = new QueryOptimizer();
    }
    return QueryOptimizer.instance;
  }

  // 优化的帖子查询
  async optimizedPostQuery(params: {
    page?: number;
    limit?: number;
    category?: string;
    type?: string;
    location?: string;
    userId?: string;
    includeUserInfo?: boolean;
    includeCategoryInfo?: boolean;
    sortBy?: string;
    useCache?: boolean;
  }) {
    const startTime = Date.now();
    const cacheKey = `posts_${JSON.stringify(params)}`;

    // 尝试从缓存获取
    if (params.useCache !== false) {
      const cached = dataCache.get(cacheKey);
      if (cached) {
        this.recordQueryStats('optimizedPostQuery', Date.now() - startTime);
        return cached;
      }
    }

    try {
      // 构建优化的查询参数
      const queryParams = this.buildOptimizedQuery(params);
      
      // 调用云函数
      const { petAPI } = await import('@/lib/cloudbase');
      const result = await petAPI.getOptimizedPosts(queryParams);

      // 缓存结果
      if (result.success && params.useCache !== false) {
        dataCache.set(cacheKey, result, 2 * 60 * 1000); // 缓存2分钟
      }

      this.recordQueryStats('optimizedPostQuery', Date.now() - startTime);
      return result;

    } catch (error) {
      console.error('查询优化失败:', error);
      throw error;
    }
  }

  // 构建优化的查询参数
  private buildOptimizedQuery(params: any) {
    const optimized: any = {
      action: 'query',
      page: params.page || 1,
      limit: Math.min(params.limit || 10, 20), // 限制最大查询数量
      includeUserInfo: params.includeUserInfo || false,
      includeCategoryInfo: params.includeCategoryInfo || false
    };

    // 添加筛选条件
    if (params.category) {
      optimized.category = params.category;
    }

    if (params.type) {
      optimized.type = params.type;
    }

    if (params.location) {
      optimized.location = params.location;
    }

    if (params.userId) {
      optimized.userId = params.userId;
    }

    if (params.sortBy) {
      optimized.sortBy = params.sortBy;
    }

    return optimized;
  }

  // 批量查询优化
  async batchQuery<T>(
    queries: Array<() => Promise<T>>,
    batchSize: number = 5
  ): Promise<T[]> {
    const results: T[] = [];
    
    for (let i = 0; i < queries.length; i += batchSize) {
      const batch = queries.slice(i, i + batchSize);
      const batchResults = await Promise.allSettled(
        batch.map(query => query())
      );
      
      batchResults.forEach(result => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          console.error('批量查询失败:', result.reason);
        }
      });
    }

    return results;
  }

  // 预加载相关数据
  async preloadRelatedData(posts: any[]): Promise<void> {
    if (!posts.length) return;

    const userIds = Array.from(new Set(posts.map(post => post.user_id || post.author_id).filter(Boolean)));
    const categoryIds = Array.from(new Set(posts.map(post => post.category).filter(Boolean)));

    // 批量预加载用户信息
    if (userIds.length > 0) {
      this.preloadUsers(userIds);
    }

    // 批量预加载分类信息
    if (categoryIds.length > 0) {
      this.preloadCategories(categoryIds);
    }
  }

  // 预加载用户信息
  private async preloadUsers(userIds: string[]): Promise<void> {
    const uncachedUserIds = userIds.filter(id => !dataCache.get(`user_${id}`));
    
    if (uncachedUserIds.length === 0) return;

    try {
      // 这里可以调用批量获取用户信息的API
      // const users = await petAPI.batchGetUsers(uncachedUserIds);
      // users.forEach(user => {
      //   dataCache.set(`user_${user._id}`, user, 10 * 60 * 1000);
      // });
    } catch (error) {
      console.error('预加载用户信息失败:', error);
    }
  }

  // 预加载分类信息
  private async preloadCategories(categoryIds: string[]): Promise<void> {
    const uncachedCategoryIds = categoryIds.filter(id => !dataCache.get(`category_${id}`));
    
    if (uncachedCategoryIds.length === 0) return;

    try {
      // 这里可以调用批量获取分类信息的API
      // const categories = await petAPI.batchGetCategories(uncachedCategoryIds);
      // categories.forEach(category => {
      //   dataCache.set(`category_${category.id}`, category, 30 * 60 * 1000);
      // });
    } catch (error) {
      console.error('预加载分类信息失败:', error);
    }
  }

  // 记录查询统计
  private recordQueryStats(queryName: string, duration: number): void {
    const stats = this.queryStats.get(queryName) || { count: 0, avgTime: 0 };
    stats.count++;
    stats.avgTime = (stats.avgTime * (stats.count - 1) + duration) / stats.count;
    this.queryStats.set(queryName, stats);
  }

  // 获取查询统计
  getQueryStats(): Map<string, { count: number; avgTime: number }> {
    return new Map(this.queryStats);
  }

  // 清除统计
  clearStats(): void {
    this.queryStats.clear();
  }
}

// 分页优化工具
export class PaginationOptimizer {
  // 智能分页大小计算
  static calculateOptimalPageSize(
    totalItems: number,
    viewportHeight: number,
    itemHeight: number = 200
  ): number {
    const itemsPerScreen = Math.floor(viewportHeight / itemHeight);
    const optimalSize = Math.max(itemsPerScreen * 2, 10); // 至少加载2屏内容
    return Math.min(optimalSize, 50); // 最大不超过50条
  }

  // 预加载下一页
  static async preloadNextPage(
    currentPage: number,
    queryFunction: (page: number) => Promise<any>
  ): Promise<void> {
    try {
      const nextPage = currentPage + 1;
      const cacheKey = `preload_page_${nextPage}`;
      
      // 检查是否已经预加载
      if (dataCache.get(cacheKey)) {
        return;
      }

      // 预加载下一页数据
      const result = await queryFunction(nextPage);
      dataCache.set(cacheKey, result, 5 * 60 * 1000); // 缓存5分钟
      
    } catch (error) {
      console.error('预加载下一页失败:', error);
    }
  }

  // 虚拟滚动优化
  static calculateVisibleRange(
    scrollTop: number,
    containerHeight: number,
    itemHeight: number,
    totalItems: number,
    buffer: number = 5
  ): { start: number; end: number } {
    const start = Math.max(0, Math.floor(scrollTop / itemHeight) - buffer);
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const end = Math.min(totalItems, start + visibleCount + buffer * 2);
    
    return { start, end };
  }
}

// 查询优化装饰器
export function optimizedQuery(options: QueryOptions = {}) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const { useCache = true, cacheTtl = 5 * 60 * 1000 } = options;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      const cacheKey = `${propertyName}_${JSON.stringify(args)}`;
      
      // 尝试从缓存获取
      if (useCache) {
        const cached = dataCache.get(cacheKey);
        if (cached !== null) {
          return cached;
        }
      }

      try {
        // 执行原方法
        const result = await method.apply(this, args);
        
        // 缓存结果
        if (useCache && result) {
          dataCache.set(cacheKey, result, cacheTtl);
        }

        // 记录性能
        const duration = Date.now() - startTime;
        console.log(`查询 ${propertyName} 耗时: ${duration}ms`);
        
        return result;
      } catch (error) {
        console.error(`查询 ${propertyName} 失败:`, error);
        throw error;
      }
    };

    return descriptor;
  };
}

// 导出单例实例
export const queryOptimizer = QueryOptimizer.getInstance();
